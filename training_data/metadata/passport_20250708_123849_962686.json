{"document_type": "passport_any", "timestamp": "20250708_123849_962686", "request_id": "47097fd1-72fa-4d9f-84df-39b98b57590b", "application_id": "8", "filename": "document.jpg", "file_size": 115793, "image_saved": true, "extracted_data": {"countryregion": "GBR", "dateofbirth": "04/12/1988", "dateofexpiration": "18/08/2026", "dateofissue": "18/08/2016", "documentnumber": "*********", "documenttype": "P", "firstname": "<PERSON>", "issuingauthority": "HMPO", "lastname": "Uk Specimen", "machinereadablezone": "{'CountryRegion': DocumentField(value_type=countryRegion, value='GBR', content=GBR, bounding_regions=[], spans=[], confidence=None), 'DateOfBirth': DocumentField(value_type=date, value=datetime.date(1988, 12, 4), content=881204, bounding_regions=[], spans=[], confidence=None), 'DateOfExpiration': DocumentField(value_type=date, value=datetime.date(2026, 8, 18), content=260818, bounding_regions=[], spans=[], confidence=None), 'DocumentNumber': DocumentField(value_type=string, value='*********', content=*********, bounding_regions=[], spans=[], confidence=None), 'FirstName': DocumentField(value_type=string, value='ANGELA ZOE', content=ANGELA<ZOE, bounding_regions=[], spans=[], confidence=None), 'LastName': DocumentField(value_type=string, value='UK SPECIMEN', content=UK<SPECIMEN, bounding_regions=[], spans=[], confidence=None), 'Nationality': DocumentField(value_type=countryRegion, value='GBR', content=GBR, bounding_regions=[], spans=[], confidence=None), 'Sex': DocumentField(value_type=string, value='F', content=F, bounding_regions=[], spans=[], confidence=None)}", "nationality": "GBR", "placeofbirth": "CROYDON", "sex": "F"}, "fraud_analysis": {"is_fraudulent": false, "is_authentic": true, "is_altered": false, "confidence_score": 1.0, "fraud_indicators": [], "authenticity_score": 1.0, "alteration_score": 0.0, "security_features_verified": ["High document authenticity confidence", "High confidence security fields: DateOfBirth, DateOfExpiration, DocumentNumber"], "requires_manual_review": false, "image_quality_score": 0.7758292924407674}, "validation_results": {"is_consistent": true, "warnings": [], "errors": [], "field_validations": {"countryregion": {"is_valid": true, "has_errors": false, "has_warnings": false, "errors": [], "warnings": []}, "dateofbirth": {"is_valid": true, "has_errors": false, "has_warnings": false, "errors": [], "warnings": []}, "dateofexpiration": {"is_valid": true, "has_errors": false, "has_warnings": false, "errors": [], "warnings": []}, "dateofissue": {"is_valid": true, "has_errors": false, "has_warnings": false, "errors": [], "warnings": []}, "documentnumber": {"is_valid": true, "has_errors": false, "has_warnings": false, "errors": [], "warnings": []}, "documenttype": {"is_valid": true, "has_errors": false, "has_warnings": false, "errors": [], "warnings": []}, "firstname": {"is_valid": true, "has_errors": false, "has_warnings": false, "errors": [], "warnings": []}, "issuingauthority": {"is_valid": true, "has_errors": false, "has_warnings": false, "errors": [], "warnings": []}, "lastname": {"is_valid": true, "has_errors": false, "has_warnings": false, "errors": [], "warnings": []}, "machinereadablezone": {"is_valid": true, "has_errors": false, "has_warnings": false, "errors": [], "warnings": []}, "nationality": {"is_valid": true, "has_errors": false, "has_warnings": false, "errors": [], "warnings": []}, "placeofbirth": {"is_valid": true, "has_errors": false, "has_warnings": false, "errors": [], "warnings": []}, "sex": {"is_valid": true, "has_errors": false, "has_warnings": false, "errors": [], "warnings": []}}}, "processing_metadata": {"processing_method": "microsoft_document_intelligence", "confidence": 0.9910714285714286, "processing_timestamp": "2025-07-08T12:38:49.957408", "model_used": "N/A"}}