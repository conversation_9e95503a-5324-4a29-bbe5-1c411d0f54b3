{"document_type": "passport_any", "timestamp": "20250708_123755_001387", "request_id": "7ecdbad6-d9ad-4cfb-87ae-c3c147662a0d", "application_id": "8", "filename": "document.jpg", "file_size": 130958, "image_saved": true, "extracted_data": {"countryregion": "IND", "dateofbirth": "23/09/1959", "dateofexpiration": "10/10/2021", "dateofissue": "11/10/2011", "documentnumber": "J8369854", "documenttype": "P", "firstname": "<PERSON><PERSON>", "lastname": "<PERSON><PERSON><PERSON><PERSON>", "machinereadablezone": "{'CountryRegion': DocumentField(value_type=countryRegion, value='IND', content=IND, bounding_regions=[], spans=[], confidence=None), 'DateOfBirth': DocumentField(value_type=date, value=datetime.date(1959, 9, 23), content=590923, bounding_regions=[], spans=[], confidence=None), 'DateOfExpiration': DocumentField(value_type=date, value=datetime.date(2021, 10, 10), content=211010, bounding_regions=[], spans=[], confidence=None), 'DocumentNumber': DocumentField(value_type=string, value='J8369854', content=J8369854, bounding_regions=[], spans=[], confidence=None), 'FirstName': DocumentField(value_type=string, value='SITA MAHA LAKSHMI', content=SITA<MAHA<LAKSHMI, bounding_regions=[], spans=[], confidence=None), 'LastName': DocumentField(value_type=string, value='RAMADUGULA', content=RAMADUGULA, bounding_regions=[], spans=[], confidence=None), 'Nationality': DocumentField(value_type=countryRegion, value='IND', content=IND, bounding_regions=[], spans=[], confidence=None), 'Sex': DocumentField(value_type=string, value='F', content=F, bounding_regions=[], spans=[], confidence=None)}", "nationality": "IND", "placeofissue": "HYDERABAD", "sex": "F"}, "fraud_analysis": {"is_fraudulent": true, "is_authentic": false, "is_altered": false, "confidence_score": 0.6000000000000001, "fraud_indicators": ["Low confidence in fields: countryregion, dateofbirth, dateofexpiration, nationality"], "authenticity_score": 0.6000000000000001, "alteration_score": 0.3999999999999999, "security_features_verified": ["High document authenticity confidence", "High confidence security fields: DocumentNumber"], "requires_manual_review": true, "image_quality_score": 0.5990708779807196}, "validation_results": {"is_consistent": true, "warnings": ["Low confidence (0.44) for countryregion", "Low confidence (0.56) for dateofbirth", "Low confidence (0.49) for dateofexpiration", "Low confidence (0.35) for nationality"], "errors": [], "field_validations": {"countryregion": {"is_valid": true, "has_errors": false, "has_warnings": true, "errors": [], "warnings": ["Low confidence (0.44) for countryregion"]}, "dateofbirth": {"is_valid": true, "has_errors": false, "has_warnings": true, "errors": [], "warnings": ["Low confidence (0.56) for dateofbirth"]}, "dateofexpiration": {"is_valid": true, "has_errors": false, "has_warnings": true, "errors": [], "warnings": ["Low confidence (0.49) for dateofexpiration"]}, "dateofissue": {"is_valid": true, "has_errors": false, "has_warnings": false, "errors": [], "warnings": []}, "documentnumber": {"is_valid": true, "has_errors": false, "has_warnings": false, "errors": [], "warnings": []}, "documenttype": {"is_valid": true, "has_errors": false, "has_warnings": false, "errors": [], "warnings": []}, "firstname": {"is_valid": true, "has_errors": false, "has_warnings": false, "errors": [], "warnings": []}, "lastname": {"is_valid": true, "has_errors": false, "has_warnings": false, "errors": [], "warnings": []}, "machinereadablezone": {"is_valid": true, "has_errors": false, "has_warnings": false, "errors": [], "warnings": []}, "nationality": {"is_valid": true, "has_errors": false, "has_warnings": true, "errors": [], "warnings": ["Low confidence (0.35) for nationality"]}, "placeofissue": {"is_valid": true, "has_errors": false, "has_warnings": false, "errors": [], "warnings": []}, "sex": {"is_valid": true, "has_errors": false, "has_warnings": false, "errors": [], "warnings": []}}}, "processing_metadata": {"processing_method": "microsoft_document_intelligence", "confidence": 0.8163846153846154, "processing_timestamp": "2025-07-08T12:37:54.998623", "model_used": "N/A"}}