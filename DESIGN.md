# SolidCheck Design System

This document outlines the design system and configuration for the SolidCheck application. All design tokens, components, and styling guidelines are centralized to ensure consistency and easy maintenance.

## 📁 Configuration Files

### Core Configuration
- **`lib/core/config/design_config.dart`** - Central design tokens (colors, spacing, typography, etc.)
- **`lib/core/config/component_config.dart`** - Component-specific styling and configurations
- **`lib/core/theme/colors.dart`** - Color constants (legacy, being migrated to design_config.dart)

## 🎨 Design Tokens

### Colors

#### Primary Colors
```dart
// Primary brand color - used for buttons, icons, and primary elements
static const Color primaryColor = AppColors.kBlueColor; // #1E88E5

// Secondary brand color - used for accents and secondary elements  
static const Color secondaryColor = AppColors.kBlueDarkColor; // #0D47A1
```

#### Text Colors
```dart
static const Color primaryTextColor = Colors.black;
static const Color secondaryTextColor = Colors.black87;
static const Color hintTextColor = Colors.grey;
```

#### Background Colors
```dart
static const Color primaryBackgroundColor = Colors.white;
static const Color cardBackgroundColor = Colors.white;
```

### Spacing

#### Standard Spacing Units
```dart
static const double spaceXS = 4.0;   // Extra small spacing
static const double spaceSM = 8.0;   // Small spacing  
static const double spaceMD = 12.0;  // Medium spacing (default)
static const double spaceLG = 16.0;  // Large spacing
static const double spaceXL = 20.0;  // Extra large spacing
```

#### Component Spacing
```dart
static const double cardPadding = spaceLG;      // 16px
static const double sectionSpacing = spaceLG;   // 16px
static const double itemSpacing = spaceSM;      // 8px
```

### Typography

#### Font Sizes
```dart
static const double fontSizeXS = 10.0;  // Extra small text
static const double fontSizeSM = 12.0;  // Small text
static const double fontSizeMD = 14.0;  // Medium text (default)
static const double fontSizeLG = 16.0;  // Large text
```

#### Text Styles
```dart
DesignConfig.headingLarge    // 24px, bold
DesignConfig.headingMedium   // 18px, semibold  
DesignConfig.bodyMedium      // 14px, regular
DesignConfig.bodySmall       // 12px, regular
```

### Icons

#### Icon Sizes
```dart
static const double iconSizeXS = 12.0;  // Extra small icons
static const double iconSizeSM = 16.0;  // Small icons (default)
static const double iconSizeMD = 20.0;  // Medium icons
static const double iconSizeLG = 24.0;  // Large icons
```

#### Icon Colors
```dart
static const Color primaryIconColor = primaryColor;    // #1E88E5
static const Color secondaryIconColor = hintTextColor; // Grey
```

## 🧩 Components

### Buttons

All buttons in the app follow the same design pattern with enhanced hover states, animations, and consistent styling.

#### Primary Button
```dart
ElevatedButton(
  style: ComponentConfig.primaryButtonStyle,
  onPressed: () {},
  child: Text('Primary Button'),
)

// Or use the utility method
ComponentConfig.getPrimaryButton(
  text: 'Primary Button',
  onPressed: () {},
  icon: Icons.add, // optional
  isLoading: false, // optional
)
```

#### Secondary Button
```dart
OutlinedButton(
  style: ComponentConfig.secondaryButtonStyle,
  onPressed: () {},
  child: Text('Secondary Button'),
)

// Or use the utility method
ComponentConfig.getSecondaryButton(
  text: 'Secondary Button',
  onPressed: () {},
  icon: Icons.edit, // optional
)
```

#### Text Button
```dart
TextButton(
  style: ComponentConfig.textButtonStyle,
  onPressed: () {},
  child: Text('Text Button'),
)

// Or use the utility method
ComponentConfig.getTextButton(
  text: 'Text Button',
  onPressed: () {},
  icon: Icons.info, // optional
)
```

#### Button Variants

**Compact Buttons** (for toolbars, navigation)
```dart
ElevatedButton(
  style: ComponentConfig.compactButtonStyle,
  onPressed: () {},
  child: Text('Compact'),
)
```

**Large Buttons** (for prominent actions)
```dart
ElevatedButton(
  style: ComponentConfig.largeButtonStyle,
  onPressed: () {},
  child: Text('Large Action'),
)
```

**Danger Buttons** (for destructive actions)
```dart
ElevatedButton(
  style: ComponentConfig.dangerButtonStyle,
  onPressed: () {},
  child: Text('Delete'),
)
```

**Success Buttons** (for positive actions)
```dart
ElevatedButton(
  style: ComponentConfig.successButtonStyle,
  onPressed: () {},
  child: Text('Save'),
)
```

#### Button Design Principles

All buttons in the SolidCheck app follow these design principles:

1. **Consistent Visual Hierarchy**
   - Primary buttons: Solid background with primary color
   - Secondary buttons: Outlined with primary color
   - Text buttons: Minimal styling for tertiary actions

2. **Enhanced Interactivity**
   - Hover states with subtle color changes and elevation
   - Press states with visual feedback
   - Smooth animations (200ms duration)
   - Disabled states with reduced opacity

3. **Responsive Design**
   - Consistent padding and sizing across devices
   - Compact variants for smaller spaces
   - Large variants for prominent actions

4. **Accessibility**
   - Sufficient color contrast ratios
   - Clear focus indicators
   - Semantic button roles
   - Keyboard navigation support

5. **Loading States**
   - Built-in loading indicators
   - Disabled state during loading
   - Clear visual feedback for async operations

### Cards

#### Standard Card
```dart
Container(
  decoration: ComponentConfig.cardDecoration,
  child: Padding(
    padding: EdgeInsets.all(DesignConfig.cardPadding),
    child: YourContent(),
  ),
)
```

### Input Fields

#### Standard Input
```dart
TextFormField(
  decoration: ComponentConfig.getInputDecoration(
    hintText: 'Enter text',
    labelText: 'Label',
    prefixIcon: Icon(Icons.person),
  ),
)
```

### Icons

#### Standard Icon
```dart
ComponentConfig.getIcon(Icons.person)
ComponentConfig.getIcon(Icons.person, size: DesignConfig.iconSizeLG)
```

#### Icon Button
```dart
ComponentConfig.getIconButton(
  Icons.add,
  onPressed: () {},
  tooltip: 'Add item',
)
```

### Headers

#### Section Header
```dart
ComponentConfig.getSectionHeader(
  'Section Title',
  icon: Icons.info,
  isMobile: ResponsiveUtil.isMobile(context),
)
```

### Badges

#### Standard Badge
```dart
ComponentConfig.getBadge('Status')
ComponentConfig.getBadge('Small', isSmall: true)
```

## 📱 Responsive Design

### Breakpoints
```dart
static const double mobileBreakpoint = 768.0;
static const double tabletBreakpoint = 1024.0;
static const double desktopBreakpoint = 1440.0;
```

### Responsive Helpers
```dart
// Get responsive spacing
double spacing = DesignConfig.getResponsiveSpacing(context, DesignConfig.spaceLG);

// Get responsive font size
double fontSize = DesignConfig.getResponsiveFontSize(context, DesignConfig.fontSizeMD);
```

## 🔧 How to Make Changes

### Changing Colors

1. **Update Primary Color:**
   ```dart
   // In lib/core/config/design_config.dart
   static const Color primaryColor = Color(0xFF2196F3); // New blue
   ```

2. **Update Text Colors:**
   ```dart
   static const Color primaryTextColor = Color(0xFF212121); // Dark grey
   ```

### Changing Spacing

1. **Update Base Spacing:**
   ```dart
   static const double spaceMD = 16.0; // Increase default spacing
   ```

2. **Update Component Spacing:**
   ```dart
   static const double cardPadding = spaceXL; // Use larger padding
   ```

### Changing Typography

1. **Update Font Sizes:**
   ```dart
   static const double fontSizeMD = 16.0; // Larger default text
   ```

2. **Update Text Styles:**
   ```dart
   static TextStyle get bodyMedium => TextStyle(
     fontSize: fontSizeLG, // Use larger font
     fontWeight: fontWeightMedium, // Use medium weight
     color: primaryTextColor,
   );
   ```

### Changing Icons

1. **Update Icon Sizes:**
   ```dart
   static const double iconSizeSM = 18.0; // Larger default icons
   ```

2. **Update Icon Colors:**
   ```dart
   static const Color primaryIconColor = Color(0xFF4CAF50); // Green icons
   ```

## 🎯 Best Practices

### Do's ✅
- Always use design tokens from `DesignConfig`
- Use component configurations from `ComponentConfig`
- Use responsive helpers for mobile/tablet/desktop layouts
- Follow the established spacing scale
- Use consistent icon sizes and colors

### Don'ts ❌
- Don't hardcode colors, spacing, or font sizes
- Don't create custom styling without updating the design system
- Don't use arbitrary values - stick to the design tokens
- Don't ignore responsive design principles

## 🔄 Migration Guide

### From Legacy Styling
1. Replace hardcoded colors with `DesignConfig.primaryColor`
2. Replace hardcoded spacing with `DesignConfig.spaceMD` etc.
3. Replace custom button styles with `ComponentConfig.primaryButtonStyle`
4. Replace custom input decorations with `ComponentConfig.getInputDecoration()`

### Example Migration
```dart
// Before (Legacy)
Container(
  padding: EdgeInsets.all(16.0),
  decoration: BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.circular(8.0),
    border: Border.all(color: Colors.grey[300]!),
  ),
)

// After (Design System)
Container(
  padding: EdgeInsets.all(DesignConfig.cardPadding),
  decoration: ComponentConfig.cardDecoration,
)
```

## 📋 Component Checklist

When creating new components, ensure:
- [ ] Uses design tokens from `DesignConfig`
- [ ] Follows responsive design principles
- [ ] Uses consistent spacing scale
- [ ] Uses standard icon sizes and colors
- [ ] Follows typography guidelines
- [ ] Uses component configurations where applicable
- [ ] Is documented in this design system

## 📝 Real Examples

### Updated Add Applicant Screen
The add applicant screen has been updated to use the design system:

```dart
// Before (Legacy)
ElevatedButton(
  style: ElevatedButton.styleFrom(
    backgroundColor: const Color(0xFF1E88E5),
    padding: EdgeInsets.symmetric(vertical: buttonPadding),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(8),
    ),
  ),
  child: Text('Send form to applicant'),
)

// After (Design System)
ElevatedButton(
  style: ComponentConfig.primaryButtonStyle,
  onPressed: () {},
  child: Text('Send form to applicant'),
)
```

### Form Fields
```dart
// Before (Legacy)
TextFormField(
  decoration: InputDecoration(
    hintText: 'Enter email',
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(4),
      borderSide: BorderSide(color: Colors.grey.shade300),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(4),
      borderSide: BorderSide(color: AppColors.kBlueColor),
    ),
  ),
)

// After (Design System)
TextFormField(
  decoration: ComponentConfig.getInputDecoration(
    hintText: 'Enter email',
    prefixIcon: Icon(Icons.email),
  ),
)
```

### Icons and Spacing
```dart
// Before (Legacy)
Icon(Icons.person, size: 16, color: AppColors.kBlueColor)
SizedBox(height: 16.0)

// After (Design System)
ComponentConfig.getIcon(Icons.person)
SizedBox(height: DesignConfig.spaceLG)
```

## 🚀 Future Enhancements

### Planned Features
- Dark mode support
- Theme switching
- Animation configurations
- Accessibility configurations
- Custom theme builder

### Contributing
When adding new design tokens or components:
1. Update the relevant configuration files
2. Update this documentation
3. Test across all screen sizes
4. Ensure backward compatibility

## 🔍 Quick Reference

### Most Used Design Tokens
```dart
// Colors
DesignConfig.primaryColor        // #1E88E5
DesignConfig.primaryTextColor    // Colors.black
DesignConfig.primaryBorderColor  // #E0E0E0

// Spacing
DesignConfig.spaceSM    // 8px
DesignConfig.spaceMD    // 12px
DesignConfig.spaceLG    // 16px

// Typography
DesignConfig.fontSizeSM // 12px
DesignConfig.fontSizeMD // 14px
DesignConfig.fontSizeLG // 16px

// Icons
DesignConfig.iconSizeSM // 16px
DesignConfig.iconSizeMD // 20px
DesignConfig.iconSizeLG // 24px
```

### Most Used Components
```dart
// Buttons
ComponentConfig.primaryButtonStyle
ComponentConfig.secondaryButtonStyle

// Input Fields
ComponentConfig.getInputDecoration()

// Icons
ComponentConfig.getIcon()
ComponentConfig.getIconButton()

// Cards
ComponentConfig.cardDecoration

// Loading
ComponentConfig.loadingIndicator
```
