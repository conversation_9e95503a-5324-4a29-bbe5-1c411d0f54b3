<?php
// Simple test file to debug process stamps issue
require_once __DIR__ . '/../vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "<h1>Process Stamps Debug Test</h1>";

try {
    // Test the ProcessStampService
    $stampService = app(\App\Services\ProcessStampService::class);
    $applicationId = 1;
    
    echo "<h2>Testing ProcessStampService for Application ID: $applicationId</h2>";
    
    // Get application stamps
    $stamps = $stampService->getApplicationStamps($applicationId);
    echo "<p>Found " . $stamps->count() . " stamps</p>";
    
    // Get stamps by section
    $sections = $stampService->getStampsBySection($applicationId);
    echo "<p>Found " . count($sections) . " sections</p>";
    
    if (count($sections) > 0) {
        echo "<h3>Sections:</h3>";
        echo "<ul>";
        foreach ($sections as $section) {
            echo "<li>" . $section['section_name'] . " (" . count($section['stamps']) . " stamps)</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red;'><strong>NO SECTIONS FOUND!</strong></p>";
    }
    
    // Test the component
    echo "<h2>Testing DBS Component</h2>";
    
    $component = new \App\Modules\Applications\Background\DBS\ViewModels\ApplicantCriminalRecord();
    
    // Inject dependencies
    $dbsService = app(\App\Modules\Applications\Background\DBS\Services\DBSApplicationService::class);
    $dbsRepository = app(\App\Modules\Applications\Background\DBS\Repositories\DBSRepositoryInterface::class);
    $formManager = app(\SolidFuse\Modules\Applications\Background\DBS\Services\DBSFormManagerService::class);
    $validationService = app(\SolidFuse\Modules\Applications\Background\DBS\Services\DBSEnhancedValidationService::class);
    $laravelValidationService = app(\SolidFuse\Modules\Applications\Background\DBS\Services\DBSLaravelValidationService::class);
    
    $component->boot(
        $dbsService,
        $dbsRepository,
        $formManager,
        $validationService,
        $laravelValidationService
    );
    
    $component->mount(7);
    
    echo "<p>Component mounted for applicant 7</p>";
    echo "<p>Selected application: " . ($component->selectedApplication ? $component->selectedApplication->id : 'None') . "</p>";
    echo "<p>Active tab: " . $component->activeMainTab . "</p>";
    
    if ($component->selectedApplication) {
        // Test process tab logic
        $selectedApplication = $component->selectedApplication;
        $processStamps = \App\Models\ProcessStamp::where('application_id', $selectedApplication->id)->get();
        $stampSections = $stampService->getStampsBySection($selectedApplication->id);
        
        echo "<h3>Process Tab Simulation:</h3>";
        echo "<p>Application ID: " . $selectedApplication->id . "</p>";
        echo "<p>Product ID: " . $selectedApplication->product_id . "</p>";
        echo "<p>Process Stamps Count: " . $processStamps->count() . "</p>";
        echo "<p>Stamp Sections Count: " . count($stampSections) . "</p>";
        
        if (count($stampSections) === 0) {
            echo "<p style='color: red; font-weight: bold;'>ISSUE FOUND: No stamp sections returned!</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
