#!/bin/bash

echo "🚀 Setting up API Interface Project..."

# Check if .env exists
if [ ! -f .env ]; then
    echo "📝 Creating .env file..."
    cp .env.example .env
fi

# Install dependencies
echo "📦 Installing Composer dependencies..."
composer install --no-dev --optimize-autoloader

# Generate application key
echo "🔑 Generating application key..."
php artisan key:generate

# Clear and cache config
echo "⚙️ Optimizing configuration..."
php artisan config:clear
php artisan config:cache

# API documentation is auto-generated by Scramble
echo "📚 API documentation will be auto-generated by <PERSON>ramble"

# Set permissions
echo "🔒 Setting permissions..."
chmod -R 755 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache 2>/dev/null || true

echo "✅ Setup complete!"
echo ""
echo "🌐 Access your API:"
echo "   - API Tester: http://localhost:8001/api-test"
echo "   - Documentation: http://localhost:8001/docs/api"
echo "   - Telescope: http://localhost:8001/telescope"
echo ""
echo "🚀 Start development server:"
echo "   php artisan serve --host=0.0.0.0 --port=8001"
