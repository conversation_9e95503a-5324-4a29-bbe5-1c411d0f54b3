"""
TrOCR Engine - Transformer-based OCR for high accuracy text extraction
Optimized for document processing with MVVM architecture
"""

import numpy as np
import cv2
from PIL import Image
from typing import Dict, List, Optional, Any, Tuple
import logging
import re
from datetime import datetime

from app.core.interfaces import IOCREngine
from app.models.document_models import (
    DocumentType, 
    ExtractedField, 
    ExtractionMethod
)

try:
    from transformers import TrOCRProcessor, VisionEncoderDecoderModel
    import torch
    TROCR_AVAILABLE = True
except ImportError:
    TROCR_AVAILABLE = False


class TrOCREngine(IOCREngine):
    """TrOCR engine for high-accuracy text extraction"""
    
    def __init__(self, model_name: str = "microsoft/trocr-large-printed"):
        """Initialize TrOCR engine"""
        self.model_name = model_name
        self.logger = logging.getLogger(__name__)
        self.processor = None
        self.model = None
        self.confidence_score = 0.0
        
        if not TROCR_AVAILABLE:
            self.logger.error("❌ TrOCR dependencies not available. Install transformers and torch.")
            return
        
        try:
            self.logger.info(f"🔄 Loading TrOCR model: {model_name}")
            self.processor = TrOCRProcessor.from_pretrained(model_name)
            self.model = VisionEncoderDecoderModel.from_pretrained(model_name)
            
            # Set to evaluation mode
            self.model.eval()
            
            # Use GPU if available
            if torch.cuda.is_available():
                self.model = self.model.cuda()
                self.logger.info("✅ TrOCR using GPU acceleration")
            else:
                self.logger.info("✅ TrOCR using CPU")
                
            self.logger.info("✅ TrOCR model loaded successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to load TrOCR model: {e}")
            self.processor = None
            self.model = None
    
    def extract_text(self, image: np.ndarray, **kwargs) -> str:
        """Extract raw text from image using TrOCR"""
        if not self._is_available():
            return ""
        
        try:
            # Convert numpy array to PIL Image
            if len(image.shape) == 3:
                pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            else:
                pil_image = Image.fromarray(image)
            
            # Preprocess image
            pil_image = self._preprocess_image(pil_image)
            
            # Generate text
            pixel_values = self.processor(pil_image, return_tensors="pt").pixel_values
            
            if torch.cuda.is_available():
                pixel_values = pixel_values.cuda()
            
            with torch.no_grad():
                generated_ids = self.model.generate(pixel_values, max_length=512)
            
            generated_text = self.processor.batch_decode(generated_ids, skip_special_tokens=True)[0]
            
            # Set confidence based on text quality
            self.confidence_score = self._calculate_confidence(generated_text)
            
            self.logger.info(f"✅ TrOCR extracted text (confidence: {self.confidence_score:.2f})")
            return generated_text
            
        except Exception as e:
            self.logger.error(f"❌ TrOCR text extraction failed: {e}")
            return ""
    
    def extract_fields(self, image: np.ndarray, document_type: DocumentType, **kwargs) -> Dict[str, ExtractedField]:
        """Extract structured fields using TrOCR + pattern matching"""
        if not self._is_available():
            return {}
        
        try:
            # Extract raw text
            text = self.extract_text(image)
            
            if not text:
                return {}
            
            # Apply document-specific field extraction
            fields = {}
            
            if document_type == DocumentType.UK_DRIVING_LICENSE:
                fields = self._extract_uk_license_fields(text)
            elif document_type in [DocumentType.P60, DocumentType.P45, DocumentType.P45_P60]:
                fields = self._extract_tax_document_fields(text)
            elif document_type == DocumentType.BANK_STATEMENT:
                fields = self._extract_bank_statement_fields(text)
            else:
                # Generic field extraction
                fields = self._extract_generic_fields(text)
            
            self.logger.info(f"✅ TrOCR extracted {len(fields)} fields from {document_type.value}")
            return fields
            
        except Exception as e:
            self.logger.error(f"❌ TrOCR field extraction failed: {e}")
            return {}
    
    def get_confidence(self) -> float:
        """Get overall confidence score"""
        return self.confidence_score
    
    def get_engine_info(self) -> Dict[str, str]:
        """Get engine version and metadata"""
        return {
            "engine": "TrOCR",
            "version": "1.0.0",
            "model": self.model_name,
            "available": str(self._is_available()),
            "supports": "General document text extraction"
        }
    
    def _is_available(self) -> bool:
        """Check if TrOCR is available"""
        return TROCR_AVAILABLE and self.processor is not None and self.model is not None
    
    def _preprocess_image(self, image: Image.Image) -> Image.Image:
        """Preprocess image for better TrOCR results"""
        # Convert to RGB if needed
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Resize if too large (TrOCR works best with reasonable sizes)
        max_size = 1024
        if max(image.size) > max_size:
            ratio = max_size / max(image.size)
            new_size = tuple(int(dim * ratio) for dim in image.size)
            image = image.resize(new_size, Image.Resampling.LANCZOS)
        
        return image
    
    def _calculate_confidence(self, text: str) -> float:
        """Calculate confidence based on text quality"""
        if not text:
            return 0.0
        
        # Basic confidence calculation based on text characteristics
        confidence = 0.5  # Base confidence
        
        # Increase confidence for longer text
        if len(text) > 50:
            confidence += 0.1
        
        # Increase confidence for proper capitalization
        if any(c.isupper() for c in text) and any(c.islower() for c in text):
            confidence += 0.1
        
        # Increase confidence for numbers (common in documents)
        if any(c.isdigit() for c in text):
            confidence += 0.1
        
        # Decrease confidence for too many special characters
        special_chars = sum(1 for c in text if not c.isalnum() and c not in ' .,:-/')
        if special_chars > len(text) * 0.3:
            confidence -= 0.2
        
        return min(0.95, max(0.1, confidence))
    
    def _extract_uk_license_fields(self, text: str) -> Dict[str, ExtractedField]:
        """Extract UK driving license fields from text"""
        fields = {}
        confidence = self.confidence_score
        
        # UK license number pattern
        license_pattern = r'([A-Z]{5}\d{6}[A-Z]{2}\d{2})'
        match = re.search(license_pattern, text)
        if match:
            fields['licence_number'] = ExtractedField(
                value=match.group(1),
                confidence=confidence,
                extraction_method=ExtractionMethod.TROCR,
                field_type='text',
                required=True,
                editable=True
            )
        
        # Name patterns
        name_patterns = [
            r'(?:1\.\s*|Name\s*:?\s*)([A-Z][A-Z\s]+?)(?:\s+2\.|$|\n)',
            r'([A-Z]{2,}\s+[A-Z]{2,}(?:\s+[A-Z]{2,})?)'
        ]
        
        for pattern in name_patterns:
            match = re.search(pattern, text)
            if match:
                full_name = match.group(1).strip()
                name_parts = full_name.split()
                if len(name_parts) >= 2:
                    fields['first_name'] = ExtractedField(
                        value=name_parts[0].title(),
                        confidence=confidence,
                        extraction_method=ExtractionMethod.TROCR,
                        field_type='text',
                        required=True,
                        editable=True
                    )
                    fields['last_name'] = ExtractedField(
                        value=' '.join(name_parts[1:]).title(),
                        confidence=confidence,
                        extraction_method=ExtractionMethod.TROCR,
                        field_type='text',
                        required=True,
                        editable=True
                    )
                break
        
        # Date patterns
        date_pattern = r'(\d{2}[./]\d{2}[./]\d{4})'
        dates = re.findall(date_pattern, text)
        if dates:
            # First date is usually DOB
            fields['date_of_birth'] = ExtractedField(
                value=self._format_date(dates[0]),
                confidence=confidence,
                extraction_method=ExtractionMethod.TROCR,
                field_type='date',
                required=True,
                editable=True
            )
        
        # Postcode pattern
        postcode_pattern = r'([A-Z]{1,2}\d[A-Z\d]?\s?\d[A-Z]{2})'
        match = re.search(postcode_pattern, text)
        if match:
            fields['postcode'] = ExtractedField(
                value=match.group(1).upper(),
                confidence=confidence,
                extraction_method=ExtractionMethod.TROCR,
                field_type='text',
                required=True,
                editable=True
            )
        
        return fields
    
    def _extract_tax_document_fields(self, text: str) -> Dict[str, ExtractedField]:
        """Extract P60/P45 tax document fields from text"""
        fields = {}
        confidence = self.confidence_score
        
        # Employee name patterns
        name_patterns = [
            r'(?:Employee|Name|To)\s*:?\s*([A-Z][A-Z\s]+?)(?:\s+(?:Address|Employer|Tax|Year|NI)|\n|$)',
            r'To\s+employee\s*:?\s*([A-Z][A-Z\s]+?)(?:\s+(?:Address|Employer|Tax|Year|NI)|\n|$)'
        ]
        
        for pattern in name_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                full_name = match.group(1).strip().title()
                fields['employee_name'] = ExtractedField(
                    value=full_name,
                    confidence=confidence,
                    extraction_method=ExtractionMethod.TROCR,
                    field_type='text',
                    required=True,
                    editable=True
                )
                
                # Split into first and last name
                name_parts = full_name.split()
                if len(name_parts) >= 2:
                    fields['first_name'] = ExtractedField(
                        value=name_parts[0],
                        confidence=confidence,
                        extraction_method=ExtractionMethod.TROCR,
                        field_type='text',
                        required=True,
                        editable=True
                    )
                    fields['last_name'] = ExtractedField(
                        value=' '.join(name_parts[1:]),
                        confidence=confidence,
                        extraction_method=ExtractionMethod.TROCR,
                        field_type='text',
                        required=True,
                        editable=True
                    )
                break
        
        # NI number pattern
        ni_pattern = r'([A-Z]{2}\s?\d{2}\s?\d{2}\s?\d{2}\s?[A-Z])'
        match = re.search(ni_pattern, text)
        if match:
            ni_number = match.group(1).upper()
            # Format properly
            ni_clean = re.sub(r'[^A-Z0-9]', '', ni_number)
            if len(ni_clean) == 9:
                formatted_ni = f"{ni_clean[:2]} {ni_clean[2:4]} {ni_clean[4:6]} {ni_clean[6:8]} {ni_clean[8]}"
                fields['ni_number'] = ExtractedField(
                    value=formatted_ni,
                    confidence=confidence,
                    extraction_method=ExtractionMethod.TROCR,
                    field_type='text',
                    required=True,
                    editable=True
                )
        
        # Tax year pattern
        tax_year_pattern = r'(\d{4}[-/]\d{2,4})'
        match = re.search(tax_year_pattern, text)
        if match:
            fields['tax_year'] = ExtractedField(
                value=match.group(1),
                confidence=confidence,
                extraction_method=ExtractionMethod.TROCR,
                field_type='text',
                required=False,
                editable=True
            )
        
        # Employer name pattern
        employer_patterns = [
            r'(?:Employer|Company)\s*:?\s*([A-Z][A-Z\s&.,\-]+?)(?:\s+(?:Address|Tax|Year|NI|PAYE)|\n|$)',
            r'PAYE\s+reference\s*:?\s*[A-Z0-9/]+\s*([A-Z][A-Z\s&.,\-]+?)(?:\s+(?:Address|Tax|Year|NI)|\n|$)'
        ]
        
        for pattern in employer_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                fields['employer_name'] = ExtractedField(
                    value=match.group(1).strip().title(),
                    confidence=confidence,
                    extraction_method=ExtractionMethod.TROCR,
                    field_type='text',
                    required=False,
                    editable=True
                )
                break
        
        # Postcode pattern (from address)
        postcode_pattern = r'([A-Z]{1,2}\d[A-Z\d]?\s?\d[A-Z]{2})'
        match = re.search(postcode_pattern, text)
        if match:
            fields['postcode'] = ExtractedField(
                value=match.group(1).upper(),
                confidence=confidence,
                extraction_method=ExtractionMethod.TROCR,
                field_type='text',
                required=False,
                editable=True
            )
        
        return fields
    
    def _extract_bank_statement_fields(self, text: str) -> Dict[str, ExtractedField]:
        """Extract bank statement fields from text"""
        fields = {}
        confidence = self.confidence_score
        
        # Account holder name pattern
        name_pattern = r'(?:Account holder|Name)\s*:?\s*([A-Z][A-Z\s]+)'
        match = re.search(name_pattern, text, re.IGNORECASE)
        if match:
            fields['account_holder'] = ExtractedField(
                value=match.group(1).strip().title(),
                confidence=confidence,
                extraction_method=ExtractionMethod.TROCR,
                field_type='text',
                required=True,
                editable=True
            )
        
        # Account number pattern
        account_pattern = r'(?:Account|A/C)\s*(?:number|no\.?)\s*:?\s*(\d{8,12})'
        match = re.search(account_pattern, text, re.IGNORECASE)
        if match:
            fields['account_number'] = ExtractedField(
                value=match.group(1),
                confidence=confidence,
                extraction_method=ExtractionMethod.TROCR,
                field_type='text',
                required=False,
                editable=True
            )
        
        # Sort code pattern
        sort_pattern = r'(?:Sort code)\s*:?\s*(\d{2}[-\s]?\d{2}[-\s]?\d{2})'
        match = re.search(sort_pattern, text, re.IGNORECASE)
        if match:
            fields['sort_code'] = ExtractedField(
                value=match.group(1),
                confidence=confidence,
                extraction_method=ExtractionMethod.TROCR,
                field_type='text',
                required=False,
                editable=True
            )
        
        return fields
    
    def _extract_generic_fields(self, text: str) -> Dict[str, ExtractedField]:
        """Extract generic fields from any document"""
        fields = {}
        confidence = self.confidence_score
        
        # Store raw text for manual review
        fields['raw_text'] = ExtractedField(
            value=text,
            confidence=confidence,
            extraction_method=ExtractionMethod.TROCR,
            field_type='textarea',
            required=False,
            editable=True
        )
        
        return fields
    
    def _format_date(self, date_str: str) -> str:
        """Format date to DD/MM/YYYY"""
        if not date_str:
            return ""
        
        # Replace dots with slashes
        date_str = date_str.replace('.', '/')
        
        # If already in DD/MM/YYYY format, return as is
        if len(date_str) == 10 and date_str.count('/') == 2:
            return date_str
        
        return date_str
