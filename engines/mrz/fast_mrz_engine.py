"""
FastMRZ Engine - Specialized MRZ extraction for passports
Based on reference implementation with MVVM architecture
"""

import numpy as np
import cv2
import pytesseract
import os
import base64
from datetime import datetime
from io import BytesIO
from PIL import Image
from typing import Dict, Optional, Any
import logging

from app.core.interfaces import IOCREngine
from app.models.document_models import (
    DocumentType, 
    ExtractedField, 
    ExtractionMethod
)


class FastMRZEngine(IOCREngine):
    """FastMRZ engine for passport MRZ extraction"""
    
    def __init__(self, tesseract_path: str = "", model_path: str = ""):
        """Initialize FastMRZ engine"""
        self.tesseract_path = tesseract_path or "/usr/bin/tesseract"
        self.model_path = model_path or os.path.join(os.path.dirname(__file__), "models/mrz_seg.onnx")
        self.logger = logging.getLogger(__name__)

        # Initialize ONNX model
        try:
            self.net = cv2.dnn.readNetFromONNX(self.model_path)
            self.logger.info("✅ FastMRZ ONNX model loaded successfully")
        except Exception as e:
            self.logger.error(f"❌ Failed to load FastMRZ model: {e}")
            self.net = None

        # Set tesseract path
        pytesseract.pytesseract.tesseract_cmd = self.tesseract_path
        self.logger.info(f"✅ Tesseract path set to: {self.tesseract_path}")

        # Test tesseract availability
        try:
            import subprocess
            result = subprocess.run([self.tesseract_path, '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                self.logger.info(f"✅ Tesseract available: {result.stdout.split()[1] if len(result.stdout.split()) > 1 else 'unknown version'}")
            else:
                self.logger.error(f"❌ Tesseract test failed: {result.stderr}")
        except Exception as e:
            self.logger.error(f"❌ Tesseract test error: {e}")
    
    def extract_text(self, image: np.ndarray, **kwargs) -> str:
        """Extract raw MRZ text from passport image"""
        if self.net is None:
            raise RuntimeError("FastMRZ model not loaded")
        
        try:
            # Process image for MRZ detection
            processed_image = self._process_image(image)
            
            # Run MRZ segmentation
            self.net.setInput(processed_image)
            output_data = self.net.forward()
            
            # Extract MRZ region and perform OCR
            mrz_text = self._get_roi_text(output_data, image)
            
            # Clean and validate MRZ
            cleaned_mrz = self._cleanse_mrz(mrz_text)

            self.logger.info(f"🔍 Raw MRZ text: {repr(mrz_text[:100])}")
            self.logger.info(f"🔍 Cleaned MRZ: {repr(cleaned_mrz[:100])}")

            return cleaned_mrz
            
        except Exception as e:
            self.logger.error(f"❌ FastMRZ text extraction failed: {e}")
            return ""
    
    def extract_fields(self, image: np.ndarray, document_type: DocumentType, **kwargs) -> Dict[str, ExtractedField]:
        """Extract structured fields from passport MRZ"""
        if document_type not in [DocumentType.PASSPORT, DocumentType.PASSPORT_ANY]:
            return {}
        
        try:
            # Extract MRZ text
            mrz_text = self.extract_text(image)
            
            if not mrz_text:
                return {}
            
            # Parse MRZ into structured data
            parsed_data = self._parse_mrz(mrz_text)
            
            if parsed_data.get("status") != "SUCCESS":
                self.logger.warning(f"⚠️ MRZ parsing failed: {parsed_data.get('status_messages', [])}")
                return {}
            
            # Convert to ExtractedField objects
            fields = {}
            confidence = 0.95  # FastMRZ typically has high confidence
            
            # Map MRZ fields to our field names
            field_mapping = {
                'document_number': 'passport_number',
                'surname': 'last_name',
                'given_name': 'first_name',
                'nationality_code': 'nationality',
                'birth_date': 'date_of_birth',
                'expiry_date': 'expiry_date',
                'sex': 'gender',
                'issuer_code': 'issuing_country'
            }
            
            for mrz_field, our_field in field_mapping.items():
                if mrz_field in parsed_data:
                    value = str(parsed_data[mrz_field])
                    
                    # Format dates properly
                    if 'date' in mrz_field and value:
                        value = self._format_date_for_dbs(value)
                    
                    # Clean names
                    if 'name' in our_field:
                        value = self._clean_name(value)
                    
                    fields[our_field] = ExtractedField(
                        value=value,
                        confidence=confidence,
                        extraction_method=ExtractionMethod.FAST_MRZ,
                        field_type='date' if 'date' in our_field else 'text',
                        required=our_field in ['passport_number', 'first_name', 'last_name', 'date_of_birth'],
                        editable=True
                    )
            
            # Add document type
            if 'document_code' in parsed_data:
                fields['document_type'] = ExtractedField(
                    value=parsed_data['document_code'],
                    confidence=confidence,
                    extraction_method=ExtractionMethod.FAST_MRZ,
                    field_type='text',
                    required=False,
                    editable=False
                )
            
            self.logger.info(f"✅ FastMRZ extracted {len(fields)} fields from passport")
            return fields
            
        except Exception as e:
            self.logger.error(f"❌ FastMRZ field extraction failed: {e}")
            return {}
    
    def get_confidence(self) -> float:
        """Get overall confidence score"""
        return 0.95  # FastMRZ typically has high confidence for valid MRZ
    
    def get_engine_info(self) -> Dict[str, str]:
        """Get engine version and metadata"""
        return {
            "engine": "FastMRZ",
            "version": "1.0.0",
            "model_path": self.model_path,
            "tesseract_path": self.tesseract_path,
            "supports": "Passport MRZ extraction"
        }
    
    def _process_image(self, image: np.ndarray) -> np.ndarray:
        """Preprocess image for MRZ detection"""
        # Resize to model input size
        processed = cv2.resize(image, (256, 256), interpolation=cv2.INTER_NEAREST)
        processed = np.asarray(np.float32(processed / 255))
        
        # Ensure 3 channels
        if len(processed.shape) >= 3:
            processed = processed[:, :, :3]
        
        # Reshape for ONNX model
        processed = np.reshape(processed, (1, 256, 256, 3))
        
        return processed
    
    def _get_roi_text(self, output_data: np.ndarray, original_image: np.ndarray) -> str:
        """Extract text from MRZ region with improved detection"""
        try:
            # Try ONNX model segmentation first
            onnx_roi = self._extract_roi_from_onnx(output_data, original_image)

            # If ONNX segmentation fails or gives poor results, use fallback
            if onnx_roi is None:
                self.logger.info("🔄 ONNX segmentation failed, using fallback MRZ detection")
                roi = self._extract_roi_fallback(original_image)
            else:
                roi = onnx_roi

            if roi is None:
                return ""

            # Extract text from ROI with improved OCR
            text = self._extract_text_from_roi_improved(roi)
            return text

        except Exception as e:
            self.logger.error(f"❌ ROI text extraction failed: {e}")
            return ""

    def _extract_roi_from_onnx(self, output_data: np.ndarray, original_image: np.ndarray) -> np.ndarray:
        """Extract ROI using ONNX model segmentation"""
        try:
            # Process segmentation output
            output_data = (output_data[0, :, :, 0] > 0.25) * 1
            output_data = np.uint8(output_data * 255)

            # Resize to original image size
            mask = cv2.resize(output_data, (original_image.shape[1], original_image.shape[0]))

            # Morphological operations to clean mask
            kernel = np.ones((5, 5), dtype=np.float32)
            mask = cv2.erode(mask, kernel, iterations=1)

            # Find contours
            contours, _ = cv2.findContours(mask.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)

            if len(contours) == 0:
                return None

            # Get largest contour (MRZ region)
            areas = [cv2.contourArea(c) for c in contours]
            largest_contour = contours[np.argmax(areas)]

            # Get bounding rectangle with padding
            x, y, w, h = cv2.boundingRect(largest_contour)

            # Validate that this looks like an MRZ region (should be in bottom half and wide)
            img_height = original_image.shape[0]
            if y < img_height * 0.5 or w < original_image.shape[1] * 0.5:
                self.logger.info("🔄 ONNX detected region doesn't look like MRZ, using fallback")
                return None

            padding = 10
            x_start = max(0, x - padding)
            y_start = max(0, y - padding)
            x_end = min(original_image.shape[1], x + w + padding)
            y_end = min(original_image.shape[0], y + h + padding)

            # Extract ROI
            roi = original_image[y_start:y_end, x_start:x_end].copy()
            return roi

        except Exception as e:
            self.logger.error(f"❌ ONNX ROI extraction failed: {e}")
            return None

    def _extract_roi_fallback(self, original_image: np.ndarray) -> np.ndarray:
        """Fallback MRZ region extraction using bottom portion"""
        try:
            # MRZ is typically in the bottom 15-25% of passport
            h, w = original_image.shape[:2]

            # Try different bottom portions
            for bottom_ratio in [0.2, 0.25, 0.3]:
                y_start = int(h * (1 - bottom_ratio))
                roi = original_image[y_start:, :].copy()

                # Quick validation - check if this region has text-like patterns
                gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)

                # Look for horizontal lines (typical in MRZ)
                edges = cv2.Canny(gray, 50, 150)
                horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (40, 1))
                horizontal_lines = cv2.morphologyEx(edges, cv2.MORPH_OPEN, horizontal_kernel)

                # If we find horizontal patterns, this might be MRZ
                if np.sum(horizontal_lines) > 100:  # Threshold for horizontal line detection
                    self.logger.info(f"🎯 Using fallback MRZ region: bottom {bottom_ratio*100:.0f}%")
                    return roi

            # If no good region found, use bottom 20%
            y_start = int(h * 0.8)
            roi = original_image[y_start:, :].copy()
            self.logger.info("🎯 Using default fallback: bottom 20%")
            return roi

        except Exception as e:
            self.logger.error(f"❌ Fallback ROI extraction failed: {e}")
            return None

    def _extract_text_from_roi_improved(self, roi: np.ndarray) -> str:
        """Extract text from ROI with improved OCR preprocessing"""
        try:
            # Convert to grayscale and enhance
            roi_gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)

            # Enhance contrast
            roi_enhanced = cv2.equalizeHist(roi_gray)

            # Apply morphological operations to clean up
            kernel = np.ones((2, 2), np.uint8)
            roi_enhanced = cv2.morphologyEx(roi_enhanced, cv2.MORPH_CLOSE, kernel)

            # Multiple threshold methods
            roi_thresh = cv2.threshold(roi_enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)[1]
            roi_adaptive = cv2.adaptiveThreshold(roi_enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)

            # Invert if background is dark
            if np.mean(roi_thresh) < 127:
                roi_thresh = cv2.bitwise_not(roi_thresh)
            if np.mean(roi_adaptive) < 127:
                roi_adaptive = cv2.bitwise_not(roi_adaptive)

            # OCR with improved configuration for MRZ-like text
            configs = [
                # MRZ-specific configs with character whitelist
                r'--oem 3 --psm 6 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789<',
                r'--oem 3 --psm 7 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789<',
                r'--oem 3 --psm 8 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789<',
                # Single line configs for MRZ
                r'--oem 3 --psm 13 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789<',
                # Fallback configs
                r'--oem 3 --psm 6',
                r'--oem 1 --psm 6'
            ]

            text = ""
            best_score = 0

            # Try both threshold methods
            for roi_image in [roi_thresh, roi_adaptive]:
                for config in configs:
                    try:
                        # Try available languages
                        for lang in ["ocrb", "eng"]:
                            try:
                                result = pytesseract.image_to_string(roi_image, lang=lang, config=config)
                                if result:
                                    # Score based on length and MRZ-like patterns
                                    score = self._score_mrz_text(result)
                                    if score > best_score:
                                        text = result
                                        best_score = score
                                        self.logger.info(f"🔍 Better OCR result (score: {score:.2f}) with {lang}")
                            except:
                                continue
                    except:
                        continue

            # If still no good result, try basic OCR
            if not text or best_score < 0.1:
                try:
                    text = pytesseract.image_to_string(roi_thresh, config=r'--oem 3 --psm 6')
                    self.logger.info("🔍 Using basic OCR fallback")
                except:
                    pass

            return text

        except Exception as e:
            self.logger.error(f"❌ Improved text extraction failed: {e}")
            return ""

    def _score_mrz_text(self, text: str) -> float:
        """Score OCR result based on MRZ-like characteristics"""
        if not text:
            return 0.0

        score = 0.0
        lines = text.strip().split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Look for MRZ patterns
            if line.startswith('P<'):
                score += 2.0  # Strong MRZ indicator
            elif '<' in line and len(line) > 20:
                score += 1.0  # Likely MRZ line
            elif any(c.isdigit() for c in line) and any(c.isalpha() for c in line):
                score += 0.5  # Mixed alphanumeric (common in MRZ)

            # Penalize very short lines
            if len(line) < 10:
                score -= 0.2

        return score
    
    def _cleanse_mrz(self, mrz_text: str) -> str:
        """Clean and validate MRZ text with improved logic"""
        if not mrz_text:
            return ""

        # Split into lines and clean each line
        lines = mrz_text.split("\n")
        cleaned_lines = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Remove spaces from potential MRZ lines
            line_no_spaces = line.replace(" ", "")

            # Look for MRZ-like patterns
            # MRZ lines typically start with P< or contain passport numbers
            is_mrz_line = (
                line_no_spaces.startswith("P<") or
                ("<" in line_no_spaces and len(line_no_spaces) >= 25) or
                (any(c.isdigit() for c in line_no_spaces) and any(c.isalpha() for c in line_no_spaces) and len(line_no_spaces) >= 25)
            )

            if is_mrz_line:
                # Clean up OCR errors in MRZ lines
                cleaned_line = self._clean_mrz_line(line_no_spaces)
                if cleaned_line:
                    cleaned_lines.append(cleaned_line)

        # If we found potential MRZ lines, return them
        if cleaned_lines:
            # Sort by length (longer lines first) and take top 2
            cleaned_lines.sort(key=len, reverse=True)
            return "\n".join(cleaned_lines[:2])

        # Fallback: look for any long alphanumeric lines
        for line in lines:
            line_clean = line.replace(" ", "").strip()
            if len(line_clean) >= 20 and any(c.isalnum() for c in line_clean):
                return line_clean

        return ""

    def _clean_mrz_line(self, line: str) -> str:
        """Clean OCR errors in MRZ line"""
        if not line:
            return ""

        # Common OCR corrections for MRZ
        corrections = {
            'c<c<x<': '<<<<<<',  # Common OCR error
            'c<c<': '<<<<',
            'x<': '<<',
            '0': 'O',  # In names, 0 is usually O
            '1': 'I',  # In names, 1 is usually I
        }

        cleaned = line
        for error, correction in corrections.items():
            cleaned = cleaned.replace(error, correction)

        # Ensure proper MRZ character set (A-Z, 0-9, <)
        result = ""
        for char in cleaned:
            if char.isalnum() or char == '<':
                result += char.upper()
            else:
                result += '<'  # Replace invalid chars with <

        return result
    
    def _get_checkdigit(self, input_string: str) -> str:
        """Calculate MRZ checkdigit"""
        weights_pattern = [7, 3, 1]

        total = 0
        for i, char in enumerate(input_string):
            if char.isdigit():
                value = int(char)
            elif char.isalpha():
                value = ord(char.upper()) - ord("A") + 10
            else:
                value = 0
            total += value * weights_pattern[i % len(weights_pattern)]

        return str(total % 10)

    def _parse_mrz(self, mrz_text: str, include_checkdigit: bool = True) -> Dict[str, Any]:
        """Parse MRZ text into structured data with improved error tolerance"""
        if not mrz_text:
            return {"status": "FAILURE", "status_messages": ["No MRZ detected"]}

        mrz_lines = [line.strip() for line in mrz_text.strip().split("\n") if line.strip()]

        # More flexible line count handling
        if len(mrz_lines) < 1:
            return {"status": "FAILURE", "status_messages": ["No valid MRZ lines found"]}

        # If we have only one line, try to split it or work with what we have
        if len(mrz_lines) == 1 and len(mrz_lines[0]) > 60:
            # Try to split long line into two parts
            line = mrz_lines[0]
            mid_point = len(line) // 2
            mrz_lines = [line[:mid_point], line[mid_point:]]

        mrz_code_dict = {}
        status_messages = []

        try:
            # Handle different line configurations
            if len(mrz_lines) >= 2:
                line1, line2 = mrz_lines[0], mrz_lines[1]

                # Determine MRZ type with more flexibility
                mrz_code_dict["mrz_type"] = "TD3"  # Default to TD3 (passport)

                # Extract document code (first 1-2 chars)
                if len(line1) >= 1:
                    mrz_code_dict["document_code"] = line1[0] if line1[0] in ['P', 'V', 'I'] else "P"

                # Extract issuer code (chars 2-5, with error tolerance)
                if len(line1) >= 5:
                    issuer_raw = line1[1:5] if line1[0] in ['P', 'V', 'I'] else line1[2:5]
                    # Clean issuer code
                    issuer_clean = ''.join(c for c in issuer_raw if c.isalpha() or c == '<')
                    mrz_code_dict["issuer_code"] = issuer_clean.replace('<', '').ljust(3, 'X')[:3]

                # Extract names with better error handling
                if len(line1) > 5:
                    name_part = line1[5:] if line1[0] in ['P', 'V', 'I'] else line1[6:]

                    # Handle different name separators
                    if "<<" in name_part:
                        names = name_part.split("<<")
                        mrz_code_dict["surname"] = self._clean_mrz_name(names[0])
                        mrz_code_dict["given_name"] = self._clean_mrz_name(names[1]) if len(names) > 1 else ""
                    elif "<" in name_part:
                        # Single < separator or multiple < characters
                        name_clean = name_part.replace("<", " ")
                        name_parts = [p.strip() for p in name_clean.split() if p.strip()]
                        if name_parts:
                            mrz_code_dict["surname"] = name_parts[0]
                            mrz_code_dict["given_name"] = " ".join(name_parts[1:]) if len(name_parts) > 1 else ""
                    else:
                        # No clear separator, try to extract what we can
                        mrz_code_dict["surname"] = name_part[:20].strip()
                        mrz_code_dict["given_name"] = ""

                # Extract data from line2 with error tolerance
                if len(line2) >= 9:
                    # Document number (first 9 chars, clean)
                    doc_num_raw = line2[:9]
                    mrz_code_dict["document_number"] = ''.join(c for c in doc_num_raw if c.isalnum()).ljust(9, '0')[:9]

                if len(line2) >= 13:
                    # Nationality (chars 10-13)
                    nationality_raw = line2[10:13]
                    mrz_code_dict["nationality_code"] = ''.join(c for c in nationality_raw if c.isalpha()).ljust(3, 'X')[:3]

                if len(line2) >= 19:
                    # Birth date (chars 13-19)
                    birth_raw = line2[13:19]
                    birth_clean = ''.join(c for c in birth_raw if c.isdigit()).ljust(6, '0')[:6]
                    if len(birth_clean) == 6:
                        mrz_code_dict["birth_date"] = self._format_mrz_date(birth_clean)

                if len(line2) >= 21:
                    # Sex (char 20)
                    sex_char = line2[20]
                    mrz_code_dict["sex"] = sex_char if sex_char in ['M', 'F'] else 'M'

                if len(line2) >= 27:
                    # Expiry date (chars 21-27)
                    expiry_raw = line2[21:27]
                    expiry_clean = ''.join(c for c in expiry_raw if c.isdigit()).ljust(6, '0')[:6]
                    if len(expiry_clean) == 6:
                        mrz_code_dict["expiry_date"] = self._format_mrz_date(expiry_clean)

                        # Adjust birth date based on expiry date
                        if "birth_date" in mrz_code_dict:
                            mrz_code_dict["birth_date"] = self._get_birth_date(
                                mrz_code_dict["birth_date"],
                                mrz_code_dict["expiry_date"]
                            )

            elif len(mrz_lines) == 1:
                # Single line processing - extract what we can
                line = mrz_lines[0]
                if len(line) >= 10:
                    mrz_code_dict["document_number"] = ''.join(c for c in line[:9] if c.isalnum())
                    status_messages.append("Partial MRZ data extracted from single line")

            # Validate we got some useful data
            required_fields = ['document_number', 'surname']
            if not any(field in mrz_code_dict for field in required_fields):
                return {"status": "FAILURE", "status_messages": ["No extractable data found in MRZ"]}

            # Success with warnings
            mrz_code_dict["status"] = "SUCCESS"
            if status_messages:
                mrz_code_dict["status_messages"] = status_messages
            else:
                mrz_code_dict["status_messages"] = ["MRZ successfully parsed"]

            return mrz_code_dict

        except Exception as e:
            self.logger.error(f"❌ MRZ parsing failed: {e}")
            return {"status": "FAILURE", "status_messages": [f"Parsing error: {str(e)}"]}

    def _clean_mrz_name(self, name_str: str) -> str:
        """Clean MRZ name string"""
        if not name_str:
            return ""

        # Remove < characters and clean
        cleaned = name_str.replace("<", " ").strip()
        # Remove extra spaces
        cleaned = " ".join(cleaned.split())
        return cleaned

    def _get_birth_date(self, birth_date_str: str, expiry_date_str: str) -> str:
        """Adjust birth date based on expiry date to handle century ambiguity"""
        if not birth_date_str or not expiry_date_str:
            return birth_date_str

        try:
            # Parse dates in DD/MM/YYYY format
            birth_parts = birth_date_str.split("/")
            expiry_parts = expiry_date_str.split("/")

            if len(birth_parts) != 3 or len(expiry_parts) != 3:
                return birth_date_str

            birth_year = int(birth_parts[2])
            expiry_year = int(expiry_parts[2])

            if expiry_year > birth_year:
                return birth_date_str

            # Adjust birth year by subtracting 100 years
            adjusted_year = birth_year - 100
            return f"{birth_parts[0]}/{birth_parts[1]}/{adjusted_year}"

        except (ValueError, IndexError):
            return birth_date_str

    def _format_mrz_date(self, date_str: str) -> str:
        """Format MRZ date (YYMMDD) to DD/MM/YYYY"""
        if not date_str or len(date_str) != 6:
            return ""

        try:
            # Parse YYMMDD format
            year = int(date_str[:2])
            month = int(date_str[2:4])
            day = int(date_str[4:6])

            # Handle century (assume 20xx for years < 30, 19xx for years >= 30)
            if year < 30:
                full_year = 2000 + year
            else:
                full_year = 1900 + year

            return f"{day:02d}/{month:02d}/{full_year}"

        except ValueError:
            return date_str
    
    def _format_date_for_dbs(self, date_str: str) -> str:
        """Format date for DBS requirements (DD/MM/YYYY)"""
        if not date_str:
            return ""
        
        # If already in DD/MM/YYYY format, return as is
        if "/" in date_str and len(date_str) == 10:
            return date_str
        
        # If in YYYY-MM-DD format, convert
        if "-" in date_str and len(date_str) == 10:
            try:
                year, month, day = date_str.split("-")
                return f"{day}/{month}/{year}"
            except ValueError:
                return date_str
        
        return date_str
    
    def _clean_name(self, name: str) -> str:
        """Clean and format name"""
        if not name:
            return ""
        
        # Remove extra spaces and title case
        cleaned = " ".join(name.split()).title()
        
        # Remove common prefixes
        prefixes = ["Mr", "Mrs", "Miss", "Ms", "Dr", "Prof"]
        for prefix in prefixes:
            if cleaned.startswith(f"{prefix} "):
                cleaned = cleaned[len(prefix) + 1:]
        
        return cleaned
