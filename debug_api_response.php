<?php

require_once __DIR__ . '/../api_interface/vendor/autoload.php';

use GuzzleHttp\Client;

$client = new Client([
    'base_uri' => 'http://localhost:8002',
    'timeout' => 30,
]);

try {
    // First, login to get a token
    $loginResponse = $client->post('/api/login', [
        'json' => [
            'email' => '<EMAIL>',
            'password' => 'password'
        ]
    ]);
    
    $loginData = json_decode($loginResponse->getBody(), true);
    $token = $loginData['data']['token'];
    
    echo "✅ Login successful, token: " . substr($token, 0, 20) . "...\n\n";
    
    // Now call the documents endpoint
    $documentsResponse = $client->get('/api/applications/1/documents', [
        'headers' => [
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json',
        ]
    ]);
    
    $documentsData = json_decode($documentsResponse->getBody(), true);
    
    echo "📄 Documents API Response:\n";
    echo "Status Code: " . $documentsResponse->getStatusCode() . "\n";
    echo "Success: " . ($documentsData['success'] ? 'true' : 'false') . "\n\n";
    
    if (isset($documentsData['data']['documents']['available_by_group'])) {
        echo "🔍 Available by group structure:\n";
        foreach ($documentsData['data']['documents']['available_by_group'] as $groupKey => $groupData) {
            echo "Group $groupKey:\n";
            echo "  Type: " . gettype($groupData) . "\n";
            if (is_array($groupData)) {
                echo "  Keys: " . implode(', ', array_keys($groupData)) . "\n";
                if (isset($groupData['group_name'])) {
                    echo "  Group Name: " . $groupData['group_name'] . "\n";
                    echo "  Document Count: " . $groupData['document_count'] . "\n";
                    echo "  Documents Type: " . gettype($groupData['documents']) . "\n";
                    echo "  Documents Count: " . count($groupData['documents']) . "\n";
                } else {
                    echo "  ❌ Missing group_name - this is the problem!\n";
                    echo "  Raw data: " . json_encode($groupData, JSON_PRETTY_PRINT) . "\n";
                }
            }
            echo "\n";
        }
    } else {
        echo "❌ No available_by_group found in response\n";
        echo "Full response: " . json_encode($documentsData, JSON_PRETTY_PRINT) . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
