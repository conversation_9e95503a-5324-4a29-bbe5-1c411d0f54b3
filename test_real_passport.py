#!/usr/bin/env python3
"""
Test the improved FastMRZ with a real passport image
"""

import requests
import json
import base64
from pathlib import Path

def create_test_passport_image():
    """Create a test passport image with proper MRZ"""
    import cv2
    import numpy as np
    
    # Create a passport-like image
    img = np.ones((600, 800, 3), dtype=np.uint8) * 240  # Light gray background
    
    # Add passport header
    cv2.putText(img, "UNITED KINGDOM OF GREAT BRITAIN AND NORTHERN IRELAND", 
                (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
    cv2.putText(img, "PASSPORT", (350, 100), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 0, 0), 3)
    
    # Add personal info
    cv2.putText(img, "Type/Type: P", (50, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "Country Code/Code du pays: GBR", (50, 180), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "Passport No./Passeport No.: *********", (50, 210), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "Surname/Nom: UK SPECIMEN", (50, 240), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "Given Names/Prenoms: ANGELA ZOE", (50, 270), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "Nationality/Nationalite: BRITISH CITIZEN", (50, 300), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "Date of birth/Date de naissance: 04 DEC/DEC 88", (50, 330), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "Sex/Sexe: F", (50, 360), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "Place of birth/Lieu de naissance: CROYDON", (50, 390), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "Date of issue/Date de delivrance: 18 AUG/AOUT 16", (50, 420), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "Date of expiry/Date d'expiration: 18 AUG/AOUT 26", (50, 450), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    
    # Add MRZ at the bottom (most important part)
    mrz_y_start = 520
    cv2.rectangle(img, (30, mrz_y_start - 10), (770, mrz_y_start + 60), (200, 200, 200), -1)  # MRZ background
    
    # MRZ Line 1: P<GBRUK<SPECIMEN<<ANGELA<ZOE<<<<<<<<<<<<<<<<
    cv2.putText(img, "P<GBRUK<SPECIMEN<<ANGELA<ZOE<<<<<<<<<<<<<<<<", 
                (40, mrz_y_start + 20), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
    
    # MRZ Line 2: *********ZGBR8812049F2608189<<<<<<<<<<<<<08
    cv2.putText(img, "*********ZGBR8812049F2608189<<<<<<<<<<<<<08", 
                (40, mrz_y_start + 50), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
    
    # Save the test image
    test_path = "/tmp/test_passport_mrz.jpg"
    cv2.imwrite(test_path, img)
    print(f"✅ Created test passport image: {test_path}")
    return test_path

def test_improved_fastmrz():
    """Test the improved FastMRZ implementation"""
    
    print("🧪 Testing Improved FastMRZ Implementation")
    print("=" * 50)
    
    # Create test passport image
    image_path = create_test_passport_image()
    
    # Read the image
    with open(image_path, 'rb') as f:
        image_data = f.read()
    
    # Test the SolidTech API
    url = 'http://127.0.0.1:9000/api/v1/documents/ai-extract'
    
    files = {
        'file': ('passport.jpg', image_data, 'image/jpeg')
    }
    
    data = {
        'document_type': 'passport',
        'application_id': 'test_improved_mrz'
    }
    
    print(f"📤 Sending request to: {url}")
    
    try:
        response = requests.post(url, files=files, data=data, timeout=30)
        
        print(f"📥 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ SUCCESS! Improved FastMRZ processing completed")
            print("\n📋 Extracted Fields:")
            print("=" * 50)
            
            extracted_fields = result.get('extracted_fields', {})
            
            # Expected values from our test image
            expected_values = {
                'passport_number': '*********Z',
                'first_name': 'ANGELA',
                'last_name': 'UK SPECIMEN',
                'date_of_birth': '04/12/1988',
                'expiry_date': '18/08/2026',
                'nationality': 'GBR',
                'gender': 'F',
                'issuing_country': 'GBR'
            }
            
            # Check each field
            for field_name, expected_value in expected_values.items():
                if field_name in extracted_fields:
                    field_data = extracted_fields[field_name]
                    actual_value = field_data.get('value', 'N/A')
                    confidence = field_data.get('confidence', 0)
                    method = field_data.get('extraction_method', 'unknown')
                    
                    # Check if extraction is correct
                    is_correct = "✅" if actual_value == expected_value else "❌"
                    
                    print(f"  {field_name:15}: {actual_value} {is_correct} (expected: {expected_value})")
                    print(f"                   confidence: {confidence:.2f}, method: {method}")
                else:
                    print(f"  {field_name:15}: NOT EXTRACTED ❌ (expected: {expected_value})")
            
            # Overall assessment
            extracted_count = len([f for f in expected_values.keys() if f in extracted_fields])
            total_expected = len(expected_values)
            success_rate = (extracted_count / total_expected) * 100
            
            print(f"\n📊 Extraction Summary:")
            print(f"  Fields extracted: {extracted_count}/{total_expected} ({success_rate:.1f}%)")
            print(f"  Overall confidence: {result.get('confidence', 0):.2f}")
            print(f"  Processing method: {result.get('processing_method', 'unknown')}")
            
            # Fraud analysis
            fraud_analysis = result.get('fraud_analysis', {})
            print(f"\n🛡️ Fraud Analysis:")
            print(f"  Is authentic: {fraud_analysis.get('is_authentic', 'unknown')}")
            print(f"  Confidence score: {fraud_analysis.get('confidence_score', 0):.2f}")
            
            # Recommendations
            recommendations = result.get('recommendations', [])
            if recommendations:
                print(f"\n💡 Recommendations:")
                for rec in recommendations:
                    print(f"  • {rec}")

            # Show all extracted fields for debugging
            print(f"\n🔧 All Extracted Fields (for debugging):")
            for field_name, field_data in extracted_fields.items():
                print(f"  {field_name}: {field_data}")

            # Success criteria
            if success_rate >= 70 and result.get('confidence', 0) > 0.5:
                print(f"\n🎉 IMPROVEMENT SUCCESS! FastMRZ is working much better!")
                print(f"   ✅ Extraction rate: {success_rate:.1f}% (target: ≥70%)")
                print(f"   ✅ Confidence: {result.get('confidence', 0):.2f} (target: >0.5)")
            else:
                print(f"\n⚠️ Still needs improvement:")
                if success_rate < 70:
                    print(f"   ❌ Low extraction rate: {success_rate:.1f}% (target: ≥70%)")
                if result.get('confidence', 0) <= 0.5:
                    print(f"   ❌ Low confidence: {result.get('confidence', 0):.2f} (target: >0.5)")
            
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    # Check if server is running
    try:
        health_response = requests.get('http://127.0.0.1:9000/health', timeout=5)
        if health_response.status_code == 200:
            print("✅ SolidTech server is running")
            test_improved_fastmrz()
        else:
            print("❌ SolidTech server health check failed")
    except:
        print("❌ SolidTech server is not running")
        print("   Start it with: cd /home/<USER>/Work/SolidTech && python3 mvvm_server.py")
