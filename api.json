{"openapi": "3.1.0", "info": {"title": "API Interface Documentation", "version": "1.0.0", "description": "API interface for Flutter application to manage entities, applications, and user authentication for portal users."}, "servers": [{"url": "http://localhost:8001/api", "description": "Local"}], "security": [{"http": []}], "paths": {"/v1/applications/{applicationId}/form-data": {"get": {"operationId": "application.getApplicationFormData", "tags": ["Application"], "parameters": [{"name": "applicationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"application_id": {"type": "string"}, "external_reference": {"type": "string"}, "status": {"type": "string"}, "form_data": {"type": "string"}, "form_fields": {"type": "string"}, "completion_status": {"type": "object", "properties": {"is_started": {"type": "boolean"}, "is_complete": {"type": "boolean"}, "status_text": {"type": "string", "enum": ["In progress", "Complete", "Not started"]}}, "required": ["is_started", "is_complete", "status_text"]}, "product": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "code": {"type": "string"}, "variant": {"type": "string"}}, "required": ["id", "name", "code", "variant"]}}, "required": ["application_id", "external_reference", "status", "form_data", "form_fields", "completion_status", "product"]}, "message": {"type": "string", "example": "Application form data retrieved successfully"}}, "required": ["success", "data", "message"]}}}}, "404": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Application not found"}}, "required": ["success", "message"]}}}}, "400": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Invalid application ID"}}, "required": ["success", "message"]}}}}, "403": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Access denied for this user type"}}, "required": ["success", "message"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/v1/applications/save-data": {"post": {"operationId": "application.saveApplicationData", "tags": ["Application"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveApplicationDataRequest"}}}}, "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"application_id": {"type": "string"}}, "required": ["application_id"]}, "message": {"type": "string", "example": "Application data saved successfully"}}, "required": ["success", "data", "message"]}}}}, "402": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Payment required before saving application data"}}, "required": ["success", "message"]}}}}, "422": {"$ref": "#/components/responses/ValidationException"}, "404": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Application not found"}}, "required": ["success", "message"]}}}}, "403": {"$ref": "#/components/responses/AuthorizationException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/v1/applicants/{applicantId}": {"get": {"operationId": "application.getApplicantDetails", "tags": ["Application"], "parameters": [{"name": "applicantId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"applicant": {"type": "object", "properties": {"id": {"type": "string"}, "email": {"type": "string"}, "user_type": {"type": "string"}, "profile": {"type": "object", "properties": {"first_name": {"type": "string"}, "last_name": {"type": "string"}, "full_name": {"type": ["string", "null"]}, "telephone": {"type": "string"}, "address": {"type": "string"}, "date_of_birth": {"type": "string"}}, "required": ["first_name", "last_name", "full_name", "telephone", "address", "date_of_birth"]}, "entities": {"type": "string"}}, "required": ["id", "email", "user_type", "profile", "entities"]}, "applications": {"type": "string"}, "total_applications": {"type": "string"}}, "required": ["applicant", "applications", "total_applications"]}, "message": {"type": "string", "example": "Applicant details retrieved successfully"}}, "required": ["success", "data", "message"]}}}}, "404": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Applicant not found"}}, "required": ["success", "message"]}}}}, "403": {"description": "", "content": {"application/json": {"schema": {"anyOf": [{"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "You do not have access to this applicant"}}, "required": ["success", "message"]}, {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "You can only access your own data"}}, "required": ["success", "message"]}, {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Access denied for this user type"}}, "required": ["success", "message"]}]}}}}, "400": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Invalid applicant ID"}}, "required": ["success", "message"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/v1/client-applicants": {"get": {"operationId": "application.getClientApplicants", "tags": ["Application"], "parameters": [{"name": "search", "in": "query", "schema": {"type": "string", "maxLength": 255}}, {"name": "status_filter", "in": "query", "schema": {"type": "string", "enum": ["all", "in_progress", "further_action_pending", "staff_review_pending", "complete"]}}, {"name": "page", "in": "query", "schema": {"type": "integer", "minimum": 1}}, {"name": "per_page", "in": "query", "schema": {"type": "integer", "minimum": 1, "maximum": 100}}], "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"anyOf": [{"type": "object", "properties": {"status_summary": {"type": "object", "properties": {"total_applicants": {"type": "string"}, "in_progress": {"type": "integer", "example": 0}, "further_action_pending": {"type": "integer", "example": 0}, "staff_review_pending": {"type": "integer", "example": 0}, "complete": {"type": "integer", "example": 0}}, "required": ["total_applicants", "in_progress", "further_action_pending", "staff_review_pending", "complete"]}, "applicants": {"type": "array", "items": {"type": "string"}, "minItems": 0, "maxItems": 0, "additionalItems": false}, "pagination": {"type": "object", "properties": {"current_page": {"type": "integer"}, "per_page": {"type": "integer"}, "total": {"type": "string"}, "total_pages": {"type": "string"}, "has_more": {"type": "string"}}, "required": ["current_page", "per_page", "total", "total_pages", "has_more"]}}, "required": ["status_summary", "applicants", "pagination"]}, {"type": "object", "properties": {"status_summary": {"type": "object", "properties": {"total_applicants": {"type": "integer", "example": 0}, "in_progress": {"type": "integer", "example": 0}, "further_action_pending": {"type": "integer", "example": 0}, "staff_review_pending": {"type": "integer", "example": 0}, "complete": {"type": "integer", "example": 0}}, "required": ["total_applicants", "in_progress", "further_action_pending", "staff_review_pending", "complete"]}, "applicants": {"type": "array", "items": {"type": "string"}, "minItems": 0, "maxItems": 0, "additionalItems": false}, "pagination": {"type": "object", "properties": {"current_page": {"type": "integer", "example": 1}, "per_page": {"type": "integer", "example": 20}, "total": {"type": "integer", "example": 0}, "total_pages": {"type": "integer", "example": 0}, "has_more": {"type": "boolean"}}, "required": ["current_page", "per_page", "total", "total_pages", "has_more"]}}, "required": ["status_summary", "applicants", "pagination"]}]}, "message": {"type": "string", "example": "Client applicants retrieved successfully"}}, "required": ["success", "data", "message"]}}}}, "403": {"$ref": "#/components/responses/AuthorizationException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}}}}, "/v1/addapplicant": {"post": {"operationId": "application.createApplicant", "tags": ["Application"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateApplicantRequest"}}}}, "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}}}}, "400": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"applicant": {"type": "object", "properties": {"id": {"type": "string"}, "email": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "phone": {"type": "string"}, "full_name": {"type": "string"}}, "required": ["id", "email", "first_name", "last_name", "phone", "full_name"]}, "entity": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "entity_code": {"type": "string"}}, "required": ["id", "name", "entity_code"]}, "job_role": {"type": "object", "properties": {"id": {"type": "string"}, "job_label": {"type": "string"}, "job_title": {"type": "string"}}, "required": ["id", "job_label", "job_title"]}, "applications": {"type": "string"}, "generated_password": {"type": "string"}, "total_applications": {"type": "string"}, "products_auto_selected": {"type": "string"}}, "required": ["applicant", "entity", "job_role", "applications", "generated_password", "total_applications", "products_auto_selected"]}, "message": {"type": "string"}}, "required": ["success", "data", "message"]}}}}, "403": {"$ref": "#/components/responses/AuthorizationException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}}}}, "/v1/auth/login": {"post": {"operationId": "auth.login", "tags": ["<PERSON><PERSON>"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"anyOf": [{"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"user": {"type": "object", "properties": {"id": {"type": "string"}, "email": {"type": "string"}, "user_type": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "phone": {"type": "string"}, "address": {"type": "string"}, "two_factor_enabled": {"type": "string"}}, "required": ["id", "email", "user_type", "first_name", "last_name", "phone", "address", "two_factor_enabled"]}, "token": {"type": "string"}, "token_type": {"type": "string", "example": "Bearer"}}, "required": ["user", "token", "token_type"]}, "message": {"type": "string", "enum": ["Applicant logged in successfully", "User logged in successfully"]}}, "required": ["success", "data", "message"]}, {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"requires_two_factor": {"type": "boolean"}, "message": {"type": "string", "example": "Two-factor authentication required"}}, "required": ["requires_two_factor", "message"]}, "message": {"type": "string", "example": "Two-factor authentication required"}}, "required": ["success", "data", "message"]}, {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"requires_two_factor_setup": {"type": "boolean"}, "qr_code_image": {"type": "string"}, "temp_secret": {"type": "string"}, "message": {"type": "string", "description": "Temporary secret for verification", "example": "Please set up two-factor authentication by scanning the QR code"}}, "required": ["requires_two_factor_setup", "qr_code_image", "temp_secret", "message"]}, "message": {"type": "string", "example": "Two-factor authentication setup required"}}, "required": ["success", "data", "message"]}]}}}}, "403": {"$ref": "#/components/responses/AuthorizationException"}, "401": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Invalid credentials"}}, "required": ["success", "message"]}}}}, "422": {"$ref": "#/components/responses/ValidationException"}}}}, "/v1/auth/verify-pin": {"post": {"operationId": "auth.verify<PERSON>in", "tags": ["<PERSON><PERSON>"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "username": {"type": "string"}, "password": {"type": "string"}, "pin": {"type": "string", "minLength": 6, "maxLength": 6}, "temp_secret": {"type": "string"}}, "required": ["password", "pin"]}}}}, "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"user": {"type": "object", "properties": {"id": {"type": "string"}, "email": {"type": "string"}, "user_type": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "phone": {"type": "string"}, "address": {"type": "string"}, "two_factor_enabled": {"type": "string"}}, "required": ["id", "email", "user_type", "first_name", "last_name", "phone", "address", "two_factor_enabled"]}, "token": {"type": "string"}, "token_type": {"type": "string", "example": "Bearer"}}, "required": ["user", "token", "token_type"]}, "message": {"type": "string", "enum": ["Applicant logged in successfully with two-factor authentication", "User logged in successfully with two-factor authentication"]}}, "required": ["success", "data", "message"]}}}}, "401": {"description": "", "content": {"application/json": {"schema": {"anyOf": [{"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Invalid two-factor authentication PIN"}}, "required": ["success", "message"]}, {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Invalid credentials"}}, "required": ["success", "message"]}]}}}}, "404": {"description": "", "content": {"application/json": {"schema": {"anyOf": [{"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Two-factor authentication is not set up for this user"}}, "required": ["success", "message"]}, {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Two-factor authentication is not enabled for this user"}}, "required": ["success", "message"]}]}}}}, "403": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "User account is not active"}}, "required": ["success", "message"]}}}}, "422": {"$ref": "#/components/responses/ValidationException"}}}}, "/v1/auth/logout": {"post": {"operationId": "auth.logout", "tags": ["<PERSON><PERSON>"], "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"type": "string"}, "minItems": 0, "maxItems": 0, "additionalItems": false}, "message": {"type": "string", "example": "User logged out successfully"}}, "required": ["success", "data", "message"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/v1/auth/change-password": {"post": {"operationId": "auth.changePassword", "summary": "Change user password", "tags": ["<PERSON><PERSON>"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}}}, "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}}}}, "422": {"$ref": "#/components/responses/ValidationException"}, "200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"user_id": {"type": "string"}, "email": {"type": "string"}, "tokens_invalidated": {"type": "string"}, "changed_at": {"type": "string"}, "security_notice": {"type": "array", "prefixItems": [{"type": "string", "example": "Your password has been successfully changed."}, {"type": "string", "example": "If you did not make this change, please contact support immediately."}], "minItems": 2, "maxItems": 2, "additionalItems": false}}, "required": ["user_id", "email", "tokens_invalidated", "changed_at", "security_notice"]}}, "required": ["success", "message", "data"]}}}}, "403": {"$ref": "#/components/responses/AuthorizationException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/v1/billing/payment-status/{applicantId}": {"get": {"operationId": "billing.getPaymentStatus", "tags": ["Billing"], "parameters": [{"name": "applicantId", "in": "path", "required": true, "schema": {"type": "integer"}}, {"name": "applicantId", "in": "query", "required": true, "schema": {"type": "integer"}}], "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"payment_required": {"type": "string"}, "total_amount": {"type": "integer", "example": 0}, "currency": {"type": "string", "example": "GBP"}, "applications": {"type": "array", "items": {"type": "string"}, "minItems": 0, "maxItems": 0, "additionalItems": false}, "total_applications_requiring_payment": {"type": "string"}}, "required": ["payment_required", "total_amount", "currency", "applications", "total_applications_requiring_payment"]}, "message": {"type": "string", "example": "Payment status retrieved successfully"}}, "required": ["success", "data", "message"]}}}}, "403": {"$ref": "#/components/responses/AuthorizationException"}, "404": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Applicant not found"}}, "required": ["success", "message"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}}}}, "/v1/dbs/allowed-fields": {"get": {"operationId": "dbs.getAllowedFields", "summary": "Get allowed field names for a product", "tags": ["DBS"], "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"product_id": {"type": "integer"}, "allowed_fields": {"type": "array", "items": {}}, "field_count": {"type": "string"}}, "required": ["product_id", "allowed_fields", "field_count"]}, "message": {"type": "string", "example": "Allowed field names retrieved successfully"}}, "required": ["success", "data", "message"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/v1/applications/{id}/documents": {"get": {"operationId": "documentNomination.getDocuments", "summary": "Get available documents and route information for an application", "tags": ["DocumentNomination"], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Application ID", "schema": {"type": "integer"}}], "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Failed to retrieve document information"}}, "required": ["success", "message"]}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"applicant_context": {"type": "object", "properties": {"nationality": {"type": "string"}, "current_address_country": {"type": "string"}, "is_uk_national": {"type": "boolean"}, "is_uk_resident": {"type": "boolean"}, "work_type": {"type": "string", "example": "paid"}, "product_code": {"type": "string"}}, "required": ["nationality", "current_address_country", "is_uk_national", "is_uk_resident", "work_type", "product_code"]}, "recommended_route": {"type": "integer"}, "available_routes": {"type": "array", "items": {}}, "route_requirements": {"type": "array", "items": {"type": "string"}, "minItems": 0, "maxItems": 0, "additionalItems": false}, "available_documents": {"type": "array", "items": {}}, "current_nominations": {"type": "string"}}, "required": ["applicant_context", "recommended_route", "available_routes", "route_requirements", "available_documents", "current_nominations"]}, "message": {"type": "string", "example": "Document information retrieved successfully"}}, "required": ["success", "data", "message"]}}}}, "404": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Application not found"}}, "required": ["success", "message"]}}}}, "400": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Invalid application ID"}}, "required": ["success", "message"]}}}}, "403": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Access denied for this user type"}}, "required": ["success", "message"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/v1/applications/{id}/validate": {"post": {"operationId": "documentNomination.validateDocuments", "summary": "Validate document nominations", "tags": ["DocumentNomination"], "parameters": [{"name": "id", "in": "path", "required": true, "description": "Application ID", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"route_number": {"type": "integer", "minimum": 1, "maximum": 3}, "nominated_documents": {"type": "array", "items": {"type": "object", "properties": {"document_type_id": {"type": "integer"}, "document_data": {"type": "array", "items": {"type": "string"}}, "confirms_address": {"type": "boolean"}}, "required": ["document_type_id", "document_data"]}, "minItems": 1}}, "required": ["route_number", "nominated_documents"]}}}}, "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Failed to validate documents"}}, "required": ["success", "message"]}}}}, "422": {"$ref": "#/components/responses/ValidationException"}, "200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"validation_result": {"type": "object", "properties": {"is_valid": {"type": "boolean"}, "route_completed": {"type": "boolean"}, "can_proceed": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}, "minItems": 0, "maxItems": 0, "additionalItems": false}}, "required": ["is_valid", "route_completed", "can_proceed", "errors"]}, "route_analysis": {"type": "object", "properties": {"route_number": {"type": "string"}, "requirements_met": {"type": "array", "items": {"type": "string"}, "minItems": 0, "maxItems": 0, "additionalItems": false}}, "required": ["route_number", "requirements_met"]}, "document_validations": {"type": "array", "items": {"type": "string"}, "minItems": 0, "maxItems": 0, "additionalItems": false}, "next_steps": {"anyOf": [{"type": "object", "properties": {"action": {"type": "string", "example": "add_documents"}, "message": {"type": "string"}, "alternative_actions": {"type": "array", "items": {"type": "string"}, "minItems": 0, "maxItems": 0, "additionalItems": false}}, "required": ["action", "message", "alternative_actions"]}, {"type": "object", "properties": {"action": {"type": "string", "example": "complete_route"}, "message": {"type": "string"}, "alternative_actions": {"type": "array", "items": {"type": "string"}, "minItems": 0, "maxItems": 0, "additionalItems": false}}, "required": ["action", "message", "alternative_actions"]}]}}, "required": ["validation_result", "route_analysis", "document_validations", "next_steps"]}, "message": {"type": "string", "enum": ["Documents validated and saved successfully", "Document validation failed"]}}, "required": ["success", "data", "message"]}}}}, "404": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Application not found"}}, "required": ["success", "message"]}}}}, "400": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Invalid application ID"}}, "required": ["success", "message"]}}}}, "403": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Access denied for this user type"}}, "required": ["success", "message"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/v1/job-roles": {"get": {"operationId": "jobRole.getJobRoles", "description": "Retrieve all job roles with complete data for both table display and applications", "summary": "Get job roles", "tags": ["JobRole"], "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"anyOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}, "minItems": 0, "maxItems": 0, "additionalItems": false}]}, "message": {"type": "string", "example": "Job roles retrieved successfully"}}, "required": ["success", "data", "message"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "post": {"operationId": "jobRole.store", "description": "Create a new job role for any client where this user is associated with", "summary": "Create job role", "tags": ["JobRole"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateJobRoleRequest"}}}}, "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"id": {"type": "string", "description": "Table display data"}, "job_label": {"type": "string"}, "job_title": {"type": "string"}, "job_workforce": {"type": "string"}, "role_description": {"type": "string"}, "self_payment": {"type": "string"}, "employment_sector": {"type": "string"}, "entity_name": {"type": "string"}, "entity_code": {"type": "string"}, "job_role_name": {"type": "string", "description": "Application data"}, "organisation_name": {"type": "string"}, "entity_id": {"type": "string"}, "products": {"anyOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}, "minItems": 0, "maxItems": 0, "additionalItems": false}]}}, "required": ["id", "job_label", "job_title", "job_workforce", "role_description", "self_payment", "employment_sector", "entity_name", "entity_code", "job_role_name", "organisation_name", "entity_id", "products"]}, "message": {"type": "string", "example": "Job role created successfully"}}, "required": ["success", "data", "message"]}}}}, "404": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Entity not found or inactive"}}, "required": ["success", "message"]}}}}, "403": {"$ref": "#/components/responses/AuthorizationException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}, "422": {"$ref": "#/components/responses/ValidationException"}}}}, "/v1/job-roles/{jobRole}": {"put": {"operationId": "jobRole.update", "description": "Update a job role (client users only)", "summary": "Update job role", "tags": ["JobRole"], "parameters": [{"name": "jobRole", "in": "path", "required": true, "description": "The job role ID", "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"job_label": {"type": "string", "maxLength": 255}, "job_title": {"type": "string", "maxLength": 255}, "job_workforce": {"type": "string", "maxLength": 255}, "role_description": {"type": ["string", "null"]}, "self_payment": {"type": ["boolean", "null"]}, "employment_sector": {"type": ["string", "null"], "maxLength": 255}, "product_ids": {"type": ["array", "null"], "items": {"type": "integer"}}}}}}}, "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"id": {"type": "string", "description": "Table display data"}, "job_label": {"type": "string"}, "job_title": {"type": "string"}, "job_workforce": {"type": "string"}, "role_description": {"type": "string"}, "self_payment": {"type": "string"}, "employment_sector": {"type": "string"}, "entity_name": {"type": "string"}, "entity_code": {"type": "string"}, "job_role_name": {"type": "string", "description": "Application data"}, "organisation_name": {"type": "string"}, "entity_id": {"type": "string"}, "products": {"anyOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}, "minItems": 0, "maxItems": 0, "additionalItems": false}]}}, "required": ["id", "job_label", "job_title", "job_workforce", "role_description", "self_payment", "employment_sector", "entity_name", "entity_code", "job_role_name", "organisation_name", "entity_id", "products"]}, "message": {"type": "string", "example": "Job role updated successfully"}}, "required": ["success", "data", "message"]}}}}, "403": {"description": "", "content": {"application/json": {"schema": {"anyOf": [{"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "You do not have access to this job role"}}, "required": ["success", "message"]}, {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Only client users can update job roles"}}, "required": ["success", "message"]}]}}}}, "422": {"$ref": "#/components/responses/ValidationException"}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "delete": {"operationId": "jobRole.destroy", "description": "Delete a job role (client users only)", "summary": "Delete job role", "tags": ["JobRole"], "parameters": [{"name": "jobRole", "in": "path", "required": true, "description": "The job role ID", "schema": {"type": "integer"}}], "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"type": "string"}, "minItems": 0, "maxItems": 0, "additionalItems": false}, "message": {"type": "string", "example": "Job role deleted successfully"}}, "required": ["success", "data", "message"]}}}}, "403": {"description": "", "content": {"application/json": {"schema": {"anyOf": [{"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "You do not have access to this job role"}}, "required": ["success", "message"]}, {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Only client users can delete job roles"}}, "required": ["success", "message"]}]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}}, "components": {"securitySchemes": {"http": {"type": "http", "description": "Laravel Sanctum Bearer Token Authentication. Use the token received from the login endpoint.", "scheme": "bearer", "bearerFormat": "sanctum"}}, "schemas": {"ChangePasswordRequest": {"type": "object", "properties": {"current_password": {"type": "string", "minLength": 1}, "new_password": {"type": "string"}, "new_password_confirmation": {"type": "string"}, "invalidate_all_tokens": {"type": "boolean"}}, "required": ["current_password", "new_password", "new_password_confirmation"], "title": "ChangePasswordRequest"}, "CreateApplicantRequest": {"type": "object", "properties": {"first_name": {"type": "string", "maxLength": 255}, "last_name": {"type": "string", "maxLength": 255}, "email": {"type": "string", "format": "email", "maxLength": 255}, "phone": {"type": "string", "maxLength": 20}, "job_role_id": {"type": "integer"}}, "required": ["first_name", "last_name", "email", "phone", "job_role_id"], "title": "CreateApplicantRequest"}, "CreateJobRoleRequest": {"type": "object", "properties": {"entity_id": {"type": "integer"}, "job_label": {"type": "string", "maxLength": 255}, "job_title": {"type": "string", "maxLength": 255}, "job_workforce": {"type": "string", "maxLength": 255}, "role_description": {"type": ["string", "null"]}, "self_payment": {"type": ["boolean", "null"]}, "employment_sector": {"type": ["string", "null"], "maxLength": 255}, "product_ids": {"type": ["array", "null"], "items": {"type": "integer"}}}, "required": ["entity_id", "job_label", "job_title", "job_workforce"], "title": "CreateJobRoleRequest"}, "LoginRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string"}}, "required": ["email", "password"], "title": "LoginRequest"}, "SaveApplicationDataRequest": {"type": "object", "properties": {"application_id": {"type": "integer"}, "form_data": {"type": "array", "items": {"type": "string"}, "minItems": 1}}, "required": ["application_id"], "title": "SaveApplicationDataRequest"}}, "responses": {"ValidationException": {"description": "Validation error", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "Errors overview."}, "errors": {"type": "object", "description": "A detailed description of each field that failed validation.", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}, "required": ["message", "errors"]}}}}, "AuthorizationException": {"description": "Authorization error", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "Error overview."}}, "required": ["message"]}}}}, "AuthenticationException": {"description": "Unauthenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "Error overview."}}, "required": ["message"]}}}}, "ModelNotFoundException": {"description": "Not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "Error overview."}}, "required": ["message"]}}}}}}}