# Application Configuration
APP_NAME=SolidTech Document Verification
APP_VERSION=1.0.0
DEBUG=false
ENVIRONMENT=production

# Microsoft Document Intelligence Configuration
AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT=https://soliddoc.cognitiveservices.azure.com/
AZURE_DOCUMENT_INTELLIGENCE_KEY=8QYuYReVUzDCvvEKCGM0eHnFiQn6OqcY0Fntmk93eZUyyBX7KPSjJQQJ99BGACmepeSXJ3w3AAALACOGYtqn
ENABLE_MICROSOFT_AI=true
ENABLE_FALLBACK_OCR=true

# Server Configuration
HOST=0.0.0.0
PORT=8000
WORKERS=4
RELOAD=false

# Security Configuration
SECRET_KEY=your-super-secret-key-change-this-in-production
JWT_SECRET_KEY=your-jwt-secret-key-change-this-in-production
JWT_ALGORITHM=RS256
JWT_EXPIRE_MINUTES=30
ENCRYPTION_KEY=your-32-byte-encryption-key-here

# RSA Key Paths
RSA_PRIVATE_KEY_PATH=./config/keys/private_key.pem
RSA_PUBLIC_KEY_PATH=./config/keys/public_key.pem

# Database Configuration
DATABASE_URL=postgresql+asyncpg://solidtech:password@localhost:5432/solidtech_db
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30
DATABASE_POOL_TIMEOUT=30

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=your-redis-password
REDIS_MAX_CONNECTIONS=20

# ML Model Configuration
MODEL_PATH=./models/trained
DEVICE=cuda  # cuda or cpu
BATCH_SIZE=32
MAX_IMAGE_SIZE=2048
SUPPORTED_FORMATS=jpg,jpeg,png,pdf,tiff

# OCR Configuration
TESSERACT_PATH=/usr/bin/tesseract
OCR_LANGUAGES=eng,fra,deu,spa
OCR_CONFIDENCE_THRESHOLD=60

# Document Processing
MAX_FILE_SIZE=10485760  # 10MB
PROCESSING_TIMEOUT=300  # 5 minutes
TEMP_STORAGE_PATH=./temp
CLEANUP_INTERVAL=3600  # 1 hour

# Security Limits
RATE_LIMIT_PER_MINUTE=60
MAX_CONCURRENT_UPLOADS=10
IP_WHITELIST=127.0.0.1,::1

# Monitoring & Logging
LOG_LEVEL=INFO
LOG_FORMAT=json
SENTRY_DSN=your-sentry-dsn-here
PROMETHEUS_PORT=9090

# External Services
NOTIFICATION_SERVICE_URL=http://localhost:8001
AUDIT_SERVICE_URL=http://localhost:8002

# Development Only
MOCK_ML_RESPONSES=false
SKIP_ENCRYPTION=false
ALLOW_HTTP=false
