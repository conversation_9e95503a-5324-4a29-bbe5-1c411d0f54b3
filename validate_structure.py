#!/usr/bin/env python3
"""
Validate SolidTech project structure and basic imports
"""

import os
import sys
from pathlib import Path


def check_file_exists(file_path: str, description: str) -> bool:
    """Check if a file exists"""
    if Path(file_path).exists():
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (MISSING)")
        return False


def check_directory_exists(dir_path: str, description: str) -> bool:
    """Check if a directory exists"""
    if Path(dir_path).is_dir():
        print(f"✅ {description}: {dir_path}")
        return True
    else:
        print(f"❌ {description}: {dir_path} (MISSING)")
        return False


def validate_python_syntax(file_path: str) -> bool:
    """Validate Python file syntax"""
    try:
        with open(file_path, 'r') as f:
            compile(f.read(), file_path, 'exec')
        return True
    except SyntaxError as e:
        print(f"❌ Syntax error in {file_path}: {e}")
        return False
    except Exception as e:
        print(f"⚠️ Could not validate {file_path}: {e}")
        return False


def main():
    """Validate the SolidTech project structure"""
    
    print("🔍 Validating SolidTech Document Verification System")
    print("=" * 60)
    
    all_good = True
    
    # Check main application files
    print("\n📁 Core Application Files:")
    files_to_check = [
        ("main.py", "Main application entry point"),
        ("start_server.py", "Server startup script"),
        ("requirements.txt", "Python dependencies"),
        ("README.md", "Project documentation"),
        (".env.example", "Environment configuration example"),
    ]
    
    for file_path, description in files_to_check:
        if not check_file_exists(file_path, description):
            all_good = False
    
    # Check directory structure
    print("\n📂 Directory Structure:")
    directories_to_check = [
        ("app", "Main application package"),
        ("app/api", "API endpoints"),
        ("app/core", "Core configuration"),
        ("app/ml", "Machine learning components"),
        ("app/models", "Data models"),
        ("app/utils", "Utility functions"),
        ("app/database", "Database components"),
        ("config", "Configuration files"),
        ("models", "ML model storage"),
        ("temp", "Temporary files"),
    ]
    
    for dir_path, description in directories_to_check:
        if not check_directory_exists(dir_path, description):
            all_good = False
    
    # Check Python files syntax
    print("\n🐍 Python File Validation:")
    python_files = [
        "main.py",
        "start_server.py",
        "test_system.py",
        "app/core/config.py",
        "app/core/security.py",
        "app/core/exceptions.py",
        "app/core/logging.py",
        "app/api/routes.py",
        "app/ml/model_manager.py",
        "app/ml/fraud_detector.py",
        "app/ml/ocr_engine.py",
        "app/ml/document_processor.py",
        "app/utils/file_handler.py",
        "app/database/connection.py",
    ]
    
    syntax_errors = 0
    for file_path in python_files:
        if Path(file_path).exists():
            if validate_python_syntax(file_path):
                print(f"✅ Syntax OK: {file_path}")
            else:
                syntax_errors += 1
                all_good = False
        else:
            print(f"⚠️ File not found: {file_path}")
    
    # Check for __init__.py files
    print("\n📦 Package Structure:")
    init_files = [
        "app/__init__.py",
        "app/api/__init__.py",
        "app/core/__init__.py",
        "app/ml/__init__.py",
        "app/models/__init__.py",
        "app/utils/__init__.py",
        "app/database/__init__.py",
    ]
    
    for init_file in init_files:
        if not check_file_exists(init_file, f"Package init file"):
            all_good = False
    
    # Summary
    print("\n" + "=" * 60)
    if all_good:
        print("🎉 SolidTech project structure validation PASSED!")
        print("✅ All required files and directories are present")
        print("✅ All Python files have valid syntax")
        print("\n🚀 Ready for deployment!")
        return 0
    else:
        print("💥 SolidTech project structure validation FAILED!")
        print("❌ Some files are missing or have syntax errors")
        print("\n🔧 Please fix the issues above before deployment")
        return 1


if __name__ == "__main__":
    sys.exit(main())
