<?php

/**
 * Simple script to test 2FA functionality
 * Run with: php scripts/test_2fa.php
 */

require_once __DIR__ . '/../vendor/autoload.php';

use App\Modules\Auth\Services\TwoFactorService;
use PragmaRX\Google2FA\Google2FA;

echo "🔐 Testing Two-Factor Authentication Implementation\n";
echo "================================================\n\n";

// Test 1: Service instantiation
echo "1. Testing TwoFactorService instantiation...\n";
try {
    $service = new TwoFactorService();
    echo "   ✅ TwoFactorService created successfully\n\n";
} catch (Exception $e) {
    echo "   ❌ Failed to create TwoFactorService: " . $e->getMessage() . "\n\n";
    exit(1);
}

// Test 2: Secret generation
echo "2. Testing secret generation...\n";
try {
    $secret = $service->generateSecret();
    echo "   ✅ Secret generated: $secret\n";
    echo "   ✅ Secret length: " . strlen($secret) . " characters\n\n";
} catch (Exception $e) {
    echo "   ❌ Failed to generate secret: " . $e->getMessage() . "\n\n";
    exit(1);
}

// Test 3: QR Code URL generation (using Google2FA directly)
echo "3. Testing QR code URL generation...\n";
try {
    $google2fa = new Google2FA();
    $appName = 'API Interface';
    $userEmail = '<EMAIL>';

    $qrUrl = $google2fa->getQRCodeUrl($appName, $userEmail, $secret);
    echo "   ✅ QR Code URL generated successfully\n";
    echo "   ✅ URL: $qrUrl\n";
    echo "   ✅ Contains email: " . (strpos($qrUrl, '<EMAIL>') !== false ? 'Yes' : 'No') . "\n";
    echo "   ✅ Contains secret: " . (strpos($qrUrl, $secret) !== false ? 'Yes' : 'No') . "\n\n";
} catch (Exception $e) {
    echo "   ❌ Failed to generate QR code URL: " . $e->getMessage() . "\n\n";
    exit(1);
}

// Test 4: Code verification with Google2FA directly
echo "4. Testing TOTP code verification...\n";
try {
    $google2fa = new Google2FA();

    // Generate a current valid code
    $currentCode = $google2fa->getCurrentOtp($secret);
    echo "   ✅ Current TOTP code: $currentCode\n";

    // Verify the code
    $isValid = $service->verifyCode($secret, $currentCode);
    echo "   ✅ Code verification: " . ($isValid ? 'VALID' : 'INVALID') . "\n\n";
} catch (Exception $e) {
    echo "   ❌ Failed to verify code: " . $e->getMessage() . "\n\n";
    exit(1);
}

// Test 5: Backup codes generation
echo "5. Testing backup codes generation...\n";
try {
    $backupCodes = $service->generateBackupCodes();
    echo "   ✅ Generated " . count($backupCodes) . " backup codes\n";
    echo "   ✅ Sample codes: " . implode(', ', array_slice($backupCodes, 0, 3)) . "...\n\n";
} catch (Exception $e) {
    echo "   ❌ Failed to generate backup codes: " . $e->getMessage() . "\n\n";
    exit(1);
}

echo "🎉 All tests passed! Two-Factor Authentication is working correctly.\n\n";

echo "📱 To test with a real authenticator app:\n";
echo "1. Scan this QR code URL in your browser: $qrUrl\n";
echo "2. Add the account to your authenticator app\n";
echo "3. Use the generated codes to test the API endpoints\n\n";

echo "🔗 Simple 3-Endpoint API:\n";
echo "- POST /api/v1/auth/login (handles all login scenarios)\n";
echo "- POST /api/v1/auth/verify-pin (verify PIN and complete login)\n";
echo "- POST /api/v1/auth/logout (logout user)\n\n";

echo "📚 See docs/TWO_FACTOR_AUTHENTICATION.md for complete documentation.\n";
