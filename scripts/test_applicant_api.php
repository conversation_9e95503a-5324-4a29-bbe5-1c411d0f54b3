<?php

/**
 * Simple script to test applicant applications API
 * Run with: php scripts/test_applicant_api.php
 */

require_once __DIR__ . '/../vendor/autoload.php';

use App\Modules\Applications\Controllers\ApplicationController;
use App\Modules\Applications\Models\Application;
use App\Modules\Auth\Models\PortalUser;
use App\Modules\Products\Models\Product;

echo "📱 Testing Applicant Applications API\n";
echo "====================================\n\n";

// Test 1: Controller class exists
echo "1. Testing ApplicationController class exists...\n";
try {
    if (class_exists(ApplicationController::class)) {
        echo "   ✅ ApplicationController class found\n\n";
    } else {
        echo "   ❌ ApplicationController class not found\n\n";
    }
} catch (Exception $e) {
    echo "   ❌ Error checking ApplicationController: " . $e->getMessage() . "\n\n";
    exit(1);
}

// Test 2: Check if models exist
echo "2. Testing model availability...\n";
try {
    $applicationModel = new Application();
    $userModel = new PortalUser();
    $productModel = new Product();
    
    echo "   ✅ Application model available\n";
    echo "   ✅ PortalUser model available\n";
    echo "   ✅ Product model available\n\n";
} catch (Exception $e) {
    echo "   ❌ Failed to instantiate models: " . $e->getMessage() . "\n\n";
    exit(1);
}

// Test 3: Check relationships
echo "3. Testing model relationships...\n";
try {
    $application = new Application();
    
    // Test if relationships are defined
    $productRelation = $application->product();
    $applicantRelation = $application->applicant();
    
    echo "   ✅ Application->product relationship defined\n";
    echo "   ✅ Application->applicant relationship defined\n\n";
} catch (Exception $e) {
    echo "   ❌ Failed to test relationships: " . $e->getMessage() . "\n\n";
    exit(1);
}

echo "🎉 All basic tests passed! Applicant Applications API is ready.\n\n";

echo "📋 Expected API Response Format:\n";
echo "{\n";
echo "  \"success\": true,\n";
echo "  \"data\": [\n";
echo "    {\n";
echo "      \"name\": \"DBS Check\",\n";
echo "      \"code\": \"DBS001\",\n";
echo "      \"description\": \"Enhanced DBS Check\"\n";
echo "    },\n";
echo "    {\n";
echo "      \"name\": \"Another Product\",\n";
echo "      \"code\": \"PROD002\"\n";
echo "    }\n";
echo "  ],\n";
echo "  \"message\": \"Applications retrieved successfully\"\n";
echo "}\n\n";

echo "🔗 API Endpoints:\n";
echo "- POST /api/v1/auth/login\n";
echo "- POST /api/v1/auth/verify-pin\n";
echo "- GET  /api/v1/applicants/{applicantId}\n";
echo "- GET  /api/v1/applications/{id}/form-data\n";
echo "- POST /api/v1/auth/logout\n\n";

echo "📚 See docs/APPLICANT_APPLICATIONS_API.md for complete documentation.\n";

echo "🧪 Test with curl:\n";
echo "curl -X POST http://localhost:8001/api/v1/auth/login \\\n";
echo "  -H \"Content-Type: application/json\" \\\n";
echo "  -d '{\"email\": \"<EMAIL>\", \"password\": \"password\"}'\n\n";

echo "curl -X GET http://localhost:8001/api/v1/applicants/1 \\\n";
echo "  -H \"Authorization: Bearer YOUR_TOKEN_HERE\"\n";
