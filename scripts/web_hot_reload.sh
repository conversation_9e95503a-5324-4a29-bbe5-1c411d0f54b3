#!/bin/bash

# Flutter 3.32.0+ Web Hot Reload Script
# This script enables the experimental web hot reload feature for faster development

echo "🚀 Starting Flutter Web with Hot Reload (Experimental)"
echo "Flutter 3.32.0+ Web Hot Reload Feature"
echo "======================================="

# Check if Flutter is available
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter is not installed or not in PATH"
    exit 1
fi

# Check Flutter version
FLUTTER_VERSION=$(flutter --version | head -n 1 | cut -d ' ' -f 2)
echo "📱 Flutter Version: $FLUTTER_VERSION"

# Check if Chrome is available
if ! command -v google-chrome &> /dev/null && ! command -v chrome &> /dev/null; then
    echo "❌ Chrome browser is not available"
    echo "Please install Google Chrome to use web hot reload"
    exit 1
fi

# Clean previous builds
echo "🧹 Cleaning previous builds..."
flutter clean

# Get dependencies
echo "📦 Getting dependencies..."
flutter pub get

# Start web development server with hot reload
echo "🔥 Starting web server with hot reload..."
echo "This will open your app in Chrome with instant hot reload capabilities"
echo "Make changes to your code and see them instantly in the browser!"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Run with experimental web hot reload
flutter run -d chrome --web-experimental-hot-reload --web-port=8080 --web-hostname=localhost

echo ""
echo "✅ Web hot reload session ended"
