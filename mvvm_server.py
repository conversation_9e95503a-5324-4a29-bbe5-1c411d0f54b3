"""
MVVM Document Processing Server
Clean architecture implementation with dependency injection
"""

import os
import sys
import logging
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from fastapi import FastAPI, File, UploadFile, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from typing import Dict, Any, Optional
import uuid
import time

# Import our MVVM components
from app.models.document_models import (
    DocumentType,
    DocumentProcessingRequest,
    DocumentExtractionResult
)
from app.services.document_processing_service import DocumentProcessingService

# Import engines with fallback
try:
    from engines.mrz.fast_mrz_engine import FastMRZEngine
    FAST_MRZ_AVAILABLE = True
except ImportError as e:
    logger.warning(f"FastMRZ not available: {e}")
    FAST_MRZ_AVAILABLE = False

try:
    from engines.ocr.trocr_engine import TrOCREngine
    TROCR_AVAILABLE = True
except ImportError as e:
    logger.warning(f"TrOCR not available: {e}")
    TROCR_AVAILABLE = False

# Import specialized processors
try:
    from processors.passport_processor import PassportProcessor
    from processors.uk_license_processor import UKLicenseProcessor
    from processors.tax_document_processor import TaxDocumentProcessor
    PROCESSORS_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Specialized processors not available: {e}")
    PROCESSORS_AVAILABLE = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="SolidTech Document Processing API",
    description="Enterprise-grade document processing with MVVM architecture",
    version="2.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global service instance (will be initialized on startup)
document_service: Optional[DocumentProcessingService] = None


class MockFraudDetector:
    """Mock fraud detector for initial implementation"""
    
    def analyze_document(self, image, document_type):
        from app.models.document_models import FraudAnalysis
        return FraudAnalysis(
            is_authentic=True,
            is_fraudulent=False,
            is_altered=False,
            confidence_score=0.85,
            authenticity_score=0.85,
            alteration_score=0.0,
            fraud_indicators=[],
            security_features_verified=["basic_structure"],
            requires_manual_review=False
        )


class MockFaceProcessor:
    """Mock face processor for initial implementation"""
    
    def extract_face(self, image):
        from app.models.document_models import FaceAnalysis
        return FaceAnalysis(
            face_detected=True,
            face_count=1,
            face_confidence=0.8,
            liveness_score=0.8,
            is_live=True
        )


class MockFieldValidator:
    """Mock field validator for initial implementation"""
    
    def validate_field(self, fields, document_type, application_data=None):
        from app.models.document_models import ValidationResult
        return ValidationResult(
            is_valid=True,
            is_consistent=True,
            errors=[],
            warnings=[]
        )


class MockRepository:
    """Mock repository for initial implementation"""
    
    def save_processing_result(self, result):
        return str(uuid.uuid4())
    
    def get_processing_result(self, result_id):
        return None
    
    def save_training_data(self, image_data, metadata):
        return str(uuid.uuid4())
    
    def get_training_statistics(self):
        return {"total_documents": 0}


class MockConfigService:
    """Mock configuration service"""
    
    def get_ocr_config(self, document_type):
        return {}
    
    def get_fraud_detection_config(self):
        return {}
    
    def get_validation_rules(self, document_type):
        return {}
    
    def get_processing_options(self):
        return {}


class MockLoggingService:
    """Mock logging service"""
    
    def log_processing_start(self, request):
        logger.info(f"🔄 Processing started: {request.request_id}")
    
    def log_processing_complete(self, result):
        logger.info(f"✅ Processing completed: {result.processing_metadata.request_id}")
    
    def log_error(self, error, context):
        logger.error(f"❌ Error: {error} | Context: {context}")
    
    def log_performance_metrics(self, metrics):
        logger.info(f"📊 Metrics: {metrics}")


class MockMetricsCollector:
    """Mock metrics collector"""

    def record_processing_time(self, document_type, processing_time):
        logger.info(f"⏱️ Processing time: {processing_time:.2f}s for {document_type.value}")

    def record_accuracy_score(self, document_type, accuracy):
        logger.info(f"🎯 Accuracy: {accuracy:.2f} for {document_type.value}")

    def record_fraud_detection(self, document_type, is_fraudulent):
        logger.info(f"🔍 Fraud detection: {is_fraudulent} for {document_type.value}")

    def get_metrics_summary(self):
        return {"total_processed": 0}


class MockOCREngine:
    """Mock OCR engine for testing"""

    def extract_text(self, image, **kwargs):
        return "Mock extracted text from document"

    def extract_fields(self, image, document_type, **kwargs):
        from app.models.document_models import ExtractedField, ExtractionMethod

        # Return mock fields based on document type
        fields = {}

        if document_type == DocumentType.PASSPORT:
            fields = {
                'passport_number': ExtractedField(
                    value='*********',
                    confidence=0.8,
                    extraction_method=ExtractionMethod.TESSERACT,
                    field_type='text',
                    required=True,
                    editable=True
                ),
                'first_name': ExtractedField(
                    value='John',
                    confidence=0.8,
                    extraction_method=ExtractionMethod.TESSERACT,
                    field_type='text',
                    required=True,
                    editable=True
                ),
                'last_name': ExtractedField(
                    value='Doe',
                    confidence=0.8,
                    extraction_method=ExtractionMethod.TESSERACT,
                    field_type='text',
                    required=True,
                    editable=True
                )
            }
        elif document_type == DocumentType.UK_DRIVING_LICENSE:
            fields = {
                'licence_number': ExtractedField(
                    value='MORGA753116SM9IJ',
                    confidence=0.8,
                    extraction_method=ExtractionMethod.TESSERACT,
                    field_type='text',
                    required=True,
                    editable=True
                ),
                'first_name': ExtractedField(
                    value='Morgan',
                    confidence=0.8,
                    extraction_method=ExtractionMethod.TESSERACT,
                    field_type='text',
                    required=True,
                    editable=True
                ),
                'last_name': ExtractedField(
                    value='Meredyth',
                    confidence=0.8,
                    extraction_method=ExtractionMethod.TESSERACT,
                    field_type='text',
                    required=True,
                    editable=True
                ),
                'postcode': ExtractedField(
                    value='EH1 9GP',
                    confidence=0.8,
                    extraction_method=ExtractionMethod.TESSERACT,
                    field_type='text',
                    required=True,
                    editable=True
                )
            }

        return fields

    def get_confidence(self):
        return 0.8

    def get_engine_info(self):
        return {
            "engine": "MockOCR",
            "version": "1.0.0",
            "supports": "All document types (mock data)"
        }


def initialize_services():
    """Initialize all services with dependency injection"""
    global document_service
    
    try:
        logger.info("🔄 Initializing SolidTech MVVM services...")
        
        # Initialize OCR engines
        ocr_engines = {}
        
        # FastMRZ for passports
        if FAST_MRZ_AVAILABLE:
            try:
                fast_mrz = FastMRZEngine(
                    model_path=str(project_root / "engines/mrz/models/mrz_seg.onnx")
                )
                ocr_engines['fast_mrz'] = fast_mrz
                logger.info("✅ FastMRZ engine initialized")
            except Exception as e:
                logger.warning(f"⚠️ FastMRZ engine failed to initialize: {e}")

        # TrOCR for general documents
        if TROCR_AVAILABLE:
            try:
                trocr = TrOCREngine()
                ocr_engines['trocr'] = trocr
                logger.info("✅ TrOCR engine initialized")
            except Exception as e:
                logger.warning(f"⚠️ TrOCR engine failed to initialize: {e}")

        # Add a mock OCR engine if no real engines are available
        if not ocr_engines:
            logger.warning("⚠️ No OCR engines available, adding mock engine")
            ocr_engines['mock'] = MockOCREngine()
        
        # Initialize mock services (will be replaced with real implementations)
        fraud_detector = MockFraudDetector()
        face_processor = MockFaceProcessor()
        field_validator = MockFieldValidator()
        repository = MockRepository()
        config_service = MockConfigService()
        logging_service = MockLoggingService()
        metrics_collector = MockMetricsCollector()
        
        # Initialize specialized processors if available
        specialized_processors = {}
        if PROCESSORS_AVAILABLE:
            try:
                # Passport processor
                passport_processor = PassportProcessor(
                    mrz_engine=ocr_engines.get('fast_mrz'),
                    fraud_detector=fraud_detector,
                    face_processor=face_processor,
                    field_validator=field_validator
                )
                specialized_processors[DocumentType.PASSPORT] = passport_processor
                specialized_processors[DocumentType.PASSPORT_ANY] = passport_processor

                # UK License processor
                uk_license_processor = UKLicenseProcessor(
                    ocr_engine=ocr_engines.get('trocr') or ocr_engines.get('mock'),
                    fraud_detector=fraud_detector,
                    face_processor=face_processor,
                    field_validator=field_validator
                )
                specialized_processors[DocumentType.UK_DRIVING_LICENSE] = uk_license_processor

                # Tax document processor
                tax_processor = TaxDocumentProcessor(
                    ocr_engine=ocr_engines.get('trocr') or ocr_engines.get('mock'),
                    fraud_detector=fraud_detector,
                    field_validator=field_validator
                )
                specialized_processors[DocumentType.P60] = tax_processor
                specialized_processors[DocumentType.P45] = tax_processor
                specialized_processors[DocumentType.P45_P60] = tax_processor

                logger.info("✅ Specialized processors initialized")
            except Exception as e:
                logger.warning(f"⚠️ Failed to initialize specialized processors: {e}")

        # Initialize main document processing service
        document_service = DocumentProcessingService(
            ocr_engines=ocr_engines,
            fraud_detector=fraud_detector,
            face_processor=face_processor,
            field_validator=field_validator,
            repository=repository,
            config_service=config_service,
            logging_service=logging_service,
            metrics_collector=metrics_collector,
            specialized_processors=specialized_processors
        )
        
        logger.info("✅ SolidTech MVVM services initialized successfully")
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize services: {e}")
        raise


@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    initialize_services()


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "SolidTech Document Processing API v2.0",
        "architecture": "MVVM with Clean Architecture",
        "engines": ["FastMRZ", "TrOCR", "Custom OCR"],
        "status": "ready"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "services": {
            "document_service": document_service is not None,
            "ocr_engines": len(document_service.ocr_engines) if document_service else 0
        }
    }


@app.post("/api/v1/documents/ai-extract")
async def extract_document_data(
    file: UploadFile = File(...),
    document_type: str = "passport",
    application_id: str = "test"
):
    """Extract data from document using MVVM architecture"""
    
    if document_service is None:
        raise HTTPException(status_code=500, detail="Document service not initialized")
    
    try:
        # Read file data
        file_data = await file.read()
        
        # Map document type string to enum
        doc_type_mapping = {
            "passport": DocumentType.PASSPORT,
            "passport_any": DocumentType.PASSPORT_ANY,
            "photocard_drivers_licence_uk": DocumentType.UK_DRIVING_LICENSE,
            "p60": DocumentType.P60,
            "p45": DocumentType.P45,
            "p45_p60": DocumentType.P45_P60,
            "bank_statement": DocumentType.BANK_STATEMENT
        }
        
        doc_type = doc_type_mapping.get(document_type, DocumentType.PASSPORT)
        
        # Create processing request
        request = DocumentProcessingRequest(
            document_type=doc_type,
            image_data=file_data,
            filename=file.filename or "document.jpg",
            application_id=application_id,
            request_id=str(uuid.uuid4())
        )
        
        # Process document
        result = document_service.process_document(request)

        # Convert to Flutter-expected format
        result_dict = result.to_dict()

        # Debug logging
        logger.info(f"🔍 Processing result status: {result_dict['status']}")
        logger.info(f"🔍 Extracted fields count: {len(result_dict['extracted_fields'])}")
        logger.info(f"🔍 Confidence: {result_dict['confidence']}")
        logger.info(f"🔍 Extracted fields: {list(result_dict['extracted_fields'].keys())}")

        # Log each field for debugging
        for field_name, field_data in result_dict['extracted_fields'].items():
            logger.info(f"🔍 Field {field_name}: {field_data.get('value', 'N/A')} (confidence: {field_data.get('confidence', 0)})")

        # Transform to match Flutter AIExtractionResult structure
        flutter_response = {
            "success": result_dict["status"] == "success",
            "request_id": request.request_id,
            "processing_method": result_dict.get("processing_metadata", {}).get("processing_method", "ai_extraction"),
            "review_data": {
                "document_type": result_dict["document_type"],
                "extraction_confidence": result_dict["confidence"],
                "fraud_analysis": result_dict["fraud_analysis"],
                "extracted_fields": result_dict["extracted_fields"],
                "portrait_data": result_dict.get("face_analysis"),
                "validation_results": result_dict["validation_result"],
                "recommendations": result_dict["recommendations"],
                "requires_manual_review": result_dict["fraud_analysis"].get("requires_manual_review", False),
                "processing_timestamp": result_dict.get("processing_metadata", {}).get("processing_timestamp", "")
            },
            "message": "Document processed successfully. Please review extracted data." if result_dict["status"] == "success" else "Document processing failed"
        }

        return JSONResponse(content=flutter_response)
        
    except Exception as e:
        logger.error(f"❌ Document extraction failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/engines/status")
async def get_engine_status():
    """Get status of all OCR engines"""
    
    if document_service is None:
        return {"error": "Document service not initialized"}
    
    engine_status = {}
    for name, engine in document_service.ocr_engines.items():
        try:
            info = engine.get_engine_info()
            engine_status[name] = {
                "status": "available",
                "info": info
            }
        except Exception as e:
            engine_status[name] = {
                "status": "error",
                "error": str(e)
            }
    
    return engine_status


@app.get("/api/v1/supported-documents")
async def get_supported_documents():
    """Get list of supported document types"""
    
    if document_service is None:
        return {"error": "Document service not initialized"}
    
    supported_types = document_service.get_supported_document_types()
    return {
        "supported_documents": [doc_type.value for doc_type in supported_types],
        "count": len(supported_types)
    }


if __name__ == "__main__":
    logger.info("🚀 Starting SolidTech MVVM Document Processing Server...")
    uvicorn.run(
        "mvvm_server:app",
        host="127.0.0.1",
        port=9000,
        reload=True,
        log_level="info"
    )
