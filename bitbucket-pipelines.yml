image: ghcr.io/cirruslabs/flutter:stable

definitions:
  caches:
    sonar: ~/.sonar
    flutter: ~/.pub-cache
  steps:
    - step: &build-step
        name: Build Flutter web project and run SonarQube analysis
        script:
          # Print environment info for debugging
          - echo "Flutter version:" && flutter --version
          - echo "Java version:" && java -version || echo "Java not installed"

          # Install dependencies
          - apt-get update && apt-get install -y openjdk-17-jre unzip
          - java -version # Verify Java installation

          # Enable web support and fetch dependencies
          - flutter config --enable-web
          - flutter pub get

          # Run static analysis and tests for web
          - flutter analyze || echo "Flutter analyze completed with warnings"
          - flutter test --platform=chrome --coverage || echo "Tests completed, coverage generated"

          # Build web version
          - flutter build web --release || echo "Web build completed"

          - export SONAR_SCANNER_VERSION=7.0.2.4839
          - mkdir $HOME/.sonar
          - curl -sSLo $HOME/.sonar/sonar-scanner.zip https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-${SONAR_SCANNER_VERSION}-linux-x64.zip
          - unzip -o $HOME/.sonar/sonar-scanner.zip -d $HOME/.sonar/
          - export PATH="$PATH:$HOME/.sonar/sonar-scanner-${SONAR_SCANNER_VERSION}-linux-x64/bin"
          - sonar-scanner
        artifacts:
          - coverage/lcov.info # Store coverage report
          - build/web/** # Store web build output
clone:
  depth: full

pipelines:
  branches:
    master: # Matches your log's branch name
      - step: *build-step
  pull-requests:
    '**':
      - step: *build-step