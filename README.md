# API Interface Project

Laravel API interface for Flutter application to manage entities, applications, and user authentication for portal users.

## 🚀 Features

- **Token-based Authentication** using Laravel Sanctum
- **MVVM Architecture** with modular structure
- **API Documentation** with Scramble
- **Development Tools** (Telescope, Debugbar)
- **Web Testing Interface** for API endpoints
- **Database Integration** with existing portal_users table
- **Simplified Authentication** - Login only with existing users

## 📁 Project Structure

```
api_interface/
├── app/
│   ├── Core/
│   │   ├── BaseApiController.php
│   │   └── Middleware/
│   │       └── CheckRole.php
│   └── Modules/
│       ├── Auth/
│       │   ├── Controllers/
│       │   │   └── AuthController.php
│       │   └── Models/
│       │       └── PortalUser.php
│       ├── Users/
│       │   └── Models/
│       │       ├── Profile.php
│       │       └── UserType.php
│       ├── Entities/
│       │   ├── Models/
│       │   │   ├── Entity.php
│       │   │   ├── EntityProfile.php
│       │   │   ├── EntityOption.php
│       │   │   └── EntityProductSetting.php
│       │   └── Enums/
│       │       └── EntityType.php
│       ├── Products/
│       │   └── Models/
│       │       ├── Product.php
│       │       ├── ProductFormField.php
│       │       └── ProductFee.php
│       └── Applications/
│           └── Models/
│               ├── Application.php
│               └── ApplicationFormData.php
```

## 🛠️ Installation & Setup

### 1. Environment Configuration

Update `.env` file with your database settings:

```env
APP_NAME="API Interface"
APP_URL=http://localhost:8001

DB_CONNECTION=mysql
DB_HOST=**********
DB_PORT=3306
DB_DATABASE=laravel_api
DB_USERNAME=laravel
DB_PASSWORD=Aldjedydss
```

### 2. Install Dependencies

```bash
composer install
```

### 3. Generate Application Key

```bash
php artisan key:generate
```

### 4. Run Migrations (if needed)

```bash
php artisan migrate
```

### 5. Install Telescope (Development)

```bash
php artisan telescope:install
php artisan migrate
```

## 📚 API Documentation

### Available Endpoints

#### Authentication (Unified for All User Types)
- `POST /api/v1/auth/login` - Login for all user types (applicants, client users, requesters, document checkers)
- `POST /api/v1/auth/verify-pin` - Two-factor authentication PIN verification for all user types
- `POST /api/v1/auth/logout` - User logout (revoke token)

#### Applications (Hierarchical Access)
- `GET /api/v1/applicants/{applicantId}` - Get all details for a specific applicant including profile and applications
- `GET /api/v1/applications/{id}/form-data` - Get application form data with completion status
- `POST /api/v1/applications/save-data` - Save application form data with entity-based security and payment validation
- `GET /api/v1/applications/payment-status/{applicantId}` - Check if applicant needs to make payment for applications

#### Applicant Management (Client Users)
- `POST /api/v1/addapplicant` - Create new applicant with auto-generated applications and billing snapshots

#### Job Roles & Product Pricing
- `GET /api/v1/entities/{entityId}/job-roles` - Get job roles with inheritance and product pricing
- `GET /api/v1/entities/{entityId}/job-roles/own` - Get entity's own job roles only
- `GET /api/v1/job-roles/{jobRoleId}` - Get specific job role with product pricing

### Access Documentation

1. **Interactive API Docs**: Visit `/docs/api`
2. **API Tester**: Visit `/api-test` or `/`
3. **Telescope Debug**: Visit `/telescope`

### Documentation Features

- **Auto-generated** from code annotations and request classes
- **Interactive testing** with Try It Out functionality
- **Real-time updates** - no manual generation needed
- **OpenAPI 3.0 specification** available at `/docs/api.json`

## 🧪 Testing the API

### Web Interface

Visit `http://localhost:8001/api-test` for a web-based API testing interface with:

- **Authentication testing** (login with existing portal users)
- **Token management** (auto-fill from login response)
- **Response display** with syntax highlighting
- **Quick access** to documentation and debugging tools
- **Pre-filled test credentials** for existing portal users

### Sample API Calls

#### Login
```bash
curl -X POST http://localhost:8001/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password"
  }'
```

#### Get User Info (with token)
```bash
curl -X GET http://localhost:8001/api/v1/auth/user \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json"
```

#### Two-Factor Authentication (Simple 3-Endpoint Flow)
```bash
# Step 1: Login (if 2FA enabled but not set up, automatically returns QR code)
curl -X POST http://localhost:8001/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password"
  }'

# Step 2: After scanning QR code, verify PIN to complete login
curl -X POST http://localhost:8001/api/v1/auth/verify-pin \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password",
    "pin": "123456"
  }'

# Step 3: Logout
curl -X POST http://localhost:8001/api/v1/auth/logout \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

#### Applications Access (Hierarchical)
```bash
# Login (works for all user types - applicants, client users, etc.)
curl -X POST http://localhost:8001/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password"
  }'

# Get applications (works for both applicants and client users with appropriate access)
curl -X GET http://localhost:8001/api/v1/applications \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"

# Get specific application details
curl -X GET http://localhost:8001/api/v1/applications/1 \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 🔧 Development Tools

### Laravel Telescope
- **URL**: `/telescope`
- **Purpose**: Debug requests, queries, exceptions, and performance
- **Features**: Request monitoring, database query analysis, exception tracking

### Laravel Debugbar
- **Purpose**: Development debugging toolbar
- **Features**: Query analysis, route information, view data inspection

### Scramble API Documentation
- **URL**: `/docs/api`
- **Purpose**: Interactive API documentation with OpenAPI 3.0 spec
- **Features**: Auto-generated docs, Try-it-out functionality, real-time updates

## 🗃️ Database Models

### Core Models
- **PortalUser**: Main user authentication model
- **Profile**: User profile information
- **Entity**: Client/group entities with hierarchy
- **EntityProfile**: Entity contact information
- **Product**: Available products/services
- **Application**: Application forms and submissions

### User Types (from existing portal_users table)
- `client_user` - Client users (full API access + hierarchical application access)
- `requester` - Request handlers (full API access + hierarchical application access)
- `document_checker` - Document reviewers (full API access + hierarchical application access)
- `applicant` - Application submitters (limited API access to view their own applications)

## 🔐 Authentication

Uses **Laravel Sanctum** for token-based authentication with existing portal_users:

### Two-Factor Authentication (2FA)
- **TOTP Support**: Compatible with Google Authenticator, Microsoft Authenticator
- **Automatic Setup**: QR code generated automatically on first login
- **Simple Flow**: Only 3 endpoints needed (login, verify-pin, logout)
- **Secure Storage**: Encrypted secret storage

See [Two-Factor Authentication Documentation](docs/TWO_FACTOR_AUTHENTICATION.md) for detailed implementation guide.

1. Login via `/api/v1/auth/login` with existing portal user credentials
2. Receive bearer token in response
3. Include token in `Authorization: Bearer {token}` header for protected endpoints
4. Use existing seeded users (<EMAIL>, <EMAIL>, etc.) with password "password"

## 🚦 Development Server

Start the development server:

```bash
php artisan serve --host=0.0.0.0 --port=8001
```

Access the application at `http://localhost:8001`

## 📝 Next Steps

1. **Create remaining controllers** for Entities, Applications, Products, Users
2. **Implement services and repositories** for business logic
3. **Add validation requests** for input validation
4. **Set up proper error handling** and logging
5. **Add rate limiting** for API endpoints
6. **Implement caching** for performance optimization

## 🤝 Contributing

Follow the established MVVM pattern and modular structure when adding new features.

## 📄 License

This project is part of the admin system and follows the same licensing terms.
