# SolidTech Document Verification System

🔒 **Enterprise-grade ML-powered document verification and fraud detection system**

## Overview

SolidTech is a comprehensive document verification microservice that uses machine learning to detect fraudulent documents, verify authenticity, and extract data with high accuracy. The system provides end-to-end encryption, advanced fraud detection, and seamless integration capabilities.

## Key Features

### 🛡️ Security
- **RSA-4096/AES-256-GCM hybrid encryption** for secure document transfer
- **JWT authentication** with RS256 signing using RSA key pairs
- **HMAC signatures** for data integrity verification
- **IP whitelisting** and rate limiting
- **Comprehensive security middleware** with CORS protection

### 🤖 Machine Learning
- **Multi-engine fraud detection** with authenticity verification
- **Copy detection** using scan artifacts and print quality analysis
- **Alteration detection** with Error Level Analysis (ELA)
- **Document-specific security feature validation**
- **Multi-engine OCR** combining Tesseract and EasyOCR

### 📄 Document Processing
- **Advanced image preprocessing** with noise reduction and contrast enhancement
- **Perspective correction** using contour detection
- **Quality assessment** with blur, brightness, and contrast analysis
- **Support for multiple formats**: JPG, PNG, PDF, TIFF, BMP

### 🔍 Data Extraction & Validation
- **Intelligent OCR** with document-specific parsing
- **Fuzzy matching** for comparing extracted vs application data
- **Confidence scoring** and recommendation generation
- **Comprehensive data validation** against form submissions

## Architecture

```
SolidTech/
├── app/
│   ├── api/                 # API endpoints and routing
│   ├── core/                # Core configuration and security
│   ├── ml/                  # Machine learning components
│   ├── models/              # Data models and schemas
│   ├── utils/               # Utility functions
│   └── database/            # Database connections and models
├── config/                  # Configuration files
├── models/                  # Trained ML models
├── temp/                    # Temporary file storage
└── logs/                    # Application logs
```

## Quick Start

### Prerequisites
- Python 3.9+
- PostgreSQL 13+
- Redis 6+
- Tesseract OCR

### Installation

1. **Clone and setup**:
```bash
cd SolidTech
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

2. **Environment configuration**:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Database setup**:
```bash
# Create PostgreSQL database
createdb solidtech

# Run migrations (when available)
alembic upgrade head
```

4. **Start the server**:
```bash
python start_server.py
```

### Testing

Run system tests:
```bash
python test_system.py
```

## API Endpoints

### Authentication
- `POST /api/v1/auth/token` - Get JWT token
- `GET /api/v1/security/public-key` - Get RSA public key

### Document Processing
- `POST /api/v1/documents/upload` - Upload and analyze document
- `GET /api/v1/documents/{doc_id}/status` - Check processing status
- `GET /api/v1/documents/{doc_id}/results` - Get analysis results

### Health & Monitoring
- `GET /health` - System health check
- `GET /metrics` - Prometheus metrics

## Configuration

Key environment variables:

```env
# Application
APP_NAME=SolidTech Document Verification
ENVIRONMENT=development
DEBUG=true
HOST=0.0.0.0
PORT=8000

# Security
SECRET_KEY=your-secret-key
RSA_PRIVATE_KEY_PATH=./config/keys/private_key.pem
RSA_PUBLIC_KEY_PATH=./config/keys/public_key.pem

# Database
DATABASE_URL=postgresql+asyncpg://user:password@localhost/solidtech

# ML Configuration
MOCK_ML_RESPONSES=true
MAX_IMAGE_SIZE=2048
MODEL_PATH=./models/trained
```

## Integration

### Flutter Integration Example

```dart
// Encrypt and upload document
final encryptedData = await encryptDocument(documentBytes);
final response = await http.post(
  Uri.parse('$baseUrl/api/v1/documents/upload'),
  headers: {
    'Authorization': 'Bearer $jwtToken',
    'Content-Type': 'multipart/form-data',
  },
  body: {
    'file': encryptedData,
    'document_type': 'passport',
    'application_data': jsonEncode(applicationData),
  },
);
```

## Security Features

### Encryption Flow
1. **Key Exchange**: Client requests RSA public key
2. **AES Key Generation**: Client generates random AES-256 key
3. **Hybrid Encryption**: Document encrypted with AES, key encrypted with RSA
4. **Secure Transfer**: Encrypted payload sent with HMAC signature
5. **Server Decryption**: Server decrypts using private RSA key

### Fraud Detection
- **Authenticity Analysis**: Validates document security features
- **Copy Detection**: Identifies scanned/photocopied documents
- **Alteration Detection**: Detects digital modifications
- **Quality Assessment**: Ensures document meets verification standards

## Development

### Adding New Document Types

1. **Update models**:
```python
# app/models/document.py
class NewDocumentType(BaseModel):
    specific_field: str
```

2. **Add OCR patterns**:
```python
# app/ml/ocr_engine.py
def extract_new_document_data(self, text: str) -> Dict:
    # Add extraction logic
```

3. **Update fraud detection**:
```python
# app/ml/fraud_detector.py
async def verify_new_document_authenticity(self, image: np.ndarray) -> Dict:
    # Add verification logic
```

### Testing

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app

# Run specific test
pytest tests/test_document_processing.py
```

## Monitoring

- **Health Checks**: `/health` endpoint for system status
- **Metrics**: Prometheus metrics at `/metrics`
- **Logging**: Structured JSON logging with correlation IDs
- **Error Tracking**: Sentry integration for error monitoring

## Production Deployment

### Docker Deployment
```bash
docker build -t solidtech .
docker run -p 8000:8000 --env-file .env solidtech
```

### Security Checklist
- [ ] Generate production RSA keys
- [ ] Set strong SECRET_KEY
- [ ] Configure IP whitelist
- [ ] Enable HTTPS with valid certificates
- [ ] Set up database with proper credentials
- [ ] Configure Redis with authentication
- [ ] Set MOCK_ML_RESPONSES=false
- [ ] Enable rate limiting
- [ ] Set up monitoring and alerting

## License

Proprietary - SolidTech Document Verification System

## Support

For technical support and integration assistance, please contact the development team.
