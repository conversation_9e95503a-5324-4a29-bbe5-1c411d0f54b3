# Save Application Data API

This document describes the API endpoint for saving application form data with enhanced security controls.

## Overview

The Save Application Data API allows authorized users to save form data for applications. The API implements strict security controls to ensure that only authorized users can save data for specific applications.

## Security Model

### Access Control Rules

1. **Applicants**: Can only save data for their own applications
2. **Client Users** (`client_user`, `requester`, `document_checker`): Can save data for applications where:
   - The applicant is associated with an entity that the client user has access to
   - This includes entity hierarchy access (parent/super group relationships)

### Entity Hierarchy Security

The API follows the entity hierarchy structure:
- **Super Group** → **Parent Group** → **Client**
- Users with access to parent entities can access applications from child entities
- Users cannot access applications from entities outside their hierarchy

## API Endpoint

### Save Application Data

```http
POST /api/v1/applications/save-data
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
    "application_id": 1,
    "form_data": {
        "ApplicantDetails::TITLE": "MR",
        "ApplicantDetails::GENDER": "Male",
        "ApplicantDetails::FORENAME": "Tester",
        "ApplicantDetails::MIDDLENAMES": "N/A",
        "ApplicantDetails::PRESENT_SURNAME": "NiceTest",
        "ApplicantDetails\\AdditionalApplicantDetails::OTHER_NAMES": "N/A",
        "ApplicantDetails::DATE_OF_BIRTH": "1995/05/26"
    }
}
```

**Success Response (200):**
```json
{
    "success": true,
    "data": {
        "application_id": 1
    },
    "message": "Application data saved successfully"
}
```

**Validation Error Response (422):**
```json
{
    "success": false,
    "message": "Validation failed",
    "data": {
        "form_data": ["Form data must be an array"]
    }
}
```

**Form Field Validation Error Response (422):**
```json
{
    "success": false,
    "message": "Form validation failed",
    "data": {
        "ApplicantDetails::FORENAME": "The First Name field is required",
        "ApplicantDetails::DATE_OF_BIRTH": "The Date of Birth must be a valid date"
    }
}
```

**Access Denied Response (404):**
```json
{
    "success": false,
    "message": "Application not found"
}
```

## Request Validation

### Required Fields
- `application_id`: Must be a valid integer and exist in the applications table
- `form_data`: Must be an array (object) with string values and cannot be empty

### Form Data Validation

The API validates form data against the product's form field configuration:

1. **Required Fields**: Checks that all required fields are provided and not empty
2. **Field Types**: Validates data types based on field configuration:
   - `email`: Must be a valid email address
   - `date`: Must be a valid date (supports Y-m-d, Y/m/d, d-m-Y, d/m/Y formats)
   - `number`: Must be numeric
   - `select`/`radio`: Must be one of the allowed options
3. **Field Options**: For select/radio fields, validates against allowed options

## Data Storage

Form data is stored in the `application_data` table with the following structure:
- `application_id`: Foreign key to applications table
- `field_key`: The form field identifier (e.g., "ApplicantDetails::TITLE")
- `value`: The form field value
- `created_at`/`updated_at`: Timestamps

### Data Replacement
When saving new data, the API:
1. Removes all existing application data for the application
2. Inserts new data in a database transaction
3. Ensures data consistency and integrity

## Security Implementation

### Entity-Based Access Control

The security implementation checks:

1. **Applicant Access**: If the user is an applicant, they can only access their own applications
2. **Client User Access**: For client users, the system:
   - Gets all entities the client user has access to (including hierarchy)
   - Gets the applicant's entity from the `applicant_misc` table
   - Allows access only if the applicant's entity is within the client user's accessible entities
   - If applicant is not in `applicant_misc` table, allows access (for newly created applicants)

### Hierarchy Resolution

The system automatically resolves entity hierarchies:
- Users associated with parent groups can access child entities
- Users associated with super groups can access all descendant entities
- Access is determined by the `entity_relationships` table

## Error Handling

### Common Error Scenarios

1. **Unauthorized Access (401)**: No valid authentication token
2. **Access Denied (404)**: User doesn't have access to the application
3. **Validation Errors (422)**: Form data doesn't meet validation requirements
4. **Server Errors (500)**: Internal server errors during processing

### Detailed Validation Error Cases

#### Request Structure Validation Errors

1. **Missing form_data field:**
```json
{
    "success": false,
    "message": "Validation failed",
    "data": {
        "form_data": ["Form data is required"]
    }
}
```

2. **form_data is not an array (string, number, etc.):**
```json
{
    "success": false,
    "message": "Validation failed",
    "data": {
        "form_data": ["Form data must be an array"]
    }
}
```

3. **form_data is empty array:**
```json
{
    "success": false,
    "message": "Validation failed",
    "data": {
        "form_data": ["Form data cannot be empty"]
    }
}
```

4. **form_data contains non-string values:**
```json
{
    "success": false,
    "message": "Validation failed",
    "data": {
        "form_data.field_name": ["All form field values must be strings"]
    }
}
```

#### Product Form Field Validation Errors

These errors occur after basic request validation passes, when validating against the product's form field configuration:

```json
{
    "success": false,
    "message": "Form validation failed",
    "data": {
        "ApplicantDetails::FORENAME": "The First Name field is required",
        "ApplicantDetails::EMAIL": "The Email must be a valid email address",
        "ApplicantDetails::DATE_OF_BIRTH": "The Date of Birth must be a valid date",
        "ApplicantDetails::AGE": "The Age must be a number",
        "ApplicantDetails::GENDER": "The Gender contains an invalid option"
    }
}
```

### Security Logging

All access attempts are logged for security auditing purposes.

## Usage Examples

### Applicant Saving Their Own Data

```bash
curl -X POST http://localhost:8002/api/v1/applications/save-data \
  -H "Authorization: Bearer {applicant_token}" \
  -H "Content-Type: application/json" \
  -d '{
    "application_id": 1,
    "form_data": {
      "ApplicantDetails::TITLE": "MR",
      "ApplicantDetails::FORENAME": "John",
      "ApplicantDetails::DATE_OF_BIRTH": "1990/01/01"
    }
  }'
```

### Client User Saving Data for Applicant

```bash
curl -X POST http://localhost:8002/api/v1/applications/save-data \
  -H "Authorization: Bearer {client_user_token}" \
  -H "Content-Type: application/json" \
  -d '{
    "application_id": 1,
    "form_data": {
      "ApplicantDetails::TITLE": "MS",
      "ApplicantDetails::FORENAME": "Jane",
      "ApplicantDetails::DATE_OF_BIRTH": "1985/12/15"
    }
  }'
```

## Integration Notes

- The API integrates with the existing ApplicationAccessService for security
- Form validation uses the product's form field configuration
- Data is stored separately from the main application record
- The API supports the same authentication mechanisms as other endpoints
