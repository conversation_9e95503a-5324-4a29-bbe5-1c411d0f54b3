# Applicant Details API

This document describes the API endpoint for retrieving comprehensive applicant details including profile information and all associated applications.

## Overview

The endpoint allows users to retrieve complete information about a specific applicant, including:
- Personal profile information (name, contact details, etc.)
- Associated entities and roles
- All applications for the applicant
- Application completion status for each application
- Product information for each application

This endpoint is designed to provide a complete overview of an applicant for dashboard and detail view purposes.

## Endpoint

### Get Applicant Details

```http
GET /api/v1/applicants/{applicantId}
Authorization: Bearer {token}
```

**Parameters:**
- `applicantId` (path, required): The ID of the applicant

**Access Control:**
- **Applicants**: Can only access their own details
- **Client Users**: Can access applicants from their entity hierarchy
- **Requesters**: Can access applicants from their entity hierarchy  
- **Document Checkers**: Can access applicants from their entity hierarchy

## Response Format

### Success Response (200)

```json
{
    "success": true,
    "data": {
        "applicant": {
            "id": 1,
            "email": "<EMAIL>",
            "user_type": "applicant",
            "profile": {
                "first_name": "<PERSON>",
                "last_name": "<PERSON>",
                "full_name": "<PERSON>",
                "telephone": "+44123456789",
                "address": "123 Main St, London",
                "date_of_birth": "1990-01-01"
            },
            "entities": [
                {
                    "id": 1,
                    "name": "Example Company",
                    "entity_code": "EXC001",
                    "role": "applicant"
                }
            ]
        },
        "applications": [
            {
                "id": 1,
                "external_reference": "APP-000001",
                "status": "in_progress",
                "result": null,
                "consent_date": "2024-01-15",
                "created_at": "2024-01-15 10:30:00",
                "product": {
                    "id": 1,
                    "name": "DBS - Basic check",
                    "code": "DBS001",
                    "variant": "DBS"
                },
                "completion_status": {
                    "is_started": true,
                    "is_complete": false,
                    "status_text": "In progress"
                },
                "submitted_by": {
                    "id": 1,
                    "email": "<EMAIL>",
                    "name": "John Smith"
                }
            },
            {
                "id": 2,
                "external_reference": "APP-000002",
                "status": "completed",
                "result": "clear",
                "consent_date": "2024-01-10",
                "created_at": "2024-01-10 14:20:00",
                "product": {
                    "id": 2,
                    "name": "Reference Check",
                    "code": "REF001",
                    "variant": "Reference"
                },
                "completion_status": {
                    "is_started": true,
                    "is_complete": true,
                    "status_text": "Complete"
                },
                "submitted_by": {
                    "id": 2,
                    "email": "<EMAIL>",
                    "name": "Jane Client"
                }
            }
        ],
        "total_applications": 2
    },
    "message": "Applicant details retrieved successfully"
}
```

### Data Structure

#### Applicant Object
- **`id`**: Unique applicant identifier
- **`email`**: Applicant's email address
- **`user_type`**: Always "applicant" for this endpoint
- **`profile`**: Personal information object
- **`entities`**: Array of associated entities and roles

#### Profile Object
- **`first_name`**: Applicant's first name
- **`last_name`**: Applicant's last name
- **`full_name`**: Concatenated full name
- **`telephone`**: Contact phone number
- **`address`**: Residential address
- **`date_of_birth`**: Date of birth (YYYY-MM-DD format)

#### Applications Array
Each application includes:
- **`id`**: Unique application identifier
- **`external_reference`**: Human-readable reference
- **`status`**: Current application status
- **`result`**: Application result (if completed)
- **`consent_date`**: Date consent was given
- **`created_at`**: Application creation timestamp
- **`product`**: Product information object
- **`completion_status`**: Form completion information
- **`submitted_by`**: Information about who submitted the application

#### Completion Status
- **`is_started`**: Boolean indicating if form data exists
- **`is_complete`**: Boolean indicating if all required fields are filled
- **`status_text`**: Human-readable status ("Not started", "In progress", "Complete")

### Error Responses

#### Invalid Applicant ID (400)
```json
{
    "success": false,
    "message": "Invalid applicant ID"
}
```

#### Applicant Not Found (404)
```json
{
    "success": false,
    "message": "Applicant not found"
}
```

#### Access Denied - Own Data Only (403)
```json
{
    "success": false,
    "message": "You can only access your own data"
}
```

#### Access Denied - Hierarchy (403)
```json
{
    "success": false,
    "message": "You do not have access to this applicant"
}
```

#### Access Denied - User Type (403)
```json
{
    "success": false,
    "message": "Access denied for this user type"
}
```

## Use Cases

### 1. Applicant Dashboard
When an applicant logs in, call this endpoint with their own ID to display:
- Personal profile information
- List of all their applications
- Progress status for each application
- Quick access to continue incomplete applications

### 2. Client User Management
Client users can call this endpoint to:
- View details of applicants in their entity hierarchy
- Monitor application progress
- See completion status across all applications
- Access applicant contact information

### 3. Application Management
Use the response data to:
- Display applicant overview screens
- Show application completion progress
- Provide navigation to specific application forms
- Track application status across multiple products

## Example Usage

```bash
# Get details for applicant ID 1
curl -X GET http://localhost:8001/api/v1/applicants/1 \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"

# Applicant accessing their own details
curl -X GET http://localhost:8001/api/v1/applicants/123 \
  -H "Authorization: Bearer APPLICANT_TOKEN_HERE"

# Client user accessing applicant in their hierarchy
curl -X GET http://localhost:8001/api/v1/applicants/456 \
  -H "Authorization: Bearer CLIENT_USER_TOKEN_HERE"
```

## Security Features

- **Authentication Required**: All requests must include a valid Bearer token
- **Role-Based Access Control**: Users can only access applicants they have permission to view
- **Entity Hierarchy Support**: Client users can access applicants from their entity hierarchy
- **Data Isolation**: Applicants can only access their own data
- **Comprehensive Validation**: All input parameters are validated
- **Null Safety**: All date fields and relationships are protected against null values

## Related Endpoints

- `GET /api/v1/applications/{id}/form-data` - Get detailed form data for a specific application
- `POST /api/v1/applications/save-data` - Save form data to an application
- `GET /api/v1/client-applicants` - Get list of applicants for client users
