# Application Form Data API

This document describes the API endpoint for retrieving application form data with completion status information.

## Overview

The endpoint allows users to retrieve form data for a specific application, including:
- Current form data (key-value pairs)
- Form field definitions and validation rules
- Completion status (not started, in progress, complete)
- Application and product information

This endpoint is designed to support form continuation functionality where users can:
1. Check if a form has been started
2. Retrieve partially completed form data
3. Determine what fields still need to be filled
4. Continue filling out the form from where they left off

## Endpoint

### Get Application Form Data

```http
GET /api/v1/applications/{applicationId}/form-data
Authorization: Bearer {token}
```

**Parameters:**
- `applicationId` (path, required): The ID of the application

**Access Control:**
- **Applicants**: Can only access their own applications
- **Client Users**: Can access applications from their entity hierarchy
- **Requesters**: Can access applications from their entity hierarchy  
- **Document Checkers**: Can access applications from their entity hierarchy

## Response Format

### Success Response (200)

```json
{
    "success": true,
    "data": {
        "application_id": 1,
        "external_reference": "APP-000001",
        "status": "in_progress",
        "form_data": {
            "ApplicantDetails.Title": "Mr",
            "ApplicantDetails.FirstName": "<PERSON>",
            "ApplicantDetails.LastName": "Smith",
            "ApplicantDetails.DateOfBirth": "1990-01-01",
            "ContactDetails.Email": "<EMAIL>"
        },
        "form_fields": [
            {
                "field_name": "ApplicantDetails.Title",
                "field_label": "Title",
                "field_type": "select",
                "field_options": ["Mr", "Mrs", "Ms", "Dr"],
                "is_required": true,
                "sort_order": 1
            },
            {
                "field_name": "ApplicantDetails.FirstName",
                "field_label": "First Name",
                "field_type": "text",
                "field_options": null,
                "is_required": true,
                "sort_order": 2
            }
        ],
        "completion_status": {
            "is_started": true,
            "is_complete": false,
            "status_text": "In progress"
        },
        "product": {
            "id": 1,
            "name": "DBS - Basic check",
            "code": "DBS001",
            "variant": "DBS"
        }
    },
    "message": "Application form data retrieved successfully"
}
```

### Completion Status Values

The `completion_status` object provides three pieces of information:

- **`is_started`** (boolean): `true` if any form data has been saved, `false` if no data exists
- **`is_complete`** (boolean): `true` if all required fields have been filled, `false` otherwise
- **`status_text`** (string): Human-readable status with possible values:
  - `"Not started"`: No form data has been saved
  - `"In progress"`: Some form data exists but required fields are missing
  - `"Complete"`: All required fields have been filled

### Form Data Structure

- **`form_data`**: Key-value pairs of submitted form data from the `application_data` table
- **`form_fields`**: Array of available form fields with their definitions and validation rules

### Error Responses

#### Invalid Application ID (400)
```json
{
    "success": false,
    "message": "Invalid application ID"
}
```

#### Application Not Found / Access Denied (404)
```json
{
    "success": false,
    "message": "Application not found"
}
```

#### Access Denied (403)
```json
{
    "success": false,
    "message": "Access denied for this user type"
}
```

## Use Cases

### 1. Form Continuation
When a user returns to fill out a form, the frontend can:
1. Call this endpoint to check completion status
2. If `is_started` is `true`, populate the form with existing data
3. If `is_complete` is `false`, highlight missing required fields
4. Allow the user to continue from where they left off

### 2. Form Validation
Before allowing form submission, check if `is_complete` is `true` to ensure all required fields are filled.

### 3. Progress Tracking
Use the `status_text` to display progress information to users and administrators.

## Example Usage

```bash
# Get form data for application ID 1
curl -X GET http://localhost:8001/api/v1/applications/1/form-data \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## Security Features

- **Authentication Required**: All requests must include a valid Bearer token
- **Role-Based Access Control**: Users can only access applications they have permission to view
- **Entity Hierarchy Support**: Client users can access applications from their entity hierarchy
- **Data Isolation**: Applicants can only access their own application data

## Related Endpoints

- `POST /api/v1/applications/save-data` - Save form data to an application
- `GET /api/v1/applications/{id}` - Get complete application details
