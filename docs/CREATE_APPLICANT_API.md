# Create Applicant API

This document describes the API endpoint for creating new applicants with associated applications.

## Overview

This API allows client users to create new applicants and automatically generate applications for selected products. The applicant is linked to an entity through the selected job role.

## Endpoint

**POST** `/api/v1/addapplicant`

### Authentication

- **Required**: Bearer token
- **User Types**: `client_user`, `requester`, `document_checker`

### Request Body

```json
{
    "first_name": "<PERSON>",
    "last_name": "<PERSON>",
    "email": "<EMAIL>",
    "phone": "+44 ***********",
    "job_role_id": 1
}
```

### Request Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `first_name` | string | Yes | Applicant's first name (max 255 chars) |
| `last_name` | string | Yes | Applicant's last name (max 255 chars) |
| `email` | string | Yes | Unique email address (max 255 chars) |
| `phone` | string | Yes | Phone number (max 20 chars) |
| `job_role_id` | integer | Yes | Valid job role ID that user has access to |

**Note**: Products are automatically selected based on the job role. All products associated with the selected job role will have applications created automatically.

### Validation Rules

- **Email**: Must be unique across all portal users
- **Job Role**: Must exist and user must have access to the job role's entity
- **Job Role Products**: Selected job role must have at least one product associated
- **Access Control**: User must have access to the entity associated with the selected job role

## Response

### Success Response (201 Created)

```json
{
    "success": true,
    "message": "Applicant created successfully",
    "data": {
        "applicant": {
            "id": 123,
            "email": "<EMAIL>",
            "first_name": "John",
            "last_name": "Smith",
            "phone": "+44 ***********",
            "full_name": "John Smith"
        },
        "entity": {
            "id": 5,
            "name": "ABC Company Ltd",
            "entity_code": "ABC001"
        },
        "job_role": {
            "id": 1,
            "job_label": "Cleaner",
            "job_title": "Office Cleaner"
        },
        "applications": [
            {
                "id": 456,
                "product_id": 1,
                "status": "draft",
                "billing": {
                    "admin_fee": 40.00,
                    "supplier_fee": 10.00,
                    "total_fee": 50.00,
                    "self_payment": true,
                    "currency": "GBP"
                }
            },
            {
                "id": 457,
                "product_id": 2,
                "status": "draft",
                "billing": {
                    "admin_fee": 35.00,
                    "supplier_fee": 15.00,
                    "total_fee": 50.00,
                    "self_payment": true,
                    "currency": "GBP"
                }
            }
        ],
        "generated_password": "randomPassword123",
        "total_applications": 2,
        "products_auto_selected": 2
    },
    "message": "Applicant created successfully with 2 applications auto-generated"
}
```

### Error Responses

#### Job Role Has No Products (400 Bad Request)

```json
{
    "success": false,
    "message": "Selected job role has no products associated with it",
    "data": []
}
```

#### Validation Error (422 Unprocessable Entity)

```json
{
    "success": false,
    "message": "The given data was invalid.",
    "errors": {
        "email": ["This email address is already registered"],
        "job_role_id": ["Selected job role is invalid"],
        "product_ids": ["At least one product must be selected"]
    }
}
```

#### Access Denied (403 Forbidden)

```json
{
    "success": false,
    "message": "You do not have access to create applicants for this job role"
}
```

#### Unauthorized (401 Unauthorized)

```json
{
    "success": false,
    "message": "Unauthenticated."
}
```

## Business Logic

### Applicant Creation Process

1. **Validation**: Validate all input fields and check permissions
2. **Job Role Validation**: Ensure job role has associated products
3. **User Creation**: Create new `PortalUser` with type `applicant`
4. **Profile Creation**: Create user profile with personal details in `portal_user_profiles`
5. **Entity Association**: Link applicant to entity via `applicant_misc` table
6. **Automatic Product Selection**: Fetch all products associated with the job role
7. **Application Generation**: Create draft applications for ALL job role products automatically
8. **Billing Snapshots**: Create billing snapshots with pricing information for each application
9. **Password Generation**: Generate random password for applicant login

### Entity Association

- Applicant is associated with the entity through the selected job role
- The `applicant_misc` table links the applicant to the entity
- Entity ID is derived from the job role's `entity_id`

### Access Control

- Only client users can create applicants
- User must have access to the job role's entity (including hierarchy)
- Follows the same entity hierarchy rules as other APIs

### Generated Data

- **Password**: Random 12-character password for applicant login
- **Status**: Applicant status set to 1 (active), employment status to 1 (pending)
- **Applications**: All applications created with 'draft' status
- **Billing Snapshots**: Pricing information captured at time of application creation

### Billing Snapshots

Each application gets a billing snapshot that captures:
- **Admin Fee**: Administrative fee for the product
- **Supplier Fee**: Supplier/processing fee for the product
- **Self Payment**: Whether the applicant pays directly (from job role setting)
- **Pricing Source**: Determined by entity hierarchy (entity → parent → super group → default)

The billing snapshot preserves pricing at the time of application creation, ensuring consistent billing even if prices change later.

### Automatic Product Selection Benefits

- **Reduced Human Error**: No manual product selection eliminates the risk of missing required products
- **Consistency**: All applicants for the same job role get identical product applications
- **Efficiency**: Streamlined process requires minimal input from users
- **Completeness**: Ensures all relevant products for the job role are included automatically
- **Maintenance**: Product associations are managed at the job role level, not per applicant

## Integration with Frontend

This API is designed to work with the frontend form that:

1. **Job Role Selection**: Uses `/api/v1/job-roles` to populate job role dropdown
2. **Product Preview**: Shows products associated with selected job role for user awareness (read-only)
3. **Applicant Details**: Collects basic applicant information (name, email, phone)
4. **Automatic Processing**: Backend automatically creates applications for all job role products

### Frontend Workflow

```
1. Load job roles → GET /api/v1/job-roles
2. User selects job role → Auto-populate products from job role data
3. User modifies product selection (optional)
4. User fills applicant details
5. Submit form → POST /api/v1/addapplicant
6. Display success message with generated password
```

## Security Considerations

- **Email Uniqueness**: Prevents duplicate applicant accounts
- **Entity Access Control**: Ensures users can only create applicants for entities they manage
- **Password Security**: Generated passwords should be communicated securely to applicants
- **Audit Trail**: All creation actions are logged with the creating user ID

## Related APIs

- **Job Roles**: `GET /api/v1/job-roles` - Get available job roles with products
- **Applications**: `GET /api/v1/applications` - View created applications
- **Applicant Login**: `POST /api/v1/auth/login` - Unified authentication for all user types
