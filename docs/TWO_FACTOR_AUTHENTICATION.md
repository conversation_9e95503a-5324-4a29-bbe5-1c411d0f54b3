# Simple Two-Factor Authentication (2FA) Implementation

This document describes the simple Two-Factor Authentication implementation using Google/Microsoft Authenticator for the API Interface project.

## Overview

The 2FA implementation uses TOTP (Time-based One-Time Password) algorithm compatible with Google Authenticator, Microsoft Authenticator, and other TOTP-based authenticator apps.

### Simple Flow Logic

1. **When `two_factor_enabled = true` and `secret = null`**: <PERSON><PERSON> automatically generates secret and returns QR code
2. **When `two_factor_enabled = true` and `secret exists`**: <PERSON>gin requires PIN verification
3. **When `two_factor_enabled = false`**: Normal login without 2FA

## Features

- ✅ Automatic QR code generation on first login
- ✅ Simple PIN verification
- ✅ Secure secret storage (encrypted)
- ✅ Only 3 essential endpoints

## API Endpoints

### 3 Essential Endpoints

#### 1. Login (Handles all scenarios)
```http
POST /api/v1/auth/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password"
}
```

**Response when 2FA is disabled:**
```json
{
    "success": true,
    "data": {
        "user": { ... },
        "entities": [ ... ],
        "token": "bearer_token_here",
        "token_type": "Bearer"
    },
    "message": "User logged in successfully"
}
```

**Response when 2FA is enabled but not set up (secret is null):**
```json
{
    "success": true,
    "data": {
        "requires_two_factor_setup": true,
        "qr_code_image": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nMjAwJy...",
        "temp_secret": "JBSWY3DPEHPK3PXP",
        "message": "Please set up two-factor authentication by scanning the QR code"
    },
    "message": "Two-factor authentication setup required"
}
```

**Response when 2FA is enabled and set up (secret exists):**
```json
{
    "success": true,
    "data": {
        "requires_two_factor": true,
        "message": "Two-factor authentication required"
    },
    "message": "Two-factor authentication required"
}
```

#### 2. Verify PIN (when 2FA is set up)
```http
POST /api/v1/auth/verify-pin
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password",
    "pin": "123456"
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "user": { ... },
        "entities": [ ... ],
        "token": "bearer_token_here",
        "token_type": "Bearer"
    },
    "message": "User logged in successfully with two-factor authentication"
}
```

#### 3. Logout
```http
POST /api/v1/auth/logout
Authorization: Bearer {token}
```

**Response:**
```json
{
    "success": true,
    "data": [],
    "message": "User logged out successfully"
}
```

## Setup Process

### Simple User Flow

1. **Admin enables 2FA**: Set `two_factor_enabled = true` in user profile (secret remains null)
2. **User attempts login**: Call `/api/v1/auth/login` with email/password
3. **System automatically generates secret and returns QR code**: Response includes QR code URL and base64 image
4. **User scans QR code**: Use Google/Microsoft Authenticator to scan the QR code
5. **User enters PIN**: Call `/api/v1/auth/verify-pin` with email, password, and PIN from authenticator app
6. **Login complete**: User receives token and is logged in

### For Developers

1. **Install Package**: The `pragmarx/google2fa-laravel` package is already installed
2. **Database Fields**: The `portal_user_profiles` table already has the required fields:
   - `two_factor_enabled` (boolean)
   - `two_factor_secret` (string, encrypted)

## Security Features

- **Encrypted Secrets**: 2FA secrets are encrypted before storage
- **Automatic Setup**: Secret is generated and saved automatically on first login
- **Time-based Codes**: Uses TOTP algorithm with 30-second windows

## Compatible Authenticator Apps

- Google Authenticator
- Microsoft Authenticator
- Authy
- 1Password
- LastPass Authenticator
- Any TOTP-compatible app

## Error Handling

The API returns appropriate error messages for:
- Invalid credentials
- Invalid 2FA PINs
- 2FA not enabled
- Network/server errors

## Database Schema

The implementation uses existing database fields:

```sql
-- portal_user_profiles table
two_factor_enabled BOOLEAN DEFAULT FALSE
two_factor_secret VARCHAR(255) NULLABLE -- Encrypted
```

## Testing

Use the provided test script to verify functionality:

```bash
php scripts/test_2fa.php
```
