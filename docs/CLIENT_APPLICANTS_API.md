# Client Applicants API

This document describes the API endpoint for retrieving all applicants accessible to client users with status summary and filtering capabilities.

## Overview

This API provides client users with a comprehensive view of all applicants they can access based on entity hierarchy. It includes:

- **Status Summary Cards**: Total counts for different applicant statuses
- **Detailed Applicant List**: Searchable and filterable list of applicants
- **Hierarchical Access Control**: Uses SolidFuse logic for proper entity access
- **Security**: Implements proper input validation and access control

## Endpoint

**GET** `/api/v1/client-applicants`

### Authentication

- **Required**: Bearer token
- **User Types**: `client_user`, `requester`, `doc_checker`
- **Excluded**: `applicant` users cannot access this endpoint

### Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `search` | string | No | - | Search term for applicant name, email, or organization |
| `status_filter` | string | No | `all` | Filter by status: `all`, `in_progress`, `further_action_pending`, `staff_review_pending`, `complete` |
| `page` | integer | No | `1` | Page number for pagination |
| `per_page` | integer | No | `20` | Number of results per page (max: 100) |

### Example Request

```http
GET /api/v1/client-applicants?search=john&status_filter=in_progress&page=1&per_page=10
Authorization: Bearer {token}
```

## Response Format

### Success Response (200 OK)

```json
{
    "success": true,
    "message": "Client applicants retrieved successfully",
    "data": {
        "status_summary": {
            "total_applicants": 15,
            "in_progress": 8,
            "further_action_pending": 2,
            "staff_review_pending": 3,
            "complete": 2
        },
        "applicants": {
            "0": {
                "applicant_id": 123,
                "name": "John Smith",
                "email": "<EMAIL>",
                "reference": "123",
                "organization": "ABC Company Ltd",
                "date_created": "2024-01-15T10:30:00.000000Z",
                "requested_checks": [
                    {
                        "product_code": "DBS",
                        "product_name": "DBS Check",
                        "status": "in_progress"
                    },
                    {
                        "product_code": "RTW",
                        "product_name": "Right to Work",
                        "status": "pending"
                    }
                ],
                "status": "in_progress",
                "status_display": "In Progress"
            },
            "1": {
                "applicant_id": 124,
                "name": "Jane Doe",
                "email": "<EMAIL>",
                "reference": "124",
                "organization": "XYZ Corporation",
                "date_created": "2024-01-16T14:20:00.000000Z",
                "requested_checks": [
                    {
                        "product_code": "AC",
                        "product_name": "Academic Check",
                        "status": "completed"
                    }
                ],
                "status": "complete",
                "status_display": "Complete"
            }
        },
        "pagination": {
            "current_page": 1,
            "per_page": 10,
            "total": 15,
            "total_pages": 2,
            "has_more": true
        }
    }
}
```

## Status Categories

The API categorizes applicants into the following status groups based on their application statuses:

### In Progress
- Applications with status: `in_progress`, `pending`, `draft`
- Applicants with no applications (default status)

### Further Action Pending
- Applications with status: `rejected`, `requires_action`, `incomplete`
- Requires client or applicant action to proceed

### Staff Review Pending
- Applications with status: `under_review`, `reviewing`, `submitted`
- Currently being processed by staff

### Complete
- All applications have status: `completed`
- All checks have been finished

## Data Fields

### Status Summary
- `total_applicants`: Total number of accessible applicants
- `in_progress`: Count of applicants with in-progress applications
- `further_action_pending`: Count of applicants requiring action
- `staff_review_pending`: Count of applicants under review
- `complete`: Count of applicants with all checks complete

### Applicant Data
- `applicant_id`: Unique applicant identifier
- `name`: Full name (first_name + last_name)
- `email`: Applicant email address
- `reference`: Generated reference number (padded applicant ID)
- `organization`: Entity/organization name
- `date_created`: When the applicant was created
- `requested_checks`: Array of products/checks for this applicant
- `status`: Overall status category
- `status_display`: Human-readable status name

### Requested Checks
Each check includes:
- `product_code`: Product identifier code
- `product_name`: Human-readable product name
- `status`: Current application status for this product

## Access Control

### Entity Hierarchy
- Users can access applicants from their associated entities
- Includes access to child entities in the hierarchy
- Follows Super Group → Parent Group → Client structure
- Uses existing SolidFuse logic for inheritance

### Security Features
- **Input Validation**: All parameters are validated and sanitized
- **SQL Injection Protection**: Uses parameterized queries
- **Access Control**: Entity-based access restrictions
- **Rate Limiting**: Standard API rate limits apply

## Error Responses

### Forbidden Access (403)
```json
{
    "success": false,
    "message": "Access denied for this user type"
}
```

### Validation Error (422)
```json
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "status_filter": ["Invalid status filter. Must be one of: all, in_progress, further_action_pending, staff_review_pending, complete"]
    }
}
```

### Server Error (500)
```json
{
    "success": false,
    "message": "Failed to retrieve client applicants: [error details]"
}
```

## Usage Examples

### Get All Applicants
```bash
curl -X GET "http://localhost:8001/api/v1/client-applicants" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### Search for Specific Applicant
```bash
curl -X GET "http://localhost:8001/api/v1/client-applicants?search=john" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### Filter by Status
```bash
curl -X GET "http://localhost:8001/api/v1/client-applicants?status_filter=complete" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### Paginated Results
```bash
curl -X GET "http://localhost:8001/api/v1/client-applicants?page=2&per_page=5" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## Implementation Notes

- Uses existing `ApplicationAccessService` for entity hierarchy logic
- Leverages SolidFuse package for shared business logic
- Implements proper input sanitization and validation
- Returns numbered object keys for applicants array as per user preference
- Supports search across name, email, and organization fields
- Status determination is based on all applications for each applicant
- Reference numbers are generated using zero-padded applicant IDs

## Related APIs

- **Applications**: `GET /api/v1/applications` - View applications
- **Create Applicant**: `POST /api/v1/addapplicant` - Create new applicants
- **Job Roles**: `GET /api/v1/job-roles` - Get available job roles
- **Authentication**: `POST /api/v1/auth/login` - User authentication
