# Save Application Data API - Complete Integration Guide

## Overview

The `saveapplicationdata` endpoint allows saving form data for applications. This document provides complete requirements including how to handle multiple previous addresses.

## Endpoint Details

**URL:** `POST /api/v1/applications/save-data`

**Headers:**
```
Content-Type: application/json
Accept: application/json
Authorization: Bearer {token}
```

## Request Structure

### Required Fields

```json
{
  "application_id": 123,
  "form_data": {
    // Flat key-value pairs with string values
  }
}
```

### Validation Rules

1. **application_id** (integer, required)
   - Must exist in the applications table
   - User must have access to this application

2. **form_data** (object, required)
   - Must be a flat key-value object (not nested)
   - Cannot be empty
   - All values must be strings
   - Keys use `::` separator for hierarchy

## Field Naming Convention

### Basic Fields
```json
{
  "ApplicantDetails::TITLE": "MR",
  "ApplicantDetails::FORENAME": "JOHN",
  "ApplicantDetails::PRESENT_SURNAME": "SMITH",
  "ApplicantDetails::DATE_OF_BIRTH": "1990-01-01",
  "ApplicantDetails::GENDER": "male",
  "ApplicantDetails::NI_NUMBER": ""
}
```

**Note:** The `NI_NUMBER` field is optional and can be empty. It will not cause validation errors if left blank.

### Current Address
```json
{
  "CurrentAddress::ADDRESS_LINE1": "123 MAIN STREET",
  "CurrentAddress::ADDRESS_LINE2": "APARTMENT 4B",
  "CurrentAddress::ADDRESS_TOWN": "LONDON",
  "CurrentAddress::ADDRESS_COUNTY": "GREATER LONDON",
  "CurrentAddress::POSTCODE": "SW1A 1AA",
  "CurrentAddress::COUNTRY_CODE": "GB",
  "CurrentAddress::RESIDENT_FROM_YEAR_MONTH": "2020-01"
}
```

### Previous Addresses (Multiple)

For multiple previous addresses, use indexed field names with dot notation:

#### First Previous Address (Index 0)
```json
{
  "PreviousAddress::0::Address::AddressLine1": "456 OLD STREET",
  "PreviousAddress::0::Address::AddressLine2": "FLAT 2",
  "PreviousAddress::0::Address::AddressTown": "MANCHESTER",
  "PreviousAddress::0::Address::AddressCounty": "GREATER MANCHESTER",
  "PreviousAddress::0::Address::Postcode": "M1 1AA",
  "PreviousAddress::0::Address::CountryCode": "GB",
  "PreviousAddress::0::ResidentDates::ResidentFromGyearMonth": "2018-01",
  "PreviousAddress::0::ResidentDates::ResidentToGyearMonth": "2019-12"
}
```

#### Second Previous Address (Index 1)
```json
{
  "PreviousAddress::1::Address::AddressLine1": "789 ANOTHER ROAD",
  "PreviousAddress::1::Address::AddressLine2": "",
  "PreviousAddress::1::Address::AddressTown": "BIRMINGHAM",
  "PreviousAddress::1::Address::AddressCounty": "WEST MIDLANDS",
  "PreviousAddress::1::Address::Postcode": "B1 1AA",
  "PreviousAddress::1::Address::CountryCode": "GB",
  "PreviousAddress::1::ResidentDates::ResidentFromGyearMonth": "2016-06",
  "PreviousAddress::1::ResidentDates::ResidentToGyearMonth": "2017-12"
}
```

#### Third Previous Address (Index 2)
```json
{
  "PreviousAddress::2::Address::AddressLine1": "321 PREVIOUS LANE",
  "PreviousAddress::2::Address::AddressLine2": "UNIT 5",
  "PreviousAddress::2::Address::AddressTown": "BRISTOL",
  "PreviousAddress::2::Address::AddressCounty": "BRISTOL",
  "PreviousAddress::2::Address::Postcode": "BS1 1AA",
  "PreviousAddress::2::Address::CountryCode": "GB",
  "PreviousAddress::2::ResidentDates::ResidentFromGyearMonth": "2014-03",
  "PreviousAddress::2::ResidentDates::ResidentToGyearMonth": "2016-05"
}
```

## Complete Example Request

### Frontend Data Transformation

**FROM (Current Frontend Format):**
```json
{
  "eBulkApplication": {
    "ApplicantDetails": {
      "Title": "MR",
      "Forename": "JOHN",
      "PresentSurname": "SMITH",
      "DateOfBirth": "1990-01-01",
      "Gender": "male"
    },
    "CurrentAddress": {
      "Address": {
        "AddressLine1": "123 MAIN STREET",
        "AddressLine2": "APARTMENT 4B",
        "AddressTown": "LONDON",
        "AddressCounty": "GREATER LONDON",
        "Postcode": "SW1A 1AA",
        "CountryCode": "GB"
      },
      "ResidentFromGyearMonth": "2020-01"
    },
    "PreviousAddress": [
      {
        "Address": {
          "AddressLine1": "456 OLD STREET",
          "AddressLine2": "FLAT 2",
          "AddressTown": "MANCHESTER",
          "AddressCounty": "GREATER MANCHESTER",
          "Postcode": "M1 1AA",
          "CountryCode": "GB"
        },
        "ResidentDates": {
          "ResidentFromGyearMonth": "2018-01",
          "ResidentToGyearMonth": "2019-12"
        }
      },
      {
        "Address": {
          "AddressLine1": "789 ANOTHER ROAD",
          "AddressTown": "BIRMINGHAM",
          "AddressCounty": "WEST MIDLANDS",
          "Postcode": "B1 1AA",
          "CountryCode": "GB"
        },
        "ResidentDates": {
          "ResidentFromGyearMonth": "2016-06",
          "ResidentToGyearMonth": "2017-12"
        }
      }
    ]
  }
}
```

**TO (Required API Format):**
```json
{
  "application_id": 123,
  "form_data": {
    "ApplicantDetails::TITLE": "MR",
    "ApplicantDetails::FORENAME": "JOHN",
    "ApplicantDetails::PRESENT_SURNAME": "SMITH",
    "ApplicantDetails::DATE_OF_BIRTH": "1990-01-01",
    "ApplicantDetails::GENDER": "male",
    "CurrentAddress::ADDRESS_LINE1": "123 MAIN STREET",
    "CurrentAddress::ADDRESS_LINE2": "APARTMENT 4B",
    "CurrentAddress::ADDRESS_TOWN": "LONDON",
    "CurrentAddress::ADDRESS_COUNTY": "GREATER LONDON",
    "CurrentAddress::POSTCODE": "SW1A 1AA",
    "CurrentAddress::COUNTRY_CODE": "GB",
    "CurrentAddress::RESIDENT_FROM_YEAR_MONTH": "2020-01",
    "PreviousAddress::0::Address::AddressLine1": "456 OLD STREET",
    "PreviousAddress::0::Address::AddressLine2": "FLAT 2",
    "PreviousAddress::0::Address::AddressTown": "MANCHESTER",
    "PreviousAddress::0::Address::AddressCounty": "GREATER MANCHESTER",
    "PreviousAddress::0::Address::Postcode": "M1 1AA",
    "PreviousAddress::0::Address::CountryCode": "GB",
    "PreviousAddress::0::ResidentDates::ResidentFromGyearMonth": "2018-01",
    "PreviousAddress::0::ResidentDates::ResidentToGyearMonth": "2019-12",
    "PreviousAddress::1::Address::AddressLine1": "789 ANOTHER ROAD",
    "PreviousAddress::1::Address::AddressLine2": "",
    "PreviousAddress::1::Address::AddressTown": "BIRMINGHAM",
    "PreviousAddress::1::Address::AddressCounty": "WEST MIDLANDS",
    "PreviousAddress::1::Address::Postcode": "B1 1AA",
    "PreviousAddress::1::Address::CountryCode": "GB",
    "PreviousAddress::1::ResidentDates::ResidentFromGyearMonth": "2016-06",
    "PreviousAddress::1::ResidentDates::ResidentToGyearMonth": "2017-12"
  }
}
```

## Data Validation Requirements

### Field Name Transformation
The API automatically transforms field names from the API format to the internal validation format:
- `ApplicantDetails::NI_NUMBER` → `NINumber` (nullable/optional)
- `ApplicantDetails::TITLE` → `Title` (required)
- `CurrentAddress::ADDRESS_LINE1` → `CurrentAddress.Address.AddressLine1` (required)

### Field Value Transformation
The API also transforms field values to the correct format for validation:
- `AdditionalApplicantDetails::DECLARATION_BY_APPLICANT: "y"` → `DeclarationByApplicant: true` (boolean)
- `AdditionalApplicantDetails::UNSPENT_CONVICTIONS: "n"` → `UnspentConvictions: "n"` (string)
- Fields with `accepted` validation rules require boolean `true`, not string `"y"`

### Business Logic Validation
The API enforces specific business rules beyond basic field validation:

1. **Birth Surname Until** - **MANDATORY**
   - Field: `AdditionalApplicantDetails::BIRTH_SURNAME_UNTIL`
   - Cannot be empty or null
   - Must be a valid year (e.g., "1995")

2. **Postcode Validation** - **CONDITIONAL**
   - **UK Addresses (CountryCode = "GB")**: Postcode is **REQUIRED**
     - Current Address: `CurrentAddress::POSTCODE` (required if `CurrentAddress::COUNTRY_CODE` = "GB")
     - Previous Address: `PreviousAddress::X::Address::Postcode` (required if `PreviousAddress::X::Address::CountryCode` = "GB")
   - **Non-UK Addresses (CountryCode ≠ "GB")**: Postcode is **OPTIONAL**
     - US, FR, DE, etc.: Postcode can be empty or provided
     - No validation errors for empty postcodes on non-UK addresses

3. **NI Number** - **OPTIONAL**
   - Field: `ApplicantDetails::NI_NUMBER`
   - Can be empty string
   - No validation errors for empty values

4. **Identity Verification Fields** - **EXCLUDED**
   - Fields: `ApplicantIdentityDetails::IDENTITY_VERIFIED`, `ApplicantIdentityDetails::EVIDENCE_CHECKED_BY`
   - These fields are **NOT processed** during the save operation
   - They are handled later in the application workflow
   - Should not be included in the `form_data` when saving application data

### Examples

**✅ Valid UK Address:**
```json
{
  "CurrentAddress::COUNTRY_CODE": "GB",
  "CurrentAddress::POSTCODE": "SW1A 1AA"
}
```

**❌ Invalid UK Address (missing postcode):**
```json
{
  "CurrentAddress::COUNTRY_CODE": "GB",
  "CurrentAddress::POSTCODE": ""
}
```

**✅ Valid US Address (postcode optional):**
```json
{
  "CurrentAddress::COUNTRY_CODE": "US",
  "CurrentAddress::POSTCODE": ""
}
```

**✅ Valid US Address (with postcode):**
```json
{
  "CurrentAddress::COUNTRY_CODE": "US",
  "CurrentAddress::POSTCODE": "12345"
}
```

### Date Formats
- **Date of Birth**: `YYYY-MM-DD` (e.g., "1990-01-01")
- **Year-Month dates**: `YYYY-MM` (e.g., "2020-01")

### Address History Rules
- Must cover 5 years of address history for DBS applications
- No gaps allowed between address periods
- Previous addresses must be in chronological order
- All address periods must have valid from/to dates

### Required Fields for Previous Addresses
- `AddressLine1` (required)
- `AddressTown` (required)
- `CountryCode` (required, 2-letter code)
- `ResidentFromGyearMonth` (required, YYYY-MM format)
- `ResidentToGyearMonth` (required, YYYY-MM format)

### Optional Fields for Previous Addresses
- `AddressLine2`
- `AddressCounty`
- `Postcode`

## Response Examples

### Success Response (200)
```json
{
  "success": true,
  "data": {
    "application_id": 123
  },
  "message": "Application data saved successfully"
}
```

### Validation Error (422)
```json
{
  "success": false,
  "message": "Validation failed",
  "data": {
    "application_id": ["Application ID is required"],
    "form_data": ["Form data is required"]
  }
}
```

### Business Logic Validation Error (422)
```json
{
  "success": false,
  "message": "Validation failed",
  "data": {
    "form_data.AdditionalApplicantDetails::BIRTH_SURNAME_UNTIL": ["Birth surname until date is mandatory and cannot be empty."],
    "form_data.CurrentAddress::POSTCODE": ["Postcode is required for UK addresses."],
    "form_data.PreviousAddress::0::Address::Postcode": ["Postcode is required for UK addresses."]
  }
}
```

### Form Validation Error (422)
```json
{
  "success": false,
  "message": "Form validation failed",
  "data": {
    "PreviousAddress::0::Address::AddressLine1": ["Address Line 1 is required"],
    "PreviousAddress::0::ResidentDates::ResidentFromGyearMonth": ["Invalid date format"]
  }
}
```

### Payment Required Error (402) - For Applicants Only
```json
{
  "success": false,
  "message": "Payment required before saving application data",
  "data": {
    "payment_required": true,
    "total_amount": "50.00",
    "currency": "GBP",
    "applications_requiring_payment": [
      {
        "application_id": 123,
        "amount": "50.00"
      }
    ]
  }
}
```

## Common Issues and Solutions

### Issue 1: 422 Error - "Application ID is required"
**Problem:** Missing `application_id` field in request body
**Solution:** Ensure `application_id` is included at the root level of the request

### Issue 2: 422 Error - "Form data is required"
**Problem:** Missing `form_data` field or sending nested objects
**Solution:** Include `form_data` as a flat object with string values

### Issue 3: 422 Error - "Form data must be an array"
**Problem:** Sending `form_data` as a string or other type
**Solution:** Ensure `form_data` is an object/array with key-value pairs

### Issue 4: Business logic validation errors
**Problem:** Birth surname until is empty, or postcode missing for UK addresses
**Solution:**
- Ensure `AdditionalApplicantDetails::BIRTH_SURNAME_UNTIL` has a valid year value
- Ensure `CurrentAddress::POSTCODE` is provided for UK addresses (CountryCode = "GB")
- Ensure `PreviousAddress::X::Address::Postcode` is provided for UK previous addresses

### Issue 5: Declaration validation error ("DeclarationByApplicant": "Validation failed")
**Problem:** Declaration field sent as string "y" but validation expects boolean true
**Solution:** The API now automatically converts "y" to boolean true for declaration fields
- This is handled automatically by the field value transformation
- No frontend changes needed

### Issue 6: Form validation errors for previous addresses
**Problem:** Missing required fields or invalid date formats
**Solution:** Ensure all required fields are present and dates are in YYYY-MM format

## Testing the Integration

### Test with curl
```bash
curl -X POST http://localhost:8001/api/v1/applications/save-data \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "application_id": 123,
    "form_data": {
      "ApplicantDetails::TITLE": "MR",
      "ApplicantDetails::FORENAME": "JOHN",
      "CurrentAddress::ADDRESS_LINE1": "123 MAIN STREET",
      "PreviousAddress::0::Address::AddressLine1": "456 OLD STREET",
      "PreviousAddress::0::ResidentDates::ResidentFromGyearMonth": "2018-01"
    }
  }'
```

### Validation Checklist
- [ ] `application_id` is included and is a valid integer
- [ ] `form_data` is a flat object with string values
- [ ] All field names use the correct `::` separator format
- [ ] Previous addresses use indexed format (`::0::`, `::1::`, etc.)
- [ ] All dates are in the correct format (YYYY-MM-DD or YYYY-MM)
- [ ] All required fields for previous addresses are included
- [ ] Address history covers the required 5-year period
- [ ] **Business Logic Requirements:**
  - [ ] `AdditionalApplicantDetails::BIRTH_SURNAME_UNTIL` has a valid year value (mandatory)
  - [ ] `CurrentAddress::POSTCODE` is provided for UK addresses (CountryCode = "GB")
  - [ ] `PreviousAddress::X::Address::Postcode` is provided for UK previous addresses
  - [ ] `ApplicantDetails::NI_NUMBER` can be empty (optional)
  - [ ] **Excluded Fields:** Do NOT include identity verification fields:
    - [ ] `ApplicantIdentityDetails::IDENTITY_VERIFIED` (excluded)
    - [ ] `ApplicantIdentityDetails::EVIDENCE_CHECKED_BY` (excluded)

## Security Notes

- Only authenticated users can access this endpoint
- Users can only save data for applications they have access to
- Applicants may need to complete payment before saving data
- All access attempts are logged for security auditing
