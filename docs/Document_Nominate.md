# Document Nomination System - Frontend Implementation Guide

## Overview
DBS document verification follows a **three-route system** with specific document group requirements. Users must nominate documents with detailed data for identity verification.

## Core Rules
- **Route Priority**: Always try Route 1 → Route 2 → Route 3 (sequential fallback)
- **Document Groups**: 1, 1a, 2a, 2b, a, b1, b2
- **One Document Per Group**: Each document from a group can only be counted once
- **Address Confirmation**: At least one document must confirm current address

### Route Requirements
- **Route 1**: 1 from Group 1 + 2 additional from Groups 1/2a/2b + address confirmation
- **Route 2**: 1 from Group 2a + 2 additional from Groups 2a/2b + external ID validation
- **Route 3**: Birth certificate + 1 from Group 2a + 3 additional from Groups 2a/2b

## API Endpoints

### 1. Get Available Documents
```
GET /api/v1/applications/{id}/documents
Authorization: Bearer {token}
```

**Access Control:**
- `applicant`: Can only access their own applications
- `client_user`, `requester`, `doc_checker`: Can access applications from their entity hierarchy

**Response:**
```json
{
  "success": true,
  "data": {
    "applicant_context": {
      "nationality": "British",
      "is_uk_national": true,
      "work_type": "paid",
      "product_code": "DBSEC"
    },
    "recommended_route": 1,
    "available_routes": [1, 2, 3],
    "route_requirements": {
      "route_1": {
        "description": "Primary route - requires 1 document from Group 1",
        "required_groups": {"1": {"min": 1, "max": 1}},
        "total_documents": 3,
        "address_confirmation_required": true
      }
    },
    "available_documents": {
      "group_1": [
        {
          "id": 1,
          "name": "Passport (Any Country)",
          "document_group": "1",
          "requires_photo": true,
          "confirms_address": false,
          "data_fields": {
            "passport_number": {"type": "string", "required": true},
            "issue_country": {"type": "string", "required": true},
            "issue_date": {"type": "date", "required": true},
            "expiry_date": {"type": "date", "required": true}
          }
        }
      ]
    },
    "current_nominations": []
  }
}
```

### 2. Validate Document Nominations
```
POST /api/v1/applications/{id}/validate
Authorization: Bearer {token}
Content-Type: application/json
```

**Access Control:**
- Same as above - users can only validate documents for applications they have access to

**Request:**
```json
{
  "route_number": 1,
  "nominated_documents": [
    {
      "document_type_id": 1,
      "document_data": {
        "passport_number": "*********",
        "issue_country": "United Kingdom",
        "issue_date": "2020-01-15",
        "expiry_date": "2030-01-15"
      },
      "confirms_address": false
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "validation_result": {
      "is_valid": true,
      "route_completed": true,
      "can_proceed": true
    },
    "route_analysis": {
      "requirements_met": {
        "group_1_documents": {"required": 1, "provided": 1, "met": true},
        "total_documents": {"required": 3, "provided": 3, "met": true},
        "address_confirmation": {"required": true, "provided": true, "met": true}
      }
    },
    "document_validations": [
      {
        "document_type_id": 1,
        "is_valid": true,
        "errors": [],
        "warnings": []
      }
    ],
    "next_steps": {
      "action": "complete_route",
      "message": "All requirements met for Route 1"
    }
  }
}
```

## Frontend Implementation Flow

### 1. Initial Load
1. Call `GET /applications/{id}/documents`
2. Display recommended route and requirements
3. Show available documents grouped by document groups
4. Display any existing nominations

### 2. Document Selection
1. User selects document type from available list
2. Show dynamic form based on `data_fields` from API
3. Collect document data (passport number, dates, etc.)
4. Mark if document confirms address

### 3. Real-time Validation
1. Call `POST /applications/{id}/validate` after each document addition/removal
2. Show validation status for each document
3. Display route completion progress
4. Show errors/warnings for individual documents

### 4. Route Management
1. Start with recommended route
2. If route fails validation, suggest fallback routes
3. Allow manual route switching if needed
4. Show different requirements for each route

## UI Components Needed

### Document Selection Card
```
[Document Group 1]
□ Passport (Any Country) - Photo required
□ Biometric Residence Permit (UK) - Photo required
□ Photocard Drivers Licence (UK) - Photo required

[Document Group 2a]
□ Paper-style Driving Licence (UK)
□ Birth Certificate (UK) - Issued 12+ months after birth
```

### Document Data Form
```
Document: Passport (Any Country)
┌─────────────────────────────────────┐
│ Passport Number: [____________]     │
│ Issue Country: [United Kingdom ▼]  │
│ Issue Date: [DD/MM/YYYY]           │
│ Expiry Date: [DD/MM/YYYY]          │
│ □ This document confirms my address │
└─────────────────────────────────────┘
[Add Document] [Cancel]
```

### Route Progress Indicator
```
Route 1 Progress: 2/3 documents ⚠️
✅ Group 1: Passport provided
❌ Address confirmation: Required
⚠️ Additional documents: Need 1 more from Groups 1/2a/2b
```

### Validation Messages
```
✅ Passport details validated successfully
⚠️ Bank statement is older than 3 months
❌ Driving licence has expired
```

## Key Frontend Logic

### Document Group Validation
- Track which groups have been used
- Prevent multiple documents from same group
- Show available groups for additional documents

### Address Confirmation
- Track if any nominated document confirms address
- Highlight address-confirming documents
- Show warning if address confirmation missing

### Route Switching
- Allow switching between available routes
- Clear invalid documents when switching routes
- Show different requirements for each route

### Form Validation
- Validate required fields before submission
- Show field-level errors
- Prevent submission if validation fails

## Error Handling

### Common Errors
- Missing required document data fields
- Invalid date formats or expired documents
- Insufficient documents for route completion
- Missing address confirmation
- Documents not applicable for applicant's nationality

### User Guidance
- Show clear error messages with suggestions
- Highlight missing requirements
- Suggest alternative routes if current route fails
- Provide help text for document data fields

## Data Storage
- Save nominations locally until validation passes
- Auto-save document data as user types
- Clear invalid data when switching routes
- Persist completed nominations to backend after validation

## Implementation Status ✅

### Backend Implementation Complete:
- ✅ Database tables created (`document_types`, `doc_daf_data`)
- ✅ Models created (`DocumentType`, `DocDafData`)
- ✅ Services implemented (`DocumentRequirementService`, `DocumentValidationService`)
- ✅ Controller created (`DocumentNominationController`)
- ✅ API routes configured
- ✅ Sample document types seeded (Group 1 documents)
- ✅ Application model updated with `current_route` field
- ✅ **Security implemented** - Hierarchical access control integrated
- ✅ **Access validation** - Users can only access applications they have permission for

### Ready for Frontend Integration:
The backend APIs are fully functional and ready for frontend implementation. Test the endpoints using the application IDs from your existing data.

### Next Steps:
1. Complete document seeder with all groups (1a, 2a, 2b, a, b1, b2)
2. Add product-specific document requirements
3. Implement document file upload functionality
4. Add admin interface for managing document types
