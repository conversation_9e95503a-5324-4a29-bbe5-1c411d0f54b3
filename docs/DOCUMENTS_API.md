# Documents API Documentation

## Overview

The Documents API provides endpoints for managing document nominations for DBS applications. The system uses a **config-based approach** for document types, eliminating database dependencies and improving performance.

## Base URL
```
http://localhost:8001/api/v1
```

## Authentication
All endpoints require authentication using Laravel Sanctum tokens.

```http
Authorization: Bearer {token}
```

## Endpoints

### 1. Get Available Documents

Retrieves the list of documents available for nomination for a specific application.

**Endpoint:** `GET /applications/{id}/documents`

**Parameters:**
- `id` (integer, required): Application ID

**User Types Allowed:**
- `applicant` - Can access their own applications
- `client_user` - Can access applications within their entity hierarchy
- `requester` - Can access applications within their entity hierarchy  
- `doc_checker` - Can access applications within their entity hierarchy

**Security Features:**
- Rate limiting: 60 requests per minute per user/IP
- Comprehensive access control via entity hierarchy
- Technical audit logging via Laravel logging
- Input validation and sanitization
- IP and user agent tracking

**Response Structure:**

```json
{
  "success": true,
  "message": "Document information retrieved successfully",
  "data": {
    "application": {
      "id": 123,
      "product_code": "DBSEC",
      "product_name": "DBS Enhanced Check",
      "applicant_name": "<PERSON>e",
      "status": "in_progress"
    },
    "applicant_context": {
      "nationality": "British",
      "current_address_country": "UK",
      "is_uk_national": true,
      "is_uk_resident": true,
      "work_type": "paid",
      "product_code": "DBSEC"
    },
    "routing": {
      "recommended_route": 1,
      "available_routes": [1, 2, 3],
      "route_requirements": {
        "route_1": {
          "description": "Primary route - requires 1 document from Group 1 and 2 additional documents",
          "total_documents_required": 3,
          "address_confirmation_required": true,
          "right_to_work_required": false,
          "required_groups": {
            "1": {"min": 1, "max": 1}
          },
          "additional_groups": ["1", "2a", "2b"]
        }
      }
    },
    "documents": {
      "available_by_group": {
        "1": {
          "group_name": "Primary Identity Documents",
          "document_count": 5,
          "documents": [
            {
              "key": "passport_any",
              "name": "Passport (Any Country)",
              "requires_photo": true,
              "confirms_address": false,
              "applicable_countries": ["ANY"],
              "data_fields": [
                {
                  "name": "passport_number",
                  "type": "string",
                  "required": true,
                  "label": "Passport Number"
                },
                {
                  "name": "issue_country",
                  "type": "string", 
                  "required": true,
                  "label": "Issue Country"
                }
              ]
            }
          ]
        }
      },
      "total_available": 45,
      "current_nominations": [
        {
          "id": 1,
          "document_type_key": "passport_any",
          "document_name": "Passport (Any Country)",
          "document_group": "1",
          "route_number": 1,
          "confirms_address": false,
          "status": "nominated",
          "document_data": {
            "passport_number": "*********",
            "issue_country": "UK"
          },
          "created_at": "2025-06-28T10:30:00.000Z",
          "updated_at": "2025-06-28T10:30:00.000Z"
        }
      ],
      "nomination_summary": {
        "total_nominations": 1,
        "by_status": {
          "nominated": 1
        },
        "by_group": {
          "1": 1
        },
        "by_route": {
          "1": 1
        }
      }
    },
    "metadata": {
      "retrieved_at": "2025-06-28T10:30:00.000Z",
      "user_type": "applicant",
      "config_version": "2.0"
    }
  }
}
```

### 2. Validate Documents

Validates document nominations for a specific route.

**Endpoint:** `POST /applications/{id}/validate`

**Parameters:**
- `id` (integer, required): Application ID

**Request Body:**
```json
{
  "route_number": 1,
  "nominated_documents": [
    {
      "document_type_key": "passport_any",
      "document_data": {
        "passport_number": "*********",
        "issue_country": "UK",
        "issue_date": "2020-01-01",
        "expiry_date": "2030-01-01"
      },
      "confirms_address": false
    }
  ]
}
```

**Security Features:**
- Rate limiting: 30 requests per minute per user/IP
- Same access control as GET endpoint
- Comprehensive validation of document data
- Technical logging for security monitoring

## Document Groups

The system organizes documents into the following groups based on **new government guidelines**:

### **Group 1: Primary Identity Documents**
- **Passport** - Any current and valid passport (UK passport can be expired up to 6 months)
- **e-Visa** - Accessed via 'View and Prove' service with general share code
- **Biometric Residence Permit** - UK (can be used up to 18 months past expiry for certain types)
- **Application Registration Card (ARC)** - Issued by Home Office
- **Current driving licence photocard** - UK, Isle of Man, Channel Islands (full or provisional)
- **Birth certificate** - UK, Isle of Man, Channel Islands (issued within 12 months of birth)
- **Adoption certificate** - UK and Channel Islands

### **Group 1a: Right to Work Documents**
- Various immigration and work authorization documents

### **Group 2a: Trusted Government Documents**
- **Current driving licence photocard** - Non-UK countries (full or provisional)
- **Paper driving licence** - UK, Isle of Man, Channel Islands (if issued before March 2000)
- **Birth certificate** - UK, Isle of Man, Channel Islands (issued after time of birth)
- **Marriage/civil partnership certificate** - UK and Channel Islands
- **Immigration document, visa, or work permit** - Non-UK countries
- **HM Forces ID card or HM Armed Forces Veteran card** - UK
- **Firearms licence** - UK, Isle of Man, Channel Islands

### **Group 2b: Financial and Social History Documents**
- **Mortgage statement** - UK (last 12 months)
- **Bank/building society statement** - UK/Channel Islands (last 3 months), Non-UK (last 3 months)
- **Bank account opening confirmation letter** - UK (last 3 months)
- **Credit card statement** - UK (last 3 months)
- **Financial statement** - UK (last 12 months)
- **P45 or P60 statement** - UK/Channel Islands (last 12 months)
- **Council Tax statement** - UK/Channel Islands (last 12 months)
- **Utility bill** - UK (last 3 months, not mobile, cannot be printed from online)
- **Benefit statement** - UK (last 3 months)
- **Government correspondence** - UK/Channel Islands (last 3 months)
- **HMRC self-assessment/tax demand** - UK (last 12 months)
- **EHIC/GHIC** - UK (must be valid)
- **EEA National ID card** - Must be valid
- **Irish Passport Card** - Cannot be used with Irish passport
- **PASS accreditation cards** - UK/Isle of Man/Channel Islands (including digital)
- **Letter from head teacher/college principal** - UK, 16-19 year olds only (last month)

## Document Routes

### **Route 1 (Primary Route)**
- **Requirements**: 1 document from Group 1 + 2 additional documents
- **Total documents**: 3
- **Address confirmation**: Required
- **Best for**: Applicants with primary identity documents

### **Route 2 (Alternative Route) - NEW REQUIREMENTS**
- **Requirements**: 1 document from Group 2a + 2 further documents from Groups 2a or 2b
- **Total documents**: 3
- **Address confirmation**: Required
- **Important**: The combination must confirm applicant's name and date of birth
- **Best for**: Applicants without Group 1 documents

### **Route 3 (Birth Certificate Route)**
- **Requirements**: UK birth certificate + 2 additional documents
- **Total documents**: 3
- **Address confirmation**: Required
- **Best for**: UK nationals with birth certificates

## Error Responses

### 401 Unauthorized
```json
{
  "success": false,
  "message": "Unauthenticated"
}
```

### 403 Forbidden
```json
{
  "success": false,
  "message": "Access denied for this user type"
}
```

### 404 Not Found
```json
{
  "success": false,
  "message": "Application not found"
}
```

### 429 Too Many Requests
```json
{
  "success": false,
  "message": "Too many requests. Please try again later."
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Failed to retrieve document information",
  "data": {
    "error": "Error details"
  }
}
```

## Security Protocols

### Authentication & Authorization
- Laravel Sanctum token-based authentication
- Role-based access control (RBAC)
- Entity hierarchy-based authorization
- User type validation

### Rate Limiting
- Documents endpoint: 60 requests/minute per user/IP
- Validation endpoint: 30 requests/minute per user/IP
- Automatic rate limit reset after time window

### Technical Logging
- Security-related events logged via Laravel logging
- Includes user ID, IP address, user agent, timestamps
- Failed access attempts logged with detailed context
- Rate limiting violations and unauthorized access attempts tracked

### Input Validation
- Application ID range validation (1 to 2147483647)
- User type whitelist validation
- Document data field validation against config schema
- Request payload size limits

### Data Protection
- No sensitive data in logs
- Secure token handling
- HTTPS enforcement (production)
- SQL injection prevention via Eloquent ORM

## Performance Features

### Config-Based Architecture
- Zero database queries for document type lookups
- In-memory document configuration
- Fast response times (<100ms typical)
- Horizontal scaling friendly

### Caching Strategy
- Document configurations cached in memory
- Rate limit data cached in Redis
- Application data cached where appropriate

### Response Optimization
- Structured JSON responses
- Minimal data transfer
- Efficient array operations
- Lazy loading of relationships

## Testing

Run the test suite to verify endpoint functionality:

```bash
php artisan test tests/Feature/Documents/DocumentNominationEndpointTest.php
```

The test suite covers:
- Authentication requirements
- Authorization checks
- Rate limiting
- Response structure validation
- Error handling
- Config-based document loading
- Nomination management
