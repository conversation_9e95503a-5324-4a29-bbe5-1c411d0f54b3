# Applications API with Hierarchical Access Control

This document describes the API endpoints for accessing applications with hierarchical access control.

## Overview

This API provides access to applications based on user roles and entity hierarchy:

- **Applicants**: Can view their own applications
- **Client Users**: Can view applications created under their entities and any child entities in the hierarchy
- **Users in Parent/Super Groups**: Can view applications from all associated child entities

### Access Control

**Current Implementation:**
- **Applicants**: Can view only their own applications
- **Client Users** (`client_user`, `requester`, `document_checker`): Can view all applications

**Note**: Full hierarchical access control based on entity relationships will be implemented in a future update when the applications table includes entity associations.

## Authentication Flows

### Unified Authentication (All User Types)

Both applicants and client users now use the same authentication endpoints:

#### 1. Login (All User Types)
```http
POST /api/v1/auth/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password"
}
```

**Response (Success - Applicant):**
```json
{
    "success": true,
    "data": {
        "user": {
            "id": 1,
            "email": "<EMAIL>",
            "user_type": "applicant",
            "first_name": "<PERSON>",
            "last_name": "Doe",
            "phone": null,
            "address": null,
            "two_factor_enabled": false
        },
        "token": "1|abc123...",
        "token_type": "Bearer"
    },
    "message": "Applicant logged in successfully"
}
```

**Response (Success - Client User):**
```json
{
    "success": true,
    "data": {
        "user": {
            "id": 2,
            "email": "<EMAIL>",
            "user_type": "client_user",
            "first_name": "Jane",
            "last_name": "Smith",
            "phone": "+44 ************",
            "address": "123 Business St",
            "two_factor_enabled": true
        },
        "entities": [
            {
                "id": 1,
                "name": "ABC Company Ltd",
                "code": "ABC001",
                "role": "admin",
                "email": "<EMAIL>",
                "phone": "+44 ************"
            }
        ],
        "token": "2|def456...",
        "token_type": "Bearer"
    },
    "message": "User logged in successfully"
}
```

**Response (2FA Required):**
```json
{
    "success": true,
    "data": {
        "requires_two_factor": true,
        "message": "Two-factor authentication required"
    },
    "message": "Two-factor authentication required"
}
```

#### 2. PIN Verification (if 2FA enabled)
```http
POST /api/v1/auth/verify-pin
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password",
    "pin": "123456"
}
```

**Note**: The response format for PIN verification follows the same pattern as login - applicants receive user data without entities, while client users receive user data with entities.

## Application Endpoints

### Applicant Endpoints

#### 1. Get Applicant Details (Hierarchical Access)
```http
GET /api/v1/applicants/{applicantId}
Authorization: Bearer {token}
```

**Access Control:**
- **Applicants**: Can only access their own details
- **Client Users**: Can access applicants from their entity hierarchy

**Response:**
```json
{
    "success": true,
    "data": {
        "applicant": {
            "id": 1,
            "email": "<EMAIL>",
            "user_type": "applicant",
            "profile": {
                "first_name": "John",
                "last_name": "Smith",
                "full_name": "John Smith",
                "telephone": "+44123456789",
                "address": "123 Main St, London",
                "date_of_birth": "1990-01-01"
            },
            "entities": [
                {
                    "id": 1,
                    "name": "Example Company",
                    "entity_code": "EXC001",
                    "role": "applicant"
                }
            ]
        },
        "applications": [
            {
                "id": 1,
                "external_reference": "APP-000001",
                "status": "in_progress",
                "result": null,
                "consent_date": "2024-01-15",
                "created_at": "2024-01-15 10:30:00",
                "product": {
                    "id": 1,
                    "name": "DBS - Basic check",
                    "code": "DBS001",
                    "variant": "DBS"
                },
                "completion_status": {
                    "is_started": true,
                    "is_complete": false,
                    "status_text": "In progress"
                },
                "submitted_by": {
                    "id": 1,
                    "email": "<EMAIL>",
                    "name": "John Smith"
                }
            }
        ],
        "total_applications": 1
    },
    "message": "Applicant details retrieved successfully"
}
```

#### 3. Logout
```http
POST /api/v1/auth/logout
Authorization: Bearer {token}
```

### Application Form Data Endpoint

#### Get Application Form Data
```http
GET /api/v1/applications/{applicationId}/form-data
Authorization: Bearer {token}
```

**Purpose**: Retrieve form data for a specific application with completion status information.

**Access Control:**
- **Applicants**: Can only access their own applications
- **Client Users**: Can access applications from their entity hierarchy

**Response:**
```json
{
    "success": true,
    "data": {
        "application_id": 1,
        "external_reference": "APP-000001",
        "status": "in_progress",
        "form_data": {
            "ApplicantDetails.Title": "Mr",
            "ApplicantDetails.FirstName": "John",
            "ApplicantDetails.LastName": "Smith"
        },
        "form_fields": [
            {
                "field_name": "ApplicantDetails.Title",
                "field_label": "Title",
                "field_type": "select",
                "field_options": ["Mr", "Mrs", "Ms", "Dr"],
                "is_required": true,
                "sort_order": 1
            }
        ],
        "completion_status": {
            "is_started": true,
            "is_complete": false,
            "status_text": "In progress"
        },
        "product": {
            "id": 1,
            "name": "DBS - Basic check",
            "code": "DBS001",
            "variant": "DBS"
        }
    },
    "message": "Application form data retrieved successfully"
}
```

**Completion Status Values:**
- `"Not started"`: No form data has been saved
- `"In progress"`: Some form data exists but required fields are missing
- `"Complete"`: All required fields have been filled

## Security Features

### Access Control
- **Hierarchical Access**: Application endpoints support multiple user types with appropriate access control
- **Role-Based Access**:
  - `applicant`: Can only see their own applications
  - `client_user`, `requester`, `document_checker`: Can see applications from their associated entities and child entities
- **Entity Hierarchy**: Follows Super Group → Parent Group → Client structure for access control
- **Data Isolation**: Users can only access applications within their authorized entity hierarchy
- **Token-Based Authentication**: Uses Laravel Sanctum for secure API access

### Error Responses

**Unauthorized Access (Invalid User Type):**
```json
{
    "success": false,
    "message": "Access denied for this user type"
}
```

**Application Not Found:**
```json
{
    "success": false,
    "message": "Application not found"
}
```

**Invalid Credentials:**
```json
{
    "success": false,
    "message": "Invalid credentials"
}
```

## Application Status Values

- `pending` - Application submitted, awaiting processing
- `in_progress` - Application is being processed
- `completed` - Application has been completed
- `rejected` - Application was rejected
- `cancelled` - Application was cancelled

## Data Structure

### Application Fields
- `id` - Unique application identifier
- `application_reference` - Human-readable reference number
- `status` - Current application status
- `submitted_at` - When the application was submitted
- `completed_at` - When the application was completed (if applicable)
- `created_at` - When the application record was created

### Entity Information
- `id`, `name`, `code` - Entity identification
- `contact_email`, `phone`, `address` - Entity contact details

### Product Information
- `id`, `name`, `code`, `description` - Product details
- `form_fields` - Available form fields for the product (detailed view only)

### Form Data
- Key-value pairs of submitted form data
- Only available in detailed application view

## Usage Examples

### Complete Flow Example
```bash
# 1. Login (works for both applicants and client users)
curl -X POST http://localhost:8001/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password"
  }'

# 2. Get applicant details
curl -X GET http://localhost:8001/api/v1/applicants/1 \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"

# 3. Get application form data with completion status
curl -X GET http://localhost:8001/api/v1/applications/1/form-data \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"

# 4. Logout
curl -X POST http://localhost:8001/api/v1/auth/logout \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## Implementation Notes

- Applications are created by client users for applicants
- Applicants can only view, not modify applications
- All application data includes related entity and product information
- Form data is stored separately and aggregated for display
- Two-factor authentication is supported for applicants if enabled
