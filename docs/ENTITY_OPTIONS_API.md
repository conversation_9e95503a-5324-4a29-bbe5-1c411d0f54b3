# Entity Options API Documentation

## Overview

The Entity Options API provides endpoints for retrieving entity-specific configuration options with support for hierarchical inheritance. The system **always respects the entity hierarchy** when retrieving values, ensuring consistent behavior across all endpoints.

**Entity Hierarchy**: **Super Group** → **Parent Group** → **Client**

**Key Features**:
- ✅ **Always respects hierarchy** - No need for special flags
- ✅ **Clean RESTful endpoints** - Simple and intuitive API design
- ✅ **Automatic type conversion** - Boolean, integer, float, JSON support
- ✅ **Source tracking** - Optional detailed inheritance information
- ✅ **Access control** - Users can only access authorized entities

## Base URL
```
http://localhost:8001/api/v1
```

## Authentication
All endpoints require authentication using Laravel Sanctum tokens.

```http
Authorization: Bearer {token}
```

## Entity Hierarchy and Inheritance

### Hierarchy Structure
- **Super Group**: Top-level entity that can contain multiple parent groups
- **Parent Group**: Mid-level entity that can contain multiple clients
- **Client**: Bottom-level entity that represents individual clients

### Option Inheritance Logic
1. **Direct Value**: If an entity has a direct option value with `inherit_from_parent = false`, that value is used
2. **Inherited Value**: If an entity has `inherit_from_parent = true`, the system traverses up the hierarchy to find the value
3. **Fallback**: If no inherited value is found, the entity's own value is used (even if marked for inheritance)
4. **Not Found**: If no option exists at any level, `null` is returned

## Endpoints

### 1. Get Single Option Value

Retrieves a single option value for a specific entity. The system always respects the entity hierarchy when retrieving values.

**Endpoint:** `GET /entities/{entityId}/option/{optionName}`

**Parameters:**
- `entityId` (integer, required): Entity ID
- `optionName` (string, required): Name of the option to retrieve
- `include_source` (boolean, optional): Include source information in response

**Example Request (simple value):**
```http
GET /api/v1/entities/123/option/allow_applicant_registration
Authorization: Bearer {token}
```

**Example Response (simple value):**
```json
{
    "success": true,
    "data": true,
    "message": "Entity option retrieved successfully"
}
```

**Example Request (with source information):**
```http
GET /api/v1/entities/123/option/allow_applicant_registration?include_source=true
Authorization: Bearer {token}
```

**Example Response (with source information):**
```json
{
    "success": true,
    "data": {
        "entity": {
            "id": 123,
            "name": "Test Client",
            "entity_code": "TC001",
            "entity_type": "client"
        },
        "option_name": "allow_applicant_registration",
        "option_value": true,
        "source": "entity",
        "source_entity": {
            "id": 122,
            "name": "Test Parent Group"
        },
        "hierarchy_path": [
            {
                "id": 123,
                "name": "Test Client",
                "type": "client"
            },
            {
                "id": 122,
                "name": "Test Parent Group",
                "type": "parent_group"
            },
            {
                "id": 121,
                "name": "Test Super Group",
                "type": "super_group"
            }
        ]
    },
    "message": "Entity option retrieved successfully"
}
```

### 2. Get Multiple Options

Retrieves multiple option values for a specific entity in a single request. The system always respects the entity hierarchy when retrieving values.

**Endpoint:** `POST /entities/{entityId}/options`

**Parameters:**
- `entityId` (integer, required): Entity ID
- Request body: Array of option names to retrieve
- `include_source` (boolean, optional): Include source information in response (query parameter)

**Example Request:**
```http
POST /api/v1/entities/123/options
Authorization: Bearer {token}
Content-Type: application/json

[
    "allow_applicant_registration",
    "max_applications_per_day",
    "require_document_verification"
]
```

**Example Response:**
```json
{
    "success": true,
    "data": {
        "entity": {
            "id": 123,
            "name": "Test Client",
            "entity_code": "TC001",
            "entity_type": "client"
        },
        "options": {
            "allow_applicant_registration": true,
            "max_applications_per_day": 100,
            "require_document_verification": false
        }
    },
    "message": "Entity options retrieved successfully"
}
```

### 3. Get User Accessible Entities

Retrieves all entities that the authenticated user has access to, including child entities through hierarchy.

**Endpoint:** `GET /entities/accessible`

**Example Request:**
```http
GET /api/v1/entities/accessible
Authorization: Bearer {token}
```

**Example Response:**
```json
{
    "success": true,
    "data": {
        "entities": [
            {
                "id": 121,
                "name": "Test Super Group",
                "entity_code": "TSG001",
                "entity_type": "super_group",
                "contact_email": "<EMAIL>",
                "phone": "+1234567890"
            },
            {
                "id": 122,
                "name": "Test Parent Group",
                "entity_code": "TPG001",
                "entity_type": "parent_group",
                "contact_email": "<EMAIL>",
                "phone": "+1234567891"
            },
            {
                "id": 123,
                "name": "Test Client",
                "entity_code": "TC001",
                "entity_type": "client",
                "contact_email": "<EMAIL>",
                "phone": "+1234567892"
            }
        ],
        "total_count": 3
    },
    "message": "Accessible entities retrieved successfully"
}
```

## Data Types and Formatting

The system automatically formats option values based on their content:

- **Boolean**: `"true"`, `"1"` → `true`; `"false"`, `"0"` → `false`
- **Integer**: `"123"` → `123`
- **Float**: `"123.45"` → `123.45`
- **JSON**: Valid JSON strings are parsed to arrays/objects
- **String**: All other values remain as strings

## Source Types

When `include_source=true`, the `source` field can have these values:

- `"entity"`: Value comes from a specific entity in the hierarchy
- `"entity_default"`: Value comes from the requested entity (fallback)
- `"not_found"`: Option doesn't exist at any level
- `"entity_not_found"`: The requested entity doesn't exist
- `"error"`: An error occurred during retrieval

## Access Control

- Users can only access options for entities they have permissions for
- Access follows the entity hierarchy - users with access to parent entities can access child entities
- The system uses existing `entity_user_links` table for access control
- SolidFuse EntityHelper is used for consistent hierarchy traversal

## Error Responses

### Validation Error (422)
```json
{
    "success": false,
    "message": "Validation Error",
    "data": {
        "option_name": ["The option name field is required."]
    }
}
```

### Access Denied (403)
```json
{
    "success": false,
    "message": "Access denied to this entity"
}
```

### Entity Not Found (404)
```json
{
    "success": false,
    "message": "Entity not found"
}
```

### Server Error (500)
```json
{
    "success": false,
    "message": "Failed to retrieve entity option: [error details]"
}
```

## Usage Examples

### Check if Applicant Registration is Allowed
```javascript
// Get option value for a specific client (returns just the value)
const response = await fetch('/api/v1/entities/123/option/allow_applicant_registration', {
    headers: {
        'Authorization': 'Bearer ' + token,
        'Accept': 'application/json'
    }
});

const data = await response.json();
const isAllowed = data.data; // true/false (direct value)
```

### Get Multiple Configuration Options
```javascript
const response = await fetch('/api/v1/entities/123/options', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify([
        'allow_applicant_registration',
        'max_applications_per_day',
        'require_document_verification'
    ])
});

const data = await response.json();
const options = data.data.options;
```

### Find Source of Configuration
```javascript
// Get option with source information to understand inheritance
const response = await fetch('/api/v1/entities/123/option/allow_applicant_registration?include_source=true', {
    headers: {
        'Authorization': 'Bearer ' + token,
        'Accept': 'application/json'
    }
});

const data = await response.json();
console.log(`Value: ${data.data.option_value}`);
console.log(`Source: ${data.data.source_entity.name}`);
console.log(`Hierarchy:`, data.data.hierarchy_path);
```

## Integration with SolidFuse

This API is designed to work seamlessly with the SolidFuse package:

- Uses SolidFuse EntityHelper for hierarchy traversal
- Compatible with existing entity relationship structures
- Follows SolidFuse patterns for option inheritance
- Maintains consistency with other SolidFuse-based features

## Testing

Comprehensive test coverage is provided:

- **Feature Tests**: End-to-end API testing with authentication
- **Unit Tests**: Service layer testing with hierarchy scenarios
- **Access Control Tests**: Permission and security validation
- **Edge Cases**: Error handling and boundary conditions

Run tests with:
```bash
php artisan test --filter EntityOption
```
