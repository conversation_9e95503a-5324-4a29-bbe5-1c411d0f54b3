"""
SolidTech Document Verification System
Main application entry point with comprehensive security and ML integration
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator

import structlog
import uvicorn
from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from prometheus_client import make_asgi_app

from app.api.routes import api_router
from app.core.config import get_settings
from app.core.exceptions import SolidTechException
from app.core.logging import setup_logging
from app.core.security import SecurityMiddleware
from app.database.connection import database_manager
from app.ml.model_manager import model_manager
from app.ml.training_scheduler import training_scheduler


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan management with proper startup/shutdown"""
    settings = get_settings()
    logger = structlog.get_logger()
    
    try:
        # Startup sequence
        logger.info("🚀 Starting SolidTech Document Verification System")
        
        # Initialize database connections
        logger.info("📊 Initializing database connections...")
        await database_manager.connect()
        
        # Load ML models
        logger.info("🤖 Loading ML models...")
        await model_manager.load_models()
        
        # Verify security configuration
        logger.info("🔒 Verifying security configuration...")
        from app.core.security import verify_security_config
        await verify_security_config()

        # Start training scheduler
        logger.info("📅 Starting training scheduler...")
        await training_scheduler.start()

        logger.info("✅ SolidTech system startup complete")
        
        yield
        
    except Exception as e:
        logger.error("❌ Failed to start SolidTech system", error=str(e))
        raise
    finally:
        # Shutdown sequence
        logger.info("🛑 Shutting down SolidTech system...")

        # Stop training scheduler
        await training_scheduler.stop()

        # Cleanup ML models
        await model_manager.cleanup()

        # Close database connections
        await database_manager.disconnect()

        logger.info("✅ SolidTech system shutdown complete")


def create_application() -> FastAPI:
    """Create and configure the FastAPI application"""
    settings = get_settings()
    
    app = FastAPI(
        title="SolidTech Document Verification API",
        description="Secure ML-powered document authentication and fraud detection system",
        version=settings.APP_VERSION,
        docs_url="/docs" if settings.DEBUG else None,
        redoc_url="/redoc" if settings.DEBUG else None,
        openapi_url="/openapi.json" if settings.DEBUG else None,
        lifespan=lifespan,
    )
    
    # Security middleware (must be first)
    app.add_middleware(SecurityMiddleware)
    
    # Trusted host middleware
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=settings.ALLOWED_HOSTS
    )
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE"],
        allow_headers=["*"],
    )
    
    # Include API routes
    app.include_router(api_router, prefix="/api/v1")
    
    # Prometheus metrics endpoint
    metrics_app = make_asgi_app()
    app.mount("/metrics", metrics_app)
    
    # Global exception handler
    @app.exception_handler(SolidTechException)
    async def solidtech_exception_handler(request: Request, exc: SolidTechException):
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": exc.error_code,
                "message": exc.message,
                "details": exc.details,
                "timestamp": exc.timestamp.isoformat(),
            }
        )
    
    # Health check endpoint
    @app.get("/health")
    async def health_check():
        """System health check endpoint"""
        return {
            "status": "healthy",
            "service": "SolidTech Document Verification",
            "version": settings.APP_VERSION,
            "timestamp": asyncio.get_event_loop().time()
        }
    
    # Security headers middleware
    @app.middleware("http")
    async def add_security_headers(request: Request, call_next):
        response = await call_next(request)
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        response.headers["Content-Security-Policy"] = "default-src 'self'"
        return response
    
    return app


# Create the application instance
app = create_application()


if __name__ == "__main__":
    # Setup logging
    setup_logging()
    
    # Get settings
    settings = get_settings()
    
    # Configure uvicorn
    uvicorn_config = {
        "app": "main:app",
        "host": settings.HOST,
        "port": settings.PORT,
        "reload": settings.RELOAD and settings.DEBUG,
        "workers": 1 if settings.DEBUG else settings.WORKERS,
        "log_config": None,  # Use our custom logging
        "access_log": False,  # Disable default access log
    }
    
    # Add SSL configuration for production
    if not settings.DEBUG and not settings.ALLOW_HTTP:
        uvicorn_config.update({
            "ssl_keyfile": settings.SSL_KEY_FILE,
            "ssl_certfile": settings.SSL_CERT_FILE,
        })
    
    # Start the server
    uvicorn.run(**uvicorn_config)
