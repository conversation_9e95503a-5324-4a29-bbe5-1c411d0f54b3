<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Test route
Route::get('/test', function () {
    return response()->json(['message' => 'API is working']);
});

// Test Upload Routes (for debugging) - No auth required
Route::post('/test/upload', [App\Http\Controllers\TestUploadController::class, 'testUpload']);
Route::get('/test/files', [App\Http\Controllers\TestUploadController::class, 'checkFiles']);

// Debug route for document services
Route::get('/debug/documents/{id}', function ($id) {
    try {
        $mappingService = app(\App\Modules\Documents\Services\DocumentProductMappingService::class);
        $requirementService = app(\App\Modules\Documents\Services\DocumentRequirementService::class);
        $validationService = app(\App\Modules\Documents\Services\DocumentValidationService::class);

        // Test basic functionality
        $allDocuments = $mappingService->getAllDocumentTypes();

        return response()->json([
            'status' => 'success',
            'message' => 'Document services working',
            'application_id' => $id,
            'services_loaded' => [
                'DocumentProductMappingService' => true,
                'DocumentRequirementService' => true,
                'DocumentValidationService' => true
            ],
            'sample_documents_count' => count($allDocuments),
            'sample_documents' => array_slice($allDocuments, 0, 3) // First 3 documents
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ], 500);
    }
});



// Health check route for API
Route::get('/v1/health', function () {
    return response()->json([
        'status' => 'healthy',
        'message' => 'API is running',
        'timestamp' => now()->toISOString(),
        'version' => '1.0.0'
    ]);
});

// Test QR code generation
Route::get('/test-qr', function () {
    $twoFactorService = new \App\Modules\Auth\Services\TwoFactorService();

    // Create a mock user for testing
    $mockUser = new \App\Modules\Auth\Models\PortalUser();
    $mockUser->email = '<EMAIL>';

    $secret = $twoFactorService->generateSecret();
    $qrCodeImage = $twoFactorService->generateQrCodeImage($mockUser, $secret);

    return response()->json([
        'requires_two_factor_setup' => true,
        'qr_code_image' => $qrCodeImage,
        'temp_secret' => $secret,
        'message' => 'Please set up two-factor authentication by scanning the QR code'
    ]);
});

// Public routes
Route::prefix('v1')->group(function () {
    // Authentication routes (supports both users and applicants)
    Route::post('/auth/login', [App\Modules\Auth\Controllers\AuthController::class, 'login']);
    Route::post('/auth/verify-pin', [App\Modules\Auth\Controllers\AuthController::class, 'verifyPin']);

    // Debug routes (no auth required)
    Route::post('/debug/submit/{id}', function ($id) {
        try {
            return response()->json([
                'status' => 'success',
                'message' => 'Submit endpoint working',
                'application_id' => $id,
                'data' => request()->all()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ], 500);
        }
    });
});

// Protected routes
Route::prefix('v1')->middleware('auth:sanctum')->group(function () {
    // Authentication
    Route::post('/auth/logout', [App\Modules\Auth\Controllers\AuthController::class, 'logout']);
    Route::post('/auth/change-password', [App\Modules\Auth\Controllers\AuthController::class, 'changePassword'])
        ->middleware('throttle.password:5,15');

    // Job Roles (Client/Entity users only)
    Route::get('/job-roles', [App\Modules\Entities\SubModules\JobRoles\Controllers\JobRoleController::class, 'getJobRoles']);
    Route::post('/job-roles', [App\Modules\Entities\SubModules\JobRoles\Controllers\JobRoleController::class, 'store']);
    Route::put('/job-roles/{jobRole}', [App\Modules\Entities\SubModules\JobRoles\Controllers\JobRoleController::class, 'update']);
    Route::delete('/job-roles/{jobRole}', [App\Modules\Entities\SubModules\JobRoles\Controllers\JobRoleController::class, 'destroy']);

    // Entity-specific Job Roles (with inheritance and pricing)
    Route::get('/entities/{entityId}/job-roles', [App\Modules\Entities\Controllers\JobRoleController::class, 'getJobRoles']);
    Route::get('/entities/{entityId}/job-roles/own', [App\Modules\Entities\Controllers\JobRoleController::class, 'getEntityJobRoles']);

    // Applications (Applicants and Client users with hierarchy access)
    Route::get('/applications/{id}/form-data', [App\Modules\Applications\Controllers\ApplicationController::class, 'getApplicationFormData']);
    Route::post('/applications/save-data', [App\Modules\Applications\Controllers\ApplicationController::class, 'saveApplicationData']);

    // Applicant details
    Route::get('/applicants/{applicantId}', [App\Modules\Applications\Controllers\ApplicationController::class, 'getApplicantDetails']);

    // Billing and Payments
    Route::get('/billing/payment-status/{applicantId}', [App\Modules\Billing\Controllers\BillingController::class, 'getPaymentStatus']);

    // Applicant Management (Client users only)
    Route::get('/client-applicants', [App\Modules\Applications\Controllers\ApplicationController::class, 'getClientApplicants']);
    Route::post('/addapplicant', [App\Modules\Applications\Controllers\ApplicationController::class, 'createApplicant']);

    // Document Nomination Routes
    Route::get('/applications/{id}/documents', [App\Modules\Documents\Controllers\DocumentNominationController::class, 'getDocuments']);
    Route::post('/applications/{id}/validate', [App\Modules\Documents\Controllers\DocumentNominationController::class, 'validateDocuments']);
    Route::post('/applications/{applicationId}/nominations/{nominationId}/upload', [App\Modules\Documents\Controllers\DocumentNominationController::class, 'uploadFile']);
    Route::post('/applications/{id}/documents/nominate', [App\Modules\Documents\Controllers\DocumentNominationController::class, 'submitDocuments']);
    Route::post('/applications/{id}/submit', [App\Modules\Documents\Controllers\DocumentNominationController::class, 'validateDocuments']); // Same as validate for now

    // Document Upload Routes
    Route::post('/applications/{applicationId}/nominations/{nominationId}/upload', [App\Modules\Documents\Controllers\DocumentUploadController::class, 'uploadDocumentFile']);
    Route::get('/applications/{applicationId}/nominations/{nominationId}/files', [App\Modules\Documents\Controllers\DocumentUploadController::class, 'getDocumentFiles']);
    Route::delete('/applications/{applicationId}/nominations/{nominationId}/files/{fileId}', [App\Modules\Documents\Controllers\DocumentUploadController::class, 'deleteDocumentFile']);

    // Test Upload Routes (for debugging)
    Route::post('/test/upload', [App\Http\Controllers\TestUploadController::class, 'testUpload']);
    Route::get('/test/files', [App\Http\Controllers\TestUploadController::class, 'checkFiles']);

    // Document Completion Routes
    Route::post('/applications/{id}/documents/complete', [App\Modules\Documents\Controllers\DocumentCompletionController::class, 'completeDocumentNomination']);
    Route::get('/applications/{id}/documents/status', [App\Modules\Documents\Controllers\DocumentCompletionController::class, 'getDocumentStatus']);

    // Entity Options Routes
    Route::get('/entities/accessible', [App\Modules\Entities\Controllers\EntityOptionController::class, 'getUserAccessibleEntities']);
    Route::get('/entities/{entityId}/option/{optionName}', [App\Modules\Entities\Controllers\EntityOptionController::class, 'getOption']);
    Route::post('/entities/{entityId}/options', [App\Modules\Entities\Controllers\EntityOptionController::class, 'getMultipleOptions']);

    // DBS STRD Enhanced Routes (for testing/reference only)
    Route::prefix('dbs')->group(function () {
        Route::get('/allowed-fields', [App\Modules\Applications\Background\DBS\Controllers\DBSController::class, 'getAllowedFields']);
    });
});
