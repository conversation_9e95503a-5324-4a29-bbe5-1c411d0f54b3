<?php

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('api-tester');
});

Route::get('/test-stamps', function () {
    return view('debug.criminal-record', ['applicantId' => 7]);
});

// API Testing interface (dev only)
Route::get('/api-test', function () {
    return view('api-tester');
})->name('api.test');

// Debug route for testing process stamps
Route::get('/debug/applicant/{applicantId}/criminal-record', function ($applicantId) {
    return view('debug.criminal-record', ['applicantId' => $applicantId]);
})->name('debug.criminal-record');
