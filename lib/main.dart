import 'package:SolidCheck/core/navigation/app_router_new.dart' as new_router;
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/auth/di/auth_providers.dart';
import 'package:SolidCheck/features/dbs/data/services/dbs_api_service.dart';
import 'package:SolidCheck/features/dbs/data/services/postcode_lookup_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  DBSApiService.initialize();
  PostcodeLookupService.initialize();

  try {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('token');
    final is2FACompleted = prefs.getBool('is2FACompleted') ?? false;
    final isQrCodeScan = prefs.getBool('isQrCodeScan') ?? false;

    runApp(
      ProviderScope(
        overrides: [sharedPreferencesProvider.overrideWithValue(prefs)],
        child: MyApp(
          initialToken: token,
          is2FACompleted: is2FACompleted,
          isQrCodeScan: isQrCodeScan,
        ),
      ),
    );
  } catch (e) {
    final fallbackPrefs = await SharedPreferences.getInstance();
    runApp(
      ProviderScope(
        overrides: [sharedPreferencesProvider.overrideWithValue(fallbackPrefs)],
        child: const MyApp(
          initialToken: null,
          is2FACompleted: false,
          isQrCodeScan: false,
        ),
      ),
    );
  }
}

class MyApp extends ConsumerWidget {
  const MyApp({
    super.key,
    this.initialToken,
    this.is2FACompleted,
    this.isQrCodeScan,
  });

  final String? initialToken;
  final bool? is2FACompleted;
  final bool? isQrCodeScan;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return new_router.AppNavigationWrapper(
      child: MaterialApp(
        title: 'SolidCheck',
        debugShowCheckedModeBanner: false,
        navigatorKey: new_router.AppRouter.navigatorKey,
        onGenerateRoute: new_router.AppRouter.generateRoute,
        initialRoute: new_router.AppRouter.login,
        scrollBehavior: const MaterialScrollBehavior().copyWith(
          scrollbars: true,
        ),
        theme: ThemeData(
          useMaterial3: true,
          fontFamily: 'Roboto',
          iconTheme: const IconThemeData(
            size: 24.0,
            color: Colors.black87,
          ),
          primaryIconTheme: const IconThemeData(
            size: 24.0,
            color: Colors.white,
          ),
          colorScheme: ColorScheme.fromSeed(
            seedColor: AppColors.kBlueColor,
            brightness: Brightness.light,
          ),
          scaffoldBackgroundColor: AppColors.kWhiteColor,
          appBarTheme: AppBarTheme(
            backgroundColor: AppColors.kAppBarWhiteColorAndFieldActiveStateColor,
          ),
          cardColor: AppColors.kWhiteColor,
          cardTheme: CardThemeData(
            color: AppColors.kWhiteColor,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(5.0)),
            ),
            shadowColor: AppColors.kShadowColor,
            elevation: 5.0,
          ),
          checkboxTheme: CheckboxThemeData(
            side: BorderSide(color: AppColors.kBlueDarkColor),
            checkColor: WidgetStateProperty.all(AppColors.kBlueDarkColor),
          ),
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              backgroundColor: Colors.transparent,
              shadowColor: Colors.transparent,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.0),
              ),
              textStyle: const TextStyle(
                fontSize: 16.0,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          outlinedButtonTheme: OutlinedButtonThemeData(
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              side: BorderSide(
                color: AppColors.kBlueColor,
                width: 1.5,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.0),
              ),
              textStyle: TextStyle(
                fontSize: 16.0,
                fontWeight: FontWeight.bold,
                color: AppColors.kBlueColor,
              ),
            ),
          ),
          textButtonTheme: TextButtonThemeData(
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.0),
              ),
              textStyle: TextStyle(
                fontSize: 16.0,
                fontWeight: FontWeight.bold,
                color: AppColors.kBlueColor,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
