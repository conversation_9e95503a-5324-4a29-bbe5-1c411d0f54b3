import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'features/document_nomination/presentation/providers/document_nomination_provider.dart';
import 'services/entity_options_service.dart';

class TestEntityOptionsScreen extends ConsumerStatefulWidget {
  const TestEntityOptionsScreen({super.key});

  @override
  ConsumerState<TestEntityOptionsScreen> createState() => _TestEntityOptionsScreenState();
}

class _TestEntityOptionsScreenState extends ConsumerState<TestEntityOptionsScreen> {
  Map<String, bool>? _options;
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadOptions();
  }

  Future<void> _loadOptions() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Commented out for testing - this was causing unauthenticated API calls
      // final entityOptionsService = EntityOptionsService();
      // final options = await entityOptionsService.getDocumentNominationOptions(123);

      setState(() {
        _options = {
          'allow_aidoc_scanner': false,
          'require_document_verification': true,
          'allow_manual_document_entry': true,
          'enable_document_upload': true,
        };
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final documentNominationState = ref.watch(documentNominationProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Entity Options Test'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Document Nomination Provider State',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildStatusRow(
                      'AI Document Scanner Enabled',
                      documentNominationState.isAiDocumentScannerEnabled,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Entity Options: ${documentNominationState.entityOptions}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Direct Entity Options Service',
                          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        ElevatedButton(
                          onPressed: _loadOptions,
                          child: const Text('Refresh'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    if (_isLoading)
                      const Center(child: CircularProgressIndicator())
                    else if (_error != null)
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.red[50],
                          border: Border.all(color: Colors.red[300]!),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'Error: $_error',
                          style: TextStyle(color: Colors.red[700]),
                        ),
                      )
                    else if (_options != null)
                      Column(
                        children: _options!.entries.map((entry) {
                          return _buildStatusRow(entry.key, entry.value);
                        }).toList(),
                      ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Test Instructions',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      '1. The AI Document Scanner should be ENABLED (using fallback values for testing)\n'
                      '2. Navigate to Document Nomination screen\n'
                      '3. Select a document to process\n'
                      '4. You should see the AI-Powered Extraction option\n'
                      '5. Change the hardcoded value in EntityOptionsService to test disable functionality',
                      style: TextStyle(fontSize: 14),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pushNamed('/document-nomination', arguments: {
                          'applicationId': '123',
                          'applicantName': 'Test Applicant',
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green[600],
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Go to Document Nomination'),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, bool value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Icon(
            value ? Icons.check_circle : Icons.cancel,
            color: value ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: value ? Colors.green[100] : Colors.red[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              value ? 'ENABLED' : 'DISABLED',
              style: TextStyle(
                color: value ? Colors.green[700] : Colors.red[700],
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
