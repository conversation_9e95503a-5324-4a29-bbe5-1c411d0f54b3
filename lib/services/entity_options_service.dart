import 'package:SolidCheck/core/constants/constants.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

class EntityOptionsService {
  late final Dio _dio;
  static final Map<String, Map<String, bool>> _cache = {};
  static final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(minutes: 5);

  EntityOptionsService() {
    _dio = Dio(
      BaseOptions(
        baseUrl: AppConstants.baseURL,
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ),
    );

    _dio.interceptors.add(
      LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (obj) => debugPrint('🔵 Entity Options API: $obj'),
      ),
    );
  }

  Future<bool> getOptionValue(int entityId, String optionName, {bool defaultValue = false}) async {
    try {
      // For testing purposes, use hardcoded values until API is ready
      if (optionName == 'allow_aidoc_scanner') {
        // TODO: Remove this hardcoded value once API is implemented
        return false; // Disable AI scanner for testing
      }

      final response = await _dio.get(
        '/entities/$entityId/option/$optionName',
      );

      if (response.data['success'] == true) {
        final value = response.data['data'];

        if (value is bool) {
          return value;
        } else if (value is String) {
          return value.toLowerCase() == 'true' || value == '1';
        } else if (value is int) {
          return value == 1;
        }

        return defaultValue;
      } else {
        return defaultValue;
      }
    } catch (e) {
      // Fallback values for testing
      if (optionName == 'allow_aidoc_scanner') {
        return false; // Disable for testing
      }

      return defaultValue;
    }
  }

  Future<Map<String, dynamic>> getMultipleOptions(int entityId, List<String> optionNames, {String? token}) async {
    try {
      final headers = <String, String>{};
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }

      final response = await _dio.post(
        '/entities/$entityId/options',
        data: optionNames,
        options: Options(headers: headers),
      ).timeout(const Duration(seconds: 3));

      if (response.data['success'] == true) {
        return response.data['data']['options'] ?? {};
      } else {
        return {};
      }
    } catch (e) {
      return {};
    }
  }

  Future<EntityOptionWithSource?> getOptionWithSource(int entityId, String optionName) async {
    try {

      final response = await _dio.get(
        '/entities/$entityId/option/$optionName',
        queryParameters: {'include_source': true},
      );

      if (response.data['success'] == true) {
        return EntityOptionWithSource.fromJson(response.data['data']);
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  Future<List<AccessibleEntity>> getAccessibleEntities() async {
    try {

      final response = await _dio.get('/entities/accessible');

      if (response.data['success'] == true) {
        final entities = response.data['data']['entities'] as List;
        return entities.map((e) => AccessibleEntity.fromJson(e)).toList();
      } else {
        return [];
      }
    } catch (e) {
      return [];
    }
  }

  Future<bool> isAiDocumentScannerEnabled(int entityId) async {
    return await getOptionValue(entityId, 'allow_aidoc_scanner', defaultValue: false);
  }

  Future<Map<String, bool>> getDocumentNominationOptions(int entityId, {String? token}) async {
    final cacheKey = 'entity_$entityId';
    final now = DateTime.now();

    // Check cache first
    if (_cache.containsKey(cacheKey) && _cacheTimestamps.containsKey(cacheKey)) {
      final cacheTime = _cacheTimestamps[cacheKey]!;
      if (now.difference(cacheTime) < _cacheExpiry) {
        return _cache[cacheKey]!;
      }
    }

    try {
      final options = await getMultipleOptions(entityId, [
        'allow_aidoc_scanner',
        'require_document_verification',
        'allow_manual_document_entry',
        'enable_document_upload',
      ], token: token);

      final result = {
        'allow_aidoc_scanner': _parseBoolOption(options['allow_aidoc_scanner'], false), // Default to false for testing
        'require_document_verification': _parseBoolOption(options['require_document_verification'], true),
        'allow_manual_document_entry': _parseBoolOption(options['allow_manual_document_entry'], true),
        'enable_document_upload': _parseBoolOption(options['enable_document_upload'], true),
      };

      // Cache the result
      _cache[cacheKey] = result;
      _cacheTimestamps[cacheKey] = now;

      return result;
    } catch (e) {

      // Return fallback values for testing
      final fallback = {
        'allow_aidoc_scanner': false, // Disable AI scanner for testing
        'require_document_verification': true,
        'allow_manual_document_entry': true,
        'enable_document_upload': true,
      };

      // Cache fallback values too to avoid repeated failures
      _cache[cacheKey] = fallback;
      _cacheTimestamps[cacheKey] = now;

      return fallback;
    }
  }

  bool _parseBoolOption(dynamic value, bool defaultValue) {
    if (value is bool) return value;
    if (value is String) return value.toLowerCase() == 'true' || value == '1';
    if (value is int) return value == 1;
    return defaultValue;
  }
}

class EntityOptionWithSource {
  final EntityInfo entity;
  final String optionName;
  final dynamic optionValue;
  final String source;
  final EntityInfo? sourceEntity;
  final List<HierarchyEntity> hierarchyPath;

  EntityOptionWithSource({
    required this.entity,
    required this.optionName,
    required this.optionValue,
    required this.source,
    this.sourceEntity,
    required this.hierarchyPath,
  });

  factory EntityOptionWithSource.fromJson(Map<String, dynamic> json) {
    return EntityOptionWithSource(
      entity: EntityInfo.fromJson(json['entity']),
      optionName: json['option_name'],
      optionValue: json['option_value'],
      source: json['source'],
      sourceEntity: json['source_entity'] != null 
          ? EntityInfo.fromJson(json['source_entity'])
          : null,
      hierarchyPath: (json['hierarchy_path'] as List)
          .map((e) => HierarchyEntity.fromJson(e))
          .toList(),
    );
  }
}

class EntityInfo {
  final int id;
  final String name;
  final String? entityCode;
  final String entityType;

  EntityInfo({
    required this.id,
    required this.name,
    this.entityCode,
    required this.entityType,
  });

  factory EntityInfo.fromJson(Map<String, dynamic> json) {
    return EntityInfo(
      id: json['id'],
      name: json['name'],
      entityCode: json['entity_code'],
      entityType: json['entity_type'],
    );
  }
}

class HierarchyEntity {
  final int id;
  final String name;
  final String type;

  HierarchyEntity({
    required this.id,
    required this.name,
    required this.type,
  });

  factory HierarchyEntity.fromJson(Map<String, dynamic> json) {
    return HierarchyEntity(
      id: json['id'],
      name: json['name'],
      type: json['type'],
    );
  }
}

class AccessibleEntity {
  final int id;
  final String name;
  final String entityCode;
  final String entityType;
  final String? contactEmail;
  final String? phone;

  AccessibleEntity({
    required this.id,
    required this.name,
    required this.entityCode,
    required this.entityType,
    this.contactEmail,
    this.phone,
  });

  factory AccessibleEntity.fromJson(Map<String, dynamic> json) {
    return AccessibleEntity(
      id: json['id'],
      name: json['name'],
      entityCode: json['entity_code'],
      entityType: json['entity_type'],
      contactEmail: json['contact_email'],
      phone: json['phone'],
    );
  }
}
