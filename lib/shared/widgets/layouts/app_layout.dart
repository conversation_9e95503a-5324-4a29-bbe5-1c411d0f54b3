import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/shared/widgets/navigation/navbar_providers.dart';
import 'package:SolidCheck/shared/widgets/navigation/universal_navbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class AppLayout extends ConsumerWidget {
  final Widget child;
  final NavbarType? navbarType;
  final bool showNavbar;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final String? customTitle;
  final List<Widget>? customActions;

  const AppLayout({
    super.key,
    required this.child,
    this.navbarType,
    this.showNavbar = true,
    this.showBackButton = false,
    this.onBackPressed,
    this.customTitle,
    this.customActions,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userType = ref.watch(userTypeProvider);
    final showNavbarState = ref.watch(navbarVisibilityProvider);
    final isMobile = context.isMobile;

    NavbarType effectiveNavbarType =
        navbarType ?? _getNavbarTypeFromUserType(userType);

    return Scaffold(
      appBar: (showNavbar && showNavbarState)
          ? UniversalNavbar(
              type: effectiveNavbarType,
              showBackButton: showBackButton,
              onBackPressed: onBackPressed,
              customTitle: customTitle,
              customActions: customActions,
              showDrawerIcon: isMobile,
              onDrawerPressed: () {
                ref.read(drawerStateProvider.notifier).state = true;
              },
            )
          : null,
      bottomNavigationBar:
          (showNavbar && showNavbarState && isMobile && !showBackButton)
          ? UniversalBottomNavbar(type: effectiveNavbarType)
          : null,
      drawer: isMobile
          ? _buildMobileDrawer(context, ref, effectiveNavbarType)
          : null,
      body: child,
    );
  }

  NavbarType _getNavbarTypeFromUserType(String userType) {
    switch (userType.toLowerCase()) {
      case 'applicant':
        return NavbarType.applicant;
      case 'admin':
        return NavbarType.admin;
      case 'client':
      default:
        return NavbarType.client;
    }
  }

  Widget _buildMobileDrawer(
    BuildContext context,
    WidgetRef ref,
    NavbarType navbarType,
  ) {
    final config = _getConfigForType(navbarType);
    final selectedIndex = ref.watch(globalSelectedIndexProvider);

    return Drawer(
      child: Column(
        children: [
          Container(
            height: 120,
            width: double.infinity,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [Color(0xFF1E88E5), Color(0xFF42A5F5)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: const SafeArea(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    CircleAvatar(
                      radius: 20,
                      backgroundColor: Colors.white,
                      child: Text(
                        'JS',
                        style: TextStyle(
                          color: Color(0xFF1E88E5),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'John Smith',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.zero,
              itemCount: config.items.length,
              itemBuilder: (context, index) {
                final item = config.items[index];
                final isSelected = selectedIndex == item.index;

                return ListTile(
                  leading: Icon(
                    item.icon,
                    color: isSelected
                        ? const Color(0xFF1E88E5)
                        : Colors.grey[600],
                  ),
                  title: Text(
                    item.label,
                    style: TextStyle(
                      color: isSelected
                          ? const Color(0xFF1E88E5)
                          : Colors.grey[800],
                      fontWeight: isSelected
                          ? FontWeight.w600
                          : FontWeight.normal,
                    ),
                  ),
                  selected: isSelected,
                  selectedTileColor: const Color(0xFF1E88E5).withValues(alpha: 0.1),
                  onTap: () {
                    ref.read(globalSelectedIndexProvider.notifier).state =
                        item.index;
                    Navigator.of(context).pop();
                  },
                );
              },
            ),
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.settings, color: Colors.grey),
            title: const Text('Settings'),
            onTap: () {
              Navigator.of(context).pop();
            },
          ),
          ListTile(
            leading: const Icon(Icons.logout, color: Colors.grey),
            title: const Text('Logout'),
            onTap: () {
              Navigator.of(context).pop();
            },
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  NavigationConfig _getConfigForType(NavbarType type) {
    switch (type) {
      case NavbarType.client:
        return NavbarConfigs.client;
      case NavbarType.applicant:
        return NavbarConfigs.applicant;
      case NavbarType.admin:
        return NavbarConfigs.admin;
    }
  }
}

class SimpleLayout extends StatelessWidget {
  final Widget child;
  final PreferredSizeWidget? appBar;

  const SimpleLayout({super.key, required this.child, this.appBar});

  @override
  Widget build(BuildContext context) {
    return Scaffold(appBar: appBar, body: child);
  }
}

class CustomAppBarLayout extends ConsumerWidget {
  final Widget child;
  final String title;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final List<Widget>? actions;

  const CustomAppBarLayout({
    super.key,
    required this.child,
    required this.title,
    this.showBackButton = false,
    this.onBackPressed,
    this.actions,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0.5,
        leading: showBackButton
            ? IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.black),
                onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
              )
            : null,
        title: Text(
          title,
          style: const TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: actions,
      ),
      body: child,
    );
  }
}
