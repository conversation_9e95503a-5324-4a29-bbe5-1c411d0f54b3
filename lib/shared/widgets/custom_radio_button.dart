import 'package:SolidCheck/core/theme/colors.dart';
import 'package:flutter/material.dart';

class CustomRadioButton extends StatelessWidget {
  final bool isSelected;

  const CustomRadioButton({super.key, required this.isSelected});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 15,
      height: 15,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: isSelected ? AppColors.kCheckBoxLoginColor : Colors.transparent,
        border: Border.all(
          color: AppColors.kBlueColor,
          width: 2,
        ),
      ),
      child: isSelected
          ? Icon(
              Icons.circle,
              color: AppColors.kCheckBoxLoginColor,
              size: 8,
            )
          : null,
    );
  }
}
