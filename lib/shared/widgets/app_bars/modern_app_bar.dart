import 'package:SolidCheck/core/navigation/app_router_new.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/features/auth/di/auth_providers.dart';
import 'package:SolidCheck/features/dashboard/presentation/widgets/appbar/sign_out_dialog_widget.dart';
import 'package:SolidCheck/shared/widgets/custom_snakbar.dart';
import 'package:SolidCheck/shared/widgets/logo/solid_check_logo_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';


final modernAppBarSelectedIndexProvider = StateProvider<int>((ref) => 0);

class ModernAppBar extends ConsumerWidget implements PreferredSizeWidget {
  final bool showDrawerIcon;
  final VoidCallback? onDrawerIconPressed;
  final bool automaticallyImplyLeading;
  final GlobalKey<ScaffoldState>? scaffoldKey;

  ModernAppBar({
    super.key,
    this.showDrawerIcon = false,
    this.onDrawerIconPressed,
    this.automaticallyImplyLeading = true,
    this.scaffoldKey,
  });

  final List<String> navigationItems = [
    'Overview',
    'Job Roles',
    'Reports',
    'Reminders',
    'Help',
  ];

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight + 10);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedIndex = ref.watch(modernAppBarSelectedIndexProvider);
    final isMobile = context.isMobile;
    
    return Container(
      decoration: BoxDecoration(
        color: AppColors.kWhiteColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: AppBar(
        automaticallyImplyLeading: automaticallyImplyLeading,
        backgroundColor: Colors.transparent,
        elevation: 0,
        toolbarHeight: kToolbarHeight + 10,
        title: isMobile ? _buildMobileTitle() : _buildDesktopTitle(ref, selectedIndex),
        actions: [
          if (!isMobile) _buildProfileSection(context, ref),
          if (isMobile && showDrawerIcon)
            IconButton(
              icon: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: AppColors.kLoginBoxGradientColor,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.menu,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              onPressed: onDrawerIconPressed,
            ),
        ],
      ),
    );
  }

  Widget _buildMobileTitle() {
    return Center(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: AppColors.kLoginBoxGradientColor,
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: AppColors.kBlueColor.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: const SolidCheckHorizontalLogo(
          height: 32,
          width: 120,
        ),
      ),
    );
  }

  Widget _buildDesktopTitle(WidgetRef ref, int selectedIndex) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: AppColors.kLoginBoxGradientColor,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: AppColors.kBlueColor.withValues(alpha: 0.3),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: const SolidCheckHorizontalLogo(
            height: 36,
            width: 120,
          ),
        ),
        const SizedBox(width: 32),
        Expanded(
          child: _buildNavigationItems(ref, selectedIndex),
        ),
      ],
    );
  }

  Widget _buildNavigationItems(WidgetRef ref, int selectedIndex) {
    return Row(
      children: navigationItems.asMap().entries.map((entry) {
        final index = entry.key;
        final item = entry.value;
        final isSelected = selectedIndex == index;
        
        return Padding(
          padding: const EdgeInsets.only(right: 8),
          child: _buildNavigationItem(
            ref,
            item,
            index,
            isSelected,
          ),
        );
      }).toList(),
    );
  }

  Widget _buildNavigationItem(WidgetRef ref, String title, int index, bool isSelected) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          ref.read(modernAppBarSelectedIndexProvider.notifier).state = index;
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          decoration: BoxDecoration(
            gradient: isSelected
                ? LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: AppColors.kLoginBoxGradientColor,
                  )
                : null,
            borderRadius: BorderRadius.circular(12),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: AppColors.kBlueColor.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ]
                : null,
          ),
          child: Text(
            title,
            style: TextStyle(
              color: isSelected ? Colors.white : AppColors.kBlackColor,
              fontSize: 14,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfileSection(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.only(right: 16),
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        child: GestureDetector(
          onTap: () => _showProfileMenu(context, ref),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: AppColors.kWhiteColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.kBlueColor.withValues(alpha: 0.2),
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: AppColors.kLoginBoxGradientColor,
                    ),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const Icon(
                    Icons.person,
                    color: Colors.white,
                    size: 18,
                  ),
                ),
                const SizedBox(width: 8),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: AppColors.kBlueColor,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _showProfileMenu(BuildContext context, WidgetRef ref) async {
    final shouldSignOut = await buildLogoutConfirmationDialogAppBar(context);
    if (shouldSignOut && context.mounted) {
      try {
        await ref.read(authViewModelProvider.notifier).logout();
        if (context.mounted) {
          AppRouter.navigateToLogin();
        }
      } catch (error) {
        if (context.mounted) {
          CustomSnackBar.show(
            context: context,
            message: 'Error signing out: $error',
            backgroundColor: Colors.red,
            textColor: Colors.white,
            icon: Icons.error,
          );
        }
      }
    }
  }
}
