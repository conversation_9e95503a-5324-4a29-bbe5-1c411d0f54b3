import 'package:SolidCheck/core/theme/colors.dart';
import 'package:flutter/material.dart';

class MobileViewNavBar extends StatelessWidget {
  const MobileViewNavBar({
    super.key,
    required this.title,
    required this.icon,
    this.onTap,
    this.isSelected = false,
    required this.context,
  });

  final void Function()? onTap;
  final BuildContext context;
  final IconData icon;
  final bool isSelected;
  final String title;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Container(
            height: 5.0,
            decoration: BoxDecoration(
              color: isSelected ? AppColors.kWhiteColor : Colors.transparent,
              borderRadius: BorderRadius.circular(100.0),
            ),
          ),
          IconButton(
            onPressed: null,
            icon: Icon(
              icon,
              color: isSelected ? AppColors.kWhiteColor : AppColors.kBlackColor,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12.0,
              color: isSelected ? AppColors.kWhiteColor : AppColors.kBlackColor,
            ),
          ),
          const SizedBox(height: 2.0),
        ],
      ),
    );
  }
}
