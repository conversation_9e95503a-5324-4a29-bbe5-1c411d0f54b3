import 'package:SolidCheck/core/theme/colors.dart';
import 'package:flutter/material.dart';

class SolidCheckLogoWidget extends StatelessWidget {
  final double? height;
  final double? width;
  final BoxFit fit;
  final bool showFallback;
  final String? customAssetPath;

  const SolidCheckLogoWidget({
    super.key,
    this.height,
    this.width,
    this.fit = BoxFit.contain,
    this.showFallback = true,
    this.customAssetPath,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      opacity: 1.0,
      duration: const Duration(milliseconds: 300),
      child: _buildFallbackLogo(),
    );
  }

  Widget _buildFallbackLogo() {
    final logoHeight = height ?? 120;
    final isCompact = logoHeight < 80;

    return SizedBox(
      height: logoHeight,
      width: width ?? (logoHeight * 1.8),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(height: isCompact ? 4 : 8),
            Icon(
              Icons.all_inclusive,
              color: AppColors.kBlueColor,
              size: isCompact ? logoHeight * 0.2 : logoHeight * 0.25,
            ),
          ],
        ),
      ),
    );
  }
}

class SolidCheckLoginLogo extends StatelessWidget {
  final double? height;
  final double? width;
  final bool isMobile;

  const SolidCheckLoginLogo({
    super.key,
    this.height,
    this.width,
    this.isMobile = false,
  });

  @override
  Widget build(BuildContext context) {
    final logoHeight = height ?? (isMobile ? 80.0 : 120.0);

    return SolidCheckLogoWidget(
      height: logoHeight,
      width: width,
      showFallback: true,
    );
  }
}

class SolidCheckHorizontalLogo extends StatelessWidget {
  final double? height;
  final double? width;

  const SolidCheckHorizontalLogo({super.key, this.height, this.width});

  @override
  Widget build(BuildContext context) {
    return SolidCheckLogoWidget(
      height: height ?? 36,
      width: width ?? (height != null ? height! * 2.5 : 90),
      showFallback: true,
    );
  }
}

class SolidCheckAppBarLogo extends StatelessWidget {
  final double? height;
  final double? width;

  const SolidCheckAppBarLogo({super.key, this.height, this.width});

  @override
  Widget build(BuildContext context) {
    return SolidCheckLogoWidget(
      height: height ?? 40,
      width: width ?? 100,
      showFallback: true,
    );
  }
}

class SolidCheckCompactLogo extends StatelessWidget {
  final double? height;
  final double? width;
  final Color? color;

  const SolidCheckCompactLogo({super.key, this.height, this.width, this.color});

  @override
  Widget build(BuildContext context) {
    final logoHeight = height ?? 24.0;
    final logoColor = color ?? AppColors.kBlueColor;

    return SizedBox(
      height: logoHeight,
      width: width ?? logoHeight,
      child: Center(
        child: Icon(
          Icons.all_inclusive,
          color: logoColor,
          size: logoHeight * 0.8,
        ),
      ),
    );
  }
}
