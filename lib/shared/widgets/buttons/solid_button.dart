import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:flutter/material.dart';

enum SolidButtonType {
  primary,
  secondary,
  outline,
  text,
  danger,
  success,
}

enum SolidButtonSize {
  small,
  medium,
  large,
}

class SolidButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final SolidButtonType type;
  final SolidButtonSize size;
  final IconData? icon;
  final bool isLoading;
  final bool isFullWidth;
  final EdgeInsetsGeometry? padding;
  final double? customWidth;
  final double? customHeight;

  const SolidButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = SolidButtonType.primary,
    this.size = SolidButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
    this.padding,
    this.customWidth,
    this.customHeight,
  });

  @override
  Widget build(BuildContext context) {
    final isResponsive = context.isDesktop;
    
    return Container(
      width: isFullWidth ? double.infinity : customWidth,
      height: customHeight ?? _getButtonHeight(isResponsive),
      padding: padding,
      child: _buildButton(context, isResponsive),
    );
  }

  Widget _buildButton(BuildContext context, bool isResponsive) {
    switch (type) {
      case SolidButtonType.primary:
        return _buildPrimaryButton(isResponsive);
      case SolidButtonType.secondary:
        return _buildSecondaryButton(isResponsive);
      case SolidButtonType.outline:
        return _buildOutlineButton(isResponsive);
      case SolidButtonType.text:
        return _buildTextButton(isResponsive);
      case SolidButtonType.danger:
        return _buildDangerButton(isResponsive);
      case SolidButtonType.success:
        return _buildSuccessButton(isResponsive);
    }
  }

  Widget _buildPrimaryButton(bool isResponsive) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.kBlueColor,
            AppColors.kBlueDarkColor,
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.kBlueColor.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: EdgeInsets.symmetric(
            horizontal: isResponsive ? 24 : 16,
            vertical: isResponsive ? 16 : 12,
          ),
        ),
        child: _buildButtonContent(Colors.white, isResponsive),
      ),
    );
  }

  Widget _buildSecondaryButton(bool isResponsive) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.kWhiteColor,
        foregroundColor: AppColors.kBlueColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: AppColors.kBlueColor, width: 2),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: isResponsive ? 24 : 16,
          vertical: isResponsive ? 16 : 12,
        ),
        elevation: 2,
      ),
      child: _buildButtonContent(AppColors.kBlueColor, isResponsive),
    );
  }

  Widget _buildOutlineButton(bool isResponsive) {
    return OutlinedButton(
      onPressed: isLoading ? null : onPressed,
      style: OutlinedButton.styleFrom(
        foregroundColor: AppColors.kBlueColor,
        side: BorderSide(color: AppColors.kBlueColor, width: 1.5),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: isResponsive ? 24 : 16,
          vertical: isResponsive ? 16 : 12,
        ),
      ),
      child: _buildButtonContent(AppColors.kBlueColor, isResponsive),
    );
  }

  Widget _buildTextButton(bool isResponsive) {
    return TextButton(
      onPressed: isLoading ? null : onPressed,
      style: TextButton.styleFrom(
        foregroundColor: AppColors.kBlueColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: isResponsive ? 24 : 16,
          vertical: isResponsive ? 16 : 12,
        ),
      ),
      child: _buildButtonContent(AppColors.kBlueColor, isResponsive),
    );
  }

  Widget _buildDangerButton(bool isResponsive) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: isResponsive ? 24 : 16,
          vertical: isResponsive ? 16 : 12,
        ),
        elevation: 2,
      ),
      child: _buildButtonContent(Colors.white, isResponsive),
    );
  }

  Widget _buildSuccessButton(bool isResponsive) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: isResponsive ? 24 : 16,
          vertical: isResponsive ? 16 : 12,
        ),
        elevation: 2,
      ),
      child: _buildButtonContent(Colors.white, isResponsive),
    );
  }

  Widget _buildButtonContent(Color textColor, bool isResponsive) {
    if (isLoading) {
      return SizedBox(
        height: 20,
        width: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(textColor),
        ),
      );
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: _getIconSize(isResponsive),
            color: textColor,
          ),
          const SizedBox(width: 8),
          Text(
            text,
            style: TextStyle(
              fontSize: _getFontSize(isResponsive),
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
        ],
      );
    }

    return Text(
      text,
      style: TextStyle(
        fontSize: _getFontSize(isResponsive),
        fontWeight: FontWeight.bold,
        color: textColor,
      ),
    );
  }

  double _getButtonHeight(bool isResponsive) {
    switch (size) {
      case SolidButtonSize.small:
        return isResponsive ? 36 : 40;
      case SolidButtonSize.medium:
        return isResponsive ? 44 : 48;
      case SolidButtonSize.large:
        return isResponsive ? 52 : 56;
    }
  }

  double _getFontSize(bool isResponsive) {
    switch (size) {
      case SolidButtonSize.small:
        return isResponsive ? 12 : 14;
      case SolidButtonSize.medium:
        return isResponsive ? 14 : 16;
      case SolidButtonSize.large:
        return isResponsive ? 16 : 18;
    }
  }

  double _getIconSize(bool isResponsive) {
    switch (size) {
      case SolidButtonSize.small:
        return isResponsive ? 16 : 18;
      case SolidButtonSize.medium:
        return isResponsive ? 18 : 20;
      case SolidButtonSize.large:
        return isResponsive ? 20 : 22;
    }
  }
}
