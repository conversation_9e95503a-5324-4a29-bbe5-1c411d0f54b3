import 'package:flutter/material.dart';

class DividerWithText extends StatelessWidget {
  final String title;

  const DividerWithText({
    super.key,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Padding(
        padding: const EdgeInsets.only(left: 20.0, right: 20.0),
        child: Row(
          children: [
            const Expanded(child: Divider()),
            const SizedBox(width: 10.0),
            Text(title),
            const SizedBox(width: 10.0),
            const Expanded(child: Divider()),
          ],
        ),
      ),
    );
  }
}
