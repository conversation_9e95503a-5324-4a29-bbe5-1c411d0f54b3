import 'package:SolidCheck/core/theme/colors.dart';

import 'package:flutter/material.dart';

class CustomDropdownButton2 extends StatelessWidget {
  const CustomDropdownButton2({
    required this.hint,
    required this.title,
    this.value,
    this.selectedValue,
    required this.dropdownItems,
    required this.onChanged,
    this.selectedItemBuilder,
    this.hintAlignment,
    this.valueAlignment,
    this.buttonHeight,
    this.buttonWidth,
    this.buttonPadding,
    this.buttonDecoration,
    this.buttonElevation,
    this.icon,
    this.iconSize,
    this.iconEnabledColor,
    this.iconDisabledColor,
    this.itemHeight,
    this.itemPadding,
    this.dropdownHeight,
    this.dropdownWidth,
    this.dropdownPadding,
    this.dropdownDecoration,
    this.dropdownElevation,
    this.scrollbarRadius,
    this.scrollbarThickness,
    this.scrollbarAlwaysShow,
    this.offset = Offset.zero,
    super.key,
    this.isRquired,
    this.validator,
  });
  final String hint;
  final String title;
  final String? value;
  final String? selectedValue;
  final List<DropdownMenuItem<String>> dropdownItems;
  final ValueChanged<String?>? onChanged;
  final DropdownButtonBuilder? selectedItemBuilder;
  final Alignment? hintAlignment;
  final Alignment? valueAlignment;
  final double? buttonHeight, buttonWidth;
  final EdgeInsetsGeometry? buttonPadding;
  final BoxDecoration? buttonDecoration;
  final int? buttonElevation;
  final Widget? icon;
  final double? iconSize;
  final Color? iconEnabledColor;
  final Color? iconDisabledColor;
  final double? itemHeight;
  final EdgeInsetsGeometry? itemPadding;
  final double? dropdownHeight, dropdownWidth;
  final EdgeInsetsGeometry? dropdownPadding;
  final BoxDecoration? dropdownDecoration;
  final int? dropdownElevation;
  final Radius? scrollbarRadius;
  final double? scrollbarThickness;
  final bool? scrollbarAlwaysShow;
  final Offset offset;
  final bool? isRquired;
  final String? Function(String?)? validator;

  @override
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            color: isRquired == true
                ? AppColors.orangeColor
                : AppColors.kBlackColor,
          ),
        ),
        const SizedBox(height: 8.0),
        SizedBox(
          width: MediaQuery.of(context).size.width,
          height: 48,
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              isExpanded: true,
              hint: Container(
                alignment: hintAlignment,
                child: Text(
                  hint,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).hintColor,
                  ),
                ),
              ),
              value: selectedValue,
              items: dropdownItems,
              onChanged: onChanged,
              selectedItemBuilder: selectedItemBuilder,
              icon: icon ?? const Icon(Icons.keyboard_arrow_down_rounded),
              iconSize: 20,
              iconEnabledColor: iconEnabledColor,
              iconDisabledColor: iconDisabledColor,
              dropdownColor: AppColors.kQuestionContainerColor,
            ),
          ),
        ),
      ],
    );
  }
}

class CustomDropdownButton extends StatelessWidget {
  const CustomDropdownButton({
    required this.hint,
    required this.title,
    this.value,
    this.selectedValue,
    required this.dropdownItems,
    required this.onChanged,
    this.selectedItemBuilder,
    this.hintAlignment,
    this.valueAlignment,
    this.buttonHeight,
    this.buttonWidth,
    this.buttonPadding,
    this.buttonDecoration,
    this.buttonElevation,
    this.icon,
    this.iconSize,
    this.iconEnabledColor,
    this.iconDisabledColor,
    this.itemHeight,
    this.itemPadding,
    this.dropdownHeight,
    this.dropdownWidth,
    this.dropdownPadding,
    this.dropdownDecoration,
    this.dropdownElevation,
    this.scrollbarRadius,
    this.scrollbarThickness,
    this.scrollbarAlwaysShow,
    this.offset = Offset.zero,
    this.validator,
    super.key,
  });

  final String hint;
  final String title;
  final String? value;
  final String? selectedValue;
  final List<DropdownMenuItem<String>> dropdownItems;
  final ValueChanged<String?>? onChanged;
  final DropdownButtonBuilder? selectedItemBuilder;
  final Alignment? hintAlignment;
  final Alignment? valueAlignment;
  final double? buttonHeight, buttonWidth;
  final EdgeInsetsGeometry? buttonPadding;
  final BoxDecoration? buttonDecoration;
  final int? buttonElevation;
  final Widget? icon;
  final double? iconSize;
  final Color? iconEnabledColor;
  final Color? iconDisabledColor;
  final double? itemHeight;
  final EdgeInsetsGeometry? itemPadding;
  final double? dropdownHeight, dropdownWidth;
  final EdgeInsetsGeometry? dropdownPadding;
  final BoxDecoration? dropdownDecoration;
  final int? dropdownElevation;
  final Radius? scrollbarRadius;
  final double? scrollbarThickness;
  final bool? scrollbarAlwaysShow;
  final Offset offset;
  final String? Function(String?)? validator;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title),
        const SizedBox(height: 8.0),
        FormField<String>(
          validator: validator,
          builder: (field) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: MediaQuery.of(context).size.width,
                  height: 52,
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      isExpanded: true,
                      hint: Container(
                        alignment: hintAlignment,
                        child: Text(
                          hint,
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          style: TextStyle(
                            fontSize: 14,
                            color: Theme.of(context).hintColor,
                          ),
                        ),
                      ),
                      value: selectedValue,
                      items: dropdownItems,
                      onChanged: (value) {
                        field.didChange(value);
                        onChanged?.call(value);
                      },
                      selectedItemBuilder: selectedItemBuilder,
                      icon: icon ?? const Icon(Icons.keyboard_arrow_down_rounded),
                      iconSize: 20,
                      iconEnabledColor: iconEnabledColor,
                      iconDisabledColor: iconDisabledColor,
                      dropdownColor: AppColors.kQuestionContainerColor,
                    ),
                  ),
                ),
                if (field.hasError)
                  Padding(
                    padding: const EdgeInsets.only(top: 5),
                    child: Text(
                      field.errorText ?? '',
                      style: const TextStyle(
                        color: Colors.red,
                        fontSize: 12,
                      ),
                    ),
                  ),
              ],
            );
          },
        ),
      ],
    );
  }
}

class CustomDropDownMain extends StatelessWidget {
  final String title;
  final List<DropdownMenuItem<String>> dropdownItems;
  final void Function(String?) onChanged;
  final String? selectedValue;
  final Widget hint;
  final bool isHintTextRequired;
  final bool isFillColorRequired;

  const CustomDropDownMain({
    super.key,
    required this.title,
    required this.dropdownItems,
    required this.onChanged,
    this.selectedValue,
    required this.hint,
    required this.isHintTextRequired,
    required this.isFillColorRequired,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title),
          const SizedBox(height: 8.0),
          DropdownButtonFormField<String>(
            borderRadius: BorderRadius.circular(8.0),
            value: selectedValue,
            decoration: InputDecoration(
              filled: true,
              fillColor:
                  isFillColorRequired == true ? AppColors.kWhiteColor : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
            ),
            hint: isHintTextRequired == true ? hint : null,
            items: dropdownItems,
            isExpanded: true,
            onChanged: onChanged,
            dropdownColor: AppColors.kQuestionContainerColor,
          ),
          const SizedBox(height: 48.0, child: SizedBox()),
        ],
      ),
    );
  }
}
