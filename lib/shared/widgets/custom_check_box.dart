import 'package:SolidCheck/core/theme/colors.dart';
import 'package:flutter/material.dart';

class CustomCheckBoxMain extends StatefulWidget {
  final bool value;
  final ValueChanged<bool?> onChanged;
  final String title;
  final double size;

  const CustomCheckBoxMain({
    super.key,
    required this.value,
    required this.onChanged,
    this.title = '',
    this.size = 20.0,
  });

  @override
  CustomCheckBoxMainState createState() => CustomCheckBoxMainState();
}

class CustomCheckBoxMainState extends State<CustomCheckBoxMain> {
  late bool isChecked;

  @override
  void initState() {
    super.initState();
    isChecked = widget.value;
  }

  void _toggleCheckBox(bool? newValue) {
    setState(() {
      isChecked = newValue ?? false;
    });
    widget.onChanged(isChecked);
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          height: widget.size,
          width: widget.size,
          decoration: BoxDecoration(
            shape: BoxShape.rectangle,
            border: Border.all(color: AppColors.kBlueColor, width: 2),
            color: AppColors.kWhiteColor,
          ),
          child: Center(
            child: Checkbox(
              value: isChecked,
              onChanged: _toggleCheckBox,
              activeColor: AppColors.kBlueColor,
              checkColor: AppColors.kBlueColor,
              fillColor: WidgetStateProperty.all(AppColors.kWhiteColor),
            ),
          ),
        ),
        if (widget.title.isNotEmpty) ...[
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              widget.title,
              style: TextStyle(
                color: AppColors.kBlueColor,
                fontWeight: FontWeight.w400,
              ),
              overflow: TextOverflow.visible,
            ),
          ),
        ],
      ],
    );
  }
}

class LogInCheckBox extends StatelessWidget {
  final bool value;
  final void Function(bool?) onChanged;

  const LogInCheckBox({
    super.key,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Checkbox(
          value: value,
          onChanged: onChanged,
          checkColor: Colors.white,
          fillColor: WidgetStateProperty.resolveWith<Color?>(
              (Set<WidgetState> states) {
            return AppColors.sideBarMenuColor;
          }),
        ),
        const Text('Remember Me', style: TextStyle(fontSize: 16)),
      ],
    );
  }
}
