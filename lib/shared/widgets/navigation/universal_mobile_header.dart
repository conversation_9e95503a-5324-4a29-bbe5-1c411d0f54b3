import 'package:SolidCheck/core/theme/colors.dart';
import 'package:flutter/material.dart';

class UniversalMobileHeader extends StatelessWidget {
  final String? title;
  final String? subtitle;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final List<Widget>? actions;

  const UniversalMobileHeader({
    super.key,
    this.title,
    this.subtitle,
    this.showBackButton = false,
    this.onBackPressed,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 56, // Standard Android AppBar height
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: SafeArea(
        bottom: false,
        child: Container(
          height: 56,
          padding: const EdgeInsets.symmetric(horizontal: 4.0),
          child: Row(
            children: [
              Builder(
                builder: (context) => IconButton(
                  icon: Icon(
                    Icons.menu,
                    color: AppColors.kBlueColor,
                    size: 24,
                  ),
                  onPressed: () => Scaffold.of(context).openDrawer(),
                  padding: const EdgeInsets.all(16),
                  constraints: const BoxConstraints(
                    minWidth: 48,
                    minHeight: 48,
                  ),
                  splashRadius: 24,
                ),
              ),
              
              Expanded(
                child: _buildCenterContent(),
              ),
              
              if (showBackButton)
                IconButton(
                  icon: Icon(
                    Icons.arrow_back,
                    color: AppColors.kBlueColor,
                    size: 24,
                  ),
                  onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
                  padding: const EdgeInsets.all(16),
                  constraints: const BoxConstraints(
                    minWidth: 48,
                    minHeight: 48,
                  ),
                  splashRadius: 24,
                )
              else if (actions != null)
                ...actions!
              else
                const SizedBox(width: 8),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCenterContent() {
    if (title != null || subtitle != null) {
      return Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Logo
            Icon(
              Icons.all_inclusive,
              color: AppColors.kBlueColor,
              size: 24,
            ),
            
            if (title != null) ...[
              const SizedBox(height: 2),
              Text(
                title!,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: AppColors.kBlueColor,
                  height: 1.0,
                ),
                textAlign: TextAlign.center,
              ),
            ],
            
            if (subtitle != null) ...[
              Text(
                subtitle!,
                style: const TextStyle(
                  fontSize: 10,
                  color: Color(0xFF6B7280),
                  height: 1.0,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      );
    }

    return Center(
      child: Icon(
        Icons.all_inclusive,
        color: AppColors.kBlueColor,
        size: 32,
      ),
    );
  }
}
