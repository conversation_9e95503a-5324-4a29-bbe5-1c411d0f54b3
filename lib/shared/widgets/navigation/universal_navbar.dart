import 'package:SolidCheck/core/navigation/app_router_new.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/features/auth/di/auth_providers.dart';
import 'package:SolidCheck/features/dashboard/presentation/widgets/appbar/dashboard_appbar_widget.dart';
import 'package:SolidCheck/shared/widgets/logo/solid_check_logo_widget.dart';
import 'package:SolidCheck/shared/widgets/navigation/universal_mobile_header.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

enum NavbarType { client, applicant, admin }

class NavigationConfig {
  final List<NavigationItem> items;
  final String title;
  final bool showProfile;

  NavigationConfig({
    required this.items,
    required this.title,
    this.showProfile = true,
  });
}

class NavigationItem {
  final String label;
  final IconData icon;
  final String route;
  final int index;

  NavigationItem({
    required this.label,
    required this.icon,
    required this.route,
    required this.index,
  });
}

class NavbarConfigs {
  static NavigationConfig client = NavigationConfig(
    title: 'Client Dashboard',
    items: [
      NavigationItem(label: 'Overview', icon: Icons.home_outlined, route: '/dashboard', index: 0),
      NavigationItem(label: 'Job Roles', icon: Icons.assignment_outlined, route: '/job-roles', index: 1),
      NavigationItem(label: 'Download Reports', icon: Icons.download_outlined, route: '/reports', index: 2),
      NavigationItem(label: 'Reminders', icon: Icons.notifications_active_outlined, route: '/reminders', index: 3),
      NavigationItem(label: 'Help', icon: Icons.help_outline, route: '/help', index: 4),
    ],
  );

  static NavigationConfig applicant = NavigationConfig(
    title: 'Applicant Portal',
    items: [
      NavigationItem(label: 'Dashboard', icon: Icons.dashboard_outlined, route: '/applicant-dashboard', index: 0),
      NavigationItem(label: 'Applications', icon: Icons.assignment_outlined, route: '/applications', index: 1),
      NavigationItem(label: 'Documents', icon: Icons.folder_outlined, route: '/documents', index: 2),
      NavigationItem(label: 'Messages', icon: Icons.message_outlined, route: '/messages', index: 3),
    ],
  );

  static NavigationConfig admin = NavigationConfig(
    title: 'Admin Panel',
    items: [
      NavigationItem(label: 'Dashboard', icon: Icons.dashboard_outlined, route: '/admin-dashboard', index: 0),
      NavigationItem(label: 'Users', icon: Icons.people_outlined, route: '/users', index: 1),
      NavigationItem(label: 'Reports', icon: Icons.analytics_outlined, route: '/admin-reports', index: 2),
      NavigationItem(label: 'Settings', icon: Icons.settings_outlined, route: '/admin-settings', index: 3),
    ],
  );
}

class UniversalNavbar extends ConsumerWidget implements PreferredSizeWidget {
  final NavbarType type;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final bool showDrawerIcon;
  final VoidCallback? onDrawerPressed;
  final String? customTitle;
  final List<Widget>? customActions;

  const UniversalNavbar({
    super.key,
    required this.type,
    this.showBackButton = false,
    this.onBackPressed,
    this.showDrawerIcon = false,
    this.onDrawerPressed,
    this.customTitle,
    this.customActions,
  });

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  NavigationConfig get _config {
    switch (type) {
      case NavbarType.client:
        return NavbarConfigs.client;
      case NavbarType.applicant:
        return NavbarConfigs.applicant;
      case NavbarType.admin:
        return NavbarConfigs.admin;
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isMobile = context.isMobile;
    final selectedIndex = ref.watch(selectedIndexProvider);

    // Use enhanced mobile header for mobile devices
    if (isMobile) {
      return PreferredSize(
        preferredSize: const Size.fromHeight(56),
        child: UniversalMobileHeader(
          showBackButton: showBackButton,
          onBackPressed: onBackPressed,
          actions: customActions,
        ),
      );
    }

    // Desktop AppBar
    return AppBar(
      backgroundColor: AppColors.kWhiteColor,
      elevation: 0.5,
      automaticallyImplyLeading: false,
      title: _buildTitle(context, ref, isMobile, selectedIndex),
      actions: customActions ?? _buildActions(context, ref, isMobile),
    );
  }

  Widget _buildTitle(BuildContext context, WidgetRef ref, bool isMobile, int selectedIndex) {
    if (showBackButton) {
      return Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black),
            onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
          ),
          if (customTitle != null) ...[
            const SizedBox(width: 8),
            Text(
              customTitle!,
              style: TextStyle(
                color: AppColors.kBlueColor,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ],
      );
    }

    if (isMobile) {
      return const SolidCheckHorizontalLogo(height: 120.0, width: 120.0);
    }

    return Row(
      children: [
        const SolidCheckHorizontalLogo(height: 110.0, width: 110.0),
        const SizedBox(width: 20.0),
        Flexible(child: _buildNavigationItems(ref, selectedIndex)),
      ],
    );
  }

  Widget _buildNavigationItems(WidgetRef ref, int selectedIndex) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: _config.items.asMap().entries.map((entry) {
          final item = entry.value;
          final index = entry.key;
          final isSelected = index == selectedIndex;

          return InkWell(
            onTap: () {
              ref.read(selectedIndexProvider.notifier).state = index;
            },
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 12.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    item.label,
                    style: TextStyle(
                      fontSize: 16,
                      color: isSelected ? AppColors.kBlueColor : AppColors.kBlackColor,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                  if (isSelected)
                    Container(
                      margin: const EdgeInsets.only(top: 4.0),
                      height: 2.0,
                      width: 40.0,
                      color: AppColors.kBlueColor,
                    ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  List<Widget> _buildActions(BuildContext context, WidgetRef ref, bool isMobile) {
    final actions = <Widget>[];

    if (showDrawerIcon && isMobile) {
      actions.add(
        IconButton(
          icon: const Icon(Icons.menu, color: Colors.black),
          onPressed: onDrawerPressed,
        ),
      );
    }

    if (_config.showProfile && !isMobile) {
      actions.add(_buildProfileSection(context, ref));
    }

    return actions;
  }

  Widget _buildProfileSection(BuildContext context, WidgetRef ref) {
    final GlobalKey profileKey = GlobalKey();

    return Container(
      key: profileKey,
      margin: const EdgeInsets.only(right: 16),
      child: InkWell(
        onTap: () => _showProfileMenu(context, ref, profileKey),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircleAvatar(
              radius: 16,
              backgroundColor: AppColors.kBlueColor,
              child: const Text(
                'JS',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 8),
            const Text(
              'John Smith',
              style: TextStyle(
                color: Colors.black,
                fontSize: 14,
              ),
            ),
            const Icon(Icons.keyboard_arrow_down, color: Colors.black),
          ],
        ),
      ),
    );
  }

  Future<void> _showProfileMenu(BuildContext context, WidgetRef ref, GlobalKey profileKey) async {
    final RenderBox renderBox = profileKey.currentContext!.findRenderObject() as RenderBox;
    final offset = renderBox.localToGlobal(Offset.zero);
    final height = renderBox.size.height;

    await showMenu<int>(
      context: context,
      position: RelativeRect.fromLTRB(
        offset.dx,
        offset.dy + height,
        offset.dx + renderBox.size.width,
        offset.dy
      ),
      items: [
        PopupMenuItem<int>(
          value: 0,
          child: const Text('Account Settings'),
          onTap: () => AppRouter.navigateToAccountSettings(),
        ),
        PopupMenuItem<int>(
          value: 1,
          child: const Text('Notifications'),
          onTap: () {
          },
        ),
        PopupMenuItem<int>(
          value: 2,
          child: const Text('Logout'),
          onTap: () => _handleLogout(ref),
        ),
      ],
    );
  }

  void _handleLogout(WidgetRef ref) {
    ref.read(authViewModelProvider.notifier).logout();
    AppRouter.navigateToLogin();
  }
}

class UniversalBottomNavbar extends ConsumerWidget {
  final NavbarType type;

  const UniversalBottomNavbar({
    super.key,
    required this.type,
  });

  NavigationConfig get _config {
    switch (type) {
      case NavbarType.client:
        return NavbarConfigs.client;
      case NavbarType.applicant:
        return NavbarConfigs.applicant;
      case NavbarType.admin:
        return NavbarConfigs.admin;
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedIndex = ref.watch(selectedIndexProvider);

    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      selectedItemColor: AppColors.kNavItemSelected,
      unselectedItemColor: AppColors.kNavItem,
      showUnselectedLabels: true,
      currentIndex: selectedIndex.clamp(0, _config.items.length - 1),
      onTap: (index) {
        ref.read(selectedIndexProvider.notifier).state = index;
      },
      items: _config.items.map((item) {
        return BottomNavigationBarItem(
          icon: Icon(item.icon),
          label: item.label,
          backgroundColor: AppColors.sideBarMenuColor,
        );
      }).toList(),
    );
  }
}
