import 'dart:ui';

import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/applicants/presentation/viewmodels/applicant_dashboard_viewmodel.dart';
import 'package:SolidCheck/features/dbs/presentation/screens/dbs_application_form_screen.dart';
import 'package:SolidCheck/shared/utils/responsive.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SidebarItem {
  final String label;
  final IconData icon;
  final String route;
  final int index;
  final bool isActive;

  SidebarItem({
    required this.label,
    required this.icon,
    required this.route,
    required this.index,
    this.isActive = false,
  });
}

final sidebarSelectedIndexProvider = StateProvider<int>(
  (ref) => 0,
);

class ApplicantSidebar extends ConsumerWidget {
  final String? applicantId;
  final bool showBackButton;
  final VoidCallback? onBackPressed;

  const ApplicantSidebar({
    super.key,
    this.applicantId,
    this.showBackButton = false,
    this.onBackPressed,
  });

  List<SidebarItem> _generateSidebarItems(WidgetRef ref) {
    final dashboardState = ref.watch(applicantDashboardViewModelProvider);

    final items = <SidebarItem>[];

    items.add(
      SidebarItem(
        label: 'Overview',
        icon: Icons.dashboard_outlined,
        route: '/applicant-overview',
        index: 0,
      ),
    );

    if (dashboardState.isLoading) {
      items.add(
        SidebarItem(
          label: 'Loading...',
          icon: Icons.hourglass_empty,
          route: '/loading',
          index: 1,
        ),
      );
      return items;
    }

    if (dashboardState.applicantDetails?.data.applications != null) {
      final applications = dashboardState.applicantDetails!.data.applications;

      for (int i = 0; i < applications.length; i++) {
        final app = applications[i];
        final icon = _getIconForProduct(app.product.variant, app.product.name);

        items.add(
          SidebarItem(
            label: app.product.name,
            icon: icon,
            route: '/application/${app.id}',
            index: i + 1,
          ),
        );
      }
    } else if (dashboardState.applicant?.requestedChecks != null) {
      final checks = dashboardState.applicant!.requestedChecks!;

      for (int i = 0; i < checks.length; i++) {
        final check = checks[i];
        final icon = _getIconForProduct(check.type ?? '', check.name ?? '');

        items.add(
          SidebarItem(
            label: check.name ?? 'Unknown Check',
            icon: icon,
            route: '/check/${check.id}',
            index: i + 1,
          ),
        );
      }
    } else if (dashboardState.error != null) {
      items.add(
        SidebarItem(
          label: 'Error loading data',
          icon: Icons.error_outline,
          route: '/error',
          index: 1,
        ),
      );
    } else {
      items.add(
        SidebarItem(
          label: 'No applications available',
          icon: Icons.info_outline,
          route: '/no-data',
          index: 1,
        ),
      );
    }

    return items;
  }

  IconData _getIconForProduct(String variant, String name) {
    final lowerName = name.toLowerCase();
    final lowerVariant = variant.toLowerCase();

    if (lowerVariant.contains('dbs') || lowerName.contains('dbs')) {
      return Icons.security;
    } else if (lowerName.contains('reference')) {
      return Icons.people_outline;
    } else if (lowerName.contains('right to work') ||
        lowerName.contains('eligibility')) {
      return Icons.work_outline;
    } else if (lowerName.contains('qualification') ||
        lowerName.contains('education')) {
      return Icons.school_outlined;
    } else if (lowerName.contains('id') || lowerName.contains('identity')) {
      return Icons.badge_outlined;
    } else if (lowerName.contains('disclosure')) {
      return Icons.description_outlined;
    } else if (lowerName.contains('medical') || lowerName.contains('health')) {
      return Icons.local_hospital;
    } else {
      return Icons.check_circle_outline;
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedIndex = ref.watch(sidebarSelectedIndexProvider);
    final isMobile = ResponsiveUtil.isMobile(context);

    if (isMobile) {
      return _buildMobileDrawer(context, ref, selectedIndex);
    }

    return _buildDesktopSidebar(context, ref, selectedIndex);
  }

  Widget _buildDesktopSidebar(
    BuildContext context,
    WidgetRef ref,
    int selectedIndex,
  ) {
    final sidebarItems = _generateSidebarItems(ref);

    return ClipRRect(
      borderRadius: const BorderRadius.only(
        topRight: Radius.circular(20),
        bottomRight: Radius.circular(20),
      ),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          width: 240,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white.withValues(alpha: 0.1),
                Colors.white.withValues(alpha: 0.05),
              ],
            ),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.2),
              width: 1,
            ),
            borderRadius: const BorderRadius.only(
              topRight: Radius.circular(20),
              bottomRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              if (showBackButton) _buildBackButton(context),

              Expanded(
                child: ListView.builder(
                  padding: EdgeInsets.symmetric(
                    vertical: showBackButton ? 8 : 16,
                  ),
                  itemCount: sidebarItems.length,
                  itemBuilder: (context, index) {
                    final item = sidebarItems[index];
                    final isSelected = selectedIndex == item.index;

                    return _buildSidebarItem(context, ref, item, isSelected);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMobileDrawer(
    BuildContext context,
    WidgetRef ref,
    int selectedIndex,
  ) {
    final sidebarItems = _generateSidebarItems(ref);

    return Drawer(
      child: Container(
        color: Colors.white,
        child: Column(
          children: [
            // Header with horizontal line design matching universal header
            _buildMobileDrawerHeader(context),

            // Sidebar items
            Expanded(
              child: ListView.builder(
                padding: EdgeInsets.symmetric(
                  vertical: showBackButton ? 8 : 16,
                ),
                itemCount: sidebarItems.length,
                itemBuilder: (context, index) {
                  final item = sidebarItems[index];
                  final isSelected = selectedIndex == item.index;

                  return _buildSidebarItem(
                    context,
                    ref,
                    item,
                    isSelected,
                    isMobile: true,
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMobileDrawerHeader(BuildContext context) {
    return Container(
      height: 56,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: SafeArea(
        bottom: false,
        child: Container(
          height: 56,
          padding: const EdgeInsets.symmetric(horizontal: 4.0),
          child: Row(
            children: [
              // Close drawer button
              IconButton(
                icon: Icon(
                  Icons.close,
                  color: AppColors.kBlueColor,
                  size: 24,
                ),
                onPressed: () => Navigator.of(context).pop(),
                padding: const EdgeInsets.all(16),
                constraints: const BoxConstraints(
                  minWidth: 48,
                  minHeight: 48,
                ),
                splashRadius: 24,
              ),

              // Logo centered
              Expanded(
                child: Center(
                  child: Icon(
                    Icons.all_inclusive,
                    color: AppColors.kBlueColor,
                    size: 32,
                  ),
                ),
              ),

              // Back button if needed
              if (showBackButton)
                IconButton(
                  icon: Icon(
                    Icons.arrow_back,
                    color: AppColors.kBlueColor,
                    size: 24,
                  ),
                  onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
                  padding: const EdgeInsets.all(16),
                  constraints: const BoxConstraints(
                    minWidth: 48,
                    minHeight: 48,
                  ),
                  splashRadius: 24,
                )
              else
                const SizedBox(width: 8),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBackButton(BuildContext context, {bool isMobile = false}) {
    return Container(
      margin: const EdgeInsets.all(16),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.kBlueColor.withValues(alpha: 0.8),
                  AppColors.kBlueDarkColor.withValues(alpha: 0.7),
                ],
              ),
              border: Border.all(
                color: AppColors.kBlueColor.withValues(alpha: 0.5),
                width: 1,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(12),
                onTap: () {
                  if (isMobile) {
                    Navigator.of(context).pop();
                  }
                  if (onBackPressed != null) {
                    onBackPressed!();
                  }
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.arrow_back, color: Colors.white, size: 20),
                      SizedBox(width: 12),
                      Text(
                        'Back to Dashboard',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          shadows: [
                            Shadow(
                              offset: Offset(0, 1),
                              blurRadius: 2,
                              color: Colors.black26,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSidebarItem(
    BuildContext context,
    WidgetRef ref,
    SidebarItem item,
    bool isSelected, {
    bool isMobile = false,
  }) {
    if (isMobile) {
      // Clean mobile drawer item style
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(8),
            onTap: () {
              ref.read(sidebarSelectedIndexProvider.notifier).state = item.index;
              Navigator.of(context).pop();
              _handleNavigation(context, ref, item);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: isSelected
                    ? AppColors.kBlueColor.withValues(alpha: 0.1)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
                border: isSelected
                    ? Border.all(color: AppColors.kBlueColor.withValues(alpha: 0.3))
                    : null,
              ),
              child: Row(
                children: [
                  Icon(
                    item.icon,
                    color: isSelected ? AppColors.kBlueColor : Colors.grey[600],
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      item.label,
                      style: TextStyle(
                        color: isSelected ? AppColors.kBlueColor : Colors.grey[800],
                        fontSize: 14,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    }

    // Desktop sidebar item style (glassmorphism)
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
          child: Container(
            decoration: BoxDecoration(
              gradient: isSelected
                  ? LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppColors.kBlueColor.withValues(
                          alpha: 0.9,
                        ),
                        AppColors.kBlueDarkColor.withValues(alpha: 0.8),
                      ],
                    )
                  : LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white.withValues(alpha: 0.15),
                        Colors.white.withValues(alpha: 0.08),
                      ],
                    ),
              border: Border.all(
                color: isSelected
                    ? AppColors.kBlueColor.withValues(alpha: 0.6)
                    : Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(12),
                onTap: () {
                  ref.read(sidebarSelectedIndexProvider.notifier).state =
                      item.index;

                  if (isMobile) {
                    Navigator.of(context).pop();
                  }

                  _handleNavigation(context, ref, item);
                },
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  child: Row(
                    children: [
                      Icon(
                        item.icon,
                        color: isSelected ? Colors.white : AppColors.kBlueColor,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          item.label,
                          style: TextStyle(
                            color: isSelected ? Colors.white : Colors.black,
                            fontSize: 14,
                            fontWeight: isSelected
                                ? FontWeight.w600
                                : FontWeight.w500,
                            shadows: isSelected
                                ? null
                                : [
                                    Shadow(
                                      offset: const Offset(0, 1),
                                      blurRadius: 1,
                                      color: Colors.white.withValues(
                                        alpha: 0.5,
                                      ),
                                    ),
                                  ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _handleNavigation(
    BuildContext context,
    WidgetRef ref,
    SidebarItem item,
  ) {
    if (item.index == 0) {
      _navigateToOverview(context);
      return;
    }

    if (item.route == '/loading' ||
        item.route == '/error' ||
        item.route == '/no-data') {
      return;
    }

    if (item.route.startsWith('/application/')) {
      _handleApplicationNavigation(context, ref, item);
      return;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Navigate to ${item.label}'),
        duration: const Duration(seconds: 2),
        backgroundColor: AppColors.kBlueColor,
      ),
    );
  }

  void _navigateToOverview(BuildContext context) {
    final currentRoute = ModalRoute.of(context)?.settings.name;

    if (currentRoute == '/applicant-dashboard' ||
        currentRoute == '/applicant-detail') {
      return;
    }

    if (applicantId != null) {
      Navigator.of(context).pushNamedAndRemoveUntil(
        '/applicant-detail',
        (route) => route.settings.name == '/dashboard',
        arguments: {'applicantId': applicantId},
      );
    } else {
      final routeArgs =
          ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
      final contextApplicantId = routeArgs?['applicantId'] as String?;

      if (contextApplicantId != null) {
        Navigator.of(context).pushNamedAndRemoveUntil(
          '/applicant-detail',
          (route) => route.settings.name == '/dashboard',
          arguments: {'applicantId': contextApplicantId},
        );
      } else {
        Navigator.of(
          context,
        ).pushNamedAndRemoveUntil('/applicant-dashboard', (route) => false);
      }
    }
  }

  void _handleApplicationNavigation(
    BuildContext context,
    WidgetRef ref,
    SidebarItem item,
  ) {
    final dashboardState = ref.read(applicantDashboardViewModelProvider);

    if (dashboardState.applicantDetails?.data.applications != null) {
      final applications = dashboardState.applicantDetails!.data.applications;

      for (final app in applications) {
        if (app.product.name == item.label) {
          if (app.product.variant.toUpperCase() == 'DBS') {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => DBSApplicationFormScreen(
                  applicantId: applicantId,
                  productCode: app.product.code,
                  productName: app.product.name,
                ),
              ),
            );
            return;
          }
        }
      }
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${item.label} form coming soon'),
        duration: const Duration(seconds: 2),
        backgroundColor: AppColors.kBlueColor,
      ),
    );
  }
}
