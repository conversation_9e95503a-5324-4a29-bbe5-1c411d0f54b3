import 'package:flutter_riverpod/flutter_riverpod.dart';

final globalSelectedIndexProvider = StateProvider<int>((ref) => 0);

final navigationHistoryProvider = StateProvider<List<String>>((ref) => []);

final currentRouteProvider = StateProvider<String>((ref) => '/dashboard');

final userTypeProvider = StateProvider<String>((ref) => 'client');

final navbarVisibilityProvider = StateProvider<bool>((ref) => true);

final drawerStateProvider = StateProvider<bool>((ref) => false);
