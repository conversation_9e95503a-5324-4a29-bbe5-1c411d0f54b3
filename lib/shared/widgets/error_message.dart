import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/shared/utils/responsive.dart';
import 'package:flutter/material.dart';

class ErrorMessageWidget extends StatefulWidget {
  const ErrorMessageWidget({
    super.key,
    required this.errorMessage,
    required this.onClose,
  });

  final String? errorMessage;
  final VoidCallback onClose;

  @override
  State<ErrorMessageWidget> createState() => _ErrorMessageWidgetState();
}

class _ErrorMessageWidgetState extends State<ErrorMessageWidget> {
  @override
  Widget build(BuildContext context) {
    final isMobile = ResponsiveUtil.isMobile(context) ||
        ResponsiveUtil.isMobileLarge(context);
    final double padding = isMobile ? 10 : 30;
    return widget.errorMessage != null
        ? Padding(
            padding: EdgeInsets.symmetric(horizontal: padding, vertical: 8.0),
            child: Container(
              width: 283,
              decoration: BoxDecoration(
                color: AppColors.kRedShadeBoxColor,
                borderRadius: BorderRadius.circular(8.0),
              ),
              padding: const EdgeInsets.all(8.0),
              child: Row(
                children: [
                  Icon(Icons.warning_amber_outlined,
                      color: AppColors.kRedTextColor),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Text(
                      widget.errorMessage!,
                      style: TextStyle(
                          color: AppColors.kRedTextColor, fontSize: 12),
                    ),
                  ),
                  // IconButton(
                  //   icon: Icon(Icons.close_outlined,
                  //       color: AppColors.kRedTextColor),
                  //   onPressed: widget.onClose,
                  // ),
                ],
              ),
            ),
          )
        : const SizedBox.shrink();
  }
}
