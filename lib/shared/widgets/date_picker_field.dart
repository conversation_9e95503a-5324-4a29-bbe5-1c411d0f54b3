import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/shared/widgets/custom_check_box.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class DatePickerField extends StatefulWidget {
  const DatePickerField({
    super.key,
    required this.dayController,
    required this.monthController,
    required this.yearController,
    required this.prefixIcon,
    required this.title,
    required this.isFieldRequired,
    required this.dayValidator,
    required this.monthValidator,
    required this.yearValidator,
    this.isCheckBoxRequired = false,
    this.checkBoxTitle,
    this.isChecked = false,
    this.shouldValidatePastDate = false,
    this.shouldValidateFutureDate = false,
    this.onCheckedChanged,
  });

  final String? Function(String?)? dayValidator;
  final String? Function(String?)? monthValidator;
  final String? Function(String?)? yearValidator;
  final String? checkBoxTitle;
  final TextEditingController dayController;
  final bool isCheckBoxRequired;
  final bool isChecked;
  final bool isFieldRequired;
  final TextEditingController monthController;
  final IconData prefixIcon;
  final String title;
  final TextEditingController yearController;
  final bool shouldValidatePastDate;
  final bool shouldValidateFutureDate;
  final void Function(bool)? onCheckedChanged;

  @override
  DatePickerFieldState createState() => DatePickerFieldState();
}

class DatePickerFieldState extends State<DatePickerField> {
  OverlayEntry? _overlayEntry;
  late bool isChecked;

  @override
  void initState() {
    super.initState();
    isChecked = widget.isChecked;
  }

  void _showCalendarDropdown(BuildContext context) {
    if (_overlayEntry != null) return;

    _overlayEntry = OverlayEntry(
      builder: (context) => GestureDetector(
        onTap: _hideCalendarDropdown,
        behavior: HitTestBehavior.translucent,
        child: Stack(
          children: [
            Positioned.fill(
              child: Container(color: Colors.transparent),
            ),
            Center(
              child: Material(
                elevation: 4.0,
                child: Container(
                  height: 300.0,
                  width: 350.0,
                  padding: const EdgeInsets.all(10.0),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(5.0),
                    border: Border.all(color: AppColors.kBlueColor),
                  ),
                  child: CalendarDatePicker(
                    initialDate: DateTime.now(),
                    firstDate: widget.shouldValidateFutureDate
                        ? DateTime.now()
                        : DateTime(1900),
                    lastDate: widget.shouldValidatePastDate
                        ? DateTime.now()
                        : DateTime(2100),
                    onDateChanged: (pickedDate) {
                      widget.dayController.text =
                          pickedDate.day.toString().padLeft(2, '0');
                      widget.monthController.text =
                          pickedDate.month.toString().padLeft(2, '0');
                      widget.yearController.text = pickedDate.year.toString();
                      _hideCalendarDropdown();
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideCalendarDropdown() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  Widget _buildDateInputField({
    required TextEditingController controller,
    required String hintText,
    required String? Function(String?)? validator,
  }) {
    final responsive = context.isDesktop;
    return SizedBox(
      width: responsive ? 70.0 : 60.0,
      child: Column(
        children: [
          TextFormField(
            controller: controller,
            keyboardType: TextInputType.number,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: const BorderRadius.all(Radius.circular(8.0)),
                borderSide: BorderSide(color: AppColors.kBlueColor),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: const BorderRadius.all(Radius.circular(8.0)),
                borderSide: BorderSide(color: AppColors.kRedColor, width: 1.5),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: const BorderRadius.all(Radius.circular(8.0)),
                borderSide: BorderSide(color: AppColors.kBlueColor, width: 1.5),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: const BorderRadius.all(Radius.circular(8.0)),
                borderSide: BorderSide(color: AppColors.kRedColor, width: 1.5),
              ),
              hintText: hintText,
              contentPadding:
                  const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
              alignLabelWithHint: true,
            ),
            validator: (value) {
              if (widget.isCheckBoxRequired && isChecked) return null;
              return validator?.call(value);
            },
          ),
          const SizedBox(height: 16.0),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final responsive = context.isDesktop;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      spacing: 10,
      children: [
        Text(
          widget.title,
          style: TextStyle(
            color: widget.isFieldRequired
                ? AppColors.orangeColor
                : AppColors.kBlackColor,
          ),
        ),
        Center(
          child: Row(
            spacing: responsive ? 10 : 5,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDateInputField(
                controller: widget.dayController,
                hintText: 'DD',
                validator: widget.dayValidator,
              ),
              _buildDateInputField(
                controller: widget.monthController,
                hintText: 'MM',
                validator: widget.monthValidator,
              ),
              _buildDateInputField(
                controller: widget.yearController,
                hintText: 'YYYY',
                validator: widget.yearValidator,
              ),
              InkWell(
                onTap: () => _showCalendarDropdown(context),
                child: Container(
                  decoration: BoxDecoration(
                    color: const Color(0XFFDAD9D9),
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      spacing: responsive ? 10 : 5,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.calendar_month,
                          size: 20.0,
                          color: AppColors.kQuestionTextColor,
                        ),
                        Container(
                          height: 24,
                          width: 1.5,
                          color: AppColors.kQuestionTextColor,
                        ),
                        if (MediaQuery.of(context).size.width >= 426)
                          Text(
                            'Pick a date',
                            style: TextStyle(
                              color: AppColors.kQuestionTextColor,
                              fontSize: 14.0,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(
          child: widget.isCheckBoxRequired
              ? CustomCheckBoxMain(
                  value: widget.isChecked,
                  onChanged: (value) {
                    setState(() {
                      isChecked = value!;
                    });
                    if (widget.onCheckedChanged != null) {
                      widget.onCheckedChanged!(value!);
                    }
                  },
                  title: widget.checkBoxTitle ?? '',
                )
              : const SizedBox(),
        ),
      ],
    );
  }
}
