import 'package:flutter/material.dart';

class SidebarClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    path.moveTo(size.width, 0);
    path.quadraticBezierTo(size.width, 15, size.width - 15, 15);

    path.lineTo(0, 15);

    path.lineTo(0, size.height - 15);

    path.lineTo(size.width - 15, size.height - 15);

    path.quadraticBezierTo(
      size.width,
      size.height - 15,
      size.width,
      size.height,
    );

    path.close();
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return false;
  }
}
