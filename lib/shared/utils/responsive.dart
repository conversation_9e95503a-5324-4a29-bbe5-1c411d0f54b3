import 'package:flutter/material.dart';

class ResponsiveUtil extends StatefulWidget {
  const ResponsiveUtil(
      {super.key,
      required this.mobile,
      this.mobileLarge,
      this.tablet,
      required this.desktop,
      required this.web});

  final Widget desktop;
  final Widget mobile;
  final Widget? mobileLarge;
  final Widget? tablet;
  final Widget web;

  static bool isMobileS(BuildContext context) {
    final double width = MediaQuery.of(context).size.width;
    return width <= 325;
  }

  static bool isMobile(BuildContext context) {
    final double width = MediaQuery.of(context).size.width;
    return width <= 450;
  }

  static bool isMobileLarge(BuildContext context) {
    final double width = MediaQuery.of(context).size.width;
    return width > 450 && width <= 800;
  }

  static bool isTablet(BuildContext context) {
    final double width = MediaQuery.of(context).size.width;
    return width > 800 && width <= 1920;
  }

  static bool isMobileSmall(BuildContext context) {
    final double width = MediaQuery.of(context).size.width;
    return width <= 425;
  }

  static bool isDesktop(BuildContext context) {
    final double width = MediaQuery.of(context).size.width;
    return width > 1920;
  }

  static bool isWeb(BuildContext context) {
    final double width = MediaQuery.of(context).size.width;
    return width >= 1921;
  }

  @override
  State<ResponsiveUtil> createState() => _ResponsiveUtilState();
}

class _ResponsiveUtilState extends State<ResponsiveUtil> {
  @override
  Widget build(BuildContext context) {
    final double width = MediaQuery.of(context).size.width;

    if (width >= 1921) {
      return widget.web;
    } else if (width > 1920) {
      return widget.desktop;
    } else if (width > 800) {
      return widget.tablet ?? widget.desktop;
    } else if (width > 450) {
      return widget.mobileLarge ?? widget.tablet ?? widget.desktop;
    } else {
      return widget.mobile;
    }
  }
}
