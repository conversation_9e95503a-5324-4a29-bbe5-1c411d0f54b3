import 'package:SolidCheck/shared/data/country_data.dart';

class DBSValidationRules {
  static String personalNamesPattern =
      r"^([A-Z]+)|([A-Z][A-Z &'-]*[A-Z])$";
  static String addressPattern =
      r"^([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])$";
  static String birthNationalityPattern =
      r"^([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])$";
  static String contactNumberPattern =
      r'^([A-Z0-9()/\-&]+)|([A-Z0-9()/\-&][A-Z0-9()/\-&]*[A-Z0-9()/\-&])$';
  static String postcodePattern = r"^([A-Z0-9()/\-']+)$";
  static String niNumberPattern = r'^[A-Z]{2}[0-9]{6}[ABCD]$';
  static String documentPattern =
      r"^([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])$";
  static String ukPostcodePattern =
      r'^[A-Z]{1,2}[0-9][A-Z0-9]?\s?[0-9][A-Z]{2}$';

  static List<String> validTitles = [
    'MR',
    'MRS',
    'MISS',
    'MS',
    'DR',
    'PROF',
    'REV',
    'SIR',
    'LADY',
    'LORD',
  ];
  static List<String> validGenders = ['male', 'female'];
  static String? validateTitle(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Title is required';
    }
    if (!validTitles.contains(value.toUpperCase())) {
      return 'Please select a valid title';
    }
    return null;
  }

  static String? validateForename(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Forename is required';
    }
    final trimmed = value.trim();
    if (trimmed.length > 60) {
      return 'Forename must not exceed 60 characters';
    }
    if (!RegExp(personalNamesPattern).hasMatch(trimmed.toUpperCase())) {
      return 'Forename contains invalid characters';
    }
    return null;
  }

  static String? validateMiddleName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null;
    }
    final trimmed = value.trim();
    if (trimmed.length > 60) {
      return 'Middle name must not exceed 60 characters';
    }
    if (!RegExp(personalNamesPattern).hasMatch(trimmed.toUpperCase())) {
      return 'Middle name contains invalid characters';
    }
    return null;
  }

  static String? validatePresentSurname(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Present surname is required';
    }
    final trimmed = value.trim();
    if (trimmed.length > 60) {
      return 'Present surname must not exceed 60 characters';
    }
    if (!RegExp(personalNamesPattern).hasMatch(trimmed.toUpperCase())) {
      return 'Present surname contains invalid characters';
    }
    return null;
  }

  static String? validateDateOfBirth(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Date of birth is required';
    }

    try {
      final date = DateTime.parse(value);
      final now = DateTime.now();

      if (date.isAfter(now)) {
        return 'Date of birth cannot be in the future';
      }

      final age = now.difference(date).inDays / 365.25;
      if (age < 16) {
        return 'Applicant must be at least 16 years old';
      }
      if (age > 110) {
        return 'Applicant age cannot exceed 110 years';
      }

      return null;
    } catch (e) {
      return 'Please enter a valid date in YYYY-MM-DD format';
    }
  }

  static String? validateGender(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Gender is required';
    }

    final validGenders = ['male', 'female'];
    if (!validGenders.contains(value.toLowerCase())) {
      return 'Please select a valid gender';
    }

    return null;
  }

  static String? validateNINumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null;
    }

    final trimmed = value.trim().toUpperCase();
    if (trimmed.length > 9) {
      return 'NI Number must not exceed 9 characters';
    }

    final pattern = RegExp(r'^[A-Z]{2}[0-9]{6}[ABCD]$');
    if (!pattern.hasMatch(trimmed)) {
      return 'Please enter a valid NI Number format (e.g. *********)';
    }

    return null;
  }

  static String? validateBirthSurname(
    String? value,
    String? gender,
    String? title,
  ) {
    final isFemale = gender?.toLowerCase() == 'female';
    final isNotMiss = title?.toUpperCase() != 'MISS';

    if (isFemale && isNotMiss) {
      if (value == null || value.trim().isEmpty) {
        return 'Birth surname is required for females with titles other than Miss';
      }
    }

    if (value != null && value.trim().isNotEmpty) {
      final trimmed = value.trim();
      if (trimmed.length > 60) {
        return 'Birth surname must not exceed 60 characters';
      }

      if (trimmed != trimmed.toUpperCase()) {
        return 'Birth surname must be in uppercase';
      }

      final pattern = RegExp(r"^([A-Z]+)|([A-Z][A-Z &'-]*[A-Z])$");
      if (!pattern.hasMatch(trimmed)) {
        return 'Birth surname contains invalid characters. Only A-Z, space, &, \', - are allowed';
      }
    }

    return null;
  }

  static String? validateBirthSurnameUntil(String? value, String? dateOfBirth) {
    if (value == null || value.trim().isEmpty) {
      return null;
    }

    try {
      final year = int.parse(value.trim());
      final currentYear = DateTime.now().year;

      if (year > currentYear) {
        return 'Birth surname until year cannot be in the future';
      }

      if (dateOfBirth != null && dateOfBirth.isNotEmpty) {
        final birthDate = DateTime.parse(dateOfBirth);
        if (year < birthDate.year) {
          return 'Birth surname until year cannot be before birth year';
        }
      }

      return null;
    } catch (e) {
      return 'Please enter a valid year (YYYY)';
    }
  }

  static String? validateBirthTown(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Birth town is required';
    }

    final trimmed = value.trim();
    if (trimmed.length > 30) {
      return 'Birth town must not exceed 30 characters';
    }

    if (trimmed != trimmed.toUpperCase()) {
      return 'Birth town must be in uppercase';
    }

    final pattern = RegExp(
      r"^([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])$",
    );
    if (!pattern.hasMatch(trimmed)) {
      return 'Birth town contains invalid characters. Only A-Z, 0-9, (), /, -, \', & are allowed';
    }

    return null;
  }

  static String? validateBirthCounty(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null;
    }

    final trimmed = value.trim();
    if (trimmed.length > 30) {
      return 'Birth county must not exceed 30 characters';
    }

    if (trimmed != trimmed.toUpperCase()) {
      return 'Birth county must be in uppercase';
    }

    final pattern = RegExp(
      r"^([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])$",
    );
    if (!pattern.hasMatch(trimmed)) {
      return 'Birth county contains invalid characters. Only A-Z, 0-9, (), /, -, \', & are allowed';
    }

    return null;
  }

  static String? validateBirthCountry(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Birth country is required';
    }

    if (!Countries.isValidIsoCode(value.trim())) {
      return 'Please select a valid country';
    }

    return null;
  }

  static String? validateBirthNationality(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Birth nationality is required';
    }

    final trimmed = value.trim();
    if (trimmed.length > 30) {
      return 'Birth nationality must not exceed 30 characters';
    }

    if (trimmed != trimmed.toUpperCase()) {
      return 'Birth nationality must be in uppercase';
    }

    final pattern = RegExp(
      r"^([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])$",
    );
    if (!pattern.hasMatch(trimmed)) {
      return 'Birth nationality contains invalid characters. Only A-Z, 0-9, (), /, -, \', & are allowed';
    }

    return null;
  }

  static String? validateContactNumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Contact number is required';
    }

    final trimmed = value.trim();
    if (trimmed.length > 30) {
      return 'Contact number must not exceed 30 characters';
    }

    if (trimmed != trimmed.toUpperCase()) {
      return 'Contact number must be in uppercase';
    }

    final pattern = RegExp(
      r"^([A-Z0-9()/\-&]+)|([A-Z0-9()/\-&][A-Z0-9()/\-&]*[A-Z0-9()/\-&])$",
    );
    if (!pattern.hasMatch(trimmed)) {
      return 'Contact number contains invalid characters. Only A-Z, 0-9, (), /, -, & are allowed';
    }

    return null;
  }

  static String? validateAddressLine1(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Address line 1 is required';
    }

    final trimmed = value.trim();
    if (trimmed.length > 60) {
      return 'Address line 1 must not exceed 60 characters';
    }

    if (trimmed != trimmed.toUpperCase()) {
      return 'Address line 1 must be in uppercase';
    }

    final pattern = RegExp(
      r"^([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])$",
    );
    if (!pattern.hasMatch(trimmed)) {
      return 'Address line 1 contains invalid characters. Only A-Z, 0-9, (), /, -, \', & are allowed';
    }

    return null;
  }

  static String? validateAddressLine2(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null;
    }

    final trimmed = value.trim();
    if (trimmed.length > 60) {
      return 'Address line 2 must not exceed 60 characters';
    }

    if (trimmed != trimmed.toUpperCase()) {
      return 'Address line 2 must be in uppercase';
    }

    final pattern = RegExp(
      r"^([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])$",
    );
    if (!pattern.hasMatch(trimmed)) {
      return 'Address line 2 contains invalid characters. Only A-Z, 0-9, (), /, -, \', & are allowed';
    }

    return null;
  }

  static String? validateAddressTown(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Town/City is required';
    }

    final trimmed = value.trim();
    if (trimmed.length > 30) {
      return 'Town/City must not exceed 30 characters';
    }

    if (trimmed != trimmed.toUpperCase()) {
      return 'Town/City must be in uppercase';
    }

    final pattern = RegExp(
      r"^([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])$",
    );
    if (!pattern.hasMatch(trimmed)) {
      return 'Town/City contains invalid characters. Only A-Z, 0-9, (), /, -, \', & are allowed';
    }

    return null;
  }

  static String? validateAddressCounty(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null;
    }

    final trimmed = value.trim();
    if (trimmed.length > 30) {
      return 'County must not exceed 30 characters';
    }

    if (trimmed != trimmed.toUpperCase()) {
      return 'County must be in uppercase';
    }

    final pattern = RegExp(
      r"^([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])$",
    );
    if (!pattern.hasMatch(trimmed)) {
      return 'County contains invalid characters. Only A-Z, 0-9, (), /, -, \', & are allowed';
    }

    return null;
  }

  static String? validatePostcode(String? value, String? countryCode) {
    final isGB = countryCode?.toUpperCase() == 'GB';

    if (isGB && (value == null || value.trim().isEmpty)) {
      return 'Postcode is required for UK addresses';
    }

    if (value != null && value.trim().isNotEmpty) {
      final trimmed = value.trim();
      if (trimmed.length > 30) {
        return 'Postcode must not exceed 30 characters';
      }

      final pattern = RegExp(r"^([A-Z0-9()/\-']+)$");
      if (!pattern.hasMatch(trimmed.toUpperCase())) {
        return 'Postcode contains invalid characters. Only A-Z, 0-9, (), /, -, \' are allowed';
      }

      if (isGB) {
        final ukPattern = RegExp(
          r'^[A-Z]{1,2}[0-9][A-Z0-9]?\s?[0-9][A-Z]{2}$',
          caseSensitive: false,
        );
        if (!ukPattern.hasMatch(trimmed)) {
          return 'Please enter a valid UK postcode format';
        }
      }
    }

    return null;
  }

  static String? validateCountryCode(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Country is required';
    }

    if (!Countries.isValidIsoCode(value.trim())) {
      return 'Please select a valid country';
    }

    return null;
  }

  static String? validateResidentFromYearMonth(
    String? value,
    String? dateOfBirth,
  ) {
    if (value == null || value.trim().isEmpty) {
      return 'Resident from date is required';
    }

    try {
      final parts = value.split('-');
      if (parts.length != 2) {
        return 'Please enter date in YYYY-MM format';
      }

      final year = int.parse(parts[0]);
      final month = int.parse(parts[1]);

      if (month < 1 || month > 12) {
        return 'Month must be between 1 and 12';
      }

      final date = DateTime(year, month, 1);
      final now = DateTime.now();

      if (date.isAfter(now)) {
        return 'Resident from date cannot be in the future';
      }

      if (year < 1900 || year > 2099) {
        return 'Year must be between 1900 and 2099';
      }

      if (dateOfBirth != null && dateOfBirth.isNotEmpty) {
        final birthDate = DateTime.parse(dateOfBirth);
        if (date.isBefore(DateTime(birthDate.year, birthDate.month, 1))) {
          return 'Resident from date cannot be before birth date';
        }
      }

      return null;
    } catch (e) {
      return 'Please enter a valid date in YYYY-MM format';
    }
  }

  static String? validateResidentToYearMonth(
    String? value,
    String? fromDate,
    String? dateOfBirth,
  ) {
    if (value == null || value.trim().isEmpty) {
      return null;
    }

    try {
      final parts = value.split('-');
      if (parts.length != 2) {
        return 'Please enter date in YYYY-MM format';
      }

      final year = int.parse(parts[0]);
      final month = int.parse(parts[1]);

      if (month < 1 || month > 12) {
        return 'Month must be between 1 and 12';
      }

      final date = DateTime(year, month, 1);
      final now = DateTime.now();

      if (date.isAfter(now)) {
        return 'Resident to date cannot be in the future';
      }

      if (dateOfBirth != null && dateOfBirth.isNotEmpty) {
        final birthDate = DateTime.parse(dateOfBirth);
        if (date.isBefore(DateTime(birthDate.year, birthDate.month, 1))) {
          return 'Resident to date cannot be before birth date';
        }
      }

      if (fromDate != null && fromDate.isNotEmpty) {
        final fromParts = fromDate.split('-');
        if (fromParts.length == 2) {
          final fromYear = int.parse(fromParts[0]);
          final fromMonth = int.parse(fromParts[1]);
          final fromDateTime = DateTime(fromYear, fromMonth, 1);

          if (date.isBefore(fromDateTime)) {
            return 'Resident to date must be same as or later than from date';
          }
        }
      }

      return null;
    } catch (e) {
      return 'Please enter a valid date in YYYY-MM format';
    }
  }

  static String? validatePassportNumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Passport number is required';
    }

    final trimmed = value.trim();
    if (trimmed.length > 11) {
      return 'Passport number must not exceed 11 characters';
    }

    final pattern = RegExp(
      r"^([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])$",
    );
    if (!pattern.hasMatch(trimmed.toUpperCase())) {
      return 'Passport number contains invalid characters. Only A-Z, 0-9, (), /, -, \', & are allowed';
    }

    return null;
  }

  static String? validatePassportDOB(String? value, String? applicantDOB) {
    if (value == null || value.trim().isEmpty) {
      return 'Passport date of birth is required';
    }

    try {
      final passportDate = DateTime.parse(value);
      final now = DateTime.now();

      if (passportDate.isAfter(now)) {
        return 'Passport date of birth cannot be in the future';
      }

      if (applicantDOB != null && applicantDOB.isNotEmpty) {
        final applicantDate = DateTime.parse(applicantDOB);
        if (!_isSameDate(passportDate, applicantDate)) {
          return 'Passport date of birth must match your birth details date of birth';
        }
      }

      return null;
    } catch (e) {
      return 'Please enter a valid date in YYYY-MM-DD format';
    }
  }

  static String? validatePassportNationality(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Passport nationality is required';
    }

    final trimmed = value.trim();
    if (trimmed.length > 30) {
      return 'Passport nationality must not exceed 30 characters';
    }

    if (trimmed != trimmed.toUpperCase()) {
      return 'Passport nationality must be in uppercase';
    }

    final pattern = RegExp(
      r"^([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])$",
    );
    if (!pattern.hasMatch(trimmed)) {
      return 'Passport nationality contains invalid characters. Only A-Z, 0-9, (), /, -, \', & are allowed';
    }

    return null;
  }

  static String? validatePassportIssueDate(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Passport issue date is required';
    }

    try {
      final issueDate = DateTime.parse(value);
      final now = DateTime.now();

      if (issueDate.isAfter(now)) {
        return 'Passport issue date cannot be in the future';
      }

      return null;
    } catch (e) {
      return 'Please enter a valid date in YYYY-MM-DD format';
    }
  }

  static String? validateDriverLicenceNumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Driving licence number is required';
    }

    final trimmed = value.trim();
    if (trimmed.length > 18) {
      return 'Driving licence number must not exceed 18 characters';
    }

    final pattern = RegExp(
      r"^([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])$",
    );
    if (!pattern.hasMatch(trimmed.toUpperCase())) {
      return 'Driving licence number contains invalid characters. Only A-Z, 0-9, (), /, -, \', & are allowed';
    }

    return null;
  }

  static String? validateDriverLicenceDOB(String? value, String? applicantDOB) {
    if (value == null || value.trim().isEmpty) {
      return 'Driving licence date of birth is required';
    }

    try {
      final licenceDate = DateTime.parse(value);
      final now = DateTime.now();

      if (licenceDate.isAfter(now)) {
        return 'Driving licence date of birth cannot be in the future';
      }

      if (applicantDOB != null && applicantDOB.isNotEmpty) {
        final applicantDate = DateTime.parse(applicantDOB);
        if (!_isSameDate(licenceDate, applicantDate)) {
          return 'Driving licence date of birth must match your birth details date of birth';
        }
      }

      return null;
    } catch (e) {
      return 'Please enter a valid date in YYYY-MM-DD format';
    }
  }

  static String? validateDriverLicenceType(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Driving licence type is required';
    }

    final validTypes = ['paper', 'photo'];
    if (!validTypes.contains(value.toLowerCase())) {
      return 'Please select a valid licence type';
    }

    return null;
  }

  static String? validateDriverLicenceValidFrom(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Driving licence valid from date is required';
    }

    try {
      final validFromDate = DateTime.parse(value);
      final now = DateTime.now();

      if (validFromDate.isAfter(now)) {
        return 'Driving licence valid from date cannot be in the future';
      }

      return null;
    } catch (e) {
      return 'Please enter a valid date in YYYY-MM-DD format';
    }
  }

  static String? validateUnspentConvictions(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Please answer the convictions question';
    }

    final validValues = ['y', 'n'];
    if (!validValues.contains(value.toLowerCase())) {
      return 'Please select Yes or No';
    }

    return null;
  }

  static String? validateDeclarationByApplicant(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Declaration is required';
    }

    if (value.toLowerCase() != 'y') {
      return 'You must agree to the declaration to proceed';
    }

    return null;
  }

  static String? validateLanguagePreference(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Language preference is required';
    }

    final validLanguages = ['english', 'welsh'];
    if (!validLanguages.contains(value.toLowerCase())) {
      return 'Please select a valid language preference';
    }

    return null;
  }

  static bool _isSameDate(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }
}
