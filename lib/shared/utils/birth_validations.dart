import 'package:SolidCheck/shared/data/country_data.dart';

class BirthValidations {
  static String? validateBirthTown(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Birth town is required';
    }

    final trimmedValue = value.trim();

    if (trimmedValue.length > 30) {
      return 'Birth town must not exceed 30 characters';
    }

    final upperValue = trimmedValue.toUpperCase();

    if (trimmedValue != upperValue) {
      return 'Birth town must be in uppercase';
    }

    final pattern = RegExp(
      r"^([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])$",
    );

    if (!pattern.hasMatch(upperValue)) {
      return 'Birth town contains invalid characters. Only A-Z, 0-9, (), /, -, \', & are allowed';
    }

    return null;
  }

  static String? validateBirthCounty(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null;
    }

    final trimmedValue = value.trim();

    if (trimmedValue.length > 30) {
      return 'Birth county must not exceed 30 characters';
    }

    final upperValue = trimmedValue.toUpperCase();

    if (trimmedValue != upperValue) {
      return 'Birth county must be in uppercase';
    }

    final pattern = RegExp(
      r"^([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])$",
    );

    if (!pattern.hasMatch(upperValue)) {
      return 'Birth county contains invalid characters. Only A-Z, 0-9, (), /, -, \', & are allowed';
    }

    return null;
  }

  static String? validateBirthCountry(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Birth country is required';
    }

    final trimmedValue = value.trim();

    if (!Countries.isValidIsoCode(trimmedValue)) {
      return 'Please select a valid country';
    }

    return null;
  }

  static String? validateBirthNationality(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Birth nationality is required';
    }

    final trimmedValue = value.trim();

    if (trimmedValue.length > 30) {
      return 'Birth nationality must not exceed 30 characters';
    }

    final upperValue = trimmedValue.toUpperCase();

    if (trimmedValue != upperValue) {
      return 'Birth nationality must be in uppercase';
    }

    final pattern = RegExp(
      r"^([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])$",
    );

    if (!pattern.hasMatch(upperValue)) {
      return 'Birth nationality contains invalid characters. Only A-Z, 0-9, (), /, -, \', & are allowed';
    }

    return null;
  }

  static String formatBirthField(String input) {
    String formatted = input.toUpperCase();

    formatted = formatted.replaceAll(RegExp(r"[^A-Z0-9()/\-'&]"), '');

    if (formatted.length > 30) {
      formatted = formatted.substring(0, 30);
    }

    return formatted;
  }

  static String? validateDateOfBirth(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Date of birth is required';
    }

    final dateRegex = RegExp(r'^\d{2}/\d{2}/\d{4}$');
    if (!dateRegex.hasMatch(value)) {
      return 'Please enter date in DD/MM/YYYY format';
    }

    final parts = value.split('/');
    final day = int.tryParse(parts[0]);
    final month = int.tryParse(parts[1]);
    final year = int.tryParse(parts[2]);

    if (day == null || month == null || year == null) {
      return 'Please enter a valid date';
    }

    if (day < 1 || day > 31) {
      return 'Day must be between 1 and 31';
    }

    if (month < 1 || month > 12) {
      return 'Month must be between 1 and 12';
    }

    if (year < 1900 || year > DateTime.now().year) {
      return 'Year must be between 1900 and ${DateTime.now().year}';
    }

    try {
      final date = DateTime(year, month, day);
      if (date.day != day || date.month != month || date.year != year) {
        return 'Please enter a valid date';
      }

      if (date.isAfter(DateTime.now())) {
        return 'Date of birth cannot be in the future';
      }
    } catch (e) {
      return 'Please enter a valid date';
    }

    return null;
  }
}
