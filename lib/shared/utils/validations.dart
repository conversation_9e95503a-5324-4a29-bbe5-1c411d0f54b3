String requiredMessage = 'This field is required';
String emailPattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';

String? validateRequired(String? value) {
  if (value == null || value.isEmpty) {
    return requiredMessage;
  }
  return null;
}

String? validateEmail(String? value) {
  if (value == null || value.isEmpty) {
    return requiredMessage;
  } else if (!RegExp(emailPattern).hasMatch(value)) {
    return 'Please enter a valid email address';
  }
  return null;
}

String? validateContactNumber(String? value) {
  if (value == null || value.isEmpty) return null;

  final regex = RegExp(
    r"^([A-Z0-9\(\)\-\/&]+)|([A-Z0-9\(\)\-\/&][A-Z0-9\(\)\-\/&]*[A-Z0-9\(\)\-\/&])$",
  );

  if (value.length > 30) {
    return 'Contact number must be at most 30 characters';
  }

  if (!regex.hasMatch(value.toUpperCase())) {
    return 'Invalid contact number format';
  }

  return null;
}

String? validateAlphanumeric(String? value) {
  final alphanumericRegex = RegExp(r'^[a-zA-Z0-9]+$');
  if (value == null || value.isEmpty) {
    return requiredMessage;
  } else if (value.length < 10) {
    return 'The contact number must be at least 10 characters long';
  } else if (!alphanumericRegex.hasMatch(value)) {
    return 'Only alphanumeric characters are allowed';
  }
  return null;
}

String? validateName(String? value) {
  if (value == null || value.trim().isEmpty) {
    return 'This field is required';
  }

  final trimmedValue = value.trim();

  if (trimmedValue.length > 60) {
    return 'Maximum 60 characters allowed';
  }

  final regex = RegExp(r"^[A-Z](?:[A-Z '\-]*[A-Z])?$");

  if (!regex.hasMatch(trimmedValue)) {
    return 'Only uppercase letters are allowed.';
  }

  return null;
}

String? validateNationalInsurance(String? value) {
  final niRegex = RegExp(r'^[A-CEGHJ-PR-TW-Z]{2}\d{6}[A-D]{1}$');
  if (value == null || value.isEmpty) {
    return requiredMessage;
  } else if (!niRegex.hasMatch(value)) {
    return 'Please enter a valid UK National Insurance number';
  }
  return null;
}

String? validateDateOfBirth(String? value) {
  if (value == null || value.isEmpty) {
    return requiredMessage;
  }
  try {
    final dateOfBirth = DateTime.parse(value);
    final today = DateTime.now();
    final age = today.year - dateOfBirth.year;
    if (age < 16 || age > 110) {
      return 'Date of birth must make the applicant between 16 and 110 years old';
    }
  } catch (e) {
    return 'Please enter a valid date in DD/MM/YYYY format';
  }
  return null;
}

String? validateCustomFormat(
  String? value,
  String pattern,
  String errorMessage,
) {
  final customRegex = RegExp(pattern);
  if (value == null || !customRegex.hasMatch(value)) {
    return errorMessage;
  }
  return null;
}

String? validateDate(String? day, String? month, String? year) {
  if ([day, month, year].contains(null) ||
      [day, month, year].any((e) => e!.isEmpty) ||
      [day, month, year].any((e) => int.tryParse(e!) == null)) {
    return 'Invalid date format';
  }
  return null;
}

// DBS Enhanced Application Validation Rules
class DBSValidations {
  // Valid titles as per DBS specification
  static List<String> validTitles = [
    'Mr',
    'Mrs',
    'Miss',
    'Ms',
    'Dr',
    'Prof',
    'Rev',
    'Sir',
    'Dame',
    'Lord',
    'Lady',
  ];

  // Valid genders as per DBS specification
  static List<String> validGenders = ['male', 'female'];

  // Valid language preferences
  static List<String> validLanguagePreferences = ['english', 'welsh'];

  // Valid driver licence types
  static List<String> validDriverLicenceTypes = ['paper', 'photo'];

  /// Validates Title field
  /// Must be one of the constrained values
  static String? validateTitle(String? value) {
    if (value == null || value.isEmpty) {
      return 'Title is required';
    }
    if (!validTitles.contains(value)) {
      return 'Please select a valid title';
    }
    return null;
  }

  /// Validates Forename field
  /// Upper case (max 60 chars), Pattern: ([A-Z]+)|([A-Z][A-Z &'-]*[A-Z])
  static String? validateForename(String? value) {
    if (value == null || value.isEmpty) {
      return 'Forename is required';
    }

    final upperValue = value.toUpperCase();
    if (upperValue.length > 60) {
      return 'Forename must be 60 characters or less';
    }

    // DBS Pattern: ([A-Z]+)|([A-Z][A-Z &'-]*[A-Z])
    final namePattern = RegExp(r"^([A-Z]+)|([A-Z][A-Z &'-]*[A-Z])$");
    if (!namePattern.hasMatch(upperValue)) {
      return 'Invalid forename format. Only letters, spaces, apostrophes, and hyphens are allowed';
    }

    return null;
  }

  /// Validates Middle Name field
  /// Upper case (max 60 chars), min length 1, Pattern: ([A-Z]+)|([A-Z][A-Z &'-]*[A-Z])
  static String? validateMiddleName(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Middle name is optional
    }

    final trimmedValue = value.trim();
    if (trimmedValue.isEmpty) {
      return null; // Empty after trim is considered optional
    }

    final upperValue = trimmedValue.toUpperCase();

    // Check max length
    if (upperValue.length > 60) {
      return 'Middle name must be 60 characters or less';
    }

    // Check min length
    if (upperValue.isEmpty) {
      return 'Middle name must be at least 1 character';
    }

    // DBS Pattern: ([A-Z]+)|([A-Z][A-Z &'-]*[A-Z])
    final namePattern = RegExp(r"^([A-Z]+)|([A-Z][A-Z &'-]*[A-Z])$");
    if (!namePattern.hasMatch(upperValue)) {
      return 'Invalid middle name format. Only letters, spaces, apostrophes, and hyphens are allowed';
    }

    return null;
  }

  /// Validates a list of middle names (1-3 allowed)
  static String? validateMiddleNames(List<String> middleNames) {
    // Remove empty entries
    final nonEmptyNames = middleNames
        .where((name) => name.trim().isNotEmpty)
        .toList();

    if (nonEmptyNames.isEmpty) {
      return null; // No middle names is valid
    }

    if (nonEmptyNames.length > 3) {
      return 'Maximum 3 middle names are allowed';
    }

    // Validate each middle name
    for (int i = 0; i < nonEmptyNames.length; i++) {
      final validation = validateMiddleName(nonEmptyNames[i]);
      if (validation != null) {
        return 'Middle name ${i + 1}: $validation';
      }
    }

    return null;
  }

  /// Validates Present Surname field
  /// Upper case (max 60 chars), Pattern: ([A-Z]+)|([A-Z][A-Z &'-]*[A-Z])
  static String? validatePresentSurname(String? value) {
    if (value == null || value.isEmpty) {
      return 'Present surname is required';
    }

    final upperValue = value.toUpperCase();
    if (upperValue.length > 60) {
      return 'Present surname must be 60 characters or less';
    }

    // DBS Pattern: ([A-Z]+)|([A-Z][A-Z &'-]*[A-Z])
    final namePattern = RegExp(r"^([A-Z]+)|([A-Z][A-Z &'-]*[A-Z])$");
    if (!namePattern.hasMatch(upperValue)) {
      return 'Invalid surname format. Only letters, spaces, apostrophes, and hyphens are allowed';
    }

    return null;
  }

  /// Validates Date of Birth
  /// Format: YYYY-MM-DD, age must be between 16 and 110 years, not future date
  static String? validateDateOfBirth(String? day, String? month, String? year) {
    if (day == null ||
        day.isEmpty ||
        month == null ||
        month.isEmpty ||
        year == null ||
        year.isEmpty) {
      return 'Date of birth is required';
    }

    final dayInt = int.tryParse(day);
    final monthInt = int.tryParse(month);
    final yearInt = int.tryParse(year);

    if (dayInt == null || monthInt == null || yearInt == null) {
      return 'Please enter valid numbers for date';
    }

    if (dayInt < 1 || dayInt > 31) {
      return 'Day must be between 1 and 31';
    }

    if (monthInt < 1 || monthInt > 12) {
      return 'Month must be between 1 and 12';
    }

    if (yearInt < 1900 || yearInt > DateTime.now().year) {
      return 'Please enter a valid year';
    }

    try {
      final dateOfBirth = DateTime(yearInt, monthInt, dayInt);
      final now = DateTime.now();

      if (dateOfBirth.isAfter(now)) {
        return 'Date of birth cannot be in the future';
      }

      final age = now.difference(dateOfBirth).inDays ~/ 365;
      if (age < 16 || age > 110) {
        return 'Applicant age must be between 16 and 110 years';
      }
    } catch (e) {
      return 'Please enter a valid date';
    }

    return null;
  }

  /// Validates Gender field
  /// Must be 'male' or 'female'
  static String? validateGender(String? value) {
    if (value == null || value.isEmpty) {
      return 'Gender is required';
    }

    final lowerValue = value.toLowerCase();
    if (!validGenders.contains(lowerValue)) {
      return 'Gender must be either male or female';
    }

    return null;
  }

  /// Validates National Insurance Number
  /// Max 9 chars, must conform to NI Number formatting rules
  static String? validateNINumber(String? value) {
    if (value == null || value.isEmpty) {
      return null; // NI Number is optional
    }

    if (value.length > 9) {
      return 'National Insurance Number must be 9 characters or less';
    }

    // Basic NI Number pattern: 2 letters + 6 digits + 1 letter (A/B/C/D) or space
    final niPattern = RegExp(r'^[A-Z]{2}[0-9]{6}[ABCD\s]$');
    if (!niPattern.hasMatch(value.toUpperCase())) {
      return 'Invalid National Insurance Number format';
    }

    return null;
  }

  /// Validates Address Line 1
  /// Upper case (max 60 chars), Pattern: ([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])
  static String? validateAddressLine1(String? value) {
    if (value == null || value.isEmpty) {
      return 'Address Line 1 is required';
    }

    final upperValue = value.toUpperCase();
    if (upperValue.length > 60) {
      return 'Address Line 1 must be 60 characters or less';
    }

    // DBS Pattern for address fields
    final addressPattern = RegExp(
      r"^([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])$",
    );
    if (!addressPattern.hasMatch(upperValue)) {
      return 'Invalid address format';
    }

    return null;
  }

  /// Validates Address Line 2
  /// Upper case (max 60 chars), Pattern: ([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])
  static String? validateAddressLine2(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Address Line 2 is optional
    }

    final upperValue = value.toUpperCase();
    if (upperValue.length > 60) {
      return 'Address Line 2 must be 60 characters or less';
    }

    // DBS Pattern for address fields
    final addressPattern = RegExp(
      r"^([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])$",
    );
    if (!addressPattern.hasMatch(upperValue)) {
      return 'Invalid address format';
    }

    return null;
  }

  /// Validates Address Town
  /// Upper case (max 30 chars), Pattern: ([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])
  static String? validateAddressTown(String? value) {
    if (value == null || value.isEmpty) {
      return 'Town is required';
    }

    final upperValue = value.toUpperCase();
    if (upperValue.length > 30) {
      return 'Town must be 30 characters or less';
    }

    // DBS Pattern for address fields
    final addressPattern = RegExp(
      r"^([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])$",
    );
    if (!addressPattern.hasMatch(upperValue)) {
      return 'Invalid town format';
    }

    return null;
  }

  /// Validates Address County
  /// Upper case (max 30 chars), Pattern: ([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])
  static String? validateAddressCounty(String? value) {
    if (value == null || value.isEmpty) {
      return 'County is required';
    }

    final upperValue = value.toUpperCase();
    if (upperValue.length > 30) {
      return 'County must be 30 characters or less';
    }

    // DBS Pattern for address fields
    final addressPattern = RegExp(
      r"^([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])$",
    );
    if (!addressPattern.hasMatch(upperValue)) {
      return 'Invalid county format';
    }

    return null;
  }

  /// Validates Postcode
  /// Max 30 chars, Pattern: ([A-Z0-9()/\-']+), mandatory if CountryCode is "GB"
  static String? validatePostcode(String? value, String? countryCode) {
    if (countryCode?.toUpperCase() == 'GB') {
      if (value == null || value.isEmpty) {
        return 'Postcode is required for UK addresses';
      }
    }

    if (value != null && value.isNotEmpty) {
      if (value.length > 30) {
        return 'Postcode must be 30 characters or less';
      }

      // DBS Pattern for postcode
      final postcodePattern = RegExp(r"^([A-Z0-9()/\-']+)$");
      if (!postcodePattern.hasMatch(value.toUpperCase())) {
        return 'Invalid postcode format';
      }

      // UK postcode format validation if country is GB
      if (countryCode?.toUpperCase() == 'GB') {
        final ukPostcodePattern = RegExp(
          r'^[A-Z]{1,2}[0-9][A-Z0-9]?\s?[0-9][A-Z]{2}$',
        );
        if (!ukPostcodePattern.hasMatch(
          value.toUpperCase().replaceAll(' ', ''),
        )) {
          return 'Invalid UK postcode format';
        }
      }
    }

    return null;
  }

  /// Validates Country Code
  /// Must be a valid 2-letter ISO Country Code
  static String? validateCountryCode(String? value) {
    if (value == null || value.isEmpty) {
      return 'Country is required';
    }

    if (value.length != 2) {
      return 'Country code must be 2 characters';
    }

    // Basic validation for 2-letter country codes
    final countryPattern = RegExp(r'^[A-Z]{2}$');
    if (!countryPattern.hasMatch(value.toUpperCase())) {
      return 'Invalid country code format';
    }

    return null;
  }

  /// Validates Resident From Year-Month
  /// Format: YYYY-MM, must not be in the future, year must begin with '19' or '20'
  static String? validateResidentFromYearMonth(String? value) {
    if (value == null || value.isEmpty) {
      return 'Resident from date is required';
    }

    // Format: YYYY-MM
    final yearMonthPattern = RegExp(r'^\d{4}-\d{2}$');
    if (!yearMonthPattern.hasMatch(value)) {
      return 'Date format must be YYYY-MM';
    }

    try {
      final parts = value.split('-');
      final year = int.parse(parts[0]);
      final month = int.parse(parts[1]);

      if (month < 1 || month > 12) {
        return 'Month must be between 01 and 12';
      }

      if (year < 1900 || year > 2099) {
        return 'Year must begin with 19 or 20';
      }

      final date = DateTime(year, month);
      if (date.isAfter(DateTime.now())) {
        return 'Date cannot be in the future';
      }
    } catch (e) {
      return 'Invalid date format';
    }

    return null;
  }

  /// Validates Contact Number
  /// Upper case alphanumeric (max 30 chars), Pattern: ([A-Z0-9()/\-&]+)|([A-Z0-9()/\-&][A-Z0-9()/\-&]*[A-Z0-9()/\-&])
  static String? validateContactNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Contact number is required';
    }

    final upperValue = value.toUpperCase();
    if (upperValue.length > 30) {
      return 'Contact number must be 30 characters or less';
    }

    // DBS Pattern for contact number
    final contactPattern = RegExp(
      r"^([A-Z0-9()/\-&]+)|([A-Z0-9()/\-&][A-Z0-9()/\-&]*[A-Z0-9()/\-&])$",
    );
    if (!contactPattern.hasMatch(upperValue)) {
      return 'Invalid contact number format';
    }

    return null;
  }

  /// Validates Email Address
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email address is required';
    } else if (!RegExp(emailPattern).hasMatch(value)) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  /// Validates Birth Surname
  /// Mandatory if Gender is 'Female' and Title is any EXCEPT 'Miss'
  static String? validateBirthSurname(
    String? value,
    String? gender,
    String? title,
  ) {
    if (gender?.toLowerCase() == 'female' && title != 'Miss') {
      if (value == null || value.isEmpty) {
        return 'Birth surname is required for married/divorced females';
      }
    }

    if (value != null && value.isNotEmpty) {
      final upperValue = value.toUpperCase();
      if (upperValue.length > 60) {
        return 'Birth surname must be 60 characters or less';
      }

      // DBS Pattern for names
      final namePattern = RegExp(r"^([A-Z]+)|([A-Z][A-Z &'-]*[A-Z])$");
      if (!namePattern.hasMatch(upperValue)) {
        return 'Invalid birth surname format';
      }
    }

    return null;
  }

  /// Validates Language Preference
  /// Must be 'english' or 'welsh'
  static String? validateLanguagePreference(String? value) {
    if (value == null || value.isEmpty) {
      return 'Language preference is required';
    }

    final lowerValue = value.toLowerCase();
    if (!validLanguagePreferences.contains(lowerValue)) {
      return 'Language preference must be English or Welsh';
    }

    return null;
  }
}

// Legacy Validations class for backward compatibility
class Validations {
  static String? validateTitle(String? value) =>
      DBSValidations.validateTitle(value);
  static String? validateForename(String? value) =>
      DBSValidations.validateForename(value);
  static String? validateMiddlename(String? value) =>
      DBSValidations.validateForename(value);
  static String? validateSurname(String? value) =>
      DBSValidations.validatePresentSurname(value);
  static String? validateGender(String? value) =>
      DBSValidations.validateGender(value);
  static String? validateDay(String? value) {
    if (value == null || value.isEmpty) return 'Day is required';
    final day = int.tryParse(value);
    if (day == null || day < 1 || day > 31) {
      return 'Please enter a valid day (1-31)';
    }
    return null;
  }

  static String? validateMonth(String? value) {
    if (value == null || value.isEmpty) return 'Month is required';
    final month = int.tryParse(value);
    if (month == null || month < 1 || month > 12) {
      return 'Please enter a valid month (1-12)';
    }
    return null;
  }

  static String? validateYear(String? value) {
    if (value == null || value.isEmpty) return 'Year is required';
    final year = int.tryParse(value);
    if (year == null || year < 1900 || year > DateTime.now().year) {
      return 'Please enter a valid year';
    }
    return null;
  }

  static String? validateContactNumber(String? value) =>
      DBSValidations.validateContactNumber(value);
  static String? validateEmail(String? value) =>
      DBSValidations.validateEmail(value);
}
