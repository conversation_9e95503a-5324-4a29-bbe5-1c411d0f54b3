import 'package:SolidCheck/features/dbs/presentation/screens/dbs_form_screen.dart';
import 'package:SolidCheck/features/reference_check/views/screens/initial.dart';
import 'package:flutter/material.dart';

class AnimatedSideBar {
  static Widget buildDBSFormSideBarWidgetContent() {
    return const DBSFormScreen();
  }

  static Widget buildReferenceCheckSideBarWidgetContent() {
    return const ReferenceCheckInitialScreen();
  }

  static Widget buildSMCSideBarContent() {
    return const Center(child: Text('SMC Side Bar Content'));
  }

  static Widget buildIDSideBarWidgetContent() {
    return const Center(child: Text('ID SideBar Content'));
  }

  static Widget buildRightToWorkSideBarWidgetContent() {
    return const Center(child: Text('Right to Work SideBar Content'));
  }

  static Widget buildAdverseCreditCheckSideBarWidgetContent() {
    return const Center(child: Text('Adverse Check Side Bar Content'));
  }

  static Widget buildDBSCHECKSideBarWidgetContent() {
    return const Center(child: Text('DBS Check Content - TODO: Implement'));
  }
}
