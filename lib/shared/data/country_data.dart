import 'package:flutter/material.dart';

class CountryData {
  final String name;
  final String isoCode;
  final String nationality;

  CountryData({
    required this.name,
    required this.isoCode,
    required this.nationality,
  });
}

class Countries {
  static List<CountryData> all = [
    CountryData(name: 'United Kingdom', isoCode: 'GB', nationality: 'BRITISH'),
    CountryData(name: 'Afghanistan', isoCode: 'AF', nationality: 'AFGHAN'),
    CountryData(name: 'Albania', isoCode: 'AL', nationality: 'ALBANIAN'),
    CountryData(name: 'Algeria', isoCode: 'DZ', nationality: 'ALGERIAN'),
    CountryData(name: 'Andorra', isoCode: 'AD', nationality: 'ANDORRAN'),
    CountryData(name: 'Angola', isoCode: 'AO', nationality: 'ANGOL<PERSON>'),
    CountryData(name: 'Argentina', isoCode: 'AR', nationality: 'ARGENTINIAN'),
    CountryData(name: 'Armenia', isoCode: 'AM', nationality: 'ARMENIAN'),
    CountryData(name: 'Australia', isoCode: 'AU', nationality: 'AUSTRALIAN'),
    CountryData(name: 'Austria', isoCode: 'AT', nationality: 'AUST<PERSON><PERSON>'),
    CountryData(name: 'Azerbaijan', isoCode: 'AZ', nationality: 'AZERBAIJANI'),
    CountryData(name: 'Bahamas', isoCode: 'BS', nationality: 'BAHAMIAN'),
    CountryData(name: 'Bahrain', isoCode: 'BH', nationality: 'BAHRAINI'),
    CountryData(name: 'Bangladesh', isoCode: 'BD', nationality: 'BANGLADESHI'),
    CountryData(name: 'Barbados', isoCode: 'BB', nationality: 'BARBADIAN'),
    CountryData(name: 'Belarus', isoCode: 'BY', nationality: 'BELARUSIAN'),
    CountryData(name: 'Belgium', isoCode: 'BE', nationality: 'BELGIAN'),
    CountryData(name: 'Belize', isoCode: 'BZ', nationality: 'BELIZEAN'),
    CountryData(name: 'Benin', isoCode: 'BJ', nationality: 'BENINESE'),
    CountryData(name: 'Bhutan', isoCode: 'BT', nationality: 'BHUTANESE'),
    CountryData(name: 'Bolivia', isoCode: 'BO', nationality: 'BOLIVIAN'),
    CountryData(name: 'Bosnia and Herzegovina', isoCode: 'BA', nationality: 'BOSNIAN'),
    CountryData(name: 'Botswana', isoCode: 'BW', nationality: 'BOTSWANAN'),
    CountryData(name: 'Brazil', isoCode: 'BR', nationality: 'BRAZILIAN'),
    CountryData(name: 'Brunei', isoCode: 'BN', nationality: 'BRUNEIAN'),
    CountryData(name: 'Bulgaria', isoCode: 'BG', nationality: 'BULGARIAN'),
    CountryData(name: 'Burkina Faso', isoCode: 'BF', nationality: 'BURKINABE'),
    CountryData(name: 'Burundi', isoCode: 'BI', nationality: 'BURUNDIAN'),
    CountryData(name: 'Cambodia', isoCode: 'KH', nationality: 'CAMBODIAN'),
    CountryData(name: 'Cameroon', isoCode: 'CM', nationality: 'CAMEROONIAN'),
    CountryData(name: 'Canada', isoCode: 'CA', nationality: 'CANADIAN'),
    CountryData(name: 'Cape Verde', isoCode: 'CV', nationality: 'CAPE VERDEAN'),
    CountryData(name: 'Central African Republic', isoCode: 'CF', nationality: 'CENTRAL AFRICAN'),
    CountryData(name: 'Chad', isoCode: 'TD', nationality: 'CHADIAN'),
    CountryData(name: 'Chile', isoCode: 'CL', nationality: 'CHILEAN'),
    CountryData(name: 'China', isoCode: 'CN', nationality: 'CHINESE'),
    CountryData(name: 'Colombia', isoCode: 'CO', nationality: 'COLOMBIAN'),
    CountryData(name: 'Comoros', isoCode: 'KM', nationality: 'COMORAN'),
    CountryData(name: 'Congo', isoCode: 'CG', nationality: 'CONGOLESE'),
    CountryData(name: 'Costa Rica', isoCode: 'CR', nationality: 'COSTA RICAN'),
    CountryData(name: 'Croatia', isoCode: 'HR', nationality: 'CROATIAN'),
    CountryData(name: 'Cuba', isoCode: 'CU', nationality: 'CUBAN'),
    CountryData(name: 'Cyprus', isoCode: 'CY', nationality: 'CYPRIOT'),
    CountryData(name: 'Czech Republic', isoCode: 'CZ', nationality: 'CZECH'),
    CountryData(name: 'Denmark', isoCode: 'DK', nationality: 'DANISH'),
    CountryData(name: 'Djibouti', isoCode: 'DJ', nationality: 'DJIBOUTIAN'),
    CountryData(name: 'Dominica', isoCode: 'DM', nationality: 'DOMINICAN'),
    CountryData(name: 'Dominican Republic', isoCode: 'DO', nationality: 'DOMINICAN'),
    CountryData(name: 'Ecuador', isoCode: 'EC', nationality: 'ECUADORIAN'),
    CountryData(name: 'Egypt', isoCode: 'EG', nationality: 'EGYPTIAN'),
    CountryData(name: 'El Salvador', isoCode: 'SV', nationality: 'SALVADORAN'),
    CountryData(name: 'Equatorial Guinea', isoCode: 'GQ', nationality: 'EQUATORIAL GUINEAN'),
    CountryData(name: 'Eritrea', isoCode: 'ER', nationality: 'ERITREAN'),
    CountryData(name: 'Estonia', isoCode: 'EE', nationality: 'ESTONIAN'),
    CountryData(name: 'Eswatini', isoCode: 'SZ', nationality: 'SWAZI'),
    CountryData(name: 'Ethiopia', isoCode: 'ET', nationality: 'ETHIOPIAN'),
    CountryData(name: 'Fiji', isoCode: 'FJ', nationality: 'FIJIAN'),
    CountryData(name: 'Finland', isoCode: 'FI', nationality: 'FINNISH'),
    CountryData(name: 'France', isoCode: 'FR', nationality: 'FRENCH'),
    CountryData(name: 'Gabon', isoCode: 'GA', nationality: 'GABONESE'),
    CountryData(name: 'Gambia', isoCode: 'GM', nationality: 'GAMBIAN'),
    CountryData(name: 'Georgia', isoCode: 'GE', nationality: 'GEORGIAN'),
    CountryData(name: 'Germany', isoCode: 'DE', nationality: 'GERMAN'),
    CountryData(name: 'Ghana', isoCode: 'GH', nationality: 'GHANAIAN'),
    CountryData(name: 'Greece', isoCode: 'GR', nationality: 'GREEK'),
    CountryData(name: 'Grenada', isoCode: 'GD', nationality: 'GRENADIAN'),
    CountryData(name: 'Guatemala', isoCode: 'GT', nationality: 'GUATEMALAN'),
    CountryData(name: 'Guinea', isoCode: 'GN', nationality: 'GUINEAN'),
    CountryData(name: 'Guinea-Bissau', isoCode: 'GW', nationality: 'GUINEA-BISSAUAN'),
    CountryData(name: 'Guyana', isoCode: 'GY', nationality: 'GUYANESE'),
    CountryData(name: 'Haiti', isoCode: 'HT', nationality: 'HAITIAN'),
    CountryData(name: 'Honduras', isoCode: 'HN', nationality: 'HONDURAN'),
    CountryData(name: 'Hungary', isoCode: 'HU', nationality: 'HUNGARIAN'),
    CountryData(name: 'Iceland', isoCode: 'IS', nationality: 'ICELANDIC'),
    CountryData(name: 'India', isoCode: 'IN', nationality: 'INDIAN'),
    CountryData(name: 'Indonesia', isoCode: 'ID', nationality: 'INDONESIAN'),
    CountryData(name: 'Iran', isoCode: 'IR', nationality: 'IRANIAN'),
    CountryData(name: 'Iraq', isoCode: 'IQ', nationality: 'IRAQI'),
    CountryData(name: 'Ireland', isoCode: 'IE', nationality: 'IRISH'),
    CountryData(name: 'Israel', isoCode: 'IL', nationality: 'ISRAELI'),
    CountryData(name: 'Italy', isoCode: 'IT', nationality: 'ITALIAN'),
    CountryData(name: 'Jamaica', isoCode: 'JM', nationality: 'JAMAICAN'),
    CountryData(name: 'Japan', isoCode: 'JP', nationality: 'JAPANESE'),
    CountryData(name: 'Jordan', isoCode: 'JO', nationality: 'JORDANIAN'),
    CountryData(name: 'Kazakhstan', isoCode: 'KZ', nationality: 'KAZAKHSTANI'),
    CountryData(name: 'Kenya', isoCode: 'KE', nationality: 'KENYAN'),
    CountryData(name: 'Kiribati', isoCode: 'KI', nationality: 'I-KIRIBATI'),
    CountryData(name: 'Kuwait', isoCode: 'KW', nationality: 'KUWAITI'),
    CountryData(name: 'Kyrgyzstan', isoCode: 'KG', nationality: 'KYRGYZSTANI'),
    CountryData(name: 'Laos', isoCode: 'LA', nationality: 'LAOTIAN'),
    CountryData(name: 'Latvia', isoCode: 'LV', nationality: 'LATVIAN'),
    CountryData(name: 'Lebanon', isoCode: 'LB', nationality: 'LEBANESE'),
    CountryData(name: 'Lesotho', isoCode: 'LS', nationality: 'LESOTHAN'),
    CountryData(name: 'Liberia', isoCode: 'LR', nationality: 'LIBERIAN'),
    CountryData(name: 'Libya', isoCode: 'LY', nationality: 'LIBYAN'),
    CountryData(name: 'Liechtenstein', isoCode: 'LI', nationality: 'LIECHTENSTEINER'),
    CountryData(name: 'Lithuania', isoCode: 'LT', nationality: 'LITHUANIAN'),
    CountryData(name: 'Luxembourg', isoCode: 'LU', nationality: 'LUXEMBOURGISH'),
    CountryData(name: 'Madagascar', isoCode: 'MG', nationality: 'MALAGASY'),
    CountryData(name: 'Malawi', isoCode: 'MW', nationality: 'MALAWIAN'),
    CountryData(name: 'Malaysia', isoCode: 'MY', nationality: 'MALAYSIAN'),
    CountryData(name: 'Maldives', isoCode: 'MV', nationality: 'MALDIVIAN'),
    CountryData(name: 'Mali', isoCode: 'ML', nationality: 'MALIAN'),
    CountryData(name: 'Malta', isoCode: 'MT', nationality: 'MALTESE'),
    CountryData(name: 'Marshall Islands', isoCode: 'MH', nationality: 'MARSHALLESE'),
    CountryData(name: 'Mauritania', isoCode: 'MR', nationality: 'MAURITANIAN'),
    CountryData(name: 'Mauritius', isoCode: 'MU', nationality: 'MAURITIAN'),
    CountryData(name: 'Mexico', isoCode: 'MX', nationality: 'MEXICAN'),
    CountryData(name: 'Micronesia', isoCode: 'FM', nationality: 'MICRONESIAN'),
    CountryData(name: 'Moldova', isoCode: 'MD', nationality: 'MOLDOVAN'),
    CountryData(name: 'Monaco', isoCode: 'MC', nationality: 'MONEGASQUE'),
    CountryData(name: 'Mongolia', isoCode: 'MN', nationality: 'MONGOLIAN'),
    CountryData(name: 'Montenegro', isoCode: 'ME', nationality: 'MONTENEGRIN'),
    CountryData(name: 'Morocco', isoCode: 'MA', nationality: 'MOROCCAN'),
    CountryData(name: 'Mozambique', isoCode: 'MZ', nationality: 'MOZAMBICAN'),
    CountryData(name: 'Myanmar', isoCode: 'MM', nationality: 'BURMESE'),
    CountryData(name: 'Namibia', isoCode: 'NA', nationality: 'NAMIBIAN'),
    CountryData(name: 'Nauru', isoCode: 'NR', nationality: 'NAURUAN'),
    CountryData(name: 'Nepal', isoCode: 'NP', nationality: 'NEPALESE'),
    CountryData(name: 'Netherlands', isoCode: 'NL', nationality: 'DUTCH'),
    CountryData(name: 'New Zealand', isoCode: 'NZ', nationality: 'NEW ZEALANDER'),
    CountryData(name: 'Nicaragua', isoCode: 'NI', nationality: 'NICARAGUAN'),
    CountryData(name: 'Niger', isoCode: 'NE', nationality: 'NIGERIEN'),
    CountryData(name: 'Nigeria', isoCode: 'NG', nationality: 'NIGERIAN'),
    CountryData(name: 'North Korea', isoCode: 'KP', nationality: 'NORTH KOREAN'),
    CountryData(name: 'North Macedonia', isoCode: 'MK', nationality: 'MACEDONIAN'),
    CountryData(name: 'Norway', isoCode: 'NO', nationality: 'NORWEGIAN'),
    CountryData(name: 'Oman', isoCode: 'OM', nationality: 'OMANI'),
    CountryData(name: 'Pakistan', isoCode: 'PK', nationality: 'PAKISTANI'),
    CountryData(name: 'Palau', isoCode: 'PW', nationality: 'PALAUAN'),
    CountryData(name: 'Panama', isoCode: 'PA', nationality: 'PANAMANIAN'),
    CountryData(name: 'Papua New Guinea', isoCode: 'PG', nationality: 'PAPUA NEW GUINEAN'),
    CountryData(name: 'Paraguay', isoCode: 'PY', nationality: 'PARAGUAYAN'),
    CountryData(name: 'Peru', isoCode: 'PE', nationality: 'PERUVIAN'),
    CountryData(name: 'Philippines', isoCode: 'PH', nationality: 'FILIPINO'),
    CountryData(name: 'Poland', isoCode: 'PL', nationality: 'POLISH'),
    CountryData(name: 'Portugal', isoCode: 'PT', nationality: 'PORTUGUESE'),
    CountryData(name: 'Qatar', isoCode: 'QA', nationality: 'QATARI'),
    CountryData(name: 'Romania', isoCode: 'RO', nationality: 'ROMANIAN'),
    CountryData(name: 'Russia', isoCode: 'RU', nationality: 'RUSSIAN'),
    CountryData(name: 'Rwanda', isoCode: 'RW', nationality: 'RWANDAN'),
    CountryData(name: 'Saint Kitts and Nevis', isoCode: 'KN', nationality: 'KITTITIAN'),
    CountryData(name: 'Saint Lucia', isoCode: 'LC', nationality: 'SAINT LUCIAN'),
    CountryData(name: 'Saint Vincent and the Grenadines', isoCode: 'VC', nationality: 'VINCENTIAN'),
    CountryData(name: 'Samoa', isoCode: 'WS', nationality: 'SAMOAN'),
    CountryData(name: 'San Marino', isoCode: 'SM', nationality: 'SAMMARINESE'),
    CountryData(name: 'Saudi Arabia', isoCode: 'SA', nationality: 'SAUDI ARABIAN'),
    CountryData(name: 'Senegal', isoCode: 'SN', nationality: 'SENEGALESE'),
    CountryData(name: 'Serbia', isoCode: 'RS', nationality: 'SERBIAN'),
    CountryData(name: 'Seychelles', isoCode: 'SC', nationality: 'SEYCHELLOIS'),
    CountryData(name: 'Sierra Leone', isoCode: 'SL', nationality: 'SIERRA LEONEAN'),
    CountryData(name: 'Singapore', isoCode: 'SG', nationality: 'SINGAPOREAN'),
    CountryData(name: 'Slovakia', isoCode: 'SK', nationality: 'SLOVAK'),
    CountryData(name: 'Slovenia', isoCode: 'SI', nationality: 'SLOVENIAN'),
    CountryData(name: 'Solomon Islands', isoCode: 'SB', nationality: 'SOLOMON ISLANDER'),
    CountryData(name: 'Somalia', isoCode: 'SO', nationality: 'SOMALI'),
    CountryData(name: 'South Africa', isoCode: 'ZA', nationality: 'SOUTH AFRICAN'),
    CountryData(name: 'South Korea', isoCode: 'KR', nationality: 'SOUTH KOREAN'),
    CountryData(name: 'South Sudan', isoCode: 'SS', nationality: 'SOUTH SUDANESE'),
    CountryData(name: 'Spain', isoCode: 'ES', nationality: 'SPANISH'),
    CountryData(name: 'Sri Lanka', isoCode: 'LK', nationality: 'SRI LANKAN'),
    CountryData(name: 'Sudan', isoCode: 'SD', nationality: 'SUDANESE'),
    CountryData(name: 'Suriname', isoCode: 'SR', nationality: 'SURINAMESE'),
    CountryData(name: 'Sweden', isoCode: 'SE', nationality: 'SWEDISH'),
    CountryData(name: 'Switzerland', isoCode: 'CH', nationality: 'SWISS'),
    CountryData(name: 'Syria', isoCode: 'SY', nationality: 'SYRIAN'),
    CountryData(name: 'Taiwan', isoCode: 'TW', nationality: 'TAIWANESE'),
    CountryData(name: 'Tajikistan', isoCode: 'TJ', nationality: 'TAJIKISTANI'),
    CountryData(name: 'Tanzania', isoCode: 'TZ', nationality: 'TANZANIAN'),
    CountryData(name: 'Thailand', isoCode: 'TH', nationality: 'THAI'),
    CountryData(name: 'Timor-Leste', isoCode: 'TL', nationality: 'TIMORESE'),
    CountryData(name: 'Togo', isoCode: 'TG', nationality: 'TOGOLESE'),
    CountryData(name: 'Tonga', isoCode: 'TO', nationality: 'TONGAN'),
    CountryData(name: 'Trinidad and Tobago', isoCode: 'TT', nationality: 'TRINIDADIAN'),
    CountryData(name: 'Tunisia', isoCode: 'TN', nationality: 'TUNISIAN'),
    CountryData(name: 'Turkey', isoCode: 'TR', nationality: 'TURKISH'),
    CountryData(name: 'Turkmenistan', isoCode: 'TM', nationality: 'TURKMEN'),
    CountryData(name: 'Tuvalu', isoCode: 'TV', nationality: 'TUVALUAN'),
    CountryData(name: 'Uganda', isoCode: 'UG', nationality: 'UGANDAN'),
    CountryData(name: 'Ukraine', isoCode: 'UA', nationality: 'UKRAINIAN'),
    CountryData(name: 'United Arab Emirates', isoCode: 'AE', nationality: 'EMIRATI'),
    CountryData(name: 'United States', isoCode: 'US', nationality: 'AMERICAN'),
    CountryData(name: 'Uruguay', isoCode: 'UY', nationality: 'URUGUAYAN'),
    CountryData(name: 'Uzbekistan', isoCode: 'UZ', nationality: 'UZBEKISTANI'),
    CountryData(name: 'Vanuatu', isoCode: 'VU', nationality: 'VANUATUAN'),
    CountryData(name: 'Vatican City', isoCode: 'VA', nationality: 'VATICAN'),
    CountryData(name: 'Venezuela', isoCode: 'VE', nationality: 'VENEZUELAN'),
    CountryData(name: 'Vietnam', isoCode: 'VN', nationality: 'VIETNAMESE'),
    CountryData(name: 'Yemen', isoCode: 'YE', nationality: 'YEMENI'),
    CountryData(name: 'Zambia', isoCode: 'ZM', nationality: 'ZAMBIAN'),
    CountryData(name: 'Zimbabwe', isoCode: 'ZW', nationality: 'ZIMBABWEAN'),
  ];

  static List<DropdownMenuItem<String>> getCountryDropdownItems() {
    return all.map((country) => DropdownMenuItem(
      value: country.isoCode,
      child: Text(country.name),
    )).toList();
  }

  static List<DropdownMenuItem<String>> getNationalityDropdownItems() {
    return all.map((country) => DropdownMenuItem(
      value: country.nationality,
      child: Text(country.nationality),
    )).toList();
  }

  static String? getCountryNameByCode(String isoCode) {
    try {
      return all.firstWhere((country) => country.isoCode == isoCode).name;
    } catch (e) {
      return null;
    }
  }

  static String? getNationalityByCountryCode(String isoCode) {
    try {
      return all.firstWhere((country) => country.isoCode == isoCode).nationality;
    } catch (e) {
      return null;
    }
  }

  static bool isValidIsoCode(String isoCode) {
    return all.any((country) => country.isoCode == isoCode);
  }
}
