import 'package:SolidCheck/core/config/component_config.dart';
import 'package:SolidCheck/core/config/design_config.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/features/document_nomination/presentation/providers/document_nomination_provider.dart';
import 'package:SolidCheck/screens/ai_document_extraction_screen.dart';
import 'package:SolidCheck/screens/manual_document_entry_screen.dart';
import 'package:SolidCheck/services/document_processing_service.dart';
import 'package:SolidCheck/shared/widgets/navigation/applicant_sidebar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class DocumentProcessingChoiceScreen extends ConsumerStatefulWidget {
  final String documentType;
  final int applicationId;
  final Map<String, dynamic> documentConfig;
  
  const DocumentProcessingChoiceScreen({
    super.key,
    required this.documentType,
    required this.applicationId,
    required this.documentConfig,
  });

  @override
  ConsumerState<DocumentProcessingChoiceScreen> createState() => _DocumentProcessingChoiceScreenState();
}

class _DocumentProcessingChoiceScreenState extends ConsumerState<DocumentProcessingChoiceScreen> {
  final DocumentProcessingService _processingService = DocumentProcessingService();
  final bool _isLoading = false;

  @override
  void dispose() {
    _processingService.dispose();
    super.dispose();
  }

  void _navigateToAIExtraction() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AIDocumentExtractionScreen(
          documentType: widget.documentType,
          applicationId: widget.applicationId,
          documentConfig: widget.documentConfig,
        ),
      ),
    );
  }

  void _navigateToManualEntry() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ManualDocumentEntryScreen(
          documentType: widget.documentType,
          applicationId: widget.applicationId,
          documentConfig: widget.documentConfig,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isMobile = ResponsiveHelper.isMobile(context);
    final documentNominationState = ref.watch(documentNominationProvider);
    final isAiScannerEnabled = documentNominationState.isAiDocumentScannerEnabled;

    return Scaffold(
      backgroundColor: DesignConfig.primaryBackgroundColor,
      drawer: isMobile ? const ApplicantSidebar() : null,
      body: Row(
        children: [
          if (!isMobile) const ApplicantSidebar(),
          Expanded(
            child: Column(
              children: [
                Container(
                  padding: EdgeInsets.all(DesignConfig.spaceLG),
                  decoration: BoxDecoration(
                    color: DesignConfig.cardBackgroundColor,
                    border: Border(
                      bottom: BorderSide(
                        color: DesignConfig.primaryBorderColor,
                        width: 1,
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      if (isMobile)
                        Builder(
                          builder: (context) => IconButton(
                            icon: const Icon(Icons.menu),
                            color: DesignConfig.primaryColor,
                            onPressed: () => Scaffold.of(context).openDrawer(),
                          ),
                        ),
                      IconButton(
                        icon: const Icon(Icons.arrow_back),
                        color: DesignConfig.primaryColor,
                        onPressed: () => Navigator.of(context).pop(),
                        tooltip: 'Back to Document List',
                      ),
                      Expanded(
                        child: Text(
                          'Document Processing',
                          style: DesignConfig.headingMedium,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: SafeArea(
                    child: SingleChildScrollView(
                      padding: EdgeInsets.all(DesignConfig.space2XL),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
              
              const SizedBox(height: 8),
              
              Text(
                'Choose how you would like to process your ${widget.documentConfig['name'] ?? widget.documentType}',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              
              const SizedBox(height: 40),

              // Show AI disabled message if not enabled
              if (!isAiScannerEnabled) ...[
                Container(
                  padding: const EdgeInsets.all(16),
                  margin: const EdgeInsets.only(bottom: 24),
                  decoration: BoxDecoration(
                    color: Colors.amber[50],
                    border: Border.all(color: Colors.amber[300]!),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.amber[700]),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'AI Document Scanner is not available for your organization. Please use manual entry.',
                          style: TextStyle(
                            color: Colors.amber[800],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // Show AI option only if enabled for this entity
              if (isAiScannerEnabled) ...[
                _buildProcessingOptionCard(
                  title: 'AI-Powered Extraction',
                  subtitle: 'Upload your document and let AI extract the information',
                  icon: Icons.smart_toy,
                  color: Colors.blue,
                  benefits: [
                    'Automatic data extraction',
                    'Fast and accurate processing',
                    'Review and correct extracted data',
                    'Advanced fraud detection',
                  ],
                  onTap: _navigateToAIExtraction,
                  isRecommended: true,
                ),
                const SizedBox(height: 24),
              ],
              
              _buildProcessingOptionCard(
                title: 'Manual Entry',
                subtitle: 'Enter document information manually',
                icon: Icons.edit_document,
                color: Colors.green,
                benefits: [
                  'Full control over data entry',
                  'No document upload required',
                  'Traditional form-based entry',
                  'Optional document attachment',
                ],
                onTap: _navigateToManualEntry,
                isRecommended: !isAiScannerEnabled, // Recommend manual entry when AI is disabled
              ),
              
              SizedBox(height: DesignConfig.space3XL),
              
              _buildInfoSection(),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProcessingOptionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required List<String> benefits,
    required VoidCallback onTap,
    required bool isRecommended,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: DesignConfig.space2XL),
      decoration: BoxDecoration(
        color: DesignConfig.cardBackgroundColor,
        borderRadius: BorderRadius.circular(DesignConfig.radiusLG),
        border: isRecommended
          ? Border.all(color: DesignConfig.primaryColor, width: 2)
          : Border.all(color: DesignConfig.primaryBorderColor, width: 1),
        boxShadow: DesignConfig.shadowMD,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _isLoading ? null : onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(DesignConfig.spaceMD),
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(DesignConfig.radiusMD),
                      ),
                      child: Icon(
                        icon,
                        color: color,
                        size: DesignConfig.iconSizeLG,
                      ),
                    ),
                    SizedBox(width: DesignConfig.spaceLG),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                title,
                                style: DesignConfig.headingSmall,
                              ),
                              if (isRecommended) ...[
                                SizedBox(width: DesignConfig.spaceSM),
                                ComponentConfig.getBadge(
                                  'RECOMMENDED',
                                  backgroundColor: color,
                                  textColor: Colors.white,
                                ),
                              ],
                            ],
                          ),
                          SizedBox(height: DesignConfig.spaceXS),
                          Text(
                            subtitle,
                            style: DesignConfig.bodyMedium.copyWith(
                              color: DesignConfig.secondaryTextColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 20),
                
                ...benefits.map((benefit) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: color,
                        size: 16,
                      ),
                      SizedBox(width: DesignConfig.spaceSM),
                      Expanded(
                        child: Text(
                          benefit,
                          style: DesignConfig.bodySmall.copyWith(
                            color: DesignConfig.secondaryTextColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                )),
                
                SizedBox(height: DesignConfig.spaceLG),
                
                SizedBox(
                  width: double.infinity,
                  child: ComponentConfig.getPrimaryButton(
                    text: 'Choose This Option',
                    onPressed: _isLoading ? null : onTap,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoSection() {
    return Container(
      padding: EdgeInsets.all(DesignConfig.spaceXL),
      decoration: BoxDecoration(
        color: DesignConfig.primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(DesignConfig.radiusMD),
        border: Border.all(color: DesignConfig.primaryColor.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: DesignConfig.primaryColor,
                size: DesignConfig.iconSizeMD,
              ),
              SizedBox(width: DesignConfig.spaceSM),
              Text(
                'Processing Information',
                style: DesignConfig.headingSmall.copyWith(
                  color: DesignConfig.primaryColor,
                ),
              ),
            ],
          ),
          SizedBox(height: DesignConfig.spaceMD),
          Text(
            'Both processing methods are secure and will save your document information to your application. '
            'AI extraction uses advanced machine learning to automatically read your documents, while manual entry '
            'gives you complete control over the data input process.',
            style: DesignConfig.bodySmall.copyWith(
              color: DesignConfig.primaryColor,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }
}
