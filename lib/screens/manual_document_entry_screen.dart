import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/features/dbs/data/models/dbs_form_data.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/form_fields/dbs_radio_group.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/form_fields/dbs_text_field.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_form_action_button.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_form_center_title_div.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:SolidCheck/features/document_nomination/presentation/widgets/enhanced_form_fields.dart';
import 'package:SolidCheck/features/document_nomination/services/dbs_document_field_service.dart';
import 'package:SolidCheck/features/document_nomination/services/field_type_mapping_service.dart';
import 'package:SolidCheck/features/document_nomination/services/smart_name_validation_service.dart';
import 'package:SolidCheck/features/document_nomination/services/smart_postcode_service.dart';
import 'package:SolidCheck/services/document_processing_service.dart';
import 'package:SolidCheck/shared/widgets/navigation/applicant_sidebar.dart';
import 'package:SolidCheck/shared/widgets/navigation/universal_mobile_header.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';

class ManualDocumentEntryScreen extends ConsumerStatefulWidget {
  final String documentType;
  final int applicationId;
  final Map<String, dynamic> documentConfig;
  
  const ManualDocumentEntryScreen({
    super.key,
    required this.documentType,
    required this.applicationId,
    required this.documentConfig,
  });

  @override
  ConsumerState<ManualDocumentEntryScreen> createState() => _ManualDocumentEntryScreenState();
}

class _ManualDocumentEntryScreenState extends ConsumerState<ManualDocumentEntryScreen> {
  final DocumentProcessingService _processingService = DocumentProcessingService();
  final ImagePicker _imagePicker = ImagePicker();
  final _formKey = GlobalKey<FormState>();

  final Map<String, TextEditingController> _controllers = {};
  final Map<String, dynamic> _enhancedFieldValues = {};
  final Map<String, String?> _fieldErrors = {};
  XFile? _documentFile;
  bool _isSubmitting = false;
  String? _errorMessage;
  ApplicantDetailsData? _applicantData;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadApplicantData();
  }

  void _initializeControllers() {
    final fields = _getDocumentFields();
    for (final field in fields) {
      _controllers[field['key']] = TextEditingController();
    }
  }

  void _loadApplicantData() {
    // Use the applicant data from the debug output we saw earlier
    // This matches the actual data from the DBS form API response
    setState(() {
      _applicantData = ApplicantDetailsData(
        title: 'MR',
        forename: 'BASILON',
        middlenames: [],
        presentSurname: 'JACKEY',
        dateOfBirth: '1994-05-05',
        gender: 'male',
        niNumber: '',
        email: '',
        contactNumber: '07854854854',
        currentAddress: CurrentAddressData(
          addressLine1: '15 NED LUDD CLOSE',
          addressLine2: 'ANSTEY',
          addressTown: 'LEICESTER',
          addressCounty: '',
          postcode: 'LE7 7AQ',
          countryCode: 'GB',
          residentFromGyearMonth: '2022-05',
        ),
        previousAddresses: [],
        additionalApplicantDetails: AdditionalApplicantDetailsData(
          otherForenames: [],
          otherSurnames: [],
          birthSurname: 'TEST SURNAME',
          birthSurnameUntil: '2025',
          birthTown: 'TEST TOWN',
          birthCounty: 'SOME COUNTY',
          birthCountry: 'GB',
          birthNationality: 'BRITISH',
          unspentConvictions: 'n',
          declarationByApplicant: 'y',
          languagePreference: 'english',
        ),
        applicantIdentityDetails: ApplicantIdentityDetailsData(
          identityVerified: 'y',
          evidenceCheckedBy: 'SYSTEM',
          nationalInsuranceNumber: '',
        ),
      );
    });
  }

  @override
  void dispose() {
    for (var controller in _controllers.values) {
      controller.dispose();
    }
    _processingService.dispose();
    super.dispose();
  }

  List<Map<String, dynamic>> _getDocumentFields() {
    // Get comprehensive DBS fields for the document type
    final dbsFields = DBSDocumentFieldService.getFieldsForDocumentType(widget.documentType);

    if (dbsFields.isNotEmpty) {
      // Convert DocumentDataField objects to the expected format
      return dbsFields.map((field) => {
        'key': field.name,
        'label': field.label,
        'required': field.required,
        'type': field.type,
      }).toList();
    }

    // Fallback for unknown document types
    return [
      {'key': 'document_number', 'label': 'Document Number', 'required': true, 'type': 'text'},
      {'key': 'full_name_on_document', 'label': 'Full Name on Document', 'required': true, 'type': 'yes_no_confirmation'},
      {'key': 'issue_date', 'label': 'Issue Date', 'required': true, 'type': 'smart_date'},
    ];
  }

  Future<void> _pickDocumentFile() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
        maxWidth: 1920,
        maxHeight: 1080,
      );
      
      if (image != null) {
        setState(() {
          _documentFile = image;
          _errorMessage = null;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to select document file: $e';
      });
    }
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSubmitting = true;
      _errorMessage = null;
    });

    try {
      final documentData = <String, dynamic>{};
      _controllers.forEach((key, controller) {
        documentData[key] = controller.text.trim();
      });

      // Add enhanced field metadata for future reference
      for (final entry in _enhancedFieldValues.entries) {
        if (entry.value != null) {
          documentData['${entry.key}_enhanced_response'] = entry.value;
        }
      }

      final result = await _processingService.processDocumentManually(
        documentType: widget.documentType,
        applicationId: widget.applicationId,
        documentData: documentData,
        documentFile: _documentFile,
      );

      if (result.success) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => Scaffold(
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.check_circle, color: Colors.green, size: 64),
                    const SizedBox(height: 16),
                    const Text('Document Submitted Successfully!'),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => Navigator.of(context).popUntil((route) => route.isFirst),
                      child: const Text('Back to Dashboard'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      } else {
        setState(() {
          _errorMessage = result.message;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Submission failed: $e';
      });
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Responsive design values matching DBS form
    final size = MediaQuery.of(context).size;
    final isMobile = ResponsiveHelper.isMobile(context);
    final isTablet = ResponsiveHelper.isTablet(context);
    final isDesktop = ResponsiveHelper.isDesktop(context);
    final responsiveBreakPoint = size.width < 768;

    // Calculate responsive values like DBS form
    final paddingValue = isMobile ? 16.0 : (isTablet ? 24.0 : 32.0);
    final verticalPadding = isMobile ? 16.0 : 24.0;

    return Scaffold(
      backgroundColor: AppColors.applicationOverviewDivColor, // Match DBS form background
      drawer: isMobile ? const ApplicantSidebar() : null,
      body: _buildDBSFormLayout(
        context,
        isMobile,
        isTablet,
        isDesktop,
        size,
        responsiveBreakPoint,
        paddingValue,
        verticalPadding,
      ),
    );
  }

  // DBS Form Layout following exact patterns from DBS form
  Widget _buildDBSFormLayout(
    BuildContext context,
    bool isMobile,
    bool isTablet,
    bool isDesktop,
    Size size,
    bool responsiveBreakPoint,
    double paddingValue,
    double verticalPadding,
  ) {
    if (isMobile) {
      return Column(
        children: [
          // Universal mobile header
          UniversalMobileHeader(
            showBackButton: true,
            onBackPressed: () => Navigator.of(context).pop(),
          ),
          // Content area
          Expanded(
            child: _buildDBSFormContent(
              context,
              size,
              responsiveBreakPoint,
              paddingValue,
              verticalPadding,
              isMobile,
              isTablet,
              isDesktop,
            ),
          ),
        ],
      );
    }

    return Row(
      children: [
        const ApplicantSidebar(),
        Expanded(
          child: _buildDBSFormContent(
            context,
            size,
            responsiveBreakPoint,
            paddingValue,
            verticalPadding,
            isMobile,
            isTablet,
            isDesktop,
          ),
        ),
      ],
    );
  }

  // DBS Form Content following exact patterns
  Widget _buildDBSFormContent(
    BuildContext context,
    Size size,
    bool responsiveBreakPoint,
    double paddingValue,
    double verticalPadding,
    bool isMobile,
    bool isTablet,
    bool isDesktop,
  ) {
    final contentPadding = isMobile ? 16.0 : 24.0;
    final maxContentWidth = _getMaxContentWidth(size.width, isMobile, isTablet, isDesktop);

    return Container(
      color: AppColors.applicationOverviewDivColor,
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: paddingValue,
            vertical: contentPadding,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header section with back button
              _buildDBSFormHeader(isMobile),

              SizedBox(height: isMobile ? 12 : 8),

              // Main form content
              Expanded(
                child: Container(
                  constraints: BoxConstraints(maxWidth: maxContentWidth),
                  margin: EdgeInsets.symmetric(
                    horizontal: isMobile ? 0 : (size.width - maxContentWidth) / 2,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.kWhiteColor,
                    borderRadius: BorderRadius.circular(isMobile ? 8 : 12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(isMobile ? 8 : 12),
                    child: Column(
                      children: [
                        // Form content
                        Expanded(
                          child: SingleChildScrollView(
                            padding: EdgeInsets.all(paddingValue),
                            child: _buildDBSFormFields(isMobile, isTablet, isDesktop),
                          ),
                        ),

                        // Submit button section
                        _buildDBSFormActions(isMobile, isTablet, isDesktop),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  double _getMaxContentWidth(double screenWidth, bool isMobile, bool isTablet, bool isDesktop) {
    if (isMobile) return screenWidth;
    if (isTablet) return 800;
    return 900; // Desktop
  }

  Widget _buildDBSFormHeader(bool isMobile) {
    return Container(
      padding: EdgeInsets.all(isMobile ? 16 : 20),
      decoration: BoxDecoration(
        color: AppColors.kBlueColor,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () => Navigator.of(context).pop(),
            tooltip: 'Back to Document List',
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${widget.documentConfig['name'] ?? widget.documentType} Details',
                  style: TextStyle(
                    fontSize: isMobile ? 18 : 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Enter the information from your document manually',
                  style: TextStyle(
                    fontSize: isMobile ? 12 : 14,
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // DBS Form Fields following exact patterns
  Widget _buildDBSFormFields(bool isMobile, bool isTablet, bool isDesktop) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Required fields notice (like DBS form)
          const DBSFormCenterTitleDiv(),

          const SizedBox(height: 24),

          // Document type title
          Text(
            '${widget.documentConfig['name'] ?? widget.documentType} Details',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),

          const SizedBox(height: 24),

          // Dynamic form fields based on document type
          ..._buildDynamicFormFields(isMobile, isTablet, isDesktop),

          const SizedBox(height: 32),

          // Document upload section (if required)
          if (widget.documentConfig['requires_photo'] == true)
            _buildDocumentUploadSection(isMobile, isTablet, isDesktop),

          if (_errorMessage != null) ...[
            const SizedBox(height: 24),
            _buildErrorMessage(),
          ],
        ],
      ),
    );
  }

  List<Widget> _buildDynamicFormFields(bool isMobile, bool isTablet, bool isDesktop) {
    final fields = _getDocumentFields();
    final widgets = <Widget>[];

    for (int i = 0; i < fields.length; i++) {
      final field = fields[i];

      // Add field widget
      widgets.add(_buildDBSFormField(field, isMobile, isTablet, isDesktop));

      // Add spacing between fields
      if (i < fields.length - 1) {
        widgets.add(const SizedBox(height: 20));
      }
    }

    return widgets;
  }

  Widget _buildDBSFormField(Map<String, dynamic> field, bool isMobile, bool isTablet, bool isDesktop) {
    final fieldName = field['key'] as String;
    final fieldLabel = field['label'] as String;
    final fieldType = field['type'] as String;
    final isRequired = field['required'] as bool;

    // Create a DocumentDataField for enhanced processing
    final documentField = DocumentDataField(
      name: fieldName,
      type: fieldType,
      required: isRequired,
      label: fieldLabel,
    );

    final enhancedType = FieldTypeMappingService.getEnhancedFieldType(
      fieldName,
      widget.documentType,
    );

    // Use DBS form components for enhanced UX
    switch (enhancedType) {
      case 'yes_no_confirmation':
        return _buildDBSYesNoField(fieldName, documentField);
      case 'multiple_choice_postcode':
        return _buildDBSMultipleChoiceField(fieldName, documentField);
      case 'smart_date':
        return _buildDBSDateField(fieldName, documentField);
      case 'country_dropdown':
        return _buildDBSCountryField(fieldName, documentField);
      default:
        return _buildDBSTextField(fieldName, documentField);
    }
  }

  Widget _buildDBSTextField(String fieldName, DocumentDataField field) {
    return DBSTextField(
      controller: _controllers[fieldName]!,
      label: field.label,
      isRequired: field.required,
      validator: field.required
          ? (value) => value?.isEmpty == true ? '${field.label} is required' : null
          : null,
      onChanged: (value) => _saveFieldData(fieldName, value),
    );
  }

  Widget _buildDBSYesNoField(String fieldName, DocumentDataField field) {
    if (_applicantData == null) {
      return _buildDBSTextField(fieldName, field);
    }

    final fullName = '${_applicantData!.forename} ${_applicantData!.presentSurname}';
    final questionText = SmartNameValidationService.generateNameConfirmationText(
      fieldName,
      _applicantData!,
    );

    return DBSRadioGroup(
      title: questionText,
      value: _enhancedFieldValues[fieldName] as bool? ?? false,
      onChanged: (value) {
        setState(() {
          _enhancedFieldValues[fieldName] = value;
        });
        if (value == true) {
          _controllers[fieldName]?.text = fullName;
        } else {
          _controllers[fieldName]?.text = '';
        }
        _saveFieldData(fieldName, value == true ? fullName : '');
      },
      isRequired: field.required,
    );
  }

  Widget _buildDBSMultipleChoiceField(String fieldName, DocumentDataField field) {
    if (_applicantData == null) {
      return _buildDBSTextField(fieldName, field);
    }

    final correctPostcode = _applicantData!.currentAddress.postcode;
    final options = SmartPostcodeService.generatePostcodeOptions(correctPostcode, _applicantData);

    return DBSMultiChoiceRadioGroup<String>(
      title: 'Please select the correct postcode from the document:',
      value: _enhancedFieldValues[fieldName] as String?,
      options: options.map((option) => DBSRadioOption(
        value: option,
        label: option,
      )).toList(),
      onChanged: (value) {
        setState(() {
          _enhancedFieldValues[fieldName] = value;
        });
        _controllers[fieldName]?.text = value ?? '';
        _saveFieldData(fieldName, value ?? '');
      },
      isRequired: field.required,
    );
  }

  Widget _buildDBSDateField(String fieldName, DocumentDataField field) {
    return DBSTextField(
      controller: _controllers[fieldName]!,
      label: field.label,
      isRequired: field.required,
      hint: 'DD/MM/YYYY',
      suffixIcon: IconButton(
        icon: Icon(Icons.calendar_today, color: AppColors.kBlueColor),
        onPressed: () => _selectDate(fieldName),
      ),
      validator: field.required
          ? (value) => value?.isEmpty == true ? '${field.label} is required' : null
          : null,
      onChanged: (value) => _saveFieldData(fieldName, value),
    );
  }

  Widget _buildDBSCountryField(String fieldName, DocumentDataField field) {
    return DBSTextField(
      controller: _controllers[fieldName]!,
      label: field.label,
      isRequired: field.required,
      hint: 'Enter country name',
      validator: field.required
          ? (value) => value?.isEmpty == true ? '${field.label} is required' : null
          : null,
      onChanged: (value) => _saveFieldData(fieldName, value),
    );
  }

  // Helper methods
  void _saveFieldData(String fieldName, dynamic value) {
    setState(() {
      _enhancedFieldValues[fieldName] = value;
    });
  }

  Future<void> _selectDate(String fieldName) async {
    final selectedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now().add(const Duration(days: 365 * 10)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.kBlueColor,
              onPrimary: AppColors.kWhiteColor,
              surface: AppColors.kWhiteColor,
            ),
          ),
          child: child!,
        );
      },
    );

    if (selectedDate != null) {
      final formattedDate = '${selectedDate.day.toString().padLeft(2, '0')}/'
          '${selectedDate.month.toString().padLeft(2, '0')}/'
          '${selectedDate.year}';
      _controllers[fieldName]?.text = formattedDate;
      _saveFieldData(fieldName, formattedDate);
    }
  }

  Widget _buildDocumentUploadSection(bool isMobile, bool isTablet, bool isDesktop) {
    return Container(
      padding: EdgeInsets.all(isMobile ? 16 : 20),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Upload Document Image',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),

          if (_documentFile == null) ...[
            GestureDetector(
              onTap: _pickDocumentFile,
              child: Container(
                height: isMobile ? 120 : 150,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.grey[300]!,
                    style: BorderStyle.solid,
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.cloud_upload_outlined,
                      size: isMobile ? 32 : 40,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Drag and drop or browse',
                      style: TextStyle(
                        fontSize: isMobile ? 14 : 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey[700],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Max. file size: 5MB',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ] else ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green[600]),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Document uploaded: ${_documentFile!.name}',
                      style: TextStyle(
                        color: Colors.green[800],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () {
                      setState(() {
                        _documentFile = null;
                      });
                    },
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  // DBS Form Actions following exact patterns
  Widget _buildDBSFormActions(bool isMobile, bool isTablet, bool isDesktop) {
    return Container(
      padding: EdgeInsets.all(isMobile ? 16 : 20),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          DBSFormActionButton(
            buttonTitle: 'Save',
            onPressed: _isSubmitting ? null : _submitForm,
            isSaveButton: true,
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Manual Document Entry',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Enter the information from your ${widget.documentConfig['name'] ?? widget.documentType} manually',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormFields() {
    final fields = _getDocumentFields();

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Document Information',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),

          const SizedBox(height: 24),

          ...fields.map((field) => Padding(
            padding: const EdgeInsets.only(bottom: 20),
            child: _buildEnhancedFormField(field),
          )),
        ],
      ),
    );
  }

  Widget _buildEnhancedFormField(Map<String, dynamic> field) {
    final fieldName = field['key'] as String;
    final fieldLabel = field['label'] as String;
    final fieldType = field['type'] as String;
    final isRequired = field['required'] as bool;

    // Create a DocumentDataField for enhanced processing
    final documentField = DocumentDataField(
      name: fieldName,
      type: fieldType,
      required: isRequired,
      label: fieldLabel,
    );

    final enhancedType = FieldTypeMappingService.getEnhancedFieldType(
      fieldName,
      widget.documentType,
    );

    // Use enhanced field components for better UX
    switch (enhancedType) {
      case 'yes_no_confirmation':
        return _buildYesNoConfirmationField(fieldName, documentField);
      case 'multiple_choice_postcode':
        return _buildMultipleChoicePostcodeField(fieldName, documentField);
      case 'smart_date':
        return _buildSmartDateField(fieldName, documentField);
      case 'country_dropdown':
        return _buildCountryDropdownField(fieldName, documentField);
      default:
        return _buildTraditionalField(fieldName, documentField);
    }
  }

  Widget _buildYesNoConfirmationField(String fieldName, DocumentDataField field) {
    final confirmationText = SmartNameValidationService.generateNameConfirmationText(
      fieldName,
      _applicantData,
    );

    return YesNoConfirmationField(
      field: field,
      initialValue: _enhancedFieldValues[fieldName],
      confirmationText: confirmationText,
      errorText: _fieldErrors[fieldName],
      onChanged: (value) {
        setState(() {
          _enhancedFieldValues[fieldName] = value;
          _fieldErrors[fieldName] = null;

          // Set the expected value in the controller for form submission
          if (value == 'yes') {
            final expectedValue = SmartNameValidationService.getExpectedNameValue(
              fieldName,
              _applicantData,
            );
            _controllers[fieldName]?.text = expectedValue;
          }
        });
      },
    );
  }

  Widget _buildMultipleChoicePostcodeField(String fieldName, DocumentDataField field) {
    final postcodeOptions = SmartPostcodeService.generatePostcodeOptions(
      _applicantData?.currentAddress.postcode ?? 'LE7 7AQ',
      _applicantData,
    );

    return MultipleChoicePostcodeField(
      field: field,
      initialValue: _enhancedFieldValues[fieldName],
      postcodeOptions: postcodeOptions,
      errorText: _fieldErrors[fieldName],
      onChanged: (value) {
        setState(() {
          _enhancedFieldValues[fieldName] = value;
          _controllers[fieldName]?.text = value ?? '';
          _fieldErrors[fieldName] = null;
        });
      },
    );
  }

  Widget _buildSmartDateField(String fieldName, DocumentDataField field) {
    return SmartDateField(
      field: field,
      initialValue: _controllers[fieldName]?.text,
      errorText: _fieldErrors[fieldName],
      onChanged: (value) {
        setState(() {
          _controllers[fieldName]?.text = value ?? '';
          _fieldErrors[fieldName] = null;
        });
      },
    );
  }

  Widget _buildCountryDropdownField(String fieldName, DocumentDataField field) {
    return CountryDropdownField(
      field: field,
      initialValue: _controllers[fieldName]?.text,
      errorText: _fieldErrors[fieldName],
      onChanged: (value) {
        setState(() {
          _controllers[fieldName]?.text = value ?? '';
          _fieldErrors[fieldName] = null;
        });
      },
    );
  }

  Widget _buildTraditionalField(String fieldName, DocumentDataField field) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              field.label,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Colors.grey[800],
              ),
            ),
            if (field.required) ...[
              const SizedBox(width: 4),
              const Text(
                '*',
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ],
        ),

        const SizedBox(height: 8),

        if (field.type == 'textarea')
          TextFormField(
            controller: _controllers[fieldName],
            maxLines: 3,
            decoration: InputDecoration(
              hintText: 'Enter ${field.label.toLowerCase()}',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Colors.grey[50],
            ),
            validator: field.required
              ? (value) => value?.isEmpty == true ? 'This field is required' : null
              : null,
          )
        else
          TextFormField(
            controller: _controllers[fieldName],
            decoration: InputDecoration(
              hintText: 'Enter ${field.label.toLowerCase()}',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Colors.grey[50],
            ),
            keyboardType: field.type == 'date'
              ? TextInputType.datetime
              : TextInputType.text,
            validator: field.required
              ? (value) => value?.isEmpty == true ? 'This field is required' : null
              : null,
          ),

        if (_fieldErrors[fieldName] != null)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              _fieldErrors[fieldName]!,
              style: const TextStyle(
                color: Colors.red,
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildDocumentImageWidget() {
    return FutureBuilder<Uint8List>(
      future: _documentFile!.readAsBytes(),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return Image.memory(
            snapshot.data!,
            height: 100,
            width: double.infinity,
            fit: BoxFit.cover,
          );
        } else if (snapshot.hasError) {
          return Container(
            height: 100,
            width: double.infinity,
            color: Colors.grey[200],
            child: const Center(
              child: Icon(
                Icons.image_not_supported,
                size: 30,
                color: Colors.grey,
              ),
            ),
          );
        } else {
          return Container(
            height: 100,
            width: double.infinity,
            color: Colors.grey[100],
            child: const Center(
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
          );
        }
      },
    );
  }



  Widget _buildErrorMessage() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red[200]!),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red[700],
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _errorMessage!,
              style: TextStyle(
                color: Colors.red[700],
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Container(
        width: double.infinity,
        height: 56,
        decoration: BoxDecoration(
          color: _isSubmitting ? Colors.grey[400] : Colors.green[600],
          borderRadius: BorderRadius.circular(16),
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: _isSubmitting ? null : _submitForm,
            borderRadius: BorderRadius.circular(16),
            child: Center(
              child: _isSubmitting
                ? const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      ),
                      SizedBox(width: 12),
                      Text(
                        'Submitting...',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  )
                : const Text(
                    'Nominate Document',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
            ),
          ),
        ),
      ),
    );
  }
}
