class AppException implements Exception {
  final String message;
  final String? prefix;
  final String? url;

  AppException([this.message = "", this.prefix, this.url]);

  @override
  String toString() {
    return "$prefix$message${url != null ? ' (URL: $url)' : ''}";
  }
}

class FetchDataException extends AppException {
  FetchDataException([String message = ""]) : super(message, "");
}

class BadRequestException extends AppException {
  BadRequestException([String message = ""])
      : super(message, "Invalid Request: ");
}

class UnauthorisedException extends AppException {
  UnauthorisedException([String message = ""])
      : super(message, "Unauthorised: ");
}

class ValidationException extends AppException {
  ValidationException([String message = ""])
      : super(message, "Validation Error: ");
}
