import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:SolidCheck/core/constants/constants.dart';
import 'package:SolidCheck/core/network/api_exceptions.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

class HttpClient {
  static final HttpClient _singleton = HttpClient._internal();

  factory HttpClient() {
    return _singleton;
  }

  late final Dio _dio;

  HttpClient._internal() {
    _dio = Dio(
      BaseOptions(
        baseUrl: AppConstants.baseURL,
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ),
    );

    _dio.interceptors.add(
      LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (obj) => debugPrint('🔵 HTTP: $obj'),
      ),
    );
  }

  Future<dynamic> fetchData(
    String url, {
    Map<String, String>? params,
    Map<String, String>? headers,
  }) async {
    try {
      final response = await _dio.get(
        url,
        queryParameters: params,
        options: Options(headers: headers),
      );
      return _returnResponse(response);
    } on DioException catch (e) {
      throw _handleDioError(e);
    } on SocketException {
      throw FetchDataException('No Internet connection');
    }
  }

  Future<dynamic> postData(
    String url,
    dynamic body, {
    Map<String, String>? headers,
  }) async {
    try {
      final response = await _dio.post(
        url,
        data: body,
        options: Options(headers: headers),
      );
      return _returnResponse(response);
    } on DioException catch (e) {
      throw _handleDioError(e);
    } on SocketException {
      throw FetchDataException('No Internet connection');
    }
  }

  Future<dynamic> putData(
    String url,
    dynamic body, {
    Map<String, String>? headers,
  }) async {
    try {
      final response = await _dio.put(
        url,
        data: body,
        options: Options(headers: headers),
      );
      return _returnResponse(response);
    } on DioException catch (e) {
      throw _handleDioError(e);
    } on SocketException {
      throw FetchDataException('No Internet connection');
    }
  }

  dynamic _returnResponse(Response response) {
    switch (response.statusCode) {
      case 200:
      case 201:
        return response.data;
      case 302:
        try {
          if (response.data != null) {
            return response.data;
          }
        } catch (e) {
          // Ignore JSON parsing errors for 401 responses
        }
        throw UnauthorisedException('Session invalidated - please login again');
      case 400:
        throw BadRequestException(response.data.toString());
      case 401:
        throw UnauthorisedException(response.data.toString());
      case 403:
        throw UnauthorisedException('Access forbidden');
      case 422:
        throw ValidationException(response.data.toString());
      case 500:
      default:
        throw FetchDataException(
          'Error occurred while Communication with Server with StatusCode: ${response.statusCode}',
        );
    }
  }

  Exception _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return FetchDataException('Connection timeout');
      case DioExceptionType.badResponse:
        if (e.response != null) {
          return _returnResponse(e.response!);
        }
        return FetchDataException('Bad response');
      case DioExceptionType.cancel:
        return FetchDataException('Request cancelled');
      case DioExceptionType.connectionError:
        return FetchDataException('No Internet connection');
      default:
        return FetchDataException('Something went wrong');
    }
  }
}
