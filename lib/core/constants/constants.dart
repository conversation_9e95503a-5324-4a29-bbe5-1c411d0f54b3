import 'package:flutter/foundation.dart';

class AppConstants {
  static String get baseURL {
    if (kIsWeb) {
      // For web, we need to handle CORS. Options:
      // 1. Configure CORS in your Laravel API (recommended)
      // 2. Use a proxy server
      // 3. Temporarily disable web security (development only)
      return 'http://localhost:8001/api/v1';
    } else {
      return 'http://127.0.0.1:8001/api/v1';
    }
  }

  static String get postcodeServiceBaseURL {
    if (kIsWeb) {
      return 'http://localhost:8081/api/v1';
    } else {
      return 'http://127.0.0.1:8081/api/v1';
    }
  }

  static String errorSignForDbsPng =
      'assets/images/error_sign_dbs_form_1.png';
  static String errorSignForDbsSvg =
      'assets/images/error_sign_dbs_form.svg';
  static String logoAppBar = 'assets/logos/solidcheck-logo-appbar.png';
  static String refCheckInitScreenText =
      '''It looks like you haven't filled out the Reference Check Application Form yet. Please complete this form to allow us to proceed with your background check. Your responses are crucial and will be kept confidential.When you are ready, click the button below to begin filling out the form.''';
  static String resultDoneIcon =
      'assets/images/done_icon_for_result_page.svg';
  static String resultScreenText =
      'Your DBS Check Application Form has been successfully submitted.\nLet’s move to the next step and nominate the necessary documents.';
  static String dbsFormScreenGreetings = '''
      Thank you for taking the time to complete this form. Your responses are crucial for our background check process and will be kept confidential. Please provide accurate information as you fill out each section.''';
  static String dbsMainScreenFormContent = '''
      It looks like you haven't filled out the DBS Check Application Form yet. Please complete this form to allow us to proceed with your background check. Your responses are crucial and will be kept confidential. When you are ready, click the button below to begin filling out the form.''';
  static String solidCheckLogoAppBar =
      'assets/logos/solidcheck-logo-for-navbar.png';
  static String solidCheckHorizontalLogo =
      'assets/logos/solidcheck-logo-horizontal.svg';
  static String solidCheckLoginLogo =
      'assets/logos/solidcheck-logo-login-screen.png';
  static String solidCheckOnlineLogo =
      'assets/logos/solidcheck-online-logo.png';
  static String solidCheckOnlineloader = 'assets/gif/loader.gif';
  static String creditCard = 'Credit Card';
  static String creditCardText =
      'Pay with any major credit or debit card. Enter your card details for fast and secure processing';
  static String bankTransfer = 'Bank Transfer';
  static String bankTransferText =
      'Directly transfer funds from your bank account. This may take additional processing time before your form is unlocked.';
  static String emptyFieldMessage = 'Please fill out this field';
  static String addQuestionString = '+ Add question for the referee';
  static String enterQuestion = 'Enter Question:';
  static String clinicalExpertiseQuestion =
      'How would you describe the candidates level of clinical expertise?';
  static String partTime = 'Part-time';
  static String fullTime = 'Full-time';
  static String newApplicants = 'New Applicants';
  static String inProgress = 'In Progress';
  static String withClient = 'With Client';
  static String withSolidCheck = 'With SolidCheck';
  static String complete = 'Complete';
  static String kAddEducationReferee = 'Add Education Refree';
  static String kAddEmploymentReferee = 'Add Employment Refree';
  static String kAddPersonalReferee = 'Add Personal Refree';
  static String kAddSelfEmploymentReferee = 'Add Self Employment Refree';
  static String kAddEmploymentGap = 'Add Employment Gap';
  static String kDeclarationForm = 'DeclarationForm';
  static String kIssuedInUKOrChannelIslands =
      'Issued in the UK or Channel Islands within the last 12 months.';
  static String kIssuedInNonUKThreeMonths =
      'Issued in the Non-UK within the last 3 months.';
  static String kIssuedInUKThreeMonths =
      'Issued in the UK within the last 3 months.';
  static String kIssuedInTheUK = 'Issued in the UK.';
  static String kSelectOption = 'Select an Option';
  static String kUnitedKingdom = 'United Kingdom';
  static String kWorkPermit = 'Work Permit';
  static String kDocumentType = 'Document Type';
  static String kDbsForm = 'DBS Form';
  static String kReferenceCheck = 'Reference Check';
  static String kRightToWork = 'Right to Work';
  static String kTownCity = 'Town/City:';
  static String kJobRole = 'Job Role: *';
  static String kJobRoleErrorMessage = 'Please select a job role';
}
