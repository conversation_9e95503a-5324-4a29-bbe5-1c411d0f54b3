class ConsentConstants {
  static String consentRadioButtonText =
      'I have read and understood the above infromation and consent to the submission of the application';

  static String consentText1 = '''
I have provided complete and true information in support of the application and I understand that knowingly making a false statement for this purpose is a criminal offence.
''';

  static String consentText2 = '''
I have read, understood and accept the Privacy Policy. I also give my consent for this information to be passed to the Disclosure and Barring Service for the purpose of applying for a Disclosure.
''';

  static String consentText3 = '''
 I have read the Basic DBS Check Processing Privacy Policy and I understand how DBS will process my personal data.
''';

  static String consentText4 = '''
I consent to the DBS providing an electronic result directly to the responsible organisation that has submitted my application. I understand that an electronic result contains a message that indicates either the certificate does not contain criminal record information or to await certificate which will indicate that my certificate contains criminal record information. In some cases the responsible organisation may provide this information directly to my employer prior to me receiving my certificate. I understand if I do not consent to an electronic result being issued to the responsible organisation submitting my application that I must not proceed with this application and I should apply directly to DBS Request a basic DBS check - GOV.UK (www.gov.uk)
I understand that to withdraw my consent whilst my application is in progress I must contact the DBS helpline 03000 200 190. My application will then be withdrawn.
''';

  static String consentText5 = '''
I give my consent to SolidCheck receiving, inspecting and disposing of, if necessary, the certificate and Solid Check informing the recruiting organisation about the content of the Certificate and/or if it contains information.
''';
}
