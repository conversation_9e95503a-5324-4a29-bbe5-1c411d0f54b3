import 'dart:math';
import 'package:SolidCheck/core/constants/dashboard_icons.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/shared/utils/responsive.dart';
import 'package:flutter/material.dart';

class DashboardConstants {
  static List<String> applicantNames = [
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>'
  ];

  static Widget buildNumberDiv(
    Size size,
    String title1,
    String title2,
    Color color,
    context,
  ) {
    final responsive = ResponsiveUtil.isMobile(context) ||
        ResponsiveUtil.isMobileLarge(context);
    return Padding(
      padding: const EdgeInsets.only(right: 20.0, top: 20.0),
      child: Container(
        height: size.height * 0.20,
        width: responsive ? size.width : size.width * 0.50,
        decoration: BoxDecoration(
            color: color, borderRadius: BorderRadius.circular(10.0)),
        child: Center(
          child: Column(
            children: [
              Align(
                alignment: Alignment.topLeft,
                child: Padding(
                  padding: const EdgeInsets.only(left: 10.0, top: 10.0),
                  child: Expanded(
                      child: Text(title1,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(fontSize: 25.0))),
                ),
              ),
              const Spacer(),
              Align(
                alignment: Alignment.bottomRight,
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 10.0, right: 10.0),
                  child: Expanded(
                    child: Text(
                      title2,
                      style: TextStyle(
                        fontSize: 30.0,
                        color: AppColors.kSideMenuIconColor,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  static Widget buildSearchBarDiv(
    Size size,
    void Function(String) onOptionSelected,
    List<String> filterOptions,
    List<String> sortOptions,
    BuildContext context,
  ) {
    void showOptionsDialog(BuildContext context) {
      showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext context) {
          return AlertDialog(
            contentPadding: EdgeInsets.zero,
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  color: AppColors.kBlueColor,
                  child: const ListTile(
                    title:
                        Text('Sort By:', style: TextStyle(color: Colors.white)),
                  ),
                ),
                ListTile(
                  title: const Text('Date Created - Oldest First'),
                  onTap: () {
                    onOptionSelected('Date Created - Oldest First');
                    Navigator.of(context).pop();
                  },
                ),
                ListTile(
                  title: const Text('Date Created - Newest First'),
                  onTap: () {
                    onOptionSelected('Date Created - Newest First');
                    Navigator.of(context).pop();
                  },
                ),
                ListTile(
                  title: const Text('Alphabetical (A-Z)'),
                  onTap: () {
                    onOptionSelected('Alphabetical (A-Z)');
                    Navigator.of(context).pop();
                  },
                ),
                ListTile(
                  title: const Text('Alphabetical (Z-A)'),
                  onTap: () {
                    onOptionSelected('Alphabetical (Z-A)');
                    Navigator.of(context).pop();
                  },
                ),
                Container(
                  color: AppColors.kBlueColor,
                  child: const ListTile(
                    title:
                        Text('Filter:', style: TextStyle(color: Colors.white)),
                  ),
                ),
                ListTile(
                  title: const Text('Status'),
                  onTap: () {
                    onOptionSelected('Status');
                    Navigator.of(context).pop();
                  },
                ),
                SizedBox(
                  height: 200,
                  child: GridView.builder(
                    shrinkWrap: true,
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                    ),
                    itemCount: filterOptions.length,
                    itemBuilder: (BuildContext context, int index) {
                      return ListTile(
                        title: Text(filterOptions[index]),
                        onTap: () {
                          onOptionSelected(filterOptions[index]);
                          Navigator.of(context).pop();
                        },
                      );
                    },
                  ),
                ),
                ListTile(
                  title: const Text('Organization'),
                  onTap: () {
                    onOptionSelected('Organization');
                    Navigator.of(context).pop();
                  },
                ),
                const Row(
                  children: [
                    Expanded(child: Text('Organization A')),
                    Expanded(child: Text('Organization B')),
                  ],
                ),
              ],
            ),
            actions: <Widget>[
              TextButton(
                child: const Text('Close'),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        },
      );
    }

    return SizedBox(
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: TextField(
              decoration: InputDecoration(
                filled: true,
                fillColor: AppColors.kGreyColor.withValues(alpha: 51),
                border: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: AppColors.kGreyColor.withValues(alpha: 51),
                  ),
                  borderRadius: BorderRadius.circular(10.0),
                ),
                hintText: 'Search for Applicants',
                suffixIcon: const Icon(Icons.search_outlined),
              ),
            ),
          ),
          const SizedBox(width: 10.0),
          Container(
            height: 50.0,
            width: 50.0,
            decoration: BoxDecoration(
              color: AppColors.kGreyColor.withValues(alpha: 51),
              borderRadius: BorderRadius.circular(10.0),
            ),
            child: Center(
              child: IconButton(
                icon: const Icon(Icons.tune_outlined),
                onPressed: () {
                  showOptionsDialog(context);
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  static Color getStatusColor(String status) {
    switch (status) {
      case 'Not Started':
        return AppColors.listOfStatusColorDashboard[0];
      case 'With Applicant':
        return AppColors.listOfStatusColorDashboard[1];
      case 'With Employer':
        return AppColors.listOfStatusColorDashboard[1];
      case 'Processing':
        return AppColors.listOfStatusColorDashboard[1];
      case 'Done':
        return AppColors.listOfStatusColorDashboard[2];
      default:
        return AppColors.kBlackColor;
    }
  }

  static String getStatusText(int index) {
    List<String> statuses = [
      'Not Started',
      'With Applicant',
      'With Employer',
      'Processing',
      'Done',
      'Done',
    ];
    return statuses[index % statuses.length];
  }

  static SizedBox buildStatusBox(int index) {
    return SizedBox(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 7.0),
        child: Container(
          decoration: BoxDecoration(
              color: getStatusColor(getStatusText(index)),
              borderRadius: BorderRadius.circular(10.0)),
          child: Center(child: Text(getStatusText(index))),
        ),
      ),
    );
  }

  static List<String> getIconsForIndex(int index) {
    if (index == 0) {
      return [
        DashboardIcons.smcIcon,
        DashboardIcons.refIcon,
        DashboardIcons.idIcon
      ];
    } else if (index == 1) {
      return [DashboardIcons.dbsIcon, DashboardIcons.idIcon];
    } else if (index == 2) {
      return [DashboardIcons.acIcon];
    } else if (index == 3) {
      return [
        DashboardIcons.idIcon,
        DashboardIcons.acIcon,
      ];
    } else if (index == 4) {
      return [DashboardIcons.idIcon];
    } else if (index == 5) {
      return [DashboardIcons.acIcon];
    } else {
      return [
        DashboardIcons.acIcon,
        DashboardIcons.idIcon,
        DashboardIcons.smcIcon,
        DashboardIcons.refIcon,
        DashboardIcons.dbsIcon,
      ];
    }
  }

  static String getRandomApplicantName() {
    final random = Random();
    return applicantNames[random.nextInt(applicantNames.length)];
  }

  static Widget buildIconCell(int index) {
    List<String> icons = DashboardConstants.getIconsForIndex(index);

    List<Widget> iconWidgets = icons.take(3).map((icon) {
      return SizedBox(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 5.0, vertical: 5.0),
          child: Image.asset(icon),
        ),
      );
    }).toList();

    if (icons.length > 3) {
      iconWidgets.add(
        TextButton(
          onPressed: () {},
          child: Text('+${icons.length - 3}'),
        ),
      );
    }

    return Row(
      children: iconWidgets,
    );
  }
}
