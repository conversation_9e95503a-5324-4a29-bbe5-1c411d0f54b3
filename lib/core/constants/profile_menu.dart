class ProfileMenuAssets {
  static String profileMenuAccountSettingSideBarIcon =
      'assets/profile_menu/account_settings_icon.svg';
  static String profileMenuAccountNotificationSideBarIcon =
      'assets/profile_menu/account_notification_icon.svg';

  static String profileMenuQRCodeImage =
      'assets/profile_menu/qr_code_image.svg';
  static String profileMenuSuccessImage =
      'assets/profile_menu/success_image.png';
}

String kLoginTime = 'Login Time';
String kLoginDevice = 'Login Device';
String kBrowser = 'Browser';
String kLoginDate = 'May 25, 2024 at 4:55pm';

final List<Map<String, String>> loginHistory = [
  {kLoginTime: kLoginDate, kLoginDevice: 'John\' laptop', kBrowser: 'Chrome'},
  {kLoginTime: kLoginDate, kLoginDevice: 'HP', kBrowser: 'Chrome'},
  {kLoginTime: kLoginDate, kLoginDevice: 'HP PC', kBrowser: 'Chrome'},
  {kLoginTime: kLoginDate, kLoginDevice: 'Smith Macbook', kBrowser: 'Chrome'},
  {kLoginTime: kLoginDate, kLoginDevice: 'Smith PC', kBrowser: 'Firefox'},
  {kLoginTime: kLoginDate, kLoginDevice: 'Smith', kBrowser: 'Firefox'},
];
