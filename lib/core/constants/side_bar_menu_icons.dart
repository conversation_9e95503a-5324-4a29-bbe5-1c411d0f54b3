class SideBarMenuIcons {
  static String creditCheckIcon =
      'assets/sidebar_icons/credit_check_icon_sidebar_menu.svg';

  static String dashboardIcon =
      'assets/sidebar_icons/dashboard_icon_sidebar_menu.svg';

  static String dbsCheckIcon =
      'assets/sidebar_icons/dbs_check_icon_sidebar_menu.svg';

  static String idCheckIcon =
      'assets/sidebar_icons/id_check_icon_sidebar_menu.svg';

  static String refCheckIcon =
      'assets/sidebar_icons/ref_check_icon_sidebar_menu.svg';

  static String rightToWorkIcon =
      'assets/sidebar_icons/right_to_work_icon_sidebar_menu.svg';

  static String smcIcon =
      'assets/sidebar_icons/smc_icon_sidebar_menu.svg';
}

class AddApplicantProIcons {
  static String dbsCheckIcon =
      'assets/add_applicant_icons/basic_check.svg';
  static String idCheckIcon = 'assets/add_applicant_icons/id_check.svg';
  static String smcIcon =
      'assets/add_applicant_icons/social_media_screening.svg';
  static String rightToWorkIcon =
      'assets/add_applicant_icons/right_to_work.svg';
  static String refCheckIcon =
      'assets/add_applicant_icons/refernce check.svg';
  static String creditCheckIcon =
      'assets/add_applicant_icons/adverse_right_check.svg';
}

class SideBarMenuIconsMobileView {
  SideBarMenuIconsMobileView(
      {required this.screenTitle, required this.iconTitle});

  final String iconTitle;
  final String screenTitle;
}

List<SideBarMenuIconsMobileView> sideBarMenusMobile = [
  SideBarMenuIconsMobileView(
      screenTitle: 'Dashboard', iconTitle: SideBarMenuIcons.dashboardIcon),
  SideBarMenuIconsMobileView(
      screenTitle: 'DBS Check', iconTitle: SideBarMenuIcons.dbsCheckIcon),
  SideBarMenuIconsMobileView(
      screenTitle: 'Reference Check', iconTitle: SideBarMenuIcons.refCheckIcon),
  SideBarMenuIconsMobileView(
      screenTitle: 'Social Media Screening',
      iconTitle: SideBarMenuIcons.smcIcon),
  SideBarMenuIconsMobileView(
      screenTitle: 'Right to Work',
      iconTitle: SideBarMenuIcons.rightToWorkIcon),
  SideBarMenuIconsMobileView(
      screenTitle: 'Adverse Credit Check',
      iconTitle: SideBarMenuIcons.creditCheckIcon),
  SideBarMenuIconsMobileView(
      screenTitle: 'ID Check', iconTitle: SideBarMenuIcons.idCheckIcon),
  SideBarMenuIconsMobileView(
      screenTitle: 'DBS Check', iconTitle: SideBarMenuIcons.dbsCheckIcon),
];
