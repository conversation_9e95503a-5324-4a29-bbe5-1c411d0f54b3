import 'package:SolidCheck/core/config/design_config.dart';
import 'package:flutter/material.dart';

class ComponentConfig {

  static ButtonStyle get primaryButtonStyle =>
      ElevatedButton.styleFrom(
        backgroundColor: DesignConfig.primaryColor,
        foregroundColor: Colors.white,
        disabledBackgroundColor: Colors.grey.shade300,
        disabledForegroundColor: Colors.grey.shade600,
        elevation: 0,
        shadowColor: Colors.transparent,
        padding: EdgeInsets.symmetric(
          horizontal: DesignConfig.spaceXL,
          vertical: DesignConfig.spaceMD,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignConfig.radiusMD),
        ),
        textStyle: TextStyle(
          fontSize: DesignConfig.fontSizeMD,
          fontWeight: DesignConfig.fontWeightSemiBold,
          letterSpacing: 0.5,
        ),
        animationDuration: const Duration(milliseconds: 200),
      ).copyWith(
        backgroundColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.disabled)) {
            return Colors.grey.shade300;
          }
          if (states.contains(WidgetState.pressed)) {
            return DesignConfig.primaryColor.withValues(alpha: 0.8);
          }
          if (states.contains(WidgetState.hovered)) {
            return DesignConfig.primaryColor.withValues(alpha: 0.9);
          }
          return DesignConfig.primaryColor;
        }),
        elevation: WidgetStateProperty.resolveWith<double>((states) {
          if (states.contains(WidgetState.disabled)) return 0;
          if (states.contains(WidgetState.pressed)) return 1;
          if (states.contains(WidgetState.hovered)) return 2;
          return 0;
        }),
        overlayColor: WidgetStateProperty.resolveWith<Color?>((states) {
          if (states.contains(WidgetState.pressed)) {
            return Colors.white.withValues(alpha: 0.1);
          }
          if (states.contains(WidgetState.hovered)) {
            return Colors.white.withValues(alpha: 0.05);
          }
          return null;
        }),
      );

  static ButtonStyle get secondaryButtonStyle =>
      OutlinedButton.styleFrom(
        foregroundColor: DesignConfig.primaryColor,
        backgroundColor: Colors.transparent,
        disabledForegroundColor: Colors.grey.shade400,
        side: BorderSide(color: DesignConfig.primaryColor, width: 1.5),
        padding: EdgeInsets.symmetric(
          horizontal: DesignConfig.spaceXL,
          vertical: DesignConfig.spaceMD,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignConfig.radiusMD),
        ),
        textStyle: TextStyle(
          fontSize: DesignConfig.fontSizeMD,
          fontWeight: DesignConfig.fontWeightSemiBold,
          letterSpacing: 0.5,
        ),
        animationDuration: const Duration(milliseconds: 200),
      ).copyWith(
        backgroundColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.disabled)) return Colors.grey.shade50;
          if (states.contains(WidgetState.pressed)) {
            return DesignConfig.primaryColor.withValues(alpha: 0.1);
          }
          if (states.contains(WidgetState.hovered)) {
            return DesignConfig.primaryColor.withValues(alpha: 0.05);
          }
          return Colors.transparent;
        }),
        foregroundColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.disabled)) {
            return Colors.grey.shade400;
          }
          if (states.contains(WidgetState.pressed)) {
            return DesignConfig.primaryColor.withValues(alpha: 0.8);
          }
          return DesignConfig.primaryColor;
        }),
        side: WidgetStateProperty.resolveWith<BorderSide>((states) {
          if (states.contains(WidgetState.disabled)) {
            return BorderSide(color: Colors.grey.shade300, width: 1.5);
          }
          if (states.contains(WidgetState.pressed)) {
            return BorderSide(
              color: DesignConfig.primaryColor.withValues(alpha: 0.8),
              width: 2,
            );
          }
          if (states.contains(WidgetState.hovered)) {
            return BorderSide(color: DesignConfig.primaryColor, width: 2);
          }
          return BorderSide(color: DesignConfig.primaryColor, width: 1.5);
        }),
      );

  static ButtonStyle get textButtonStyle =>
      TextButton.styleFrom(
        foregroundColor: DesignConfig.primaryColor,
        disabledForegroundColor: Colors.grey.shade400,
        backgroundColor: Colors.transparent,
        padding: EdgeInsets.symmetric(
          horizontal: DesignConfig.spaceLG,
          vertical: DesignConfig.spaceSM,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(DesignConfig.radiusSM),
        ),
        textStyle: TextStyle(
          fontSize: DesignConfig.fontSizeMD,
          fontWeight: DesignConfig.fontWeightMedium,
          letterSpacing: 0.3,
        ),
        animationDuration: const Duration(milliseconds: 200),
      ).copyWith(
        backgroundColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.disabled)) return Colors.transparent;
          if (states.contains(WidgetState.pressed)) {
            return DesignConfig.primaryColor.withValues(alpha: 0.1);
          }
          if (states.contains(WidgetState.hovered)) {
            return DesignConfig.primaryColor.withValues(alpha: 0.05);
          }
          return Colors.transparent;
        }),
        foregroundColor: WidgetStateProperty.resolveWith<Color>((states) {
          if (states.contains(WidgetState.disabled)) {
            return Colors.grey.shade400;
          }
          if (states.contains(WidgetState.pressed)) {
            return DesignConfig.primaryColor.withValues(alpha: 0.8);
          }
          return DesignConfig.primaryColor;
        }),
        overlayColor: WidgetStateProperty.resolveWith<Color?>((states) {
          if (states.contains(WidgetState.pressed)) {
            return DesignConfig.primaryColor.withValues(alpha: 0.1);
          }
          if (states.contains(WidgetState.hovered)) {
            return DesignConfig.primaryColor.withValues(alpha: 0.05);
          }
          return null;
        }),
      );

  static ButtonStyle get compactButtonStyle => primaryButtonStyle.copyWith(
    padding: WidgetStateProperty.all(
      EdgeInsets.symmetric(
        horizontal: DesignConfig.spaceMD,
        vertical: DesignConfig.spaceSM,
      ),
    ),
    textStyle: WidgetStateProperty.all(
      TextStyle(
        fontSize: DesignConfig.fontSizeSM,
        fontWeight: DesignConfig.fontWeightSemiBold,
        letterSpacing: 0.3,
      ),
    ),
    minimumSize: WidgetStateProperty.all(const Size(80, 36)),
  );

  static ButtonStyle get compactSecondaryButtonStyle =>
      secondaryButtonStyle.copyWith(
        padding: WidgetStateProperty.all(
          EdgeInsets.symmetric(
            horizontal: DesignConfig.spaceMD,
            vertical: DesignConfig.spaceSM,
          ),
        ),
        textStyle: WidgetStateProperty.all(
          TextStyle(
            fontSize: DesignConfig.fontSizeSM,
            fontWeight: DesignConfig.fontWeightSemiBold,
            letterSpacing: 0.3,
          ),
        ),
        minimumSize: WidgetStateProperty.all(const Size(80, 36)),
      );

  static ButtonStyle get largeButtonStyle => primaryButtonStyle.copyWith(
    padding: WidgetStateProperty.all(
      EdgeInsets.symmetric(
        horizontal: DesignConfig.space2XL,
        vertical: DesignConfig.spaceLG,
      ),
    ),
    textStyle: WidgetStateProperty.all(
      TextStyle(
        fontSize: DesignConfig.fontSizeLG,
        fontWeight: DesignConfig.fontWeightSemiBold,
        letterSpacing: 0.5,
      ),
    ),
    minimumSize: WidgetStateProperty.all(const Size(120, 48)),
  );

  static ButtonStyle get dangerButtonStyle => primaryButtonStyle.copyWith(
    backgroundColor: WidgetStateProperty.resolveWith<Color>((states) {
      if (states.contains(WidgetState.disabled)) return Colors.grey.shade300;
      if (states.contains(WidgetState.pressed)) {
        return DesignConfig.errorColor.withValues(alpha: 0.8);
      }
      if (states.contains(WidgetState.hovered)) {
        return DesignConfig.errorColor.withValues(alpha: 0.9);
      }
      return DesignConfig.errorColor;
    }),
  );

  static ButtonStyle get successButtonStyle => primaryButtonStyle.copyWith(
    backgroundColor: WidgetStateProperty.resolveWith<Color>((states) {
      if (states.contains(WidgetState.disabled)) return Colors.grey.shade300;
      if (states.contains(WidgetState.pressed)) {
        return DesignConfig.successColor.withValues(alpha: 0.8);
      }
      if (states.contains(WidgetState.hovered)) {
        return DesignConfig.successColor.withValues(alpha: 0.9);
      }
      return DesignConfig.successColor;
    }),
  );

  static BoxDecoration get cardDecoration => BoxDecoration(
    color: DesignConfig.cardBackgroundColor,
    borderRadius: BorderRadius.circular(DesignConfig.radiusMD),
    border: Border.all(color: DesignConfig.primaryBorderColor, width: 1),
    boxShadow: DesignConfig.shadowSM,
  );

  static BoxDecoration get elevatedCardDecoration => BoxDecoration(
    color: DesignConfig.cardBackgroundColor,
    borderRadius: BorderRadius.circular(DesignConfig.radiusLG),
    boxShadow: DesignConfig.shadowMD,
  );

  static InputDecoration getInputDecoration({
    required String hintText,
    String? labelText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    bool isError = false,
  }) => InputDecoration(
    labelText: labelText,
    hintText: hintText,
    hintStyle: TextStyle(
      color: DesignConfig.hintTextColor,
      fontSize: DesignConfig.fontSizeMD,
    ),
    prefixIcon: prefixIcon != null
        ? IconTheme(
            data: IconThemeData(
              color: DesignConfig.primaryIconColor,
              size: DesignConfig.iconSizeSM,
            ),
            child: prefixIcon,
          )
        : null,
    suffixIcon: suffixIcon != null
        ? IconTheme(
            data: IconThemeData(
              color: DesignConfig.primaryIconColor,
              size: DesignConfig.iconSizeSM,
            ),
            child: suffixIcon,
          )
        : null,
    filled: true,
    fillColor: DesignConfig.primaryBackgroundColor,
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(DesignConfig.radiusMD),
      borderSide: BorderSide(
        color: DesignConfig.primaryBorderColor,
        width: 1,
      ),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(DesignConfig.radiusMD),
      borderSide: BorderSide(
        color: DesignConfig.primaryBorderColor,
        width: 1,
      ),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(DesignConfig.radiusMD),
      borderSide: BorderSide(
        color: DesignConfig.focusedBorderColor,
        width: 2,
      ),
    ),
    errorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(DesignConfig.radiusMD),
      borderSide: BorderSide(
        color: DesignConfig.errorBorderColor,
        width: 1,
      ),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(DesignConfig.radiusMD),
      borderSide: BorderSide(
        color: DesignConfig.errorBorderColor,
        width: 2,
      ),
    ),
    contentPadding: EdgeInsets.symmetric(
      horizontal: DesignConfig.spaceLG,
      vertical: DesignConfig.spaceMD,
    ),
  );

  static Widget getIcon(
    IconData iconData, {
    double? size,
    Color? color,
    String? semanticLabel,
  }) => Icon(
    iconData,
    size: size ?? DesignConfig.iconSizeSM,
    color: color ?? DesignConfig.primaryIconColor,
    semanticLabel: semanticLabel,
  );

  static Widget getIconButton(
    IconData iconData, {
    required VoidCallback onPressed,
    double? size,
    Color? color,
    String? tooltip,
  }) => IconButton(
    onPressed: onPressed,
    icon: getIcon(iconData, size: size, color: color),
    tooltip: tooltip,
    padding: EdgeInsets.all(DesignConfig.spaceSM),
    constraints: BoxConstraints(
      minWidth: DesignConfig.iconSizeLG + DesignConfig.spaceLG,
      minHeight: DesignConfig.iconSizeLG + DesignConfig.spaceLG,
    ),
  );

  static Widget getSectionHeader(
    String title, {
    IconData? icon,
    Widget? trailing,
    bool isMobile = false,
  }) => Row(
    children: [
      if (icon != null) ...[
        getIcon(icon, size: DesignConfig.iconSizeSM),
        SizedBox(width: DesignConfig.spaceSM),
      ],
      Expanded(
        child: Text(
          title,
          style: TextStyle(
            color: DesignConfig.primaryTextColor,
            fontWeight: DesignConfig.fontWeightSemiBold,
            fontSize: isMobile
                ? DesignConfig.fontSizeMD
                : DesignConfig.fontSizeLG,
          ),
        ),
      ),
      if (trailing != null) trailing,
    ],
  );

  static Widget getBadge(
    String text, {
    Color? backgroundColor,
    Color? textColor,
    bool isSmall = false,
  }) => Container(
    padding: EdgeInsets.symmetric(
      horizontal: isSmall ? DesignConfig.spaceSM : DesignConfig.spaceMD,
      vertical: isSmall ? DesignConfig.spaceXS : DesignConfig.spaceSM,
    ),
    decoration: BoxDecoration(
      color:
          backgroundColor ?? DesignConfig.primaryColor.withValues(alpha: 0.1),
      borderRadius: BorderRadius.circular(DesignConfig.radiusLG),
    ),
    child: Text(
      text,
      style: TextStyle(
        color: textColor ?? DesignConfig.primaryColor,
        fontSize: isSmall ? DesignConfig.fontSizeXS : DesignConfig.fontSizeSM,
        fontWeight: DesignConfig.fontWeightSemiBold,
      ),
    ),
  );


  static Widget get divider => Divider(
    color: DesignConfig.primaryBorderColor,
    thickness: 1,
    height: DesignConfig.spaceLG,
  );

  static Widget get verticalDivider => VerticalDivider(
    color: DesignConfig.primaryBorderColor,
    thickness: 1,
    width: DesignConfig.spaceLG,
  );

  static Widget getPrimaryButton({
    required String text,
    required VoidCallback? onPressed,
    IconData? icon,
    bool isLoading = false,
    bool isCompact = false,
    bool isLarge = false,
  }) {
    final buttonStyle = isCompact
        ? compactButtonStyle
        : isLarge
        ? largeButtonStyle
        : primaryButtonStyle;

    if (icon != null) {
      return ElevatedButton.icon(
        onPressed: isLoading ? null : onPressed,
        style: buttonStyle,
        icon: isLoading
            ? smallLoadingIndicator
            : Icon(icon, size: DesignConfig.iconSizeSM),
        label: Text(text),
      );
    }

    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: buttonStyle,
      child: isLoading
          ? Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                smallLoadingIndicator,
                SizedBox(width: DesignConfig.spaceSM),
                Text(text),
              ],
            )
          : Text(text),
    );
  }

  static Widget getSecondaryButton({
    required String text,
    required VoidCallback? onPressed,
    IconData? icon,
    bool isCompact = false,
  }) {
    final buttonStyle = isCompact
        ? compactSecondaryButtonStyle
        : secondaryButtonStyle;

    if (icon != null) {
      return OutlinedButton.icon(
        onPressed: onPressed,
        style: buttonStyle,
        icon: Icon(icon, size: DesignConfig.iconSizeSM),
        label: Text(text),
      );
    }

    return OutlinedButton(
      onPressed: onPressed,
      style: buttonStyle,
      child: Text(text),
    );
  }

  static Widget getTextButton({
    required String text,
    required VoidCallback? onPressed,
    IconData? icon,
  }) {
    if (icon != null) {
      return TextButton.icon(
        onPressed: onPressed,
        style: textButtonStyle,
        icon: Icon(icon, size: DesignConfig.iconSizeSM),
        label: Text(text),
      );
    }

    return TextButton(
      onPressed: onPressed,
      style: textButtonStyle,
      child: Text(text),
    );
  }

  static Widget get loadingIndicator => CircularProgressIndicator(
    valueColor: AlwaysStoppedAnimation<Color>(DesignConfig.primaryColor),
    strokeWidth: 2,
  );

  static Widget get smallLoadingIndicator => SizedBox(
    width: DesignConfig.iconSizeSM,
    height: DesignConfig.iconSizeSM,
    child: CircularProgressIndicator(
      valueColor: AlwaysStoppedAnimation<Color>(DesignConfig.primaryColor),
      strokeWidth: 2,
    ),
  );
}
