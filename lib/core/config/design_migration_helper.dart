import 'package:SolidCheck/core/config/component_config.dart';
import 'package:SolidCheck/core/config/design_config.dart';
import 'package:flutter/material.dart';

class DesignMigrationHelper {
  static final Map<Color, Color> colorMigrations = {
    const Color(0xFF1E88E5): DesignConfig.primaryColor,
    const Color(0xFF0D47A1): DesignConfig.secondaryColor,
    Colors.black: DesignConfig.primaryTextColor,
    Colors.black87: DesignConfig.secondaryTextColor,
    Colors.grey: DesignConfig.hintTextColor,
    Colors.white: DesignConfig.primaryBackgroundColor,
    Colors.red: DesignConfig.errorColor,
    Colors.green: DesignConfig.successColor,
    Colors.orange: DesignConfig.warningColor,
  };

  static Color getMigratedColor(Color legacyColor) {
    return colorMigrations[legacyColor] ?? legacyColor;
  }

  static final Map<double, double> spacingMigrations = {
    4.0: DesignConfig.spaceXS,
    8.0: DesignConfig.spaceSM,
    12.0: DesignConfig.spaceMD,
    16.0: DesignConfig.spaceLG,
    20.0: DesignConfig.spaceXL,
    24.0: DesignConfig.space2XL,
    32.0: DesignConfig.space3XL,
  };

  static double getMigratedSpacing(double legacySpacing) {
    return spacingMigrations[legacySpacing] ?? legacySpacing;
  }

  static final Map<double, double> fontSizeMigrations = {
    10.0: DesignConfig.fontSizeXS,
    12.0: DesignConfig.fontSizeSM,
    14.0: DesignConfig.fontSizeMD,
    16.0: DesignConfig.fontSizeLG,
    18.0: DesignConfig.fontSizeXL,
    20.0: DesignConfig.fontSize2XL,
    24.0: DesignConfig.fontSize3XL,
  };

  static double getMigratedFontSize(double legacyFontSize) {
    return fontSizeMigrations[legacyFontSize] ?? legacyFontSize;
  }

  static ButtonStyle migratePrimaryButtonStyle({
    Color? backgroundColor,
    EdgeInsetsGeometry? padding,
    BorderRadius? borderRadius,
  }) {
    return ComponentConfig.primaryButtonStyle.copyWith(
      backgroundColor: backgroundColor != null
          ? WidgetStateProperty.all(getMigratedColor(backgroundColor))
          : null,
      padding: padding != null ? WidgetStateProperty.all(padding) : null,
      shape: borderRadius != null
          ? WidgetStateProperty.all(
              RoundedRectangleBorder(borderRadius: borderRadius),
            )
          : null,
    );
  }

  static ButtonStyle migrateSecondaryButtonStyle({
    Color? borderColor,
    EdgeInsetsGeometry? padding,
    BorderRadius? borderRadius,
  }) {
    return ComponentConfig.secondaryButtonStyle.copyWith(
      side: borderColor != null
          ? WidgetStateProperty.all(
              BorderSide(color: getMigratedColor(borderColor)),
            )
          : null,
      padding: padding != null ? WidgetStateProperty.all(padding) : null,
      shape: borderRadius != null
          ? WidgetStateProperty.all(
              RoundedRectangleBorder(borderRadius: borderRadius),
            )
          : null,
    );
  }

  static InputDecoration migrateInputDecoration({
    String? hintText,
    String? labelText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    Color? borderColor,
    Color? focusedBorderColor,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? contentPadding,
  }) {
    return ComponentConfig.getInputDecoration(
      hintText: hintText ?? '',
      labelText: labelText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
    );
  }

  static TextStyle migrateTextStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? height,
    double? letterSpacing,
  }) {
    TextStyle baseStyle;

    if (fontSize != null) {
      if (fontSize >= 24) {
        baseStyle = DesignConfig.headingLarge;
      } else if (fontSize >= 18) {
        baseStyle = DesignConfig.headingMedium;
      } else if (fontSize >= 16) {
        baseStyle = DesignConfig.headingSmall;
      } else if (fontSize >= 14) {
        baseStyle = DesignConfig.bodyMedium;
      } else if (fontSize >= 12) {
        baseStyle = DesignConfig.bodySmall;
      } else {
        baseStyle = DesignConfig.caption;
      }
    } else {
      baseStyle = DesignConfig.bodyMedium;
    }

    return baseStyle.copyWith(
      fontSize: fontSize != null ? getMigratedFontSize(fontSize) : null,
      fontWeight: fontWeight,
      color: color != null ? getMigratedColor(color) : null,
      height: height,
      letterSpacing: letterSpacing,
    );
  }

  static BoxDecoration migrateContainerDecoration({
    Color? color,
    BorderRadius? borderRadius,
    Border? border,
    List<BoxShadow>? boxShadow,
  }) {
    return ComponentConfig.cardDecoration.copyWith(
      color: color != null ? getMigratedColor(color) : null,
      borderRadius: borderRadius,
      border: border,
      boxShadow: boxShadow,
    );
  }

  static Widget migrateIcon(
    IconData iconData, {
    double? size,
    Color? color,
    String? semanticLabel,
  }) {
    return ComponentConfig.getIcon(
      iconData,
      size: size != null ? getMigratedSpacing(size) : null,
      color: color != null ? getMigratedColor(color) : null,
      semanticLabel: semanticLabel,
    );
  }

  static double migrateResponsiveSpacing(
    BuildContext context,
    double mobileValue,
    double tabletValue,
    double desktopValue,
  ) {
    final screenWidth = MediaQuery.of(context).size.width;
    double baseValue;

    if (screenWidth < DesignConfig.mobileBreakpoint) {
      baseValue = mobileValue;
    } else if (screenWidth < DesignConfig.tabletBreakpoint) {
      baseValue = tabletValue;
    } else {
      baseValue = desktopValue;
    }

    return getMigratedSpacing(baseValue);
  }

  static double migrateResponsiveFontSize(
    BuildContext context,
    double mobileValue,
    double tabletValue,
    double desktopValue,
  ) {
    final screenWidth = MediaQuery.of(context).size.width;
    double baseValue;

    if (screenWidth < DesignConfig.mobileBreakpoint) {
      baseValue = mobileValue;
    } else if (screenWidth < DesignConfig.tabletBreakpoint) {
      baseValue = tabletValue;
    } else {
      baseValue = desktopValue;
    }

    return getMigratedFontSize(baseValue);
  }

  static bool isLegacyColor(Color color) {
    return colorMigrations.containsKey(color);
  }

  static bool isLegacySpacing(double spacing) {
    return spacingMigrations.containsKey(spacing);
  }

  static bool isLegacyFontSize(double fontSize) {
    return fontSizeMigrations.containsKey(fontSize);
  }

  static Map<String, List<String>> generateMigrationReport(Widget widget) {
    return {
      'colors': ['Found legacy colors that can be migrated'],
      'spacing': ['Found legacy spacing that can be migrated'],
      'typography': ['Found legacy text styles that can be migrated'],
      'components': ['Found legacy components that can be migrated'],
    };
  }
}
