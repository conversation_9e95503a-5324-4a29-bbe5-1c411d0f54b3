import 'package:SolidCheck/core/theme/colors.dart';
import 'package:flutter/material.dart';

class DesignConfig {
  static Color primaryColor = AppColors.kBlueColor;
  static Color secondaryColor = AppColors.kBlueDarkColor;
  static Color primaryTextColor = Colors.black;
  static Color secondaryTextColor = Colors.black87;
  static Color hintTextColor = Colors.grey;
  static Color disabledTextColor = Colors.grey;
  static Color primaryBackgroundColor = Colors.white;
  static Color secondaryBackgroundColor = const Color(0xFFF5F5F5);
  static Color cardBackgroundColor = Colors.white;
  static Color primaryBorderColor = const Color(0xFFE0E0E0);
  static Color focusedBorderColor = primaryColor;
  static Color errorBorderColor = Colors.red;
  static Color successColor = Colors.green;
  static Color warningColor = Colors.orange;
  static Color errorColor = Colors.red;
  static Color infoColor = primaryColor;
  static double spaceXS = 4.0;
  static double spaceSM = 8.0;
  static double spaceMD = 12.0;
  static double spaceLG = 16.0;
  static double spaceXL = 20.0;
  static double space2XL = 24.0;
  static double space3XL = 32.0;
  static double cardPadding = spaceLG;
  static double sectionSpacing = spaceLG;
  static double itemSpacing = spaceSM;
  static double buttonSpacing = spaceMD;
  static double fontSizeXS = 10.0;
  static double fontSizeSM = 12.0;
  static double fontSizeMD = 14.0;
  static double fontSizeLG = 16.0;
  static double fontSizeXL = 18.0;
  static double fontSize2XL = 20.0;
  static double fontSize3XL = 24.0;
  static FontWeight fontWeightLight = FontWeight.w300;
  static FontWeight fontWeightRegular = FontWeight.w400;
  static FontWeight fontWeightMedium = FontWeight.w500;
  static FontWeight fontWeightSemiBold = FontWeight.w600;
  static FontWeight fontWeightBold = FontWeight.w700;
  
  /// Text styles
  static TextStyle get headingLarge => TextStyle(
    fontSize: fontSize3XL,
    fontWeight: fontWeightBold,
    color: primaryTextColor,
  );
  
  static TextStyle get headingMedium => TextStyle(
    fontSize: fontSizeXL,
    fontWeight: fontWeightSemiBold,
    color: primaryTextColor,
  );
  
  static TextStyle get headingSmall => TextStyle(
    fontSize: fontSizeLG,
    fontWeight: fontWeightSemiBold,
    color: primaryTextColor,
  );
  
  static TextStyle get bodyLarge => TextStyle(
    fontSize: fontSizeLG,
    fontWeight: fontWeightRegular,
    color: primaryTextColor,
  );
  
  static TextStyle get bodyMedium => TextStyle(
    fontSize: fontSizeMD,
    fontWeight: fontWeightRegular,
    color: primaryTextColor,
  );
  
  static TextStyle get bodySmall => TextStyle(
    fontSize: fontSizeSM,
    fontWeight: fontWeightRegular,
    color: secondaryTextColor,
  );
  
  static TextStyle get caption => TextStyle(
    fontSize: fontSizeXS,
    fontWeight: fontWeightRegular,
    color: hintTextColor,
  );

  static double iconSizeXS = 12.0;
  static double iconSizeSM = 16.0;
  static double iconSizeMD = 20.0;
  static double iconSizeLG = 24.0;
  static double iconSizeXL = 32.0;

  static Color primaryIconColor = primaryColor;
  static Color secondaryIconColor = hintTextColor;
  static Color disabledIconColor = const Color(0xFFBDBDBD);

  static double radiusXS = 4.0;
  static double radiusSM = 6.0;
  static double radiusMD = 8.0;
  static double radiusLG = 12.0;
  static double radiusXL = 16.0;
  static double radiusRound = 50.0;

  static List<BoxShadow> get shadowSM => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.03),
      blurRadius: 4,
      offset: const Offset(0, 1),
    ),
  ];

  static List<BoxShadow> get shadowMD => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.05),
      blurRadius: 8,
      offset: const Offset(0, 2),
    ),
  ];

  static List<BoxShadow> get shadowLG => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.08),
      blurRadius: 16,
      offset: const Offset(0, 4),
    ),
  ];

  static double mobileBreakpoint = 768.0;
  static double tabletBreakpoint = 1024.0;
  static double desktopBreakpoint = 1440.0;

  static double getResponsiveSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < mobileBreakpoint) {
      return baseSpacing * 0.75;
    } else if (screenWidth < tabletBreakpoint) {
      return baseSpacing;
    } else {
      return baseSpacing * 1.25;
    }
  }

  static double getResponsiveFontSize(
    BuildContext context,
    double baseFontSize,
  ) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < mobileBreakpoint) {
      return baseFontSize * 0.9;
    } else {
      return baseFontSize;
    }
  }
}
