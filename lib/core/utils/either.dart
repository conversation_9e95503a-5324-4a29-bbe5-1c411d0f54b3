abstract class Either<L, R> {
  Either();

  bool get isLeft;
  bool get isRight;

  L? get left;
  R? get right;

  T fold<T>(T Function(L left) ifLeft, T Function(R right) ifRight);
}

class Left<L, R> extends Either<L, R> {
  final L _value;

  Left(this._value);

  @override
  bool get isLeft => true;

  @override
  bool get isRight => false;

  @override
  L get left => _value;

  @override
  R? get right => null;

  @override
  T fold<T>(T Function(L left) ifLeft, T Function(R right) ifRight) {
    return ifLeft(_value);
  }

  @override
  bool operator ==(Object other) {
    return other is Left && other._value == _value;
  }

  @override
  int get hashCode => _value.hashCode;

  @override
  String toString() => 'Left($_value)';
}

class Right<L, R> extends Either<L, R> {
  final R _value;

  Right(this._value);

  @override
  bool get isLeft => false;

  @override
  bool get isRight => true;

  @override
  L? get left => null;

  @override
  R get right => _value;

  @override
  T fold<T>(T Function(L left) ifLeft, T Function(R right) ifRight) {
    return ifRight(_value);
  }

  @override
  bool operator ==(Object other) {
    return other is Right && other._value == _value;
  }

  @override
  int get hashCode => _value.hashCode;

  @override
  String toString() => 'Right($_value)';
}
