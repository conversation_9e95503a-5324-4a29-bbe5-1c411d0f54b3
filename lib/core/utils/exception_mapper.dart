import 'package:SolidCheck/features/auth/domain/failures/auth_failure.dart';

class ExceptionMapper {
  static AuthFailure mapToAuthFailure(Exception exception) {
    final message = exception.toString();
    
    if (_isNetworkError(message)) return NetworkFailure(message);
    if (_isUnauthorizedError(message)) return InvalidCredentialsFailure(message);
    if (_isTwoFactorError(message)) return TwoFactorFailure(message);
    if (_isTokenError(message)) return TokenFailure(message);
    if (_isServerError(message)) return ServerFailure(message);
    
    return UnknownFailure(message);
  }

  static bool _isNetworkError(String message) {
    return message.contains('network') || 
           message.contains('connection') ||
           message.contains('timeout');
  }

  static bool _isUnauthorizedError(String message) {
    return message.contains('401') || 
           message.contains('unauthorized') ||
           message.contains('invalid credentials');
  }

  static bool _isTwoFactorError(String message) {
    return message.contains('2FA') || 
           message.contains('two factor') ||
           message.contains('PIN');
  }

  static bool _isTokenError(String message) {
    return message.contains('token') || 
           message.contains('expired') ||
           message.contains('refresh');
  }

  static bool _isServerError(String message) {
    return message.contains('500') || 
           message.contains('server') ||
           message.contains('internal');
  }
}
