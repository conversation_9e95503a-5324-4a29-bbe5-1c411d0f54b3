import 'package:flutter/material.dart';

class ResponsiveHelper {
  static double mobileMaxWidth = 450;
  static double tabletMaxWidth = 800;
  static double desktopMaxWidth = 1920;

  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width <= mobileMaxWidth;
  }

  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width > mobileMaxWidth && width <= tabletMaxWidth;
  }

  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width > tabletMaxWidth;
  }

  static bool isLargerThanMobile(BuildContext context) {
    return MediaQuery.of(context).size.width > mobileMaxWidth;
  }

  static bool isLargerThanTablet(BuildContext context) {
    return MediaQuery.of(context).size.width > tabletMaxWidth;
  }

  static T getResponsiveValue<T>(
    BuildContext context, {
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    if (isDesktop(context) && desktop != null) {
      return desktop;
    } else if (isTablet(context) && tablet != null) {
      return tablet;
    } else {
      return mobile;
    }
  }

  static EdgeInsets getResponsivePadding(BuildContext context) {
    if (isMobile(context)) {
      return const EdgeInsets.all(16.0);
    } else if (isTablet(context)) {
      return const EdgeInsets.all(24.0);
    } else {
      return const EdgeInsets.all(32.0);
    }
  }

  static EdgeInsets getResponsiveMargin(BuildContext context) {
    if (isMobile(context)) {
      return const EdgeInsets.all(8.0);
    } else if (isTablet(context)) {
      return const EdgeInsets.all(16.0);
    } else {
      return const EdgeInsets.all(24.0);
    }
  }

  static double getResponsiveFontSize(
    BuildContext context, {
    required double mobile,
    double? tablet,
    double? desktop,
  }) {
    return getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet ?? mobile * 1.1,
      desktop: desktop ?? mobile * 1.2,
    );
  }

  static double getResponsiveWidth(
    BuildContext context, {
    required double mobile,
    double? tablet,
    double? desktop,
  }) {
    return getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet ?? mobile * 1.2,
      desktop: desktop ?? mobile * 1.5,
    );
  }

  static double getResponsiveHeight(
    BuildContext context, {
    required double mobile,
    double? tablet,
    double? desktop,
  }) {
    return getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet ?? mobile * 1.1,
      desktop: desktop ?? mobile * 1.2,
    );
  }

  static int getResponsiveColumns(BuildContext context) {
    if (isMobile(context)) {
      return 1;
    } else if (isTablet(context)) {
      return 2;
    } else {
      return 3;
    }
  }

  static int getResponsiveCrossAxisCount(
    BuildContext context, {
    int mobile = 1,
    int tablet = 2,
    int desktop = 3,
  }) {
    return getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }

  static double getResponsiveAspectRatio(BuildContext context) {
    if (isMobile(context)) {
      return 16 / 9;
    } else if (isTablet(context)) {
      return 4 / 3;
    } else {
      return 16 / 10;
    }
  }

  static BoxConstraints getResponsiveConstraints(BuildContext context) {
    if (isMobile(context)) {
      return const BoxConstraints(maxWidth: 400, minHeight: 200);
    } else if (isTablet(context)) {
      return const BoxConstraints(maxWidth: 600, minHeight: 300);
    } else {
      return const BoxConstraints(maxWidth: 800, minHeight: 400);
    }
  }

  static String getScreenSizeCategory(BuildContext context) {
    if (isMobile(context)) {
      return 'mobile';
    } else if (isTablet(context)) {
      return 'tablet';
    } else {
      return 'desktop';
    }
  }

  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  static bool isPortrait(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }

  static EdgeInsets getSafeAreaPadding(BuildContext context) {
    return MediaQuery.of(context).padding;
  }

  static double getScreenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  static double getScreenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  static double getResponsiveDialogWidth(BuildContext context) {
    final screenWidth = getScreenWidth(context);
    if (isMobile(context)) {
      return screenWidth * 0.9;
    } else if (isTablet(context)) {
      return screenWidth * 0.7;
    } else {
      return screenWidth * 0.5;
    }
  }

  static double getResponsiveSidebarWidth(BuildContext context) {
    if (isMobile(context)) {
      return getScreenWidth(context) * 0.8;
    } else if (isTablet(context)) {
      return 300;
    } else {
      return 350;
    }
  }
}

extension ResponsiveExtension on BuildContext {
  bool get isMobile => ResponsiveHelper.isMobile(this);
  bool get isTablet => ResponsiveHelper.isTablet(this);
  bool get isDesktop => ResponsiveHelper.isDesktop(this);
  bool get isLargerThanMobile => ResponsiveHelper.isLargerThanMobile(this);
  bool get isLargerThanTablet => ResponsiveHelper.isLargerThanTablet(this);
  bool get isLandscape => ResponsiveHelper.isLandscape(this);
  bool get isPortrait => ResponsiveHelper.isPortrait(this);
  double get screenWidth => ResponsiveHelper.getScreenWidth(this);
  double get screenHeight => ResponsiveHelper.getScreenHeight(this);
  EdgeInsets get responsivePadding =>
      ResponsiveHelper.getResponsivePadding(this);
  EdgeInsets get responsiveMargin => ResponsiveHelper.getResponsiveMargin(this);
  EdgeInsets get safeAreaPadding => ResponsiveHelper.getSafeAreaPadding(this);

  int get responsiveColumns => ResponsiveHelper.getResponsiveColumns(this);
  double get responsiveAspectRatio =>
      ResponsiveHelper.getResponsiveAspectRatio(this);
  BoxConstraints get responsiveConstraints =>
      ResponsiveHelper.getResponsiveConstraints(this);

  String get screenSizeCategory => ResponsiveHelper.getScreenSizeCategory(this);
}
