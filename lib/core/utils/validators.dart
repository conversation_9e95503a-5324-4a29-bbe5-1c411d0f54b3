class Validators {
  static final String _emailPattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static final String _pinPattern = r'^\d{6}$';

  bool isValidEmail(String email) {
    if (email.isEmpty) return false;
    return RegExp(_emailPattern).hasMatch(email);
  }

  bool isValidPin(String pin) {
    if (pin.isEmpty) return false;
    return RegExp(_pinPattern).hasMatch(pin);
  }

  bool isNotEmpty(String value) => value.trim().isNotEmpty;

  bool hasMinLength(String value, int minLength) => value.length >= minLength;

  bool hasMaxLength(String value, int maxLength) => value.length <= maxLength;
}
