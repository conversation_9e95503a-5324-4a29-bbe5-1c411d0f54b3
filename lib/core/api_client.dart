import 'package:dio/dio.dart';
import 'network/http_client.dart';

class ApiClient {
  final HttpClient _httpClient;

  ApiClient(this._httpClient);

  Future<dynamic> get(String path, {Map<String, String>? queryParameters}) async {
    return await _httpClient.fetchData(path, params: queryParameters);
  }

  Future<dynamic> post(String path, {dynamic data}) async {
    return await _httpClient.postData(path, data);
  }

  Future<dynamic> put(String path, {dynamic data}) async {
    return await _httpClient.putData(path, data);
  }
}
