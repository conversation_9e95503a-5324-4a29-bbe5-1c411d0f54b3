import 'package:flutter/material.dart';

class AppColors {
  static Color applicationOverviewDivColor = const Color(0XFFFAF9F9);
  static Color applicationStatusDivColor1 = const Color(0XFFFFDC97);
  static Color applicationStatusDivColor2 = const Color(0XFFFFB697);
  static Color consentBodyDivColor = const Color(0XFF757575);
  static Color dividercolor = const Color(0XFF727272);
  static Color hintTextLoginFieldColor = const Color(0XFF8E8D8D);
  static Color kAppBarWhiteColorAndFieldActiveStateColor =
      const Color(0XFFFDFDFD);
  static Color kBlackColor = const Color(0xFF000000);
  static Color kInactiveStep = const Color(0xFFDAD9D9);
  static Color kNavItem = const Color(0xFF255A6B);
  static Color kNavItemSelected = const Color(0xFFEEFBFF);
  static Color kBlueColor = const Color(0xFF1E88E5);
  static Color kBlueDarkColor = const Color(0xFF0D47A1);
  static Color kCheckBoxLoginColor = const Color(0XFF58D7FF);
  static Color kDashboardSearchFieldColor = const Color(0XFFE6EDF3);
  static Color kQuestionContainerColor = const Color(0XFFF1F9FF);
  static Color kQuestionContainerButtonColor = const Color(0XFFB0C7D8);
  static Color kQuestionTextColor = const Color(0XFF282828);
  static Color kBadgesColor = const Color(0xFFCAFFB2);
  static Color kFillColor = const Color(0xFFEEFBFF);

  static Color kGreyColor = const Color(0xFF3A4750);
  static List<Color> kLoginBoxGradientColor = [
    kBlueColor,
    kBlueDarkColor,
  ];

  static Color kLoginScreenBlueDivColor = const Color(0XFF878686);
  static List<Color> kMobLoginBoxGradientColor = [
    kBlueDarkColor,
    kBlueColor,
  ];

  static Color kRedColor = const Color(0xFFD72323);
  static Color kRedShadeBoxColor = const Color(0XFFFFEEED);
  static Color kRedTextColor = const Color(0XFFFF5449);
  static Color kSideMenuIconColor = const Color(0xFF1E88E5);
  static Color kTaskTileColor = const Color(0XFFFFE5B2);
  static Color kWhiteColor = const Color(0XFFFFFFFF);
  static List<Color> listOfDashboardColors = [
    const Color(0XFF71C1FF),
    const Color(0XFFBEE2FF),
  ];

  static List<Color> listOfStatusColorDashboard = [
    const Color(0XFFFFB0AB),
    const Color(0XFFFFE5B2),
    const Color(0XFFCAFFB2),
  ];

  static Color orangeColor = const Color(0XFFFF6B00);
  static Color refCheckDivColor = const Color(0XFFFAF9F9);
  static Color sideBarMenuColor = const Color(0XFF3E99B5);
  static Color transparentColor = Colors.transparent;
  static Color defaultStateFieldColor = const Color(0XFFFAF9F9);
  static Color defaultStateTextColor = const Color(0XFF8E8D8D);
  static Color activeStateFieldOutlineColor = const Color(0xFF1E88E5);
  static Color activeStateFieldTextColor = const Color(0XFF121212);
  static Color activityLogContentCircleColor = const Color(0XFFEEEEEE);
  static Color kDashboardNumDivTitleColor = const Color(0xFF1E88E5);
  static Color kDashboardProfileMenuHoverColor = const Color(0xFFCEDDEA);
  static Color overviewDivColor = const Color(0XFFE6EDF3);
  static Color kselecteddropdown = const Color(0XFFFFFFFF);
  static Color kMobileAuthLoginHintText = const Color(0XFFFAF9F9);
  static Color referenceCheckFieldBorderColor = const Color(0XFF727272);
  static Color refCheckDropDownUnActiveValueColor = const Color(0XFFF1F9FF);
  static Color refCheckDropDownActiveValueColor = const Color(0XFFBEE2FF);
  static Color kShadowColor = const Color(0XFFAFAFAF);
  static Color kProfileMenuFieldFillAndStrokeColor = const Color(0XFF6E6D6D);
}
