import 'package:SolidCheck/features/applicants/presentation/screens/add_applicant_screen.dart';
import 'package:SolidCheck/features/applicants/presentation/screens/applicant_dashboard_screen.dart';
import 'package:SolidCheck/features/auth/di/auth_providers.dart';
import 'package:SolidCheck/features/auth/presentation/screens/login_screen.dart';
import 'package:SolidCheck/features/auth/presentation/screens/qr_code_screen.dart';
import 'package:SolidCheck/features/auth/presentation/screens/two_factor_screen.dart';
import 'package:SolidCheck/features/auth/presentation/state/auth_state.dart';
import 'package:SolidCheck/features/dashboard/presentation/screens/dashboard_main_layout_screen.dart';
import 'package:SolidCheck/features/dashboard/presentation/screens/profile/account_settings_screen.dart';
import 'package:SolidCheck/features/dashboard/presentation/screens/profile/settings_screen.dart';
import 'package:SolidCheck/features/dbs/presentation/screens/dbs_form_screen.dart';
import 'package:SolidCheck/features/dbs/presentation/screens/dbs_success_screen.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_nomination.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:SolidCheck/features/document_nomination/presentation/screens/document_detail_screen.dart';
import 'package:SolidCheck/features/document_nomination/presentation/screens/document_nomination_screen.dart';
import 'package:SolidCheck/features/document_nomination/presentation/screens/document_status_screen.dart';
import 'package:SolidCheck/features/document_nomination/presentation/screens/enhanced_document_demo_screen.dart';
import 'package:SolidCheck/test_entity_options_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
class AppRouter {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  static const String login = '/login';
  static const String qrCode = '/qr-code';
  static const String twoFactor = '/2fa';
  static const String dashboard = '/dashboard';
  static const String applicantDashboard = '/applicant-dashboard';
  static const String applicantDetail = '/applicant-detail';
  static const String addApplicant = '/add-applicant';
  static const String documentNomination = '/document-nomination';
  static const String documentDetail = '/document-detail';
  static const String documentStatus = '/document-status';
  static const String enhancedDocumentDemo = '/enhanced-document-demo';
  static const String accountSettings = '/account-settings';
  static const String settings = '/settings';
  static const String dbsForm = '/dbs-form';
  static const String dbsSuccess = '/dbs-success';

  static Route<dynamic>? generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case login:
        return MaterialPageRoute(
          builder: (_) => const LoginScreen(),
          settings: settings,
        );
      
      case qrCode:
        return MaterialPageRoute(
          builder: (_) => const QrCodeScreen(),
          settings: settings,
        );
      
      case twoFactor:
        return MaterialPageRoute(
          builder: (_) => const TwoFactorScreen(),
          settings: settings,
        );
      
      case dashboard:
        return MaterialPageRoute(
          builder: (_) => const DashboardMainLayoutScreen(),
          settings: settings,
        );

      case applicantDashboard:
        return MaterialPageRoute(
          builder: (_) => const ApplicantDashboardScreen(),
          settings: settings,
        );

      case applicantDetail:
        final args = settings.arguments as Map<String, dynamic>?;
        final applicantId = args?['applicantId'] as String?;
        debugPrint('DEBUG: Route handler for applicantDetail');
        debugPrint('DEBUG: args = $args');
        debugPrint('DEBUG: applicantId = $applicantId');
        return MaterialPageRoute(
          builder: (_) => ApplicantDashboardScreen(applicantId: applicantId),
          settings: settings,
        );

      case addApplicant:
        return MaterialPageRoute(
          builder: (_) => const AddApplicantScreen(),
          settings: settings,
        );

      case documentNomination:
        final args = settings.arguments as Map<String, dynamic>?;
        final applicationId = args?['applicationId'] as String? ?? '';
        final applicantName = args?['applicantName'] as String? ?? '';
        final applicantId = args?['applicantId'] as String?;
        return MaterialPageRoute(
          builder: (_) => DocumentNominationScreen(
            applicationId: applicationId,
            applicantName: applicantName,
            applicantId: applicantId,
          ),
          settings: settings,
        );

      case documentDetail:
        final args = settings.arguments as Map<String, dynamic>?;
        final applicationId = args?['applicationId'] as String? ?? '';
        final applicantName = args?['applicantName'] as String? ?? '';
        final documentType = args?['documentType'] as DocumentType?;
        final existingNomination = args?['existingNomination'] as DocumentNomination?;
        final applicantId = args?['applicantId'] as String?;

        if (documentType == null) {
          return MaterialPageRoute(
            builder: (_) => const Scaffold(
              body: Center(child: Text('Invalid document type')),
            ),
            settings: settings,
          );
        }
        return MaterialPageRoute(
          builder: (_) => DocumentDetailScreen(
            applicationId: applicationId,
            applicantName: applicantName,
            documentType: documentType,
            existingNomination: existingNomination,
            applicantId: applicantId,
          ),
          settings: settings,
        );

      case documentStatus:
        final args = settings.arguments as Map<String, dynamic>?;
        final applicationId = args?['applicationId'] as String? ?? '';
        return MaterialPageRoute(
          builder: (_) => DocumentStatusScreen(
            applicationId: applicationId,
          ),
          settings: settings,
        );

      case enhancedDocumentDemo:
        return MaterialPageRoute(
          builder: (_) => const EnhancedDocumentDemoScreen(),
          settings: settings,
        );

      case accountSettings:
        return MaterialPageRoute(
          builder: (_) => const AccountSettingsScreen(),
          settings: settings,
        );
      
      case AppRouter.settings:
        return MaterialPageRoute(
          builder: (_) => const SettingsScreen(),
          settings: settings,
        );

      case dbsForm:
        return MaterialPageRoute(
          builder: (_) => const DBSFormScreen(),
          settings: settings,
        );

      case dbsSuccess:
        final args = settings.arguments as Map<String, dynamic>?;
        final applicantId = args?['applicantId'] as String?;
        final applicationId = args?['applicationId'] as String?;
        final applicantName = args?['applicantName'] as String?;
        final productName = args?['productName'] as String?;
        return MaterialPageRoute(
          builder: (_) => DBSSuccessScreen(
            applicantId: applicantId,
            applicationId: applicationId,
            applicantName: applicantName,
            productName: productName,
          ),
          settings: settings,
        );

      default:
        return MaterialPageRoute(
          builder: (_) => const LoginScreen(),
          settings: settings,
        );
    }
  }

  static void navigateToLogin() {
    navigatorKey.currentState?.pushNamedAndRemoveUntil(
      login,
      (route) => false,
    );
  }

  static void navigateToQrCode() {
    navigatorKey.currentState?.pushNamed(qrCode);
  }

  static void navigateToTwoFactor() {
    navigatorKey.currentState?.pushNamed(twoFactor);
  }

  static void navigateToDashboard() {
    navigatorKey.currentState?.pushNamedAndRemoveUntil(
      dashboard,
      (route) => false,
    );
  }

  static void navigateToApplicantDashboard() {
    navigatorKey.currentState?.pushNamedAndRemoveUntil(
      applicantDashboard,
      (route) => false,
    );
  }

  static void navigateToApplicantDetail(String applicantId) {
    debugPrint('DEBUG: AppRouter.navigateToApplicantDetail called with applicantId: $applicantId');
    navigatorKey.currentState?.pushNamed(
      applicantDetail,
      arguments: {'applicantId': applicantId},
    );
  }

  static void navigateToAddApplicant() {
    navigatorKey.currentState?.pushNamed(addApplicant);
  }

  static void navigateToAccountSettings() {
    navigatorKey.currentState?.pushNamed(accountSettings);
  }

  static void navigateToSettings() {
    navigatorKey.currentState?.pushNamed(settings);
  }

  static void navigateToDocumentNomination(String applicationId, String applicantName) {
    navigatorKey.currentState?.pushNamed(
      documentNomination,
      arguments: {
        'applicationId': applicationId,
        'applicantName': applicantName,
      },
    );
  }

  static void navigateToDocumentDetail({
    required String applicationId,
    required String applicantName,
    required DocumentType documentType,
    DocumentNomination? existingNomination,
    String? applicantId,
  }) {
    navigatorKey.currentState?.pushNamed(
      documentDetail,
      arguments: {
        'applicationId': applicationId,
        'applicantName': applicantName,
        'documentType': documentType,
        'existingNomination': existingNomination,
        'applicantId': applicantId,
      },
    );
  }

  static void goBack() {
    navigatorKey.currentState?.pop();
  }

  static bool canGoBack() {
    return navigatorKey.currentState?.canPop() ?? false;
  }

  static void navigateToEnhancedDocumentDemo() {
    navigatorKey.currentState?.pushNamed(enhancedDocumentDemo);
  }
}

class AppNavigationWrapper extends ConsumerWidget {
  final Widget child;

  const AppNavigationWrapper({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen<AuthState>(authViewModelProvider, (previous, next) {
      if (next.isAuthenticated && !next.requiresTwoFactor) {
        final userType = next.authResult?.user?.userType;
        if (userType == 'applicant') {
          AppRouter.navigateToApplicantDashboard();
        } else {
          AppRouter.navigateToDashboard();
        }
      } else if (next.authResult?.requiresTwoFactorSetup == true) {
        AppRouter.navigateToQrCode();
      } else if (next.requiresTwoFactor) {
        AppRouter.navigateToTwoFactor();
      } else if (!next.isAuthenticated) {
        AppRouter.navigateToLogin();
      }
    });

    return child;
  }
}

class RouteGuard {
  static bool isProtectedRoute(String routeName) {
    final protectedRoutes = [
      AppRouter.dashboard,
      AppRouter.accountSettings,
      AppRouter.settings,
    ];
    return protectedRoutes.contains(routeName);
  }

  static bool canAccessRoute(String routeName, WidgetRef ref) {
    if (!isProtectedRoute(routeName)) {
      return true;
    }

    final authState = ref.read(authViewModelProvider);
    return authState.isAuthenticated && !authState.requiresTwoFactor;
  }
}
