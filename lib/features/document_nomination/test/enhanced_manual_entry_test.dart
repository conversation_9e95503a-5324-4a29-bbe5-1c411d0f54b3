import 'package:SolidCheck/dbs/data/models/dbs_form_data.dart';
import 'package:SolidCheck/services/dbs_document_field_service.dart';
import 'package:SolidCheck/services/field_type_mapping_service.dart';
import 'package:SolidCheck/services/smart_name_validation_service.dart';
import 'package:SolidCheck/services/smart_postcode_service.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Enhanced Manual Entry Tests', () {
    late ApplicantDetailsData sampleApplicant;

    setUp(() {
      sampleApplicant = ApplicantDetailsData(
        title: 'Mr',
        forename: '<PERSON>',
        middlenames: ['<PERSON>'],
        presentSurname: '<PERSON>',
        dateOfBirth: '1990-05-15',
        gender: 'male',
        niNumber: '*********',
        email: '<EMAIL>',
        contactNumber: '07123456789',
        currentAddress: CurrentAddressData(
          addressLine1: '123 Main Street',
          addressLine2: 'Apartment 4B',
          addressTown: 'Leicester',
          addressCounty: 'Leicestershire',
          postcode: 'LE7 7AQ',
          countryCode: 'GB',
          residentFromGyearMonth: '2020-01',
        ),
        previousAddresses: [
          PreviousAddressData(
            addressLine1: '456 Old Road',
            addressLine2: '',
            addressTown: 'Birmingham',
            addressCounty: 'West Midlands',
            postcode: 'B12 9AW',
            countryCode: 'GB',
            residentFromGyearMonth: '2018-01',
            residentToGyearMonth: '2019-12',
          ),
        ],
        additionalApplicantDetails: AdditionalApplicantDetailsData.empty(),
        applicantIdentityDetails: ApplicantIdentityDetailsData.empty(),
      );
    });

    group('Smart Postcode Service', () {
      test('should generate 4 postcode options including correct one', () {
        final options = SmartPostcodeService.generatePostcodeOptions(
          'LE7 7AQ',
          sampleApplicant,
        );

        expect(options.length, equals(4));
        expect(options.contains('LE7 7AQ'), isTrue);
      });

      test('should include applicant address history in options', () {
        final options = SmartPostcodeService.generatePostcodeOptions(
          'LE7 7AQ',
          sampleApplicant,
        );

        // Should include either current or previous address
        final hasCurrentAddress = options.contains('LE7 7AQ');
        final hasPreviousAddress = options.contains('B12 9AW');
        
        expect(hasCurrentAddress || hasPreviousAddress, isTrue);
      });

      test('should format postcodes correctly', () {
        final formatted = SmartPostcodeService.formatPostcode('LE77AQ');
        expect(formatted, equals('LE7 7AQ'));
      });

      test('should validate UK postcodes', () {
        expect(SmartPostcodeService.isValidUKPostcode('LE7 7AQ'), isTrue);
        expect(SmartPostcodeService.isValidUKPostcode('M1 1AA'), isTrue);
        expect(SmartPostcodeService.isValidUKPostcode('INVALID'), isFalse);
      });
    });

    group('Smart Name Validation Service', () {
      test('should generate correct confirmation text for full name', () {
        final text = SmartNameValidationService.generateNameConfirmationText(
          'full_name_on_document',
          sampleApplicant,
        );

        expect(text, contains('John Smith'));
        expect(text, contains('Can you confirm'));
      });

      test('should generate confirmation text for forename', () {
        final text = SmartNameValidationService.generateNameConfirmationText(
          'forename_on_document',
          sampleApplicant,
        );

        expect(text, contains('John'));
        expect(text, contains('first name'));
      });

      test('should validate yes/no responses correctly', () {
        expect(
          SmartNameValidationService.validateNameConfirmation(
            'yes',
            'full_name_on_document',
            sampleApplicant,
          ),
          isNull,
        );

        expect(
          SmartNameValidationService.validateNameConfirmation(
            'no',
            'full_name_on_document',
            sampleApplicant,
          ),
          isNotNull,
        );

        expect(
          SmartNameValidationService.validateNameConfirmation(
            null,
            'full_name_on_document',
            sampleApplicant,
          ),
          isNotNull,
        );
      });

      test('should get expected name values correctly', () {
        final fullName = SmartNameValidationService.getExpectedNameValue(
          'full_name_on_document',
          sampleApplicant,
        );
        expect(fullName, equals('John Smith'));

        final forename = SmartNameValidationService.getExpectedNameValue(
          'forename_on_document',
          sampleApplicant,
        );
        expect(forename, equals('John'));
      });
    });

    group('Field Type Mapping Service', () {
      test('should map name fields to yes/no confirmation', () {
        final type = FieldTypeMappingService.getEnhancedFieldType(
          'full_name_on_document',
          'passport',
        );
        expect(type, equals('yes_no_confirmation'));
      });

      test('should map postcode fields to multiple choice', () {
        final type = FieldTypeMappingService.getEnhancedFieldType(
          'postcode_on_document',
          'passport',
        );
        expect(type, equals('multiple_choice_postcode'));
      });

      test('should map country fields to dropdown', () {
        final type = FieldTypeMappingService.getEnhancedFieldType(
          'country_of_issue',
          'passport',
        );
        expect(type, equals('country_dropdown'));
      });

      test('should map date fields to smart date', () {
        final type = FieldTypeMappingService.getEnhancedFieldType(
          'expiry_date',
          'passport',
        );
        expect(type, equals('smart_date'));
      });

      test('should detect enhanced fields correctly', () {
        expect(
          FieldTypeMappingService.shouldUseEnhancedField(
            'full_name_on_document',
            'passport',
          ),
          isTrue,
        );

        expect(
          FieldTypeMappingService.shouldUseEnhancedField(
            'passport_number',
            'passport',
          ),
          isFalse,
        );
      });
    });

    group('DBS Document Field Service', () {
      test('should provide fields for all document types', () {
        final allDocumentTypes = DBSDocumentFieldService.getAllDocumentTypes();
        expect(allDocumentTypes.isNotEmpty, isTrue);

        // Test specific document types
        expect(allDocumentTypes.contains('passport'), isTrue);
        expect(allDocumentTypes.contains('driving_licence_photocard_uk'), isTrue);
        expect(allDocumentTypes.contains('birth_certificate_within_12_months'), isTrue);
      });

      test('should provide correct fields for passport', () {
        final passportFields = DBSDocumentFieldService.getFieldsForDocumentType('passport');
        expect(passportFields.length, equals(6));

        final fieldNames = passportFields.map((f) => f.name).toList();
        expect(fieldNames.contains('document_type'), isTrue);
        expect(fieldNames.contains('country_of_issue'), isTrue);
        expect(fieldNames.contains('passport_number'), isTrue);
        expect(fieldNames.contains('issue_date'), isTrue);
        expect(fieldNames.contains('expiry_date'), isTrue);
        expect(fieldNames.contains('full_name_on_document'), isTrue);
      });

      test('should provide correct fields for driving licence', () {
        final licenceFields = DBSDocumentFieldService.getFieldsForDocumentType('driving_licence_photocard_uk');
        expect(licenceFields.length, equals(7));

        final fieldNames = licenceFields.map((f) => f.name).toList();
        expect(fieldNames.contains('postcode_on_document'), isTrue);
        expect(fieldNames.contains('licence_number'), isTrue);
      });

      test('should group documents correctly', () {
        final documentsByGroup = DBSDocumentFieldService.getDocumentsByGroup();
        expect(documentsByGroup.keys.length, equals(4));

        expect(documentsByGroup['Group 1: Primary Identity Documents']!.contains('passport'), isTrue);
        expect(documentsByGroup['Group 2a: Trusted Government Documents']!.contains('firearms_licence'), isTrue);
        expect(documentsByGroup['Group 2b: Financial and Social History Documents']!.contains('utility_bill'), isTrue);
        expect(documentsByGroup['Non-UK Nationals Primary Documents']!.contains('irish_passport_or_passport_card'), isTrue);
      });

      test('should validate document types correctly', () {
        expect(DBSDocumentFieldService.isValidDocumentType('passport'), isTrue);
        expect(DBSDocumentFieldService.isValidDocumentType('invalid_document'), isFalse);
      });
    });

    group('Integration Tests', () {
      test('should provide complete enhanced experience for passport document', () {
        // Test DBS field service integration
        final passportFields = DBSDocumentFieldService.getFieldsForDocumentType('passport');
        expect(passportFields.isNotEmpty, isTrue);

        // Test field mappings for a typical passport document
        final nameType = FieldTypeMappingService.getEnhancedFieldType(
          'full_name_on_document',
          'passport',
        );
        final postcodeType = FieldTypeMappingService.getEnhancedFieldType(
          'postcode_on_document',
          'passport',
        );
        final countryType = FieldTypeMappingService.getEnhancedFieldType(
          'country_of_issue',
          'passport',
        );
        final dateType = FieldTypeMappingService.getEnhancedFieldType(
          'expiry_date',
          'passport',
        );

        expect(nameType, equals('yes_no_confirmation'));
        expect(postcodeType, equals('multiple_choice_postcode'));
        expect(countryType, equals('country_dropdown'));
        expect(dateType, equals('smart_date'));

        // Test that services work together
        final nameText = SmartNameValidationService.generateNameConfirmationText(
          'full_name_on_document',
          sampleApplicant,
        );
        final postcodeOptions = SmartPostcodeService.generatePostcodeOptions(
          'LE7 7AQ',
          sampleApplicant,
        );

        expect(nameText, contains('John Smith'));
        expect(postcodeOptions.length, equals(4));
        expect(postcodeOptions.contains('LE7 7AQ'), isTrue);
      });

      test('should handle all DBS document types with enhanced fields', () {
        final allDocumentTypes = DBSDocumentFieldService.getAllDocumentTypes();

        for (final documentType in allDocumentTypes) {
          final fields = DBSDocumentFieldService.getFieldsForDocumentType(documentType);
          expect(fields.isNotEmpty, isTrue, reason: 'Document type $documentType should have fields');

          // Check that enhanced field types are properly assigned
          for (final field in fields) {
            if (field.name.contains('full_name')) {
              expect(field.type, equals('yes_no_confirmation'));
            }
            if (field.name.contains('postcode')) {
              expect(field.type, equals('multiple_choice_postcode'));
            }
            if (field.name.contains('country')) {
              expect(field.type, equals('country_dropdown'));
            }
            if (field.name.contains('date')) {
              expect(field.type, equals('smart_date'));
            }
          }
        }
      });
    });
  });
}
