import 'package:SolidCheck/features/document_nomination/data/models/document_nomination.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:SolidCheck/features/document_nomination/presentation/widgets/enhanced_document_form.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class EnhancedDocumentDemoScreen extends ConsumerStatefulWidget {
  const EnhancedDocumentDemoScreen({super.key});

  @override
  ConsumerState<EnhancedDocumentDemoScreen> createState() => _EnhancedDocumentDemoScreenState();
}

class _EnhancedDocumentDemoScreenState extends ConsumerState<EnhancedDocumentDemoScreen> {
  DocumentNomination? _currentNomination;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade100,
      appBar: AppBar(
        title: const Text('Enhanced Document Form Demo'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 600),
          margin: const EdgeInsets.all(24),
          child: EnhancedDocumentForm(
            documentType: _createSampleDocumentType(),
            existingNomination: _currentNomination,
            onSave: (nomination) {
              setState(() {
                _currentNomination = nomination;
              });
              
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Document saved successfully!'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            onCancel: () {
              Navigator.of(context).pop();
            },
          ),
        ),
      ),
    );
  }

  DocumentType _createSampleDocumentType() {
    return DocumentType(
      key: 'passport_any',
      name: 'Passport',
      requiresPhoto: true,
      confirmsAddress: false,
      applicableCountries: ['ANY'],
      dataFields: [
        DocumentDataField(
          name: 'issue_country',
          type: 'string',
          required: true,
          label: 'Select country',
          placeholder: 'Select country',
        ),
        DocumentDataField(
          name: 'passport_number',
          type: 'string',
          required: true,
          label: 'Passport Number',
          placeholder: 'Enter passport number',
        ),
        DocumentDataField(
          name: 'issue_date',
          type: 'date',
          required: true,
          label: 'Issue Date',
          placeholder: 'DD/MM/YYYY',
        ),
        DocumentDataField(
          name: 'expiry_date',
          type: 'date',
          required: true,
          label: 'Expiry Date',
          placeholder: 'DD/MM/YYYY',
        ),
        DocumentDataField(
          name: 'full_name_on_document',
          type: 'string',
          required: true,
          label: 'Full Name on Document',
          placeholder: 'Full name as shown on document',
        ),
      ],
    );
  }
}

// Extension to add the missing properties to DocumentDataField
extension DocumentDataFieldExtension on DocumentDataField {
  String? get placeholder => null; // You can implement this based on your needs
}
