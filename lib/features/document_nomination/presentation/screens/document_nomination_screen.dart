import 'dart:io';
import 'package:SolidCheck/core/config/component_config.dart';
import 'package:SolidCheck/core/config/design_config.dart';
import 'package:SolidCheck/core/navigation/app_router_new.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/features/auth/di/auth_providers.dart';
import 'package:SolidCheck/features/dbs/data/models/dbs_form_data.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_form_action_button.dart';
import 'package:SolidCheck/features/document_nomination/data/models/available_documents_response.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_nomination.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:SolidCheck/features/document_nomination/data/models/route_requirement.dart';
import 'package:SolidCheck/features/document_nomination/presentation/providers/document_nomination_provider.dart';
import 'package:SolidCheck/features/document_nomination/presentation/screens/mobile_document_entry_screen.dart';
import 'package:SolidCheck/features/document_nomination/presentation/widgets/document_data_form.dart';
import 'package:SolidCheck/features/document_nomination/presentation/widgets/document_selection_card.dart';
import 'package:SolidCheck/features/document_nomination/presentation/widgets/document_upload_widget.dart';
import 'package:SolidCheck/features/document_nomination/presentation/widgets/route_progress_indicator.dart';
import 'package:SolidCheck/features/document_nomination/presentation/widgets/validation_messages.dart';
import 'package:SolidCheck/features/document_nomination/presentation/widgets/document_completion_status_widget.dart';
import 'package:SolidCheck/features/document_nomination/services/smart_document_service.dart';
import 'package:SolidCheck/screens/document_processing_choice_screen.dart';
import 'package:SolidCheck/screens/manual_document_entry_screen.dart';
import 'package:SolidCheck/shared/widgets/custom_snakbar.dart';
import 'package:SolidCheck/shared/widgets/navigation/applicant_sidebar.dart';
import 'package:SolidCheck/shared/widgets/navigation/universal_mobile_header.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Data classes for dynamic progress calculation
class RouteRequirements {
  final int totalDocuments;
  final int group1Required;
  final bool addressRequired;
  final int additionalRequired;

  RouteRequirements({
    required this.totalDocuments,
    required this.group1Required,
    required this.addressRequired,
    required this.additionalRequired,
  });
}

class ProgressRequirement {
  final String text;
  final bool isCompleted;

  ProgressRequirement({
    required this.text,
    required this.isCompleted,
  });
}

class ProgressData {
  final List<ProgressRequirement> requirements;
  final int completedRequirements;
  final int totalRequirements;
  final double progressPercentage;
  final bool isComplete;

  ProgressData({
    required this.requirements,
    required this.completedRequirements,
    required this.totalRequirements,
    required this.progressPercentage,
    required this.isComplete,
  });
}

class DocumentNominationScreen extends ConsumerStatefulWidget {
  final String applicationId;
  final String applicantName;
  final String? applicantId;

  const DocumentNominationScreen({
    super.key,
    required this.applicationId,
    required this.applicantName,
    this.applicantId,
  });

  @override
  ConsumerState<DocumentNominationScreen> createState() => _DocumentNominationScreenState();
}

class _DocumentNominationScreenState extends ConsumerState<DocumentNominationScreen> {
  bool _isCheckingProcessStamp = true;
  bool _documentsCompleted = false;
  Map<String, dynamic>? _completionData;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.applicationId.isNotEmpty) {
        _checkDocumentCompletionStatus();
        ref.read(documentNominationProvider.notifier).loadAvailableDocuments(widget.applicationId);
      } else {
      }
    });
  }

  Future<void> _checkDocumentCompletionStatus() async {
    try {
      // For now, we'll simulate checking for completed documents
      // In a real implementation, this would call the API to check for DOCUMENTS_NOMINATED process stamp
      await Future.delayed(const Duration(milliseconds: 500));

      // Mock completion data - in real implementation this would come from API
      final mockCompletionData = {
        'exists': false, // Set to true to test the completion screen
        'data': {
          'status': 'under_review',
          'documents': [
            {
              'document_name': 'Passport',
              'document_type': 'passport_any',
              'route_number': 1,
              'nominated_at': DateTime.now().subtract(const Duration(days: 2)).toIso8601String(),
              'status': 'nominated'
            },
            {
              'document_name': 'UK Driving License',
              'document_type': 'photocard_drivers_licence_uk',
              'route_number': 1,
              'nominated_at': DateTime.now().subtract(const Duration(days: 2)).toIso8601String(),
              'status': 'nominated'
            }
          ]
        }
      };

      if (mockCompletionData['exists'] == true) {
        setState(() {
          _documentsCompleted = true;
          _completionData = mockCompletionData['data'] as Map<String, dynamic>;
        });
      }
    } catch (e) {
      // If check fails, continue with normal flow
      print('Failed to check process stamp: $e');
    } finally {
      setState(() {
        _isCheckingProcessStamp = false;
      });
    }
  }

  String? _getEffectiveApplicantId() {
    final state = ref.watch(documentNominationProvider);

    // First try to get applicantId from API response
    if (state.availableDocuments?.application.applicantId != null) {
      return state.availableDocuments!.application.applicantId.toString();
    }

    // Fallback to widget parameter
    return widget.applicantId;
  }

  String _getEffectiveApplicantName() {
    final state = ref.watch(documentNominationProvider);

    // First try to get applicant name from API response
    if (state.availableDocuments?.application.applicantName != null &&
        state.availableDocuments!.application.applicantName.isNotEmpty) {
      return state.availableDocuments!.application.applicantName;
    }

    // Fallback to widget parameter
    return widget.applicantName;
  }



  Widget _buildClientSidebar() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'Document Nomination',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  _getEffectiveApplicantName(),
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          // Navigation items
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                _buildSidebarItem(
                  icon: Icons.dashboard,
                  title: 'Dashboard',
                  isActive: false,
                  onTap: () => _navigateBackToDashboard(),
                ),
                _buildSidebarItem(
                  icon: Icons.description,
                  title: 'Document Nomination',
                  isActive: true,
                  onTap: () {},
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSidebarItem({
    required IconData icon,
    required String title,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          icon,
          color: isActive ? Theme.of(context).primaryColor : Colors.grey[600],
        ),
        title: Text(
          title,
          style: TextStyle(
            color: isActive ? Theme.of(context).primaryColor : Colors.grey[800],
            fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        selected: isActive,
        selectedTileColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        onTap: onTap,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(documentNominationProvider);
    final notifier = ref.read(documentNominationProvider.notifier);

    // Responsive design values matching DBS form
    final size = MediaQuery.of(context).size;
    final isMobile = ResponsiveHelper.isMobile(context);
    final isTablet = ResponsiveHelper.isTablet(context);
    final isDesktop = ResponsiveHelper.isDesktop(context);
    final responsiveBreakPoint = size.width < 768;

    // Show completion status if documents are already completed
    if (_documentsCompleted && _completionData != null) {
      return Scaffold(
        backgroundColor: AppColors.applicationOverviewDivColor,
        body: SingleChildScrollView(
          child: DocumentCompletionStatusWidget(
            applicationId: widget.applicationId,
            nominatedDocuments: List<Map<String, dynamic>>.from(_completionData!['documents'] ?? []),
            currentStatus: _completionData!['status'] ?? 'completed',
          ),
        ),
      );
    }

    // Calculate responsive values like DBS form
    final paddingValue = isMobile ? 16.0 : (isTablet ? 24.0 : 32.0);
    final verticalPadding = isMobile ? 16.0 : 24.0;

    return Scaffold(
      backgroundColor: AppColors.applicationOverviewDivColor, // Match DBS form background
      drawer: isMobile ? ApplicantSidebar(
        applicantId: _getEffectiveApplicantId(),
        showBackButton: true,
        onBackPressed: () => Navigator.of(context).pop(),
      ) : null,
      body: (_isCheckingProcessStamp || state.isLoading)
          ? _buildLoadingLayout(context, isMobile)
          : state.error != null
              ? _buildErrorLayout(context, isMobile, state.error!)
              : _buildOptimizedLayout(
                  context,
                  isMobile,
                  isTablet,
                  isDesktop,
                  size,
                  responsiveBreakPoint,
                  paddingValue,
                  verticalPadding,
                  state,
                  notifier,
                ),
    );
  }

  // Loading layout matching universal header style
  Widget _buildLoadingLayout(BuildContext context, bool isMobile) {
    if (isMobile) {
      return Column(
        children: [
          const UniversalMobileHeader(),
          Expanded(
            child: _buildMobileLoadingContent(),
          ),
        ],
      );
    }

    return Row(
      children: [
        ApplicantSidebar(
          applicantId: _getEffectiveApplicantId(),
          showBackButton: true,
          onBackPressed: () => Navigator.of(context).pop(),
        ),
        Expanded(child: _buildMobileLoadingContent()),
      ],
    );
  }

  Widget _buildMobileLoadingContent() {
    return Container(
      color: AppColors.applicationOverviewDivColor,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text(
              'Loading document information...',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Error layout matching universal header style
  Widget _buildErrorLayout(BuildContext context, bool isMobile, String error) {
    if (isMobile) {
      return Column(
        children: [
          const UniversalMobileHeader(),
          Expanded(
            child: _buildMobileErrorContent(error),
          ),
        ],
      );
    }

    return Row(
      children: [
        ApplicantSidebar(
          applicantId: _getEffectiveApplicantId(),
          showBackButton: true,
          onBackPressed: () => Navigator.of(context).pop(),
        ),
        Expanded(child: _buildMobileErrorContent(error)),
      ],
    );
  }

  Widget _buildMobileErrorContent(String error) {
    return Container(
      color: AppColors.applicationOverviewDivColor,
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red[400],
              ),
              const SizedBox(height: 16),
              Text(
                'Error Loading Documents',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                error,
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.kBlueColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                ),
                child: const Text('Go Back'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Optimized layout following DBS form patterns
  Widget _buildOptimizedLayout(
    BuildContext context,
    bool isMobile,
    bool isTablet,
    bool isDesktop,
    Size size,
    bool responsiveBreakPoint,
    double paddingValue,
    double verticalPadding,
    DocumentNominationState state,
    DocumentNominationNotifier notifier,
  ) {
    if (isMobile) {
      return Column(
        children: [
          const UniversalMobileHeader(),
          Expanded(
            child: _buildOptimizedContent(
              context,
              size,
              responsiveBreakPoint,
              paddingValue,
              verticalPadding,
              state,
              notifier,
              isMobile,
              isTablet,
              isDesktop,
            ),
          ),
        ],
      );
    }

    return Row(
      children: [
        ApplicantSidebar(
          applicantId: _getEffectiveApplicantId(),
          showBackButton: true,
          onBackPressed: () => Navigator.of(context).pop(),
        ),
        Expanded(
          child: _buildOptimizedContent(
            context,
            size,
            responsiveBreakPoint,
            paddingValue,
            verticalPadding,
            state,
            notifier,
            isMobile,
            isTablet,
            isDesktop,
          ),
        ),
      ],
    );
  }

  // Content matching HTML structure exactly
  Widget _buildOptimizedContent(
    BuildContext context,
    Size size,
    bool responsiveBreakPoint,
    double paddingValue,
    double verticalPadding,
    DocumentNominationState state,
    DocumentNominationNotifier notifier,
    bool isMobile,
    bool isTablet,
    bool isDesktop,
  ) {
    return Container(
      color: const Color(0xFFF3F4F6), // --background-soft
      child: Column(
        children: [
          // Header section
          _buildBeautifulHeader(isMobile, isTablet, isDesktop),

          // Main content area
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(
                horizontal: isMobile ? 16 : (isTablet ? 24 : 32),
                vertical: 16,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Nomination Progress
                  _buildNominationChecklist(state, isMobile),

                  const SizedBox(height: 32),

                  // Document Groups
                  if (state.availableDocuments != null) ...[
                    _buildDocumentGroups(state, notifier, isMobile),
                  ],

                  // Proceed button
                  _buildProceedButton(state, isMobile),

                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Header matching HTML design exactly
  Widget _buildBeautifulHeader(bool isMobile, bool isTablet, bool isDesktop) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        horizontal: isMobile ? 16 : (isTablet ? 24 : 32),
        vertical: isMobile ? 16 : 24,
      ),
      color: const Color(0xFFF3F4F6), // --background-soft
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Back button
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.arrow_back,
                  color: Color(0xFF4B5563), // --text-secondary
                  size: 20,
                ),
                SizedBox(width: 8),
                Text(
                  'Back',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF4B5563), // --text-secondary
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Title and subtitle
          Text(
            'DBS Check: Document Nomination',
            style: TextStyle(
              fontSize: isMobile ? 24 : 30,
              fontWeight: FontWeight.w700,
              color: const Color(0xFF111827), // --text-primary
              height: 1.2,
            ),
          ),
          const SizedBox(height: 4),
          const Text(
            'Select the documents you will provide for your DBS check.',
            style: TextStyle(
              fontSize: 16,
              color: Color(0xFF4B5563), // --text-secondary
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  // Dynamic Nomination Progress with real-time calculations
  Widget _buildNominationChecklist(DocumentNominationState state, bool isMobile) {
    if (state.availableDocuments == null) {
      return const SizedBox.shrink();
    }

    final routeRequirements = _calculateRouteRequirements(state);
    final progressData = _calculateProgressData(state, routeRequirements);

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
        border: Border.all(color: const Color(0xFFD1D5DB)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Dynamic header with real progress
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Your Nomination Progress',
                style: TextStyle(
                  fontSize: isMobile ? 16 : 20,
                  fontWeight: FontWeight.w700,
                  color: const Color(0xFF111827),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '${progressData.completedRequirements} of ${progressData.totalRequirements} requirements met',
                style: TextStyle(
                  fontSize: isMobile ? 12 : 14,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF4B5563),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Dynamic progress bar
          Container(
            width: double.infinity,
            height: 10,
            decoration: BoxDecoration(
              color: const Color(0xFFE5E7EB),
              borderRadius: BorderRadius.circular(5),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: progressData.progressPercentage,
              child: Container(
                decoration: BoxDecoration(
                  color: const Color(0xFF2563EB),
                  borderRadius: BorderRadius.circular(5),
                ),
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Dynamic requirements list
          Column(
            children: progressData.requirements.map((requirement) =>
              Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: _buildTaskItem(
                  isCompleted: requirement.isCompleted,
                  text: requirement.text,
                ),
              ),
            ).toList(),
          ),

          const SizedBox(height: 24),

          // Info message
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              color: Color(0xFFF0F9FF),
              borderRadius: BorderRadius.only(
                topRight: Radius.circular(8),
                bottomRight: Radius.circular(8),
              ),
              border: Border(
                left: BorderSide(
                  color: Color(0xFF2563EB),
                  width: 4,
                ),
              ),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Icon(
                  Icons.info,
                  color: Color(0xFF2563EB),
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    progressData.isComplete
                        ? 'All requirements met! You can now proceed to upload your documents.'
                        : 'Once you have nominated all required documents, you can proceed to upload them.',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF1E3A8A),
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Dynamic route requirements calculation
  RouteRequirements _calculateRouteRequirements(DocumentNominationState state) {
    final availableDocuments = state.availableDocuments!;
    final routing = availableDocuments.routing;
    final recommendedRoute = routing.recommendedRoute;

    // Get requirements for recommended route
    final routeKey = 'route_$recommendedRoute';
    final routeRequirement = routing.routeRequirements[routeKey];

    if (routeRequirement == null) {
      // Fallback to basic requirements
      return RouteRequirements(
        totalDocuments: 2,
        group1Required: 1,
        addressRequired: true,
        additionalRequired: 1,
      );
    }

    return RouteRequirements(
      totalDocuments: routeRequirement.totalDocuments,
      group1Required: routeRequirement.requiredGroups['1']?.min ?? 0,
      addressRequired: routeRequirement.addressConfirmationRequired,
      additionalRequired: routeRequirement.totalDocuments - (routeRequirement.requiredGroups['1']?.min ?? 0),
    );
  }

  // Dynamic progress data calculation
  ProgressData _calculateProgressData(DocumentNominationState state, RouteRequirements requirements) {
    final group1Count = _getGroup1NominatedCount(state);
    final addressCount = _getAddressDocumentCount(state);
    final totalCount = state.nominations.length;

    final progressRequirements = <ProgressRequirement>[];
    int completedCount = 0;

    // Group 1 requirement
    if (requirements.group1Required > 0) {
      final isCompleted = group1Count >= requirements.group1Required;
      if (isCompleted) completedCount++;

      progressRequirements.add(ProgressRequirement(
        text: 'Nominate ${requirements.group1Required} document${requirements.group1Required > 1 ? 's' : ''} from Group 1',
        isCompleted: isCompleted,
      ));
    }

    // Address requirement
    if (requirements.addressRequired) {
      final isCompleted = addressCount >= 1;
      if (isCompleted) completedCount++;

      progressRequirements.add(ProgressRequirement(
        text: 'Nominate a document with your current address',
        isCompleted: isCompleted,
      ));
    }

    // Additional documents requirement
    if (requirements.additionalRequired > 0) {
      final additionalNeeded = requirements.totalDocuments - requirements.group1Required;
      final additionalNominated = totalCount - group1Count;
      final isCompleted = totalCount >= requirements.totalDocuments;
      if (isCompleted) completedCount++;

      progressRequirements.add(ProgressRequirement(
        text: 'Nominate $additionalNeeded additional document${additionalNeeded > 1 ? 's' : ''} from any group ($additionalNominated of $additionalNeeded)',
        isCompleted: isCompleted,
      ));
    }

    final totalRequirements = progressRequirements.length;
    final progressPercentage = totalRequirements > 0 ? completedCount / totalRequirements : 0.0;

    return ProgressData(
      requirements: progressRequirements,
      completedRequirements: completedCount,
      totalRequirements: totalRequirements,
      progressPercentage: progressPercentage,
      isComplete: completedCount == totalRequirements,
    );
  }

  // Task item matching HTML design exactly
  Widget _buildTaskItem({
    required bool isCompleted,
    required String text,
  }) {
    return Row(
      children: [
        Icon(
          isCompleted ? Icons.check_circle : Icons.radio_button_unchecked,
          color: isCompleted
              ? const Color(0xFF059669) // --success-green
              : const Color(0xFFD97706), // --warning-amber
          size: 20,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            text,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Color(0xFF374151), // text-gray-800
            ),
          ),
        ),
      ],
    );
  }

  // Helper method to build checklist items
  Widget _buildChecklistItem({
    required IconData icon,
    required Color iconColor,
    required Color backgroundColor,
    required String text,
    required String count,
    required bool isCompleted,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: iconColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: iconColor, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                color: iconColor.withValues(alpha: 0.8),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: iconColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              count,
              style: TextStyle(
                fontSize: 12,
                color: iconColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods for progress tracking
  int _getGroup1NominatedCount(DocumentNominationState state) {
    if (state.nominations.isEmpty || state.availableDocuments == null) return 0;

    // Get Group 1 document IDs
    final group1Documents = state.availableDocuments!.documents.availableByGroup['1']?.documents ?? [];
    final group1DocumentIds = group1Documents.map((doc) => doc.id).toSet();

    return state.nominations.where((nomination) =>
        group1DocumentIds.contains(nomination.documentTypeId)).length;
  }

  int _getAddressDocumentCount(DocumentNominationState state) {
    if (state.nominations.isEmpty) return 0;
    return state.nominations.where((nomination) =>
        nomination.confirmsAddress == true).length;
  }

  bool _hasGroup1Document(DocumentNominationState state) {
    return _getGroup1NominatedCount(state) >= 1;
  }

  bool _hasAddressDocument(DocumentNominationState state) {
    return _getAddressDocumentCount(state) >= 1;
  }

  // Enhanced dynamic document groups with better organization
  Widget _buildDocumentGroups(DocumentNominationState state, DocumentNominationNotifier notifier, bool isMobile) {
    final availableDocuments = state.availableDocuments!;
    final routing = availableDocuments.routing;
    final routeRequirements = _calculateRouteRequirements(state);

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
        border: Border.all(color: const Color(0xFFD1D5DB)),
      ),
      child: Column(
        children: [
          // Dynamic document sections based on route requirements
          ..._buildDynamicDocumentSections(state, notifier, isMobile, availableDocuments, routeRequirements),
        ],
      ),
    );
  }

  // Build route information section
  Widget _buildRouteInfo(dynamic routing, RouteRequirements requirements) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF0F9FF), // bg-blue-50
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFBAE6FD)), // border-blue-200
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.route,
                color: Color(0xFF2563EB),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Recommended Route ${routing.recommendedRoute}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF1E3A8A),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Total documents required: ${requirements.totalDocuments}',
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF1E40AF),
            ),
          ),
          if (requirements.addressRequired) ...[
            const SizedBox(height: 4),
            const Text(
              '• At least one document must confirm your current address',
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF1E40AF),
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Build document list (single column)
  Widget _buildDocumentList(List<DocumentType> documents, DocumentNominationState state, DocumentNominationNotifier notifier, bool isMobile) {
    return Column(
      children: documents.map((document) => Container(
        margin: const EdgeInsets.only(bottom: 12),
        child: _buildHtmlDocumentItem(
          document: document,
          state: state,
          notifier: notifier,
          isMobile: isMobile,
        ),
      )).toList(),
    );
  }

  // Build document grid (2 columns on large screens)
  Widget _buildDocumentGrid(List<DocumentType> documents, DocumentNominationState state, DocumentNominationNotifier notifier, bool isMobile) {
    if (isMobile) {
      // Single column on mobile
      return _buildDocumentList(documents, state, notifier, isMobile);
    }

    // Grid layout for larger screens
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 4, // Adjust based on content height
      ),
      itemCount: documents.length,
      itemBuilder: (context, index) {
        return _buildHtmlDocumentItem(
          document: documents[index],
          state: state,
          notifier: notifier,
          isMobile: isMobile,
        );
      },
    );
  }

  // Build dynamic document sections based on route requirements
  List<Widget> _buildDynamicDocumentSections(
    DocumentNominationState state,
    DocumentNominationNotifier notifier,
    bool isMobile,
    AvailableDocumentsData availableDocuments,
    RouteRequirements requirements,
  ) {
    final sections = <Widget>[];
    final availableByGroup = availableDocuments.documents.availableByGroup;

    // Group 1 - Primary Identity Documents
    if (availableByGroup.containsKey('1') && requirements.group1Required > 0) {
      sections.add(_buildEnhancedDocumentSection(
        title: 'Primary Identity Documents',
        subtitle: 'You must nominate ${requirements.group1Required} document${requirements.group1Required > 1 ? 's' : ''} from this list.',
        badge: '${requirements.group1Required} Required',
        badgeColor: const Color(0xFF059669),
        badgeBackground: const Color(0xFFDCFCE7),
        documents: availableByGroup['1']!.documents,
        state: state,
        notifier: notifier,
        isMobile: isMobile,
        isGrid: true,
        groupKey: '1',
      ));

      sections.add(_buildSectionDivider());
    }

    // Group 2b - Address Confirmation Documents
    if (availableByGroup.containsKey('2b') && requirements.addressRequired) {
      final addressDocs = availableByGroup['2b']!.documents
          .where((doc) => doc.confirmsAddress)
          .toList();

      if (addressDocs.isNotEmpty) {
        sections.add(_buildEnhancedDocumentSection(
          title: 'Address Confirmation Documents',
          subtitle: 'At least one nominated document must confirm your current address.',
          badge: '1 Required',
          badgeColor: const Color(0xFF059669),
          badgeBackground: const Color(0xFFDCFCE7),
          documents: addressDocs,
          state: state,
          notifier: notifier,
          isMobile: isMobile,
          isGrid: true,
          groupKey: '2b',
        ));

        sections.add(_buildSectionDivider());
      }
    }

    // Group 2a - Secondary Identity Documents
    if (availableByGroup.containsKey('2a')) {
      sections.add(_buildEnhancedDocumentSection(
        title: 'Secondary Identity Documents',
        subtitle: 'Additional documents to support your application.',
        badge: 'Optional',
        badgeColor: const Color(0xFF4B5563),
        badgeBackground: const Color(0xFFE5E7EB),
        documents: availableByGroup['2a']!.documents,
        state: state,
        notifier: notifier,
        isMobile: isMobile,
        isGrid: true,
        groupKey: '2a',
      ));

      sections.add(_buildSectionDivider());
    }

    // Additional supporting documents from other groups
    final supportingDocs = _collectSupportingDocuments(availableByGroup);
    if (supportingDocs.isNotEmpty) {
      sections.add(_buildEnhancedDocumentSection(
        title: 'Supporting Documents',
        subtitle: 'Additional documents that may help your application.',
        badge: 'Optional',
        badgeColor: const Color(0xFF4B5563),
        badgeBackground: const Color(0xFFE5E7EB),
        documents: supportingDocs,
        state: state,
        notifier: notifier,
        isMobile: isMobile,
        isGrid: true,
        groupKey: 'supporting',
      ));
    }

    return sections;
  }

  // Build section divider
  Widget _buildSectionDivider() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 32),
      height: 1,
      color: const Color(0xFFD1D5DB),
    );
  }

  // Collect supporting documents from various groups
  List<DocumentType> _collectSupportingDocuments(Map<String, dynamic> availableByGroup) {
    final supportingDocs = <DocumentType>[];

    // Add non-address documents from Group 2b
    if (availableByGroup.containsKey('2b')) {
      final nonAddressDocs = availableByGroup['2b']!.documents
          .where((doc) => !doc.confirmsAddress)
          .toList();
      supportingDocs.addAll(nonAddressDocs);
    }

    // Add documents from other groups if they exist
    for (final groupKey in ['1a', '3', '4']) {
      if (availableByGroup.containsKey(groupKey)) {
        supportingDocs.addAll(availableByGroup[groupKey]!.documents);
      }
    }

    return supportingDocs;
  }

  Widget _buildSupportingDocuments(DocumentNominationState state, DocumentNominationNotifier notifier, bool isMobile, AvailableDocumentsData availableDocuments) {
    final supportingDocs = <DocumentType>[];

    // Add Group 2a documents
    if (availableDocuments.documents.availableByGroup.containsKey('2a')) {
      supportingDocs.addAll(availableDocuments.documents.availableByGroup['2a']!.documents);
    }

    // Add non-address Group 2b documents
    if (availableDocuments.documents.availableByGroup.containsKey('2b')) {
      supportingDocs.addAll(availableDocuments.documents.availableByGroup['2b']!.documents.where((doc) => !doc.confirmsAddress));
    }

    return _buildEnhancedDocumentSection(
      title: 'Supporting Documents',
      subtitle: 'Additional documents to support your application.',
      badge: 'Optional',
      badgeColor: const Color(0xFF4B5563), // text-gray-700
      badgeBackground: const Color(0xFFE5E7EB), // bg-gray-200
      documents: supportingDocs,
      state: state,
      notifier: notifier,
      isMobile: isMobile,
      isGrid: true, // Use grid layout for supporting documents
      groupKey: 'supporting',
    );
  }

  // Enhanced interactive document item
  Widget _buildHtmlDocumentItem({
    required DocumentType document,
    required DocumentNominationState state,
    required DocumentNominationNotifier notifier,
    required bool isMobile,
  }) {
    final isNominated = state.nominations.any((nomination) =>
        nomination.documentTypeId == document.id);

    return GestureDetector(
      onTap: () => _handleDocumentTap(document, state, notifier),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isNominated ? AppColors.kBlueColor.withValues(alpha: 0.1) : Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isNominated ? AppColors.kBlueColor : const Color(0xFFE5E7EB),
            width: isNominated ? 2 : 1,
          ),
          boxShadow: isNominated ? [
            BoxShadow(
              color: AppColors.kBlueColor.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ] : null,
        ),
        child: Row(
          children: [
            // Enhanced checkbox with animation
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              child: Icon(
                isNominated ? Icons.check_box : Icons.check_box_outline_blank,
                key: ValueKey(isNominated),
                color: isNominated
                    ? const Color(0xFF059669)
                    : const Color(0xFF6B7280),
                size: 24,
              ),
            ),

            const SizedBox(width: 16),

            // Enhanced document info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    document.name,
                    style: const TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF111827),
                    ),
                  ),
                  if (_shouldShowProofInfo(document)) ...[
                    const SizedBox(height: 6),
                    Wrap(
                      spacing: 12,
                      children: [
                        if (document.dataFields.isNotEmpty)
                          _buildProofIndicator(
                            icon: Icons.person,
                            text: 'Identity Proof',
                          ),
                        if (document.confirmsAddress)
                          _buildProofIndicator(
                            icon: Icons.home,
                            text: 'Proof of Address',
                          ),
                      ],
                    ),
                  ],
                ],
              ),
            ),

            const SizedBox(width: 12),

            // Enhanced status indicator
            if (isNominated)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: const Color(0xFF059669),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'Nominated',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              )
            else
              IconButton(
                icon: const Icon(
                  Icons.info_outline,
                  color: Color(0xFF6B7280),
                  size: 20,
                ),
                onPressed: () => _showDocumentInfo(document),
                tooltip: 'Document information',
              ),
          ],
        ),
      ),
    );
  }

  // Build proof indicator
  Widget _buildProofIndicator({
    required IconData icon,
    required String text,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 14,
          color: const Color(0xFF4B5563),
        ),
        const SizedBox(width: 4),
        Text(
          text,
          style: const TextStyle(
            fontSize: 12,
            color: Color(0xFF4B5563),
          ),
        ),
      ],
    );
  }

  // Handle document tap - navigate to document detail page
  void _handleDocumentTap(DocumentType document, DocumentNominationState state, DocumentNominationNotifier notifier) {
    final isMobile = ResponsiveHelper.isMobile(context);

    if (isMobile) {
      _navigateToMobileDocumentEntry(document, state, notifier);
    } else {
      final existingNomination = state.getNominationForDocument(document.id);
      AppRouter.navigateToDocumentDetail(
        applicationId: widget.applicationId,
        applicantName: widget.applicantName,
        documentType: document,
        existingNomination: existingNomination,
        applicantId: widget.applicantId,
      );
    }
  }

  Future<void> _navigateToMobileDocumentEntry(DocumentType document, DocumentNominationState state, DocumentNominationNotifier notifier) async {
    final existingNomination = state.getNominationForDocument(document.id);

    final result = await Navigator.of(context).push<DocumentNomination>(
      MaterialPageRoute(
        builder: (context) => MobileDocumentEntryScreen(
          documentType: document,
          existingNomination: existingNomination,
          applicationId: widget.applicationId,
          applicantName: widget.applicantName,
          applicantId: widget.applicantId,
        ),
      ),
    );

    if (result != null) {
      notifier.addDocumentNomination(result);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${document.name} nominated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  // Build compact document tile with proper sizing
  Widget _buildCompactDocumentTile({
    required DocumentType document,
    required DocumentNominationState state,
    required DocumentNominationNotifier notifier,
    required bool isMobile,
  }) {
    final isNominated = state.nominations.any((nomination) =>
        nomination.documentTypeId == document.id);

    return Container(
      constraints: BoxConstraints(
        minHeight: isMobile ? 80 : 110,
        maxWidth: isMobile ? double.infinity : 350,
      ),
        decoration: BoxDecoration(
          color: isNominated ? const Color(0xFFF0F9FF) : Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isNominated ? const Color(0xFF0EA5E9) : const Color(0xFFE5E7EB),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.02),
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () => _handleDocumentTap(document, state, notifier),
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: EdgeInsets.all(isMobile ? 12 : 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header with checkbox and status
                  Row(
                    children: [
                      AnimatedSwitcher(
                        duration: const Duration(milliseconds: 200),
                        child: Icon(
                          isNominated ? Icons.check_box : Icons.check_box_outline_blank,
                          key: ValueKey(isNominated),
                          color: isNominated
                              ? const Color(0xFF0EA5E9)
                              : const Color(0xFF6B7280),
                          size: isMobile ? 20 : 24,
                        ),
                      ),
                      SizedBox(width: isMobile ? 8 : 12),
                      Expanded(
                        child: Text(
                          document.name,
                          style: TextStyle(
                            fontSize: isMobile ? 13 : 15,
                            fontWeight: FontWeight.w600,
                            color: const Color(0xFF111827),
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: isMobile ? 8 : 12),

                  // Document info indicators
                  if (_shouldShowProofInfo(document))
                    Wrap(
                      spacing: isMobile ? 12 : 16,
                      runSpacing: 6,
                      children: [
                        if (document.dataFields.isNotEmpty)
                          _buildCompactProofIndicator(
                            icon: Icons.person,
                            text: 'Identity Proof',
                            isMobile: isMobile,
                          ),
                        if (document.confirmsAddress)
                          _buildCompactProofIndicator(
                            icon: Icons.home,
                            text: 'Address',
                            isMobile: isMobile,
                          ),
                        if (document.requiresPhoto)
                          _buildCompactProofIndicator(
                            icon: Icons.camera_alt,
                            text: 'Photo',
                            isMobile: isMobile,
                          ),
                      ],
                    ),

                  SizedBox(height: isMobile ? 4 : 8),
                ],
              ),
            ),
          ),
        ),
    );
  }

  // Build compact proof indicator
  Widget _buildCompactProofIndicator({
    required IconData icon,
    required String text,
    bool isMobile = false,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: isMobile ? 12 : 14,
          color: const Color(0xFF4B5563),
        ),
        SizedBox(width: isMobile ? 3 : 4),
        Text(
          text,
          style: TextStyle(
            fontSize: isMobile ? 10 : 12,
            color: const Color(0xFF4B5563),
          ),
        ),
      ],
    );
  }

  // Show document information dialog
  void _showDocumentInfo(DocumentType document) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(document.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (document.dataFields.isNotEmpty) ...[
              const Text(
                'Required Information:',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 8),
              ...document.dataFields.map((field) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Text('• ${field.label}'),
              )),
              const SizedBox(height: 12),
            ],
            if (document.confirmsAddress)
              const Text('✓ This document confirms your address'),
            if (document.requiresPhoto)
              const Text('📷 Photo required for this document'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  // Build compact document grid with responsive layout
  Widget _buildCompactDocumentGrid(List<DocumentType> documents, DocumentNominationState state, DocumentNominationNotifier notifier, bool isMobile) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final crossAxisCount = isMobile ? 1 : (constraints.maxWidth > 900 ? 3 : 2);
        final itemWidth = (constraints.maxWidth - (crossAxisCount - 1) * 16) / crossAxisCount;

        return Wrap(
          spacing: 16,
          runSpacing: 16,
          children: documents.map((document) =>
            SizedBox(
              width: itemWidth,
              child: _buildCompactDocumentTile(
                document: document,
                state: state,
                notifier: notifier,
                isMobile: isMobile,
              ),
            ),
          ).toList(),
        );
      },
    );
  }

  // Build compact document list
  Widget _buildCompactDocumentList(List<DocumentType> documents, DocumentNominationState state, DocumentNominationNotifier notifier, bool isMobile) {
    return Column(
      children: documents.map((document) => Padding(
        padding: const EdgeInsets.only(bottom: 16),
        child: _buildCompactDocumentTile(
          document: document,
          state: state,
          notifier: notifier,
          isMobile: isMobile,
        ),
      )).toList(),
    );
  }

  bool _shouldShowProofInfo(DocumentType document) {
    return document.dataFields.isNotEmpty || document.confirmsAddress;
  }

  // Enhanced document section with dynamic features
  Widget _buildEnhancedDocumentSection({
    required String title,
    required String subtitle,
    required String badge,
    required Color badgeColor,
    required Color badgeBackground,
    required List<DocumentType> documents,
    required DocumentNominationState state,
    required DocumentNominationNotifier notifier,
    required bool isMobile,
    bool isGrid = false,
    String? groupKey,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: isMobile ? 16 : 20,
                      fontWeight: FontWeight.w700,
                      color: const Color(0xFF111827),
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: badgeBackground,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    badge,
                    style: TextStyle(
                      fontSize: isMobile ? 11 : 12,
                      color: badgeColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: isMobile ? 12 : 14,
                color: const Color(0xFF4B5563),
                height: 1.4,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Documents list or grid with compact tiles
        if (isGrid)
          _buildCompactDocumentGrid(documents, state, notifier, isMobile)
        else
          _buildCompactDocumentList(documents, state, notifier, isMobile),
      ],
    );
  }

  // Build individual document item
  Widget _buildDocumentItem({
    required DocumentType document,
    required DocumentNominationState state,
    required DocumentNominationNotifier notifier,
    required bool isMobile,
  }) {
    final isNominated = state.nominations.any((nomination) =>
        nomination.documentTypeId == document.id);

    return Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isNominated ? const Color(0xFFF0FDF4) : const Color(0xFFF9FAFB),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isNominated ? const Color(0xFF10B981) : const Color(0xFFE5E7EB),
            width: isNominated ? 2 : 1,
          ),
        ),
        child: Row(
        children: [
          // Selection indicator
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              shape: BoxShape.rectangle,
              borderRadius: BorderRadius.circular(4),
              color: isNominated ? AppColors.kBlueColor : Colors.transparent,
              border: Border.all(
                color: isNominated ? AppColors.kBlueColor : const Color(0xFFD1D5DB),
                width: 2,
              ),
            ),
            child: isNominated
                ? Container(
                    decoration: BoxDecoration(
                      color: AppColors.kBlueColor,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  )
                : null,
          ),

          const SizedBox(width: 12),

          // Document info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        document.name,
                        style: TextStyle(
                          fontSize: isMobile ? 14 : 16,
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFF111827),
                        ),
                      ),
                    ),
                    if (document.confirmsAddress == true)
                      const Icon(
                        Icons.home,
                        color: Color(0xFF10B981),
                        size: 16,
                      ),
                  ],
                ),
                if (document.dataFields.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    'Identity Proof',
                    style: TextStyle(
                      fontSize: isMobile ? 12 : 14,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                ],
              ],
            ),
          ),

          const SizedBox(width: 12),

          // Action button or status
          if (isNominated)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xFF10B981),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.check, color: Colors.white, size: 14),
                  const SizedBox(width: 4),
                  Text(
                    'Nominated',
                    style: TextStyle(
                      fontSize: isMobile ? 10 : 12,
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            )
          else
            IconButton(
              icon: const Icon(Icons.info_outline, color: Color(0xFF6B7280)),
              onPressed: () => _showDocumentInfo(document),
              tooltip: 'Document Information',
            ),
        ],
      ),
    );
  }

  // Build new document item matching the design
  Widget _buildNewDocumentItem({
    required DocumentType document,
    required DocumentNominationState state,
    required DocumentNominationNotifier notifier,
    required bool isMobile,
  }) {
    final isNominated = state.nominations.any((nomination) =>
        nomination.documentTypeId == document.id);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isNominated ? const Color(0xFFF0F9FF) : Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isNominated ? const Color(0xFF3B82F6) : const Color(0xFFE5E7EB),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Round checkbox (non-clickable)
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isNominated ? const Color(0xFF3B82F6) : Colors.transparent,
              border: Border.all(
                color: isNominated ? const Color(0xFF3B82F6) : const Color(0xFFD1D5DB),
                width: 2,
              ),
            ),
            child: isNominated
                ? const Icon(Icons.check, color: Colors.white, size: 12)
                : null,
          ),

          const SizedBox(width: 12),

          // Document info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  document.name,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF111827),
                  ),
                ),
                if (document.dataFields.isNotEmpty) ...[
                  const SizedBox(height: 2),
                  const Row(
                    children: [
                      Icon(
                        Icons.person,
                        size: 12,
                        color: Color(0xFF6B7280),
                      ),
                      SizedBox(width: 4),
                      Text(
                        'Identity Proof',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF6B7280),
                        ),
                      ),
                    ],
                  ),
                ],
                if (document.confirmsAddress) ...[
                  const SizedBox(height: 2),
                  const Row(
                    children: [
                      Icon(
                        Icons.home,
                        size: 12,
                        color: Color(0xFF6B7280),
                      ),
                      SizedBox(width: 4),
                      Text(
                        'Proof of Address',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF6B7280),
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),

          const SizedBox(width: 12),

          // Status or info button
          if (isNominated)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: const Color(0xFF3B82F6),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Text(
                'Nominated',
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            )
          else
            IconButton(
              icon: const Icon(Icons.info_outline, color: Color(0xFF9CA3AF), size: 20),
              onPressed: () => _showDocumentInfo(document),
              tooltip: 'Document Information',
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
            ),
        ],
      ),
    );
  }

  // Proceed button matching HTML exactly
  Widget _buildProceedButton(DocumentNominationState state, bool isMobile) {
    final canProceed = _canProceedToUpload(state);

    return Container(
      margin: const EdgeInsets.only(top: 32),
      width: double.infinity,
      child: Align(
        alignment: Alignment.centerRight,
        child: SizedBox(
          width: isMobile ? double.infinity : null,
          child: ElevatedButton(
            onPressed: canProceed ? () => _proceedToUpload(state) : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: canProceed
                  ? const Color(0xFF2563EB) // --primary-blue
                  : const Color(0xFF9CA3AF), // disabled:bg-gray-400
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              elevation: canProceed ? 4 : 0, // shadow-md
            ),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Proceed to Upload',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                SizedBox(width: 8),
                Icon(Icons.arrow_forward, size: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Helper methods
  bool _canProceedToUpload(DocumentNominationState state) {
    final hasGroup1 = _hasGroup1Document(state);
    final totalNominated = state.nominations.length;
    return hasGroup1 && totalNominated >= 2;
  }



  void _proceedToUpload(DocumentNominationState state) {
    // This should submit the nominations and proceed to the next step
    final notifier = ref.read(documentNominationProvider.notifier);
    _handleSubmitNominations(context, state, notifier);
  }



  Widget _buildOptimizedHeader(bool isMobile, bool isTablet, bool isDesktop) {
    if (isMobile) {
      // Professional Android-style app bar
      return Container(
        height: 56, // Standard Android AppBar height
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: SafeArea(
          bottom: false,
          child: Container(
            height: 56,
            padding: const EdgeInsets.symmetric(horizontal: 4.0),
            child: Row(
              children: [
                // Standard Android drawer button (hamburger menu)
                Builder(
                  builder: (context) => IconButton(
                    icon: Icon(
                      Icons.menu,
                      color: AppColors.kBlueColor,
                      size: 24,
                    ),
                    onPressed: () => Scaffold.of(context).openDrawer(),
                    padding: const EdgeInsets.all(16),
                    constraints: const BoxConstraints(
                      minWidth: 48,
                      minHeight: 48,
                    ),
                    splashRadius: 24,
                  ),
                ),

                // Logo centered
                Expanded(
                  child: Center(
                    child: Icon(
                      Icons.all_inclusive,
                      color: AppColors.kBlueColor,
                      size: 32,
                    ),
                  ),
                ),

                // Optional action buttons area (for future use)
                const SizedBox(width: 8),
              ],
            ),
          ),
        ),
      );
    }

    // Desktop header
    return Container(
      padding: const EdgeInsets.fromLTRB(24, 16, 24, 24),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Color(0xFFE5E7EB),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back, color: Color(0xFF374151)),
            onPressed: () => Navigator.of(context).pop(),
          ),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'DBS Check: Document Nomination',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF111827),
                  ),
                ),
                SizedBox(height: 2),
                Text(
                  'Select the documents you will provide for your DBS check.',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF6B7280),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentNominationContent(
    BuildContext context,
    DocumentNominationState state,
    DocumentNominationNotifier notifier,
    bool isMobile,
    bool isTablet,
    bool isDesktop,
    double paddingValue,
    double verticalPadding,
  ) {
    if (state.availableDocuments == null) {
      return _buildNoDocumentsContent(isMobile);
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(paddingValue),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Progress indicator
          if (state.availableDocuments?.routing != null)
            _buildProgressSection(state, isMobile, isTablet),

          SizedBox(height: verticalPadding),

          // Document groups
          _buildDocumentGroupsSection(
            context,
            state,
            notifier,
            isMobile,
            isTablet,
            isDesktop,
          ),

          SizedBox(height: verticalPadding * 2),

          // Submit section
          _buildSubmitSection(context, state, notifier, isMobile),
        ],
      ),
    );
  }

  Widget _buildNoDocumentsContent(bool isMobile) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(isMobile ? 24 : 48),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.description_outlined,
              size: isMobile ? 64 : 80,
              color: Colors.grey[400],
            ),
            SizedBox(height: isMobile ? 16 : 24),
            Text(
              'No Documents Available',
              style: TextStyle(
                fontSize: isMobile ? 18 : 22,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
            SizedBox(height: isMobile ? 8 : 12),
            Text(
              'Document information is being loaded. Please wait or try again.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: isMobile ? 14 : 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressSection(DocumentNominationState state, bool isMobile, bool isTablet) {
    final routing = state.availableDocuments?.routing;
    if (routing == null) return const SizedBox.shrink();

    return Container(
      padding: EdgeInsets.all(isMobile ? 16 : 20),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Document Requirements',
            style: TextStyle(
              fontSize: isMobile ? 16 : 18,
              fontWeight: FontWeight.bold,
              color: Colors.blue[800],
            ),
          ),
          const SizedBox(height: 8),

        ],
      ),
    );
  }

  Widget _buildDocumentGroupsSection(
    BuildContext context,
    DocumentNominationState state,
    DocumentNominationNotifier notifier,
    bool isMobile,
    bool isTablet,
    bool isDesktop,
  ) {
    final documents = state.availableDocuments?.documents;
    if (documents == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Available Documents',
          style: TextStyle(
            fontSize: isMobile ? 18 : 22,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
        const SizedBox(height: 16),

        // Build document groups
        ...documents.availableByGroup.entries.map((entry) {
          final groupKey = entry.key;
          final groupData = entry.value;

          return _buildDocumentGroupWidget(
            context,
            groupKey,
            groupData,
            state,
            notifier,
            isMobile,
            isTablet,
            isDesktop,
          );
        }),
      ],
    );
  }

  Widget _buildDocumentGroupWidget(
    BuildContext context,
    String groupKey,
    DocumentGroup groupData,
    DocumentNominationState state,
    DocumentNominationNotifier notifier,
    bool isMobile,
    bool isTablet,
    bool isDesktop,
  ) {
    final groupName = groupData.groupName;
    final documents = groupData.documents;

    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(isMobile ? 16 : 20),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    groupName,
                    style: TextStyle(
                      fontSize: isMobile ? 16 : 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[800],
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.blue[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${documents.length} documents',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue[800],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Document list
          if (isMobile)
            _buildMobileDocumentList(context, documents, state, notifier)
          else if (isTablet)
            _buildTabletDocumentList(context, documents, state, notifier)
          else
            _buildDesktopDocumentList(context, documents, state, notifier),
        ],
      ),
    );
  }

  Widget _buildSubmitSection(
    BuildContext context,
    DocumentNominationState state,
    DocumentNominationNotifier notifier,
    bool isMobile,
  ) {
    final hasNominations = state.hasNominations;

    return Container(
      padding: EdgeInsets.all(isMobile ? 16 : 24),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Nomination Summary',
            style: TextStyle(
              fontSize: isMobile ? 16 : 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 12),

          if (hasNominations) ...[
            Text(
              'You have nominated ${state.nominations.length} document(s)',
              style: TextStyle(
                fontSize: isMobile ? 14 : 16,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 16),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => _handleSubmitNominations(context, state, notifier),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.kBlueColor,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(
                    vertical: isMobile ? 12 : 16,
                    horizontal: 24,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'Submit Document Nominations',
                  style: TextStyle(
                    fontSize: isMobile ? 14 : 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ] else ...[
            Text(
              'Please select documents to nominate for verification',
              style: TextStyle(
                fontSize: isMobile ? 14 : 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Document list methods for different screen sizes
  Widget _buildMobileDocumentList(
    BuildContext context,
    List<DocumentType> documents,
    DocumentNominationState state,
    DocumentNominationNotifier notifier,
  ) {
    return Column(
      children: documents.map((document) {
        return _buildMobileDocumentCard(context, document, state, notifier);
      }).toList(),
    );
  }

  Widget _buildTabletDocumentList(
    BuildContext context,
    List<DocumentType> documents,
    DocumentNominationState state,
    DocumentNominationNotifier notifier,
  ) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 3,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: documents.length,
      itemBuilder: (context, index) {
        return _buildTabletDocumentCard(context, documents[index], state, notifier);
      },
    );
  }

  Widget _buildDesktopDocumentList(
    BuildContext context,
    List<DocumentType> documents,
    DocumentNominationState state,
    DocumentNominationNotifier notifier,
  ) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 2.5,
        crossAxisSpacing: 20,
        mainAxisSpacing: 20,
      ),
      itemCount: documents.length,
      itemBuilder: (context, index) {
        return _buildDesktopDocumentCard(context, documents[index], state, notifier);
      },
    );
  }

  Widget _buildMobileDocumentCard(
    BuildContext context,
    DocumentType document,
    DocumentNominationState state,
    DocumentNominationNotifier notifier,
  ) {
    final isNominated = state.isDocumentNominated(document.id);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isNominated ? AppColors.kBlueColor : Colors.grey[300]!,
          width: isNominated ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        title: Text(
          document.name,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            if (document.requiresPhoto)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.orange[100],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'Photo Required',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.orange[800],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
          ],
        ),
        trailing: null,
        onTap: () => _handleDocumentTap(document, state, notifier),
      ),
    );
  }

  Widget _buildTabletDocumentCard(
    BuildContext context,
    DocumentType document,
    DocumentNominationState state,
    DocumentNominationNotifier notifier,
  ) {
    final isNominated = state.isDocumentNominated(document.id);

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isNominated ? AppColors.kBlueColor : Colors.grey[300]!,
          width: isNominated ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _handleDocumentTap(document, state, notifier),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      document.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const Spacer(),
              if (document.requiresPhoto)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.orange[100],
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    'Photo Required',
                    style: TextStyle(
                      fontSize: 9,
                      color: Colors.orange[800],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDesktopDocumentCard(
    BuildContext context,
    DocumentType document,
    DocumentNominationState state,
    DocumentNominationNotifier notifier,
  ) {
    final isNominated = state.isDocumentNominated(document.id);

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isNominated ? AppColors.kBlueColor : Colors.grey[300]!,
          width: isNominated ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _handleDocumentTap(document, state, notifier),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      document.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const Spacer(),
              Row(
                children: [
                  if (document.requiresPhoto)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.orange[100],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'Photo Required',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.orange[800],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  const Spacer(),
                  Text(
                    isNominated ? 'Selected' : 'Select',
                    style: TextStyle(
                      fontSize: 12,
                      color: isNominated ? Colors.green[600] : Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }



  Future<void> _handleSubmitNominations(
    BuildContext context,
    DocumentNominationState state,
    DocumentNominationNotifier notifier,
  ) async {
    print('🚀 _handleSubmitNominations called');

    // Show loading
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    try {
      print('📋 Submitting nominations...');
      final success = await notifier.submitNominations(widget.applicationId);

      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        if (success) {
          print('✅ Nominations submitted successfully');
          _showSuccessDialog(context);
        } else {
          print('❌ Nomination submission failed');
          _showErrorDialog(context, state.error ?? 'Failed to submit nominations');
        }
      }
    } catch (e) {
      print('💥 Exception in _handleSubmitNominations: $e');
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        _showErrorDialog(context, e.toString());
      }
    }
  }


  void _showSuccessDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Success'),
        content: const Text('Document nominations submitted successfully!'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Go back to previous screen
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(BuildContext context, String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(error),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedContent(
    BuildContext context,
    DocumentNominationState state,
    DocumentNominationNotifier notifier,
  ) {
    if (state.availableDocuments == null) {
      return Container(
        color: Colors.white,
        child: Column(
          children: [
            _buildExactHeader(),
            const Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text(
                      'Loading documents...',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    }


    return Container(
      color: Colors.white,
      child: Column(
        children: [
          _buildExactHeader(),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildExactTasksSection(state),
                  const SizedBox(height: 24),
                  _buildExactKeySection(),
                  const SizedBox(height: 24),
                  if (state.nominations.isNotEmpty) _buildNominatedDocumentsSection(state),
                  if (state.nominations.isNotEmpty) const SizedBox(height: 24),
                  _buildExactDocumentGroups(state, notifier),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.arrow_back, color: Colors.blue),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
          const SizedBox(width: 16),
          Text(
            'Upload Documents - UK Residents',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: Colors.blue[700],
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildExactHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.arrow_back, color: Colors.blue, size: 20),
          ),
          const SizedBox(width: 8),
          const Text(
            'Upload Documents - UK Residents',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.blue,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExactTasksSection(DocumentNominationState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Tasks',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),
        _buildExactTaskItem('Upload 1 document from group 1', '0'),
        _buildExactTaskItem('Upload 2 additional documents from any group', '0'),
        _buildExactTaskItem('1 of the uploaded documents must show your current address', '0'),
      ],
    );
  }

  Widget _buildExactTaskItem(String text, String count) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xFFFFF3CD),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: const Color(0xFFFFE69C)),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              text,
              style: const TextStyle(
                fontSize: 13,
                color: Color(0xFF856404),
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.orange,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              count,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExactKeySection() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Key:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 12),
              _buildExactKeyItem('Proof of Address', 'Documents marked with a purple line provide proof of address', Colors.purple),
              const SizedBox(height: 8),
              _buildExactKeyItem('Identity Proof', 'Documents marked with a pink line provide identity proof', Colors.pink),
            ],
          ),
        ),
        const SizedBox(width: 24),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(4),
          ),
          child: const Text(
            'Submit',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black54,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildExactKeyItem(String title, String description, Color color) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 4,
          height: 40,
          color: color,
          margin: const EdgeInsets.only(right: 12),
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                description,
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.black54,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildNominatedDocumentsSection(DocumentNominationState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Nominated Documents',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),
        ...state.nominations.map((nomination) => _buildNominatedDocumentCard(nomination, state)),
      ],
    );
  }

  Widget _buildNominatedDocumentCard(DocumentNomination nomination, DocumentNominationState state) {
    // Find the document name from available documents
    String documentName = 'passport';
    for (final group in state.availableDocuments!.documents.availableByGroup.values) {
      for (final doc in group.documents) {
        if (doc.id == nomination.documentTypeId) {
          documentName = doc.name.toLowerCase().replaceAll(' ', '_');
          break;
        }
      }
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFE3F2FD),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFBBDEFB)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.red,
              borderRadius: BorderRadius.circular(4),
            ),
            child: const Text(
              'PDF',
              style: TextStyle(
                fontSize: 10,
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '$documentName.pdf',
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    const Text(
                      'Group 1 • ',
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.black54,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.green,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Text(
                        'Completed',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {},
            icon: const Icon(Icons.delete_outline, color: Colors.red, size: 20),
          ),
        ],
      ),
    );
  }

  Widget _buildExactDocumentGroups(DocumentNominationState state, DocumentNominationNotifier notifier) {
    final groups = state.availableDocuments!.documents.availableByGroup;

    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (groups.containsKey('1'))
              Expanded(child: _buildExactDocumentGroup('Group 1:', groups['1']!, notifier)),
            const SizedBox(width: 24),
            if (groups.containsKey('2a'))
              Expanded(child: _buildExactDocumentGroup('Group 2a:', groups['2a']!, notifier)),
            const SizedBox(width: 24),
            if (groups.containsKey('2b'))
              Expanded(child: _buildExactDocumentGroup('Group 2b:', groups['2b']!, notifier)),
          ],
        ),
      ],
    );
  }

  Widget _buildExactDocumentGroup(String title, DocumentGroup group, DocumentNominationNotifier notifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  group.groupName,
                  style: const TextStyle(
                    fontSize: 13,
                    color: Colors.black87,
                  ),
                ),
              ),
              const Icon(Icons.keyboard_arrow_down, color: Colors.grey, size: 20),
            ],
          ),
        ),
        const SizedBox(height: 12),
        ...group.documents.map((doc) => _buildExactDocumentItem(doc, notifier)),
      ],
    );
  }

  Widget _buildExactDocumentItem(DocumentType document, DocumentNominationNotifier notifier) {
    final state = ref.watch(documentNominationProvider);
    final isNominated = state.isDocumentNominated(document.id);

    final formData = _convertToDBSFormData(state.availableDocuments!.applicantContext);
    final relevanceScore = SmartDocumentService.getDocumentRelevanceScore(document.key, formData);
    final relevanceLevel = SmartDocumentService.getDocumentRelevanceLevel(relevanceScore);

    Color backgroundColor = Colors.transparent;
    Color borderColor = Colors.grey.shade300;

    switch (relevanceLevel) {
      case 'highly_recommended':
        backgroundColor = Colors.green.withValues(alpha: 0.05);
        borderColor = Colors.green.withValues(alpha: 0.3);
        break;
      case 'recommended':
        backgroundColor = Colors.blue.withValues(alpha: 0.05);
        borderColor = Colors.blue.withValues(alpha: 0.3);
        break;
      case 'suitable':
        backgroundColor = Colors.orange.withValues(alpha: 0.05);
        borderColor = Colors.orange.withValues(alpha: 0.3);
        break;
      default:
        break;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: backgroundColor,
        border: Border.all(color: borderColor),
        borderRadius: BorderRadius.circular(4),
      ),
      child: InkWell(
        onTap: () => _navigateToDocumentDetail(context, document, state),
        borderRadius: BorderRadius.circular(4),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
          child: Row(
            children: [
              Container(
                width: 12,
                height: 12,
                margin: const EdgeInsets.only(right: 8),
                decoration: BoxDecoration(
                  color: isNominated ? Colors.green : Colors.grey.shade400,
                  shape: BoxShape.circle,
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            document.name,
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.black87,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),

                      ],
                    ),
                    if (relevanceScore > 0.5)
                      Padding(
                        padding: const EdgeInsets.only(top: 2),
                        child: Text(
                          _getRelevanceHint(document.key, formData),
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey.shade600,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              const SizedBox(width: 8),
              Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.grey.shade400),
                ),
                child: Icon(
                  isNominated ? Icons.check : Icons.help_outline,
                  size: 12,
                  color: isNominated ? Colors.green : Colors.grey,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getRelevanceHint(String documentKey, DBSFormData formData) {
    final hints = SmartDocumentService.getContextualHints('document_number', documentKey, formData);
    if (hints.isNotEmpty) {
      return hints.first;
    }

    switch (documentKey) {
      case 'passport':
        return 'Perfect for UK nationals';
      case 'driving_licence':
      case 'drivers_licence':
        return 'Great if you drive';
      case 'birth_certificate':
        return 'Strong identity proof';
      case 'bank_statement':
        return 'Shows current address';
      case 'council_tax':
        return 'Confirms address history';
      default:
        return 'Suitable document option';
    }
  }

  Widget _buildTasksSection(DocumentNominationState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Tasks',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        _buildTaskItem(
          isCompleted: false,
          text: 'Upload at least 1 document from group 1',
        ),
        const SizedBox(height: 8),
        _buildTaskItem(
          isCompleted: false,
          text: 'Upload 2 additional documents from any group',
        ),
        const SizedBox(height: 8),
        _buildTaskItem(
          isCompleted: false,
          text: '1 of the uploaded documents must show your current address',
        ),
      ],
    );
  }



  Widget _buildKeySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Key:',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),
        _buildKeyItem(
          'Proof of Address',
          'Documents marked with a purple line provide proof of address',
          Colors.purple,
        ),
        const SizedBox(height: 12),
        _buildKeyItem(
          'Proof of Date of Birth',
          'Documents marked with a pink line provide proof of date of birth',
          Colors.pink,
        ),
      ],
    );
  }

  Widget _buildKeyItem(String title, String description, Color color) {
    return Row(
      children: [
        Container(
          width: 4,
          height: 40,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState(BuildContext context, String error, DocumentNominationNotifier notifier) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to Load Documents',
              style: TextStyle(
                fontSize: ResponsiveHelper.getResponsiveFontSize(
                  context,
                  mobile: 18.0,
                  tablet: 20.0,
                  desktop: 22.0,
                ),
                fontWeight: FontWeight.w600,
                color: Colors.red.shade700,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: ResponsiveHelper.getResponsiveFontSize(
                  context,
                  mobile: 14.0,
                  tablet: 15.0,
                  desktop: 16.0,
                ),
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                notifier.loadAvailableDocuments(widget.applicationId);
              },
              style: ComponentConfig.primaryButtonStyle,
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }



  Widget _buildRouteSelector(
    BuildContext context,
    DocumentNominationState state,
    DocumentNominationNotifier notifier,
  ) {
    final availableRoutes = state.availableDocuments!.availableRoutes;
    
    if (availableRoutes.length <= 1) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Select Route',
              style: TextStyle(
                fontSize: ResponsiveHelper.getResponsiveFontSize(
                  context,
                  mobile: 15.0,
                  tablet: 16.0,
                  desktop: 16.0,
                ),
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              children: availableRoutes.map((route) {
                final isSelected = route == state.selectedRoute;

                return FilterChip(
                  label: Text('Route $route'),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      notifier.selectRoute(route);
                    }
                  },
                  selectedColor: AppColors.kBlueColor.withValues(alpha: 0.2),
                  checkmarkColor: AppColors.kBlueColor,
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTasksAndKeySection(BuildContext context, DocumentNominationState state) {
    final isMobile = ResponsiveHelper.isMobile(context);

    if (isMobile) {
      return Column(
        children: [
          _buildTasksWidget(context, state),
          const SizedBox(height: 12),
          const SizedBox.shrink(), // Removed key widget for now
        ],
      );
    } else {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 3,
            child: _buildTasksWidget(context, state),
          ),
          const SizedBox(width: 16),
          const Expanded(
            flex: 2,
            child: SizedBox.shrink(), // Removed key widget for now
          ),
        ],
      );
    }
  }

  Widget _buildTasksWidget(BuildContext context, DocumentNominationState state) {
    final routeRequirement = state.availableDocuments!.routeRequirements['route_${state.selectedRoute}'];
    final nominatedCount = state.nominations.length;
    final addressDocumentsCount = state.nominations.where((n) => n.confirmsAddress).length;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: DesignConfig.cardBackgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: DesignConfig.primaryBorderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Tasks for Route ${state.selectedRoute}',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: DesignConfig.primaryTextColor,
            ),
          ),
          const SizedBox(height: 8),
          if (routeRequirement != null) ..._buildDynamicTasks(routeRequirement, nominatedCount, addressDocumentsCount),
        ],
      ),
    );
  }

  List<Widget> _buildDynamicTasks(RouteRequirement routeRequirement, int nominatedCount, int addressDocumentsCount) {
    final tasks = <Widget>[];

    // Task 1: Group 1 requirement (check if group_1 exists in requiredGroups)
    final group1Requirement = routeRequirement.requiredGroups['group_1'];
    if (group1Requirement != null && group1Requirement.min > 0) {
      final group1Count = nominatedCount >= group1Requirement.min ? group1Requirement.min : nominatedCount;
      tasks.add(_buildTaskItem(
        isCompleted: group1Count >= group1Requirement.min,
        text: 'Upload ${group1Requirement.min} document${group1Requirement.min > 1 ? 's' : ''} from Group 1',
      ));
      tasks.add(const SizedBox(height: 6));
    }

    // Task 2: Total documents requirement
    if (routeRequirement.totalDocuments > 0) {
      tasks.add(_buildTaskItem(
        isCompleted: nominatedCount >= routeRequirement.totalDocuments,
        text: 'Upload ${routeRequirement.totalDocuments} total documents',
      ));
      tasks.add(const SizedBox(height: 6));
    }

    // Task 3: Address confirmation requirement
    if (routeRequirement.addressConfirmationRequired) {
      tasks.add(_buildTaskItem(
        isCompleted: addressDocumentsCount >= 1,
        text: '1 document must show your current address',
      ));
      tasks.add(const SizedBox(height: 6));
    }

    // Add additional group requirements if they exist
    for (final entry in routeRequirement.requiredGroups.entries) {
      if (entry.key != 'group_1' && entry.value.min > 0) {
        final groupName = entry.key.replaceAll('_', ' ').toUpperCase();
        tasks.add(_buildTaskItem(
          isCompleted: false, // This would need proper completion logic
          text: 'Upload ${entry.value.min} document${entry.value.min > 1 ? 's' : ''} from $groupName',
        ));
        tasks.add(const SizedBox(height: 6));
      }
    }

    // Remove the last SizedBox if tasks exist
    if (tasks.isNotEmpty && tasks.last is SizedBox) {
      tasks.removeLast();
    }

    return tasks;
  }



  // Navigation methods
  void _navigateBackToDashboard() {
    Navigator.of(context).pop();
  }

  void _navigateToDocumentDetail(
    BuildContext context,
    DocumentType document,
    DocumentNominationState state,
  ) {
    try {
      final existingNomination = state.getNominationForDocument(document.id);

      // If document is already nominated, go directly to edit
      if (existingNomination != null) {
        Navigator.of(context).pushNamed(
          '/document-detail',
          arguments: {
            'applicationId': widget.applicationId,
            'applicantName': widget.applicantName,
            'documentType': document,
            'existingNomination': existingNomination,
            'applicantId': widget.applicantId,
          },
        );
      } else {
        // Check if AI scanner is enabled for this entity
        final documentNominationState = ref.read(documentNominationProvider);
        final isAiScannerEnabled = documentNominationState.isAiDocumentScannerEnabled;

        if (isAiScannerEnabled) {
          // For new documents, show processing choice
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => DocumentProcessingChoiceScreen(
                documentType: document.key,
                applicationId: int.parse(widget.applicationId),
                documentConfig: {
                  'name': document.name,
                  'requires_photo': document.requiresPhoto,
                  'data_fields': document.dataFields.map((field) => {
                    'name': field.name,
                    'label': field.label,
                    'type': field.type,
                    'required': field.required,
                  }).toList(),
                },
              ),
            ),
          );
        } else {
          // AI scanner disabled, go directly to manual entry
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => ManualDocumentEntryScreen(
                documentType: document.key,
                applicationId: int.parse(widget.applicationId),
                documentConfig: {
                  'name': document.name,
                  'requires_photo': document.requiresPhoto,
                  'data_fields': document.dataFields.map((field) => {
                    'name': field.name,
                    'label': field.label,
                    'type': field.type,
                    'required': field.required,
                  }).toList(),
                },
              ),
            ),
          );
        }
      }
    } catch (e) {
      // Handle navigation error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Navigation error: $e')),
      );
    }
  }

  // Helper method for DBS form data conversion
  DBSFormData _convertToDBSFormData(ApplicantContext context) {
    return DBSFormData(
      applicantDetails: ApplicantDetailsData(
        title: '',
        forename: 'John',
        middlenames: [],
        presentSurname: 'Smith',
        dateOfBirth: '1990-05-15',
        gender: 'Male',
        niNumber: '',
        email: '',
        contactNumber: '',
        currentAddress: CurrentAddressData(
          addressLine1: '',
          addressLine2: '',
          addressTown: '',
          addressCounty: '',
          postcode: 'SW1A 1AA',
          countryCode: context.currentAddressCountry == 'United Kingdom' ? 'GB' : 'GB',
          residentFromGyearMonth: '',
        ),
        previousAddresses: [],
        additionalApplicantDetails: AdditionalApplicantDetailsData(
          birthSurname: '',
          birthSurnameUntil: '',
          otherSurnames: [],
          otherForenames: [],
          birthTown: 'London',
          birthCounty: 'Greater London',
          birthCountry: 'United Kingdom',
          birthNationality: context.nationality,
          unspentConvictions: 'n',
          declarationByApplicant: 'n',
          languagePreference: 'english',
        ),
        applicantIdentityDetails: ApplicantIdentityDetailsData(
          identityVerified: 'y',
          evidenceCheckedBy: '',
          nationalInsuranceNumber: '',
        ),
      ),
    );
  }
}
