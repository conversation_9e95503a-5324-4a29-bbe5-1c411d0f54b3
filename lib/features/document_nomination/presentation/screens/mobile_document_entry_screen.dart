import 'dart:io';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_nomination.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:SolidCheck/features/document_nomination/presentation/widgets/mobile_document_upload_widget.dart';
import 'package:SolidCheck/features/document_nomination/presentation/widgets/mobile_form_field_builder.dart';
import 'package:SolidCheck/shared/data/country_data.dart';
import 'package:SolidCheck/shared/widgets/navigation/universal_mobile_header.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:intl/intl.dart';

class MobileDocumentEntryScreen extends StatefulWidget {
  final DocumentType documentType;
  final DocumentNomination? existingNomination;
  final String applicationId;
  final String applicantName;
  final String? applicantId;

  const MobileDocumentEntryScreen({
    super.key,
    required this.documentType,
    this.existingNomination,
    required this.applicationId,
    required this.applicantName,
    this.applicantId,
  });

  @override
  State<MobileDocumentEntryScreen> createState() => _MobileDocumentEntryScreenState();
}

class _MobileDocumentEntryScreenState extends State<MobileDocumentEntryScreen> {
  final _formKey = GlobalKey<FormBuilderState>();
  File? _uploadedFile;
  bool _confirmsAddress = false;

  @override
  void initState() {
    super.initState();
    _confirmsAddress = widget.existingNomination?.confirmsAddress ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      drawer: _buildDrawer(),
      body: _buildBody(),
    );
  }

  Widget _buildDrawer() {
    return Drawer(
      child: Container(
        color: Colors.white,
        child: Column(
          children: [
            // Enhanced Header with back button
            Container(
              padding: const EdgeInsets.fromLTRB(20, 60, 20, 24),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                      decoration: BoxDecoration(
                        color: AppColors.kBlueColor,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.kBlueColor.withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.arrow_back_ios,
                            color: Colors.white,
                            size: 18,
                          ),
                          SizedBox(width: 12),
                          Text(
                            'Back to Dashboard',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              letterSpacing: 0.5,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Menu items
            Expanded(
              child: Column(
                children: [
                  _buildDrawerItem(
                    icon: Icons.grid_view,
                    title: 'Overview',
                    isActive: false,
                    onTap: () => Navigator.of(context).pop(),
                  ),
                  _buildDrawerItem(
                    icon: Icons.verified_user,
                    title: 'DBS Enhanced Check',
                    isActive: true,
                    onTap: () {},
                  ),
                  _buildDrawerItem(
                    icon: Icons.people,
                    title: 'Reference Check',
                    isActive: false,
                    onTap: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 6),
      decoration: BoxDecoration(
        color: isActive ? AppColors.kBlueColor : Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        boxShadow: isActive ? [
          BoxShadow(
            color: AppColors.kBlueColor.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ] : null,
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: isActive
                ? Colors.white.withValues(alpha: 0.2)
                : AppColors.kBlueColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: isActive ? Colors.white : AppColors.kBlueColor,
            size: 20,
          ),
        ),
        title: Text(
          title,
          style: TextStyle(
            color: isActive ? Colors.white : Colors.black87,
            fontSize: 16,
            fontWeight: isActive ? FontWeight.w600 : FontWeight.w500,
            letterSpacing: 0.3,
          ),
        ),
        onTap: onTap,
      ),
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        // Universal mobile header
        UniversalMobileHeader(
          showBackButton: true,
          onBackPressed: () => Navigator.of(context).pop(),
        ),
        // Main content
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Document type header
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: Text(
                    widget.documentType.name,
                    style: const TextStyle(
                      color: Colors.black87,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Form content
                _buildForm(),
              ],
            ),
          ),
        ),
        // Bottom buttons
        _buildBottomButtons(),
      ],
    );
  }



  Widget _buildForm() {
    return FormBuilder(
      key: _formKey,
      child: Column(
        children: [
          _buildFormCard(),
          const SizedBox(height: 16),
          if (widget.documentType.requiresPhoto) _buildUploadCard(),
        ],
      ),
    );
  }

  Widget _buildFormCard() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: _buildFormFields(),
      ),
    );
  }

  List<Widget> _buildFormFields() {
    final fields = widget.documentType.dataFields;
    final widgets = <Widget>[];

    // Group fields by sections for better layout
    final personalFields = <DocumentDataField>[];
    final contactFields = <DocumentDataField>[];
    final documentFields = <DocumentDataField>[];
    final otherFields = <DocumentDataField>[];

    for (final field in fields) {
      final fieldName = field.name.toLowerCase();
      if (fieldName.contains('title') || fieldName.contains('first_name') ||
          fieldName.contains('forename') || fieldName.contains('last_name') ||
          fieldName.contains('surname') || fieldName.contains('gender') ||
          fieldName.contains('middle_name')) {
        personalFields.add(field);
      } else if (fieldName.contains('email') || fieldName.contains('contact') ||
                 fieldName.contains('phone') || fieldName.contains('mobile')) {
        contactFields.add(field);
      } else if (fieldName.contains('document') || fieldName.contains('number') ||
                 fieldName.contains('issue') || fieldName.contains('expiry') ||
                 fieldName.contains('authority') || fieldName.contains('country')) {
        documentFields.add(field);
      } else {
        otherFields.add(field);
      }
    }

    // Build personal information section
    if (personalFields.isNotEmpty) {
      widgets.addAll(_buildPersonalSection(personalFields));
    }

    // Build contact details section
    if (contactFields.isNotEmpty) {
      widgets.add(MobileFormFieldBuilder.buildSectionHeader('Contact Details:'));
      widgets.addAll(_buildContactSection(contactFields));
    }

    // Build document information section
    if (documentFields.isNotEmpty) {
      widgets.add(MobileFormFieldBuilder.buildSectionHeader('Document Information:'));
      widgets.addAll(_buildDocumentSection(documentFields));
    }

    // Build other fields
    if (otherFields.isNotEmpty) {
      widgets.addAll(_buildOtherSection(otherFields));
    }

    return widgets;
  }

  List<Widget> _buildPersonalSection(List<DocumentDataField> fields) {
    final widgets = <Widget>[];

    // Find specific fields for better layout
    final titleField = fields.where((f) => f.name.toLowerCase().contains('title')).firstOrNull;
    final firstNameField = fields.where((f) => f.name.toLowerCase().contains('first_name') || f.name.toLowerCase().contains('forename')).firstOrNull;
    final lastNameField = fields.where((f) => f.name.toLowerCase().contains('last_name') || f.name.toLowerCase().contains('surname')).firstOrNull;
    final genderField = fields.where((f) => f.name.toLowerCase().contains('gender')).firstOrNull;
    final middleNameField = fields.where((f) => f.name.toLowerCase().contains('middle_name')).firstOrNull;

    // Build title and name row (like DBS form)
    if (titleField != null || firstNameField != null || lastNameField != null) {
      final rowFields = <Widget>[];

      if (genderField != null) {
        widgets.add(Padding(
          padding: const EdgeInsets.only(bottom: 20),
          child: MobileFormFieldBuilder.buildField(
            field: genderField,
            initialValue: widget.existingNomination?.documentData[genderField.name]?.toString(),
          ),
        ));
      }

      if (titleField != null) {
        widgets.add(Padding(
          padding: const EdgeInsets.only(bottom: 20),
          child: MobileFormFieldBuilder.buildField(
            field: titleField,
            initialValue: widget.existingNomination?.documentData[titleField.name]?.toString(),
          ),
        ));
      }

      // First and Last name in a row
      if (firstNameField != null && lastNameField != null) {
        widgets.add(Padding(
          padding: const EdgeInsets.only(bottom: 20),
          child: MobileFormFieldBuilder.buildFieldRow([
            MobileFormFieldBuilder.buildField(
              field: firstNameField,
              initialValue: widget.existingNomination?.documentData[firstNameField.name]?.toString(),
            ),
            MobileFormFieldBuilder.buildField(
              field: lastNameField,
              initialValue: widget.existingNomination?.documentData[lastNameField.name]?.toString(),
            ),
          ]),
        ));
      } else {
        if (firstNameField != null) {
          widgets.add(Padding(
            padding: const EdgeInsets.only(bottom: 20),
            child: MobileFormFieldBuilder.buildField(
              field: firstNameField,
              initialValue: widget.existingNomination?.documentData[firstNameField.name]?.toString(),
            ),
          ));
        }
        if (lastNameField != null) {
          widgets.add(Padding(
            padding: const EdgeInsets.only(bottom: 20),
            child: MobileFormFieldBuilder.buildField(
              field: lastNameField,
              initialValue: widget.existingNomination?.documentData[lastNameField.name]?.toString(),
            ),
          ));
        }
      }

      if (middleNameField != null) {
        widgets.add(Padding(
          padding: const EdgeInsets.only(bottom: 20),
          child: MobileFormFieldBuilder.buildField(
            field: middleNameField,
            initialValue: widget.existingNomination?.documentData[middleNameField.name]?.toString(),
          ),
        ));
      }
    }

    // Add any remaining personal fields
    final processedFields = {titleField, firstNameField, lastNameField, genderField, middleNameField};
    for (final field in fields) {
      if (!processedFields.contains(field)) {
        widgets.add(Padding(
          padding: const EdgeInsets.only(bottom: 20),
          child: MobileFormFieldBuilder.buildField(
            field: field,
            initialValue: widget.existingNomination?.documentData[field.name]?.toString(),
          ),
        ));
      }
    }

    return widgets;
  }

  List<Widget> _buildContactSection(List<DocumentDataField> fields) {
    final widgets = <Widget>[];

    // Find email and contact fields for better layout
    final emailField = fields.where((f) => f.name.toLowerCase().contains('email')).firstOrNull;
    final contactField = fields.where((f) => f.name.toLowerCase().contains('contact') || f.name.toLowerCase().contains('phone') || f.name.toLowerCase().contains('mobile')).firstOrNull;

    // Build email and contact in a row (like DBS form)
    if (emailField != null && contactField != null) {
      widgets.add(Padding(
        padding: const EdgeInsets.only(bottom: 20),
        child: MobileFormFieldBuilder.buildFieldRow([
          MobileFormFieldBuilder.buildField(
            field: emailField,
            initialValue: widget.existingNomination?.documentData[emailField.name]?.toString(),
          ),
          MobileFormFieldBuilder.buildField(
            field: contactField,
            initialValue: widget.existingNomination?.documentData[contactField.name]?.toString(),
          ),
        ]),
      ));
    } else {
      // Add fields individually if not both present
      for (final field in fields) {
        widgets.add(Padding(
          padding: const EdgeInsets.only(bottom: 20),
          child: MobileFormFieldBuilder.buildField(
            field: field,
            initialValue: widget.existingNomination?.documentData[field.name]?.toString(),
          ),
        ));
      }
    }

    return widgets;
  }

  List<Widget> _buildDocumentSection(List<DocumentDataField> fields) {
    final widgets = <Widget>[];

    for (final field in fields) {
      widgets.add(Padding(
        padding: const EdgeInsets.only(bottom: 20),
        child: MobileFormFieldBuilder.buildField(
          field: field,
          initialValue: widget.existingNomination?.documentData[field.name]?.toString(),
        ),
      ));
    }

    return widgets;
  }

  List<Widget> _buildOtherSection(List<DocumentDataField> fields) {
    final widgets = <Widget>[];

    for (final field in fields) {
      widgets.add(Padding(
        padding: const EdgeInsets.only(bottom: 20),
        child: MobileFormFieldBuilder.buildField(
          field: field,
          initialValue: widget.existingNomination?.documentData[field.name]?.toString(),
        ),
      ));
    }

    return widgets;
  }

  Widget _buildUploadCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Document File (Optional)',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You can optionally attach a photo or scan of your document.',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 16),
          MobileDocumentUploadWidget(
            onFileSelected: (file) {
              setState(() {
                _uploadedFile = file;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.grey.shade600,
                  side: BorderSide(color: Colors.grey.shade300),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  'Cancel',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: ElevatedButton(
                onPressed: _handleSubmit,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.kBlueColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  widget.existingNomination != null ? 'Update Document' : 'Nominate Document',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleSubmit() {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      final formData = _formKey.currentState!.value;
      final documentData = <String, dynamic>{};

      for (final field in widget.documentType.dataFields) {
        final value = formData[field.name];
        if (value != null) {
          if (value is DateTime) {
            // Format dates as YYYY-MM-DD for backend compatibility
            documentData[field.name] = DateFormat('yyyy-MM-dd').format(value);
          } else {
            documentData[field.name] = value.toString();
          }
        }
      }

      if (_uploadedFile != null) {
        documentData['uploaded_file_path'] = _uploadedFile!.path;
        documentData['uploaded_file_name'] = _uploadedFile!.path.split('/').last;
      }

      final nomination = DocumentNomination(
        documentTypeId: widget.documentType.id,
        documentData: documentData,
        confirmsAddress: _confirmsAddress,
      );

      Navigator.of(context).pop(nomination);
    }
  }
}
