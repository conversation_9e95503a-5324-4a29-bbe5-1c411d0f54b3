import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/features/document_nomination/data/models/route_requirement.dart';
import 'package:SolidCheck/features/document_nomination/data/models/validation_response.dart';
import 'package:flutter/material.dart';

class RouteProgressIndicator extends StatelessWidget {
  final int selectedRoute;
  final RouteRequirement? routeRequirement;
  final ValidationData? validationData;
  final int nominatedDocumentsCount;

  const RouteProgressIndicator({
    super.key,
    required this.selectedRoute,
    this.routeRequirement,
    this.validationData,
    required this.nominatedDocumentsCount,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: 12),
            _buildProgressContent(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final isCompleted = validationData?.validationResult.routeCompleted ?? false;
    final canProceed = validationData?.validationResult.canProceed ?? false;
    
    return Row(
      children: [
        Icon(
          isCompleted 
              ? Icons.check_circle 
              : canProceed 
                  ? Icons.radio_button_checked 
                  : Icons.radio_button_unchecked,
          color: isCompleted 
              ? Colors.green 
              : canProceed 
                  ? AppColors.kBlueColor 
                  : Colors.grey,
          size: 24,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Route $selectedRoute Progress',
                style: TextStyle(
                  fontSize: ResponsiveHelper.getResponsiveFontSize(
                    context,
                    mobile: 16.0,
                    tablet: 17.0,
                    desktop: 18.0,
                  ),
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              if (routeRequirement != null)
                Text(
                  routeRequirement!.description,
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getResponsiveFontSize(
                      context,
                      mobile: 12.0,
                      tablet: 13.0,
                      desktop: 14.0,
                    ),
                    color: Colors.grey.shade600,
                  ),
                ),
            ],
          ),
        ),
        _buildStatusBadge(context),
      ],
    );
  }

  Widget _buildStatusBadge(BuildContext context) {
    final isCompleted = validationData?.validationResult.routeCompleted ?? false;
    final canProceed = validationData?.validationResult.canProceed ?? false;
    
    String text;
    Color backgroundColor;
    Color textColor;
    
    if (isCompleted) {
      text = 'Complete';
      backgroundColor = Colors.green;
      textColor = Colors.white;
    } else if (canProceed) {
      text = 'Ready';
      backgroundColor = AppColors.kBlueColor;
      textColor = Colors.white;
    } else {
      text = 'In Progress';
      backgroundColor = Colors.orange.shade100;
      textColor = Colors.orange.shade700;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: ResponsiveHelper.getResponsiveFontSize(
            context,
            mobile: 11.0,
            tablet: 12.0,
            desktop: 12.0,
          ),
          fontWeight: FontWeight.w600,
          color: textColor,
        ),
      ),
    );
  }

  Widget _buildProgressContent(BuildContext context) {
    if (routeRequirement == null) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        _buildDocumentCountProgress(context),
        const SizedBox(height: 8),
        _buildGroupRequirements(context),
        const SizedBox(height: 8),
        _buildAddressConfirmation(context),
      ],
    );
  }

  Widget _buildDocumentCountProgress(BuildContext context) {
    final totalRequired = routeRequirement!.totalDocuments;
    final progress = nominatedDocumentsCount / totalRequired;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Documents',
              style: TextStyle(
                fontSize: ResponsiveHelper.getResponsiveFontSize(
                  context,
                  mobile: 13.0,
                  tablet: 14.0,
                  desktop: 14.0,
                ),
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '$nominatedDocumentsCount / $totalRequired',
              style: TextStyle(
                fontSize: ResponsiveHelper.getResponsiveFontSize(
                  context,
                  mobile: 13.0,
                  tablet: 14.0,
                  desktop: 14.0,
                ),
                fontWeight: FontWeight.w600,
                color: nominatedDocumentsCount >= totalRequired 
                    ? Colors.green 
                    : AppColors.kBlueColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: progress.clamp(0.0, 1.0),
          backgroundColor: Colors.grey.shade200,
          valueColor: AlwaysStoppedAnimation<Color>(
            nominatedDocumentsCount >= totalRequired 
                ? Colors.green 
                : AppColors.kBlueColor,
          ),
        ),
      ],
    );
  }

  Widget _buildGroupRequirements(BuildContext context) {
    if (validationData?.routeAnalysis.requirementsMet.isEmpty ?? true) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Group Requirements',
          style: TextStyle(
            fontSize: ResponsiveHelper.getResponsiveFontSize(
              context,
              mobile: 13.0,
              tablet: 14.0,
              desktop: 14.0,
            ),
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        ...validationData!.routeAnalysis.requirementsMet.entries
            .where((entry) => entry.key.contains('group'))
            .map((entry) => _buildRequirementRow(context, entry.key, entry.value)),
      ],
    );
  }

  Widget _buildAddressConfirmation(BuildContext context) {
    if (!routeRequirement!.addressConfirmationRequired) {
      return const SizedBox.shrink();
    }

    final addressRequirement = validationData?.routeAnalysis.requirementsMet['address_confirmation'];
    final isMet = addressRequirement?.met ?? false;

    return _buildRequirementRow(
      context, 
      'Address Confirmation', 
      addressRequirement ?? RequirementStatus(required: 1, provided: 0, met: false),
    );
  }

  Widget _buildRequirementRow(BuildContext context, String label, RequirementStatus status) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Icon(
            status.met ? Icons.check_circle : Icons.radio_button_unchecked,
            color: status.met ? Colors.green : Colors.grey,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _formatRequirementLabel(label),
              style: TextStyle(
                fontSize: ResponsiveHelper.getResponsiveFontSize(
                  context,
                  mobile: 12.0,
                  tablet: 13.0,
                  desktop: 13.0,
                ),
                color: status.met ? Colors.green.shade700 : Colors.grey.shade600,
              ),
            ),
          ),
          if (status.required > 0)
            Text(
              '${status.provided}/${status.required}',
              style: TextStyle(
                fontSize: ResponsiveHelper.getResponsiveFontSize(
                  context,
                  mobile: 12.0,
                  tablet: 13.0,
                  desktop: 13.0,
                ),
                fontWeight: FontWeight.w500,
                color: status.met ? Colors.green.shade700 : Colors.grey.shade600,
              ),
            ),
        ],
      ),
    );
  }

  String _formatRequirementLabel(String label) {
    return label
        .replaceAll('_', ' ')
        .split(' ')
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ');
  }
}
