import 'dart:io';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/form_fields/dbs_form_builder_fields.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_nomination.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:SolidCheck/features/document_nomination/presentation/widgets/document_upload_widget.dart';
import 'package:SolidCheck/shared/data/country_data.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:intl/intl.dart';

class EnhancedDocumentForm extends StatefulWidget {
  final DocumentType documentType;
  final DocumentNomination? existingNomination;
  final Function(DocumentNomination) onSave;
  final VoidCallback onCancel;

  const EnhancedDocumentForm({
    super.key,
    required this.documentType,
    this.existingNomination,
    required this.onSave,
    required this.onCancel,
  });

  @override
  State<EnhancedDocumentForm> createState() => _EnhancedDocumentFormState();
}

class _EnhancedDocumentFormState extends State<EnhancedDocumentForm> {
  final _formKey = GlobalKey<FormBuilderState>();
  File? _uploadedFile;
  final bool _hasUploadedFile = false;
  bool _confirmsAddress = false;

  @override
  void initState() {
    super.initState();
    _confirmsAddress = widget.existingNomination?.confirmsAddress ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(),
          _buildForm(),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.kBlueColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              widget.documentType.name,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          IconButton(
            onPressed: widget.onCancel,
            icon: const Icon(Icons.close, color: Colors.white),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        ],
      ),
    );
  }

  Widget _buildForm() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: FormBuilder(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ...widget.documentType.dataFields.map((field) {
              return _buildFormField(field);
            }),
            const SizedBox(height: 20),
            if (_documentRequiresPhoto()) _buildUploadSection(),
            if (_documentRequiresPhoto()) const SizedBox(height: 20),
            _buildAddressConfirmationSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildFormField(DocumentDataField field) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: _buildFieldByType(field),
    );
  }

  Widget _buildFieldByType(DocumentDataField field) {
    final initialValue = widget.existingNomination?.documentData[field.name]?.toString();
    
    if (field.type == 'date') {
      return _buildDateField(field, initialValue);
    } else if (field.name.toLowerCase().contains('country')) {
      return _buildCountryDropdown(field, initialValue);
    } else if (field.name.toLowerCase().contains('full_name')) {
      return _buildNameConfirmationField(field);
    } else {
      return _buildTextField(field, initialValue);
    }
  }

  Widget _buildTextField(DocumentDataField field, String? initialValue) {
    return DBSFormBuilderTextField(
      name: field.name,
      label: field.label,
      isRequired: field.required,
      hint: 'Enter ${field.label.toLowerCase()}',
      initialValue: initialValue,
      validators: [
        if (field.required)
          FormBuilderValidators.required(errorText: '${field.label} is required'),
      ],
    );
  }

  Widget _buildDateField(DocumentDataField field, String? initialValue) {
    DateTime? parsedInitialValue;
    if (initialValue != null && initialValue.isNotEmpty) {
      try {
        parsedInitialValue = DateFormat('dd/MM/yyyy').parse(initialValue);
      } catch (e) {
        // If parsing fails, leave as null
      }
    }

    return Row(
      children: [
        Expanded(
          child: DBSFormBuilderDatePicker(
            name: field.name,
            label: field.label,
            isRequired: field.required,
            initialValue: parsedInitialValue,
            firstDate: DateTime(1900),
            lastDate: DateTime.now().add(const Duration(days: 365 * 10)),
            validators: [
              if (field.required)
                FormBuilderValidators.required(errorText: '${field.label} is required'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCountryDropdown(DocumentDataField field, String? initialValue) {
    return DBSFormBuilderDropdown<String>(
      name: field.name,
      label: field.label,
      isRequired: field.required,
      hint: 'Select country',
      items: Countries.getCountryDropdownItems(),
      initialValue: initialValue ?? 'GB',
      validators: [
        if (field.required)
          FormBuilderValidators.required(errorText: '${field.label} is required'),
      ],
    );
  }

  Widget _buildNameConfirmationField(DocumentDataField field) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Can you confirm the full name on this document is BASILON JACKEY?',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildConfirmationButton(
                'Yes',
                Icons.check_circle,
                Colors.green,
                true,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildConfirmationButton(
                'No',
                Icons.cancel,
                Colors.red,
                false,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildConfirmationButton(String label, IconData icon, Color color, bool value) {
    return FormBuilderField<bool>(
      name: 'name_confirmation',
      builder: (field) {
        return GestureDetector(
          onTap: () => field.didChange(value),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(
                color: field.value == value ? color : Colors.grey.shade300,
                width: field.value == value ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(8),
              color: field.value == value ? color.withValues(alpha: 0.1) : Colors.white,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color: field.value == value ? color : Colors.grey,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  label,
                  style: TextStyle(
                    color: field.value == value ? color : Colors.grey.shade700,
                    fontWeight: field.value == value ? FontWeight.w600 : FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildUploadSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Document File (Optional)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'You can optionally attach a photo or scan of your document',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          height: 120,
          decoration: BoxDecoration(
            border: Border.all(
              color: Colors.grey.shade300,
              style: BorderStyle.solid,
            ),
            borderRadius: BorderRadius.circular(8),
            color: Colors.grey.shade50,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.cloud_upload_outlined,
                size: 32,
                color: Colors.grey.shade600,
              ),
              const SizedBox(height: 8),
              Text(
                'Tap to select document file',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAddressConfirmationSection() {
    if (!widget.documentType.confirmsAddress) return const SizedBox.shrink();
    
    return FormBuilderCheckbox(
      name: 'confirms_address',
      title: const Text(
        'This document confirms my current address',
        style: TextStyle(fontSize: 14),
      ),
      initialValue: _confirmsAddress,
      onChanged: (value) {
        setState(() {
          _confirmsAddress = value ?? false;
        });
      },
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: widget.onCancel,
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                side: const BorderSide(color: Colors.grey),
              ),
              child: const Text('Cancel'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _handleSave,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.kBlueColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: Text(widget.existingNomination != null ? 'Update Document' : 'Nominate Document'),
            ),
          ),
        ],
      ),
    );
  }

  void _handleSave() {
    if (_formKey.currentState?.saveAndValidate() ?? false) {
      final formData = _formKey.currentState!.value;
      final documentData = <String, dynamic>{};

      for (final field in widget.documentType.dataFields) {
        final value = formData[field.name];
        if (value != null) {
          if (value is DateTime) {
            // Format dates as YYYY-MM-DD for backend compatibility
            documentData[field.name] = DateFormat('yyyy-MM-dd').format(value);
          } else {
            documentData[field.name] = value.toString();
          }
        }
      }

      final nomination = DocumentNomination(
        documentTypeId: widget.documentType.id,
        documentData: documentData,
        confirmsAddress: _confirmsAddress,
      );

      widget.onSave(nomination);
    }
  }

  bool _documentRequiresPhoto() {
    return widget.documentType.requiresPhoto;
  }
}
