import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:flutter/material.dart';

class DocumentSelectionCard extends StatelessWidget {
  final DocumentType documentType;
  final bool isSelected;
  final bool isNominated;
  final VoidCallback? onTap;
  final bool isEnabled;

  const DocumentSelectionCard({
    super.key,
    required this.documentType,
    required this.isSelected,
    required this.isNominated,
    this.onTap,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {

    
    final cardPadding = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 12.0,
      tablet: 14.0,
      desktop: 16.0,
    );

    final iconSize = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 20.0,
      tablet: 22.0,
      desktop: 24.0,
    );

    return Card(
      elevation: isSelected ? 4 : 1,
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color: isSelected
              ? AppColors.kBlueColor
              : isNominated
                  ? AppColors.kBlueColor
                  : Colors.grey.shade300,
          width: isSelected || isNominated ? 2 : 1,
        ),
      ),
      child: InkWell(
        onTap: isEnabled ? onTap : null,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: EdgeInsets.all(cardPadding),
          child: Row(
            children: [
              _buildIcon(iconSize),
              const SizedBox(width: 12),
              Expanded(
                child: _buildContent(context),
              ),
              if (isSelected && !isNominated) _buildSelectedIndicator(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIcon(double iconSize) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: isSelected
            ? AppColors.kBlueColor
            : isNominated
                ? AppColors.kBlueColor
                : Colors.grey.shade400,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Icon(
        _getDocumentIcon(),
        color: Colors.white,
        size: iconSize,
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          documentType.name,
          style: TextStyle(
            fontSize: ResponsiveHelper.getResponsiveFontSize(
              context,
              mobile: 14.0,
              tablet: 15.0,
              desktop: 16.0,
            ),
            fontWeight: FontWeight.w600,
            color: isEnabled ? Colors.black87 : Colors.grey.shade500,
          ),
        ),
        const SizedBox(height: 4),
        _buildFeatures(context),
      ],
    );
  }

  Widget _buildFeatures(BuildContext context) {
    final features = <String>[];
    
    if (documentType.requiresPhoto) {
      features.add('Photo required');
    }
    
    if (documentType.confirmsAddress) {
      features.add('Confirms address');
    }
    
    features.add('Group ${documentType.documentGroup}');

    return Wrap(
      spacing: 8,
      runSpacing: 4,
      children: features.map((feature) => _buildFeatureChip(context, feature)).toList(),
    );
  }

  Widget _buildFeatureChip(BuildContext context, String feature) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: feature.contains('address') 
            ? Colors.green.shade50 
            : feature.contains('Photo') 
                ? Colors.orange.shade50 
                : Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: feature.contains('address') 
              ? Colors.green.shade200 
              : feature.contains('Photo') 
                  ? Colors.orange.shade200 
                  : Colors.blue.shade200,
        ),
      ),
      child: Text(
        feature,
        style: TextStyle(
          fontSize: ResponsiveHelper.getResponsiveFontSize(
            context,
            mobile: 10.0,
            tablet: 11.0,
            desktop: 12.0,
          ),
          color: feature.contains('address') 
              ? Colors.green.shade700 
              : feature.contains('Photo') 
                  ? Colors.orange.shade700 
                  : Colors.blue.shade700,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }



  Widget _buildSelectedIndicator() {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: AppColors.kBlueColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Icon(
        Icons.add,
        color: Colors.white,
        size: 16,
      ),
    );
  }

  IconData _getDocumentIcon() {
    final name = documentType.name.toLowerCase();
    
    if (name.contains('passport')) return Icons.flight_takeoff;
    if (name.contains('driving') || name.contains('licence')) return Icons.directions_car;
    if (name.contains('birth')) return Icons.child_care;
    if (name.contains('residence') || name.contains('permit')) return Icons.home;
    if (name.contains('bank')) return Icons.account_balance;
    if (name.contains('utility') || name.contains('bill')) return Icons.receipt;
    if (name.contains('council')) return Icons.location_city;
    if (name.contains('mortgage')) return Icons.home_work;
    if (name.contains('p45') || name.contains('p60')) return Icons.work;
    if (name.contains('benefit')) return Icons.money;
    
    return Icons.description;
  }
}
