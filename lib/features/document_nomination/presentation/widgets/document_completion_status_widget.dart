import 'package:flutter/material.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/navigation/app_router_new.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';

class DocumentCompletionStatusWidget extends StatelessWidget {
  final String applicationId;
  final List<Map<String, dynamic>> nominatedDocuments;
  final String currentStatus;

  const DocumentCompletionStatusWidget({
    super.key,
    required this.applicationId,
    required this.nominatedDocuments,
    required this.currentStatus,
  });

  @override
  Widget build(BuildContext context) {
    final isMobile = context.isMobile;

    return Container(
      margin: EdgeInsets.all(isMobile ? 16.0 : 24.0),
      padding: EdgeInsets.all(isMobile ? 20.0 : 32.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context, isMobile),
          const SizedBox(height: 24),
          _buildStatusCard(context, isMobile),
          const SizedBox(height: 24),
          _buildDocumentsList(context, isMobile),
          const SizedBox(height: 32),
          _buildActionButtons(context, isMobile),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context, bool isMobile) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            Icons.check_circle,
            color: Colors.green,
            size: 32,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Documents Successfully Submitted',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.green[700],
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Your documents have been nominated and are now under review',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatusCard(BuildContext context, bool isMobile) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.kFillColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.kBlueColor.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppColors.kBlueColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Current Status',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.kBlueColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: _getStatusColor().withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: _getStatusColor().withValues(alpha: 0.3)),
            ),
            child: Text(
              _getStatusDisplayText(),
              style: TextStyle(
                color: _getStatusColor(),
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
          ),
          const SizedBox(height: 12),
          Text(
            _getStatusMessage(),
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentsList(BuildContext context, bool isMobile) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Nominated Documents (${nominatedDocuments.length})',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...nominatedDocuments.map((doc) => _buildDocumentTile(context, doc, isMobile)),
      ],
    );
  }

  Widget _buildDocumentTile(BuildContext context, Map<String, dynamic> document, bool isMobile) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey[50],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.description,
              color: Colors.green,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  document['document_name'] ?? 'Unknown Document',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Route ${document['route_number'] ?? 'N/A'} • Submitted ${_formatDate(document['nominated_at'])}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          const Icon(
            Icons.check_circle,
            color: Colors.green,
            size: 20,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, bool isMobile) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () {
              Navigator.pushNamed(
                context,
                AppRouter.documentStatus,
                arguments: {'applicationId': applicationId},
              );
            },
            icon: const Icon(Icons.visibility),
            label: const Text('View Detailed Status'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.kBlueColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
            },
            icon: const Icon(Icons.arrow_back),
            label: const Text('Back to Dashboard'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.kBlueColor,
              side: BorderSide(color: AppColors.kBlueColor),
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Color _getStatusColor() {
    switch (currentStatus.toLowerCase()) {
      case 'approved':
        return Colors.green;
      case 'under_review':
      case 'nominated':
        return Colors.orange;
      case 'rejected':
        return Colors.red;
      default:
        return AppColors.kBlueColor;
    }
  }

  String _getStatusDisplayText() {
    switch (currentStatus.toLowerCase()) {
      case 'approved':
        return 'Documents Approved';
      case 'under_review':
        return 'Under Review';
      case 'nominated':
        return 'Submitted for Review';
      case 'rejected':
        return 'Action Required';
      default:
        return 'In Progress';
    }
  }

  String _getStatusMessage() {
    switch (currentStatus.toLowerCase()) {
      case 'approved':
        return 'All documents have been approved and your application has been submitted to DBS for processing.';
      case 'under_review':
      case 'nominated':
        return 'Your documents are currently being reviewed by our team. This typically takes 3-5 business days.';
      case 'rejected':
        return 'Some documents require attention. Please check the detailed status for more information.';
      default:
        return 'Your documents are being processed. Please check back later for updates.';
    }
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return 'Unknown';
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }
}
