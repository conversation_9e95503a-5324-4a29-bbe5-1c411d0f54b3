import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/features/document_nomination/data/models/validation_response.dart';
import 'package:flutter/material.dart';

class ValidationMessages extends StatelessWidget {
  final List<DocumentValidation> documentValidations;
  final String? nextStepsMessage;

  const ValidationMessages({
    super.key,
    required this.documentValidations,
    this.nextStepsMessage,
  });

  @override
  Widget build(BuildContext context) {
    final hasMessages = documentValidations.any((doc) => 
        doc.errors.isNotEmpty || doc.warnings.isNotEmpty) || 
        nextStepsMessage != null;

    if (!hasMessages) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 1,
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: 12),
            ...documentValidations.map((doc) => _buildDocumentValidation(context, doc)),
            if (nextStepsMessage != null) _buildNextSteps(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final hasErrors = documentValidations.any((doc) => doc.errors.isNotEmpty);
    final hasWarnings = documentValidations.any((doc) => doc.warnings.isNotEmpty);

    return Row(
      children: [
        Icon(
          hasErrors 
              ? Icons.error 
              : hasWarnings 
                  ? Icons.warning 
                  : Icons.info,
          color: hasErrors 
              ? Colors.red 
              : hasWarnings 
                  ? Colors.orange 
                  : AppColors.kBlueColor,
          size: 20,
        ),
        const SizedBox(width: 8),
        Text(
          hasErrors 
              ? 'Validation Errors' 
              : hasWarnings 
                  ? 'Validation Warnings' 
                  : 'Validation Messages',
          style: TextStyle(
            fontSize: ResponsiveHelper.getResponsiveFontSize(
              context,
              mobile: 15.0,
              tablet: 16.0,
              desktop: 16.0,
            ),
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget _buildDocumentValidation(BuildContext context, DocumentValidation validation) {
    if (validation.errors.isEmpty && validation.warnings.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (validation.errors.isNotEmpty)
            ...validation.errors.map((error) => _buildMessage(
              context, 
              error, 
              MessageType.error,
            )),
          if (validation.warnings.isNotEmpty)
            ...validation.warnings.map((warning) => _buildMessage(
              context, 
              warning, 
              MessageType.warning,
            )),
        ],
      ),
    );
  }

  Widget _buildMessage(BuildContext context, String message, MessageType type) {
    Color backgroundColor;
    Color borderColor;
    Color iconColor;
    Color textColor;
    IconData icon;

    switch (type) {
      case MessageType.error:
        backgroundColor = Colors.red.shade50;
        borderColor = Colors.red.shade200;
        iconColor = Colors.red.shade600;
        textColor = Colors.red.shade800;
        icon = Icons.error_outline;
        break;
      case MessageType.warning:
        backgroundColor = Colors.orange.shade50;
        borderColor = Colors.orange.shade200;
        iconColor = Colors.orange.shade600;
        textColor = Colors.orange.shade800;
        icon = Icons.warning_outlined;
        break;
      case MessageType.success:
        backgroundColor = Colors.green.shade50;
        borderColor = Colors.green.shade200;
        iconColor = Colors.green.shade600;
        textColor = Colors.green.shade800;
        icon = Icons.check_circle_outline;
        break;
      case MessageType.info:
        backgroundColor = Colors.blue.shade50;
        borderColor = Colors.blue.shade200;
        iconColor = Colors.blue.shade600;
        textColor = Colors.blue.shade800;
        icon = Icons.info_outline;
        break;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: borderColor),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: iconColor,
            size: 18,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                fontSize: ResponsiveHelper.getResponsiveFontSize(
                  context,
                  mobile: 13.0,
                  tablet: 14.0,
                  desktop: 14.0,
                ),
                color: textColor,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNextSteps(BuildContext context) {
    if (nextStepsMessage == null) return const SizedBox.shrink();

    return _buildMessage(context, nextStepsMessage!, MessageType.info);
  }
}

enum MessageType {
  error,
  warning,
  success,
  info,
}

class ValidationSuccessMessage extends StatelessWidget {
  final String message;

  const ValidationSuccessMessage({
    super.key,
    required this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.shade200),
      ),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: Colors.green.shade600,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Validation Successful',
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getResponsiveFontSize(
                      context,
                      mobile: 15.0,
                      tablet: 16.0,
                      desktop: 16.0,
                    ),
                    fontWeight: FontWeight.w600,
                    color: Colors.green.shade800,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  message,
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getResponsiveFontSize(
                      context,
                      mobile: 13.0,
                      tablet: 14.0,
                      desktop: 14.0,
                    ),
                    color: Colors.green.shade700,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
