import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:SolidCheck/shared/data/country_data.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:intl/intl.dart';

class MobileFormFieldBuilder {
  static Widget buildField({
    required DocumentDataField field,
    String? initialValue,
    String? applicantName,
  }) {
    switch (field.type) {
      case 'date':
        return _buildDateField(field, initialValue);
      case 'yes_no_confirmation':
        return _buildYesNoField(field, applicantName);
      case 'dropdown':
        return _buildDropdownField(field, initialValue);
      case 'country':
        return _buildCountryField(field, initialValue);
      case 'radio':
      case 'gender':
        return _buildRadioField(field, initialValue);
      case 'title':
        return _buildTitleField(field, initialValue);
      default:
        return _buildTextField(field, initialValue);
    }
  }

  static Widget buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(top: 24, bottom: 16),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: AppColors.kBlueColor,
        ),
      ),
    );
  }

  static Widget buildFieldRow(List<Widget> fields) {
    if (fields.length == 1) {
      return fields.first;
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: fields.asMap().entries.map((entry) {
        final index = entry.key;
        final field = entry.value;

        return Expanded(
          child: Container(
            margin: EdgeInsets.only(
              right: index < fields.length - 1 ? 16 : 0,
            ),
            child: field,
          ),
        );
      }).toList(),
    );
  }

  static Widget _buildTextField(DocumentDataField field, String? initialValue) {
    // Determine max length based on field type
    int? maxLength;
    if (field.name.toLowerCase().contains('first_name') || field.name.toLowerCase().contains('forename')) {
      maxLength = 60;
    } else if (field.name.toLowerCase().contains('last_name') || field.name.toLowerCase().contains('surname')) {
      maxLength = 60;
    } else if (field.name.toLowerCase().contains('contact') || field.name.toLowerCase().contains('phone')) {
      maxLength = 30;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(field),
        const SizedBox(height: 8),
        SizedBox(
          height: 48, // DBS field height
          child: FormBuilderTextField(
            name: field.name,
            initialValue: initialValue,
            maxLength: maxLength,
            decoration: InputDecoration(
              hintText: 'Enter ${field.label.toLowerCase()}',
              hintStyle: TextStyle(color: Colors.grey.shade500, fontSize: 14),
              filled: true,
              fillColor: Colors.white,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.kBlueColor, width: 2),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.red, width: 1),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.red, width: 2),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              counterStyle: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 12,
              ),
            ),
            validator: field.required
                ? FormBuilderValidators.required(errorText: '${field.label} is required')
                : null,
          ),
        ),
      ],
    );
  }

  static Widget _buildDateField(DocumentDataField field, String? initialValue) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(field),
        const SizedBox(height: 8),
        FormBuilderField<String>(
          name: field.name,
          initialValue: initialValue,
          validator: field.required
              ? FormBuilderValidators.compose([
                  FormBuilderValidators.required(errorText: '${field.label} is required'),
                  (value) {
                    if (value != null && value.isNotEmpty) {
                      try {
                        DateFormat('dd/MM/yyyy').parseStrict(value);
                        return null;
                      } catch (e) {
                        return 'Please enter a valid date (DD/MM/YYYY)';
                      }
                    }
                    return null;
                  },
                ])
              : (value) {
                  if (value != null && value.isNotEmpty) {
                    try {
                      DateFormat('dd/MM/yyyy').parseStrict(value);
                      return null;
                    } catch (e) {
                      return 'Please enter a valid date (DD/MM/YYYY)';
                    }
                  }
                  return null;
                },
          builder: (FormFieldState<String> fieldState) {
            final controller = TextEditingController(text: fieldState.value ?? '');

            return SizedBox(
              height: 48, // DBS field height
              child: TextFormField(
                controller: controller,
                decoration: InputDecoration(
                  hintText: 'DD/MM/YYYY',
                  hintStyle: TextStyle(color: Colors.grey.shade500, fontSize: 14),
                  filled: true,
                  fillColor: Colors.white,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: AppColors.kBlueColor, width: 2),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Colors.red, width: 1),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Colors.red, width: 2),
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.calendar_today, color: Colors.grey),
                  onPressed: () async {
                    final currentDate = DateTime.now();
                    DateTime? initialDate;

                    // Try to parse current value as initial date
                    if (controller.text.isNotEmpty) {
                      try {
                        initialDate = DateFormat('dd/MM/yyyy').parse(controller.text);
                      } catch (e) {
                        initialDate = currentDate;
                      }
                    } else {
                      initialDate = currentDate;
                    }

                    final selectedDate = await showDatePicker(
                      context: fieldState.context,
                      initialDate: initialDate,
                      firstDate: DateTime(1900),
                      lastDate: DateTime(2100),
                      builder: (context, child) {
                        return Theme(
                          data: Theme.of(context).copyWith(
                            colorScheme: Theme.of(context).colorScheme.copyWith(
                              primary: AppColors.kBlueColor,
                            ),
                          ),
                          child: child!,
                        );
                      },
                    );

                    if (selectedDate != null) {
                      final formattedDate = DateFormat('dd/MM/yyyy').format(selectedDate);
                      controller.text = formattedDate;
                      fieldState.didChange(formattedDate);
                    }
                  },
                ),
                errorText: fieldState.errorText,
              ),
              keyboardType: TextInputType.datetime,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'[0-9/]')),
                LengthLimitingTextInputFormatter(10),
                _DateInputFormatter(),
              ],
              onChanged: (value) {
                fieldState.didChange(value);
              },
            ),
          );
          },
        ),
      ],
    );
  }

  static Widget _buildYesNoField(DocumentDataField field, String? applicantName) {
    final displayName = applicantName ?? 'APPLICANT NAME';
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Can you confirm the full name on this document is $displayName?',
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),
        FormBuilderField<bool>(
          name: field.name,
          validator: field.required
              ? (value) => value == null ? 'Please select an option' : null
              : null,
          builder: (FormFieldState<bool> fieldState) {
            return Row(
              children: [
                SizedBox(
                  width: 80,
                  child: _buildYesNoButton(
                    text: 'Yes',
                    icon: Icons.check,
                    color: Colors.green,
                    isSelected: fieldState.value == true,
                    onTap: () => fieldState.didChange(true),
                  ),
                ),
                const SizedBox(width: 12),
                SizedBox(
                  width: 80,
                  child: _buildYesNoButton(
                    text: 'No',
                    icon: Icons.close,
                    color: Colors.red,
                    isSelected: fieldState.value == false,
                    onTap: () => fieldState.didChange(false),
                  ),
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  static Widget _buildYesNoButton({
    required String text,
    required IconData icon,
    required Color color,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? color.withValues(alpha: 0.1) : Colors.grey.shade100,
          border: Border.all(
            color: isSelected ? color : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isSelected ? color : Colors.grey.shade600,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              text,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: isSelected ? color : Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  static Widget _buildDropdownField(DocumentDataField field, String? initialValue) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(field),
        const SizedBox(height: 8),
        SizedBox(
          height: 48, // DBS field height
          child: FormBuilderDropdown<String>(
            name: field.name,
            initialValue: initialValue,
            decoration: InputDecoration(
              hintText: 'Select ${field.label.toLowerCase()}',
              hintStyle: TextStyle(color: Colors.grey.shade500, fontSize: 14),
              filled: true,
              fillColor: Colors.white,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.kBlueColor, width: 2),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.red, width: 1),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.red, width: 2),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          items: (field.options ?? [])
              .map((option) => DropdownMenuItem(
                    value: option,
                    child: Text(
                      option,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ))
              .toList(),
          validator: field.required
              ? FormBuilderValidators.required(errorText: '${field.label} is required')
              : null,
          ),
        ),
      ],
    );
  }

  static Widget _buildCountryField(DocumentDataField field, String? initialValue) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(field),
        const SizedBox(height: 8),
        SizedBox(
          height: 48, // DBS field height
          child: FormBuilderDropdown<String>(
            name: field.name,
            initialValue: initialValue,
            decoration: InputDecoration(
              hintText: 'Select country',
              hintStyle: TextStyle(color: Colors.grey.shade500, fontSize: 14),
              filled: true,
              fillColor: Colors.white,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.kBlueColor, width: 2),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.red, width: 1),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.red, width: 2),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          items: Countries.all
              .map((country) => DropdownMenuItem(
                    value: country.name,
                    child: Text(
                      country.name,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ))
              .toList(),
          validator: field.required
              ? FormBuilderValidators.required(errorText: '${field.label} is required')
              : null,
          ),
        ),
      ],
    );
  }

  static Widget _buildTitleField(DocumentDataField field, String? initialValue) {
    final titleOptions = ['Mr', 'Mrs', 'Miss', 'Ms', 'Dr', 'Prof', 'Rev', 'Other'];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(field),
        const SizedBox(height: 8),
        SizedBox(
          height: 48, // DBS field height
          child: FormBuilderDropdown<String>(
            name: field.name,
            initialValue: initialValue,
            decoration: InputDecoration(
              hintText: 'Select title',
              hintStyle: TextStyle(color: Colors.grey.shade500, fontSize: 14),
              filled: true,
              fillColor: Colors.white,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppColors.kBlueColor, width: 2),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.red, width: 1),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.red, width: 2),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            suffixIcon: const Icon(Icons.arrow_drop_down, color: Colors.grey),
          ),
          items: titleOptions
              .map((title) => DropdownMenuItem(
                    value: title,
                    child: Text(
                      title,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ))
              .toList(),
          validator: field.required
              ? FormBuilderValidators.required(errorText: '${field.label} is required')
              : null,
          ),
        ),
      ],
    );
  }

  static Widget _buildRadioField(DocumentDataField field, String? initialValue) {
    // Define options based on field type
    List<String> options = [];
    if (field.type == 'gender' || field.name.toLowerCase().contains('gender')) {
      options = ['Male', 'Female'];
    } else if (field.options != null && field.options!.isNotEmpty) {
      options = field.options!;
    } else {
      options = ['Yes', 'No'];
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(field),
        const SizedBox(height: 12),
        FormBuilderField<String>(
          name: field.name,
          initialValue: initialValue,
          validator: field.required
              ? (value) => value == null ? '${field.label} is required' : null
              : null,
          builder: (FormFieldState<String> fieldState) {
            return Column(
              children: [
                Row(
                  children: options.map((option) {
                    return Expanded(
                      child: GestureDetector(
                        onTap: () => fieldState.didChange(option),
                        child: Container(
                          margin: EdgeInsets.only(
                            right: option != options.last ? 12 : 0,
                          ),
                          child: Row(
                            children: [
                              Radio<String>(
                                value: option,
                                groupValue: fieldState.value,
                                onChanged: (String? value) {
                                  if (value != null) {
                                    fieldState.didChange(value);
                                  }
                                },
                                activeColor: AppColors.kBlueColor,
                                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              ),
                              Expanded(
                                child: Text(
                                  option,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: Colors.black87,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
                if (fieldState.hasError)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Text(
                      fieldState.errorText!,
                      style: const TextStyle(
                        color: Colors.red,
                        fontSize: 12,
                      ),
                    ),
                  ),
              ],
            );
          },
        ),
      ],
    );
  }

  static Widget _buildFieldLabel(DocumentDataField field) {
    return RichText(
      text: TextSpan(
        text: field.label,
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        children: field.required
            ? [
                const TextSpan(
                  text: ' *',
                  style: TextStyle(color: Colors.red),
                ),
              ]
            : null,
      ),
    );
  }
}

/// Custom input formatter for date fields (DD/MM/YYYY)
class _DateInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text;

    // Remove any non-digit characters except /
    String digitsOnly = text.replaceAll(RegExp(r'[^0-9/]'), '');

    // Auto-format as user types
    String formatted = '';
    int digitCount = 0;

    for (int i = 0; i < digitsOnly.length; i++) {
      final char = digitsOnly[i];

      if (char == '/') {
        if (digitCount == 2 || digitCount == 5) {
          formatted += char;
        }
        continue;
      }

      formatted += char;
      digitCount++;

      // Add slashes after day and month
      if (digitCount == 2 && i < digitsOnly.length - 1 && digitsOnly[i + 1] != '/') {
        formatted += '/';
      } else if (digitCount == 4 && i < digitsOnly.length - 1 && digitsOnly[i + 1] != '/') {
        formatted += '/';
      }

      // Limit to 8 digits (DD/MM/YYYY)
      if (digitCount >= 8) break;
    }

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}
