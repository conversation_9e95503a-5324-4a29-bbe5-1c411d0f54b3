import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/features/dbs/data/models/dbs_form_data.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:SolidCheck/shared/data/country_data.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class YesNoConfirmationField extends StatefulWidget {
  final DocumentDataField field;
  final String? initialValue;
  final Function(String?) onChanged;
  final String? confirmationText;
  final String? errorText;

  const YesNoConfirmationField({
    super.key,
    required this.field,
    this.initialValue,
    required this.onChanged,
    this.confirmationText,
    this.errorText,
  });

  @override
  State<YesNoConfirmationField> createState() => _YesNoConfirmationFieldState();
}

class _YesNoConfirmationFieldState extends State<YesNoConfirmationField> {
  String? _selectedValue;

  @override
  void initState() {
    super.initState();
    _selectedValue = widget.initialValue;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.confirmationText ?? 'Can you confirm this information is correct?',
          style: TextStyle(
            fontSize: ResponsiveHelper.getResponsiveFontSize(
              context,
              mobile: 16.0,
              tablet: 17.0,
              desktop: 18.0,
            ),
            fontWeight: FontWeight.w600,
            color: AppColors.kBlackColor,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            SizedBox(
              width: 80,
              child: _buildOptionButton(
                'Yes',
                'yes',
                Icons.check_circle,
                Colors.green,
              ),
            ),
            const SizedBox(width: 12),
            SizedBox(
              width: 80,
              child: _buildOptionButton(
                'No',
                'no',
                Icons.cancel,
                AppColors.kRedColor,
              ),
            ),
          ],
        ),
        if (widget.errorText != null) ...[
          const SizedBox(height: 8),
          Text(
            widget.errorText!,
            style: TextStyle(
              color: AppColors.kRedColor,
              fontSize: ResponsiveHelper.getResponsiveFontSize(
                context,
                mobile: 12.0,
                tablet: 13.0,
                desktop: 14.0,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildOptionButton(String label, String value, IconData icon, Color color) {
    final isSelected = _selectedValue == value;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedValue = value;
        });
        widget.onChanged(value);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
        decoration: BoxDecoration(
          color: isSelected ? color.withValues(alpha: 0.1) : Colors.grey[50],
          border: Border.all(
            color: isSelected ? color : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isSelected ? color : Colors.grey[600],
              size: ResponsiveHelper.getResponsiveFontSize(
                context,
                mobile: 16.0,
                tablet: 18.0,
                desktop: 20.0,
              ),
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                fontSize: ResponsiveHelper.getResponsiveFontSize(
                  context,
                  mobile: 14.0,
                  tablet: 15.0,
                  desktop: 16.0,
                ),
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                color: isSelected ? color : Colors.grey[700],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class MultipleChoicePostcodeField extends StatefulWidget {
  final DocumentDataField field;
  final String? initialValue;
  final Function(String?) onChanged;
  final List<String> postcodeOptions;
  final String? errorText;

  const MultipleChoicePostcodeField({
    super.key,
    required this.field,
    this.initialValue,
    required this.onChanged,
    required this.postcodeOptions,
    this.errorText,
  });

  @override
  State<MultipleChoicePostcodeField> createState() => _MultipleChoicePostcodeFieldState();
}

class _MultipleChoicePostcodeFieldState extends State<MultipleChoicePostcodeField> {
  String? _selectedValue;

  @override
  void initState() {
    super.initState();
    _selectedValue = widget.initialValue;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Please select the correct postcode from the document:',
          style: TextStyle(
            fontSize: ResponsiveHelper.getResponsiveFontSize(
              context,
              mobile: 16.0,
              tablet: 17.0,
              desktop: 18.0,
            ),
            fontWeight: FontWeight.w600,
            color: AppColors.kBlackColor,
          ),
        ),
        const SizedBox(height: 12),
        ...widget.postcodeOptions.asMap().entries.map((entry) {
          final index = entry.key;
          final postcode = entry.value;
          return Padding(
            padding: EdgeInsets.only(bottom: index < widget.postcodeOptions.length - 1 ? 12 : 0),
            child: _buildPostcodeOption(postcode),
          );
        }),
        if (widget.errorText != null) ...[
          const SizedBox(height: 8),
          Text(
            widget.errorText!,
            style: TextStyle(
              color: AppColors.kRedColor,
              fontSize: ResponsiveHelper.getResponsiveFontSize(
                context,
                mobile: 12.0,
                tablet: 13.0,
                desktop: 14.0,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildPostcodeOption(String postcode) {
    final isSelected = _selectedValue == postcode;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedValue = postcode;
        });
        widget.onChanged(postcode);
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.kBlueColor.withValues(alpha: 0.1) : Colors.grey[50],
          border: Border.all(
            color: isSelected ? AppColors.kBlueColor : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isSelected ? AppColors.kBlueColor : Colors.transparent,
                border: Border.all(
                  color: isSelected ? AppColors.kBlueColor : Colors.grey[400]!,
                  width: 2,
                ),
              ),
              child: isSelected
                  ? const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 14,
                    )
                  : null,
            ),
            const SizedBox(width: 12),
            Text(
              postcode,
              style: TextStyle(
                fontSize: ResponsiveHelper.getResponsiveFontSize(
                  context,
                  mobile: 16.0,
                  tablet: 17.0,
                  desktop: 18.0,
                ),
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                color: isSelected ? AppColors.kBlueColor : Colors.grey[700],
                letterSpacing: 1.2,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class SmartDateField extends StatefulWidget {
  final DocumentDataField field;
  final String? initialValue;
  final Function(String?) onChanged;
  final String? errorText;

  const SmartDateField({
    super.key,
    required this.field,
    this.initialValue,
    required this.onChanged,
    this.errorText,
  });

  @override
  State<SmartDateField> createState() => _SmartDateFieldState();
}

class _SmartDateFieldState extends State<SmartDateField> {
  final TextEditingController _controller = TextEditingController();
  DateTime? _selectedDate;

  @override
  void initState() {
    super.initState();
    if (widget.initialValue != null && widget.initialValue!.isNotEmpty) {
      _controller.text = widget.initialValue!;
      try {
        final parts = widget.initialValue!.split('/');
        if (parts.length == 3) {
          _selectedDate = DateTime(
            int.parse(parts[2]),
            int.parse(parts[1]),
            int.parse(parts[0]),
          );
        }
      } catch (e) {
        // Invalid date format
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.field.label,
          style: TextStyle(
            fontSize: ResponsiveHelper.getResponsiveFontSize(
              context,
              mobile: 14.0,
              tablet: 15.0,
              desktop: 16.0,
            ),
            fontWeight: FontWeight.w600,
            color: AppColors.kBlackColor,
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 48, // DBS field height
          child: TextFormField(
            controller: _controller,
            decoration: InputDecoration(
              hintText: 'DD/MM/YYYY',
              hintStyle: TextStyle(
                color: Colors.grey[500],
                fontSize: 14,
              ),
              filled: true,
              fillColor: Colors.white,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: Colors.grey[300]!,
                  width: 1,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: Colors.grey[300]!,
                  width: 1,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: AppColors.kBlueColor,
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: Colors.red,
                  width: 1,
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: Colors.red,
                  width: 2,
                ),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              suffixIcon: IconButton(
                icon: Icon(
                  Icons.calendar_today,
                  color: AppColors.kBlueColor,
                  size: 20,
                ),
                onPressed: _selectDate,
              ),
            ),
            keyboardType: TextInputType.datetime,
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'[0-9/]')),
              LengthLimitingTextInputFormatter(10),
              _DateInputFormatter(),
            ],
            onChanged: (value) {
              widget.onChanged(value);
            },
          ),
        ),
        if (widget.errorText != null) ...[
          const SizedBox(height: 8),
          Text(
            widget.errorText!,
            style: TextStyle(
              color: AppColors.kRedColor,
              fontSize: ResponsiveHelper.getResponsiveFontSize(
                context,
                mobile: 12.0,
                tablet: 13.0,
                desktop: 14.0,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime(2100),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.kBlueColor,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _selectedDate = picked;
        _controller.text = '${picked.day.toString().padLeft(2, '0')}/${picked.month.toString().padLeft(2, '0')}/${picked.year}';
      });
      widget.onChanged(_controller.text);
    }
  }
}

class CountryDropdownField extends StatefulWidget {
  final DocumentDataField field;
  final String? initialValue;
  final Function(String?) onChanged;
  final String? errorText;

  const CountryDropdownField({
    super.key,
    required this.field,
    this.initialValue,
    required this.onChanged,
    this.errorText,
  });

  @override
  State<CountryDropdownField> createState() => _CountryDropdownFieldState();
}

class _CountryDropdownFieldState extends State<CountryDropdownField> {
  String? _selectedValue;

  @override
  void initState() {
    super.initState();

    // Ensure we have a valid ISO code
    _selectedValue = _getValidIsoCode(widget.initialValue);
  }

  String _getValidIsoCode(String? value) {
    // If value is null or empty, default to GB
    if (value == null || value.isEmpty) {
      return 'GB';
    }

    // If value is already a valid ISO code, use it
    if (Countries.isValidIsoCode(value)) {
      return value;
    }

    // If value is a country name, try to find the ISO code
    try {
      final country = Countries.all.firstWhere(
        (c) => c.name.toLowerCase() == value.toLowerCase(),
      );
      return country.isoCode;
    } catch (e) {
      // If not found, default to GB
      return 'GB';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.field.label,
          style: TextStyle(
            fontSize: ResponsiveHelper.getResponsiveFontSize(
              context,
              mobile: 14.0,
              tablet: 15.0,
              desktop: 16.0,
            ),
            fontWeight: FontWeight.w600,
            color: AppColors.kBlackColor,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _selectedValue,
          decoration: InputDecoration(
            hintText: 'Select country',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            filled: true,
            fillColor: Colors.grey[50],
          ),
          items: Countries.getCountryDropdownItems(),
          onChanged: (value) {
            setState(() {
              _selectedValue = value;
            });
            widget.onChanged(value);
          },
        ),
        if (widget.errorText != null) ...[
          const SizedBox(height: 8),
          Text(
            widget.errorText!,
            style: TextStyle(
              color: AppColors.kRedColor,
              fontSize: ResponsiveHelper.getResponsiveFontSize(
                context,
                mobile: 12.0,
                tablet: 13.0,
                desktop: 14.0,
              ),
            ),
          ),
        ],
      ],
    );
  }
}

/// Custom input formatter for date fields (DD/MM/YYYY)
class _DateInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text;

    // Remove any non-digit characters except /
    String digitsOnly = text.replaceAll(RegExp(r'[^0-9/]'), '');

    // Auto-format as user types
    String formatted = '';
    int digitCount = 0;

    for (int i = 0; i < digitsOnly.length; i++) {
      final char = digitsOnly[i];

      if (char == '/') {
        continue; // Skip existing slashes
      }

      digitCount++;
      formatted += char;

      // Add slashes after day and month
      if (digitCount == 2 || digitCount == 4) {
        formatted += '/';
      }

      // Stop at 8 digits (DDMMYYYY)
      if (digitCount >= 8) {
        break;
      }
    }

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}
