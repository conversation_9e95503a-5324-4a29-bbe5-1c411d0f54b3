import 'package:SolidCheck/core/navigation/app_router_new.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/features/document_nomination/data/models/available_documents_response.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:SolidCheck/features/document_nomination/presentation/providers/document_nomination_provider.dart';
import 'package:SolidCheck/screens/document_processing_choice_screen.dart';
import 'package:SolidCheck/screens/manual_document_entry_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class EnhancedDocumentList extends ConsumerWidget {
  final String applicationId;
  final String applicantName;
  final AvailableDocumentsData documentsData;

  const EnhancedDocumentList({
    super.key,
    required this.applicationId,
    required this.applicantName,
    required this.documentsData,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(documentNominationProvider);
    final isMobile = context.isMobile;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(context),
        const SizedBox(height: 16),
        _buildInstructions(context),
        const SizedBox(height: 24),
        Expanded(
          child: _buildDocumentGroups(context, ref, state, isMobile),
        ),
        _buildLegend(context),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Text(
      'Nominating Further Documents',
      style: TextStyle(
        fontSize: ResponsiveHelper.getResponsiveFontSize(
          context,
          mobile: 20.0,
          tablet: 22.0,
          desktop: 24.0,
        ),
        fontWeight: FontWeight.bold,
        color: Colors.black87,
      ),
    );
  }

  Widget _buildInstructions(BuildContext context) {
    return Text(
      'To nominate or check further documents, please click on a document from the list below.',
      style: TextStyle(
        fontSize: ResponsiveHelper.getResponsiveFontSize(
          context,
          mobile: 14.0,
          tablet: 15.0,
          desktop: 16.0,
        ),
        color: Colors.grey.shade700,
      ),
    );
  }

  Widget _buildDocumentGroups(
    BuildContext context,
    WidgetRef ref,
    DocumentNominationState state,
    bool isMobile,
  ) {
    final groupedDocuments = documentsData.availableDocuments;
    
    if (groupedDocuments.isEmpty) {
      return _buildEmptyState(context);
    }

    return SingleChildScrollView(
      child: isMobile
          ? _buildMobileLayout(context, ref, state, groupedDocuments)
          : _buildDesktopLayout(context, ref, state, groupedDocuments),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.description_outlined,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'No Documents Available',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileLayout(
    BuildContext context,
    WidgetRef ref,
    DocumentNominationState state,
    Map<String, List<DocumentType>> groupedDocuments,
  ) {
    return Column(
      children: groupedDocuments.entries.map((entry) {
        return _buildDocumentGroup(
          context,
          ref,
          state,
          entry.key,
          entry.value,
          isMobile: true,
        );
      }).toList(),
    );
  }

  Widget _buildDesktopLayout(
    BuildContext context,
    WidgetRef ref,
    DocumentNominationState state,
    Map<String, List<DocumentType>> groupedDocuments,
  ) {
    final groups = groupedDocuments.entries.toList();
    final leftGroups = <MapEntry<String, List<DocumentType>>>[];
    final rightGroups = <MapEntry<String, List<DocumentType>>>[];

    for (int i = 0; i < groups.length; i++) {
      if (i % 2 == 0) {
        leftGroups.add(groups[i]);
      } else {
        rightGroups.add(groups[i]);
      }
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Column(
            children: leftGroups.map((entry) {
              return _buildDocumentGroup(
                context,
                ref,
                state,
                entry.key,
                entry.value,
                isMobile: false,
              );
            }).toList(),
          ),
        ),
        const SizedBox(width: 24),
        Expanded(
          child: Column(
            children: rightGroups.map((entry) {
              return _buildDocumentGroup(
                context,
                ref,
                state,
                entry.key,
                entry.value,
                isMobile: false,
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildDocumentGroup(
    BuildContext context,
    WidgetRef ref,
    DocumentNominationState state,
    String groupKey,
    List<DocumentType> documents,
    {required bool isMobile}
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildGroupHeader(context, groupKey),
          const SizedBox(height: 12),
          ...documents.map((document) {
            final isNominated = state.isDocumentNominated(document.id);
            return _buildDocumentItem(
              context,
              ref,
              document,
              isNominated,
            );
          }),
        ],
      ),
    );
  }

  Widget _buildGroupHeader(BuildContext context, String groupKey) {
    final groupName = _getGroupDisplayName(groupKey);
    final groupDescription = _getGroupDescription(groupKey);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          groupName,
          style: TextStyle(
            fontSize: ResponsiveHelper.getResponsiveFontSize(
              context,
              mobile: 16.0,
              tablet: 17.0,
              desktop: 18.0,
            ),
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        if (groupDescription.isNotEmpty) ...[
          const SizedBox(height: 4),
          Text(
            groupDescription,
            style: TextStyle(
              fontSize: ResponsiveHelper.getResponsiveFontSize(
                context,
                mobile: 13.0,
                tablet: 14.0,
                desktop: 14.0,
              ),
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDocumentItem(
    BuildContext context,
    WidgetRef ref,
    DocumentType document,
    bool isNominated,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () => _handleDocumentTap(context, ref, document),
        borderRadius: BorderRadius.circular(4),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
          child: Row(
            children: [
              Container(
                width: 8,
                height: 8,
                margin: const EdgeInsets.only(right: 12),
                decoration: BoxDecoration(
                  color: isNominated ? Colors.green : Colors.grey.shade400,
                  shape: BoxShape.circle,
                ),
              ),
              Expanded(
                child: Text(
                  document.name,
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getResponsiveFontSize(
                      context,
                      mobile: 14.0,
                      tablet: 15.0,
                      desktop: 15.0,
                    ),
                    color: isNominated ? Colors.green.shade700 : AppColors.kBlueColor,
                    decoration: TextDecoration.underline,
                    decorationColor: isNominated ? Colors.green.shade700 : AppColors.kBlueColor,
                  ),
                ),
              ),

              _buildDocumentBadges(document),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDocumentBadges(DocumentType document) {
    final badges = <Widget>[];
    
    if (document.requiresPhoto) {
      badges.add(_buildBadge('Photo', Colors.orange));
    }
    
    if (document.confirmsAddress) {
      badges.add(_buildBadge('Address', Colors.green));
    }

    if (badges.isEmpty) return const SizedBox.shrink();

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: badges,
    );
  }

  Widget _buildBadge(String text, Color color) {
    return Container(
      margin: const EdgeInsets.only(left: 8),
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: _getShade700(color),
        ),
      ),
    );
  }

  Widget _buildLegend(BuildContext context) {
    return Row(
      children: [
        _buildLegendItem(
          Icons.circle,
          Colors.red,
          'Less than 3 months old',
        ),
        const SizedBox(width: 24),
        _buildLegendItem(
          Icons.circle,
          Colors.green,
          'Less than 12 months old',
        ),
      ],
    );
  }

  Widget _buildLegendItem(IconData icon, Color color, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: color, size: 12),
        const SizedBox(width: 8),
        Text(
          text,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  void _handleDocumentTap(
    BuildContext context,
    WidgetRef ref,
    DocumentType document,
  ) {
    final state = ref.read(documentNominationProvider);
    final existingNomination = state.getNominationForDocument(document.id);

    // If document is already nominated, go directly to edit
    if (existingNomination != null) {
      AppRouter.navigateToDocumentDetail(
        applicationId: applicationId,
        applicantName: applicantName,
        documentType: document,
        existingNomination: existingNomination,
      );
    } else {
      // Check if AI scanner is enabled for this entity
      final documentNominationState = ref.read(documentNominationProvider);
      final isAiScannerEnabled = documentNominationState.isAiDocumentScannerEnabled;

      if (isAiScannerEnabled) {
        // For new documents, show processing choice
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => DocumentProcessingChoiceScreen(
              documentType: document.key,
              applicationId: int.parse(applicationId),
              documentConfig: {
                'name': document.name,
                'requires_photo': document.requiresPhoto,
                'data_fields': document.dataFields.map((field) => {
                  'name': field.name,
                  'label': field.label,
                  'type': field.type,
                  'required': field.required,
                }).toList(),
              },
            ),
          ),
        );
      } else {
        // AI scanner disabled, go directly to manual entry
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => ManualDocumentEntryScreen(
              documentType: document.key,
              applicationId: int.parse(applicationId),
              documentConfig: {
                'name': document.name,
                'requires_photo': document.requiresPhoto,
                'data_fields': document.dataFields.map((field) => {
                  'name': field.name,
                  'label': field.label,
                  'type': field.type,
                  'required': field.required,
                }).toList(),
              },
            ),
          ),
        );
      }
    }
  }

  String _getGroupDisplayName(String groupKey) {
    switch (groupKey) {
      case 'group_1':
        return 'Group 1: Primary identity documents';
      case 'group_1a':
        return 'Group 1a: Primary identity documents (non-UK)';
      case 'group_2a':
        return 'Group 2a: Trusted government documents';
      case 'group_2b':
        return 'Group 2b: Financial and social history documents';
      default:
        return 'Document Group ${groupKey.replaceAll('group_', '')}';
    }
  }

  String _getGroupDescription(String groupKey) {
    switch (groupKey) {
      case 'group_1':
        return '';
      case 'group_1a':
        return '';
      case 'group_2a':
        return '';
      case 'group_2b':
        return '';
      default:
        return '';
    }
  }

  Color _getShade700(Color color) {
    if (color == Colors.orange) return Colors.orange.shade700;
    if (color == Colors.green) return Colors.green.shade700;
    if (color == Colors.blue) return Colors.blue.shade700;
    if (color == Colors.red) return Colors.red.shade700;
    return color;
  }
}
