import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';

class DBSDocumentFieldService {
  static Map<String, List<DocumentDataField>> getAllDBSDocumentFields() {
    return {
      // Group 1: Primary Identity Documents - API document keys
      'passport_any': _getPassportFields(),
      'passport': _getPassportFields(), // fallback
      'e_visa': _getEVisaFields(),
      'biometric_residence_permit_uk': _getBiometricResidencePermitFields(),
      'application_registration_card_group1': _getApplicationRegistrationCardFields(),
      'photocard_drivers_licence_uk': _getDrivingLicencePhotocardUKFields(),
      'driving_licence_photocard_uk': _getDrivingLicencePhotocardUKFields(), // fallback
      'birth_certificate_uk_12m': _getBirthCertificateWithin12MonthsFields(),
      'birth_certificate_within_12_months': _getBirthCertificateWithin12MonthsFields(), // fallback
      'adoption_certificate_uk': _getAdoptionCertificateFields(),
      'adoption_certificate': _getAdoptionCertificateFields(), // fallback

      // Group 2a: Trusted Government Documents - API document keys
      'photocard_driving_licence_non_uk': _getDrivingLicencePhotocardNonUKFields(),
      'driving_licence_photocard_non_uk': _getDrivingLicencePhotocardNonUKFields(), // fallback
      'paper_driving_licence_uk': _getDrivingLicencePaperPre2000Fields(),
      'driving_licence_paper_pre_2000': _getDrivingLicencePaperPre2000Fields(), // fallback
      'birth_certificate_uk_after_birth': _getBirthCertificateAfterBirthFields(),
      'birth_certificate_after_birth': _getBirthCertificateAfterBirthFields(), // fallback
      'marriage_civil_partnership_uk': _getMarriageCivilPartnershipCertificateFields(),
      'marriage_civil_partnership_certificate': _getMarriageCivilPartnershipCertificateFields(), // fallback
      'immigration_document_non_uk': _getImmigrationDocumentVisaWorkPermitFields(),
      'immigration_document_visa_work_permit': _getImmigrationDocumentVisaWorkPermitFields(), // fallback
      'hm_forces_id': _getHMForcesIDVeteranCardFields(),
      'hm_forces_id_card': _getHMForcesIDVeteranCardFields(),
      'hm_forces_id_veteran_card': _getHMForcesIDVeteranCardFields(), // fallback
      'firearms_licence': _getFirearmsLicenceFields(),

      // Group 2b: Financial and Social History Documents - API document keys
      'mortgage_statement_uk': _getMortgageStatementFields(),
      'mortgage_statement': _getMortgageStatementFields(), // fallback
      'bank_statement_uk': _getBankBuildingSocietyStatementFields(),
      'bank_statement_non_uk': _getBankBuildingSocietyStatementFields(),
      'bank_building_society_statement': _getBankBuildingSocietyStatementFields(), // fallback
      'bank_opening_letter_uk': _getBankAccountOpeningConfirmationFields(),
      'bank_account_opening_confirmation': _getBankAccountOpeningConfirmationFields(), // fallback
      'credit_card_statement_uk': _getCreditCardStatementFields(),
      'credit_card_statement': _getCreditCardStatementFields(), // fallback
      'financial_statement_uk': _getFinancialStatementFields(),
      'financial_statement': _getFinancialStatementFields(), // fallback
      'p45_p60': _getP45P60StatementFields(),
      'p45_p60_statement': _getP45P60StatementFields(), // fallback
      'council_tax_statement_uk': _getCouncilTaxStatementFields(),
      'council_tax_statement': _getCouncilTaxStatementFields(), // fallback
      'sponsorship_letter_non_uk': _getLetterOfSponsorshipFields(),
      'letter_of_sponsorship': _getLetterOfSponsorshipFields(), // fallback
      'utility_bill': _getUtilityBillFields(),
      'benefit_statement_uk': _getBenefitStatementFields(),
      'benefit_statement': _getBenefitStatementFields(), // fallback
      'government_correspondence_uk': _getGovernmentAgencyDocumentFields(),
      'government_agency_document': _getGovernmentAgencyDocumentFields(), // fallback
      'hmrc_self_assessment': _getHMRCSelfAssessmentFields(),
      'ehic_ghic': _getEHICGHICFields(),
      'eea_national_id': _getEEANationalIDCardFields(),
      'eea_national_id_card': _getEEANationalIDCardFields(), // fallback
      'irish_passport_card': _getIrishPassportCardFields(),
      'pass_card': _getPASSAccreditationCardFields(),
      'pass_accreditation_card': _getPASSAccreditationCardFields(), // fallback
      'head_teacher_letter': _getLetterHeadTeacherCollegePrincipalFields(),
      'letter_head_teacher_college_principal': _getLetterHeadTeacherCollegePrincipalFields(), // fallback
      
      // Non-UK Nationals Primary Documents
      'irish_passport_or_passport_card': _getIrishPassportOrPassportCardFields(),
      'home_office_document_indefinite_stay': _getHomeOfficeDocumentIndefiniteStayFields(),
      'biometric_immigration_document_indefinite': _getBiometricImmigrationDocumentIndefiniteFields(),
      'online_evidence_immigration_status': _getOnlineEvidenceImmigrationStatusFields(),
      'passport_exempt_immigration_control': _getPassportExemptImmigrationControlFields(),
      'immigration_status_document_ni_number': _getImmigrationStatusDocumentNINumberFields(),
      'passport_time_limited_work_permitted': _getPassportTimeLimitedWorkPermittedFields(),
      'biometric_immigration_document_time_limited': _getBiometricImmigrationDocumentTimeLimitedFields(),
      'home_office_document_eea_swiss': _getHomeOfficeDocumentEEASwissFields(),
      'frontier_worker_permit': _getFrontierWorkerPermitFields(),
      'immigration_status_document_time_limited_ni': _getImmigrationStatusDocumentTimeLimitedNIFields(),
      'appendix_eu_application': _getAppendixEUApplicationFields(),
      'application_registration_card': _getApplicationRegistrationCardFields(),
      'positive_verification_notice': _getPositiveVerificationNoticeFields(),
    };
  }

  // Group 1: Primary Identity Documents
  static List<DocumentDataField> _getPassportFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'passport_number', type: 'text', required: true, label: 'Passport Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'expiry_date', type: 'smart_date', required: true, label: 'Expiry Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
    ];
  }

  static List<DocumentDataField> _getBiometricResidencePermitFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'permit_number', type: 'text', required: true, label: 'Permit Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'expiry_date', type: 'smart_date', required: true, label: 'Expiry Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
    ];
  }

  static List<DocumentDataField> _getDrivingLicencePhotocardUKFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'licence_number', type: 'text', required: true, label: 'Licence Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'expiry_date', type: 'smart_date', required: true, label: 'Expiry Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'postcode_on_document', type: 'multiple_choice_postcode', required: true, label: 'Postcode on Document'),
    ];
  }

  static List<DocumentDataField> _getBirthCertificateWithin12MonthsFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'certificate_number', type: 'text', required: true, label: 'Certificate Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'date_of_birth', type: 'smart_date', required: true, label: 'Date of Birth'),
    ];
  }

  static List<DocumentDataField> _getAdoptionCertificateFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'entry_number', type: 'text', required: true, label: 'Entry Number'),
      DocumentDataField(name: 'adoption_date', type: 'smart_date', required: true, label: 'Adoption Date'),
      DocumentDataField(name: 'issued_in_uk', type: 'yes_no_confirmation', required: true, label: 'Is this document issued in United Kingdom (UK)?'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
    ];
  }

  static List<DocumentDataField> _getEVisaFields() {
    return [
      DocumentDataField(name: 'share_code', type: 'text', required: true, label: 'Share Code'),
      DocumentDataField(name: 'expiry_date', type: 'smart_date', required: false, label: 'Expiry Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
    ];
  }

  static List<DocumentDataField> _getApplicationRegistrationCardFields() {
    return [
      DocumentDataField(name: 'card_number', type: 'text', required: true, label: 'Card Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'expiry_date', type: 'smart_date', required: true, label: 'Expiry Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
    ];
  }

  // Group 2a: Trusted Government Documents
  static List<DocumentDataField> _getDrivingLicencePhotocardNonUKFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'licence_number', type: 'text', required: true, label: 'Licence Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'expiry_date', type: 'smart_date', required: true, label: 'Expiry Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'postcode_on_document', type: 'multiple_choice_postcode', required: true, label: 'Postcode on Document'),
    ];
  }

  static List<DocumentDataField> _getDrivingLicencePaperPre2000Fields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'licence_number', type: 'text', required: true, label: 'Licence Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'postcode_on_document', type: 'multiple_choice_postcode', required: true, label: 'Postcode on Document'),
    ];
  }

  static List<DocumentDataField> _getBirthCertificateAfterBirthFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'certificate_number', type: 'text', required: true, label: 'Certificate Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'date_of_birth', type: 'smart_date', required: true, label: 'Date of Birth'),
    ];
  }

  static List<DocumentDataField> _getMarriageCivilPartnershipCertificateFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'certificate_number', type: 'text', required: true, label: 'Certificate Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
    ];
  }

  static List<DocumentDataField> _getImmigrationDocumentVisaWorkPermitFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'document_number', type: 'text', required: true, label: 'Document Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'expiry_date', type: 'smart_date', required: true, label: 'Expiry Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'postcode_on_document', type: 'multiple_choice_postcode', required: true, label: 'Postcode on Document'),
    ];
  }

  static List<DocumentDataField> _getHMForcesIDVeteranCardFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'card_number', type: 'text', required: true, label: 'Card Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'expiry_date', type: 'smart_date', required: false, label: 'Expiry Date (if applicable)'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
    ];
  }

  static List<DocumentDataField> _getFirearmsLicenceFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'licence_number', type: 'text', required: true, label: 'Licence Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'expiry_date', type: 'smart_date', required: true, label: 'Expiry Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'postcode_on_document', type: 'multiple_choice_postcode', required: true, label: 'Postcode on Document'),
    ];
  }

  // Group 2b: Financial and Social History Documents
  static List<DocumentDataField> _getMortgageStatementFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date (within last 12 months)'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'postcode_on_document', type: 'multiple_choice_postcode', required: true, label: 'Postcode on Document'),
    ];
  }

  static List<DocumentDataField> _getBankBuildingSocietyStatementFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date (within last 3 months)'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'postcode_on_document', type: 'multiple_choice_postcode', required: true, label: 'Postcode on Document'),
    ];
  }

  static List<DocumentDataField> _getBankAccountOpeningConfirmationFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date (within last 3 months)'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'postcode_on_document', type: 'multiple_choice_postcode', required: true, label: 'Postcode on Document'),
    ];
  }

  static List<DocumentDataField> _getCreditCardStatementFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date (within last 3 months)'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'postcode_on_document', type: 'multiple_choice_postcode', required: true, label: 'Postcode on Document'),
    ];
  }

  static List<DocumentDataField> _getFinancialStatementFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date (within last 12 months)'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'postcode_on_document', type: 'multiple_choice_postcode', required: true, label: 'Postcode on Document'),
    ];
  }

  static List<DocumentDataField> _getP45P60StatementFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date (within last 12 months)'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'postcode_on_document', type: 'multiple_choice_postcode', required: true, label: 'Postcode on Document'),
    ];
  }

  static List<DocumentDataField> _getCouncilTaxStatementFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date (within last 12 months)'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'postcode_on_document', type: 'multiple_choice_postcode', required: true, label: 'Postcode on Document'),
    ];
  }

  static List<DocumentDataField> _getLetterOfSponsorshipFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'expiry_date', type: 'smart_date', required: false, label: 'Expiry Date (if applicable)'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'postcode_on_document', type: 'multiple_choice_postcode', required: true, label: 'Postcode on Document'),
    ];
  }

  static List<DocumentDataField> _getUtilityBillFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date (within last 3 months)'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'postcode_on_document', type: 'multiple_choice_postcode', required: true, label: 'Postcode on Document'),
    ];
  }

  static List<DocumentDataField> _getBenefitStatementFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date (within last 3 months)'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'postcode_on_document', type: 'multiple_choice_postcode', required: true, label: 'Postcode on Document'),
    ];
  }

  static List<DocumentDataField> _getGovernmentAgencyDocumentFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date (within last 3 months)'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'postcode_on_document', type: 'multiple_choice_postcode', required: true, label: 'Postcode on Document'),
    ];
  }

  static List<DocumentDataField> _getEEANationalIDCardFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'card_number', type: 'text', required: true, label: 'Card Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'expiry_date', type: 'smart_date', required: true, label: 'Expiry Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
    ];
  }

  static List<DocumentDataField> _getIrishPassportCardFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'card_number', type: 'text', required: true, label: 'Card Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'expiry_date', type: 'smart_date', required: true, label: 'Expiry Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
    ];
  }

  static List<DocumentDataField> _getPASSAccreditationCardFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'card_number', type: 'text', required: true, label: 'Card Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'expiry_date', type: 'smart_date', required: true, label: 'Expiry Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
    ];
  }

  static List<DocumentDataField> _getLetterHeadTeacherCollegePrincipalFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'postcode_on_document', type: 'multiple_choice_postcode', required: true, label: 'Postcode on Document'),
    ];
  }

  static List<DocumentDataField> _getHMRCSelfAssessmentFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'document_date', type: 'smart_date', required: true, label: 'Document Date'),
      DocumentDataField(name: 'tax_reference', type: 'text', required: true, label: 'Tax Reference'),
      DocumentDataField(name: 'taxpayer_address', type: 'text', required: true, label: 'Taxpayer Address'),
      DocumentDataField(name: 'tax_year', type: 'text', required: true, label: 'Tax Year'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'postcode_on_document', type: 'multiple_choice_postcode', required: true, label: 'Postcode on Document'),
    ];
  }

  static List<DocumentDataField> _getEHICGHICFields() {
    return [
      DocumentDataField(name: 'card_type', type: 'text', required: true, label: 'Card Type'),
      DocumentDataField(name: 'card_number', type: 'text', required: true, label: 'Card Number'),
      DocumentDataField(name: 'expiry_date', type: 'smart_date', required: true, label: 'Expiry Date'),
      DocumentDataField(name: 'holder_name', type: 'text', required: true, label: 'Holder Name'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
    ];
  }

  // Non-UK Nationals Primary Documents
  static List<DocumentDataField> _getIrishPassportOrPassportCardFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'document_number', type: 'text', required: true, label: 'Document Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'expiry_date', type: 'smart_date', required: true, label: 'Expiry Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
    ];
  }

  static List<DocumentDataField> _getHomeOfficeDocumentIndefiniteStayFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'document_number', type: 'text', required: true, label: 'Document Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'postcode_on_document', type: 'multiple_choice_postcode', required: true, label: 'Postcode on Document'),
    ];
  }

  static List<DocumentDataField> _getBiometricImmigrationDocumentIndefiniteFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'permit_number', type: 'text', required: true, label: 'Permit Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'postcode_on_document', type: 'multiple_choice_postcode', required: true, label: 'Postcode on Document'),
    ];
  }

  static List<DocumentDataField> _getOnlineEvidenceImmigrationStatusFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'share_code', type: 'text', required: true, label: 'Share Code'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
    ];
  }

  static List<DocumentDataField> _getPassportExemptImmigrationControlFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'passport_number', type: 'text', required: true, label: 'Passport Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'expiry_date', type: 'smart_date', required: true, label: 'Expiry Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'endorsement_details', type: 'text', required: true, label: 'Endorsement Details'),
    ];
  }

  static List<DocumentDataField> _getImmigrationStatusDocumentNINumberFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'document_number', type: 'text', required: true, label: 'Document Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'national_insurance_number', type: 'text', required: true, label: 'National Insurance Number'),
      DocumentDataField(name: 'postcode_on_document', type: 'multiple_choice_postcode', required: true, label: 'Postcode on Document'),
    ];
  }

  static List<DocumentDataField> _getPassportTimeLimitedWorkPermittedFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'country_of_issue', type: 'country_dropdown', required: true, label: 'Country of Issue'),
      DocumentDataField(name: 'passport_number', type: 'text', required: true, label: 'Passport Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'expiry_date', type: 'smart_date', required: true, label: 'Expiry Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'endorsement_details', type: 'text', required: true, label: 'Endorsement Details'),
    ];
  }

  static List<DocumentDataField> _getBiometricImmigrationDocumentTimeLimitedFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'permit_number', type: 'text', required: true, label: 'Permit Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'expiry_date', type: 'smart_date', required: true, label: 'Expiry Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'postcode_on_document', type: 'multiple_choice_postcode', required: true, label: 'Postcode on Document'),
    ];
  }

  static List<DocumentDataField> _getHomeOfficeDocumentEEASwissFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'document_number', type: 'text', required: true, label: 'Document Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'expiry_date', type: 'smart_date', required: true, label: 'Expiry Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'postcode_on_document', type: 'multiple_choice_postcode', required: true, label: 'Postcode on Document'),
    ];
  }

  static List<DocumentDataField> _getFrontierWorkerPermitFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'permit_number', type: 'text', required: true, label: 'Permit Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'expiry_date', type: 'smart_date', required: true, label: 'Expiry Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
    ];
  }

  static List<DocumentDataField> _getImmigrationStatusDocumentTimeLimitedNIFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'document_number', type: 'text', required: true, label: 'Document Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'expiry_date', type: 'smart_date', required: true, label: 'Expiry Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'national_insurance_number', type: 'text', required: true, label: 'National Insurance Number'),
      DocumentDataField(name: 'postcode_on_document', type: 'multiple_choice_postcode', required: true, label: 'Postcode on Document'),
    ];
  }

  static List<DocumentDataField> _getAppendixEUApplicationFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'document_number', type: 'text', required: true, label: 'Document Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
      DocumentDataField(name: 'positive_verification_notice_reference', type: 'text', required: true, label: 'Positive Verification Notice Reference'),
    ];
  }

  static List<DocumentDataField> _getPositiveVerificationNoticeFields() {
    return [
      DocumentDataField(name: 'document_type', type: 'text', required: true, label: 'Document Type'),
      DocumentDataField(name: 'reference_number', type: 'text', required: true, label: 'Reference Number'),
      DocumentDataField(name: 'issue_date', type: 'smart_date', required: true, label: 'Issue Date'),
      DocumentDataField(name: 'full_name_on_document', type: 'yes_no_confirmation', required: true, label: 'Full Name on Document'),
    ];
  }

  static List<DocumentDataField> getFieldsForDocumentType(String documentKey) {
    final allFields = getAllDBSDocumentFields();
    return allFields[documentKey] ?? [];
  }

  static bool isValidDocumentType(String documentKey) {
    return getAllDBSDocumentFields().containsKey(documentKey);
  }

  static List<String> getAllDocumentTypes() {
    return getAllDBSDocumentFields().keys.toList();
  }

  static Map<String, List<String>> getDocumentsByGroup() {
    return {
      'Group 1: Primary Identity Documents': [
        'passport_any',
        'e_visa',
        'biometric_residence_permit_uk',
        'application_registration_card_group1',
        'photocard_drivers_licence_uk',
        'birth_certificate_uk_12m',
        'adoption_certificate_uk',
      ],
      'Group 2a: Trusted Government Documents': [
        'photocard_driving_licence_non_uk',
        'paper_driving_licence_uk',
        'birth_certificate_uk_after_birth',
        'marriage_civil_partnership_uk',
        'immigration_document_non_uk',
        'hm_forces_id',
        'firearms_licence',
      ],
      'Group 2b: Financial and Social History Documents': [
        'mortgage_statement_uk',
        'bank_statement_uk',
        'bank_statement_non_uk',
        'bank_opening_letter_uk',
        'credit_card_statement_uk',
        'financial_statement_uk',
        'p45_p60',
        'council_tax_statement_uk',
        'sponsorship_letter_non_uk',
        'utility_bill',
        'benefit_statement_uk',
        'government_correspondence_uk',
        'hmrc_self_assessment',
        'ehic_ghic',
        'eea_national_id',
        'irish_passport_card',
        'pass_card',
        'head_teacher_letter',
      ],
      'Non-UK Nationals Primary Documents': [
        'irish_passport_or_passport_card',
        'home_office_document_indefinite_stay',
        'biometric_immigration_document_indefinite',
        'online_evidence_immigration_status',
        'passport_exempt_immigration_control',
        'immigration_status_document_ni_number',
        'passport_time_limited_work_permitted',
        'biometric_immigration_document_time_limited',
        'home_office_document_eea_swiss',
        'frontier_worker_permit',
        'immigration_status_document_time_limited_ni',
        'appendix_eu_application',
        'application_registration_card',
        'positive_verification_notice',
      ],
    };
  }
}
