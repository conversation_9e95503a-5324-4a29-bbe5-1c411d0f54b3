import 'dart:math';
import 'package:SolidCheck/features/dbs/data/models/dbs_form_data.dart';

class SmartPostcodeService {
  static const List<String> _ukPostcodePrefixes = [
    'AB', 'AL', 'B', 'BA', 'BB', 'BD', 'BH', 'BL', 'BN', 'BR', 'BS', 'BT',
    'CA', 'CB', 'CF', 'CH', 'CM', 'CO', 'CR', 'CT', 'CV', 'CW',
    'DA', 'DD', 'DE', 'DG', 'DH', 'DL', 'DN', 'DT', 'DY',
    'E', 'EC', 'EH', 'EN', 'EX',
    'FK', 'FY',
    'G', 'GL', 'GU',
    'HA', 'HD', 'HG', 'HP', 'HR', 'HS', 'HU', 'HX',
    'IG', 'IP', 'IV',
    'KA', 'KT', 'KW', 'KY',
    'L', 'LA', 'LD', 'LE', 'LL', 'LN', 'LS', 'LU',
    'M', 'ME', 'MK', 'ML',
    'N', 'NE', 'NG', 'NN', 'NP', 'NR', 'NW',
    'OL', 'OX',
    'PA', 'PE', 'PH', 'PL', 'PO', 'PR',
    'RG', 'RH', 'RM',
    'S', 'SA', 'SE', 'SG', 'SK', 'SL', 'SM', 'SN', 'SO', 'SP', 'SR', 'SS', 'ST', 'SW', 'SY',
    'TA', 'TD', 'TF', 'TN', 'TQ', 'TR', 'TS', 'TW',
    'UB',
    'W', 'WA', 'WC', 'WD', 'WF', 'WN', 'WR', 'WS', 'WV',
    'YO',
    'ZE'
  ];

  static const List<String> _commonPostcodeSuffixes = [
    '1AA', '1AB', '1AD', '1AE', '1AF', '1AG', '1AH', '1AJ', '1AL', '1AN',
    '2AA', '2AB', '2AD', '2AE', '2AF', '2AG', '2AH', '2AJ', '2AL', '2AN',
    '3AA', '3AB', '3AD', '3AE', '3AF', '3AG', '3AH', '3AJ', '3AL', '3AN',
    '4AA', '4AB', '4AD', '4AE', '4AF', '4AG', '4AH', '4AJ', '4AL', '4AN',
    '5AA', '5AB', '5AD', '5AE', '5AF', '5AG', '5AH', '5AJ', '5AL', '5AN',
    '6AA', '6AB', '6AD', '6AE', '6AF', '6AG', '6AH', '6AJ', '6AL', '6AN',
    '7AA', '7AB', '7AD', '7AE', '7AF', '7AG', '7AH', '7AJ', '7AL', '7AN',
    '8AA', '8AB', '8AD', '8AE', '8AF', '8AG', '8AH', '8AJ', '8AL', '8AN',
    '9AA', '9AB', '9AD', '9AE', '9AF', '9AG', '9AH', '9AJ', '9AL', '9AN',
    '0AA', '0AB', '0AD', '0AE', '0AF', '0AG', '0AH', '0AJ', '0AL', '0AN',
  ];

  static List<String> generatePostcodeOptions(
    String correctPostcode,
    ApplicantDetailsData? applicantData,
  ) {
    final options = <String>[correctPostcode];
    final random = Random();
    
    // Get applicant's address history for context
    final applicantPostcodes = _getApplicantPostcodes(applicantData);
    
    // Generate 3 plausible alternatives
    while (options.length < 4) {
      String fakePostcode;
      
      if (options.length == 2 && applicantPostcodes.isNotEmpty) {
        // Use one postcode from applicant's history (but not the correct one)
        final historicalPostcodes = applicantPostcodes
            .where((pc) => pc.toUpperCase() != correctPostcode.toUpperCase())
            .toList();
        
        if (historicalPostcodes.isNotEmpty) {
          fakePostcode = historicalPostcodes[random.nextInt(historicalPostcodes.length)];
        } else {
          fakePostcode = _generateSimilarPostcode(correctPostcode, random);
        }
      } else {
        fakePostcode = _generateSimilarPostcode(correctPostcode, random);
      }
      
      // Ensure no duplicates
      if (!options.any((pc) => pc.toUpperCase() == fakePostcode.toUpperCase())) {
        options.add(fakePostcode);
      }
    }
    
    // Shuffle the options so correct answer isn't always first
    final shuffled = List<String>.from(options);
    shuffled.shuffle(random);
    
    return shuffled;
  }

  static List<String> _getApplicantPostcodes(ApplicantDetailsData? applicantData) {
    if (applicantData == null) return [];
    
    final postcodes = <String>[];
    
    // Add current address postcode
    if (applicantData.currentAddress.postcode.isNotEmpty) {
      postcodes.add(applicantData.currentAddress.postcode);
    }
    
    // Add previous addresses postcodes
    for (final address in applicantData.previousAddresses) {
      if (address.postcode.isNotEmpty) {
        postcodes.add(address.postcode);
      }
    }
    
    return postcodes;
  }

  static String _generateSimilarPostcode(String correctPostcode, Random random) {
    final cleanPostcode = correctPostcode.replaceAll(' ', '').toUpperCase();
    
    if (cleanPostcode.length < 5) {
      // Generate a completely random postcode if input is invalid
      return _generateRandomPostcode(random);
    }
    
    // Extract prefix and suffix
    final prefixLength = cleanPostcode.length - 3;
    final prefix = cleanPostcode.substring(0, prefixLength);

    // Strategy 1: Change the suffix but keep prefix similar
    if (random.nextBool()) {
      final similarPrefix = _getSimilarPrefix(prefix, random);
      final randomSuffix = _commonPostcodeSuffixes[random.nextInt(_commonPostcodeSuffixes.length)];
      return '$similarPrefix $randomSuffix';
    }

    // Strategy 2: Keep prefix but change suffix
    final randomSuffix = _commonPostcodeSuffixes[random.nextInt(_commonPostcodeSuffixes.length)];
    return '$prefix $randomSuffix';
  }

  static String _getSimilarPrefix(String originalPrefix, Random random) {
    // Try to find prefixes that start with the same letter(s)
    final firstChar = originalPrefix.isNotEmpty ? originalPrefix[0] : 'L';
    final similarPrefixes = _ukPostcodePrefixes
        .where((prefix) => prefix.startsWith(firstChar))
        .toList();
    
    if (similarPrefixes.isNotEmpty) {
      return similarPrefixes[random.nextInt(similarPrefixes.length)];
    }
    
    // Fallback to random prefix
    return _ukPostcodePrefixes[random.nextInt(_ukPostcodePrefixes.length)];
  }

  static String _generateRandomPostcode(Random random) {
    final prefix = _ukPostcodePrefixes[random.nextInt(_ukPostcodePrefixes.length)];
    final suffix = _commonPostcodeSuffixes[random.nextInt(_commonPostcodeSuffixes.length)];
    return '$prefix$suffix';
  }

  static String formatPostcode(String postcode) {
    final clean = postcode.replaceAll(' ', '').toUpperCase();
    if (clean.length < 5) return postcode;
    
    final prefixLength = clean.length - 3;
    final prefix = clean.substring(0, prefixLength);
    final suffix = clean.substring(prefixLength);
    
    return '$prefix $suffix';
  }

  static bool isValidUKPostcode(String postcode) {
    final clean = postcode.replaceAll(' ', '').toUpperCase();
    
    // Basic UK postcode pattern validation
    final pattern = RegExp(r'^[A-Z]{1,2}[0-9][A-Z0-9]?[0-9][A-Z]{2}$');
    return pattern.hasMatch(clean);
  }

  static String generateNameConfirmationText(
    String fieldName,
    ApplicantDetailsData? applicantData,
  ) {
    if (applicantData == null) {
      return 'Can you confirm the name on this document is correct?';
    }
    
    final fullName = '${applicantData.forename} ${applicantData.presentSurname}';
    
    if (fieldName.contains('full_name')) {
      return 'Can you confirm the full name on this document is **$fullName**?';
    }
    
    if (fieldName.contains('forename') || fieldName.contains('first_name')) {
      return 'Can you confirm the first name on this document is **${applicantData.forename}**?';
    }
    
    if (fieldName.contains('surname') || fieldName.contains('last_name')) {
      return 'Can you confirm the surname on this document is **${applicantData.presentSurname}**?';
    }
    
    return 'Can you confirm the name on this document matches your application details?';
  }

  static List<String> getApplicantPostcodes(ApplicantDetailsData? applicantData) {
    return _getApplicantPostcodes(applicantData);
  }
}
