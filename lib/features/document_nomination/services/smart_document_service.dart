import 'package:SolidCheck/features/dbs/data/models/dbs_form_data.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';

class SmartDocumentService {
  static Map<String, List<String>> getSimplifiedFields(String documentKey) {
    return {
      'essential': _getEssentialFields(documentKey),
      'verification': _getVerificationFields(documentKey),
      'optional': _getOptionalFields(documentKey),
    };
  }

  static List<String> _getEssentialFields(String documentKey) {
    final key = documentKey.toLowerCase();

    // Identity Documents - Only ask for what's needed to verify identity
    if (key.contains('passport')) {
      return ['document_number']; // Just the passport number
    }

    if (key.contains('driving_licence') || key.contains('drivers_licence') || key.contains('photocard')) {
      return ['licence_number']; // Just the licence number
    }

    if (key.contains('birth_certificate')) {
      return []; // Birth certificate doesn't need any input - just confirmation it exists
    }

    if (key.contains('adoption_certificate')) {
      return []; // Adoption certificate doesn't need input - just confirmation
    }

    if (key.contains('biometric_residence_permit')) {
      return ['document_number']; // Just the BRP number
    }

    // Address Documents - Only ask for what's needed to verify address
    if (key.contains('bank_statement')) {
      return ['statement_date']; // Just when the statement is from
    }

    if (key.contains('mortgage_statement')) {
      return ['statement_date']; // Just when the statement is from
    }

    if (key.contains('council_tax')) {
      return ['tax_year']; // Just which tax year
    }

    if (key.contains('utility_bill')) {
      return ['bill_date']; // Just when the bill is from
    }

    if (key.contains('marriage_certificate')) {
      return []; // Marriage certificate doesn't need input - just confirmation
    }

    if (key.contains('civil_partnership')) {
      return []; // Civil partnership certificate doesn't need input
    }

    return []; // Most documents don't need any input fields
  }

  static List<String> _getVerificationFields(String documentKey) {
    final key = documentKey.toLowerCase();

    // Only ask for verification if the document name might not match exactly
    if (key.contains('passport') || key.contains('driving_licence') || key.contains('drivers_licence')) {
      return ['name_matches_application']; // Simple yes/no question
    }

    // For address documents, only verify the address matches
    if (_isAddressDocument(documentKey)) {
      return ['address_matches_application']; // Simple yes/no question
    }

    return []; // Most documents don't need verification fields
  }

  static List<String> _getOptionalFields(String documentKey) {
    // No optional fields - keep forms as simple as possible
    return [];
  }

  static bool _isIdentityDocument(String documentKey) {
    final identityDocs = [
      'passport', 'driving_licence', 'drivers_licence', 'birth_certificate', 'adoption_certificate',
      'marriage_certificate', 'civil_partnership', 'divorce_decree', 'deed_poll',
      'firearms_licence', 'hm_forces_id'
    ];
    return identityDocs.any((doc) => documentKey.contains(doc));
  }

  static List<String> getPredictedDocumentTypes(DBSFormData formData) {
    final predictions = <String>[];
    final nationality = formData.applicantDetails.additionalApplicantDetails.birthNationality;
    final currentCountry = formData.applicantDetails.currentAddress.countryCode;
    final addressHistory = formData.applicantDetails.previousAddresses;

    if (nationality == 'BRITISH' || currentCountry == 'GB') {
      predictions.addAll([
        'passport_uk', 'photocard_drivers_licence_uk', 'birth_certificate_uk',
        'bank_statement_uk', 'utility_bill_uk', 'council_tax_uk'
      ]);
    }

    if (nationality == 'IRISH') {
      predictions.addAll(['passport_irish', 'drivers_licence_irish']);
    }

    if (addressHistory.length >= 2) {
      predictions.addAll(['bank_statement', 'utility_bill', 'council_tax']);
    }

    return predictions;
  }

  static Map<String, String> getSmartDefaults(String documentKey, DBSFormData formData) {
    final defaults = <String, String>{};
    final predictedCountries = getPredictedCountries(formData);

    if (predictedCountries.isNotEmpty) {
      defaults['issue_country'] = _getCountryName(predictedCountries.first);
    }

    if (documentKey.contains('council_tax')) {
      final currentYear = DateTime.now().year;
      defaults['tax_year'] = '$currentYear-${currentYear + 1}';
    }

    if (documentKey.contains('bank_statement') || documentKey.contains('utility_bill')) {
      final threeMonthsAgo = DateTime.now().subtract(const Duration(days: 90));
      defaults['statement_date'] = '${threeMonthsAgo.day.toString().padLeft(2, '0')}/${threeMonthsAgo.month.toString().padLeft(2, '0')}/${threeMonthsAgo.year}';
    }

    return defaults;
  }

  static bool _isAddressDocument(String documentKey) {
    final addressDocuments = [
      'bank_statement', 'utility_bill', 'council_tax', 'mortgage_statement',
      'p45_p60', 'benefit_statement', 'credit_card_statement', 'financial_statement'
    ];
    
    return addressDocuments.any((doc) => documentKey.contains(doc));
  }

  static List<String> getPredictedCountries(DBSFormData formData) {
    final countries = <String>[];
    
    final nationality = formData.applicantDetails.additionalApplicantDetails.birthNationality;
    countries.add(_mapNationalityToCountryCode(nationality));
      
    final addresses = [
      formData.applicantDetails.currentAddress.countryCode,
      ...formData.applicantDetails.previousAddresses.map((addr) => addr.countryCode)
    ];
    
    for (final country in addresses) {
      if (!countries.contains(country)) {
        countries.add(country);
      }
    }
    
    if (countries.isEmpty) {
      countries.add('GB');
    }
    
    return countries;
  }

  static String _mapNationalityToCountryCode(String nationality) {
    final mapping = {
      'BRITISH': 'GB',
      'IRISH': 'IE',
      'AMERICAN': 'US',
      'CANADIAN': 'CA',
      'AUSTRALIAN': 'AU',
      'INDIAN': 'IN',
      'PAKISTANI': 'PK',
      'BANGLADESHI': 'BD',
      'NIGERIAN': 'NG',
      'SOUTH_AFRICAN': 'ZA',
      'FRENCH': 'FR',
      'GERMAN': 'DE',
      'SPANISH': 'ES',
      'ITALIAN': 'IT',
      'POLISH': 'PL',
      'ROMANIAN': 'RO',
      'BULGARIAN': 'BG',
      'LITHUANIAN': 'LT',
      'LATVIAN': 'LV',
      'ESTONIAN': 'EE',
      'CZECH': 'CZ',
      'SLOVAK': 'SK',
      'HUNGARIAN': 'HU',
      'SLOVENIAN': 'SI',
      'CROATIAN': 'HR',
      'PORTUGUESE': 'PT',
      'DUTCH': 'NL',
      'BELGIAN': 'BE',
      'AUSTRIAN': 'AT',
      'SWISS': 'CH',
      'SWEDISH': 'SE',
      'NORWEGIAN': 'NO',
      'DANISH': 'DK',
      'FINNISH': 'FI',
      'GREEK': 'GR',
      'CYPRIOT': 'CY',
      'MALTESE': 'MT',
      'LUXEMBOURGISH': 'LU',
    };
    
    return mapping[nationality.toUpperCase()] ?? 'GB';
  }

  static List<String> getCountryOptions(String fieldName, List<String> predictedCountries) {
    if (fieldName == 'issue_country' || fieldName == 'issuing_country') {
      final options = <String>[];
      
      for (final country in predictedCountries) {
        options.add(_getCountryName(country));
      }
      
      if (!options.contains('United Kingdom')) {
        options.insert(0, 'United Kingdom');
      }
      
      return options;
    }
    
    return [];
  }

  static String _getCountryName(String countryCode) {
    final mapping = {
      'GB': 'United Kingdom',
      'IE': 'Ireland',
      'US': 'United States',
      'CA': 'Canada',
      'AU': 'Australia',
      'IN': 'India',
      'PK': 'Pakistan',
      'BD': 'Bangladesh',
      'NG': 'Nigeria',
      'ZA': 'South Africa',
      'FR': 'France',
      'DE': 'Germany',
      'ES': 'Spain',
      'IT': 'Italy',
      'PL': 'Poland',
      'RO': 'Romania',
      'BG': 'Bulgaria',
      'LT': 'Lithuania',
      'LV': 'Latvia',
      'EE': 'Estonia',
      'CZ': 'Czech Republic',
      'SK': 'Slovakia',
      'HU': 'Hungary',
      'SI': 'Slovenia',
      'HR': 'Croatia',
      'PT': 'Portugal',
      'NL': 'Netherlands',
      'BE': 'Belgium',
      'AT': 'Austria',
      'CH': 'Switzerland',
      'SE': 'Sweden',
      'NO': 'Norway',
      'DK': 'Denmark',
      'FI': 'Finland',
      'GR': 'Greece',
      'CY': 'Cyprus',
      'MT': 'Malta',
      'LU': 'Luxembourg',
    };
    
    return mapping[countryCode] ?? countryCode;
  }

  static String? validatePostcodeMatch(String documentPostcode, List<String> applicationPostcodes) {
    final cleanDocPostcode = documentPostcode.replaceAll(' ', '').toUpperCase();
    
    for (final appPostcode in applicationPostcodes) {
      final cleanAppPostcode = appPostcode.replaceAll(' ', '').toUpperCase();
      if (cleanDocPostcode == cleanAppPostcode) {
        return null;
      }
    }
    
    return 'Document postcode must match one of your addresses: ${applicationPostcodes.join(', ')}';
  }

  static String? validateDateOfBirthMatch(String documentDOB, String applicationDOB) {
    try {
      final docDate = DateTime.parse(documentDOB);
      final appDate = DateTime.parse(applicationDOB);
      
      if (docDate.year == appDate.year && 
          docDate.month == appDate.month && 
          docDate.day == appDate.day) {
        return null;
      }
      
      return 'Date of birth on document must match your application details';
    } catch (e) {
      return 'Invalid date format';
    }
  }

  static String? validateNameMatch(String documentName, String applicationName) {
    final docNameClean = documentName.toLowerCase().replaceAll(RegExp(r'[^a-z]'), '');
    final appNameClean = applicationName.toLowerCase().replaceAll(RegExp(r'[^a-z]'), '');

    if (docNameClean.contains(appNameClean) || appNameClean.contains(docNameClean)) {
      return null;
    }

    return 'Name on document must match your application details';
  }

  static String? validateBirthCertificateIssueDate(String issueDate, String dateOfBirth, String documentKey) {

    try {
      final issueParts = issueDate.split('/');
      final birthParts = dateOfBirth.split('/');

      if (issueParts.length != 3 || birthParts.length != 3) {
        return 'Invalid date format';
      }

      final issueDateTime = DateTime(
        int.parse(issueParts[2]), // year
        int.parse(issueParts[1]), // month
        int.parse(issueParts[0]), // day
      );

      final birthDateTime = DateTime(
        int.parse(birthParts[2]), // year
        int.parse(birthParts[1]), // month
        int.parse(birthParts[0]), // day
      );


      // Basic validation: Issue date cannot be before birth date
      if (issueDateTime.isBefore(birthDateTime)) {
        return 'Issue date cannot be before date of birth';
      }

      // For birth certificates issued within 12 months of birth
      if (documentKey.contains('12m')) {
        // Add 12 months to birth date
        final twelveMonthsAfterBirth = DateTime(
          birthDateTime.year + 1,
          birthDateTime.month,
          birthDateTime.day,
        );


        if (issueDateTime.isAfter(twelveMonthsAfterBirth)) {
          return 'Birth certificate must be issued within 12 months of birth date';
        }
      } else {
        // For "after birth" certificates, we still need reasonable limits
        // Issue date shouldn't be more than 100 years after birth (reasonable upper limit)
        final maxReasonableDate = DateTime(
          birthDateTime.year + 100,
          birthDateTime.month,
          birthDateTime.day,
        );

        if (issueDateTime.isAfter(maxReasonableDate)) {
          return 'Issue date seems unreasonable for this document';
        }
      }

      return null;
    } catch (e) {
      return 'Invalid date format';
    }
  }

  static double getDocumentRelevanceScore(String documentKey, DBSFormData formData) {
    double score = 0.0;

    final nationality = formData.applicantDetails.additionalApplicantDetails.birthNationality ?? '';
    final currentCountry = formData.applicantDetails.currentAddress.countryCode ?? '';
    final addressHistory = formData.applicantDetails.previousAddresses;
    final birthCountry = formData.applicantDetails.additionalApplicantDetails.birthCountry ?? '';
    final age = _calculateAge(formData.applicantDetails.dateOfBirth);

    final isBritish = nationality == 'BRITISH' || nationality == 'UK';
    final isUkResident = currentCountry == 'GB' || currentCountry == 'UK';
    final isBornInUk = birthCountry == 'GB' || birthCountry == 'UK';
    final hasLongUkHistory = _hasLongUkAddressHistory(formData);

    final key = documentKey.toLowerCase();

    if (key.contains('passport')) {
      if (isBritish && key.contains('uk')) {
        score = 0.95; // Perfect for British nationals with UK passport
      } else if (isBritish && !key.contains('uk')) {
        score = 0.40; // Lower for British nationals with non-UK passport
      } else {
        score = 0.90; // High for non-British nationals with any passport
      }
    } else if (key.contains('driving_licence') || key.contains('drivers_licence') || key.contains('photocard')) {
      if (isBritish && isUkResident && age >= 17) {
        score = 0.90; // Perfect for UK nationals who can drive
      } else if (isUkResident && hasLongUkHistory && age >= 17) {
        score = 0.70; // Good for long-term residents
      } else {
        score = 0.30; // Lower for others
      }
    } else if (key.contains('birth_certificate')) {
      if (isBornInUk && isBritish) {
        score = 0.85; // Excellent for UK-born British nationals
      } else if (isBritish) {
        score = 0.70; // Good for British nationals born abroad
      } else {
        score = 0.40; // Less relevant for non-British
      }
    } else if (key.contains('adoption_certificate')) {
      // Only relevant for adopted individuals - very low default
      score = 0.05;
    } else if (key.contains('biometric_residence_permit')) {
      if (!isBritish && isUkResident) {
        score = 0.95; // Perfect for non-UK nationals in UK
      } else {
        score = 0.02; // Not relevant for British nationals
      }
    } else if (key.contains('bank_statement') || key.contains('mortgage_statement')) {
      if (isUkResident && hasLongUkHistory) {
        score = 0.85; // Excellent for established residents
      } else if (isUkResident) {
        score = 0.70; // Good for current residents
      } else {
        score = 0.50; // Moderate for others
      }
    } else if (key.contains('council_tax')) {
      if (isUkResident && hasLongUkHistory) {
        score = 0.90; // Perfect for long-term UK residents
      } else if (isUkResident) {
        score = 0.75; // Good for current residents
      } else {
        score = 0.20; // Not relevant for non-residents
      }
    } else if (key.contains('utility_bill')) {
      if (isUkResident) {
        score = 0.75; // Good for UK residents
      } else {
        score = 0.45; // Moderate for others
      }
    } else if (key.contains('marriage_certificate') || key.contains('civil_partnership')) {
      // Only relevant if married - low default
      score = 0.25;
    } else {
      score = 0.40; // Moderate default
    }

    return score.clamp(0.0, 1.0);
  }

  static int _calculateAge(String dateOfBirth) {
    if (dateOfBirth.isEmpty) return 25;
    try {
      final dob = DateTime.parse(dateOfBirth);
      final now = DateTime.now();
      int age = now.year - dob.year;
      if (now.month < dob.month || (now.month == dob.month && now.day < dob.day)) {
        age--;
      }
      return age;
    } catch (e) {
      return 25;
    }
  }

  static bool _hasLongUkAddressHistory(DBSFormData formData) {
    final currentCountry = formData.applicantDetails.currentAddress.countryCode ?? '';
    final isCurrentlyInUk = currentCountry == 'GB' || currentCountry == 'UK';

    if (!isCurrentlyInUk) return false;

    final ukAddressCount = formData.applicantDetails.previousAddresses
        .where((addr) => addr.countryCode == 'GB' || addr.countryCode == 'UK')
        .length;

    return ukAddressCount >= 2; // Has at least 2 previous UK addresses
  }

  static bool _isRecentUkResident(DBSFormData formData) {
    final currentCountry = formData.applicantDetails.currentAddress.countryCode ?? '';
    return currentCountry == 'GB' || currentCountry == 'UK';
  }

  static String getDocumentRelevanceLevel(double score) {
    if (score >= 0.8) return 'highly_recommended';
    if (score >= 0.5) return 'recommended';
    if (score >= 0.3) return 'suitable';
    return 'alternative';
  }

  static String? validateUKDrivingLicence(String licenceNumber, DBSFormData formData) {
    if (licenceNumber.length != 16) {
      return 'UK driving licence number must be 16 characters';
    }

    try {
      final surname = formData.applicantDetails.presentSurname.toUpperCase();
      final forename = formData.applicantDetails.forename.toUpperCase();
      final dateOfBirth = DateTime.parse(formData.applicantDetails.dateOfBirth);
      final gender = formData.applicantDetails.gender.toLowerCase();

      final licenceUpper = licenceNumber.toUpperCase();

      final surnameFromLicence = licenceUpper.substring(0, 5);
      final expectedSurname = _formatSurnameForLicence(surname);

      if (surnameFromLicence != expectedSurname) {
        return 'Driving licence surname does not match application details';
      }

      final yearFromLicence = int.parse(licenceUpper.substring(5, 7));
      final expectedYear = dateOfBirth.year % 100;

      if (yearFromLicence != expectedYear) {
        return 'Driving licence year of birth does not match application details';
      }

      final monthFromLicence = int.parse(licenceUpper.substring(7, 9));
      int expectedMonth = dateOfBirth.month;
      if (gender == 'female') {
        expectedMonth += 50;
      }

      if (monthFromLicence != expectedMonth) {
        return 'Driving licence month/gender does not match application details';
      }

      final dayFromLicence = int.parse(licenceUpper.substring(9, 11));
      if (dayFromLicence != dateOfBirth.day) {
        return 'Driving licence day of birth does not match application details';
      }

      final initialsFromLicence = licenceUpper.substring(13, 15);
      final expectedInitials = _getInitialsForLicence(forename);

      if (initialsFromLicence != expectedInitials) {
        return 'Driving licence initials do not match application details';
      }

      return null;
    } catch (e) {
      return 'Invalid driving licence number format';
    }
  }

  static String _formatSurnameForLicence(String surname) {
    String formatted = surname.replaceAll(RegExp(r'[^A-Z]'), '');
    if (formatted.startsWith('MAC') || formatted.startsWith('MC')) {
      formatted = 'MC${formatted.substring(formatted.startsWith('MAC') ? 3 : 2)}';
    }
    return formatted.padRight(5, '9').substring(0, 5);
  }

  static String _getInitialsForLicence(String forename) {
    final names = forename.split(' ');
    String initials = '';

    if (names.isNotEmpty) {
      initials += names[0].isNotEmpty ? names[0][0] : '9';
    }

    if (names.length > 1) {
      initials += names[1].isNotEmpty ? names[1][0] : '9';
    } else {
      initials += '9';
    }

    return initials;
  }

  static String? validateDocumentExpiry(String expiryDate, String documentKey, String nationality) {
    try {
      final expiry = DateTime.parse(expiryDate.split('/').reversed.join('-'));
      final now = DateTime.now();

      if (expiry.isBefore(now)) {
        return 'Document has expired';
      }

      if (documentKey.contains('passport')) {
        if (nationality != 'BRITISH' && expiry.difference(now).inDays < 90) {
          return 'Non-UK passport must be valid for at least 3 months';
        }
      }

      if (documentKey.contains('driving_licence')) {
        final validTo70 = DateTime(now.year + (70 - _calculateAge(now.toString())), now.month, now.day - 1);
        if (documentKey.contains('uk') && expiry.isAfter(validTo70)) {
          return 'UK driving licence expiry date appears incorrect (should expire day before 70th birthday)';
        }
      }

      return null;
    } catch (e) {
      return 'Invalid expiry date format';
    }
  }

  static List<String> getContextualHints(String fieldName, String documentKey, DBSFormData formData) {
    final hints = <String>[];

    if (fieldName == 'postcode_on_document') {
      final appPostcodes = [
        formData.applicantDetails.currentAddress.postcode,
        ...formData.applicantDetails.previousAddresses.map((addr) => addr.postcode)
      ].where((pc) => pc.isNotEmpty).toList();

      if (appPostcodes.isNotEmpty) {
        hints.add('Must match one of your addresses: ${appPostcodes.join(', ')}');
      }
    }

    if (fieldName == 'issue_country' && documentKey.contains('passport')) {
      final nationality = formData.applicantDetails.additionalApplicantDetails.birthNationality;
      if (nationality == 'BRITISH') {
        hints.add('UK passports are typically issued by HM Passport Office');
      }
    }

    if (fieldName == 'licence_number' && documentKey.contains('uk')) {
      hints.add('16-character format: 5 letters + 6 digits + 2 letters + 2 digits + 1 letter');
    }

    return hints;
  }

  static String? validateEnhancedField(
    String fieldName,
    String fieldType,
    dynamic value,
    Map<String, dynamic>? applicationData,
  ) {
    switch (fieldType) {
      case 'yes_no_confirmation':
        return _validateYesNoConfirmation(fieldName, value, applicationData);
      case 'multiple_choice_postcode':
        return _validateMultipleChoicePostcode(fieldName, value, applicationData);
      case 'smart_date':
        return _validateSmartDate(fieldName, value, applicationData);
      case 'country_dropdown':
        return _validateCountryDropdown(fieldName, value, applicationData);
      default:
        return null;
    }
  }

  static String? _validateYesNoConfirmation(
    String fieldName,
    dynamic value,
    Map<String, dynamic>? applicationData,
  ) {
    if (value == null || value.toString().isEmpty) {
      return 'Please confirm whether the information matches';
    }

    if (value.toString().toLowerCase() == 'no') {
      return 'Please ensure the document information matches your application details';
    }

    if (value.toString().toLowerCase() != 'yes') {
      return 'Please select Yes or No';
    }

    return null;
  }

  static String? _validateMultipleChoicePostcode(
    String fieldName,
    dynamic value,
    Map<String, dynamic>? applicationData,
  ) {
    if (value == null || value.toString().isEmpty) {
      return 'Please select a postcode from the options';
    }

    final postcode = value.toString().trim();

    final ukPostcodeRegex = RegExp(r'^[A-Z]{1,2}[0-9][A-Z0-9]?\s?[0-9][A-Z]{2}$');
    if (!ukPostcodeRegex.hasMatch(postcode.toUpperCase())) {
      return 'Please select a valid UK postcode';
    }

    return null;
  }

  static String? _validateSmartDate(
    String fieldName,
    dynamic value,
    Map<String, dynamic>? applicationData,
  ) {
    if (value == null || value.toString().isEmpty) {
      return 'Please enter a date';
    }

    final dateString = value.toString();

    final dateRegex = RegExp(r'^\d{2}/\d{2}/\d{4}$');
    if (!dateRegex.hasMatch(dateString)) {
      return 'Please enter a valid date (DD/MM/YYYY)';
    }

    try {
      final parts = dateString.split('/');
      final day = int.parse(parts[0]);
      final month = int.parse(parts[1]);
      final year = int.parse(parts[2]);

      final date = DateTime(year, month, day);
      final now = DateTime.now();

      if (fieldName.contains('expiry') || fieldName.contains('expiration')) {
        if (date.isBefore(now)) {
          return 'Document has expired';
        }
      }

      if (fieldName.contains('birth')) {
        if (date.isAfter(now)) {
          return 'Birth date cannot be in the future';
        }

        final age = now.year - date.year;
        if (age > 120) {
          return 'Birth date seems too old';
        }
      }

      if (fieldName.contains('issue')) {
        if (date.isAfter(now)) {
          return 'Issue date cannot be in the future';
        }
      }

    } catch (e) {
      return 'Please enter a valid date';
    }

    return null;
  }

  static String? _validateCountryDropdown(
    String fieldName,
    dynamic value,
    Map<String, dynamic>? applicationData,
  ) {
    if (value == null || value.toString().isEmpty) {
      return 'Please select a country';
    }

    final country = value.toString().trim();

    if (country.length < 2) {
      return 'Please select a valid country';
    }

    return null;
  }

  static bool isEnhancedFieldValid(
    String fieldName,
    String fieldType,
    dynamic value,
    Map<String, dynamic>? applicationData,
  ) {
    final validationResult = validateEnhancedField(fieldName, fieldType, value, applicationData);
    return validationResult == null;
  }

  static Map<String, String> getEnhancedFieldHints(String fieldType) {
    switch (fieldType) {
      case 'yes_no_confirmation':
        return {
          'title': 'Confirm Information',
          'description': 'Please confirm if the information on the document matches your application details',
          'help': 'Select "Yes" if the information matches exactly, or "No" if there are any differences',
        };
      case 'multiple_choice_postcode':
        return {
          'title': 'Select Postcode',
          'description': 'Choose the correct postcode as shown on the document',
          'help': 'Look carefully at the document and select the postcode that matches exactly',
        };
      case 'smart_date':
        return {
          'title': 'Enter Date',
          'description': 'Enter the date as shown on the document',
          'help': 'Use the format DD/MM/YYYY or click the calendar icon to select a date',
        };
      case 'country_dropdown':
        return {
          'title': 'Select Country',
          'description': 'Choose the country as shown on the document',
          'help': 'Select the country that issued this document or where it was created',
        };
      default:
        return {
          'title': 'Enter Information',
          'description': 'Please enter the information as shown on the document',
          'help': 'Make sure the information matches exactly what is printed on the document',
        };
    }
  }
}
