import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';

class FieldTypeMappingService {
  static const Map<String, String> _fieldNameToEnhancedType = {
    // Name fields - use yes/no confirmation
    'full_name_on_document': 'yes_no_confirmation',
    'full_name': 'yes_no_confirmation',
    'name_on_document': 'yes_no_confirmation',
    'forename_on_document': 'yes_no_confirmation',
    'surname_on_document': 'yes_no_confirmation',
    'first_name_on_document': 'yes_no_confirmation',
    'last_name_on_document': 'yes_no_confirmation',
    'complete_name': 'yes_no_confirmation',
    'document_name': 'yes_no_confirmation',
    
    // Postcode fields - use multiple choice
    'postcode_on_document': 'multiple_choice_postcode',
    'postcode': 'multiple_choice_postcode',
    'document_postcode': 'multiple_choice_postcode',
    'address_postcode': 'multiple_choice_postcode',
    
    // Country fields - use dropdown
    'country_of_issue': 'country_dropdown',
    'country': 'country_dropdown',
    'issue_country': 'country_dropdown',
    'issuing_country': 'country_dropdown',
    'nationality': 'country_dropdown',
    'birth_country': 'country_dropdown',
    
    // Date fields - use smart date picker
    'issue_date': 'smart_date',
    'expiry_date': 'smart_date',
    'date_of_birth': 'smart_date',
    'birth_date': 'smart_date',
    'valid_from': 'smart_date',
    'valid_to': 'smart_date',
    'date_issued': 'smart_date',
    'expiration_date': 'smart_date',
  };

  static const Map<String, List<String>> _documentTypeSpecificMappings = {
    'passport': [
      'passport_number',
      'document_number',
      'travel_document_number',
    ],
    'driving_licence': [
      'licence_number',
      'license_number',
      'driver_number',
    ],
    'birth_certificate': [
      'certificate_number',
      'registration_number',
      'birth_registration_number',
    ],
    'marriage_certificate': [
      'certificate_number',
      'marriage_registration_number',
    ],
  };

  static String getEnhancedFieldType(String fieldName, String documentTypeKey) {
    final lowerFieldName = fieldName.toLowerCase();
    
    // Check direct field name mappings first
    if (_fieldNameToEnhancedType.containsKey(lowerFieldName)) {
      return _fieldNameToEnhancedType[lowerFieldName]!;
    }
    
    // Check for partial matches in field names
    for (final entry in _fieldNameToEnhancedType.entries) {
      if (lowerFieldName.contains(entry.key) || entry.key.contains(lowerFieldName)) {
        return entry.value;
      }
    }
    
    // Check document type specific mappings
    final documentMappings = _documentTypeSpecificMappings[documentTypeKey.toLowerCase()];
    if (documentMappings != null) {
      for (final mapping in documentMappings) {
        if (lowerFieldName.contains(mapping.toLowerCase())) {
          return 'enhanced_text'; // Special handling for document-specific fields
        }
      }
    }
    
    // Check for common patterns
    if (_isNameField(lowerFieldName)) {
      return 'yes_no_confirmation';
    }
    
    if (_isPostcodeField(lowerFieldName)) {
      return 'multiple_choice_postcode';
    }
    
    if (_isCountryField(lowerFieldName)) {
      return 'country_dropdown';
    }
    
    if (_isDateField(lowerFieldName)) {
      return 'smart_date';
    }
    
    // Default to original type
    return 'text';
  }

  static bool _isNameField(String fieldName) {
    final nameKeywords = [
      'name', 'forename', 'surname', 'firstname', 'lastname',
      'given_name', 'family_name', 'full_name', 'complete_name'
    ];
    
    return nameKeywords.any((keyword) => 
      fieldName.contains(keyword) && 
      (fieldName.contains('document') || fieldName.contains('on_'))
    );
  }

  static bool _isPostcodeField(String fieldName) {
    final postcodeKeywords = ['postcode', 'postal_code', 'zip_code', 'zip'];
    
    return postcodeKeywords.any((keyword) => fieldName.contains(keyword));
  }

  static bool _isCountryField(String fieldName) {
    final countryKeywords = [
      'country', 'nationality', 'nation', 'issuing_country',
      'country_of_issue', 'birth_country', 'issue_country'
    ];
    
    return countryKeywords.any((keyword) => fieldName.contains(keyword));
  }

  static bool _isDateField(String fieldName) {
    final dateKeywords = [
      'date', 'expiry', 'expiration', 'issue', 'birth',
      'valid_from', 'valid_to', 'issued_on', 'expires_on'
    ];
    
    return dateKeywords.any((keyword) => fieldName.contains(keyword));
  }

  static DocumentDataField enhanceField(DocumentDataField originalField, String documentTypeKey) {
    final enhancedType = getEnhancedFieldType(originalField.name, documentTypeKey);
    
    if (enhancedType == originalField.type) {
      return originalField; // No enhancement needed
    }
    
    return originalField.copyWith(
      type: enhancedType,
      uiConfig: _getUIConfigForEnhancedType(enhancedType, originalField.name),
      validationRules: _getValidationRulesForEnhancedType(enhancedType, originalField.name),
    );
  }

  static Map<String, dynamic> _getUIConfigForEnhancedType(String enhancedType, String fieldName) {
    switch (enhancedType) {
      case 'yes_no_confirmation':
        return {
          'confirmation_style': 'buttons',
          'show_icons': true,
          'button_layout': 'horizontal',
        };
      case 'multiple_choice_postcode':
        return {
          'options_count': 4,
          'shuffle_options': true,
          'show_radio_buttons': true,
        };
      case 'smart_date':
        return {
          'show_calendar_picker': true,
          'allow_manual_entry': true,
          'date_format': 'DD/MM/YYYY',
        };
      case 'country_dropdown':
        return {
          'show_common_countries_first': true,
          'enable_search': true,
          'show_flags': false,
        };
      default:
        return {};
    }
  }

  static Map<String, dynamic> _getValidationRulesForEnhancedType(String enhancedType, String fieldName) {
    switch (enhancedType) {
      case 'yes_no_confirmation':
        return {
          'require_confirmation': true,
          'validate_against_application': true,
        };
      case 'multiple_choice_postcode':
        return {
          'validate_uk_format': true,
          'check_against_address_history': true,
        };
      case 'smart_date':
        return {
          'validate_format': true,
          'check_future_dates': fieldName.contains('expiry'),
          'check_past_dates': fieldName.contains('birth') || fieldName.contains('issue'),
        };
      case 'country_dropdown':
        return {
          'validate_country_code': true,
          'check_document_eligibility': true,
        };
      default:
        return {};
    }
  }

  static List<DocumentDataField> enhanceDocumentFields(
    List<DocumentDataField> originalFields,
    String documentTypeKey,
  ) {
    return originalFields.map((field) => enhanceField(field, documentTypeKey)).toList();
  }

  static bool shouldUseEnhancedField(String fieldName, String documentTypeKey) {
    final enhancedType = getEnhancedFieldType(fieldName, documentTypeKey);
    return enhancedType != 'text' && enhancedType != 'string';
  }

  static String getFieldDescription(String fieldName, String enhancedType) {
    switch (enhancedType) {
      case 'yes_no_confirmation':
        return 'Confirm if the name on the document matches your application details';
      case 'multiple_choice_postcode':
        return 'Select the correct postcode as shown on the document';
      case 'smart_date':
        return 'Enter the date using the date picker or manual entry';
      case 'country_dropdown':
        return 'Select the country from the dropdown list';
      default:
        return 'Enter the ${fieldName.replaceAll('_', ' ').toLowerCase()}';
    }
  }
}
