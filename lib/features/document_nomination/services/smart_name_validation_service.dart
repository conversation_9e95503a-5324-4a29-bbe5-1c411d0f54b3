import 'package:SolidCheck/features/dbs/data/models/dbs_form_data.dart';

class SmartNameValidationService {
  static String generateNameConfirmationText(
    String fieldName,
    ApplicantDetailsData? applicantData,
  ) {
    if (applicantData == null) {
      return 'Can you confirm the name on this document is correct?';
    }
    
    final forename = applicantData.forename;
    final surname = applicantData.presentSurname;
    final fullName = '$forename $surname';
    
    // Handle different field name patterns
    if (_isFullNameField(fieldName)) {
      return 'Can you confirm the full name on this document is $fullName?';
    }

    if (_isForenameField(fieldName)) {
      return 'Can you confirm the first name on this document is $forename?';
    }

    if (_isSurnameField(fieldName)) {
      return 'Can you confirm the surname on this document is $surname?';
    }

    // Default fallback
    return 'Can you confirm the name on this document matches $fullName?';
  }

  static bool _isFullNameField(String fieldName) {
    final lowerField = fieldName.toLowerCase();
    return lowerField.contains('full_name') || 
           lowerField.contains('fullname') ||
           lowerField == 'name' ||
           lowerField.contains('complete_name');
  }

  static bool _isForenameField(String fieldName) {
    final lowerField = fieldName.toLowerCase();
    return lowerField.contains('forename') || 
           lowerField.contains('first_name') ||
           lowerField.contains('firstname') ||
           lowerField.contains('given_name');
  }

  static bool _isSurnameField(String fieldName) {
    final lowerField = fieldName.toLowerCase();
    return lowerField.contains('surname') || 
           lowerField.contains('last_name') ||
           lowerField.contains('lastname') ||
           lowerField.contains('family_name');
  }

  static String? validateNameConfirmation(
    String? userResponse,
    String fieldName,
    ApplicantDetailsData? applicantData,
  ) {
    if (userResponse == null || userResponse.isEmpty) {
      return 'Please confirm whether the name matches';
    }
    
    if (userResponse.toLowerCase() == 'no') {
      return 'Please ensure the document name matches your application details';
    }
    
    if (userResponse.toLowerCase() == 'yes') {
      return null; // Valid
    }
    
    return 'Please select Yes or No';
  }

  static String getExpectedNameValue(
    String fieldName,
    ApplicantDetailsData? applicantData,
  ) {
    if (applicantData == null) return '';
    
    if (_isFullNameField(fieldName)) {
      return '${applicantData.forename} ${applicantData.presentSurname}';
    }
    
    if (_isForenameField(fieldName)) {
      return applicantData.forename;
    }
    
    if (_isSurnameField(fieldName)) {
      return applicantData.presentSurname;
    }
    
    // Default to full name
    return '${applicantData.forename} ${applicantData.presentSurname}';
  }

  static bool shouldUseNameConfirmation(String fieldName) {
    final lowerField = fieldName.toLowerCase();
    
    // Fields that should use yes/no confirmation
    final nameFields = [
      'full_name',
      'fullname',
      'name',
      'forename',
      'first_name',
      'firstname',
      'surname',
      'last_name',
      'lastname',
      'complete_name',
      'given_name',
      'family_name',
    ];
    
    // Check if field name contains any name-related keywords
    return nameFields.any((field) => 
      lowerField.contains(field) || 
      lowerField.contains('${field}_on_document') ||
      lowerField.contains('document_$field')
    );
  }

  static Map<String, String> getNameVariations(ApplicantDetailsData? applicantData) {
    if (applicantData == null) {
      return {};
    }
    
    final variations = <String, String>{};
    
    // Basic name components
    variations['forename'] = applicantData.forename;
    variations['surname'] = applicantData.presentSurname;
    variations['full_name'] = '${applicantData.forename} ${applicantData.presentSurname}';
    
    // Include middle names if available
    if (applicantData.middlenames.isNotEmpty) {
      final middleNames = applicantData.middlenames.join(' ');
      variations['full_name_with_middle'] = '${applicantData.forename} $middleNames ${applicantData.presentSurname}';
    }
    
    // Include birth surname if different
    if (applicantData.additionalApplicantDetails.birthSurname.isNotEmpty &&
        applicantData.additionalApplicantDetails.birthSurname != applicantData.presentSurname) {
      variations['birth_surname'] = applicantData.additionalApplicantDetails.birthSurname;
      variations['full_name_birth_surname'] = '${applicantData.forename} ${applicantData.additionalApplicantDetails.birthSurname}';
    }
    
    // Include other surnames if available
    for (final otherSurname in applicantData.additionalApplicantDetails.otherSurnames) {
      if (otherSurname.name.isNotEmpty) {
        variations['other_surname_${otherSurname.name}'] = otherSurname.name;
        variations['full_name_other_${otherSurname.name}'] = '${applicantData.forename} ${otherSurname.name}';
      }
    }
    
    // Include other forenames if available
    for (final otherForename in applicantData.additionalApplicantDetails.otherForenames) {
      if (otherForename.name.isNotEmpty) {
        variations['other_forename_${otherForename.name}'] = otherForename.name;
        variations['full_name_other_forename_${otherForename.name}'] = '${otherForename.name} ${applicantData.presentSurname}';
      }
    }
    
    return variations;
  }

  static String? findBestNameMatch(
    String documentName,
    ApplicantDetailsData? applicantData,
  ) {
    if (applicantData == null || documentName.isEmpty) return null;
    
    final variations = getNameVariations(applicantData);
    final cleanDocumentName = documentName.toLowerCase().trim();
    
    // Try exact matches first
    for (final entry in variations.entries) {
      if (entry.value.toLowerCase().trim() == cleanDocumentName) {
        return entry.value;
      }
    }
    
    // Try partial matches
    for (final entry in variations.entries) {
      final cleanVariation = entry.value.toLowerCase().trim();
      if (cleanVariation.contains(cleanDocumentName) || 
          cleanDocumentName.contains(cleanVariation)) {
        return entry.value;
      }
    }
    
    return null;
  }

  static double calculateNameSimilarity(String name1, String name2) {
    if (name1.isEmpty || name2.isEmpty) return 0.0;
    
    final clean1 = name1.toLowerCase().replaceAll(RegExp(r'[^a-z]'), '');
    final clean2 = name2.toLowerCase().replaceAll(RegExp(r'[^a-z]'), '');
    
    if (clean1 == clean2) return 1.0;
    
    // Simple similarity calculation based on common characters
    final commonChars = <String>{};
    for (int i = 0; i < clean1.length; i++) {
      if (clean2.contains(clean1[i])) {
        commonChars.add(clean1[i]);
      }
    }
    
    final maxLength = [clean1.length, clean2.length].reduce((a, b) => a > b ? a : b);
    return commonChars.length / maxLength;
  }

  static bool isNameSimilarEnough(String documentName, String applicationName) {
    final similarity = calculateNameSimilarity(documentName, applicationName);
    return similarity >= 0.7; // 70% similarity threshold
  }
}
