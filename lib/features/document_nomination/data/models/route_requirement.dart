import 'dart:convert';

class RouteRequirement {
  final String description;
  final Map<String, GroupRequirement> requiredGroups;
  final int totalDocuments;
  final bool addressConfirmationRequired;

  RouteRequirement({
    required this.description,
    required this.requiredGroups,
    required this.totalDocuments,
    required this.addressConfirmationRequired,
  });

  factory RouteRequirement.fromJson(Map<String, dynamic> json) {
    final requiredGroupsMap = <String, GroupRequirement>{};
    final requiredGroupsJson = json['required_groups'] as Map<String, dynamic>? ?? {};
    
    for (final entry in requiredGroupsJson.entries) {
      requiredGroupsMap[entry.key] = GroupRequirement.fromJson(entry.value);
    }

    return RouteRequirement(
      description: json['description'] ?? '',
      requiredGroups: requiredGroupsMap,
      totalDocuments: json['total_documents'] ?? 0,
      addressConfirmationRequired: json['address_confirmation_required'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    final requiredGroupsJson = <String, dynamic>{};
    for (final entry in requiredGroups.entries) {
      requiredGroupsJson[entry.key] = entry.value.toJson();
    }

    return {
      'description': description,
      'required_groups': requiredGroupsJson,
      'total_documents': totalDocuments,
      'address_confirmation_required': addressConfirmationRequired,
    };
  }

  @override
  String toString() {
    return 'RouteRequirement(description: $description, totalDocuments: $totalDocuments, addressConfirmationRequired: $addressConfirmationRequired)';
  }
}

class GroupRequirement {
  final int min;
  final int max;

  GroupRequirement({
    required this.min,
    required this.max,
  });

  factory GroupRequirement.fromJson(Map<String, dynamic> json) {
    return GroupRequirement(
      min: json['min'] ?? 0,
      max: json['max'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'min': min,
      'max': max,
    };
  }

  @override
  String toString() {
    return 'GroupRequirement(min: $min, max: $max)';
  }
}


