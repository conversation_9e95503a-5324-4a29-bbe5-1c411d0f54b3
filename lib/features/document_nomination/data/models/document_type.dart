import 'dart:convert';
import 'package:SolidCheck/features/document_nomination/services/field_type_mapping_service.dart';

class DocumentType {
  final String key;
  final String name;
  final bool requiresPhoto;
  final bool confirmsAddress;
  final List<String> applicableCountries;
  final List<DocumentDataField> dataFields;

  DocumentType({
    required this.key,
    required this.name,
    required this.requiresPhoto,
    required this.confirmsAddress,
    required this.applicableCountries,
    required this.dataFields,
  });

  // Legacy getter for backward compatibility - use a stable hash
  int get id {
    // Create a stable hash from the key string using djb2 algorithm
    int hash = 5381;
    for (int i = 0; i < key.length; i++) {
      hash = ((hash << 5) + hash + key.codeUnitAt(i)) & 0xffffffff;
    }
    // Ensure we always return a positive integer
    return hash.abs();
  }
  String get documentGroup => '';

  factory DocumentType.fromJson(Map<String, dynamic> json) {
    final dataFieldsList = <DocumentDataField>[];
    final dataFieldsJson = json['data_fields'] as List<dynamic>? ?? [];

    for (final fieldJson in dataFieldsJson) {
      dataFieldsList.add(DocumentDataField.fromJson(fieldJson));
    }

    return DocumentType(
      key: json['key'] ?? '',
      name: json['name'] ?? '',
      requiresPhoto: json['requires_photo'] ?? false,
      confirmsAddress: json['confirms_address'] ?? false,
      applicableCountries: List<String>.from(json['applicable_countries'] ?? []),
      dataFields: dataFieldsList,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'name': name,
      'requires_photo': requiresPhoto,
      'confirms_address': confirmsAddress,
      'applicable_countries': applicableCountries,
      'data_fields': dataFields.map((field) => field.toJson()).toList(),
    };
  }

  List<DocumentDataField> get enhancedDataFields {
    return FieldTypeMappingService.enhanceDocumentFields(dataFields, key);
  }

  bool get hasEnhancedFields {
    return dataFields.any((field) =>
      FieldTypeMappingService.shouldUseEnhancedField(field.name, key)
    );
  }

  @override
  String toString() {
    return 'DocumentType(id: $id, name: $name, documentGroup: $documentGroup, requiresPhoto: $requiresPhoto, confirmsAddress: $confirmsAddress)';
  }
}

class DocumentDataField {
  final String name;
  final String type;
  final bool required;
  final String label;
  final String? placeholder;
  final List<String>? options;
  final Map<String, dynamic>? validationRules;
  final Map<String, dynamic>? uiConfig;

  DocumentDataField({
    required this.name,
    required this.type,
    required this.required,
    required this.label,
    this.placeholder,
    this.options,
    this.validationRules,
    this.uiConfig,
  });

  bool get isEnhancedField => _enhancedFieldTypes.contains(type);

  static const List<String> _enhancedFieldTypes = [
    'yes_no_confirmation',
    'multiple_choice_postcode',
    'smart_date',
    'country_dropdown',
    'enhanced_text',
  ];

  String get enhancedType {
    return FieldTypeMappingService.getEnhancedFieldType(name, '');
  }

  factory DocumentDataField.fromJson(Map<String, dynamic> json) {
    return DocumentDataField(
      name: json['field_name'] ?? json['name'] ?? '',
      type: json['field_type'] ?? json['type'] ?? 'string',
      required: json['required'] ?? false,
      label: json['label'] ?? '',
      placeholder: json['placeholder'],
      options: json['options'] != null ? List<String>.from(json['options']) : null,
      validationRules: json['validation_rules'] != null && json['validation_rules'] is Map
          ? Map<String, dynamic>.from(json['validation_rules'])
          : null,
      uiConfig: json['ui_config'] != null && json['ui_config'] is Map
          ? Map<String, dynamic>.from(json['ui_config'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'type': type,
      'required': required,
      'label': label,
      if (placeholder != null) 'placeholder': placeholder,
      if (options != null) 'options': options,
      if (validationRules != null) 'validation_rules': validationRules,
      if (uiConfig != null) 'ui_config': uiConfig,
    };
  }

  DocumentDataField copyWith({
    String? name,
    String? type,
    bool? required,
    String? label,
    String? placeholder,
    List<String>? options,
    Map<String, dynamic>? validationRules,
    Map<String, dynamic>? uiConfig,
  }) {
    return DocumentDataField(
      name: name ?? this.name,
      type: type ?? this.type,
      required: required ?? this.required,
      label: label ?? this.label,
      placeholder: placeholder ?? this.placeholder,
      options: options ?? this.options,
      validationRules: validationRules ?? this.validationRules,
      uiConfig: uiConfig ?? this.uiConfig,
    );
  }

  @override
  String toString() {
    return 'DocumentDataField(name: $name, type: $type, enhancedType: $enhancedType, required: $required, label: $label)';
  }
}
