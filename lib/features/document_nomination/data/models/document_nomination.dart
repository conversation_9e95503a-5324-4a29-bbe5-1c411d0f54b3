import 'dart:convert';

class DocumentNomination {
  final int? nominationId; // Database ID from API response
  final int documentTypeId;
  final Map<String, dynamic> documentData;
  final bool confirmsAddress;

  DocumentNomination({
    this.nominationId,
    required this.documentTypeId,
    required this.documentData,
    required this.confirmsAddress,
  });

  factory DocumentNomination.fromJson(Map<String, dynamic> json) {
    try {
      print('Parsing DocumentNomination from JSON: $json');

      // Ensure proper type conversion
      final documentTypeId = json['document_type_id'];
      final confirmsAddress = json['confirms_address'];

      print('Raw documentTypeId: $documentTypeId (${documentTypeId.runtimeType})');
      print('Raw confirmsAddress: $confirmsAddress (${confirmsAddress.runtimeType})');

      final parsedDocumentTypeId = documentTypeId is int ? documentTypeId : (documentTypeId is String ? int.tryParse(documentTypeId) ?? 0 : 0);
      final parsedConfirmsAddress = confirmsAddress is bool ? confirmsAddress : (confirmsAddress == 'true' || confirmsAddress == true);

      print('Parsed documentTypeId: $parsedDocumentTypeId (${parsedDocumentTypeId.runtimeType})');
      print('Parsed confirmsAddress: $parsedConfirmsAddress (${parsedConfirmsAddress.runtimeType})');

      final result = DocumentNomination(
        nominationId: json['id'] as int?,
        documentTypeId: parsedDocumentTypeId,
        documentData: Map<String, dynamic>.from(json['document_data'] ?? {}),
        confirmsAddress: parsedConfirmsAddress,
      );

      print('Successfully created DocumentNomination: $result');
      return result;
    } catch (e, stackTrace) {
      print('Error in DocumentNomination.fromJson: $e');
      print('JSON data: $json');
      print('Stack trace: $stackTrace');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'document_type_id': documentTypeId,
      'document_data': documentData,
      'confirms_address': confirmsAddress,
    };
  }

  DocumentNomination copyWith({
    int? nominationId,
    int? documentTypeId,
    Map<String, dynamic>? documentData,
    bool? confirmsAddress,
  }) {
    return DocumentNomination(
      nominationId: nominationId ?? this.nominationId,
      documentTypeId: documentTypeId ?? this.documentTypeId,
      documentData: documentData ?? Map<String, dynamic>.from(this.documentData),
      confirmsAddress: confirmsAddress ?? this.confirmsAddress,
    );
  }

  @override
  String toString() {
    return 'DocumentNomination(documentTypeId: $documentTypeId, documentData: $documentData, confirmsAddress: $confirmsAddress)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DocumentNomination &&
        other.documentTypeId == documentTypeId &&
        other.confirmsAddress == confirmsAddress;
  }

  @override
  int get hashCode {
    return documentTypeId.hashCode ^ confirmsAddress.hashCode;
  }
}

class DocumentNominationRequest {
  final int routeNumber;
  final List<DocumentNomination> nominatedDocuments;

  DocumentNominationRequest({
    required this.routeNumber,
    required this.nominatedDocuments,
  });

  factory DocumentNominationRequest.fromJson(Map<String, dynamic> json) {
    return DocumentNominationRequest(
      routeNumber: json['route_number'] ?? 1,
      nominatedDocuments: (json['nominated_documents'] as List<dynamic>?)
              ?.map((item) => DocumentNomination.fromJson(item))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'route_number': routeNumber,
      'nominated_documents': nominatedDocuments.map((doc) => doc.toJson()).toList(),
    };
  }

  String toRawJson() => json.encode(toJson());

  @override
  String toString() {
    return 'DocumentNominationRequest(routeNumber: $routeNumber, nominatedDocuments: $nominatedDocuments)';
  }
}
