import 'package:SolidCheck/core/constants/constants.dart';
import 'package:SolidCheck/core/network/api_exceptions.dart';
import 'package:SolidCheck/features/document_nomination/data/models/available_documents_response.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_nomination.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:SolidCheck/features/document_nomination/data/models/route_requirement.dart';
import 'package:SolidCheck/features/document_nomination/data/models/validation_response.dart';
import 'package:dio/dio.dart';

class DocumentNominationApiService {
  late final Dio _dio;

  DocumentNominationApiService() {
    _dio = Dio(
      BaseOptions(
        baseUrl: AppConstants.baseURL,
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ),
    );

    _dio.interceptors.add(
      LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (obj) => print('🔵 Document Nomination API: $obj'),
      ),
    );
  }

  Future<AvailableDocumentsResponse> getAvailableDocuments(
    String token,
    String applicationId,
  ) async {
    try {
      final response = await _dio.get(
        '/applications/$applicationId/documents',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
          },
        ),
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        print('🌐 DocumentNominationApiService: Raw API response: ${response.data}');
        final parsedResponse = AvailableDocumentsResponse.fromJson(response.data);
        print('🌐 DocumentNominationApiService: Parsed response success: ${parsedResponse.success}');
        print('🌐 DocumentNominationApiService: Available groups: ${parsedResponse.data.documents.availableByGroup.keys.toList()}');
        return parsedResponse;
      } else {
        throw Exception('Failed to get available documents: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    } catch (error) {
      throw Exception('Failed to get available documents: ${error.toString()}');
    }
  }

  Future<ValidationResponse> validateDocumentNominations(
    String token,
    String applicationId,
    DocumentNominationRequest request,
  ) async {
    try {
      final response = await _dio.post(
        '/applications/$applicationId/validate',
        data: request.toJson(),
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        return ValidationResponse.fromJson(response.data);
      } else {
        throw Exception('Failed to validate document nominations: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    } catch (error) {
      throw Exception('Failed to validate document nominations: ${error.toString()}');
    }
  }

  Future<Map<String, dynamic>> getDocumentStatus(
    String token,
    String applicationId,
  ) async {
    try {
      final response = await _dio.get(
        '/applications/$applicationId/documents/status',
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        return response.data['data'] as Map<String, dynamic>;
      } else {
        throw Exception('Failed to get document status: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    } catch (error) {
      throw Exception('Failed to get document status: ${error.toString()}');
    }
  }

  Future<Map<String, dynamic>> completeDocumentNomination(
    String token,
    String applicationId,
    Map<String, dynamic> requestData,
  ) async {
    try {
      final response = await _dio.post(
        '/applications/$applicationId/documents/complete',
        data: requestData,
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'multipart/form-data',
          },
        ),
      );

      if (response.statusCode == 200) {
        return response.data['data'] as Map<String, dynamic>;
      } else {
        throw Exception('Failed to complete document nomination: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    } catch (error) {
      throw Exception('Failed to complete document nomination: ${error.toString()}');
    }
  }

  Future<ValidationResponse> submitDocumentNominations(
    String token,
    String applicationId,
    DocumentNominationRequest request,
  ) async {
    try {
      final response = await _dio.post(
        '/applications/$applicationId/documents/nominate',
        data: request.toJson(),
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200 || response.statusCode == 201) {
        return ValidationResponse.fromJson(response.data);
      } else {
        throw Exception('Failed to submit document nominations: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    } catch (error) {
      throw Exception('Failed to submit document nominations: ${error.toString()}');
    }
  }

  Exception _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return FetchDataException('Connection timeout');
      case DioExceptionType.badResponse:
        switch (e.response?.statusCode) {
          case 400:
            return BadRequestException(e.response?.data?.toString() ?? 'Bad request');
          case 401:
            return UnauthorisedException(e.response?.data?.toString() ?? 'Unauthorized');
          case 403:
            return UnauthorisedException('Access forbidden');
          case 404:
            return FetchDataException('Resource not found');
          case 422:
            return ValidationException(e.response?.data?.toString() ?? 'Validation error');
          case 500:
          default:
            return FetchDataException(
              'Server error: ${e.response?.statusCode ?? 'Unknown'}',
            );
        }
      case DioExceptionType.cancel:
        return FetchDataException('Request cancelled');
      case DioExceptionType.unknown:
      default:
        return FetchDataException('Network error: ${e.message}');
    }
  }
}
