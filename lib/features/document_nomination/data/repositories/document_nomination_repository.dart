import 'package:SolidCheck/features/auth/data/repositories/auth.dart';
import 'package:SolidCheck/features/document_nomination/data/models/available_documents_response.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_nomination.dart';
import 'package:SolidCheck/features/document_nomination/data/models/validation_response.dart';
import 'package:SolidCheck/features/document_nomination/data/services/document_nomination_api_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final documentNominationRepositoryProvider = Provider<DocumentNominationRepository>((ref) {
  // Use the existing auth provider to get proper authentication
  final authRepository = ref.read(authProvider);
  return DocumentNominationRepository(authRepository);
});

class DocumentNominationRepository {
  final AuthRepository? _authRepository;
  late final DocumentNominationApiService _apiService;

  DocumentNominationRepository(this._authRepository) {
    _apiService = DocumentNominationApiService();
  }

  Future<AvailableDocumentsResponse> getAvailableDocuments(String applicationId) async {
    try {
      if (_authRepository == null) {
        throw Exception('Authentication not available');
      }

      final isLoggedIn = await _authRepository!.isLoggedIn();
      if (!isLoggedIn) {
        throw Exception('User not authenticated');
      }

      final token = await _authRepository!.getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      return await _apiService.getAvailableDocuments(token, applicationId);
    } catch (error) {
      throw Exception('Failed to get available documents: ${error.toString()}');
    }
  }

  Future<ValidationResponse> validateDocumentNominations(
    String applicationId,
    DocumentNominationRequest request,
  ) async {
    try {
      // Temporary: Skip auth check if repository is null
      if (_authRepository == null) {
        throw Exception('Authentication temporarily disabled');
      }

      final isLoggedIn = await _authRepository!.isLoggedIn();
      if (!isLoggedIn) {
        throw Exception('User not authenticated');
      }

      final token = await _authRepository!.getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      return await _apiService.validateDocumentNominations(token, applicationId, request);
    } catch (error) {
      throw Exception('Failed to validate document nominations: ${error.toString()}');
    }
  }

  Future<ValidationResponse> submitDocumentNominations(
    String applicationId,
    DocumentNominationRequest request,
  ) async {
    try {
      print('🌐 Repository: submitDocumentNominations called');
      print('🌐 Repository: applicationId=$applicationId');
      print('🌐 Repository: request=${request.toJson()}');

      if (_authRepository == null) {
        throw Exception('Authentication not available');
      }

      final isLoggedIn = await _authRepository!.isLoggedIn();
      if (!isLoggedIn) {
        throw Exception('User not authenticated');
      }

      final token = await _authRepository!.getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      print('🌐 Repository: calling API service...');
      final result = await _apiService.submitDocumentNominations(token, applicationId, request);
      print('🌐 Repository: API service returned: $result');

      return result;
    } catch (error, stackTrace) {
      print('💥 Repository: Exception in submitDocumentNominations: $error');
      print('💥 Repository: Exception stack trace: $stackTrace');
      throw Exception('Failed to submit document nominations: ${error.toString()}');
    }
  }

  Future<bool> hasValidToken() async {
    try {
      // Temporary: Return false if auth repository is null
      if (_authRepository == null) return false;

      final isLoggedIn = await _authRepository!.isLoggedIn();
      if (!isLoggedIn) return false;

      final token = await _authRepository!.getToken();
      return token != null;
    } catch (error) {
      return false;
    }
  }

  Future<void> ensureAuthenticated() async {
    try {
      // Temporary: Skip auth check if repository is null
      if (_authRepository == null) {
        throw Exception('Authentication temporarily disabled');
      }

      final isLoggedIn = await _authRepository!.isLoggedIn();
      if (!isLoggedIn) {
        throw Exception('User not authenticated');
      }

      final token = await _authRepository!.getToken();
      if (token == null) {
        throw Exception('Authentication required');
      }
    } catch (error) {
      throw Exception('Authentication failed: ${error.toString()}');
    }
  }
}
