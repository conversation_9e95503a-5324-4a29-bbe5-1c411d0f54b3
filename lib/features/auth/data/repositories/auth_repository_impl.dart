import 'dart:async';
import 'package:SolidCheck/core/utils/either.dart';
import 'package:SolidCheck/core/utils/exception_mapper.dart';
import 'package:SolidCheck/features/auth/data/datasources/auth_local.dart';
import 'package:SolidCheck/features/auth/data/datasources/auth_remote.dart';
import 'package:SolidCheck/features/auth/domain/entities/auth_result_entity.dart';
import 'package:SolidCheck/features/auth/domain/entities/change_password_entity.dart';
import 'package:SolidCheck/features/auth/domain/failures/auth_failure.dart';
import 'package:SolidCheck/features/auth/domain/repositories/auth_repository.dart';

class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource _remoteDataSource;
  final AuthLocalDataSource _localDataSource;
  Timer? _authTimer;

  AuthRepositoryImpl(this._remoteDataSource, this._localDataSource);

  @override
  Future<Either<AuthFailure, AuthResultEntity>> login({
    required String email,
    required String password,
  }) async {
    try {
      final result = await _remoteDataSource.login(
        email: email,
        password: password,
      );

      if (result.success) {
        await _handleSuccessfulAuth(result);
        return Right(result.toEntity());
      } else {
        final errorMessage = result.message ?? 'Invalid credentials';
        return Left(InvalidCredentialsFailure(errorMessage));
      }
    } on Exception catch (e) {
      return Left(ExceptionMapper.mapToAuthFailure(e));
    }
  }

  @override
  Future<Either<AuthFailure, AuthResultEntity>> verifyTwoFactorPin({
    required String email,
    required String password,
    required String pin,
  }) async {
    try {
      final result = await _remoteDataSource.verifyTwoFactorPin(
        email: email,
        password: password,
        pin: pin,
      );

      if (result.success) {
        await _handleSuccessfulAuth(result);
      }

      return Right(result.toEntity());
    } on Exception catch (e) {
      return Left(ExceptionMapper.mapToAuthFailure(e));
    }
  }

  @override
  Future<Either<AuthFailure, AuthResultEntity>> refreshToken() async {
    try {
      final token = await _localDataSource.getToken();
      if (token == null) return Left(TokenFailure('No token available'));

      final result = await _remoteDataSource.refreshToken(token);

      if (result.success) {
        await _handleSuccessfulAuth(result);
      }

      return Right(result.toEntity());
    } on Exception catch (e) {
      return Left(ExceptionMapper.mapToAuthFailure(e));
    }
  }

  @override
  Future<Either<AuthFailure, void>> logout() async {
    try {
      await _localDataSource.clearAuthCache();
      _authTimer?.cancel();
      _authTimer = null;
      return Right(null);
    } on Exception catch (e) {
      return Left(ExceptionMapper.mapToAuthFailure(e));
    }
  }

  @override
  Future<bool> isAuthenticated() async {
    return await _localDataSource.isTokenValid();
  }

  @override
  Future<String?> getCurrentToken() async {
    return await _localDataSource.getToken();
  }

  @override
  Future<Either<AuthFailure, AuthResultEntity?>> getCurrentUser() async {
    try {
      final cachedResult = await _localDataSource.getCachedAuthResult();
      return Right(cachedResult?.toEntity());
    } on Exception catch (e) {
      return Left(ExceptionMapper.mapToAuthFailure(e));
    }
  }

  @override
  Future<Either<AuthFailure, ChangePasswordEntity>> changePassword({
    required String currentPassword,
    required String newPassword,
    required String newPasswordConfirmation,
  }) async {
    try {
      final token = await _localDataSource.getToken();

      if (token == null) {
        return Left(TokenFailure('No token available'));
      }

      final result = await _remoteDataSource.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
        newPasswordConfirmation: newPasswordConfirmation,
        token: token,
      );

      if (result.hasValidationErrors) {
        return Left(
          ValidationFailure(
            result.message,
            fieldErrors: result.validationError?.errors,
          ),
        );
      }

      if (!result.success) {
        return Left(ServerFailure(result.message));
      }

      return Right(result.toEntity());
    } on Exception catch (e) {
      return Left(ExceptionMapper.mapToAuthFailure(e));
    }
  }

  Future<void> _handleSuccessfulAuth(dynamic result) async {
    await _localDataSource.cacheAuthResult(result);

    if (result.token != null && result.expireTime != null) {
      await _localDataSource.saveToken(result.token!, result.expireTime!);
      _setupAutoLogout(result.expireTime!);
    }
  }

  void _setupAutoLogout(DateTime expiryTime) {
    _authTimer?.cancel();

    final timeToExpiry = expiryTime.difference(DateTime.now()).inSeconds;
    if (timeToExpiry > 0) {
      _authTimer = Timer(Duration(seconds: timeToExpiry), () {
        logout();
      });
    }
  }
}
