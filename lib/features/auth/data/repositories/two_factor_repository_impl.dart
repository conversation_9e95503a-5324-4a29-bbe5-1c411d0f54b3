import 'package:SolidCheck/core/utils/either.dart';
import 'package:SolidCheck/core/utils/exception_mapper.dart';
import 'package:SolidCheck/features/auth/data/datasources/two_factor_remote.dart';
import 'package:SolidCheck/features/auth/domain/failures/auth_failure.dart';
import 'package:SolidCheck/features/auth/domain/repositories/two_factor_repository.dart';

class TwoFactorRepositoryImpl implements TwoFactorRepository {
  final TwoFactorRemoteDataSource _remoteDataSource;

  TwoFactorRepositoryImpl(this._remoteDataSource);

  @override
  Future<Either<AuthFailure, String>> generateQrCode(String token) async {
    try {
      final result = await _remoteDataSource.generateQrCode(token);
      return Right(result);
    } on Exception catch (e) {
      return Left(ExceptionMapper.mapToAuthFailure(e));
    }
  }

  @override
  Future<Either<AuthFailure, String>> validateToken({
    required String otp,
    required String token,
  }) async {
    try {
      final result = await _remoteDataSource.validateToken(otp: otp, token: token);
      return Right(result);
    } on Exception catch (e) {
      return Left(ExceptionMapper.mapToAuthFailure(e));
    }
  }

  @override
  Future<Either<AuthFailure, void>> enableTwoFactor(String token) async {
    try {
      await _remoteDataSource.enableTwoFactor(token);
      return Right(null);
    } on Exception catch (e) {
      return Left(ExceptionMapper.mapToAuthFailure(e));
    }
  }

  @override
  Future<Either<AuthFailure, String>> disableTwoFactor({
    required String token,
    required String otp,
  }) async {
    try {
      final result = await _remoteDataSource.disableTwoFactor(token: token, otp: otp);
      return Right(result);
    } on Exception catch (e) {
      return Left(ExceptionMapper.mapToAuthFailure(e));
    }
  }

  @override
  Future<Either<AuthFailure, String>> verifyPin({
    required String pin,
    required String token,
  }) async {
    try {
      final result = await _remoteDataSource.verifyPin(pin: pin, token: token);
      return Right(result);
    } on Exception catch (e) {
      return Left(ExceptionMapper.mapToAuthFailure(e));
    }
  }
}
