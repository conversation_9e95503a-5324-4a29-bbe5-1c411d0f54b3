import 'package:SolidCheck/core/utils/either.dart';
import 'package:SolidCheck/features/auth/di/auth_providers.dart';
import 'package:SolidCheck/features/auth/domain/entities/auth_result_entity.dart';
import 'package:SolidCheck/features/auth/domain/failures/auth_failure.dart';
import 'package:SolidCheck/features/auth/domain/repositories/auth_repository.dart' as domain;
import 'package:SolidCheck/features/auth/data/repositories/auth_repository_impl.dart';
import 'package:SolidCheck/features/auth/presentation/viewmodels/auth_viewmodel.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class AuthRepository {
  final Ref ref;
  late final domain.AuthRepository _authRepository;

  AuthRepository(this.ref) {
    // Directly create the implementation to avoid circular dependency
    _authRepository = AuthRepositoryImpl(
      ref.read(authRemoteDataSourceProvider),
      ref.read(authLocalDataSourceProvider),
    );
  }

  Future<String?> getToken() async {
    return await _authRepository.getCurrentToken();
  }

  String? get token => null;

  Future<bool> isLoggedIn() async {
    return await _authRepository.isAuthenticated();
  }

  Future<void> logout() async {
    final authViewModel = ref.read(authViewModelProvider.notifier);
    await authViewModel.logout();
  }

  Future<void> loadPersistedToken() async {}

  Future<void> refreshToken() async {
    await _authRepository.refreshToken();
  }

  Future<Map<String, dynamic>> verifyPin(String email, String password, String pin) async {
    try {
      final authViewModel = ref.read(authViewModelProvider.notifier);
      await authViewModel.verifyTwoFactor(email: email, password: password, pin: pin);
      return {'success': true, 'message': 'PIN verified successfully'};
    } catch (e) {
      return {'success': false, 'message': e.toString()};
    }
  }

  Future<Either<AuthFailure, AuthResultEntity?>> getCurrentUser() async {
    return await _authRepository.getCurrentUser();
  }
}

final authProvider = Provider<AuthRepository>((ref) {
  return AuthRepository(ref);
});