import 'dart:convert';

LoginModel loginModelFromJson(String str) =>
    LoginModel.fromJson(json.decode(str));

String loginModelToJson(LoginModel data) => json.encode(data.toJson());

class LoginModel {
  bool? success;
  String? token;
  String? tokenType;
  DateTime? expireTime;
  List<String>? roles;
  bool? twoFactorEnabled;
  bool? requiresTwoFactorSetup;
  String? qrCodeImage;
  String? qrCodeUrl;
  String? message;
  UserData? user;
  List<Entity>? entities;

  LoginModel({
    this.success,
    this.token,
    this.tokenType,
    this.expireTime,
    this.roles,
    this.twoFactorEnabled,
    this.requiresTwoFactorSetup,
    this.qrCodeImage,
    this.qrCodeUrl,
    this.message,
    this.user,
    this.entities,
  });

  // Legacy property for backward compatibility
  int? get twoFactorActive {
    if (requiresTwoFactorSetup == true) return 1;
    if (twoFactorEnabled == true) return 1;
    return 0;
  }

  factory LoginModel.fromJson(Map<String, dynamic> json) {
    final data = json["data"];
    return LoginModel(
      success: json["success"],
      message: json["message"],
      token: data?["token"],
      tokenType: data?["token_type"],
      twoFactorEnabled: data?["user"]?["two_factor_enabled"] ?? data?["two_factor_enabled"] ?? data?["requires_two_factor"],
      requiresTwoFactorSetup: data?["requires_two_factor_setup"],
      qrCodeImage: data?["qr_code_image"],
      qrCodeUrl: data?["qr_code_url"],
      user: data?["user"] != null ? UserData.fromJson(data["user"]) : null,
      entities: data?["entities"] != null
          ? List<Entity>.from(data["entities"].map((x) => Entity.fromJson(x)))
          : null,
      // Set expiry time to 6 hours from now (default)
      expireTime: DateTime.now().add(const Duration(hours: 6)),
    );
  }

  Map<String, dynamic> toJson() => {
        "success": success,
        "message": message,
        "data": {
          "token": token,
          "token_type": tokenType,
          "user": user?.toJson(),
          "entities": entities?.map((e) => e.toJson()).toList(),
          "requires_two_factor_setup": requiresTwoFactorSetup,
          "qr_code_image": qrCodeImage,
          "qr_code_url": qrCodeUrl,
        }
      };
}

class UserData {
  int? id;
  String? email;
  String? userType;
  String? firstName;
  String? lastName;
  String? phone;
  String? address;
  bool? twoFactorEnabled;

  UserData({
    this.id,
    this.email,
    this.userType,
    this.firstName,
    this.lastName,
    this.phone,
    this.address,
    this.twoFactorEnabled,
  });

  factory UserData.fromJson(Map<String, dynamic> json) => UserData(
        id: json["id"],
        email: json["email"],
        userType: json["user_type"],
        firstName: json["first_name"],
        lastName: json["last_name"],
        phone: json["phone"],
        address: json["address"],
        twoFactorEnabled: json["two_factor_enabled"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "email": email,
        "user_type": userType,
        "first_name": firstName,
        "last_name": lastName,
        "phone": phone,
        "address": address,
        "two_factor_enabled": twoFactorEnabled,
      };
}

class Entity {
  int? id;
  String? name;
  String? code;
  String? role;
  String? email;
  String? phone;

  Entity({
    this.id,
    this.name,
    this.code,
    this.role,
    this.email,
    this.phone,
  });

  factory Entity.fromJson(Map<String, dynamic> json) => Entity(
        id: json["id"],
        name: json["name"],
        code: json["code"],
        role: json["role"],
        email: json["email"],
        phone: json["phone"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "code": code,
        "role": role,
        "email": email,
        "phone": phone,
      };
}
