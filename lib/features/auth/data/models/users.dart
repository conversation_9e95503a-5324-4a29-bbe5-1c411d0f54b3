import 'dart:convert';

class UsersModel {
  UsersModel({
    required this.users,
  });

  factory UsersModel.fromJson(Map<String, dynamic> json) {
    return UsersModel(
      users: json["users"] == null
          ? []
          : List<User>.from(json["users"].map((x) => User.fromJson(x))),
    );
  }

  factory UsersModel.fromRawJson(String str) {
    return UsersModel.fromJson(json.decode(str));
  }

  final List<User> users;

  String toRawJson() {
    return json.encode(toJson());
  }

  Map<String, dynamic> toJson() => {
        "users": users.map((user) => user.toJson()).toList(),
      };
}

class User {
  User({
    required this.id,
    required this.name,
    required this.isActive,
    this.createdAt,
    this.updatedAt,
    this.twoFactorSecret,
    this.profile = [],
    this.userName,
    this.type,
    this.associations,
    this.isSelected = false,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      name: json['name'],
      isActive: json['is_active'],
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
      twoFactorSecret: json['two_factor_secret'],
      profile: json['profile'] != null
          ? List<Profile>.from(json['profile'].map((x) => Profile.fromJson(x)))
          : [],
      userName: json['user_name'],
      type: json['type'],
      associations: json['associations'],
      isSelected: false,
    );
  }

  final int? associations;
  final DateTime? createdAt;
  final int id;
  final String isActive;
  bool isSelected;
  final String name;
  final List<Profile> profile;
  final String? twoFactorSecret;
  final String? type;
  final DateTime? updatedAt;
  final String? userName;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'is_active': isActive,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'two_factor_secret': twoFactorSecret,
      'profile': profile.map((profile) => profile.toJson()).toList(),
      'user_name': userName,
      'type': type,
      'associations': associations,
      'isSelected': isSelected,
    };
  }
}

class Profile {
  Profile({
    this.id,
    this.userId,
    this.email,
    this.firstName,
    this.lastName,
    this.telephone,
    this.createdAt,
    this.updatedAt,
  });

  factory Profile.fromJson(Map<String, dynamic> json) {
    return Profile(
      id: json['id'],
      userId: json['user_id'],
      email: json['email'],
      firstName: json['first_name'],
      lastName: json['last_name'],
      telephone: json['telephone'],
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
    );
  }

  final DateTime? createdAt;
  final String? email;
  final String? firstName;
  final int? id;
  final String? lastName;
  final int? telephone;
  final DateTime? updatedAt;
  final int? userId;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'email': email,
      'first_name': firstName,
      'last_name': lastName,
      'telephone': telephone,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}
