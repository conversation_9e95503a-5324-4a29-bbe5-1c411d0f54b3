import 'package:SolidCheck/features/auth/domain/entities/entity_entity.dart';

class EntityModel extends EntityEntity {
  EntityModel({
    super.id,
    super.name,
    super.code,
    super.role,
    super.email,
    super.phone,
  });

  factory EntityModel.fromJson(Map<String, dynamic> json) {
    return EntityModel(
      id: json['id'],
      name: json['name'],
      code: json['code'],
      role: json['role'],
      email: json['email'],
      phone: json['phone'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'role': role,
      'email': email,
      'phone': phone,
    };
  }

  EntityEntity toEntity() {
    return EntityEntity(
      id: id,
      name: name,
      code: code,
      role: role,
      email: email,
      phone: phone,
    );
  }
}
