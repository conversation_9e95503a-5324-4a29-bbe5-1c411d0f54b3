import 'package:SolidCheck/features/auth/data/models/validation_error.dart';
import 'package:SolidCheck/features/auth/domain/entities/change_password_entity.dart';

class ChangePasswordResponseModel extends ChangePasswordEntity {
  final ValidationErrorModel? validationError;

  ChangePasswordResponseModel({
    required super.success,
    required super.message,
    this.validationError,
  });

  factory ChangePasswordResponseModel.fromJson(Map<String, dynamic> json) {
    return ChangePasswordResponseModel(
      success: json['success'] ?? true,
      message: json['message'] ?? 'Password changed successfully',
      validationError: null,
    );
  }

  factory ChangePasswordResponseModel.fromValidationError(
    ValidationErrorModel validationError,
  ) {
    return ChangePasswordResponseModel(
      success: false,
      message: validationError.formattedErrorMessage,
      validationError: validationError,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      if (validationError != null)
        'validation_error': validationError!.toJson(),
    };
  }

  ChangePasswordEntity toEntity() {
    return ChangePasswordEntity(success: success, message: message);
  }

  bool get hasValidationErrors => validationError != null;

  List<String> getFieldErrors(String field) {
    return validationError?.getFieldErrors(field) ?? [];
  }

  bool hasFieldErrors(String field) {
    return validationError?.hasFieldErrors(field) ?? false;
  }

  String? getFirstFieldError(String field) {
    return validationError?.getFirstFieldError(field);
  }
}
