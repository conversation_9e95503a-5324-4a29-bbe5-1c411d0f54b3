/// Model for change password request
class ChangePasswordRequestModel {
  final String currentPassword;
  final String newPassword;
  final String newPasswordConfirmation;

  ChangePasswordRequestModel({
    required this.currentPassword,
    required this.newPassword,
    required this.newPasswordConfirmation,
  });

  Map<String, dynamic> toJson() {
    return {
      'current_password': currentPassword,
      'new_password': newPassword,
      'new_password_confirmation': newPasswordConfirmation,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChangePasswordRequestModel &&
        other.currentPassword == currentPassword &&
        other.newPassword == newPassword &&
        other.newPasswordConfirmation == newPasswordConfirmation;
  }

  @override
  int get hashCode => 
      currentPassword.hashCode ^ 
      newPassword.hashCode ^ 
      newPasswordConfirmation.hashCode;

  @override
  String toString() => 
      'ChangePasswordRequestModel(currentPassword: [HIDDEN], newPassword: [HIDDEN], newPasswordConfirmation: [HIDDEN])';
}
