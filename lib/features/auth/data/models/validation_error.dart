import 'dart:convert';

/// Model for handling API validation errors
class ValidationErrorModel {
  final String message;
  final Map<String, List<String>> errors;

  ValidationErrorModel({required this.message, required this.errors});

  factory ValidationErrorModel.fromJson(Map<String, dynamic> json) {
    final errorsMap = <String, List<String>>{};

    if (json['errors'] != null) {
      final errors = json['errors'] as Map<String, dynamic>;
      errors.forEach((key, value) {
        if (value is List) {
          errorsMap[key] = value.map((e) => e.toString()).toList();
        } else {
          errorsMap[key] = [value.toString()];
        }
      });
    }

    return ValidationErrorModel(
      message: json['message'] ?? 'Validation failed',
      errors: errorsMap,
    );
  }

  factory ValidationErrorModel.fromJsonString(String jsonString) {
    try {
      final json = jsonDecode(jsonString);
      final result = ValidationErrorModel.fromJson(json);
      return result;
    } catch (e) {
      return ValidationErrorModel(
        message: 'Validation failed',
        errors: {
          'general': [jsonString],
        },
      );
    }
  }

  String get formattedErrorMessage {
    if (errors.isEmpty) return message;

    final errorMessages = <String>[];
    errors.forEach((field, messages) {
      for (final message in messages) {
        errorMessages.add(message);
      }
    });

    return errorMessages.join('\n');
  }

  List<String> getFieldErrors(String field) {
    return errors[field] ?? [];
  }

  bool hasFieldErrors(String field) {
    return errors.containsKey(field) && errors[field]!.isNotEmpty;
  }

  String? getFirstFieldError(String field) {
    final fieldErrors = getFieldErrors(field);
    return fieldErrors.isNotEmpty ? fieldErrors.first : null;
  }

  Map<String, dynamic> toJson() {
    return {'message': message, 'errors': errors};
  }

  @override
  String toString() =>
      'ValidationErrorModel(message: $message, errors: $errors)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ValidationErrorModel &&
        other.message == message &&
        _mapEquals(other.errors, errors);
  }

  @override
  int get hashCode => message.hashCode ^ errors.hashCode;

  bool _mapEquals(
    Map<String, List<String>> map1,
    Map<String, List<String>> map2,
  ) {
    if (map1.length != map2.length) return false;
    for (final key in map1.keys) {
      if (!map2.containsKey(key)) return false;
      final list1 = map1[key]!;
      final list2 = map2[key]!;
      if (list1.length != list2.length) return false;
      for (int i = 0; i < list1.length; i++) {
        if (list1[i] != list2[i]) return false;
      }
    }
    return true;
  }
}
