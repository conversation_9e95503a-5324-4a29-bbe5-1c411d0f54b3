import 'package:SolidCheck/features/auth/domain/entities/user_entity.dart';

class UserModel extends UserEntity {
  UserModel({
    super.id,
    super.email,
    super.userType,
    super.firstName,
    super.lastName,
    super.phone,
    super.address,
    super.twoFactorEnabled,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      email: json['email'],
      userType: json['user_type'],
      firstName: json['first_name'],
      lastName: json['last_name'],
      phone: json['phone'],
      address: json['address'],
      twoFactorEnabled: json['two_factor_enabled'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'user_type': userType,
      'first_name': firstName,
      'last_name': lastName,
      'phone': phone,
      'address': address,
      'two_factor_enabled': twoFactorEnabled,
    };
  }

  UserEntity toEntity() {
    return UserEntity(
      id: id,
      email: email,
      userType: userType,
      firstName: firstName,
      lastName: lastName,
      phone: phone,
      address: address,
      twoFactorEnabled: twoFactorEnabled,
    );
  }
}
