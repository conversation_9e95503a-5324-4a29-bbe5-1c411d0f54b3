import 'package:SolidCheck/features/auth/data/models/entity.dart';
import 'package:SolidCheck/features/auth/data/models/user.dart';
import 'package:SolidCheck/features/auth/domain/entities/auth_result_entity.dart';
import 'package:SolidCheck/features/auth/domain/entities/entity_entity.dart';
import 'package:SolidCheck/features/auth/domain/entities/user_entity.dart';

class AuthResultModel extends AuthResultEntity {
  AuthResultModel({
    required super.success,
    super.token,
    super.tokenType,
    super.expireTime,
    super.roles,
    super.twoFactorEnabled,
    super.requiresTwoFactorSetup,
    super.qrCodeImage,
    super.qrCodeUrl,
    super.tempSecret,
    super.message,
    super.user,
    super.entities,
  });

  factory AuthResultModel.fromJson(Map<String, dynamic> json) {
    final data = json['data'];
    final success = json['success'] ?? false;

    // Handle error responses that don't have a data field
    if (!success && data == null) {
      return AuthResultModel(
        success: success,
        message: json['message'],
      );
    }

    return AuthResultModel(
      success: success,
      message: json['message'],
      token: data?['token'],
      tokenType: data?['token_type'],
      twoFactorEnabled: data?['user']?['two_factor_enabled'] ??
                       data?['two_factor_enabled'] ??
                       data?['requires_two_factor'],
      requiresTwoFactorSetup: data?['requires_two_factor_setup'],
      qrCodeImage: data?['qr_code_image'], // Legacy field
      qrCodeUrl: data?['qr_code_url'] ?? data?['qr_code_image_url'], // Support both old and new field names
      tempSecret: data?['temp_secret'],
      user: data?['user'] != null ? UserModel.fromJson(data['user']).toEntity() : null,
      entities: data?['entities'] != null
          ? List<EntityEntity>.from(
              data['entities'].map((x) => EntityModel.fromJson(x).toEntity())
            )
          : null,
      expireTime: DateTime.now().add(const Duration(hours: 6)),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': {
        'token': token,
        'token_type': tokenType,
        'two_factor_enabled': twoFactorEnabled,
        'requires_two_factor_setup': requiresTwoFactorSetup,
        'qr_code_image': qrCodeImage,
        'qr_code_url': qrCodeUrl,
        'temp_secret': tempSecret,
        'user': user != null ? UserModel(
          id: user!.id,
          email: user!.email,
          userType: user!.userType,
          firstName: user!.firstName,
          lastName: user!.lastName,
          phone: user!.phone,
          address: user!.address,
          twoFactorEnabled: user!.twoFactorEnabled,
        ).toJson() : null,
        'entities': entities?.map((e) => EntityModel(
          id: e.id,
          name: e.name,
          code: e.code,
          role: e.role,
          email: e.email,
          phone: e.phone,
        ).toJson()).toList(),
      },
    };
  }

  AuthResultEntity toEntity() {
    return AuthResultEntity(
      success: success,
      token: token,
      tokenType: tokenType,
      expireTime: expireTime,
      roles: roles,
      twoFactorEnabled: twoFactorEnabled,
      requiresTwoFactorSetup: requiresTwoFactorSetup,
      qrCodeImage: qrCodeImage,
      qrCodeUrl: qrCodeUrl,
      tempSecret: tempSecret,
      message: message,
      user: user,
      entities: entities,
    );
  }
}
