import 'package:SolidCheck/core/constants/constants.dart';
import 'package:SolidCheck/core/network/http_client.dart';

class AuthService {
  Future<dynamic> login(String username, String password) async {
    return await HttpClient().postData(
      '/auth/login',
      {'email': username, 'password': password},
    );
  }

  Future<dynamic> verifyPin(String email, String password, String pin) async {
    return await HttpClient().postData(
      '/auth/verify-pin',
      {
        'email': email,
        'password': password,
        'pin': pin,
      },
    );
  }
}
