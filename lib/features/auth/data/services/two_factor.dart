import 'package:SolidCheck/core/constants/constants.dart';
import 'package:dio/dio.dart';

class TwoFactorService {
  late final Dio _dio;

  TwoFactorService() {
    _dio = Dio(
      BaseOptions(
        baseUrl: AppConstants.baseURL,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ),
    );
  }

  Future<Response> generate2FARequest(String token) async {
    return await _dio.get('/auth/generate', queryParameters: {'token': token});
  }

  Future<Response> validate2FATokenRequest(String otp, String token) async {
    return await _dio.post(
      '/auth/validate2FA',
      queryParameters: {'token': token},
      data: {'one_time_password': otp},
    );
  }

  Future<Response> verifyPinRequest(String pin, String token) async {
    return await _dio.post(
      '/auth/verify-pin',
      data: {'pin': pin},
      options: Options(headers: {'Authorization': 'Bearer $token'}),
    );
  }

  Future<Response> enable2FARequest() async {
    return await _dio.get('/auth/enable2FA');
  }

  Future<Response> disable2FARequest() async {
    return await _dio.get('/auth/disable2FA');
  }
}
