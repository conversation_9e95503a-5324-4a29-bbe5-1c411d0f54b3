import 'package:SolidCheck/core/constants/constants.dart';
import 'package:dio/dio.dart';

abstract class TwoFactorRemoteDataSource {
  Future<String> generateQrCode(String token);
  Future<String> validateToken({required String otp, required String token});
  Future<void> enableTwoFactor(String token);
  Future<String> disableTwoFactor({required String token, required String otp});
  Future<String> verifyPin({required String pin, required String token});
}

class TwoFactorRemoteDataSourceImpl implements TwoFactorRemoteDataSource {
  late final Dio _dio;

  TwoFactorRemoteDataSourceImpl() {
    _dio = Dio(BaseOptions(
      baseUrl: AppConstants.baseURL,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));
  }

  @override
  Future<String> generateQrCode(String token) async {
    final response = await _dio.get('/auth/generate', queryParameters: {'token': token});

    if (response.statusCode == 200) {
      return response.data['QR_code'] ?? '';
    } else {
      throw Exception('Failed to generate QR code');
    }
  }

  @override
  Future<String> validateToken({required String otp, required String token}) async {
    final response = await _dio.post(
      '/validate2FA',
      queryParameters: {'token': token},
      data: {'one_time_password': otp},
    );

    if (response.statusCode == 200) {
      if (response.data['success'] == true) {
        return '2FA validated successfully.';
      } else if (response.data['error'] != null) {
        return response.data['error'];
      } else {
        return 'Unexpected response format.';
      }
    } else if (response.statusCode == 401) {
      return 'Invalid 2FA code';
    } else {
      throw Exception('Failed to validate 2FA: ${response.statusMessage}');
    }
  }

  @override
  Future<void> enableTwoFactor(String token) async {
    final response = await _dio.post('/auth/enable2FA', queryParameters: {'token': token});

    if (response.statusCode != 200) {
      throw Exception('Failed to enable 2FA');
    }
  }

  @override
  Future<String> disableTwoFactor({required String token, required String otp}) async {
    final response = await _dio.post(
      '/auth/disable2FA',
      queryParameters: {'token': token},
      data: {'one_time_password': otp},
    );

    if (response.statusCode == 200) {
      return response.data['message'] ?? '2FA disabled successfully';
    } else {
      throw Exception('Failed to disable 2FA');
    }
  }

  @override
  Future<String> verifyPin({required String pin, required String token}) async {
    final response = await _dio.post(
      '/auth/verifyPin',
      queryParameters: {'token': token},
      data: {'pin': pin},
    );

    if (response.statusCode == 200) {
      return response.data['message'] ?? 'PIN verified successfully';
    } else {
      throw Exception('Failed to verify PIN');
    }
  }
}
