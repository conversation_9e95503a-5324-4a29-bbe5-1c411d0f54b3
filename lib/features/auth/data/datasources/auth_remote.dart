import 'package:SolidCheck/core/network/api_exceptions.dart';
import 'package:SolidCheck/core/network/http_client.dart';
import 'package:SolidCheck/features/auth/data/models/auth_result.dart';
import 'package:SolidCheck/features/auth/data/models/change_password_response.dart';
import 'package:SolidCheck/features/auth/data/models/validation_error.dart';

abstract class AuthRemoteDataSource {
  Future<AuthResultModel> login({
    required String email,
    required String password,
  });

  Future<AuthResultModel> verifyTwoFactorPin({
    required String email,
    required String password,
    required String pin,
  });

  Future<AuthResultModel> refreshToken(String token);

  Future<ChangePasswordResponseModel> changePassword({
    required String currentPassword,
    required String newPassword,
    required String newPasswordConfirmation,
    required String token,
  });
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final HttpClient _httpClient;

  AuthRemoteDataSourceImpl(this._httpClient);

  @override
  Future<AuthResultModel> login({
    required String email,
    required String password,
  }) async {
    final response = await _httpClient.postData('/auth/login', {
      'email': email,
      'password': password,
    });

    return AuthResultModel.fromJson(response);
  }

  @override
  Future<AuthResultModel> verifyTwoFactorPin({
    required String email,
    required String password,
    required String pin,
  }) async {
    final response = await _httpClient.postData('/auth/verify-pin', {
      'email': email,
      'password': password,
      'pin': pin,
    });

    return AuthResultModel.fromJson(response);
  }

  @override
  Future<AuthResultModel> refreshToken(String token) async {
    final response = await _httpClient.postData(
      '/getUserRefresh',
      {},
      headers: {'Authorization': 'Bearer $token'},
    );

    return AuthResultModel.fromJson(response);
  }

  @override
  Future<ChangePasswordResponseModel> changePassword({
    required String currentPassword,
    required String newPassword,
    required String newPasswordConfirmation,
    required String token,
  }) async {
    try {
      final response = await _httpClient.postData(
        '/auth/change-password',
        {
          'current_password': currentPassword,
          'new_password': newPassword,
          'new_password_confirmation': newPasswordConfirmation,
        },
        headers: {'Authorization': 'Bearer $token'},
      );

      return ChangePasswordResponseModel.fromJson(response);
    } on ValidationException catch (e) {
      final validationError = ValidationErrorModel.fromJsonString(
        e.message.replaceFirst('Validation Error: ', ''),
      );
      return ChangePasswordResponseModel.fromValidationError(validationError);
    } on UnauthorisedException catch (e) {
      if (e.message.contains('Session invalidated') ||
          e.message.contains('redirected')) {
        return ChangePasswordResponseModel(
          success: true,
          message: 'Password changed successfully. Please login again.',
        );
      }
      rethrow;
    } catch (e) {
      rethrow;
    }
  }
}
