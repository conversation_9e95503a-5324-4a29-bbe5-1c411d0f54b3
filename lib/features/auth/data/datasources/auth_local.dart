import 'dart:convert';

import 'package:SolidCheck/features/auth/data/models/auth_result.dart';
import 'package:shared_preferences/shared_preferences.dart';

abstract class AuthLocalDataSource {
  Future<void> cacheAuthResult(AuthResultModel authResult);
  Future<AuthResultModel?> getCachedAuthResult();
  Future<void> clearAuthCache();
  Future<String?> getToken();
  Future<void> saveToken(String token, DateTime expiryDate);
  Future<void> clearToken();
  Future<bool> isTokenValid();
}

class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final SharedPreferences _prefs;

  AuthLocalDataSourceImpl(this._prefs);

  static final String _tokenKey = 'auth_token';
  static final String _expiryKey = 'token_expiry';
  static final String _authResultKey = 'auth_result';

  @override
  Future<void> cacheAuthResult(AuthResultModel authResult) async {
    final jsonString = jsonEncode(authResult.toJson());
    await _prefs.setString(_authResultKey, jsonString);
  }

  @override
  Future<AuthResultModel?> getCachedAuthResult() async {
    final jsonString = _prefs.getString(_authResultKey);
    if (jsonString == null) return null;
    
    try {
      final json = jsonDecode(jsonString);
      return AuthResultModel.fromJson(json);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> clearAuthCache() async {
    await _prefs.remove(_authResultKey);
    await clearToken();
  }

  @override
  Future<String?> getToken() async {
    return _prefs.getString(_tokenKey);
  }

  @override
  Future<void> saveToken(String token, DateTime expiryDate) async {
    await _prefs.setString(_tokenKey, token);
    await _prefs.setString(_expiryKey, expiryDate.toIso8601String());
  }

  @override
  Future<void> clearToken() async {
    await _prefs.remove(_tokenKey);
    await _prefs.remove(_expiryKey);
  }

  @override
  Future<bool> isTokenValid() async {
    final token = await getToken();
    if (token == null) return false;

    final expiryString = _prefs.getString(_expiryKey);
    if (expiryString == null) return false;

    try {
      final expiryDate = DateTime.parse(expiryString);
      return expiryDate.isAfter(DateTime.now());
    } catch (e) {
      return false;
    }
  }
}
