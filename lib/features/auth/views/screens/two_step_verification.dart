import 'package:SolidCheck/core/constants/constants.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/auth/presentation/screens/login_screen.dart';
import 'package:SolidCheck/features/dashboard/presentation/screens/dashboard_main_layout_screen.dart';
import 'package:SolidCheck/shared/utils/responsive.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

class TwoStepVerficationMobileView extends ConsumerStatefulWidget {
  const TwoStepVerficationMobileView({super.key, required this.onSubmit});

  final Function onSubmit;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _QrCodeScreenMobileViewState();
}

class _QrCodeScreenMobileViewState
    extends ConsumerState<TwoStepVerficationMobileView> {
  Future<bool> _showPrivacyDialog(BuildContext context, WidgetRef ref) async {
    const Color dialogBorderColor = Color(0XFFB6B5B5);
    bool agreed = false;
    await showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Container(
          decoration: const BoxDecoration(color: AppColors.kWhiteColor),
          padding: const EdgeInsets.all(16),
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 0.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    'Privacy Statement',
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: AppColors.kBlueColor,
                    ),
                  ),
                  const SizedBox(height: 15.0),
                  Container(
                    height: MediaQuery.of(context).size.height * 0.5,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5.0),
                      border: Border.all(color: dialogBorderColor),
                    ),
                    child: const SingleChildScrollView(
                      child: Padding(
                        padding: EdgeInsets.all(12.0),
                        child: Text(
                          'Welcome to Solid Check. Your privacy and data security are important to us. '
                          'We are committed to handling your personal information with transparency and care.\n\n'
                          '1. Data Collection and Use\n'
                          'Solid Check collects and processes personal information solely for the purpose of providing background checks, verifications, and related services. '
                          'We only collect information necessary to complete these checks as requested by your employer or organization.\n\n'
                          '2. Data Storage and Retention\n'
                          'Your data is stored securely on UK-based servers and is protected in compliance with GDPR standards. '
                          'We retain information for the duration required to complete our services.\n\n'
                          '3. Security Measures\n'
                          'We use advanced security measures, including encryption, secure authentication, and ongoing monitoring, to protect your data.\n\n'
                          '4. Your Rights\n'
                          'You have the right to access, update, or request the deletion of your personal information at any time.\n\n'
                          '5. Cookies and Tracking\n'
                          'We use cookies to improve your user experience and provide essential functionality.\n\n'
                          '6. Third Party Disclosure\n'
                          'We do not share your personal information with third parties except as required to complete background checks or by law.\n\n'
                          'By continuing to use Solid Check, you acknowledge and agree to this Privacy Statement.',
                          style: TextStyle(fontSize: 16, height: 1.5),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16.0),
                  // PrivacyPolicyButtons(
                  //   agreetext: "I agree with the terms",
                  //   disagreetext: "I don’t agree with the terms",
                  //   onDisagree: () {
                  //     Navigator.pop(context);
                  //     _showDisagreeDialog(context, ref);
                  //   },
                  //   onAgree: () async {
                  //     final prefs = await SharedPreferences.getInstance();
                  //     await prefs.setBool('privacy_policy_accepted_', true);
                  //     if (!mounted) {
                  //       Navigator.pop(context);
                  //     }
                  //   },
                  // ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
    return agreed;
  }



  Widget _build6DigitCodeField(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(6, (index) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 05.0),
          child: SizedBox(
            width: 39,
            child: TextField(
              onChanged: (value) {
                if (value.isNotEmpty && index < 5) {
                  FocusScope.of(context).nextFocus();
                } else if (value.isEmpty && index > 0) {
                  FocusScope.of(context).previousFocus();
                }
              },
              maxLength: 1,
              keyboardType: TextInputType.number,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              decoration: InputDecoration(
                counterText: "",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                  borderSide: const BorderSide(
                    color: AppColors.kBlueColor,
                    width: 2.0,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                  borderSide: const BorderSide(
                    color: Colors.transparent,
                    width: 0.0,
                  ),
                ),
                filled: true,
                fillColor: AppColors.kFillColor,
              ),
            ),
          ),
        );
      }),
    );
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    ResponsiveUtil.isMobile(context);
    return Stack(
      fit: StackFit.expand,
      children: [
        SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(15.0),
            child: Column(
              children: [
                Center(
                  child: Image.asset(
                    AppConstants.solidCheckLoginLogo,
                    fit: BoxFit.contain,
                    height: size.height * 0.20,
                  ),
                ),
                const SizedBox(height: 20.0),
                Card(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10.0,
                      vertical: 10.0,
                    ),
                    child: SizedBox(
                      height: size.height * 0.65,
                      child: Form(
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Text(
                                "Scan QR Code",
                                style: TextStyle(
                                  fontSize: 22,
                                  color: AppColors.kQuestionTextColor,
                                  fontWeight: FontWeight.w500,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                "Open the an authenticator app and scan this QR code ",
                                style: TextStyle(
                                  fontSize: 16,
                                  color: AppColors.kBlueColor,
                                  fontWeight: FontWeight.w400,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              // Center(
                              //   child: qrCodeState.when(
                              //     data: (qrCodeSvg) {
                              //       return SvgPicture.string(
                              //         qrCodeSvg,
                              //         width:
                              //             MediaQuery.of(context).size.width *
                              //             0.3,
                              //         height:
                              //             MediaQuery.of(context).size.width *
                              //             0.3,
                              //       );
                              //     },
                              //     loading: () =>
                              //         const CircularProgressIndicator(),
                              //     error: (e, stackTrace) =>
                              //         Text('Failed to load QR Code: $e'),
                              //   ),
                              // ),
                              SizedBox(
                                height:
                                    MediaQuery.of(context).size.height * 0.02,
                              ),
                              _build6DigitCodeField(context),
                              SizedBox(
                                height:
                                    MediaQuery.of(context).size.height * 0.02,
                              ),
                              Column(
                                spacing: 10,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  GestureDetector(
                                    onTap: () async {
                                      final prefs =
                                          await SharedPreferences.getInstance();
                                      await prefs.remove('token');
                                      await prefs.remove('is2FACompleted');
                                      if (!mounted) {
                                        Navigator.pushReplacement(
                                          context,
                                          MaterialPageRoute(
                                            builder: (_) => const LoginScreen(),
                                          ),
                                        );
                                      }
                                    },
                                    child: Container(
                                      width:
                                          MediaQuery.of(context).size.width * 1,
                                      height: 50,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                          13.5,
                                        ),
                                        border: Border.all(
                                          width: 2,
                                          color: AppColors.kBlueColor,
                                        ),
                                      ),
                                      child: const Padding(
                                        padding: EdgeInsets.symmetric(
                                          vertical: 10,
                                        ),
                                        child: Center(
                                          child: Text(
                                            'Logout',
                                            style: TextStyle(
                                              fontSize: 19.0,
                                              color: AppColors.kBlueColor,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  GestureDetector(
                                    onTap: () async {
                                      final prefs =
                                          await SharedPreferences.getInstance();
                                      await prefs.setBool(
                                        'is2FACompleted',
                                        true,
                                      );
                                      bool isAccepted =
                                          prefs.getBool(
                                            'privacy_policy_accepted_',
                                          ) ??
                                          false;
                                      if (!mounted) return;
                                      if (isAccepted) {
                                        Navigator.pushReplacement(
                                          context,
                                          MaterialPageRoute(
                                            builder: (_) =>
                                                const DashboardMainLayoutScreen(),
                                          ),
                                        );
                                      } else {
                                        _showPrivacyDialog(context, ref);
                                      }
                                    },
                                    child: Container(
                                      width:
                                          MediaQuery.of(context).size.width * 1,
                                      height: 50,
                                      decoration: BoxDecoration(
                                        color: AppColors.kBlueColor,
                                        borderRadius: BorderRadius.circular(
                                          13.5,
                                        ),
                                        border: Border.all(
                                          width: 2,
                                          color: AppColors.kBlueColor,
                                        ),
                                      ),
                                      child: const Padding(
                                        padding: EdgeInsets.symmetric(
                                          vertical: 10,
                                        ),
                                        child: Center(
                                          child: Text(
                                            'Next',
                                            style: TextStyle(
                                              fontSize: 19.0,
                                              color: AppColors.kWhiteColor,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
