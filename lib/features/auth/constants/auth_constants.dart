/// Authentication module specific constants
class AuthConstants {
  // API Endpoints
  static String loginEndpoint = '/auth/login';
  static String logoutEndpoint = '/auth/logout';
  static String generate2FAEndpoint = '/auth/generate';
  static String validate2FAEndpoint = '/auth/validate2FA';
  static String verifyPinEndpoint = '/auth/verify-pin';

  // Storage Keys
  static String tokenKey = 'auth_token';
  static String userKey = 'user_data';
  static String tempEmailKey = 'tempEmail';
  static String tempPasswordKey = 'tempPassword';
  static String qrCodeUrlKey = 'qrCodeUrl';
  static String qrCodeImageKey = 'qrCodeImage';
  static String needsPinVerificationKey = 'needsPinVerification';
  static String requiresTwoFactorSetupKey = 'requiresTwoFactorSetup';

  // Form validation
  static int minPasswordLength = 8;
  static int maxPasswordLength = 128;
  static int pinLength = 6;

  // Error messages
  static String invalidEmailError = 'Please enter a valid email address';
  static String emptyEmailError = 'Email is required';
  static String emptyPasswordError = 'Password is required';
  static String shortPasswordError = 'Password must be at least $minPasswordLength characters';
  static String longPasswordError = 'Password must be less than $maxPasswordLength characters';
  static String emptyPinError = 'PIN is required';
  static String invalidPinError = 'PIN must be $pinLength digits';
  static String loginFailedError = 'Login failed. Please check your credentials.';
  static String networkError = 'Network connection error. Please try again.';
  static String twoFactorRequiredError = 'Two-factor authentication is required';
  static String invalidTwoFactorError = 'Invalid two-factor authentication code';
  static String sessionExpiredError = 'Your session has expired. Please log in again.';

  // Success messages
  static String loginSuccessMessage = 'Login successful';
  static String logoutSuccessMessage = 'Logged out successfully';
  static String twoFactorSetupSuccessMessage = 'Two-factor authentication setup completed';
  static String pinVerificationSuccessMessage = 'PIN verification successful';

  // UI Text
  static String loginTitle = 'Welcome Back';
  static String loginSubtitle = 'Sign in to your SolidCheck account';
  static String emailLabel = 'Email Address';
  static String passwordLabel = 'Password';
  static String loginButtonText = 'Sign In';
  static String forgotPasswordText = 'Forgot Password?';
  static String rememberMeText = 'Remember me';

  // Two-Factor Authentication
  static String twoFactorTitle = 'Two-Factor Authentication';
  static String twoFactorSubtitle = 'Enter the verification code from your authenticator app';
  static String twoFactorSetupTitle = 'Set Up Two-Factor Authentication';
  static String twoFactorSetupSubtitle = 'Scan the QR code with your authenticator app';
  static String pinLabel = 'Verification Code';
  static String verifyButtonText = 'Verify';
  static String setupButtonText = 'Complete Setup';
  static String qrCodeInstructions = 'Scan this QR code with your authenticator app, then enter the verification code below.';

  // Loading states
  static String signingInText = 'Signing in...';
  static String verifyingText = 'Verifying...';
  static String settingUpText = 'Setting up...';
  static String signingOutText = 'Signing out...';

  // Validation patterns
  static String emailPattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static String pinPattern = r'^\d{6}$';

  // Animation durations
  static Duration defaultAnimationDuration = const Duration(milliseconds: 300);
  static Duration loadingAnimationDuration = const Duration(milliseconds: 500);

  // Timeouts
  static Duration loginTimeout = const Duration(seconds: 30);
  static Duration verificationTimeout = const Duration(seconds: 30);

  // Asset paths
  static String loginLogoPath = 'assets/logos/solidcheck-logo-login-screen.png';
  static String qrCodePlaceholderPath = 'assets/images/qr_placeholder.png';

  // Colors (if module-specific colors are needed)
  static String primaryColorHex = '#1E88E5';
  static String secondaryColorHex = '#0D47A1';
  static String errorColorHex = '#EF4444';
  static String successColorHex = '#10B981';

  // Responsive breakpoints
  static double mobileBreakpoint = 768;
  static double tabletBreakpoint = 1024;

  // Form field constraints
  static double formFieldHeight = 56;
  static double formFieldBorderRadius = 12;
  static double buttonHeight = 48;
  static double buttonBorderRadius = 12;

  // Spacing
  static double smallSpacing = 8;
  static double mediumSpacing = 16;
  static double largeSpacing = 24;
  static double extraLargeSpacing = 32;

  // QR Code settings
  static double qrCodeSize = 200;
  static double qrCodeBorderRadius = 16;

  // Session management
  static Duration sessionCheckInterval = const Duration(minutes: 5);
  static Duration sessionWarningTime = const Duration(minutes: 2);
}
