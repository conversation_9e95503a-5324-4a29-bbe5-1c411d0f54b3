import 'package:SolidCheck/features/auth/data/models/validation_error.dart';

/// State for change password functionality
class ChangePasswordState {
  final bool isLoading;
  final bool isSuccess;
  final String? error;
  final String? successMessage;
  final ValidationErrorModel? validationError;

  ChangePasswordState({
    this.isLoading = false,
    this.isSuccess = false,
    this.error,
    this.successMessage,
    this.validationError,
  });

  ChangePasswordState copyWith({
    bool? isLoading,
    bool? isSuccess,
    String? error,
    String? successMessage,
    ValidationErrorModel? validationError,
  }) {
    return ChangePasswordState(
      isLoading: isLoading ?? this.isLoading,
      isSuccess: isSuccess ?? this.isSuccess,
      error: error,
      successMessage: successMessage ?? this.successMessage,
      validationError: validationError,
    );
  }

  /// Check if there are validation errors
  bool get hasValidationErrors => validationError != null;

  /// Get validation errors for a specific field
  List<String> getFieldErrors(String field) {
    return validationError?.getFieldErrors(field) ?? [];
  }

  /// Check if a specific field has validation errors
  bool hasFieldErrors(String field) {
    return validationError?.hasFieldErrors(field) ?? false;
  }

  /// Get the first validation error for a specific field
  String? getFirstFieldError(String field) {
    return validationError?.getFirstFieldError(field);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChangePasswordState &&
        other.isLoading == isLoading &&
        other.isSuccess == isSuccess &&
        other.error == error &&
        other.successMessage == successMessage &&
        other.validationError == validationError;
  }

  @override
  int get hashCode =>
      isLoading.hashCode ^
      isSuccess.hashCode ^
      error.hashCode ^
      successMessage.hashCode ^
      validationError.hashCode;

  @override
  String toString() =>
      'ChangePasswordState(isLoading: $isLoading, isSuccess: $isSuccess, error: $error, successMessage: $successMessage, validationError: $validationError)';
}
