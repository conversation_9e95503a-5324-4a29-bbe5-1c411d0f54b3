/// Form state for authentication forms
class FormState {
  final String email;
  final String password;
  final String confirmPassword;
  final String pin;
  final bool obscurePassword;
  final bool obscureConfirmPassword;
  final bool rememberMe;
  final bool isLoading;
  final String? emailError;
  final String? passwordError;
  final String? confirmPasswordError;
  final String? pinError;
  final String? generalError;

  FormState({
    this.email = '',
    this.password = '',
    this.confirmPassword = '',
    this.pin = '',
    this.obscurePassword = true,
    this.obscureConfirmPassword = true,
    this.rememberMe = false,
    this.isLoading = false,
    this.emailError,
    this.passwordError,
    this.confirmPasswordError,
    this.pinError,
    this.generalError,
  });

  /// Create a copy with updated fields
  FormState copyWith({
    String? email,
    String? password,
    String? confirmPassword,
    String? pin,
    bool? obscurePassword,
    bool? obscureConfirmPassword,
    bool? rememberMe,
    bool? isLoading,
    String? emailError,
    String? passwordError,
    String? confirmPasswordError,
    String? pinError,
    String? generalError,
  }) {
    return FormState(
      email: email ?? this.email,
      password: password ?? this.password,
      confirmPassword: confirmPassword ?? this.confirmPassword,
      pin: pin ?? this.pin,
      obscurePassword: obscurePassword ?? this.obscurePassword,
      obscureConfirmPassword: obscureConfirmPassword ?? this.obscureConfirmPassword,
      rememberMe: rememberMe ?? this.rememberMe,
      isLoading: isLoading ?? this.isLoading,
      emailError: emailError ?? this.emailError,
      passwordError: passwordError ?? this.passwordError,
      confirmPasswordError: confirmPasswordError ?? this.confirmPasswordError,
      pinError: pinError ?? this.pinError,
      generalError: generalError ?? this.generalError,
    );
  }

  /// Check if the form has any errors
  bool get hasErrors => 
      emailError != null || 
      passwordError != null || 
      confirmPasswordError != null || 
      pinError != null || 
      generalError != null;

  /// Check if the form is valid for login
  bool get isValidForLogin => 
      email.isNotEmpty && 
      password.isNotEmpty && 
      !hasErrors;

  /// Check if the form is valid for PIN verification
  bool get isValidForPin => 
      pin.isNotEmpty && 
      pin.length == 6 && 
      !hasErrors;
}
