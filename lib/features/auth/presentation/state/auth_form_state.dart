class FormState {
  final String email;
  final String password;
  final String pin;
  final String? emailError;
  final String? passwordError;
  final String? pinError;
  final bool obscurePassword;
  final bool rememberMe;
  final bool isValid;

  FormState({
    this.email = '',
    this.password = '',
    this.pin = '',
    this.emailError,
    this.passwordError,
    this.pinError,
    this.obscurePassword = true,
    this.rememberMe = false,
    this.isValid = false,
  });

  FormState copyWith({
    String? email,
    String? password,
    String? pin,
    String? emailError,
    String? passwordError,
    String? pinError,
    bool? obscurePassword,
    bool? rememberMe,
    bool? isValid,
  }) {
    return FormState(
      email: email ?? this.email,
      password: password ?? this.password,
      pin: pin ?? this.pin,
      emailError: emailError ?? this.emailError,
      passwordError: passwordError ?? this.passwordError,
      pinError: pinError ?? this.pinError,
      obscurePassword: obscurePassword ?? this.obscurePassword,
      rememberMe: rememberMe ?? this.rememberMe,
      isValid: isValid ?? this.isValid,
    );
  }
}
