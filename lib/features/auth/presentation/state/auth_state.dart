import 'package:SolidCheck/features/auth/domain/entities/auth_result_entity.dart';
import 'package:SolidCheck/features/auth/domain/failures/auth_failure.dart';

enum AuthStatus { initial, loading, authenticated, unauthenticated, requiresTwoFactor, error }

class AuthState {
  final AuthStatus status;
  final AuthResultEntity? authResult;
  final AuthFailure? failure;
  final String? errorMessage;

  AuthState({
    this.status = AuthStatus.initial,
    this.authResult,
    this.failure,
    this.errorMessage,
  });

  bool get isLoading => status == AuthStatus.loading;
  bool get isAuthenticated => status == AuthStatus.authenticated;
  bool get isUnauthenticated => status == AuthStatus.unauthenticated;
  bool get hasError => status == AuthStatus.error;
  bool get requiresTwoFactor => status == AuthStatus.requiresTwoFactor || authResult?.requiresTwoFactor == true;

  AuthState copyWith({
    AuthStatus? status,
    AuthResultEntity? authResult,
    AuthFailure? failure,
    String? errorMessage,
  }) {
    return AuthState(
      status: status ?? this.status,
      authResult: authResult ?? this.authResult,
      failure: failure ?? this.failure,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthState &&
        other.status == status &&
        other.authResult == authResult &&
        other.failure == failure &&
        other.errorMessage == errorMessage;
  }

  @override
  int get hashCode {
    return Object.hash(status, authResult, failure, errorMessage);
  }

  @override
  String toString() {
    return 'AuthState(status: $status, authResult: $authResult, failure: $failure, errorMessage: $errorMessage)';
  }
}
