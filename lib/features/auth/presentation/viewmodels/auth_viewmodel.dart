import 'dart:developer' as developer;

import 'package:SolidCheck/features/auth/domain/entities/auth_result_entity.dart';
import 'package:SolidCheck/features/auth/domain/usecases/check_auth_status_usecase.dart';
import 'package:SolidCheck/features/auth/domain/usecases/login_usecase.dart';
import 'package:SolidCheck/features/auth/domain/usecases/logout_usecase.dart';
import 'package:SolidCheck/features/auth/domain/usecases/verify_two_factor_usecase.dart';
import 'package:SolidCheck/features/auth/presentation/state/auth_state.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AuthViewModel extends StateNotifier<AuthState> {
  final LoginUseCase _loginUseCase;
  final VerifyTwoFactorUseCase _verifyTwoFactorUseCase;
  final LogoutUseCase _logoutUseCase;
  final CheckAuthStatusUseCase _checkAuthStatusUseCase;

  AuthViewModel(
    this._loginUseCase,
    this._verifyTwoFactorUseCase,
    this._logoutUseCase,
    this._checkAuthStatusUseCase,
  ) : super(AuthState()) {
    _checkInitialAuthStatus();
  }

  Future<void> login({required String email, required String password}) async {
    if (state.isLoading) return;

    state = state.copyWith(
      status: AuthStatus.loading,
      failure: null,
      errorMessage: null,
    );

    final result = await _loginUseCase.call(email: email, password: password);

    await result.fold(
      (failure) async {
        developer.log(
          'Login failed with error: ${failure.message}',
          name: 'AuthViewModel',
        );
        state = state.copyWith(
          status: AuthStatus.error,
          failure: failure,
          errorMessage: _getErrorMessage(failure),
        );
      },
      (authResult) async {
        if (authResult.requiresTwoFactor) {
          await _storeQrCodeData(authResult, email, password);

          state = state.copyWith(
            status: AuthStatus.requiresTwoFactor,
            authResult: authResult,
            failure: null,
            errorMessage: null,
          );
        } else if (authResult.isAuthenticated) {
          state = state.copyWith(
            status: AuthStatus.authenticated,
            authResult: authResult,
            failure: null,
            errorMessage: null,
          );
        } else {
          state = state.copyWith(
            status: AuthStatus.error,
            errorMessage:
                authResult.message ??
                'Login failed. Please check your credentials.',
          );
        }
      },
    );
  }

  Future<void> verifyTwoFactor({
    required String email,
    required String password,
    required String pin,
  }) async {
    if (state.isLoading) return;

    state = state.copyWith(status: AuthStatus.loading);

    final result = await _verifyTwoFactorUseCase.call(
      email: email,
      password: password,
      pin: pin,
    );

    result.fold(
      (failure) => state = state.copyWith(
        status: AuthStatus.error,
        failure: failure,
        errorMessage: failure.message,
      ),
      (authResult) {
        if (authResult.isAuthenticated) {
          state = state.copyWith(
            status: AuthStatus.authenticated,
            authResult: authResult,
          );
        } else {
          state = state.copyWith(
            status: AuthStatus.error,
            errorMessage: authResult.message ?? '2FA verification failed',
          );
        }
      },
    );
  }

  Future<void> logout() async {
    if (state.isLoading) return;

    state = state.copyWith(status: AuthStatus.loading);

    final result = await _logoutUseCase.call();

    result.fold(
      (failure) => state = state.copyWith(
        status: AuthStatus.error,
        failure: failure,
        errorMessage: failure.message,
      ),
      (_) {
        state = state.copyWith(
          status: AuthStatus.unauthenticated,
          authResult: null,
          failure: null,
          errorMessage: null,
        );
      },
    );
  }

  Future<void> checkAuthStatus() async {
    final isAuthenticated = await _checkAuthStatusUseCase.isAuthenticated();

    if (isAuthenticated) {
      final userResult = await _checkAuthStatusUseCase.getCurrentUser();
      userResult.fold(
        (failure) => state = state.copyWith(
          status: AuthStatus.unauthenticated,
          failure: failure,
        ),
        (authResult) => state = state.copyWith(
          status: AuthStatus.authenticated,
          authResult: authResult,
        ),
      );
    } else {
      state = state.copyWith(status: AuthStatus.unauthenticated);
    }
  }

  void clearError() {
    state = state.copyWith(
      status: state.isAuthenticated
          ? AuthStatus.authenticated
          : AuthStatus.unauthenticated,
      failure: null,
      errorMessage: null,
    );
  }

  void resetToLogin() {
    state = state.copyWith(
      status: AuthStatus.unauthenticated,
      authResult: null,
      failure: null,
      errorMessage: null,
    );
  }

  Future<void> _checkInitialAuthStatus() async {
    await checkAuthStatus();
  }

  Future<void> _storeQrCodeData(
    AuthResultEntity authResult,
    String email,
    String password,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.setString('tempEmail', email);
      await prefs.setString('tempPassword', password);

      if (authResult.qrCodeUrl != null && authResult.qrCodeUrl!.isNotEmpty) {
        await prefs.setString('qrCodeUrl', authResult.qrCodeUrl!);
      }

      if (authResult.qrCodeImage != null &&
          authResult.qrCodeImage!.isNotEmpty) {
        await prefs.setString('qrCodeImage', authResult.qrCodeImage!);
      }

      if (authResult.tempSecret != null && authResult.tempSecret!.isNotEmpty) {
        await prefs.setString('tempSecret', authResult.tempSecret!);
      }

      if (authResult.requiresTwoFactorSetup == true) {
        await prefs.setBool('requiresTwoFactorSetup', true);
      }
    } catch (e) {
      // Ignore errors during preference storage
    }
  }

  String _getErrorMessage(dynamic failure) {
    if (failure.toString().contains('network') ||
        failure.toString().contains('connection')) {
      return 'Network error. Please check your internet connection.';
    } else if (failure.toString().contains('401') ||
        failure.toString().contains('unauthorized')) {
      return 'Invalid email or password. Please try again.';
    } else if (failure.toString().contains('server') ||
        failure.toString().contains('500')) {
      return 'Server error. Please try again later.';
    } else {
      return failure.message ??
          'An unexpected error occurred. Please try again.';
    }
  }
}
