import 'package:SolidCheck/features/auth/data/models/validation_error.dart';
import 'package:SolidCheck/features/auth/domain/failures/auth_failure.dart';
import 'package:SolidCheck/features/auth/domain/usecases/change_password_usecase.dart';
import 'package:SolidCheck/features/auth/presentation/state/change_password_state.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ChangePasswordViewModel extends StateNotifier<ChangePasswordState> {
  final ChangePasswordUseCase _changePasswordUseCase;

  ChangePasswordViewModel(this._changePasswordUseCase) : super(ChangePasswordState());

  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
    required String newPasswordConfirmation,
  }) async {
    state = state.copyWith(
      isLoading: true,
      error: null,
      validationError: null,
    );

    final result = await _changePasswordUseCase.call(
      currentPassword: currentPassword,
      newPassword: newPassword,
      newPasswordConfirmation: newPasswordConfirmation,
    );

    result.fold(
      (failure) {
        if (failure is ValidationFailure && failure.hasAnyFieldErrors) {
          // For validation failures with field errors, create a ValidationErrorModel
          final validationError = ValidationErrorModel(
            message: failure.message,
            errors: failure.fieldErrors!,
          );
          state = state.copyWith(
            isLoading: false,
            error: failure.message,
            validationError: validationError,
          );
        } else {
          // For other failures, show the general error message
          state = state.copyWith(
            isLoading: false,
            error: failure.message,
          );
        }
      },
      (message) => state = state.copyWith(
        isLoading: false,
        isSuccess: true,
        successMessage: message,
      ),
    );
  }

  void clearState() {
    state = ChangePasswordState();
  }

  void clearError() {
    state = state.copyWith(error: null, validationError: null);
  }

  /// Test method to set validation errors (for testing purposes)
  void setValidationErrors(ValidationErrorModel validationError) {
    state = state.copyWith(
      isLoading: false,
      validationError: validationError,
      error: validationError.message,
    );
  }
}
