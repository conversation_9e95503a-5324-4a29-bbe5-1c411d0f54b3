import 'package:SolidCheck/core/utils/validators.dart';
import 'package:SolidCheck/features/auth/presentation/state/auth_form_state.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class FormViewModel extends StateNotifier<FormState> {
  final Validators _validators;

  FormViewModel(this._validators) : super(FormState());

  void updateEmail(String email) {
    final emailError = _validateEmail(email);
    state = state.copyWith(
      email: email,
      emailError: emailError,
      isValid: _isFormValid(email, state.password, emailError, state.passwordError),
    );
  }

  void updatePassword(String password) {
    final passwordError = _validatePassword(password);
    state = state.copyWith(
      password: password,
      passwordError: passwordError,
      isValid: _isFormValid(state.email, password, state.emailError, passwordError),
    );
  }

  void updatePin(String pin) {
    final pinError = _validatePin(pin);
    state = state.copyWith(
      pin: pin,
      pinError: pinError,
    );
  }

  void togglePasswordVisibility() {
    state = state.copyWith(obscurePassword: !state.obscurePassword);
  }

  void toggleRememberMe() {
    state = state.copyWith(rememberMe: !state.rememberMe);
  }

  void clearForm() {
    state = FormState();
  }

  void clearErrors() {
    state = state.copyWith(
      emailError: null,
      passwordError: null,
      pinError: null,
    );
  }

  String? _validateEmail(String email) {
    if (email.isEmpty) return 'Email is required';
    if (!_validators.isValidEmail(email)) return 'Please enter a valid email';
    return null;
  }

  String? _validatePassword(String password) {
    if (password.isEmpty) return 'Password is required';
    if (!_validators.hasMinLength(password, 6)) return 'Password must be at least 6 characters';
    return null;
  }

  String? _validatePin(String pin) {
    if (pin.isEmpty) return 'PIN is required';
    if (!_validators.isValidPin(pin)) return 'PIN must be 6 digits';
    return null;
  }

  bool _isFormValid(String email, String password, String? emailError, String? passwordError) {
    return email.isNotEmpty &&
           password.isNotEmpty &&
           emailError == null &&
           passwordError == null;
  }
}
