import 'package:SolidCheck/features/auth/presentation/widgets/enhanced_auth_layout.dart';
import 'package:SolidCheck/features/auth/presentation/widgets/login_content.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Enhanced Login screen with beautiful QR code design elements
/// Features white transparent bubbles, blue gradient, and infinity symbols
class LoginScreen extends ConsumerWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return const EnhancedAuthLayout(
      title: 'Welcome Back',
      child: LoginContent(),
    );
  }
}
