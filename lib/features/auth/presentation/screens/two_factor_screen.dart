import 'package:SolidCheck/features/auth/presentation/widgets/auth_layout.dart';
import 'package:SolidCheck/features/auth/presentation/widgets/two_factor_content.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Clean 2FA verification screen following SOLID principles
/// Uses reusable components for consistent design across mobile and desktop
class TwoFactorScreen extends ConsumerWidget {
  const TwoFactorScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return const AuthLayout(
      title: "Two-Factor Authentication",
      child: TwoFactorContent(),
    );
  }
}
