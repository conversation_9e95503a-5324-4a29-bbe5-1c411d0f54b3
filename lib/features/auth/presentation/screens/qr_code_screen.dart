import 'package:SolidCheck/core/constants/constants.dart';
import 'package:SolidCheck/core/navigation/app_router_new.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/features/auth/di/auth_providers.dart';
import 'package:SolidCheck/features/auth/presentation/widgets/qr_code_content.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Beautiful QR Code setup screen matching SolidCheck branding
class QrCodeScreen extends ConsumerWidget {
  const QrCodeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final bool isMobile = context.isMobile;

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFF8F9FA), // Light gray
              Color(0xFFE3F2FD), // Very light blue
            ],
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: isMobile ? _buildMobileLayout(context, ref) : _buildDesktopLayout(context, ref),
        ),
      ),
    );
  }

  /// Enhanced mobile layout with beautiful design
  Widget _buildMobileLayout(BuildContext context, WidgetRef ref) {
    return Stack(
      children: [
        // Background bubbles and geometric elements
        _buildBackgroundElements(context),
        // Content
        Column(
          children: [
            // Custom header with close button
            _buildCustomHeader(context, ref),
            const SizedBox(height: 20),
            // Content card with enhanced design
            Expanded(
              child: Center(
                child: Container(
                  constraints: const BoxConstraints(maxWidth: 400),
                  margin: const EdgeInsets.all(20),
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.95),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        offset: const Offset(0, 8),
                        blurRadius: 30,
                      ),
                    ],
                  ),
                  child: const SingleChildScrollView(
                    child: QrCodeContent(),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Enhanced desktop layout matching SolidCheck branding
  Widget _buildDesktopLayout(BuildContext context, WidgetRef ref) {
    return Row(
      children: [
        // Left side - SolidCheck branding with infinity elements
        Expanded(
          flex: 1,
          child: Container(
            padding: const EdgeInsets.all(60),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // SolidCheck logo with infinity symbol
                _buildLogoWithInfinity(),
                const SizedBox(height: 30),
                const Text(
                  'INTEGRITY ASSURED',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF666666),
                    letterSpacing: 1.2,
                  ),
                ),
                const SizedBox(height: 20),
                // Infinity arrows decoration
                _buildInfinityArrows(),
              ],
            ),
          ),
        ),
        // Right side - Blue gradient with enhanced design
        Expanded(
          flex: 1,
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.kBlueColor,
                  AppColors.kBlueDarkColor,
                ],
              ),
            ),
            child: Stack(
              children: [
                // Enhanced geometric shapes and bubbles
                _buildEnhancedGeometricShapes(),
                // Content modal
                Center(
                  child: Container(
                    constraints: const BoxConstraints(maxWidth: 420),
                    margin: const EdgeInsets.all(40),
                    padding: const EdgeInsets.all(32),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.98),
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.2),
                          offset: const Offset(0, 12),
                          blurRadius: 40,
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Modal header with close button
                        _buildModalHeader(context, ref),
                        const SizedBox(height: 24),
                        // QR Code content - scrollable
                        const Flexible(
                          child: SingleChildScrollView(
                            child: QrCodeContent(),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Background elements with white transparent bubbles
  Widget _buildBackgroundElements(BuildContext context) {
    return Positioned.fill(
      child: Stack(
        children: [
          // Large floating bubble - top right
          Positioned(
            top: 50,
            right: 30,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(60),
              ),
            ),
          ),
          // Medium bubble - middle left
          Positioned(
            top: 200,
            left: 20,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.08),
                borderRadius: BorderRadius.circular(40),
              ),
            ),
          ),
          // Small bubble - bottom right
          Positioned(
            bottom: 100,
            right: 50,
            child: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.12),
                borderRadius: BorderRadius.circular(25),
              ),
            ),
          ),
          // Tiny accent bubbles
          Positioned(
            top: 150,
            right: 80,
            child: Container(
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                color: AppColors.kBlueColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(15),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Custom header for mobile with enhanced styling
  Widget _buildCustomHeader(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Row(
        children: [
          // Close button with enhanced styling
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  offset: const Offset(0, 2),
                  blurRadius: 8,
                ),
              ],
            ),
            child: IconButton(
              icon: const Icon(Icons.close, color: Color(0xFF666666)),
              onPressed: () => _handleBackToLogin(context, ref),
            ),
          ),
          const SizedBox(width: 16),
          // Title with infinity symbol
          Expanded(
            child: Row(
              children: [
                Text(
                  'Setup 2FA',
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.w600,
                    color: AppColors.kBlueColor,
                  ),
                ),
                const SizedBox(width: 8),
                // Infinity symbol
                Icon(
                  Icons.all_inclusive,
                  color: AppColors.kBlueColor.withValues(alpha: 0.7),
                  size: 20,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// SolidCheck logo with infinity symbol integration
  Widget _buildLogoWithInfinity() {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Main logo
        Image.asset(
          AppConstants.solidCheckLoginLogo,
          height: 120,
          fit: BoxFit.contain,
        ),
        // Infinity symbol overlay (subtle)
        Positioned(
          bottom: -5,
          right: -5,
          child: Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.all_inclusive,
              color: AppColors.kBlueColor.withValues(alpha: 0.8),
              size: 16,
            ),
          ),
        ),
      ],
    );
  }

  /// Infinity arrows decoration for branding
  Widget _buildInfinityArrows() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Left arrow
        Icon(
          Icons.arrow_back_ios,
          color: AppColors.kBlueColor.withValues(alpha: 0.6),
          size: 16,
        ),
        const SizedBox(width: 8),
        // Infinity symbol
        Icon(
          Icons.all_inclusive,
          color: AppColors.kBlueColor.withValues(alpha: 0.8),
          size: 24,
        ),
        const SizedBox(width: 8),
        // Right arrow
        Icon(
          Icons.arrow_forward_ios,
          color: AppColors.kBlueColor.withValues(alpha: 0.6),
          size: 16,
        ),
      ],
    );
  }

  /// Enhanced geometric shapes with white transparent bubbles
  Widget _buildEnhancedGeometricShapes() {
    return Positioned.fill(
      child: Stack(
        children: [
          // Large bubble - top right
          Positioned(
            top: -30,
            right: -30,
            child: Container(
              width: 180,
              height: 180,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.12),
                borderRadius: BorderRadius.circular(90),
              ),
            ),
          ),
          // Medium bubble - bottom left
          Positioned(
            bottom: -20,
            left: -20,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(60),
              ),
            ),
          ),
          // Small floating bubbles
          Positioned(
            top: 150,
            right: 40,
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(30),
              ),
            ),
          ),
          Positioned(
            top: 300,
            left: 60,
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.08),
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          ),
          // Tiny accent bubbles with infinity theme
          Positioned(
            top: 100,
            left: 100,
            child: Container(
              width: 25,
              height: 25,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12.5),
              ),
              child: Icon(
                Icons.all_inclusive,
                color: Colors.white.withValues(alpha: 0.5),
                size: 12,
              ),
            ),
          ),
          Positioned(
            bottom: 200,
            right: 100,
            child: Container(
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.18),
                borderRadius: BorderRadius.circular(15),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Modal header for desktop layout
  Widget _buildModalHeader(BuildContext context, WidgetRef ref) {
    return Row(
      children: [
        // Infinity symbol
        Icon(
          Icons.all_inclusive,
          color: AppColors.kBlueColor.withValues(alpha: 0.8),
          size: 20,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            'Setup Two-Factor Authentication',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: AppColors.kBlueColor,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFFF5F5F5),
            borderRadius: BorderRadius.circular(8),
          ),
          child: IconButton(
            icon: const Icon(Icons.close, color: Color(0xFF666666)),
            onPressed: () => _handleBackToLogin(context, ref),
          ),
        ),
      ],
    );
  }

  Future<void> _handleBackToLogin(BuildContext context, WidgetRef ref) async {
    // Clear all 2FA related preferences FIRST
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('requiresTwoFactorSetup');
    await prefs.remove('qrCodeImage');
    await prefs.remove('qrCodeUrl');
    await prefs.remove('tempSecret');
    await prefs.remove('tempEmail');
    await prefs.remove('tempPassword');
    await prefs.remove('needsPinVerification');
    await prefs.setBool('isQrCodeScan', false);
    await prefs.setBool('is2FACompleted', false);

    // Reset auth state
    final authViewModel = ref.read(authViewModelProvider.notifier);
    authViewModel.resetToLogin(); // Reset to clean login state

    if (!context.mounted) return;
    AppRouter.navigateToLogin();
  }
}
