import 'package:SolidCheck/core/navigation/app_router_new.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/auth/di/auth_providers.dart';
import 'package:SolidCheck/features/auth/presentation/services/auth_validation_service.dart';
import 'package:SolidCheck/features/auth/presentation/state/auth_state.dart';
import 'package:SolidCheck/features/auth/presentation/widgets/auth_form_field.dart';
import 'package:SolidCheck/shared/widgets/custom_snakbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class LoginContent extends ConsumerStatefulWidget {
  const LoginContent({super.key});

  @override
  ConsumerState<LoginContent> createState() => _LoginContentState();
}

class _LoginContentState extends ConsumerState<LoginContent> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authViewModelProvider);
    final obscureText = ref.watch(obscureTextProvider);

    ref.listen<AuthState>(authViewModelProvider, (previous, next) {
      if (next.status == AuthStatus.authenticated) {
        AppRouter.navigateToDashboard();
      } else if (next.status == AuthStatus.unauthenticated && next.authResult?.requiresTwoFactorSetup == true) {
        AppRouter.navigateToQrCode();
      } else if (next.status == AuthStatus.unauthenticated && next.authResult?.requiresTwoFactor == true) {
        AppRouter.navigateToTwoFactor();
      } else if (next.status == AuthStatus.error) {
        _showErrorMessage(next.errorMessage ?? 'Login failed');
      }
    });

    return SingleChildScrollView(
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [            
            const SizedBox(height: 8),
            const Text(
              "Sign in to your account",
              style: TextStyle(
                fontSize: 16,
                color: Color(0xFF666666),
                fontWeight: FontWeight.w400,
              ),
            ),
            const SizedBox(height: 40),
            
            AuthFormField(
              controller: _emailController,
              labelText: 'Email',
              hintText: 'Enter your email',
              prefixIcon: Icons.email_outlined,
              keyboardType: TextInputType.emailAddress,
              validator: AuthValidationService.validateEmail,
            ),
            const SizedBox(height: 20),

            AuthFormField(
              controller: _passwordController,
              labelText: 'Password',
              hintText: 'Enter your password',
              prefixIcon: Icons.lock_outline,
              obscureText: obscureText,
              onToggleVisibility: () {
                ref.read(obscureTextProvider.notifier).state = !obscureText;
              },
              validator: AuthValidationService.validatePassword,
            ),
            const SizedBox(height: 30),
            
            _buildLoginButton(authState),
            
            
          ],
        ),
      ),
    );
  }



  Widget _buildLoginButton(AuthState authState) {
    final isLoading = authState.status == AuthStatus.loading;

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.kBlueColor,
            AppColors.kBlueDarkColor,
          ],
        ),
        borderRadius: BorderRadius.circular(12.0),
        boxShadow: [
          BoxShadow(
            color: AppColors.kBlueColor.withValues(alpha: 0.3),
            offset: const Offset(0, 4),
            blurRadius: 12,
          ),
        ],
      ),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.0),
          ),
        ),
        onPressed: isLoading ? null : _handleLogin,
        child: isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text(
                'Sign In',
                style: TextStyle(
                  fontSize: 16.0,
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  Future<void> _handleLogin() async {
    if (_formKey.currentState!.validate()) {
      final authViewModel = ref.read(authViewModelProvider.notifier);
      await authViewModel.login(
        email: _emailController.text.trim(),
        password: _passwordController.text,
      );
    }
  }

  void _showErrorMessage(String message) {
    if (!mounted) return;
    CustomSnackBar.show(
      context: context,
      message: message,
      backgroundColor: AppColors.kRedColor,
      textColor: Colors.white,
      icon: Icons.error,
      duration: const Duration(seconds: 3),
    );
  }
}
