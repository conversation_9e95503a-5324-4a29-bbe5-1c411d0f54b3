import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/shared/widgets/logo/solid_check_logo_widget.dart';
import 'package:flutter/material.dart';

/// Enhanced auth layout with beautiful QR code screen design elements
/// Features white transparent bubbles, blue gradient, and infinity symbols
class EnhancedAuthLayout extends StatelessWidget {
  final Widget child;
  final String? title;
  final Widget? backButton;
  final bool showLogo;

  const EnhancedAuthLayout({
    super.key,
    required this.child,
    this.title,
    this.backButton,
    this.showLogo = true,
  });

  @override
  Widget build(BuildContext context) {
    final bool isDesktop = context.isDesktop;

    return Scaffold(
      body: isDesktop ? _buildDesktopLayout(context) : _buildMobileLayout(context),
    );
  }

  /// Enhanced desktop layout matching QR code screen design
  Widget _buildDesktopLayout(BuildContext context) {
    return Row(
      children: [
        // Left side - SolidCheck branding with infinity elements
        if (showLogo)
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.all(60),
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFFF8F9FA), // Light gray
                    Color(0xFFE3F2FD), // Very light blue
                  ],
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // SolidCheck logo with infinity symbol
                  _buildLogoWithInfinity(),
                ],
              ),
            ),
          ),
        // Right side - Blue gradient with enhanced design
        Expanded(
          flex: 1,
          child: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFF1E88E5), // Bright blue
                  Color(0xFF0D47A1), // Deep blue
                ],
              ),
            ),
            child: Stack(
              children: [
                // Enhanced geometric shapes and bubbles
                _buildEnhancedGeometricShapes(),
                // Content modal
                Center(
                  child: Container(
                    constraints: const BoxConstraints(maxWidth: 420),
                    margin: const EdgeInsets.all(40),
                    padding: const EdgeInsets.all(32),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.98),
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.2),
                          offset: const Offset(0, 12),
                          blurRadius: 40,
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Modal header
                        if (backButton != null || title != null)
                          _buildModalHeader(context),
                        // Content - scrollable
                        Flexible(
                          child: SingleChildScrollView(
                            child: child,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Enhanced mobile layout with beautiful design
  Widget _buildMobileLayout(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFF8F9FA), // Light gray
            Color(0xFFE3F2FD), // Very light blue
          ],
        ),
      ),
      child: SafeArea(
        child: Stack(
          children: [
            // Background bubbles and geometric elements
            _buildBackgroundElements(context),
            // Content
            Column(
              children: [
                // Custom header with logo and close button
                if (showLogo) _buildMobileHeader(context),
                const SizedBox(height: 20),
                // Content card with enhanced design
                Expanded(
                  child: Center(
                    child: Container(
                      constraints: const BoxConstraints(maxWidth: 400),
                      margin: const EdgeInsets.all(20),
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.95),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            offset: const Offset(0, 8),
                            blurRadius: 30,
                          ),
                        ],
                      ),
                      child: SingleChildScrollView(
                        child: child,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Mobile header with logo and infinity elements
  Widget _buildMobileHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      child: Row(
        children: [
          // Logo with infinity symbol
          Expanded(
            child: Center(
              child: _buildLogoWithInfinity(isMobile: true),
            ),
          ),
          // Close button if provided
          if (backButton != null) backButton!,
        ],
      ),
    );
  }

  /// Clean SolidCheck logo
  Widget _buildLogoWithInfinity({bool isMobile = false}) {
    return SolidCheckLoginLogo(
      isMobile: isMobile,
    );
  }



  /// Modal header for desktop layout
  Widget _buildModalHeader(BuildContext context) {
    return Row(
      children: [
        // Title
        Expanded(
          child: title != null
              ? Text(
                  title!,
                  style: const TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF1976D2),
                  ),
                )
              : const SizedBox.shrink(),
        ),
        // Close button
        if (backButton != null) backButton!,
      ],
    );
  }

  /// Background elements with white transparent bubbles for mobile
  Widget _buildBackgroundElements(BuildContext context) {
    return Positioned.fill(
      child: Stack(
        children: [
          // Large floating bubble - top right
          Positioned(
            top: 50,
            right: 30,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(60),
              ),
            ),
          ),
          // Medium bubble - middle left
          Positioned(
            top: 200,
            left: 20,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.08),
                borderRadius: BorderRadius.circular(40),
              ),
            ),
          ),
          // Small bubble - bottom right
          Positioned(
            bottom: 100,
            right: 50,
            child: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.12),
                borderRadius: BorderRadius.circular(25),
              ),
            ),
          ),
          // Tiny accent bubbles
          Positioned(
            top: 150,
            right: 80,
            child: Container(
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                color: const Color(0xFF1976D2).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(15),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Enhanced geometric shapes with white transparent bubbles for desktop
  Widget _buildEnhancedGeometricShapes() {
    return Positioned.fill(
      child: Stack(
        children: [
          // Large bubble - top right
          Positioned(
            top: -30,
            right: -30,
            child: Container(
              width: 180,
              height: 180,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.12),
                borderRadius: BorderRadius.circular(90),
              ),
            ),
          ),
          // Medium bubble - bottom left
          Positioned(
            bottom: -20,
            left: -20,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(60),
              ),
            ),
          ),
          // Small floating bubbles
          Positioned(
            top: 150,
            right: 40,
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(30),
              ),
            ),
          ),
          Positioned(
            top: 300,
            left: 60,
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.08),
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          ),
          // Tiny accent bubbles with infinity theme
          Positioned(
            top: 100,
            left: 100,
            child: Container(
              width: 25,
              height: 25,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12.5),
              ),
              child: Icon(
                Icons.all_inclusive,
                color: Colors.white.withValues(alpha: 0.5),
                size: 12,
              ),
            ),
          ),
          Positioned(
            bottom: 200,
            right: 100,
            child: Container(
              width: 35,
              height: 35,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(17.5),
              ),
              child: Icon(
                Icons.all_inclusive,
                color: Colors.white.withValues(alpha: 0.6),
                size: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
