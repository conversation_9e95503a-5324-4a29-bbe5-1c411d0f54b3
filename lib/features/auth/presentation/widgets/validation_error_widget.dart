import 'package:SolidCheck/core/theme/colors.dart';
import 'package:flutter/material.dart';

/// Widget to display validation errors for form fields
class ValidationErrorWidget extends StatelessWidget {
  final List<String> errors;
  final EdgeInsets? padding;

  const ValidationErrorWidget({
    super.key,
    required this.errors,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    if (errors.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: padding ?? const EdgeInsets.only(top: 5),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: AppColors.kRedShadeBoxColor,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: AppColors.kRedColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.error_outline,
                color: AppColors.kRedColor,
                size: 16,
              ),
              const SizedBox(width: 6),
              Text(
                errors.length == 1 ? 'Error:' : 'Errors:',
                style: TextStyle(
                  color: AppColors.kRedColor,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          ...errors.map((error) => Padding(
            padding: const EdgeInsets.only(left: 22, bottom: 2),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '• ',
                  style: TextStyle(
                    color: AppColors.kRedColor,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Expanded(
                  child: Text(
                    error,
                    style: TextStyle(
                      color: AppColors.kRedColor,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }
}

/// Widget to display a single validation error message
class SingleValidationErrorWidget extends StatelessWidget {
  final String error;
  final EdgeInsets? padding;

  const SingleValidationErrorWidget({
    super.key,
    required this.error,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return ValidationErrorWidget(
      errors: [error],
      padding: padding,
    );
  }
}
