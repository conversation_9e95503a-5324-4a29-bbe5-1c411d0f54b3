import 'dart:convert';

import 'package:SolidCheck/core/navigation/app_router_new.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/auth/di/auth_providers.dart';
import 'package:SolidCheck/shared/widgets/custom_snakbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

class QrCodeContent extends ConsumerStatefulWidget {
  const QrCodeContent({super.key});

  @override
  ConsumerState<QrCodeContent> createState() => _QrCodeContentState();
}

class _QrCodeContentState extends ConsumerState<QrCodeContent> {
  final List<TextEditingController> pinControllers = List.generate(
    6,
    (_) => TextEditingController(),
  );

  @override
  void dispose() {
    for (var controller in pinControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final qrCodeState = ref.watch(qrCodeProvider);
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 768;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          "Open an authenticator app and scan this QR code",
          style: TextStyle(
            fontSize: isMobile ? 12 : 14,
            color: const Color(0xFF1976D2).withValues(alpha: 0.8),
            fontWeight: FontWeight.w400,
          ),
          textAlign: TextAlign.center,
        ),

        SizedBox(height: isMobile ? 8 : 12),

        _buildQrCodeDisplay(qrCodeState),

        SizedBox(height: isMobile ? 8 : 12),

        _buildPinFields(),

        SizedBox(height: isMobile ? 12 : 16),

        _buildActionButtons(),

        SizedBox(height: isMobile ? 8 : 12),
      ],
    );
  }

  Widget _buildQrCodeDisplay(AsyncValue<String> qrCodeState) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = MediaQuery.of(context).size.width;
        final isMobile = screenWidth < 768;

        final qrSize = isMobile
            ? (screenWidth * 0.3).clamp(100.0, 140.0)
            : 180.0;

        return Container(
          width: qrSize,
          height: qrSize,
          decoration: BoxDecoration(
            color: AppColors.kWhiteColor,
            borderRadius: BorderRadius.circular(15.0),
            border: Border.all(
              color: const Color(0xFF1976D2).withValues(alpha: 0.2),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.kBlackColor.withValues(alpha: 0.1),
                offset: const Offset(0, 4),
                blurRadius: 12.0,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(13.0),
            child: qrCodeState.when(
              data: (qrCodeData) {
                return _buildQrCodeImage(qrCodeData, qrSize - 20);
              },
              loading: () {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(color: Color(0xFF1976D2)),
                      SizedBox(height: 10),
                      Text(
                        'Loading QR Code...',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF1976D2),
                        ),
                      ),
                    ],
                  ),
                );
              },
              error: (error, stackTrace) {
                return _buildErrorDisplay(error.toString(), qrSize - 20);
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildQrCodeImage(String qrCodeData, double size) {
    try {
      if (qrCodeData.startsWith('http://') ||
          qrCodeData.startsWith('https://')) {
        return Image.network(
          qrCodeData,
          width: size,
          height: size,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return const Center(child: CircularProgressIndicator());
          },
          errorBuilder: (context, error, stackTrace) =>
              _buildErrorDisplay('Network Error', size),
        );
      } else if (qrCodeData.startsWith('data:image/') &&
          qrCodeData.contains('base64,')) {
        final base64Data = qrCodeData.split(',')[1];
        final bytes = base64Decode(base64Data);
        return Image.memory(bytes, width: size, height: size);
      } else {
        return _buildErrorDisplay('Unsupported QR format', size);
      }
    } catch (e) {
      return _buildErrorDisplay('QR Code Error', size);
    }
  }

  Widget _buildErrorDisplay(String message, double size) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.orange),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.warning, color: Colors.orange, size: size * 0.2),
            const SizedBox(height: 8),
            Text(
              message,
              style: TextStyle(color: Colors.orange, fontSize: size * 0.06),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPinFields() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 15.0),
      child: LayoutBuilder(
        builder: (context, constraints) {
          final screenWidth = MediaQuery.of(context).size.width;
          final isMobile = screenWidth < 768;

          final availableWidth = constraints.maxWidth - 20;
          final spacing = isMobile ? 4.0 : 6.0;
          final totalSpacing = spacing * 5;
          final fieldWidth = ((availableWidth - totalSpacing) / 6).clamp(
            25.0,
            45.0,
          );

          final fontSize = isMobile ? 12.0 : 14.0;

          return Column(
            children: [
              Wrap(
                alignment: WrapAlignment.center,
                spacing: spacing,
                children: List.generate(6, (index) {
                  return SizedBox(
                    width: fieldWidth,
                    child: _buildPinField(index, fontSize),
                  );
                }),
              ),
              const SizedBox(height: 10),
              Text(
                'Enter the 6-digit code from your authenticator app',
                style: TextStyle(
                  fontSize: isMobile ? 10 : 11,
                  color: const Color(0xFF1976D2).withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildPinField(int index, double fontSize) {
    return TextField(
      controller: pinControllers[index],
      onChanged: (value) {
        if (value.isNotEmpty && index < 5) {
          FocusScope.of(context).nextFocus();
        } else if (value.isEmpty && index > 0) {
          FocusScope.of(context).previousFocus();
        }
      },
      maxLength: 1,
      keyboardType: TextInputType.number,
      textAlign: TextAlign.center,
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      style: TextStyle(
        fontSize: fontSize,
        fontWeight: FontWeight.bold,
        color: const Color(0xFF1976D2),
      ),
      decoration: InputDecoration(
        counterText: "",
        contentPadding: const EdgeInsets.symmetric(vertical: 8),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10.0),
          borderSide: BorderSide(
            color: const Color(0xFF1976D2).withValues(alpha: 0.3),
            width: 1.5,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10.0),
          borderSide: BorderSide(
            color: const Color(0xFF1976D2).withValues(alpha: 0.3),
            width: 1.5,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10.0),
          borderSide: const BorderSide(color: Color(0xFF1976D2), width: 2.0),
        ),
        filled: true,
        fillColor: AppColors.kWhiteColor,
      ),
    );
  }

  Widget _buildActionButtons() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 768;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: isMobile ? 10.0 : 15.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: isMobile ? 120 : 160,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: isMobile ? 10 : 12),
                backgroundColor: const Color(0xFF1976D2),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
                elevation: 2,
              ),
              onPressed: _verifyPin,
              child: Text(
                isMobile ? 'Login' : 'Verify & Login',
                style: TextStyle(
                  fontSize: isMobile ? 12.0 : 14.0,
                  color: AppColors.kWhiteColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _verifyPin() async {
    final pin = pinControllers.map((controller) => controller.text).join();

    if (pin.length != 6) {
      if (!context.mounted) return;
      CustomSnackBar.show(
        context: context,
        message: 'Enter a valid 6-digit PIN.',
        backgroundColor: AppColors.kRedColor,
        textColor: Colors.white,
        icon: Icons.info,
        duration: const Duration(seconds: 3),
      );
      return;
    }

    try {
      final response = await ref.read(
        qrCodePinVerificationProvider(pin).future,
      );

      if (!context.mounted) return;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('isQrCodeScan', true);
      await prefs.setBool('is2FACompleted', true);
      await prefs.remove('requiresTwoFactorSetup');
      await prefs.remove('qrCodeImage');
      await prefs.remove('qrCodeUrl');
      await prefs.remove('tempSecret');
      await prefs.remove('tempEmail');
      await prefs.remove('tempPassword');
      await prefs.remove('needsPinVerification');

      CustomSnackBar.show(
        context: context,
        message: response,
        backgroundColor: AppColors.kBlueColor,
        textColor: Colors.white,
        icon: Icons.check,
        duration: const Duration(seconds: 3),
      );

      if (!context.mounted) return;
      AppRouter.navigateToDashboard();
    } catch (error) {
      if (!context.mounted) return;
      CustomSnackBar.show(
        context: context,
        message: 'Error: ${error.toString()}',
        backgroundColor: AppColors.kRedColor,
        textColor: Colors.white,
        icon: Icons.info,
        duration: const Duration(seconds: 3),
      );
    }
  }
}
