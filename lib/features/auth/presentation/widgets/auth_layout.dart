import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/features/auth/presentation/widgets/custom_clipper_login.dart';
import 'package:SolidCheck/shared/widgets/logo/solid_check_logo_widget.dart';
import 'package:flutter/material.dart';

/// Reusable auth layout that provides consistent design for all auth screens
/// Supports both mobile and desktop layouts with the same visual structure
class AuthLayout extends StatelessWidget {
  final Widget child;
  final String? title;
  final Widget? backButton;
  final bool showLogo;

  const AuthLayout({
    super.key,
    required this.child,
    this.title,
    this.backButton,
    this.showLogo = true,
  });

  @override
  Widget build(BuildContext context) {
    final bool isDesktop = context.isDesktop;
    final Size size = MediaQuery.of(context).size;

    return Scaffold(
      body: isDesktop ? _buildDesktopLayout(size) : _buildMobileLayout(),
    );
  }

  Widget _buildDesktopLayout(Size size) {
    return Row(
      children: [
        // Left side - Logo
        if (showLogo)
          Expanded(
            child: Center(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  // Responsive logo sizing for desktop
                  final maxHeight = constraints.maxHeight * 0.4; // 40% of available height
                  final maxWidth = constraints.maxWidth * 0.8;   // 80% of available width

                  return SolidCheckLoginLogo(
                    height: maxHeight.clamp(200.0, 300.0), // Min 200, Max 300
                    width: maxWidth.clamp(300.0, 400.0),   // Min 300, Max 400
                  );
                },
              ),
            ),
          ),
        // Right side - Content
        Expanded(
          child: ClipPath(
            clipper: LogInCustomClipPath(),
            child: Container(
              height: size.height,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: AppColors.kMobLoginBoxGradientColor,
                ),
              ),
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.only(left: 50.0),
                  child: Container(
                    height: 482,
                    width: 466,
                    decoration: BoxDecoration(
                      color: AppColors.kWhiteColor.withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(10.0),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.kBlackColor.withValues(alpha: 0.2),
                          offset: const Offset(2, 4),
                          blurRadius: 20.0,
                        ),
                      ],
                    ),
                    child: _buildContent(),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMobileLayout() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: AppColors.kMobLoginBoxGradientColor,
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // Logo section
            if (showLogo)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 40.0),
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // Responsive logo sizing for mobile
                    final screenWidth = MediaQuery.of(context).size.width;
                    final logoWidth = (screenWidth * 0.5).clamp(150.0, 200.0); // 50% of screen width, min 150, max 200
                    final logoHeight = (logoWidth * 0.67).clamp(100.0, 140.0); // Maintain aspect ratio

                    return SolidCheckLoginLogo(
                      height: logoHeight,
                      width: logoWidth,
                      isMobile: true,
                    );
                  },
                ),
              ),
            // Content section
            Expanded(
              child: Container(
                margin: const EdgeInsets.all(20.0),
                decoration: BoxDecoration(
                  color: AppColors.kWhiteColor.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(15.0),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.kBlackColor.withValues(alpha: 0.1),
                      offset: const Offset(0, 4),
                      blurRadius: 15.0,
                    ),
                  ],
                ),
                child: _buildContent(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        children: [
          // Header with back button and title
          if (backButton != null || title != null)
            _buildHeader(),
          // Main content
          Expanded(
            child: child,
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // Back button row
        if (backButton != null)
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const SizedBox(width: 48), // Spacer for centering
              if (title != null)
                Text(
                  title!,
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w600,
                    color: AppColors.kAppBarWhiteColorAndFieldActiveStateColor,
                  ),
                ),
              backButton!,
            ],
          ),
        // Title only (if no back button)
        if (backButton == null && title != null)
          Text(
            title!,
            style: TextStyle(
              fontSize: 33,
              fontWeight: FontWeight.w600,
              color: AppColors.kAppBarWhiteColorAndFieldActiveStateColor,
            ),
            textAlign: TextAlign.center,
          ),
        const SizedBox(height: 20),
      ],
    );
  }
}
