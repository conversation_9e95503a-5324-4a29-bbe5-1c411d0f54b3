import 'package:SolidCheck/core/navigation/app_router_new.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/auth/di/auth_providers.dart';
import 'package:SolidCheck/shared/widgets/custom_snakbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Reusable 2FA content component that handles PIN entry for existing 2FA users
/// Can be used in both mobile and desktop layouts
class TwoFactorContent extends ConsumerStatefulWidget {
  const TwoFactorContent({super.key});

  @override
  ConsumerState<TwoFactorContent> createState() => _TwoFactorContentState();
}

class _TwoFactorContentState extends ConsumerState<TwoFactorContent> {
  final List<TextEditingController> pinControllers =
      List.generate(6, (_) => TextEditingController());

  @override
  void dispose() {
    for (var controller in pinControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Instructions
          Text(
            "Enter the 6-digit code from your authenticator app",
            style: TextStyle(
              fontSize: 16,
              color: AppColors.kAppBarWhiteColorAndFieldActiveStateColor,
              fontWeight: FontWeight.w400,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 40),
          
          // PIN Entry Fields
          _buildPinFields(),
          
          const SizedBox(height: 40),
          
          // Verify Button
          _buildVerifyButton(),
          
          const SizedBox(height: 20),
          
          // Back to login link
          _buildBackToLoginLink(),
        ],
      ),
    );
  }

  Widget _buildPinFields() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(6, (index) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4.0),
          child: SizedBox(
            width: 45,
            child: TextField(
              controller: pinControllers[index],
              onChanged: (value) {
                if (value.isNotEmpty && index < 5) {
                  FocusScope.of(context).nextFocus();
                } else if (value.isEmpty && index > 0) {
                  FocusScope.of(context).previousFocus();
                }
              },
              maxLength: 1,
              keyboardType: TextInputType.number,
              textAlign: TextAlign.center,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              decoration: InputDecoration(
                counterText: "",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.0),
                  borderSide: BorderSide(
                    color: AppColors.kBlueColor,
                    width: 2.0,
                  ),
                ),
                filled: true,
                fillColor: AppColors.kFillColor,
              ),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildVerifyButton() {
    return SizedBox(
      width: double.infinity,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            backgroundColor: AppColors.kBlueColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.0),
            ),
          ),
          onPressed: _verifyPin,
          child: Text(
            'Verify Code',
            style: TextStyle(
              fontSize: 16.0,
              color: AppColors.kWhiteColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBackToLoginLink() {
    return TextButton(
      onPressed: () async {
        // Clear 2FA state and go back to login
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove('tempEmail');
        await prefs.remove('tempPassword');
        await prefs.remove('needsPinVerification');
        
        final authViewModel = ref.read(authViewModelProvider.notifier);
        await authViewModel.logout();
        
        if (!context.mounted) return;
        AppRouter.navigateToLogin();
      },
      child: Text(
        'Back to Login',
        style: TextStyle(
          color: AppColors.kAppBarWhiteColorAndFieldActiveStateColor,
          fontSize: 14,
          decoration: TextDecoration.underline,
        ),
      ),
    );
  }

  Future<void> _verifyPin() async {
    final pin = pinControllers.map((controller) => controller.text).join();

    if (pin.length != 6) {
      if (!context.mounted) return;
      CustomSnackBar.show(
        context: context,
        message: 'Enter a valid 6-digit code.',
        backgroundColor: AppColors.kRedColor,
        textColor: Colors.white,
        icon: Icons.info,
        duration: const Duration(seconds: 3),
      );
      return;
    }

    try {
      final prefs = await SharedPreferences.getInstance();
      final email = prefs.getString('tempEmail');
      final password = prefs.getString('tempPassword');

      if (email == null || password == null) {
        throw Exception('Login credentials are missing. Please login again.');
      }

      final authViewModel = ref.read(authViewModelProvider.notifier);
      await authViewModel.verifyTwoFactor(
        email: email,
        password: password,
        pin: pin,
      );

      if (!context.mounted) return;

      // Clear temporary credentials
      await prefs.remove('tempEmail');
      await prefs.remove('tempPassword');
      await prefs.remove('needsPinVerification');

      CustomSnackBar.show(
        context: context,
        message: 'Two-factor authentication successful',
        backgroundColor: AppColors.kBlueColor,
        textColor: Colors.white,
        icon: Icons.check,
        duration: const Duration(seconds: 3),
      );

      // Navigate to home
      AppRouter.navigateToDashboard();
    } catch (error) {
      if (!context.mounted) return;
      CustomSnackBar.show(
        context: context,
        message: 'Error: ${error.toString()}',
        backgroundColor: AppColors.kRedColor,
        textColor: Colors.white,
        icon: Icons.info,
        duration: const Duration(seconds: 3),
      );
    }
  }
}
