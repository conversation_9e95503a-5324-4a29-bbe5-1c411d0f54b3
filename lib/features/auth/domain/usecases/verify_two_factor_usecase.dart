import 'package:SolidCheck/core/utils/either.dart';
import 'package:SolidCheck/core/utils/validators.dart';
import 'package:SolidCheck/features/auth/domain/entities/auth_result_entity.dart';
import 'package:SolidCheck/features/auth/domain/failures/auth_failure.dart';
import 'package:SolidCheck/features/auth/domain/repositories/auth_repository.dart';

class VerifyTwoFactorUseCase {
  final AuthRepository _authRepository;
  final Validators _validators;

  VerifyTwoFactorUseCase(this._authRepository, this._validators);

  Future<Either<AuthFailure, AuthResultEntity>> call({
    required String email,
    required String password,
    required String pin,
  }) async {
    final validationResult = _validateInput(email, password, pin);
    if (validationResult != null) return Left(validationResult);

    return await _authRepository.verifyTwoFactorPin(
      email: email,
      password: password,
      pin: pin,
    );
  }

  ValidationFailure? _validateInput(String email, String password, String pin) {
    if (email.isEmpty) return ValidationFailure('Email cannot be empty');
    if (password.isEmpty) return ValidationFailure('Password cannot be empty');
    if (pin.isEmpty) return ValidationFailure('PIN cannot be empty');
    if (!_validators.isValidEmail(email)) {
      return ValidationFailure('Please enter a valid email address');
    }
    if (!_validators.isValidPin(pin)) {
      return ValidationFailure('PIN must be 6 digits');
    }
    return null;
  }
}
