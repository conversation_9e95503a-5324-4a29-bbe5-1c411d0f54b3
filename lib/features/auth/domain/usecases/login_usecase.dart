import 'package:SolidCheck/core/utils/either.dart';
import 'package:SolidCheck/core/utils/validators.dart';
import 'package:SolidCheck/features/auth/domain/entities/auth_result_entity.dart';
import 'package:SolidCheck/features/auth/domain/failures/auth_failure.dart';
import 'package:SolidCheck/features/auth/domain/repositories/auth_repository.dart';

class LoginUseCase {
  final AuthRepository _authRepository;
  final Validators _validators;

  LoginUseCase(this._authRepository, this._validators);

  Future<Either<AuthFailure, AuthResultEntity>> call({
    required String email,
    required String password,
  }) async {
    final validationResult = _validateInput(email, password);
    if (validationResult != null) return Left(validationResult);

    return await _authRepository.login(email: email, password: password);
  }

  ValidationFailure? _validateInput(String email, String password) {
    if (email.isEmpty) return ValidationFailure('Email cannot be empty');
    if (password.isEmpty) return ValidationFailure('Password cannot be empty');
    if (!_validators.isValidEmail(email)) {
      return ValidationFailure('Please enter a valid email address');
    }
    return null;
  }
}
