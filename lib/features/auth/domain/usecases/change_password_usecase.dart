import 'package:SolidCheck/core/utils/either.dart';
import 'package:SolidCheck/core/utils/validators.dart';
import 'package:SolidCheck/features/auth/domain/entities/change_password_entity.dart';
import 'package:SolidCheck/features/auth/domain/failures/auth_failure.dart';
import 'package:SolidCheck/features/auth/domain/repositories/auth_repository.dart';

class ChangePasswordUseCase {
  final AuthRepository _authRepository;
  final Validators _validators;

  ChangePasswordUseCase(this._authRepository, this._validators);

  Future<Either<AuthFailure, String>> call({
    required String currentPassword,
    required String newPassword,
    required String newPasswordConfirmation,
  }) async {
    final validationResult = _validateInput(
      currentPassword,
      newPassword,
      newPasswordConfirmation,
    );
    if (validationResult != null) return Left(validationResult);

    final result = await _authRepository.changePassword(
      currentPassword: currentPassword,
      newPassword: newPassword,
      newPasswordConfirmation: newPasswordConfirmation,
    );

    return result.fold(
      (failure) => Left(failure),
      (entity) => Right(entity.message),
    );
  }

  ValidationFailure? _validateInput(
    String currentPassword,
    String newPassword,
    String newPasswordConfirmation,
  ) {
    if (currentPassword.isEmpty) {
      return ValidationFailure('Current password is required');
    }

    if (newPassword.isEmpty) {
      return ValidationFailure('New password is required');
    }

    if (newPasswordConfirmation.isEmpty) {
      return ValidationFailure('Password confirmation is required');
    }

    if (!_validators.hasMinLength(newPassword, 6)) {
      return ValidationFailure('New password must be at least 6 characters');
    }

    if (newPassword != newPasswordConfirmation) {
      return ValidationFailure('New password and confirmation do not match');
    }

    if (currentPassword == newPassword) {
      return ValidationFailure('New password must be different from current password');
    }

    return null;
  }
}
