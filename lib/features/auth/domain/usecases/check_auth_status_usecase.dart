import 'package:SolidCheck/core/utils/either.dart';
import 'package:SolidCheck/features/auth/domain/entities/auth_result_entity.dart';
import 'package:SolidCheck/features/auth/domain/failures/auth_failure.dart';
import 'package:SolidCheck/features/auth/domain/repositories/auth_repository.dart';

class CheckAuthStatusUseCase {
  final AuthRepository _authRepository;

  CheckAuthStatusUseCase(this._authRepository);

  Future<bool> isAuthenticated() async {
    return await _authRepository.isAuthenticated();
  }

  Future<Either<AuthFailure, AuthResultEntity?>> getCurrentUser() async {
    return await _authRepository.getCurrentUser();
  }

  Future<String?> getCurrentToken() async {
    return await _authRepository.getCurrentToken();
  }
}
