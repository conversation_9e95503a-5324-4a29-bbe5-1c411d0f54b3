/// Base class for authentication failures
abstract class AuthFailure {
  final String message;
  final String? code;

  AuthFailure(this.message, {this.code});

  @override
  String toString() => 'AuthFailure(message: $message, code: $code)';
}

/// Server-related authentication failures
class ServerFailure extends AuthFailure {
  ServerFailure(super.message, {super.code});
}

/// Network-related authentication failures
class NetworkFailure extends AuthFailure {
  NetworkFailure(super.message, {super.code});
}

/// Invalid credentials failure
class InvalidCredentialsFailure extends AuthFailure {
  InvalidCredentialsFailure(super.message, {super.code});
}

/// Two-factor authentication failures
class TwoFactorFailure extends AuthFailure {
  TwoFactorFailure(super.message, {super.code});
}

/// Token-related failures
class TokenFailure extends AuthFailure {
  TokenFailure(super.message, {super.code});
}

/// Validation failures
class ValidationFailure extends AuthFailure {
  final Map<String, List<String>>? fieldErrors;

  ValidationFailure(super.message, {super.code, this.fieldErrors});

  /// Get validation errors for a specific field
  List<String> getFieldErrors(String field) {
    return fieldErrors?[field] ?? [];
  }

  /// Check if a specific field has validation errors
  bool hasFieldErrors(String field) {
    return fieldErrors?.containsKey(field) == true && fieldErrors![field]!.isNotEmpty;
  }

  /// Get the first validation error for a specific field
  String? getFirstFieldError(String field) {
    final errors = getFieldErrors(field);
    return errors.isNotEmpty ? errors.first : null;
  }

  /// Check if there are any field errors
  bool get hasAnyFieldErrors => fieldErrors != null && fieldErrors!.isNotEmpty;
}

/// Cache-related failures
class CacheFailure extends AuthFailure {
  CacheFailure(super.message, {super.code});
}

/// Unknown failures
class UnknownFailure extends AuthFailure {
  UnknownFailure(super.message, {super.code});
}
