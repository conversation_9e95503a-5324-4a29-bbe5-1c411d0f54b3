/// Entity representing an organization or company entity
class EntityEntity {
  final int? id;
  final String? name;
  final String? code;
  final String? role;
  final String? email;
  final String? phone;

  EntityEntity({
    this.id,
    this.name,
    this.code,
    this.role,
    this.email,
    this.phone,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EntityEntity &&
        other.id == id &&
        other.name == name &&
        other.code == code &&
        other.role == role &&
        other.email == email &&
        other.phone == phone;
  }

  @override
  int get hashCode {
    return Object.hash(id, name, code, role, email, phone);
  }

  @override
  String toString() {
    return 'EntityEntity(id: $id, name: $name, code: $code, role: $role, email: $email, phone: $phone)';
  }
}
