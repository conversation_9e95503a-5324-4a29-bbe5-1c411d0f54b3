/// User entity representing the core user data
class UserEntity {
  final int? id;
  final String? email;
  final String? userType;
  final String? firstName;
  final String? lastName;
  final String? phone;
  final String? address;
  final bool? twoFactorEnabled;

  UserEntity({
    this.id,
    this.email,
    this.userType,
    this.firstName,
    this.lastName,
    this.phone,
    this.address,
    this.twoFactorEnabled,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserEntity &&
        other.id == id &&
        other.email == email &&
        other.userType == userType &&
        other.firstName == firstName &&
        other.lastName == lastName &&
        other.phone == phone &&
        other.address == address &&
        other.twoFactorEnabled == twoFactorEnabled;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      email,
      userType,
      firstName,
      lastName,
      phone,
      address,
      twoFactorEnabled,
    );
  }

  @override
  String toString() {
    return 'UserEntity(id: $id, email: $email, userType: $userType, firstName: $firstName, lastName: $lastName, phone: $phone, address: $address, twoFactorEnabled: $twoFactorEnabled)';
  }
}
