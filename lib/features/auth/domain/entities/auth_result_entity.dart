import 'package:SolidCheck/features/auth/domain/entities/entity_entity.dart';
import 'package:SolidCheck/features/auth/domain/entities/user_entity.dart';

class AuthResultEntity {
  final bool success;
  final String? token;
  final String? tokenType;
  final DateTime? expireTime;
  final List<String>? roles;
  final bool? twoFactorEnabled;
  final bool? requiresTwoFactorSetup;
  final String? qrCodeImage;
  final String? qrCodeUrl;
  final String? tempSecret;
  final String? message;
  final UserEntity? user;
  final List<EntityEntity>? entities;

  AuthResultEntity({
    required this.success,
    this.token,
    this.tokenType,
    this.expireTime,
    this.roles,
    this.twoFactorEnabled,
    this.requiresTwoFactorSetup,
    this.qrCodeImage,
    this.qrCodeUrl,
    this.tempSecret,
    this.message,
    this.user,
    this.entities,
  });

  bool get requiresTwoFactor => twoFactorEnabled == true || requiresTwoFactorSetup == true;

  bool get isAuthenticated => success && token != null && token!.isNotEmpty;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthResultEntity &&
        other.success == success &&
        other.token == token &&
        other.tokenType == tokenType &&
        other.expireTime == expireTime &&
        other.twoFactorEnabled == twoFactorEnabled &&
        other.requiresTwoFactorSetup == requiresTwoFactorSetup &&
        other.qrCodeImage == qrCodeImage &&
        other.qrCodeUrl == qrCodeUrl &&
        other.tempSecret == tempSecret &&
        other.message == message &&
        other.user == user;
  }

  @override
  int get hashCode {
    return Object.hash(
      success,
      token,
      tokenType,
      expireTime,
      twoFactorEnabled,
      requiresTwoFactorSetup,
      qrCodeImage,
      qrCodeUrl,
      tempSecret,
      message,
      user,
    );
  }

  @override
  String toString() {
    return 'AuthResultEntity(success: $success, token: ${token != null ? 'present' : 'null'}, twoFactorEnabled: $twoFactorEnabled, requiresTwoFactorSetup: $requiresTwoFactorSetup, message: $message)';
  }
}
