import 'package:SolidCheck/core/utils/either.dart';
import 'package:SolidCheck/features/auth/domain/failures/auth_failure.dart';

/// Abstract repository for two-factor authentication operations
abstract class TwoFactorRepository {
  /// Generate QR code for 2FA setup
  Future<Either<AuthFailure, String>> generateQrCode(String token);

  /// Validate 2FA token/OTP
  Future<Either<AuthFailure, String>> validateToken({
    required String otp,
    required String token,
  });

  /// Enable 2FA for user
  Future<Either<AuthFailure, void>> enableTwoFactor(String token);

  /// Disable 2FA for user
  Future<Either<AuthFailure, String>> disableTwoFactor({
    required String token,
    required String otp,
  });

  /// Verify PIN during 2FA setup or login
  Future<Either<AuthFailure, String>> verifyPin({
    required String pin,
    required String token,
  });
}
