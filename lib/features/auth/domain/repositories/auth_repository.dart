import 'package:SolidCheck/core/utils/either.dart';
import 'package:SolidCheck/features/auth/domain/entities/auth_result_entity.dart';
import 'package:SolidCheck/features/auth/domain/entities/change_password_entity.dart';
import 'package:SolidCheck/features/auth/domain/failures/auth_failure.dart';

/// Abstract repository for authentication operations
abstract class AuthRepository {
  /// Login with email and password
  Future<Either<AuthFailure, AuthResultEntity>> login({
    required String email,
    required String password,
  });

  /// Verify two-factor authentication PIN
  Future<Either<AuthFailure, AuthResultEntity>> verifyTwoFactorPin({
    required String email,
    required String password,
    required String pin,
  });

  /// Refresh authentication token
  Future<Either<AuthFailure, AuthResultEntity>> refreshToken();

  /// Logout user
  Future<Either<AuthFailure, void>> logout();

  /// Check if user is currently authenticated
  Future<bool> isAuthenticated();

  /// Get current authentication token
  Future<String?> getCurrentToken();

  /// Get current user data
  Future<Either<AuthFailure, AuthResultEntity?>> getCurrentUser();

  /// Change user password
  Future<Either<AuthFailure, ChangePasswordEntity>> changePassword({
    required String currentPassword,
    required String newPassword,
    required String newPasswordConfirmation,
  });
}
