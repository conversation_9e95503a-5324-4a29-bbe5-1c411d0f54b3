import 'package:SolidCheck/core/network/http_client.dart';
import 'package:SolidCheck/core/utils/validators.dart';
import 'package:SolidCheck/features/auth/data/datasources/auth_local.dart';
import 'package:SolidCheck/features/auth/data/datasources/auth_remote.dart';
import 'package:SolidCheck/features/auth/data/datasources/two_factor_remote.dart';
import 'package:SolidCheck/features/auth/data/repositories/auth_repository_impl.dart';
import 'package:SolidCheck/features/auth/data/repositories/two_factor_repository_impl.dart';
import 'package:SolidCheck/features/auth/domain/repositories/auth_repository.dart';
import 'package:SolidCheck/features/auth/domain/repositories/two_factor_repository.dart';
import 'package:SolidCheck/features/auth/domain/usecases/change_password_usecase.dart';
import 'package:SolidCheck/features/auth/domain/usecases/check_auth_status_usecase.dart';
import 'package:SolidCheck/features/auth/domain/usecases/login_usecase.dart';
import 'package:SolidCheck/features/auth/domain/usecases/logout_usecase.dart';
import 'package:SolidCheck/features/auth/domain/usecases/verify_two_factor_usecase.dart';
import 'package:SolidCheck/features/auth/presentation/state/auth_form_state.dart';
import 'package:SolidCheck/features/auth/presentation/state/auth_state.dart';
import 'package:SolidCheck/features/auth/presentation/state/change_password_state.dart';
import 'package:SolidCheck/features/auth/presentation/viewmodels/auth_form_viewmodel.dart';
import 'package:SolidCheck/features/auth/presentation/viewmodels/auth_viewmodel.dart';
import 'package:SolidCheck/features/auth/presentation/viewmodels/change_password_viewmodel.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';


final sharedPreferencesProvider = Provider<SharedPreferences>((ref) {
  throw UnimplementedError('SharedPreferences must be initialized');
});

final httpClientProvider = Provider<HttpClient>((ref) => HttpClient());

final validatorsProvider = Provider<Validators>((ref) => Validators());

final authLocalDataSourceProvider = Provider<AuthLocalDataSource>((ref) {
  return AuthLocalDataSourceImpl(ref.read(sharedPreferencesProvider));
});

final authRemoteDataSourceProvider = Provider<AuthRemoteDataSource>((ref) {
  return AuthRemoteDataSourceImpl(ref.read(httpClientProvider));
});

final twoFactorRemoteDataSourceProvider = Provider<TwoFactorRemoteDataSource>((ref) {
  return TwoFactorRemoteDataSourceImpl();
});

final authRepositoryProvider = Provider<AuthRepository>((ref) {
  return AuthRepositoryImpl(
    ref.read(authRemoteDataSourceProvider),
    ref.read(authLocalDataSourceProvider),
  );
});

final twoFactorRepositoryProvider = Provider<TwoFactorRepository>((ref) {
  return TwoFactorRepositoryImpl(ref.read(twoFactorRemoteDataSourceProvider));
});

final loginUseCaseProvider = Provider<LoginUseCase>((ref) {
  return LoginUseCase(
    ref.read(authRepositoryProvider),
    ref.read(validatorsProvider),
  );
});

final verifyTwoFactorUseCaseProvider = Provider<VerifyTwoFactorUseCase>((ref) {
  return VerifyTwoFactorUseCase(
    ref.read(authRepositoryProvider),
    ref.read(validatorsProvider),
  );
});

final logoutUseCaseProvider = Provider<LogoutUseCase>((ref) {
  return LogoutUseCase(ref.read(authRepositoryProvider));
});

final checkAuthStatusUseCaseProvider = Provider<CheckAuthStatusUseCase>((ref) {
  return CheckAuthStatusUseCase(ref.read(authRepositoryProvider));
});

final changePasswordUseCaseProvider = Provider<ChangePasswordUseCase>((ref) {
  return ChangePasswordUseCase(
    ref.read(authRepositoryProvider),
    ref.read(validatorsProvider),
  );
});

final authViewModelProvider = StateNotifierProvider<AuthViewModel, AuthState>((ref) {
  return AuthViewModel(
    ref.read(loginUseCaseProvider),
    ref.read(verifyTwoFactorUseCaseProvider),
    ref.read(logoutUseCaseProvider),
    ref.read(checkAuthStatusUseCaseProvider),
  );
});

final changePasswordViewModelProvider = StateNotifierProvider<ChangePasswordViewModel, ChangePasswordState>((ref) {
  return ChangePasswordViewModel(ref.read(changePasswordUseCaseProvider));
});

final formViewModelProvider = StateNotifierProvider<FormViewModel, FormState>((ref) {
  return FormViewModel(ref.read(validatorsProvider));
});

final obscureTextProvider = StateProvider<bool>((ref) => true);

final qrCodeProvider = FutureProvider.autoDispose<String>((ref) async {
  final prefs = await SharedPreferences.getInstance();

  final qrCodeUrl = prefs.getString('qrCodeUrl');
  if (qrCodeUrl != null && qrCodeUrl.isNotEmpty) {
    return qrCodeUrl;
  }

  final qrCodeImage = prefs.getString('qrCodeImage');
  if (qrCodeImage != null && qrCodeImage.isNotEmpty) {
    return qrCodeImage;
  }

  throw Exception('QR code data not found. Please login again to generate QR code.');
});

final qrCodePinVerificationProvider =
    FutureProvider.autoDispose.family<String, String>((ref, pin) async {
  final prefs = await SharedPreferences.getInstance();
  final email = prefs.getString('tempEmail');
  final password = prefs.getString('tempPassword');

  if (email == null || password == null) {
    throw Exception('Login credentials are missing. Please login again.');
  }

  final authViewModel = ref.read(authViewModelProvider.notifier);
  await authViewModel.verifyTwoFactor(email: email, password: password, pin: pin);

  return 'Two-factor authentication setup completed successfully';
});
