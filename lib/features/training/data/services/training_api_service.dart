import 'dart:io';

import 'package:SolidCheck/models/training_models.dart';
import 'package:dio/dio.dart';

class TrainingApiService {
  late final Dio _dio;
  final String _solidTechBaseUrl = 'http://localhost:9000'; // SolidTech ML server

  TrainingApiService() {
    _dio = Dio(
      BaseOptions(
        baseUrl: _solidTechBaseUrl,
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ),
    );

    _dio.interceptors.add(
      LogInterceptor(
        requestBody: true,
        responseBody: true,
      ),
    );
  }

  Future<LabelingSession> createLabelingSession({
    required String documentType,
    int targetCount = 20,
  }) async {
    try {
      final response = await _dio.post(
        '/training/sessions',
        data: {
          'document_type': documentType,
          'target_count': targetCount,
        },
      );

      if (response.statusCode == 200) {
        return LabelingSession.fromJson(response.data);
      } else {
        throw Exception('Failed to create labeling session: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to create labeling session: $e');
    }
  }

  Future<Map<String, dynamic>> uploadDocumentForLabeling({
    required String sessionId,
    required File imageFile,
  }) async {
    try {
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(
          imageFile.path,
          filename: imageFile.path.split('/').last,
        ),
      });

      final response = await _dio.post(
        '/training/sessions/$sessionId/documents',
        data: formData,
        options: Options(
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        ),
      );

      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to upload document: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to upload document: $e');
    }
  }

  Future<Map<String, dynamic>> submitLabeledDocument({
    required String sessionId,
    required String imagePath,
    required Map<String, dynamic> labeledFields,
    required Map<String, Map<String, double>> boundingBoxes,
    required double qualityScore,
    required String notes,
  }) async {
    try {
      final response = await _dio.post(
        '/training/sessions/$sessionId/submit',
        data: {
          'session_id': sessionId,
          'image_path': imagePath,
          'labeled_fields': labeledFields,
          'bounding_boxes': boundingBoxes,
          'quality_score': qualityScore,
          'notes': notes,
        },
      );

      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to submit labeled document: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to submit labeled document: $e');
    }
  }

  Future<TrainingStatistics> getTrainingStatistics() async {
    try {
      final response = await _dio.get('/training/statistics');

      if (response.statusCode == 200) {
        return TrainingStatistics.fromJson(response.data);
      } else {
        throw Exception('Failed to get training statistics: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to get training statistics: $e');
    }
  }

  Future<void> trainModel({
    required String documentType,
    bool force = false,
  }) async {
    try {
      final response = await _dio.post(
        '/training/models/train',
        data: {
          'document_type': documentType,
          'force': force,
        },
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to start model training: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to start model training: $e');
    }
  }

  Future<ModelStatus> getModelStatus(String documentType) async {
    try {
      final response = await _dio.get('/training/models/$documentType/status');

      if (response.statusCode == 200) {
        return ModelStatus.fromJson(response.data);
      } else {
        throw Exception('Failed to get model status: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to get model status: $e');
    }
  }

  Future<LabelingSession> getLabelingSession(String sessionId) async {
    try {
      final response = await _dio.get('/training/sessions/$sessionId');

      if (response.statusCode == 200) {
        return LabelingSession.fromJson(response.data);
      } else {
        throw Exception('Failed to get labeling session: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to get labeling session: $e');
    }
  }

  Future<TrainingProgress> getTrainingProgress(String documentType) async {
    try {
      final response = await _dio.get('/training/models/$documentType/progress');

      if (response.statusCode == 200) {
        return TrainingProgress.fromJson(response.data);
      } else {
        throw Exception('Failed to get training progress: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to get training progress: $e');
    }
  }

  Future<List<String>> getSupportedDocumentTypes() async {
    try {
      final response = await _dio.get('/training/document-types');

      if (response.statusCode == 200) {
        return List<String>.from(response.data['document_types'] ?? []);
      } else {
        throw Exception('Failed to get supported document types: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to get supported document types: $e');
    }
  }

  Future<Map<String, dynamic>> validateLabeledData({
    required String documentType,
    required Map<String, dynamic> labeledFields,
  }) async {
    try {
      final response = await _dio.post(
        '/training/validate',
        data: {
          'document_type': documentType,
          'labeled_fields': labeledFields,
        },
      );

      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to validate labeled data: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to validate labeled data: $e');
    }
  }

  Future<void> cancelTraining(String documentType) async {
    try {
      final response = await _dio.post('/training/models/$documentType/cancel');

      if (response.statusCode != 200) {
        throw Exception('Failed to cancel training: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to cancel training: $e');
    }
  }

  Future<Map<String, dynamic>> exportTrainingData(String documentType) async {
    try {
      final response = await _dio.get('/training/export/$documentType');

      if (response.statusCode == 200) {
        return response.data;
      } else {
        throw Exception('Failed to export training data: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to export training data: $e');
    }
  }

  Future<void> importTrainingData({
    required String documentType,
    required File dataFile,
  }) async {
    try {
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(
          dataFile.path,
          filename: dataFile.path.split('/').last,
        ),
        'document_type': documentType,
      });

      final response = await _dio.post(
        '/training/import',
        data: formData,
        options: Options(
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        ),
      );

      if (response.statusCode != 200) {
        throw Exception('Failed to import training data: ${response.statusMessage}');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to import training data: $e');
    }
  }
}
