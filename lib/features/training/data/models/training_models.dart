class LabelingSession {
  final String sessionId;
  final String documentType;
  final int userId;
  final String createdAt;
  final String status;
  final int labeledCount;
  final int targetCount;
  final Map<String, dynamic> instructions;

  LabelingSession({
    required this.sessionId,
    required this.documentType,
    required this.userId,
    required this.createdAt,
    required this.status,
    required this.labeledCount,
    required this.targetCount,
    required this.instructions,
  });

  factory LabelingSession.fromJson(Map<String, dynamic> json) {
    return LabelingSession(
      sessionId: json['session_id'] ?? '',
      documentType: json['document_type'] ?? '',
      userId: json['user_id'] ?? 0,
      createdAt: json['created_at'] ?? '',
      status: json['status'] ?? '',
      labeledCount: json['labeled_count'] ?? 0,
      targetCount: json['target_count'] ?? 0,
      instructions: json['instructions'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'session_id': sessionId,
      'document_type': documentType,
      'user_id': userId,
      'created_at': createdAt,
      'status': status,
      'labeled_count': labeledCount,
      'target_count': targetCount,
      'instructions': instructions,
    };
  }

  LabelingSession copyWith({
    String? sessionId,
    String? documentType,
    int? userId,
    String? createdAt,
    String? status,
    int? labeledCount,
    int? targetCount,
    Map<String, dynamic>? instructions,
  }) {
    return LabelingSession(
      sessionId: sessionId ?? this.sessionId,
      documentType: documentType ?? this.documentType,
      userId: userId ?? this.userId,
      createdAt: createdAt ?? this.createdAt,
      status: status ?? this.status,
      labeledCount: labeledCount ?? this.labeledCount,
      targetCount: targetCount ?? this.targetCount,
      instructions: instructions ?? this.instructions,
    );
  }

  double get completionPercentage {
    if (targetCount == 0) return 0.0;
    return (labeledCount / targetCount).clamp(0.0, 1.0);
  }
}

class LabeledFieldData {
  final String value;
  final double confidence;
  final Map<String, double>? boundingBox;
  final String fieldType;
  final String? notes;

  LabeledFieldData({
    required this.value,
    required this.confidence,
    this.boundingBox,
    this.fieldType = 'text',
    this.notes,
  });

  factory LabeledFieldData.fromJson(Map<String, dynamic> json) {
    return LabeledFieldData(
      value: json['value'] ?? '',
      confidence: (json['confidence'] ?? 0.0).toDouble(),
      boundingBox: json['bounding_box'] != null 
          ? Map<String, double>.from(json['bounding_box'])
          : null,
      fieldType: json['field_type'] ?? 'text',
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'value': value,
      'confidence': confidence,
      'bounding_box': boundingBox,
      'field_type': fieldType,
      'notes': notes,
    };
  }
}

class TrainingStatistics {
  final int totalLabeledDocuments;
  final Map<String, int> byDocumentType;
  final Map<String, TrainingReadiness> trainingReadiness;
  final List<RecentActivity> recentActivity;
  final Map<String, dynamic> modelPerformance;

  TrainingStatistics({
    required this.totalLabeledDocuments,
    required this.byDocumentType,
    required this.trainingReadiness,
    required this.recentActivity,
    required this.modelPerformance,
  });

  factory TrainingStatistics.fromJson(Map<String, dynamic> json) {
    return TrainingStatistics(
      totalLabeledDocuments: json['total_labeled_documents'] ?? 0,
      byDocumentType: Map<String, int>.from(json['by_document_type'] ?? {}),
      trainingReadiness: (json['training_readiness'] as Map<String, dynamic>? ?? {})
          .map((key, value) => MapEntry(key, TrainingReadiness.fromJson(value))),
      recentActivity: (json['recent_activity'] as List? ?? [])
          .map((item) => RecentActivity.fromJson(item))
          .toList(),
      modelPerformance: json['model_performance'] ?? {},
    );
  }
}

class TrainingReadiness {
  final int currentSamples;
  final int requiredSamples;
  final bool readyForTraining;
  final double completionPercentage;

  TrainingReadiness({
    required this.currentSamples,
    required this.requiredSamples,
    required this.readyForTraining,
    required this.completionPercentage,
  });

  factory TrainingReadiness.fromJson(Map<String, dynamic> json) {
    return TrainingReadiness(
      currentSamples: json['current_samples'] ?? 0,
      requiredSamples: json['required_samples'] ?? 0,
      readyForTraining: json['ready_for_training'] ?? false,
      completionPercentage: (json['completion_percentage'] ?? 0.0).toDouble(),
    );
  }
}

class RecentActivity {
  final String type;
  final String documentType;
  final String timestamp;
  final String description;

  RecentActivity({
    required this.type,
    required this.documentType,
    required this.timestamp,
    required this.description,
  });

  factory RecentActivity.fromJson(Map<String, dynamic> json) {
    return RecentActivity(
      type: json['type'] ?? '',
      documentType: json['document_type'] ?? '',
      timestamp: json['timestamp'] ?? '',
      description: json['description'] ?? '',
    );
  }
}

class ModelStatus {
  final String documentType;
  final String status;
  final String? trainedAt;
  final Map<String, dynamic> trainingResults;
  final String version;
  final String? modelPath;

  ModelStatus({
    required this.documentType,
    required this.status,
    this.trainedAt,
    required this.trainingResults,
    required this.version,
    this.modelPath,
  });

  factory ModelStatus.fromJson(Map<String, dynamic> json) {
    return ModelStatus(
      documentType: json['document_type'] ?? '',
      status: json['status'] ?? '',
      trainedAt: json['trained_at'],
      trainingResults: json['training_results'] ?? {},
      version: json['version'] ?? '1.0.0',
      modelPath: json['model_path'],
    );
  }

  bool get isActive => status == 'active';
  bool get isTrained => status != 'not_trained';
  
  double? get accuracy {
    final results = trainingResults;
    return results['final_val_accuracy']?.toDouble();
  }
  
  double? get loss {
    final results = trainingResults;
    return results['final_val_loss']?.toDouble();
  }
}

class TrainingProgress {
  final String documentType;
  final int currentEpoch;
  final int totalEpochs;
  final double currentLoss;
  final double currentAccuracy;
  final String status;
  final String? estimatedTimeRemaining;

  TrainingProgress({
    required this.documentType,
    required this.currentEpoch,
    required this.totalEpochs,
    required this.currentLoss,
    required this.currentAccuracy,
    required this.status,
    this.estimatedTimeRemaining,
  });

  factory TrainingProgress.fromJson(Map<String, dynamic> json) {
    return TrainingProgress(
      documentType: json['document_type'] ?? '',
      currentEpoch: json['current_epoch'] ?? 0,
      totalEpochs: json['total_epochs'] ?? 0,
      currentLoss: (json['current_loss'] ?? 0.0).toDouble(),
      currentAccuracy: (json['current_accuracy'] ?? 0.0).toDouble(),
      status: json['status'] ?? '',
      estimatedTimeRemaining: json['estimated_time_remaining'],
    );
  }

  double get progressPercentage {
    if (totalEpochs == 0) return 0.0;
    return (currentEpoch / totalEpochs).clamp(0.0, 1.0);
  }
}

enum TrainingStatus {
  notStarted,
  inProgress,
  completed,
  failed,
  cancelled,
}

extension TrainingStatusExtension on TrainingStatus {
  String get displayName {
    switch (this) {
      case TrainingStatus.notStarted:
        return 'Not Started';
      case TrainingStatus.inProgress:
        return 'In Progress';
      case TrainingStatus.completed:
        return 'Completed';
      case TrainingStatus.failed:
        return 'Failed';
      case TrainingStatus.cancelled:
        return 'Cancelled';
    }
  }

  bool get isActive => this == TrainingStatus.inProgress;
  bool get isCompleted => this == TrainingStatus.completed;
  bool get hasError => this == TrainingStatus.failed;
}
