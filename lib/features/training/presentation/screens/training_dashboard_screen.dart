import 'package:SolidCheck/core/config/design_config.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/providers/training_provider.dart';
import 'package:SolidCheck/shared/widgets/navigation/applicant_sidebar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'document_labeling_screen.dart';

class TrainingDashboardScreen extends ConsumerStatefulWidget {
  const TrainingDashboardScreen({super.key});

  @override
  ConsumerState<TrainingDashboardScreen> createState() => _TrainingDashboardScreenState();
}

class _TrainingDashboardScreenState extends ConsumerState<TrainingDashboardScreen> {
  final List<String> _supportedDocumentTypes = [
    'p60',
    'p45', 
    'passport',
    'driving_licence',
    'bank_statement',
    'utility_bill',
  ];

  @override
  void initState() {
    super.initState();
    _loadTrainingData();
  }

  Future<void> _loadTrainingData() async {
    await ref.read(trainingProvider.notifier).loadTrainingStatistics();
  }

  @override
  Widget build(BuildContext context) {
    final isMobile = ResponsiveHelper.isMobile(context);
    final trainingState = ref.watch(trainingProvider);

    return Scaffold(
      backgroundColor: DesignConfig.primaryBackgroundColor,
      drawer: isMobile ? const ApplicantSidebar() : null,
      body: Row(
        children: [
          if (!isMobile) const ApplicantSidebar(),
          Expanded(
            child: Column(
              children: [
                _buildHeader(context),
                Expanded(
                  child: _buildContent(context, trainingState),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(DesignConfig.spaceLG),
      decoration: BoxDecoration(
        color: DesignConfig.cardBackgroundColor,
        border: Border(
          bottom: BorderSide(
            color: DesignConfig.primaryBorderColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.model_training,
            color: DesignConfig.primaryColor,
            size: 32,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'ML Model Training Dashboard',
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getResponsiveFontSize(
                      context,
                      mobile: 20.0,
                      tablet: 22.0,
                      desktop: 24.0,
                    ),
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Train and manage ML models for document processing',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          ElevatedButton.icon(
            onPressed: _loadTrainingData,
            icon: const Icon(Icons.refresh, size: 18),
            label: const Text('Refresh'),
            style: ElevatedButton.styleFrom(
              backgroundColor: DesignConfig.primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context, TrainingState state) {
    if (state.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(DesignConfig.spaceLG),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildOverviewCards(state),
          const SizedBox(height: 32),
          _buildDocumentTypeGrid(state),
          const SizedBox(height: 32),
          _buildRecentActivity(state),
        ],
      ),
    );
  }

  Widget _buildOverviewCards(TrainingState state) {
    final stats = state.trainingStats;
    
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            title: 'Total Documents',
            value: '${stats?.totalLabeledDocuments ?? 0}',
            icon: Icons.description,
            color: Colors.blue,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            title: 'Document Types',
            value: '${stats?.byDocumentType.length ?? 0}',
            icon: Icons.category,
            color: Colors.green,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            title: 'Ready for Training',
            value: '${_getReadyForTrainingCount(stats)}',
            icon: Icons.check_circle,
            color: Colors.orange,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            title: 'Active Models',
            value: '${_getActiveModelsCount(state)}',
            icon: Icons.smart_toy,
            color: Colors.purple,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color[600], size: 24),
                const Spacer(),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: color[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentTypeGrid(TrainingState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Document Types',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: ResponsiveHelper.isMobile(context) ? 1 : 2,
            childAspectRatio: 3,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
          ),
          itemCount: _supportedDocumentTypes.length,
          itemBuilder: (context, index) {
            final documentType = _supportedDocumentTypes[index];
            return _buildDocumentTypeCard(documentType, state);
          },
        ),
      ],
    );
  }

  Widget _buildDocumentTypeCard(String documentType, TrainingState state) {
    final stats = state.trainingStats;
    final readiness = stats?.trainingReadiness[documentType];
    final modelStatus = state.modelStatuses[documentType];
    final currentSamples = stats?.byDocumentType[documentType] ?? 0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getDocumentTypeIcon(documentType),
                  color: Colors.blue[600],
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _formatDocumentTypeName(documentType),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '$currentSamples labeled documents',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                _buildStatusChip(readiness, modelStatus),
              ],
            ),
            const SizedBox(height: 12),
            if (readiness != null) ...[
              LinearProgressIndicator(
                value: readiness.completionPercentage / 100,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  readiness.readyForTraining ? Colors.green[600]! : Colors.blue[600]!,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '${readiness.currentSamples}/${readiness.requiredSamples} samples (${readiness.completionPercentage.round()}%)',
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey[600],
                ),
              ),
            ],
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _navigateToLabeling(documentType),
                    icon: const Icon(Icons.label, size: 16),
                    label: const Text('Label Documents'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue[600],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: readiness?.readyForTraining == true 
                      ? () => _trainModel(documentType)
                      : null,
                  icon: const Icon(Icons.play_arrow, size: 16),
                  label: const Text('Train'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(dynamic readiness, dynamic modelStatus) {
    String status;
    Color color;

    if (modelStatus?.isActive == true) {
      status = 'Active';
      color = Colors.green;
    } else if (readiness?.readyForTraining == true) {
      status = 'Ready';
      color = Colors.orange;
    } else {
      status = 'Needs Data';
      color = Colors.red;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status,
        style: TextStyle(
          color: color[700],
          fontSize: 11,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildRecentActivity(TrainingState state) {
    final activities = state.trainingStats?.recentActivity ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Activity',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
        const SizedBox(height: 16),
        Card(
          child: activities.isEmpty
              ? const Padding(
                  padding: EdgeInsets.all(32),
                  child: Center(
                    child: Text('No recent activity'),
                  ),
                )
              : ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: activities.length,
                  separatorBuilder: (context, index) => const Divider(),
                  itemBuilder: (context, index) {
                    final activity = activities[index];
                    return ListTile(
                      leading: Icon(
                        _getActivityIcon(activity.type),
                        color: Colors.blue[600],
                      ),
                      title: Text(activity.description),
                      subtitle: Text(
                        '${_formatDocumentTypeName(activity.documentType)} • ${_formatTimestamp(activity.timestamp)}',
                      ),
                    );
                  },
                ),
        ),
      ],
    );
  }

  IconData _getDocumentTypeIcon(String documentType) {
    switch (documentType) {
      case 'p60':
      case 'p45':
        return Icons.receipt_long;
      case 'passport':
        return Icons.book;
      case 'driving_licence':
        return Icons.credit_card;
      case 'bank_statement':
        return Icons.account_balance;
      case 'utility_bill':
        return Icons.receipt;
      default:
        return Icons.description;
    }
  }

  String _formatDocumentTypeName(String documentType) {
    switch (documentType) {
      case 'p60':
        return 'P60 Tax Certificate';
      case 'p45':
        return 'P45 Tax Form';
      case 'driving_licence':
        return 'Driving Licence';
      case 'bank_statement':
        return 'Bank Statement';
      case 'utility_bill':
        return 'Utility Bill';
      default:
        return documentType
            .split('_')
            .map((word) => word[0].toUpperCase() + word.substring(1))
            .join(' ');
    }
  }

  IconData _getActivityIcon(String activityType) {
    switch (activityType) {
      case 'labeling':
        return Icons.label;
      case 'training':
        return Icons.model_training;
      case 'validation':
        return Icons.check_circle;
      default:
        return Icons.info;
    }
  }

  String _formatTimestamp(String timestamp) {
    try {
      final dateTime = DateTime.parse(timestamp);
      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inDays > 0) {
        return '${difference.inDays} days ago';
      } else if (difference.inHours > 0) {
        return '${difference.inHours} hours ago';
      } else if (difference.inMinutes > 0) {
        return '${difference.inMinutes} minutes ago';
      } else {
        return 'Just now';
      }
    } catch (e) {
      return timestamp;
    }
  }

  int _getReadyForTrainingCount(dynamic stats) {
    if (stats?.trainingReadiness == null) return 0;

    int count = 0;
    for (final readiness in stats.trainingReadiness.values) {
      if (readiness.readyForTraining == true) {
        count++;
      }
    }
    return count;
  }

  int _getActiveModelsCount(TrainingState state) {
    int count = 0;
    for (final status in state.modelStatuses.values) {
      if (status.isActive) {
        count++;
      }
    }
    return count;
  }

  void _navigateToLabeling(String documentType) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DocumentLabelingScreen(
          documentType: documentType,
        ),
      ),
    );
  }

  Future<void> _trainModel(String documentType) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Train ${_formatDocumentTypeName(documentType)} Model'),
        content: const Text(
          'This will start training a new ML model for this document type. '
          'The process may take several minutes to complete. Continue?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green[600],
              foregroundColor: Colors.white,
            ),
            child: const Text('Start Training'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ref.read(trainingProvider.notifier).trainModel(
          documentType: documentType,
          force: false,
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Training started for ${_formatDocumentTypeName(documentType)}'),
              backgroundColor: Colors.green,
            ),
          );
        }

        await _loadTrainingData();
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to start training: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}