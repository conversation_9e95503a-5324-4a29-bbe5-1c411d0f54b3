import 'dart:io';
import 'dart:typed_data';
import 'package:SolidCheck/core/config/design_config.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/features/training/presentation/providers/training_provider.dart';
import 'package:SolidCheck/features/training/presentation/widgets/field_labeling_widget.dart';
import 'package:SolidCheck/features/training/presentation/widgets/training_progress_widget.dart';
import 'package:SolidCheck/shared/widgets/navigation/applicant_sidebar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';

class DocumentLabelingScreen extends ConsumerStatefulWidget {
  final String documentType;
  final String? sessionId;

  const DocumentLabelingScreen({
    super.key,
    required this.documentType,
    this.sessionId,
  });

  @override
  ConsumerState<DocumentLabelingScreen> createState() => _DocumentLabelingScreenState();
}

class _DocumentLabelingScreenState extends ConsumerState<DocumentLabelingScreen> {
  final ImagePicker _picker = ImagePicker();
  File? _selectedImage;
  final Map<String, dynamic> _labeledFields = {};
  final Map<String, Map<String, double>> _boundingBoxes = {};
  double _qualityScore = 1.0;
  String _notes = '';
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _initializeSession();
  }

  Future<void> _initializeSession() async {
    if (widget.sessionId == null) {
      // Create new labeling session
      await ref.read(trainingProvider.notifier).createLabelingSession(
        documentType: widget.documentType,
        targetCount: 20,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final isMobile = ResponsiveHelper.isMobile(context);
    final trainingState = ref.watch(trainingProvider);

    return Scaffold(
      backgroundColor: DesignConfig.primaryBackgroundColor,
      drawer: isMobile ? const ApplicantSidebar() : null,
      body: Row(
        children: [
          if (!isMobile) const ApplicantSidebar(),
          Expanded(
            child: Column(
              children: [
                _buildHeader(context),
                Expanded(
                  child: _buildContent(context, trainingState),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(DesignConfig.spaceLG),
      decoration: BoxDecoration(
        color: DesignConfig.cardBackgroundColor,
        border: Border(
          bottom: BorderSide(
            color: DesignConfig.primaryBorderColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: Icon(
              Icons.arrow_back,
              color: DesignConfig.primaryColor,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Document Labeling',
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getResponsiveFontSize(
                      context,
                      mobile: 20.0,
                      tablet: 22.0,
                      desktop: 24.0,
                    ),
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Label ${widget.documentType.toUpperCase()} documents for ML training',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          TrainingProgressWidget(documentType: widget.documentType),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context, TrainingState state) {
    if (state.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
            const SizedBox(height: 16),
            Text(
              'Error: ${state.error}',
              style: TextStyle(color: Colors.red[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _initializeSession,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(DesignConfig.spaceLG),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInstructionsCard(state),
          const SizedBox(height: 24),
          _buildImageUploadSection(),
          if (_selectedImage != null) ...[
            const SizedBox(height: 24),
            _buildLabelingSection(state),
            const SizedBox(height: 24),
            _buildQualitySection(),
            const SizedBox(height: 24),
            _buildNotesSection(),
            const SizedBox(height: 32),
            _buildSubmitSection(),
          ],
        ],
      ),
    );
  }

  Widget _buildInstructionsCard(TrainingState state) {
    final instructions = state.currentSession?.instructions;
    if (instructions == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue[600]),
                const SizedBox(width: 8),
                Text(
                  instructions['title'] ?? 'Labeling Instructions',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              instructions['description'] ?? '',
              style: TextStyle(color: Colors.grey[700]),
            ),
            if (instructions['required_fields'] != null) ...[
              const SizedBox(height: 16),
              Text(
                'Required Fields:',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: (instructions['required_fields'] as List)
                    .map((field) => Chip(
                          label: Text(field.toString()),
                          backgroundColor: Colors.blue[50],
                          labelStyle: TextStyle(color: Colors.blue[700]),
                        ))
                    .toList(),
              ),
            ],
            if (instructions['tips'] != null) ...[
              const SizedBox(height: 16),
              Text(
                'Tips:',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
              const SizedBox(height: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: (instructions['tips'] as List)
                    .map((tip) => Padding(
                          padding: const EdgeInsets.only(bottom: 4),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('• ', style: TextStyle(color: Colors.grey[600])),
                              Expanded(
                                child: Text(
                                  tip.toString(),
                                  style: TextStyle(color: Colors.grey[600]),
                                ),
                              ),
                            ],
                          ),
                        ))
                    .toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildImageUploadSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Upload Document Image',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 16),
            if (_selectedImage == null)
              GestureDetector(
                onTap: _pickImage,
                child: Container(
                  height: 200,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Colors.grey[300]!,
                      style: BorderStyle.solid,
                      width: 2,
                    ),
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.grey[50],
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.cloud_upload_outlined,
                        size: 48,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Click to upload document image',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Supported formats: JPG, PNG, PDF',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              Column(
                children: [
                  Container(
                    height: 300,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.file(
                        _selectedImage!,
                        fit: BoxFit.contain,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      ElevatedButton.icon(
                        onPressed: _pickImage,
                        icon: const Icon(Icons.refresh),
                        label: const Text('Change Image'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey[600],
                          foregroundColor: Colors.white,
                        ),
                      ),
                      const Spacer(),
                      ElevatedButton.icon(
                        onPressed: () {
                          setState(() {
                            _selectedImage = null;
                            _labeledFields.clear();
                            _boundingBoxes.clear();
                          });
                        },
                        icon: const Icon(Icons.delete),
                        label: const Text('Remove'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red[600],
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildLabelingSection(TrainingState state) {
    final instructions = state.currentSession?.instructions;
    final requiredFields = instructions?['required_fields'] as List? ?? [];
    final fieldDescriptions = instructions?['field_descriptions'] as Map? ?? {};

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Label Document Fields',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 16),
            ...requiredFields.map((fieldName) => FieldLabelingWidget(
                  fieldName: fieldName.toString(),
                  description: fieldDescriptions[fieldName]?.toString() ?? '',
                  onFieldLabeled: (fieldData) {
                    setState(() {
                      _labeledFields[fieldName.toString()] = fieldData;
                    });
                  },
                  onBoundingBoxSet: (boundingBox) {
                    setState(() {
                      _boundingBoxes[fieldName.toString()] = boundingBox;
                    });
                  },
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildQualitySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Document Quality Assessment',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Rate the overall quality of this document for training:',
              style: TextStyle(color: Colors.grey[700]),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                const Text('Poor'),
                Expanded(
                  child: Slider(
                    value: _qualityScore,
                    min: 0.0,
                    max: 1.0,
                    divisions: 10,
                    label: '${(_qualityScore * 100).round()}%',
                    onChanged: (value) {
                      setState(() {
                        _qualityScore = value;
                      });
                    },
                  ),
                ),
                const Text('Excellent'),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Quality Score: ${(_qualityScore * 100).round()}%',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: _qualityScore >= 0.7 ? Colors.green[700] :
                       _qualityScore >= 0.4 ? Colors.orange[700] : Colors.red[700],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Additional Notes (Optional)',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              maxLines: 3,
              decoration: InputDecoration(
                hintText: 'Add any notes about this document, labeling challenges, or observations...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                filled: true,
                fillColor: Colors.grey[50],
              ),
              onChanged: (value) {
                setState(() {
                  _notes = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubmitSection() {
    final canSubmit = _selectedImage != null &&
                     _labeledFields.isNotEmpty &&
                     !_isSubmitting;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Submit Labeled Document',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Icon(
                  Icons.check_circle_outline,
                  color: canSubmit ? Colors.green[600] : Colors.grey[400],
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    canSubmit
                        ? 'Ready to submit labeled document for training'
                        : 'Please complete image upload and field labeling',
                    style: TextStyle(
                      color: canSubmit ? Colors.green[700] : Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: canSubmit ? _submitLabeledDocument : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: DesignConfig.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isSubmitting
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text(
                        'Submit for Training',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
          _labeledFields.clear();
          _boundingBoxes.clear();
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to pick image: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _submitLabeledDocument() async {
    if (_selectedImage == null || _labeledFields.isEmpty) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      final trainingNotifier = ref.read(trainingProvider.notifier);

      // First upload the image
      final uploadResult = await trainingNotifier.uploadDocumentForLabeling(
        sessionId: widget.sessionId ?? '',
        imageFile: _selectedImage!,
      );

      if (uploadResult['success'] == true) {
        // Then submit the labeled data
        await trainingNotifier.submitLabeledDocument(
          sessionId: widget.sessionId ?? '',
          imagePath: uploadResult['file_path'],
          labeledFields: _labeledFields,
          boundingBoxes: _boundingBoxes,
          qualityScore: _qualityScore,
          notes: _notes,
        );

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Document submitted successfully for training!'),
            backgroundColor: Colors.green,
          ),
        );

        // Reset form
        setState(() {
          _selectedImage = null;
          _labeledFields.clear();
          _boundingBoxes.clear();
          _qualityScore = 1.0;
          _notes = '';
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to submit document: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }
}
