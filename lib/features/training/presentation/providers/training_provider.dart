import 'dart:io';

import 'package:SolidCheck/data/models/training_models.dart';
import 'package:SolidCheck/data/services/training_api_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final trainingApiServiceProvider = Provider<TrainingApiService>((ref) {
  return TrainingApiService();
});

final trainingProvider = StateNotifierProvider<TrainingNotifier, TrainingState>((ref) {
  final apiService = ref.read(trainingApiServiceProvider);
  return TrainingNotifier(apiService);
});

class TrainingNotifier extends StateNotifier<TrainingState> {
  final TrainingApiService _apiService;

  TrainingNotifier(this._apiService) : super(TrainingState.initial());

  Future<void> createLabelingSession({
    required String documentType,
    int targetCount = 20,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final session = await _apiService.createLabelingSession(
        documentType: documentType,
        targetCount: targetCount,
      );

      state = state.copyWith(
        isLoading: false,
        currentSession: session,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<Map<String, dynamic>> uploadDocumentForLabeling({
    required String sessionId,
    required File imageFile,
  }) async {
    try {
      return await _apiService.uploadDocumentForLabeling(
        sessionId: sessionId,
        imageFile: imageFile,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString());
      rethrow;
    }
  }

  Future<void> submitLabeledDocument({
    required String sessionId,
    required String imagePath,
    required Map<String, dynamic> labeledFields,
    required Map<String, Map<String, double>> boundingBoxes,
    required double qualityScore,
    required String notes,
  }) async {
    try {
      final result = await _apiService.submitLabeledDocument(
        sessionId: sessionId,
        imagePath: imagePath,
        labeledFields: labeledFields,
        boundingBoxes: boundingBoxes,
        qualityScore: qualityScore,
        notes: notes,
      );

      // Update session progress
      if (state.currentSession != null) {
        final updatedSession = state.currentSession!.copyWith(
          labeledCount: result['session_progress']['labeled_count'],
        );
        
        state = state.copyWith(currentSession: updatedSession);
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
      rethrow;
    }
  }

  Future<void> loadTrainingStatistics() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final stats = await _apiService.getTrainingStatistics();
      
      state = state.copyWith(
        isLoading: false,
        trainingStats: stats,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> trainModel({
    required String documentType,
    bool force = false,
  }) async {
    state = state.copyWith(isTraining: true, error: null);

    try {
      await _apiService.trainModel(
        documentType: documentType,
        force: force,
      );

      state = state.copyWith(isTraining: false);
    } catch (e) {
      state = state.copyWith(
        isTraining: false,
        error: e.toString(),
      );
    }
  }

  Future<void> getModelStatus(String documentType) async {
    try {
      final status = await _apiService.getModelStatus(documentType);
      
      // Update model statuses
      final updatedStatuses = Map<String, ModelStatus>.from(state.modelStatuses);
      updatedStatuses[documentType] = status;
      
      state = state.copyWith(modelStatuses: updatedStatuses);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

class TrainingState {
  final bool isLoading;
  final bool isTraining;
  final String? error;
  final LabelingSession? currentSession;
  final TrainingStatistics? trainingStats;
  final Map<String, ModelStatus> modelStatuses;

  TrainingState({
    required this.isLoading,
    required this.isTraining,
    this.error,
    this.currentSession,
    this.trainingStats,
    required this.modelStatuses,
  });

  factory TrainingState.initial() {
    return TrainingState(
      isLoading: false,
      isTraining: false,
      modelStatuses: {},
    );
  }

  TrainingState copyWith({
    bool? isLoading,
    bool? isTraining,
    String? error,
    LabelingSession? currentSession,
    TrainingStatistics? trainingStats,
    Map<String, ModelStatus>? modelStatuses,
  }) {
    return TrainingState(
      isLoading: isLoading ?? this.isLoading,
      isTraining: isTraining ?? this.isTraining,
      error: error,
      currentSession: currentSession ?? this.currentSession,
      trainingStats: trainingStats ?? this.trainingStats,
      modelStatuses: modelStatuses ?? this.modelStatuses,
    );
  }
}
