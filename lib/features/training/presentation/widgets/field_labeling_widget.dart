import 'package:flutter/material.dart';

class FieldLabelingWidget extends StatefulWidget {
  final String fieldName;
  final String description;
  final Function(Map<String, dynamic>) onFieldLabeled;
  final Function(Map<String, double>) onBoundingBoxSet;

  const FieldLabelingWidget({
    super.key,
    required this.fieldName,
    required this.description,
    required this.onFieldLabeled,
    required this.onBoundingBoxSet,
  });

  @override
  State<FieldLabelingWidget> createState() => _FieldLabelingWidgetState();
}

class _FieldLabelingWidgetState extends State<FieldLabelingWidget> {
  final TextEditingController _valueController = TextEditingController();
  double _confidence = 1.0;
  String _fieldType = 'text';
  String _notes = '';
  late ExpansibleController _expansibleController;

  final List<String> _fieldTypes = [
    'text',
    'number',
    'date',
    'currency',
    'postcode',
    'email',
    'phone',
    'name',
    'address',
  ];

  @override
  void initState() {
    super.initState();
    _expansibleController = ExpansibleController();
  }

  @override
  void dispose() {
    _valueController.dispose();
    super.dispose();
  }

  void _updateFieldData() {
    final fieldData = {
      'value': _valueController.text,
      'confidence': _confidence,
      'field_type': _fieldType,
      'notes': _notes,
    };
    
    widget.onFieldLabeled(fieldData);
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Expansible(
        controller: _expansibleController,
        headerBuilder: (context, isExpanded) => ListTile(
          leading: Icon(
            _getFieldIcon(_fieldType),
            color: Colors.blue[600],
          ),
          title: Text(
            _formatFieldName(widget.fieldName),
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
          subtitle: widget.description.isNotEmpty
              ? Text(
                  widget.description,
                  style: TextStyle(color: Colors.grey[600]),
                )
              : null,
          trailing: Icon(
            (isExpanded as bool) ? Icons.expand_less : Icons.expand_more,
            color: Colors.grey[600],
          ),
          onTap: () {
            if (isExpanded as bool) {
              _expansibleController.collapse();
            } else {
              _expansibleController.expand();
            }
          },
        ),
        bodyBuilder: (context, isExpanded) =>
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Field Value Input
                  TextField(
                    controller: _valueController,
                    decoration: InputDecoration(
                      labelText: 'Field Value *',
                      hintText: 'Enter the extracted value from the document',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      filled: true,
                      fillColor: Colors.grey[50],
                    ),
                    onChanged: (value) => _updateFieldData(),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Field Type Selection
                  DropdownButtonFormField<String>(
                    value: _fieldType,
                    decoration: InputDecoration(
                      labelText: 'Field Type',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      filled: true,
                      fillColor: Colors.grey[50],
                    ),
                    items: _fieldTypes.map((type) {
                      return DropdownMenuItem(
                        value: type,
                        child: Row(
                          children: [
                            Icon(_getFieldIcon(type), size: 20),
                            const SizedBox(width: 8),
                            Text(_formatFieldName(type)),
                          ],
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _fieldType = value;
                        });
                        _updateFieldData();
                      }
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Confidence Slider
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Confidence Level',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: Colors.grey[700],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          const Text('Low'),
                          Expanded(
                            child: Slider(
                              value: _confidence,
                              min: 0.0,
                              max: 1.0,
                              divisions: 10,
                              label: '${(_confidence * 100).round()}%',
                              onChanged: (value) {
                                setState(() {
                                  _confidence = value;
                                });
                                _updateFieldData();
                              },
                            ),
                          ),
                          const Text('High'),
                        ],
                      ),
                      Text(
                        'Confidence: ${(_confidence * 100).round()}%',
                        style: TextStyle(
                          fontSize: 12,
                          color: _confidence >= 0.8 ? Colors.green[700] : 
                                 _confidence >= 0.5 ? Colors.orange[700] : Colors.red[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Notes Field
                  TextField(
                    maxLines: 2,
                    decoration: InputDecoration(
                      labelText: 'Notes (Optional)',
                      hintText: 'Add any notes about this field extraction...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      filled: true,
                      fillColor: Colors.grey[50],
                    ),
                    onChanged: (value) {
                      _notes = value;
                      _updateFieldData();
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Bounding Box Section
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue[200]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.crop_free, color: Colors.blue[600], size: 20),
                            const SizedBox(width: 8),
                            Text(
                              'Bounding Box (Optional)',
                              style: TextStyle(
                                fontWeight: FontWeight.w500,
                                color: Colors.blue[700],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Click and drag on the document image to mark the location of this field',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.blue[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        ElevatedButton.icon(
                          onPressed: () {
                            // TODO: Implement bounding box selection
                            _showBoundingBoxDialog();
                          },
                          icon: const Icon(Icons.crop_free, size: 16),
                          label: const Text('Set Bounding Box'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue[600],
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Validation Status
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: _valueController.text.isNotEmpty 
                          ? Colors.green[50] 
                          : Colors.orange[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: _valueController.text.isNotEmpty 
                            ? Colors.green[200]! 
                            : Colors.orange[200]!,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          _valueController.text.isNotEmpty 
                              ? Icons.check_circle 
                              : Icons.warning,
                          color: _valueController.text.isNotEmpty 
                              ? Colors.green[600] 
                              : Colors.orange[600],
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _valueController.text.isNotEmpty 
                                ? 'Field labeled successfully'
                                : 'Field value is required',
                            style: TextStyle(
                              color: _valueController.text.isNotEmpty 
                                  ? Colors.green[700] 
                                  : Colors.orange[700],
                              fontWeight: FontWeight.w500,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
      ),
    );
  }

  IconData _getFieldIcon(String fieldType) {
    switch (fieldType) {
      case 'text':
        return Icons.text_fields;
      case 'number':
        return Icons.numbers;
      case 'date':
        return Icons.calendar_today;
      case 'currency':
        return Icons.attach_money;
      case 'postcode':
        return Icons.location_on;
      case 'email':
        return Icons.email;
      case 'phone':
        return Icons.phone;
      case 'name':
        return Icons.person;
      case 'address':
        return Icons.home;
      default:
        return Icons.text_fields;
    }
  }

  String _formatFieldName(String fieldName) {
    return fieldName
        .split('_')
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ');
  }

  void _showBoundingBoxDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Set Bounding Box'),
        content: const Text(
          'Bounding box selection will be implemented in the next version. '
          'For now, you can continue with field labeling without bounding boxes.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
