import 'package:SolidCheck/features/auth/data/repositories/auth.dart';
import 'package:SolidCheck/features/dashboard/data/models/client_applicants.dart';
import 'package:SolidCheck/features/dashboard/data/models/dashboard_stats.dart';
import 'package:SolidCheck/features/dashboard/data/services/dashboard_api_service.dart';

class DashboardRepository {
  final AuthRepository _authRepository;
  late final DashboardApiService _apiService;

  DashboardRepository(this._authRepository) {
    _apiService = DashboardApiService();
  }

  Future<ClientApplicantsModel> getApplicants() async {
    try {
      await _authRepository.isLoggedIn();
      final token = await _authRepository.getToken();

      if (token == null) {
        throw Exception('No authentication token found');
      }

      return await _apiService.getApplicants(token);
    } catch (error) {
      throw Exception('Failed to get applicants: ${error.toString()}');
    }
  }

  Future<ClientApplicantsModel> getApplicantsWithPagination({
    int page = 1,
    int perPage = 20,
    String? search,
    String? statusFilter,
  }) async {
    try {
      await _authRepository.isLoggedIn();
      final token = await _authRepository.getToken();

      if (token == null) {
        throw Exception('No authentication token found');
      }

      return await _apiService.getApplicantsWithPagination(
        token,
        page: page,
        perPage: perPage,
        search: search,
        statusFilter: statusFilter,
      );
    } catch (error) {
      throw Exception('Failed to get paginated applicants: ${error.toString()}');
    }
  }

  Future<DashboardStats> getDashboardStats() async {
    try {
      await _authRepository.isLoggedIn();
      final token = await _authRepository.getToken();

      if (token == null) {
        throw Exception('No authentication token found');
      }

      return await _apiService.getDashboardStats(token);
    } catch (error) {
      throw Exception('Failed to get dashboard stats: ${error.toString()}');
    }
  }

  Future<ClientApplicant?> getApplicantById(String id) async {
    try {
      await _authRepository.isLoggedIn();
      final token = await _authRepository.getToken();

      if (token == null) {
        throw Exception('No authentication token found');
      }

      return await _apiService.getApplicantById(token, id);
    } catch (error) {
      throw Exception('Failed to get applicant: ${error.toString()}');
    }
  }

  Future<ClientApplicantsModel> searchApplicants(String query) async {
    try {
      await _authRepository.isLoggedIn();
      final token = await _authRepository.getToken();

      if (token == null) {
        throw Exception('No authentication token found');
      }

      return await _apiService.searchApplicants(token, query);
    } catch (error) {
      throw Exception('Failed to search applicants: ${error.toString()}');
    }
  }

  Future<ClientApplicantsModel> getApplicantsByStatus(String status) async {
    try {
      await _authRepository.isLoggedIn();
      final token = await _authRepository.getToken();

      if (token == null) {
        throw Exception('No authentication token found');
      }

      return await _apiService.getApplicantsByStatus(token, status);
    } catch (error) {
      throw Exception('Failed to get applicants by status: ${error.toString()}');
    }
  }

  Future<bool> updateApplicantStatus(String applicantId, String status) async {
    try {
      await _authRepository.isLoggedIn();
      final token = await _authRepository.getToken();

      if (token == null) {
        throw Exception('No authentication token found');
      }

      return await _apiService.updateApplicantStatus(token, applicantId, status);
    } catch (error) {
      throw Exception('Failed to update applicant status: ${error.toString()}');
    }
  }

  Future<bool> deleteApplicant(String applicantId) async {
    try {
      await _authRepository.isLoggedIn();
      final token = await _authRepository.getToken();

      if (token == null) {
        throw Exception('No authentication token found');
      }

      return await _apiService.deleteApplicant(token, applicantId);
    } catch (error) {
      throw Exception('Failed to delete applicant: ${error.toString()}');
    }
  }
}
