import 'dart:convert';

DashboardStats dashboardStatsFromJson(String str) =>
    DashboardStats.fromJson(json.decode(str));

String dashboardStatsToJson(DashboardStats data) => json.encode(data.toJson());

class DashboardStats {
  bool? success;
  DashboardStatsData? data;
  String? message;

  DashboardStats({
    this.success,
    this.data,
    this.message,
  });

  factory DashboardStats.fromJson(Map<String, dynamic> json) => DashboardStats(
        success: json["success"],
        data: json["data"] == null
            ? null
            : DashboardStatsData.fromJson(json["data"]),
        message: json["message"],
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "data": data?.toJson(),
        "message": message,
      };
}

class DashboardStatsData {
  int? totalApplicants;
  int? inProgress;
  int? furtherActionPending;
  int? staffReviewPending;
  int? complete;

  DashboardStatsData({
    this.totalApplicants,
    this.inProgress,
    this.furtherActionPending,
    this.staffReviewPending,
    this.complete,
  });

  factory DashboardStatsData.fromJson(Map<String, dynamic> json) =>
      DashboardStatsData(
        totalApplicants: json["total_applicants"] ?? 0,
        inProgress: json["in_progress"] ?? 0,
        furtherActionPending: json["further_action_pending"] ?? 0,
        staffReviewPending: json["staff_review_pending"] ?? 0,
        complete: json["complete"] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        "total_applicants": totalApplicants,
        "in_progress": inProgress,
        "further_action_pending": furtherActionPending,
        "staff_review_pending": staffReviewPending,
        "complete": complete,
      };
}
