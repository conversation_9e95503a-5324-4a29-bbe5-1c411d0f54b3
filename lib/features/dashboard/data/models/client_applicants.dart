import 'dart:convert';

ClientApplicantsModel clientApplicantsModelFromJson(String str) =>
    ClientApplicantsModel.fromJson(json.decode(str));

String clientApplicantsModelToJson(ClientApplicantsModel data) =>
    json.encode(data.toJson());

class ClientApplicantsModel {
  bool? success;
  List<ClientApplicant>? data;
  String? message;
  StatusSummary? statusSummary;
  PaginationMetadata? pagination;

  ClientApplicantsModel({
    this.success,
    this.data,
    this.message,
    this.statusSummary,
    this.pagination,
  });

  factory ClientApplicantsModel.fromJson(Map<String, dynamic> json) {
    // Handle the nested structure where applicants are in data.applicants
    List<ClientApplicant> applicants = [];
    StatusSummary? statusSummary;
    PaginationMetadata? pagination;

    if (json["data"] != null) {
      final dataField = json["data"];
      if (dataField is Map<String, dynamic>) {
        // New API structure: data.applicants and data.status_summary
        if (dataField["applicants"] != null) {
          applicants = List<ClientApplicant>.from(
              dataField["applicants"]!.map((x) => ClientApplicant.fromJson(x)));
        }
        if (dataField["status_summary"] != null) {
          statusSummary = StatusSummary.fromJson(dataField["status_summary"]);
        }
        if (dataField["pagination"] != null) {
          pagination = PaginationMetadata.fromJson(dataField["pagination"]);
        }
      } else if (dataField is List) {
        // Old API structure: data is direct array
        applicants = List<ClientApplicant>.from(
            dataField.map((x) => ClientApplicant.fromJson(x)));
      }
    }

    return ClientApplicantsModel(
      success: json["success"],
      data: applicants,
      message: json["message"],
      statusSummary: statusSummary,
      pagination: pagination,
    );
  }

  Map<String, dynamic> toJson() => {
        "success": success,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "message": message,
        "status_summary": statusSummary?.toJson(),
        "pagination": pagination?.toJson(),
      };
}

class ClientApplicant {
  int? id;
  int? applicantId;
  String? state;
  int? accountId;
  String? name;
  String? forename;
  String? surname;
  String? contactNumber;
  String? email;
  String? reference;
  String? jobrole;
  String? organization;
  List<String>? products;
  String? createdAt;
  String? updatedAt;
  String? dateCreated;
  List<RequestedCheck>? requestedChecks;
  String? status;
  String? statusDisplay;

  ClientApplicant({
    this.id,
    this.applicantId,
    this.state,
    this.accountId,
    this.name,
    this.forename,
    this.surname,
    this.contactNumber,
    this.email,
    this.reference,
    this.jobrole,
    this.organization,
    this.products,
    this.createdAt,
    this.updatedAt,
    this.dateCreated,
    this.requestedChecks,
    this.status,
    this.statusDisplay,
  });

  factory ClientApplicant.fromJson(Map<String, dynamic> json) => ClientApplicant(
        id: json["id"],
        applicantId: json["applicant_id"],
        state: json["state"],
        accountId: json["account_id"],
        name: json["name"],
        forename: json["applicant_name_forename"] ?? json["forename"],
        surname: json["applicant_name_surname"] ?? json["surname"],
        contactNumber: json["contact_number"],
        email: json["email"],
        reference: json["reference"],
        jobrole: json["jobrole"],
        organization: json["organization"] ?? json["company_name"],
        products: json["products"] == null
            ? []
            : List<String>.from(json["products"]!.map((x) => x)),
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
        dateCreated: json["date_created"],
        requestedChecks: json["requested_checks"] == null
            ? []
            : List<RequestedCheck>.from(
                json["requested_checks"]!.map((x) => RequestedCheck.fromJson(x))),
        status: json["status"],
        statusDisplay: json["status_display"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "applicant_id": applicantId,
        "state": state,
        "account_id": accountId,
        "name": name,
        "applicant_name_forename": forename,
        "applicant_name_surname": surname,
        "contact_number": contactNumber,
        "email": email,
        "reference": reference,
        "jobrole": jobrole,
        "organization": organization,
        "products":
            products == null ? [] : List<dynamic>.from(products!.map((x) => x)),
        "created_at": createdAt,
        "updated_at": updatedAt,
        "date_created": dateCreated,
        "requested_checks": requestedChecks == null
            ? []
            : List<dynamic>.from(requestedChecks!.map((x) => x.toJson())),
        "status": status,
        "status_display": statusDisplay,
      };

  String get fullName {
    if (name != null && name!.isNotEmpty) {
      return name!;
    }
    return '${forename ?? ''} ${surname ?? ''}'.trim();
  }
  
  String get displayStatus {
    if (statusDisplay != null && statusDisplay!.isNotEmpty) {
      return statusDisplay!;
    }
    switch (status?.toLowerCase()) {
      case 'new':
        return 'New Applicant';
      case 'in_progress':
        return 'In Progress';
      case 'further_action_pending':
        return 'Further Action Pending';
      case 'staff_review_pending':
        return 'Staff Review Pending';
      case 'complete':
        return 'Complete';
      default:
        return status ?? 'Unknown';
    }
  }
}

class RequestedCheck {
  String? productCode;
  String? productName;
  String? type;
  String? status;
  String? createdAt;
  String? updatedAt;

  RequestedCheck({
    this.productCode,
    this.productName,
    this.type,
    this.status,
    this.createdAt,
    this.updatedAt,
  });

  factory RequestedCheck.fromJson(Map<String, dynamic> json) => RequestedCheck(
        productCode: json["product_code"],
        productName: json["product_name"],
        type: json["type"],
        status: json["status"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
      );

  Map<String, dynamic> toJson() => {
        "product_code": productCode,
        "product_name": productName,
        "type": type,
        "status": status,
        "created_at": createdAt,
        "updated_at": updatedAt,
      };

  String get displayName => productName ?? type ?? 'Unknown Check';
}

class StatusSummary {
  int? totalApplicants;
  int? inProgress;
  int? furtherActionPending;
  int? staffReviewPending;
  int? complete;

  StatusSummary({
    this.totalApplicants,
    this.inProgress,
    this.furtherActionPending,
    this.staffReviewPending,
    this.complete,
  });

  factory StatusSummary.fromJson(Map<String, dynamic> json) => StatusSummary(
        totalApplicants: json["total_applicants"],
        inProgress: json["in_progress"],
        furtherActionPending: json["further_action_pending"],
        staffReviewPending: json["staff_review_pending"],
        complete: json["complete"],
      );

  Map<String, dynamic> toJson() => {
        "total_applicants": totalApplicants,
        "in_progress": inProgress,
        "further_action_pending": furtherActionPending,
        "staff_review_pending": staffReviewPending,
        "complete": complete,
      };
}

class PaginationMetadata {
  int? currentPage;
  int? totalPages;
  int? totalItems;
  int? itemsPerPage;
  bool? hasNextPage;
  bool? hasPreviousPage;
  bool? hasMore;

  PaginationMetadata({
    this.currentPage,
    this.totalPages,
    this.totalItems,
    this.itemsPerPage,
    this.hasNextPage,
    this.hasPreviousPage,
    this.hasMore,
  });

  factory PaginationMetadata.fromJson(Map<String, dynamic> json) => PaginationMetadata(
        currentPage: json["current_page"] ?? json["page"],
        totalPages: json["total_pages"] ?? json["pages"],
        totalItems: json["total_items"] ?? json["total"] ?? json["count"],
        itemsPerPage: json["items_per_page"] ?? json["per_page"] ?? json["limit"],
        hasNextPage: json["has_next_page"] ?? json["has_next"] ?? json["has_more"],
        hasPreviousPage: json["has_previous_page"] ?? json["has_previous"],
        hasMore: json["has_more"],
      );

  Map<String, dynamic> toJson() => {
        "current_page": currentPage,
        "total_pages": totalPages,
        "total_items": totalItems,
        "items_per_page": itemsPerPage,
        "has_next_page": hasNextPage,
        "has_previous_page": hasPreviousPage,
        "has_more": hasMore,
      };
}
