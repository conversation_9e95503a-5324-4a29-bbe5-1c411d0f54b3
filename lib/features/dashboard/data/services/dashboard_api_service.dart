import 'package:SolidCheck/core/constants/constants.dart';
import 'package:SolidCheck/features/dashboard/data/models/client_applicants.dart';
import 'package:SolidCheck/features/dashboard/data/models/dashboard_stats.dart';
import 'package:dio/dio.dart';

class DashboardApiService {
  late final Dio _dio;

  DashboardApiService() {
    _dio = Dio(
      BaseOptions(
        baseUrl: AppConstants.baseURL,
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ),
    );
  }

  Future<bool> checkServerHealth() async {
    try {
      final response = await _dio.get(
        '/health',
        options: Options(
          sendTimeout: const Duration(seconds: 5),
          receiveTimeout: const Duration(seconds: 5),
          headers: {
            'Accept': 'application/json',
          },
        ),
      );
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  Future<ClientApplicantsModel> getApplicants(String token) async {
    try {
      final response = await _dio.get(
        '/client-applicants',
        options: Options(headers: {'Authorization': 'Bearer $token'}),
      );

      if (response.statusCode == 200) {
        return ClientApplicantsModel.fromJson(response.data);
      } else {
        throw Exception('Failed to load applicants: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (error) {
      throw Exception('Network error: ${error.toString()}');
    }
  }

  Future<ClientApplicantsModel> getApplicantsWithPagination(
    String token, {
    int page = 1,
    int perPage = 20,
    String? search,
    String? statusFilter,
  }) async {
    try {
      final queryParameters = <String, dynamic>{
        'page': page,
        'per_page': perPage,
      };

      if (search != null && search.isNotEmpty) {
        queryParameters['search'] = search;
      }
      if (statusFilter != null && statusFilter.isNotEmpty) {
        queryParameters['status_filter'] = statusFilter;
      }

      final response = await _dio.get(
        '/client-applicants',
        queryParameters: queryParameters,
        options: Options(headers: {'Authorization': 'Bearer $token'}),
      );

      final responseStr = response.data.toString();
      if (responseStr.length > 1000) {
      } else {}

      if (response.data is Map<String, dynamic>) {
        final data = response.data as Map<String, dynamic>;

        if (data['data'] is Map) {
          final dataField = data['data'] as Map<String, dynamic>;
          if (dataField['applicants'] != null) {}
        } else if (data['data'] is List) {}
      }

      if (response.statusCode == 200) {
        try {
          final result = ClientApplicantsModel.fromJson(response.data);

          return result;
        } catch (parseError) {
          throw Exception('Failed to parse applicants data: $parseError');
        }
      } else {
        throw Exception(
          'Failed to load paginated applicants: ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 500) {
        throw Exception(
          'Server error (500): The server encountered an internal error. Please try again later or contact support.',
        );
      } else if (e.response?.statusCode == 401) {
        throw Exception('Authentication error: Please log in again.');
      } else if (e.response?.statusCode == 403) {
        throw Exception(
          'Access denied: You don\'t have permission to access this resource.',
        );
      } else {
        throw Exception(
          'Network error: ${e.message ?? 'Unknown error occurred'}',
        );
      }
    } catch (error) {
      throw Exception('Unexpected error: ${error.toString()}');
    }
  }

  Future<DashboardStats> getDashboardStats(String token) async {
    try {
      final response = await _dio.get(
        '/dashboard/stats',
        options: Options(headers: {'Authorization': 'Bearer $token'}),
      );

      if (response.statusCode == 200) {
        return DashboardStats.fromJson(response.data);
      } else {
        throw Exception(
          'Failed to load dashboard stats: ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (error) {
      throw Exception('Network error: ${error.toString()}');
    }
  }

  Future<ClientApplicant?> getApplicantById(String token, String id) async {
    try {
      final response = await _dio.get(
        '/client-applicants/$id',
        options: Options(headers: {'Authorization': 'Bearer $token'}),
      );

      if (response.statusCode == 200) {
        if (response.data['success'] == true && response.data['data'] != null) {
          return ClientApplicant.fromJson(response.data['data']);
        }
        return null;
      } else {
        throw Exception('Failed to load applicant: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (error) {
      throw Exception('Network error: ${error.toString()}');
    }
  }

  Future<ClientApplicantsModel> searchApplicants(
    String token,
    String query,
  ) async {
    try {
      final response = await _dio.get(
        '/client-applicants/search',
        queryParameters: {'q': query},
        options: Options(headers: {'Authorization': 'Bearer $token'}),
      );

      if (response.statusCode == 200) {
        return ClientApplicantsModel.fromJson(response.data);
      } else {
        throw Exception('Failed to search applicants: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (error) {
      throw Exception('Network error: ${error.toString()}');
    }
  }

  Future<ClientApplicantsModel> getApplicantsByStatus(
    String token,
    String status,
  ) async {
    try {
      final response = await _dio.get(
        '/client-applicants/status/$status',
        options: Options(headers: {'Authorization': 'Bearer $token'}),
      );

      if (response.statusCode == 200) {
        return ClientApplicantsModel.fromJson(response.data);
      } else {
        throw Exception(
          'Failed to get applicants by status: ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (error) {
      throw Exception('Network error: ${error.toString()}');
    }
  }

  Future<bool> updateApplicantStatus(
    String token,
    String applicantId,
    String status,
  ) async {
    try {
      final response = await _dio.patch(
        '/client-applicants/$applicantId/status',
        data: {'status': status},
        options: Options(headers: {'Authorization': 'Bearer $token'}),
      );

      return response.statusCode == 200;
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (error) {
      throw Exception('Network error: ${error.toString()}');
    }
  }

  Future<bool> deleteApplicant(String token, String applicantId) async {
    try {
      final response = await _dio.delete(
        '/client-applicants/$applicantId',
        options: Options(headers: {'Authorization': 'Bearer $token'}),
      );

      return response.statusCode == 200;
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (error) {
      throw Exception('Network error: ${error.toString()}');
    }
  }
}
