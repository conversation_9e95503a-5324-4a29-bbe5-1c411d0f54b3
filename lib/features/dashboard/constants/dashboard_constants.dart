import 'dart:math';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:flutter/material.dart';

/// Dashboard module specific constants
class DashboardConstants {
  // Navigation items
  static List<String> navigationItems = [
    'Overview',
    'Job Roles',
    'Download Reports',
    'Reminders',
    'Help',
  ];

  // Status constants
  static String statusInProgress = 'in_progress';
  static String statusFurtherActionPending = 'further_action_pending';
  static String statusStaffReviewPending = 'staff_review_pending';
  static String statusComplete = 'complete';

  // Display status mapping
  static Map<String, String> statusDisplayMap = {
    statusInProgress: 'In Progress',
    statusFurtherActionPending: 'Further Action Pending',
    statusStaffReviewPending: 'Staff Review Pending',
    statusComplete: 'Complete',
  };

  // Status colors
  static Map<String, Color> statusColorMap = {
    statusInProgress: Colors.orange,
    statusFurtherActionPending: Colors.amber,
    statusStaffReviewPending: Colors.purple,
    statusComplete: Colors.green,
  };

  // Dashboard icons
  static final String _iconPath = 'assets/icons';
  static String acIcon = '$_iconPath/ac_icon.png';
  static String dbsIcon = '$_iconPath/dbs_icon.png';
  static String idIcon = '$_iconPath/id_icon.png';
  static String refIcon = '$_iconPath/ref_icon.png';
  static String rtwIcon = '$_iconPath/rtw_icon.png';
  static String smcIcon = '$_iconPath/smc_icon.png';
  static String jpgIcon = '$_iconPath/jpg.png';
  static String pdfIcon = '$_iconPath/pdf.png';
  static String pngIcon = '$_iconPath/png.png';

  // Sample data for testing
  static List<String> applicantNames = [
    'John Doe',
    'Jane Smith',
    'Michael Johnson',
    'Emily Davis',
    'Robert Brown',
    'Linda Wilson',
    'David Miller',
    'Sarah Taylor',
    'James Anderson',
    'Patricia Moore'
  ];

  // Product to index mapping
  static Map<String, int> productToIndexMap = {
    'DBS Check': 1,
    'Reference Check': 2,
    'Social Media Screening': 3,
    'ID Check': 4,
    'Right to Work': 5,
    'Adverse Credit Check': 6,
    'DBS CHECK': 7,
  };

  // Get icons for specific index
  static List<String> getIconsForIndex(int index) {
    switch (index) {
      case 0:
        return [smcIcon, refIcon, idIcon];
      case 1:
        return [dbsIcon, idIcon];
      case 2:
        return [acIcon];
      case 3:
        return [idIcon, acIcon];
      case 4:
        return [idIcon];
      case 5:
        return [acIcon];
      default:
        return [acIcon, idIcon, smcIcon, refIcon, dbsIcon];
    }
  }

  // Get random applicant name for testing
  static String getRandomApplicantName() {
    final random = Random();
    return applicantNames[random.nextInt(applicantNames.length)];
  }

  // Get display status
  static String getDisplayStatus(String? status) {
    return statusDisplayMap[status?.toLowerCase()] ?? 'Unknown';
  }

  // Get status color
  static Color getStatusColor(String? status) {
    return statusColorMap[status?.toLowerCase()] ?? Colors.grey;
  }

  // Dashboard text constants
  static String emptyStateTitle = 'No Applicants Created';
  static String emptyStateMessage = 'Start by adding your first applicant to begin the verification process.';
  static String addApplicantButtonText = 'Add Applicant';
  static String searchPlaceholder = 'Search applicants by name, email, or organization...';
  
  // Error messages
  static String errorLoadingApplicants = 'Error loading applicants';
  static String errorLoadingStats = 'Error loading dashboard statistics';
  static String networkError = 'Network connection error';
  static String retryButtonText = 'Retry';

  // Success messages
  static String applicantAddedSuccess = 'Applicant added successfully';
  static String applicantUpdatedSuccess = 'Applicant updated successfully';
  static String applicantDeletedSuccess = 'Applicant deleted successfully';

  // Table headers
  static List<String> tableHeaders = [
    'Applicant',
    'Contact',
    'Organization',
    'Date Created',
    'Products',
    'Status',
  ];

  // Pagination
  static int defaultPageSize = 10;
  static int maxPageSize = 50;

  // Animation durations
  static Duration defaultAnimationDuration = const Duration(milliseconds: 200);
  static Duration loadingAnimationDuration = const Duration(milliseconds: 300);
}
