import 'package:SolidCheck/features/auth/data/repositories/auth.dart';
import 'package:SolidCheck/features/dashboard/data/models/client_applicants.dart';
import 'package:SolidCheck/features/dashboard/data/models/dashboard_stats.dart';
import 'package:SolidCheck/features/dashboard/data/repositories/dashboard_repository.dart';
import 'package:SolidCheck/features/dashboard/data/services/dashboard_api_service.dart';
import 'package:SolidCheck/features/dashboard/domain/usecases/get_applicants_usecase.dart';
import 'package:SolidCheck/features/dashboard/domain/usecases/get_dashboard_stats_usecase.dart';
import 'package:SolidCheck/features/dashboard/presentation/widgets/applicants/applicants_filter_modal_widget.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class DashboardState {
  final List<ClientApplicant> applicants;
  final List<ClientApplicant> filteredApplicants;
  final DashboardStatsData? stats;
  final bool isLoading;
  final bool isLoadingMore;
  final String? error;
  final String searchQuery;
  final String? statusFilter;
  final int sortColumnIndex;
  final bool isAscending;
  final int currentPage;
  final int totalPages;
  final int totalItems;
  final int itemsPerPage;
  final bool hasNextPage;
  final bool hasPreviousPage;

  const DashboardState({
    this.applicants = const [],
    this.filteredApplicants = const [],
    this.stats,
    this.isLoading = false,
    this.isLoadingMore = false,
    this.error,
    this.searchQuery = '',
    this.statusFilter,
    this.sortColumnIndex = 0,
    this.isAscending = true,
    this.currentPage = 1,
    this.totalPages = 1,
    this.totalItems = 0,
    this.itemsPerPage = 20,
    this.hasNextPage = false,
    this.hasPreviousPage = false,
  });

  DashboardState copyWith({
    List<ClientApplicant>? applicants,
    List<ClientApplicant>? filteredApplicants,
    DashboardStatsData? stats,
    bool? isLoading,
    bool? isLoadingMore,
    String? error,
    String? searchQuery,
    String? statusFilter,
    int? sortColumnIndex,
    bool? isAscending,
    int? currentPage,
    int? totalPages,
    int? totalItems,
    int? itemsPerPage,
    bool? hasNextPage,
    bool? hasPreviousPage,
  }) {
    return DashboardState(
      applicants: applicants ?? this.applicants,
      filteredApplicants: filteredApplicants ?? this.filteredApplicants,
      stats: stats ?? this.stats,
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      error: error ?? this.error,
      searchQuery: searchQuery ?? this.searchQuery,
      statusFilter: statusFilter ?? this.statusFilter,
      sortColumnIndex: sortColumnIndex ?? this.sortColumnIndex,
      isAscending: isAscending ?? this.isAscending,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      totalItems: totalItems ?? this.totalItems,
      itemsPerPage: itemsPerPage ?? this.itemsPerPage,
      hasNextPage: hasNextPage ?? this.hasNextPage,
      hasPreviousPage: hasPreviousPage ?? this.hasPreviousPage,
    );
  }
}

class DashboardViewModel extends StateNotifier<DashboardState> {
  final GetApplicantsUseCase _getApplicantsUseCase;
  final GetDashboardStatsUseCase _getDashboardStatsUseCase;

  DashboardViewModel(this._getApplicantsUseCase, this._getDashboardStatsUseCase)
    : super(const DashboardState());

  Future<void> initialize() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await _loadApplicantsWithRetry();

      try {
        await loadDashboardStats();
      } catch (statsError) {
        _setFallbackStats();
      }
    } catch (error) {
      String userFriendlyError;
      if (error.toString().contains('500') ||
          error.toString().contains('Server error')) {
        userFriendlyError =
            'Server is temporarily unavailable. Please check if the API server is running on port 8001, or try again later.';
      } else if (error.toString().contains('Connection refused') ||
          error.toString().contains('Failed to connect') ||
          error.toString().contains('Network is unreachable')) {
        userFriendlyError =
            'Cannot connect to server. Please ensure the API server is running on port 8001 and try again.';
      } else if (error.toString().contains('timeout') ||
          error.toString().contains('Connection timeout')) {
        userFriendlyError =
            'Connection timeout. The server is taking too long to respond. Please try again.';
      } else if (error.toString().contains('No authentication token')) {
        userFriendlyError =
            'Authentication required. Please log in again to continue.';
      } else {
        userFriendlyError = 'Unable to load applicants. ${error.toString()}';
      }

      state = state.copyWith(
        isLoading: false,
        error: userFriendlyError,
      );
    }
  }

  Future<void> _loadApplicantsWithRetry({int maxRetries = 3}) async {
    int attempts = 0;
    while (attempts < maxRetries) {
      try {
        await loadApplicants(page: 1, perPage: 20);
        return;
      } catch (error) {
        attempts++;
        if (attempts >= maxRetries) {
          rethrow;
        }

        await Future.delayed(Duration(seconds: attempts * 2));
      }
    }
  }

  Future<bool> checkServerHealth() async {
    try {
      final apiService = DashboardApiService();
      return await apiService.checkServerHealth();
    } catch (error) {
      return false;
    }
  }

  void _setFallbackStats() {
    final stats = DashboardStatsData(
      totalApplicants: 0,
      inProgress: 0,
      furtherActionPending: 0,
      staffReviewPending: 0,
      complete: 0,
    );
    state = state.copyWith(stats: stats);
  }

  void loadSampleData() {
    final sampleApplicants = [
      ClientApplicant(
        id: 1,
        applicantId: 1,
        name: 'John Doe',
        email: '<EMAIL>',
        organization: 'Test Company',
        status: 'in_progress',
        statusDisplay: 'In Progress',
        createdAt: DateTime.now().toIso8601String(),
        requestedChecks: [
          RequestedCheck(
            productCode: 'DBS',
            productName: 'DBS Check',
            status: 'pending',
          ),
        ],
      ),
      ClientApplicant(
        id: 2,
        applicantId: 2,
        name: 'Jane Smith',
        email: '<EMAIL>',
        organization: 'Another Company',
        status: 'complete',
        statusDisplay: 'Complete',
        createdAt: DateTime.now()
            .subtract(const Duration(days: 1))
            .toIso8601String(),
        requestedChecks: [
          RequestedCheck(
            productCode: 'REF',
            productName: 'Reference Check',
            status: 'complete',
          ),
        ],
      ),
    ];

    final samplePagination = PaginationMetadata(
      currentPage: 1,
      totalPages: 1,
      totalItems: 2,
      itemsPerPage: 20,
      hasNextPage: false,
      hasPreviousPage: false,
    );

    final sampleStats = DashboardStatsData(
      totalApplicants: 2,
      inProgress: 1,
      furtherActionPending: 0,
      staffReviewPending: 0,
      complete: 1,
    );

    state = state.copyWith(
      applicants: sampleApplicants,
      filteredApplicants: sampleApplicants,
      isLoading: false,
      error: null,
      stats: sampleStats,
      currentPage: 1,
      totalPages: 1,
      totalItems: 2,
      itemsPerPage: 20,
      hasNextPage: false,
      hasPreviousPage: false,
    );
  }

  Future<void> loadApplicants({
    int page = 1,
    int perPage = 20,
    String? search,
    String? statusFilter,
    bool append = false,
  }) async {
    try {
      if (!append) {
        state = state.copyWith(isLoading: true, error: null);
      } else {
        state = state.copyWith(isLoadingMore: true, error: null);
      }

      final applicantsModel = await _getApplicantsUseCase.executeWithPagination(
        page: page,
        perPage: perPage,
        search: search ?? state.searchQuery,
        statusFilter: statusFilter ?? state.statusFilter,
      );

      final newApplicants = applicantsModel.data ?? [];
      final pagination = applicantsModel.pagination;
      if (applicantsModel.statusSummary != null) {
        final summary = applicantsModel.statusSummary!;
        final stats = DashboardStatsData(
          totalApplicants: summary.totalApplicants ?? 0,
          inProgress: summary.inProgress ?? 0,
          furtherActionPending: summary.furtherActionPending ?? 0,
          staffReviewPending: summary.staffReviewPending ?? 0,
          complete: summary.complete ?? 0,
        );
        state = state.copyWith(stats: stats);
      }

      List<ClientApplicant> allApplicants;
      if (append && page > 1) {
        allApplicants = [...state.applicants, ...newApplicants];
      } else {
        allApplicants = newApplicants;
      }

      state = state.copyWith(
        applicants: allApplicants,
        filteredApplicants: allApplicants,
        isLoading: false,
        isLoadingMore: false,
        error: null,
        searchQuery: search ?? state.searchQuery,
        statusFilter: statusFilter ?? state.statusFilter,
        currentPage: pagination?.currentPage ?? page,
        totalPages: pagination?.totalPages ?? 1,
        totalItems: pagination?.totalItems ?? newApplicants.length,
        itemsPerPage: pagination?.itemsPerPage ?? perPage,
        hasNextPage: pagination?.hasNextPage ?? pagination?.hasMore ?? false,
        hasPreviousPage: pagination?.hasPreviousPage ?? false,
      );
    } catch (error) {
      String userFriendlyError;
      if (error.toString().contains('Server error (500)')) {
        userFriendlyError =
            'Server is temporarily unavailable. Please try again in a few moments.';
      } else if (error.toString().contains('Authentication error')) {
        userFriendlyError = 'Your session has expired. Please log in again.';
      } else if (error.toString().contains('Network error') ||
          error.toString().contains('connection')) {
        userFriendlyError =
            'Network connection error. Please check your internet connection and try again.';
      } else {
        userFriendlyError = 'Unable to load applicants. Please try again.';
      }

      state = state.copyWith(
        isLoading: false,
        isLoadingMore: false,
        error: userFriendlyError,
      );
    }
  }

  Future<void> loadNextPage() async {
    if (state.isLoadingMore || !state.hasNextPage) return;

    await loadApplicants(
      page: state.currentPage + 1,
      perPage: state.itemsPerPage,
      append: true,
    );
  }

  Future<void> loadPage(int page) async {
    if (state.isLoading || page < 1 || page > state.totalPages) return;

    await loadApplicants(
      page: page,
      perPage: state.itemsPerPage,
      append: false,
    );
  }

  Future<void> loadDashboardStats() async {
    try {
      final stats = await _getDashboardStatsUseCase.execute();
      state = state.copyWith(stats: stats);
    } catch (error) {
      _calculateStatsFromApplicants();
    }
  }

  void _calculateStatsFromApplicants() {
    final applicants = state.applicants;

    final stats = DashboardStatsData(
      totalApplicants: applicants.length,
      inProgress: applicants
          .where((a) => a.status?.toLowerCase() == 'in_progress')
          .length,
      furtherActionPending: applicants
          .where((a) => a.status?.toLowerCase() == 'further_action_pending')
          .length,
      staffReviewPending: applicants
          .where((a) => a.status?.toLowerCase() == 'staff_review_pending')
          .length,
      complete: applicants
          .where((a) => a.status?.toLowerCase() == 'complete')
          .length,
    );

    state = state.copyWith(stats: stats);
  }

  Future<void> searchApplicants(String query) async {
    await loadApplicants(
      page: 1,
      perPage: state.itemsPerPage,
      search: query.isEmpty ? null : query,
      statusFilter: state.statusFilter,
      append: false,
    );
  }

  Future<void> filterByStatus(String? statusFilter) async {
    await loadApplicants(
      page: 1,
      perPage: state.itemsPerPage,
      search: state.searchQuery.isEmpty ? null : state.searchQuery,
      statusFilter: statusFilter,
      append: false,
    );
  }

  Future<void> searchAndFilter(String? search, String? statusFilter) async {
    await loadApplicants(
      page: 1,
      perPage: state.itemsPerPage,
      search: search,
      statusFilter: statusFilter,
      append: false,
    );
  }

  void sortApplicants(int columnIndex, bool ascending) {
    state = state.copyWith(
      sortColumnIndex: columnIndex,
      isAscending: ascending,
    );

    final sortedApplicants = List<ClientApplicant>.from(
      state.filteredApplicants,
    );

    switch (columnIndex) {
      case 0:
        sortedApplicants.sort(
          (a, b) => ascending
              ? a.fullName.compareTo(b.fullName)
              : b.fullName.compareTo(a.fullName),
        );
        break;
      case 1:
        sortedApplicants.sort(
          (a, b) => ascending
              ? (a.email ?? '').compareTo(b.email ?? '')
              : (b.email ?? '').compareTo(a.email ?? ''),
        );
        break;
      case 2:
        sortedApplicants.sort(
          (a, b) => ascending
              ? (a.organization ?? '').compareTo(b.organization ?? '')
              : (b.organization ?? '').compareTo(a.organization ?? ''),
        );
        break;
      case 3:
        sortedApplicants.sort(
          (a, b) => ascending
              ? (a.status ?? '').compareTo(b.status ?? '')
              : (b.status ?? '').compareTo(a.status ?? ''),
        );
        break;
      case 4:
        sortedApplicants.sort(
          (a, b) => ascending
              ? (a.createdAt ?? '').compareTo(b.createdAt ?? '')
              : (b.createdAt ?? '').compareTo(a.createdAt ?? ''),
        );
        break;
    }

    state = state.copyWith(filteredApplicants: sortedApplicants);
  }

  Future<void> refresh() async {
    state = state.copyWith(
      currentPage: 1,
      applicants: [],
      filteredApplicants: [],
    );
    await initialize();
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  Future<void> debugApiResponse() async {
    try {
      final result = await _getApplicantsUseCase.executeWithPagination(
        page: 1,
        perPage: 5,
      );

      if (result.data != null && result.data!.isNotEmpty) {}
    } catch (e) {
      // Ignore errors during applicant loading
    }
  }

  void applySortAndFilter(
    SortOption sortOption,
    Set<StatusFilter> statusFilters,
    Set<String> organizationFilters,
  ) {
    List<ClientApplicant> filtered = List.from(state.applicants);

    if (statusFilters.isNotEmpty) {
      filtered = filtered.where((applicant) {
        final status = applicant.status?.toLowerCase() ?? '';
        return statusFilters.any((filter) {
          switch (filter) {
            case StatusFilter.withApplicant:
              return status.contains('with applicant') ||
                  status == 'in_progress';
            case StatusFilter.withEmployer:
              return status.contains('with employer');
            case StatusFilter.notStarted:
              return status == 'new' || status.contains('not started');
            case StatusFilter.processing:
              return status.contains('processing');
            case StatusFilter.done:
              return status == 'complete' || status == 'done';
            case StatusFilter.all:
              return true;
          }
        });
      }).toList();
    }

    if (organizationFilters.isNotEmpty) {
      filtered = filtered.where((applicant) {
        return organizationFilters.contains(
          applicant.organization ?? 'Unknown',
        );
      }).toList();
    }

    switch (sortOption) {
      case SortOption.dateLatest:
        filtered.sort((a, b) {
          final dateA =
              DateTime.tryParse(a.dateCreated ?? a.createdAt ?? '') ??
              DateTime(1970);
          final dateB =
              DateTime.tryParse(b.dateCreated ?? b.createdAt ?? '') ??
              DateTime(1970);
          return dateB.compareTo(dateA);
        });
        break;
      case SortOption.dateOldest:
        filtered.sort((a, b) {
          final dateA =
              DateTime.tryParse(a.dateCreated ?? a.createdAt ?? '') ??
              DateTime(1970);
          final dateB =
              DateTime.tryParse(b.dateCreated ?? b.createdAt ?? '') ??
              DateTime(1970);
          return dateA.compareTo(dateB);
        });
        break;
      case SortOption.alphabeticalAZ:
        filtered.sort((a, b) => a.fullName.compareTo(b.fullName));
        break;
      case SortOption.alphabeticalZA:
        filtered.sort((a, b) => b.fullName.compareTo(a.fullName));
        break;
    }

    state = state.copyWith(filteredApplicants: filtered);
  }
}

final dashboardRepositoryProvider = Provider<DashboardRepository>((ref) {
  final authRepository = ref.read(authProvider);
  return DashboardRepository(authRepository);
});

final getApplicantsUseCaseProvider = Provider<GetApplicantsUseCase>((ref) {
  final repository = ref.read(dashboardRepositoryProvider);
  return GetApplicantsUseCase(repository);
});

final getDashboardStatsUseCaseProvider = Provider<GetDashboardStatsUseCase>((
  ref,
) {
  final repository = ref.read(dashboardRepositoryProvider);
  return GetDashboardStatsUseCase(repository);
});

final dashboardViewModelProvider =
    StateNotifierProvider<DashboardViewModel, DashboardState>((ref) {
      final getApplicantsUseCase = ref.read(getApplicantsUseCaseProvider);
      final getDashboardStatsUseCase = ref.read(
        getDashboardStatsUseCaseProvider,
      );

      return DashboardViewModel(getApplicantsUseCase, getDashboardStatsUseCase);
    });
