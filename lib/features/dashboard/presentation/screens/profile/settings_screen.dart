import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/auth/data/repositories/auth.dart';
import 'package:SolidCheck/features/auth/di/auth_providers.dart';
import 'package:SolidCheck/features/dashboard/presentation/widgets/profile/profile_menu_text_field_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

final showChangePasswordProvider = StateProvider<bool>((ref) => false);
final showTwoFactorSetupProvider = StateProvider<bool>((ref) => false);
final showVerify2FAProvider = StateProvider<bool>((ref) => false);
final showSetupCompleteProvider = StateProvider<bool>((ref) => false);
final show2FAEnabledProvider = StateProvider<bool>((ref) => false);
final isDisabling2FAProvider = StateProvider<bool>((ref) => false);

final settingsQrCodeProvider = FutureProvider<String>((ref) async {
  final auth = ref.read(authProvider);
  await auth.loadPersistedToken();
  final token = await auth.getToken();

  await auth.isLoggedIn();

  if (token == null || token.isEmpty) {
    throw Exception('You have been logged out. Please sign in to continue');
  }

  final twoFactorRepo = ref.read(twoFactorRepositoryProvider);
  final result = await twoFactorRepo.generateQrCode(token);

  return result.fold(
    (failure) => throw Exception(failure.message),
    (qrCode) => qrCode,
  );
});

final otpProvider =
    FutureProvider.autoDispose.family<String, String>((ref, otp) async {
  final auth = ref.read(authProvider);
  final token = await auth.getToken();

  await auth.loadPersistedToken();

  if (token == null || token.isEmpty) {
    throw Exception('Authentication token is missing');
  }

  return 'OTP validation completed';
});

class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen> {
  bool _isTwoFactorEnabled = false;
  Color cardBgColor = const Color(0XFFE6EDF3);

  Future<void> _loadSwitchState() async {
    final prefs = await SharedPreferences.getInstance();
    bool isTwoFactorEnabled = prefs.getBool('twoFactorEnabled') ?? false;
    bool isTwoFactorDisabled = prefs.getBool('twoFactorDisable') ?? false;

    setState(() {
      _isTwoFactorEnabled = isTwoFactorEnabled;

      if (isTwoFactorDisabled) {
        _isTwoFactorEnabled = false;
      }
    });
  }

  Future<void> _saveSwitchState(bool value) async {
    final prefs = await SharedPreferences.getInstance();

    if (value) {
      ref.read(isDisabling2FAProvider.notifier).state = false;
      ref.read(showTwoFactorSetupProvider.notifier).state = true;
      ref.read(showVerify2FAProvider.notifier).state = false;
      ref.read(showSetupCompleteProvider.notifier).state = false;
    } else {
      await prefs.setBool('twoFactorEnabled', false);
      ref.read(isDisabling2FAProvider.notifier).state = true;
      ref.read(showTwoFactorSetupProvider.notifier).state = false;
      ref.read(showVerify2FAProvider.notifier).state = true;
      ref.read(showSetupCompleteProvider.notifier).state = false;
    }
  }

  void handleOtpVerificationSuccess(WidgetRef ref) {
    final isDisabling = ref.read(isDisabling2FAProvider);
    ref.read(showVerify2FAProvider.notifier).state = false;
    ref.read(showSetupCompleteProvider.notifier).state = true;

    SharedPreferences.getInstance().then((prefs) {
      prefs.setBool('twoFactorEnabled', !isDisabling);
    });

    setState(() {
      _isTwoFactorEnabled = !isDisabling;
    });
  }

  @override
  void initState() {
    super.initState();
    _loadSwitchState();
  }

  final List<TextEditingController> otpControllers =
      List.generate(6, (_) => TextEditingController());

  // Sample login history data
  final List<Map<String, String>> loginHistory = [
    {
      'Login Time': '2024-01-15 10:30 AM',
      'Login Device': 'Windows PC',
      'Browser': 'Chrome 120.0',
    },
    {
      'Login Time': '2024-01-14 02:15 PM',
      'Login Device': 'iPhone 15',
      'Browser': 'Safari Mobile',
    },
    {
      'Login Time': '2024-01-13 09:45 AM',
      'Login Device': 'MacBook Pro',
      'Browser': 'Firefox 121.0',
    },
  ];

  Widget _buildProfileInformationUI() {
    final Size size = MediaQuery.of(context).size;
    // TODO: Implement user details with new architecture
    // final userDetailsAsync = ref.watch(userDetailsProvider);
    final isMobile = size.width < 600;

    return SizedBox(
      width: isMobile ? size.width : size.width * 0.40,
      child: _buildUserProfilePlaceholder(),
    );
  }

  Widget _buildUserProfilePlaceholder() {
    // TODO: Replace with actual user details from new architecture
    return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                spacing: 10,
                children: [
                  GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Icon(Icons.arrow_back,
                          size: 25, color: AppColors.kBlueColor)),
                  Text(
                    'Profile Information',
                    style: TextStyle(
                        fontSize: 22.0,
                        fontWeight: FontWeight.w600,
                        color: AppColors.kBlueColor),
                  ),
                ],
              ),
              const SizedBox(height: 15),
              const ProfileMenuTextFieldWidget(
                  isReadonly: true,
                  label: 'Full Name:',
                  hintText: 'John Smith'),
              const ProfileMenuTextFieldWidget(
                  isReadonly: true,
                  label: 'Phone Number:',
                  hintText: '+44 123 123'),
              const ProfileMenuTextFieldWidget(
                  isReadonly: true,
                  label: 'Email Address:',
                  hintText: '<EMAIL>'),
              const ProfileMenuTextFieldWidget(
                  isReadonly: true,
                  label: 'Associated Company:',
                  hintText: 'Company'),
              const SizedBox(height: 30),
              Text(
                'Password Management',
                style: TextStyle(
                    fontSize: 22.0,
                    fontWeight: FontWeight.w600,
                    color: AppColors.kBlueColor),
              ),
              const SizedBox(height: 10),
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                const Text('Two Factor Authentication',
                    style: TextStyle(fontSize: 16)),
                Switch(
                  value: _isTwoFactorEnabled,
                  onChanged: (value) {
                    setState(() {
                      _isTwoFactorEnabled = value;
                    });
                    _saveSwitchState(value);
                    if (value) {
                      ref.read(showTwoFactorSetupProvider.notifier).state =
                          true;
                    } else {
                      ref.read(showTwoFactorSetupProvider.notifier).state =
                          true;
                      ref.read(showVerify2FAProvider.notifier).state = false;
                      ref.read(showSetupCompleteProvider.notifier).state =
                          false;
                    }
                  },
                ),
              ]),
              ListTile(
                contentPadding: EdgeInsets.zero,
                title: const Text(
                  'Change Password',
                  style: TextStyle(fontSize: 16),
                ),
                trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                onTap: () {
                  ref.read(showChangePasswordProvider.notifier).state = true;
                },
              ),
              const SizedBox(height: 30),
            ],
          );
  }

  @override
  Widget build(BuildContext context) {
    final showChangePassword = ref.watch(showChangePasswordProvider);
    final showTwoFactorSetup = ref.watch(showTwoFactorSetupProvider);
    final showVerify2FA = ref.watch(showVerify2FAProvider);
    final showSetupComplete = ref.watch(showSetupCompleteProvider);

    if (showChangePassword) {
      // TODO: Implement change password UI
      return const Center(child: Text('Change Password UI - TODO'));
    } else if (showTwoFactorSetup) {
      // TODO: Implement 2FA setup UI
      return const Center(child: Text('2FA Setup UI - TODO'));
    } else if (showVerify2FA) {
      // TODO: Implement 2FA verification UI
      return const Center(child: Text('2FA Verification UI - TODO'));
    } else if (showSetupComplete) {
      // TODO: Implement setup complete UI
      return const Center(child: Text('Setup Complete UI - TODO'));
    } else {
      return SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: _buildProfileInformationUI(),
      );
    }
  }
}
