import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ProfileNotificationsScreen extends ConsumerStatefulWidget {
  const ProfileNotificationsScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _ProfileNotificationsScreenState();
}

class _ProfileNotificationsScreenState
    extends ConsumerState<ProfileNotificationsScreen> {
  List<Map<String, dynamic>> notifications = [
    {'title': 'Email Notifications', 'enabled': true},
    {'title': 'Push Notifications', 'enabled': false},
    {'title': 'SMS Notifications', 'enabled': true},
    {'title': 'News and Updates', 'enabled': false},
    {'title': 'Promotional Alerts', 'enabled': true},
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 10.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 10),
            const Text(
              'Manage Notifications',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 15),
            Expanded(
              child: ListView.separated(
                itemCount: notifications.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final notification = notifications[index];
                  return _buildNotificationItem(notification, index);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationItem(Map<String, dynamic> notification, int index) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          notification['title'],
          style: const TextStyle(fontSize: 16),
        ),
        Switch(
          value: notification['enabled'],
          onChanged: (value) {
            setState(() {
              notifications[index]['enabled'] = value;
            });
          },
          activeColor: Colors.blueAccent,
        ),
      ],
    );
  }
}
