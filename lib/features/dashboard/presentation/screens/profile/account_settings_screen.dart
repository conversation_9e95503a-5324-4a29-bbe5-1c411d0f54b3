import 'package:SolidCheck/core/constants/profile_menu.dart';
import 'package:SolidCheck/core/navigation/app_router_new.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/features/dashboard/presentation/screens/profile/profile_notifications_screen.dart';
import 'package:SolidCheck/features/dashboard/presentation/screens/profile/settings_screen.dart';
import 'package:SolidCheck/shared/widgets/dynamic_sidebar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final selectedSidebarIndexProvider = StateProvider<int>((ref) => 0);

class AccountSettingsScreen extends ConsumerWidget {
  const AccountSettingsScreen({super.key});

  Widget _buildSidebarItem(
      WidgetRef ref, String assetPath, String title, int index) {
    final isSelected = index == ref.watch(selectedSidebarIndexProvider);

    return GestureDetector(
      onTap: () =>
          ref.read(selectedSidebarIndexProvider.notifier).state = index,
      child: Stack(
        children: [
          if (isSelected)
            ClipPath(
              clipper: SidebarClipper(),
              child: Container(
                height: 70,
                color: AppColors.kWhiteColor,
              ),
            ),
          Container(
            padding: const EdgeInsets.symmetric(vertical: 15.0),
            decoration: BoxDecoration(
              color:
                  isSelected ? Colors.transparent : AppColors.sideBarMenuColor,
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Row(
                children: [
                  const SizedBox(width: 20.0),
                  Icon(
                    _getIconForPath(assetPath),
                    size: 25,
                    color: isSelected
                        ? AppColors.kBlueColor
                        : AppColors.kWhiteColor,
                  ),
                  const SizedBox(width: 16.0),
                  Expanded(
                    child: Text(
                      title,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 17,
                        color: isSelected
                            ? AppColors.kBlueColor
                            : AppColors.kWhiteColor,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(int index) {
    switch (index) {
      case 0:
        return const SettingsScreen();
      case 1:
        return const ProfileNotificationsScreen();
      default:
        return const SettingsScreen();
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedSidebarIndex = ref.watch(selectedSidebarIndexProvider);

    final isResponsive = context.isDesktop;

    return Scaffold(
      body: Row(
        children: [
          if (isResponsive)
            Container(
              width: 250.0,
              color: AppColors.sideBarMenuColor,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 20.0),
                  Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: GestureDetector(
                      onTap: () {
                        // Use the dashboard routes helper method
                        AppRouter.navigateToDashboard();
                      },
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Icon(
                            Icons.arrow_back_ios,
                            color: AppColors.kWhiteColor,
                            size: 15,
                          ),
                          const SizedBox(width: 5.0),
                          Expanded(
                            child: Text('Back to Home',
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                    color: AppColors.kWhiteColor,
                                    fontSize: 20.0)),
                          )
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 20.0),
                  _buildSidebarItem(
                      ref,
                      ProfileMenuAssets.profileMenuAccountSettingSideBarIcon,
                      'Account Settings',
                      0),
                  _buildSidebarItem(
                      ref,
                      ProfileMenuAssets
                          .profileMenuAccountNotificationSideBarIcon,
                      'Notifications',
                      1),
                ],
              ),
            ),
          Expanded(flex: 6, child: _buildContent(selectedSidebarIndex)),
        ],
      ),
    );
  }

  IconData _getIconForPath(String assetPath) {
    // Map asset paths to Material Icons
    if (assetPath.contains('account_setting')) {
      return Icons.settings;
    } else if (assetPath.contains('notification')) {
      return Icons.notifications;
    } else if (assetPath.contains('back') || assetPath.contains('home')) {
      return Icons.home;
    }
    return Icons.settings; // Default fallback
  }
}
