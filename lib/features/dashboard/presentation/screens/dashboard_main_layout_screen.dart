import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/features/dashboard/presentation/screens/dashboard_overview_screen.dart';
import 'package:SolidCheck/features/dashboard/presentation/widgets/appbar/dashboard_appbar_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class DashboardMainLayoutScreen extends ConsumerStatefulWidget {
  const DashboardMainLayoutScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _DashboardMainLayoutScreenState();
}

class _DashboardMainLayoutScreenState extends ConsumerState<DashboardMainLayoutScreen> {

  @override
  Widget build(BuildContext context) {
    final selectedIndex = ref.watch(selectedIndexProvider);

    Widget screen;
    switch (selectedIndex) {
      case 0:
        screen = const DashboardOverviewScreen();
        break;
      case 1:
        screen = const Center(child: Text('Job Roles - Coming Soon'));
        break;
      case 2:
        screen = const Center(child: Text('Download Reports - Coming Soon'));
        break;
      case 3:
        screen = const Center(child: Text('Reminders - Coming Soon'));
        break;
      case 4:
        screen = const Center(child: Text('Help - Coming Soon'));
        break;
      default:
        screen = const DashboardOverviewScreen();
    }

    final isResponsive = context.isDesktop;

    return Scaffold(
      appBar: DashboardAppBarWidget(
        isAdminActive: false,
        showDrawerIcon: false,
        centertitle: false,
      ),
      bottomNavigationBar: !isResponsive
          ? BottomNavigationBar(
              selectedItemColor: AppColors.kNavItemSelected,
              unselectedItemColor: AppColors.kNavItem,
              showUnselectedLabels: true,
              currentIndex: selectedIndex,
              onTap: (index) {
                ref.read(selectedIndexProvider.notifier).state = index;
              },
              items: [
                BottomNavigationBarItem(
                    backgroundColor: AppColors.sideBarMenuColor,
                    icon: const Icon(Icons.home_outlined),
                    label: 'Overview'),
                const BottomNavigationBarItem(
                    icon: Icon(Icons.assignment_outlined), label: 'Job Roles'),
                const BottomNavigationBarItem(
                    icon: Icon(Icons.download_outlined), label: 'Downloads'),
                const BottomNavigationBarItem(
                    icon: Icon(Icons.notifications_active_outlined),
                    label: 'Reminders'),
                const BottomNavigationBarItem(
                    icon: Icon(Icons.help_outline), label: 'Help'),
              ],
            )
          : null,
      body: screen,
    );
  }
}
