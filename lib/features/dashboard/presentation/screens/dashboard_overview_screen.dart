import 'package:SolidCheck/core/navigation/app_router_new.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/features/dashboard/presentation/viewmodels/dashboard_viewmodel.dart';
import 'package:SolidCheck/features/dashboard/presentation/widgets/applicants/applicants_filter_modal_widget.dart';
import 'package:SolidCheck/features/dashboard/presentation/widgets/applicants/applicants_paginated_data_source.dart';
import 'package:SolidCheck/features/dashboard/presentation/widgets/applicants/dashboard_empty_state_widget.dart';
import 'package:SolidCheck/shared/widgets/buttons/solid_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class DashboardOverviewScreen extends ConsumerStatefulWidget {
  const DashboardOverviewScreen({super.key});

  @override
  ConsumerState<DashboardOverviewScreen> createState() =>
      _DashboardOverviewScreenState();
}

class _DashboardOverviewScreenState
    extends ConsumerState<DashboardOverviewScreen> {
  final TextEditingController _searchController = TextEditingController();

  SortOption _currentSort = SortOption.dateLatest;
  Set<StatusFilter> _selectedStatuses = {};
  Set<String> _selectedOrganizations = {};

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        ref.read(dashboardViewModelProvider.notifier).initialize();
      } catch (error) {
        // Ignore initialization errors
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    final query = _searchController.text;
    ref.read(dashboardViewModelProvider.notifier).searchApplicants(query);
  }

  void _showFilterModal() {
    final dashboardState = ref.read(dashboardViewModelProvider);
    final availableOrganizations = dashboardState.applicants
        .map((a) => a.organization ?? 'Unknown')
        .toSet()
        .toList();

    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: ApplicantsFilterModalWidget(
          currentSort: _currentSort,
          selectedStatuses: _selectedStatuses,
          selectedOrganizations: _selectedOrganizations,
          availableOrganizations: availableOrganizations,
          onSortChanged: (sort) {
            setState(() {
              _currentSort = sort;
            });
            _applySortAndFilter();
          },
          onStatusFilterChanged: (statuses) {
            setState(() {
              _selectedStatuses = statuses;
            });
          },
          onOrganizationFilterChanged: (organizations) {
            setState(() {
              _selectedOrganizations = organizations;
            });
          },
          onApply: _applySortAndFilter,
          onReset: () {
            setState(() {
              _currentSort = SortOption.dateLatest;
              _selectedStatuses.clear();
              _selectedOrganizations.clear();
            });
            _applySortAndFilter();
          },
        ),
      ),
    );
  }

  void _applySortAndFilter() {
    ref
        .read(dashboardViewModelProvider.notifier)
        .applySortAndFilter(
          _currentSort,
          _selectedStatuses,
          _selectedOrganizations,
        );
  }

  @override
  Widget build(BuildContext context) {
    final dashboardState = ref.watch(dashboardViewModelProvider);
    final isResponsive = context.isDesktop;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: RefreshIndicator(
        onRefresh: () =>
            ref.read(dashboardViewModelProvider.notifier).refresh(),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (dashboardState.stats != null)
                _buildStatsSection(dashboardState.stats!, isResponsive),

              if (dashboardState.stats != null) const SizedBox(height: 24),

              _buildSearchAndActionSection(isResponsive),
              const SizedBox(height: 24),

              Expanded(child: _buildContent(dashboardState, isResponsive)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatsSection(stats, bool isResponsive) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: isResponsive
          ? Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total Applicants',
                    '${stats.totalApplicants ?? 0}',
                    AppColors.kBlueColor,
                    Icons.people,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    'In Progress',
                    '${stats.inProgress ?? 0}',
                    AppColors.kBlueColor,
                    Icons.hourglass_empty,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    'Pending Review',
                    '${stats.furtherActionPending ?? 0}',
                    AppColors.kBlueColor,
                    Icons.warning,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    'Staff Review',
                    '${stats.staffReviewPending ?? 0}',
                    AppColors.kBlueColor,
                    Icons.rate_review,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    'Complete',
                    '${stats.complete ?? 0}',
                    AppColors.kBlueColor,
                    Icons.check_circle,
                  ),
                ),
              ],
            )
          : Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        'Total Applicants',
                        '${stats.totalApplicants ?? 0}',
                        AppColors.kBlueColor,
                        Icons.people,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildStatCard(
                        'In Progress',
                        '${stats.inProgress ?? 0}',
                        AppColors.kBlueColor,
                        Icons.hourglass_empty,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildStatCard(
                        'Pending Review',
                        '${stats.furtherActionPending ?? 0}',
                        AppColors.kBlueColor,
                        Icons.warning,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildStatCard(
                        'Staff Review',
                        '${stats.staffReviewPending ?? 0}',
                        AppColors.kBlueColor,
                        Icons.rate_review,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                _buildStatCard(
                  'Complete',
                  '${stats.complete ?? 0}',
                  AppColors.kBlueColor,
                  Icons.check_circle,
                ),
              ],
            ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [color.withValues(alpha: 0.1), color.withValues(alpha: 0.05)],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Icon(icon, color: color, size: 24),
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(
              color: Colors.black,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndActionSection(bool isResponsive) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search for applicant',
                prefixIcon: Icon(
                  Icons.search,
                  color: AppColors.kBlueColor,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey.withValues(alpha: 0.05),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.kBlueColor),
              borderRadius: BorderRadius.circular(8),
            ),
            child: IconButton(
              onPressed: _showFilterModal,
              icon: Icon(Icons.tune, color: AppColors.kBlueColor),
              tooltip: 'Filter & Sort',
            ),
          ),
          const SizedBox(width: 16),
          SolidButton(
            text: 'Add Applicant',
            icon: Icons.add,
            type: SolidButtonType.primary,
            size: isResponsive ? SolidButtonSize.medium : SolidButtonSize.large,
            onPressed: () {
              AppRouter.navigateToAddApplicant();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildContent(DashboardState dashboardState, bool isResponsive) {
    if (dashboardState.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (dashboardState.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.kBlueColor,
            ),
            const SizedBox(height: 16),
            const Text(
              'Error loading applicants',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              dashboardState.error!,
              style: const TextStyle(color: Colors.black87),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SolidButton(
                      text: 'Retry',
                      type: SolidButtonType.outline,
                      onPressed: () {
                        ref.read(dashboardViewModelProvider.notifier).refresh();
                      },
                    ),
                    const SizedBox(width: 16),
                    SolidButton(
                      text: 'Check Server',
                      type: SolidButtonType.outline,
                      onPressed: () async {
                        final messenger = ScaffoldMessenger.of(context);
                        final viewModel = ref.read(dashboardViewModelProvider.notifier);
                        final isHealthy = await viewModel.checkServerHealth();
                        messenger.showSnackBar(
                          SnackBar(
                            content: Text(
                              isHealthy
                                ? 'Server is running and healthy'
                                : 'Server is not responding. Please check if it\'s running on port 8001.',
                            ),
                            backgroundColor: isHealthy ? Colors.green : Colors.red,
                          ),
                        );
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                SolidButton(
                  text: 'Load Sample Data',
                  type: SolidButtonType.primary,
                  onPressed: () {
                    ref
                        .read(dashboardViewModelProvider.notifier)
                        .loadSampleData();
                  },
                ),
              ],
            ),
          ],
        ),
      );
    }

    if (dashboardState.filteredApplicants.isEmpty) {
      return const DashboardEmptyStateWidget();
    }

    return ApplicantsPaginatedDataTableWidget(
      onSort: (columnIndex, ascending) {
        ref
            .read(dashboardViewModelProvider.notifier)
            .sortApplicants(columnIndex, ascending);
      },
    );
  }
}
