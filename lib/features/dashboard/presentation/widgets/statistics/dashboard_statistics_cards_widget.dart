import 'package:SolidCheck/core/theme/colors.dart';
import 'package:flutter/material.dart';

class DashboardStatisticsCardsWidget extends StatelessWidget {
  final int totalApplicants;
  final int inProgress;
  final int furtherActionPending;
  final int staffReviewPending;
  final int complete;

  const DashboardStatisticsCardsWidget({
    super.key,
    required this.totalApplicants,
    required this.inProgress,
    required this.furtherActionPending,
    required this.staffReviewPending,
    required this.complete,
  });

  @override
  Widget build(BuildContext context) {
    final isResponsive = MediaQuery.of(context).size.width > 768;
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: isResponsive
          ? Row(
              children: [
                Flexible(child: _buildStatCard('Total Applicants', totalApplicants, AppColors.kBlueColor, Icons.people)),
                const SizedBox(width: 16),
                Flexible(child: _buildStatCard('In Progress', inProgress, Colors.orange, Icons.hourglass_empty)),
                const SizedBox(width: 16),
                Flexible(child: _buildStatCard('Further Action\nPending', furtherActionPending, Colors.amber, Icons.warning)),
                const SizedBox(width: 16),
                Flexible(child: _buildStatCard('Staff Review\nPending', staffReviewPending, Colors.purple, Icons.rate_review)),
                const SizedBox(width: 16),
                Flexible(child: _buildStatCard('Complete', complete, Colors.green, Icons.check_circle)),
              ],
            )
          : Column(
              children: [
                Row(
                  children: [
                    Expanded(child: _buildStatCard('Total Applicants', totalApplicants, AppColors.kBlueColor, Icons.people)),
                    const SizedBox(width: 16),
                    Expanded(child: _buildStatCard('In Progress', inProgress, Colors.orange, Icons.hourglass_empty)),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(child: _buildStatCard('Further Action\nPending', furtherActionPending, Colors.amber, Icons.warning)),
                    const SizedBox(width: 16),
                    Expanded(child: _buildStatCard('Staff Review\nPending', staffReviewPending, Colors.purple, Icons.rate_review)),
                  ],
                ),
                const SizedBox(height: 16),
                _buildStatCard('Complete', complete, Colors.green, Icons.check_circle),
              ],
            ),
    );
  }

  Widget _buildStatCard(String title, int count, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color.withValues(alpha: 0.1),
            color.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
              ),
              Flexible(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    count.toString().padLeft(2, '0'),
                    style: TextStyle(
                      color: color,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Flexible(
            child: Text(
              title,
              style: TextStyle(
                color: AppColors.kBlackColor.withValues(alpha: 0.7),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
