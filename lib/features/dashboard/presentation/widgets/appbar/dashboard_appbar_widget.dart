import 'dart:ui';

import 'package:SolidCheck/core/navigation/app_router_new.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/auth/di/auth_providers.dart';
import 'package:SolidCheck/features/dashboard/presentation/widgets/appbar/sign_out_dialog_widget.dart';
import 'package:SolidCheck/shared/utils/responsive.dart';
import 'package:SolidCheck/shared/widgets/custom_snakbar.dart';
import 'package:SolidCheck/shared/widgets/logo/solid_check_logo_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

final selectedIndexProvider = StateProvider<int>((ref) => 0);

class DashboardAppBarWidget extends ConsumerWidget
    implements PreferredSizeWidget {
  DashboardAppBarWidget({
    super.key,
    required this.isAdminActive,
    this.scaffoldKey,
    this.showDrawerIcon = false,
    this.centertitle = false,
    this.automaticallyImplyLeading = true,
    this.onDrawerIconPressed,
    this.shownav = true,
  });

  final bool automaticallyImplyLeading;
  final bool isAdminActive;
  final VoidCallback? onDrawerIconPressed;
  final GlobalKey<ScaffoldState>? scaffoldKey;
  final bool showDrawerIcon;
  final bool centertitle;
  final bool shownav;
  final List<String> userNavBarItems = [
    'Overview',
    'Job Roles',
    'Download Reports',
    'Reminders',
    'Help',
  ];

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  String _getInitials(String? firstName, String? lastName) {
    final first = firstName?.isNotEmpty == true
        ? firstName![0].toUpperCase()
        : 'U';
    final last = lastName?.isNotEmpty == true ? lastName![0].toUpperCase() : '';
    return '$first$last';
  }

  Widget _buildNavBarItems(
    BuildContext context,
    WidgetRef ref,
    int selectedIndex,
  ) {
    final items = userNavBarItems;

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: items.asMap().entries.map((entry) {
          final isSelected = entry.key == selectedIndex;

          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                child: Container(
                  decoration: BoxDecoration(
                    gradient: isSelected
                        ? LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              AppColors.kBlueColor.withValues(alpha: 0.9),
                              AppColors.kBlueDarkColor.withValues(alpha: 0.8),
                            ],
                          )
                        : LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Colors.white.withValues(alpha: 0.15),
                              Colors.white.withValues(alpha: 0.08),
                            ],
                          ),
                    border: Border.all(
                      color: isSelected
                          ? AppColors.kBlueColor.withValues(alpha: 0.6)
                          : Colors.white.withValues(alpha: 0.2),
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(12),
                      onTap: () {
                        ref.read(selectedIndexProvider.notifier).state =
                            entry.key;

                        if (entry.key == 0) {
                          _handleOverviewNavigation(context, ref);
                        }
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                        child: Text(
                          entry.value,
                          style: TextStyle(
                            color: isSelected ? Colors.white : Colors.black,
                            fontSize: 14,
                            fontWeight: isSelected
                                ? FontWeight.w600
                                : FontWeight.w500,
                            shadows: isSelected
                                ? null
                                : [
                                    Shadow(
                                      offset: const Offset(0, 1),
                                      blurRadius: 1,
                                      color: Colors.white.withValues(
                                        alpha: 0.5,
                                      ),
                                    ),
                                  ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  void _handleOverviewNavigation(BuildContext context, WidgetRef ref) {
    final currentRoute = ModalRoute.of(context)?.settings.name;

    if (currentRoute == '/dashboard') {
      return;
    }

    if (currentRoute != null &&
        (currentRoute.contains('/application/') ||
            currentRoute.contains('/dbs/') ||
            currentRoute.contains('/applicant-detail') ||
            currentRoute.contains('/applicant-dashboard'))) {
      AppRouter.navigateToDashboard();
      return;
    }
  }

  Future<void> _showProfileDropdownMenu(
    BuildContext context,
    GlobalKey key,
    String userName,
    String userEmail,
    userDetails,
  ) async {
    final user = userDetails?.user;
    final profile = user?.profile?.isNotEmpty == true
        ? user!.profile!.first
        : null;
    final RenderBox renderBox =
        key.currentContext!.findRenderObject() as RenderBox;
    final offset = renderBox.localToGlobal(Offset.zero);
    final height = renderBox.size.height;

    await showMenu<int>(
      context: context,
      position: RelativeRect.fromLTRB(
        offset.dx,
        offset.dy + height,
        offset.dx,
        offset.dy,
      ),
      items: [
        PopupMenuItem(
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: AppColors.kBlueColor,
              child: Center(
                child: Text(
                  _getInitials(profile?.firstName, profile?.lastName),
                  style: TextStyle(color: AppColors.kWhiteColor),
                ),
              ),
            ),
            title: Text(userName),
            subtitle: Text(userEmail),
          ),
        ),
        PopupMenuItem<int>(
          value: 0,
          child: const Text('Account Settings'),
          onTap: () {
            AppRouter.navigateToAccountSettings();
          },
        ),
        PopupMenuItem<int>(
          value: 1,
          child: const Text('Notifications'),
          onTap: () {},
        ),
        PopupMenuItem<int>(
          onTap: () async {
            final shouldLogout = await buildLogoutConfirmationDialogAppBar(
              context,
            );
            if (shouldLogout) {
              Future.microtask(() async {
                try {
                  final container = ProviderScope.containerOf(
                    context,
                    listen: false,
                  );
                  final authViewModel = container.read(
                    authViewModelProvider.notifier,
                  );

                  await authViewModel.logout();

                  final prefs = await SharedPreferences.getInstance();
                  await prefs.remove('tempEmail');
                  await prefs.remove('tempPassword');
                  await prefs.remove('qrCodeUrl');
                  await prefs.remove('qrCodeImage');
                  await prefs.remove('needsPinVerification');
                  await prefs.remove('requiresTwoFactorSetup');

                  if (context.mounted) {
                    AppRouter.navigateToLogin();
                  }
                } catch (error) {
                  if (context.mounted) {
                    CustomSnackBar.show(
                      context: context,
                      message: 'Failed to log out: $error',
                      backgroundColor: AppColors.kRedColor,
                      textColor: Colors.white,
                      icon: Icons.info,
                      duration: const Duration(seconds: 3),
                    );
                  }
                }
              });
            }
          },
          value: 2,
          child: Text(
            'Log Out',
            style: TextStyle(color: AppColors.kRedColor),
          ),
        ),
      ],
      elevation: 8.0,
      color: AppColors.kWhiteColor,
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedIndex = ref.watch(selectedIndexProvider);
    final isMobile = ResponsiveUtil.isMobile(context);
    final GlobalKey profileButtonKey = GlobalKey();

    return PreferredSize(
      preferredSize: const Size.fromHeight(kToolbarHeight),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white.withValues(alpha: 0.1),
                  Colors.white.withValues(alpha: 0.05),
                ],
              ),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(20),
                bottomRight: Radius.circular(20),
              ),
            ),
            child: AppBar(
              automaticallyImplyLeading: false,
              backgroundColor: Colors.transparent,
              elevation: 0,
              key: scaffoldKey,
              title: isMobile
                  ? const SolidCheckHorizontalLogo(height: 120.0, width: 120.0)
                  : Row(
                      children: [
                        const SolidCheckHorizontalLogo(
                          height: 110.0,
                          width: 110.0,
                        ),
                        const SizedBox(width: 20.0),
                        if (shownav)
                          Flexible(
                            child: _buildNavBarItems(
                              context,
                              ref,
                              selectedIndex,
                            ),
                          ),
                      ],
                    ),
              centerTitle: centertitle,
              actions: [
                if (!isMobile)
                  InkWell(
                    key: profileButtonKey,
                    onTap: () {
                      _showProfileDropdownMenu(
                        context,
                        profileButtonKey,
                        "User",
                        "<EMAIL>",
                        null,
                      );
                    },
                    child: Row(
                      children: [
                        CircleAvatar(
                          backgroundColor: AppColors.kBlueColor,
                          child: Center(
                            child: Text(
                              "U",
                              style: TextStyle(color: AppColors.kWhiteColor),
                            ),
                          ),
                        ),
                        const SizedBox(width: 10.0),
                        const Text(
                          "User",
                          style: TextStyle(
                            fontSize: 15.0,
                            fontWeight: FontWeight.normal,
                            color: Colors.black,
                          ),
                        ),
                        Icon(
                          Icons.keyboard_arrow_down_outlined,
                          color: AppColors.kBlueColor,
                        ),
                        const SizedBox(width: 10.0),
                      ],
                    ),
                  )
                else
                  GestureDetector(
                    key: profileButtonKey,
                    onTap: () {
                      _showProfileDropdownMenu(
                        context,
                        profileButtonKey,
                        "User",
                        "<EMAIL>",
                        null,
                      );
                    },
                    child: CircleAvatar(
                      backgroundColor: AppColors.kBlueColor,
                      child: Center(
                        child: Text(
                          "U",
                          style: TextStyle(color: AppColors.kWhiteColor),
                        ),
                      ),
                    ),
                  ),
                const SizedBox(width: 5.0),
              ],
              leading: automaticallyImplyLeading && showDrawerIcon
                  ? IconButton(
                      icon: Icon(
                        Icons.menu_outlined,
                        color: AppColors.kBlueColor,
                      ),
                      onPressed: onDrawerIconPressed,
                    )
                  : null,
            ),
          ),
        ),
      ),
    );
  }
}
