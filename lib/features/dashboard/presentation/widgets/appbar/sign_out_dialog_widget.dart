import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

Future<bool> buildLogoutConfirmationDialogAppBar(BuildContext context) async {
  return await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext dialogContext) {
          return AlertDialog(
            shape: Theme.of(context).cardTheme.shape,
            title: const Text('Confirm Logout'),
            content: const Text('Are you sure you want to log out?'),
            actions: <Widget>[
              TextButton(
                child: const Text('Cancel'),
                onPressed: () {
                  Navigator.of(dialogContext).pop(false);
                },
              ),
              TextButton(
                child: const Text('Sign Out'),
                onPressed: () async {
                  final navigator = Navigator.of(dialogContext);
                  try {
                    final prefs = await SharedPreferences.getInstance();
                    // Clear legacy preferences safely
                    await prefs.setBool('is2FACompleted', false);
                    await prefs.setBool('isQrCodeScan', false);
                    await prefs.remove('token'); // Legacy token key

                    // Clear new auth system preferences
                    await prefs.remove('tempEmail');
                    await prefs.remove('tempPassword');
                    await prefs.remove('qrCodeUrl');
                    await prefs.remove('qrCodeImage');
                    await prefs.remove('needsPinVerification');
                    await prefs.remove('requiresTwoFactorSetup');
                  } catch (e) {
                    // If SharedPreferences fails, still allow logout
                  }
                  navigator.pop(true);
                },
              ),
            ],
          );
        },
      ) ??
      false;
}
