import 'package:SolidCheck/core/theme/colors.dart';
import 'package:flutter/material.dart';

class ProfileMenuTextFieldWidget extends StatefulWidget {
  final String label;
  final String hintText;
  final bool isForgetPasswordNeeded;
  final bool? isReadonly;
  final bool obscureText;
  final Widget? suffixIcon;
  final TextEditingController? controller;
  final Function(String)? onChanged;

  const ProfileMenuTextFieldWidget({
    super.key,
    required this.label,
    required this.hintText,
    this.isForgetPasswordNeeded = false,
    this.isReadonly,
    this.obscureText = false,
    this.suffixIcon,
    this.controller,
    this.onChanged,
  });

  @override
  ProfileMenuTextFieldWidgetState createState() => ProfileMenuTextFieldWidgetState();
}

class ProfileMenuTextFieldWidgetState extends State<ProfileMenuTextFieldWidget> {
  @override
  void dispose() {
    widget.controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          widget.isForgetPasswordNeeded
              ? Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      widget.label,
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: AppColors.kBlackColor,
                      ),
                    ),
                    TextButton(
                      onPressed: () {},
                      child: Text(
                        'Forget Password?',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 12.0,
                          color: AppColors.kBlueColor,
                        ),
                      ),
                    ),
                  ],
                )
              : Text(
                  widget.label,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: AppColors.kBlackColor,
                  ),
                ),
          const SizedBox(height: 5.0),
          TextFormField(
            readOnly: widget.isReadonly ?? false,
            controller: widget.controller,
            onChanged: widget.onChanged,
            obscureText: widget.obscureText,
            decoration: InputDecoration(
              errorStyle:
                  TextStyle(color: AppColors.kRedColor, height: 0),
              hintText: widget.hintText,
              hintStyle: TextStyle(
                color: AppColors.defaultStateTextColor,
              ),
              suffixIcon: widget.suffixIcon,
              filled: true,
              fillColor: AppColors.kWhiteColor,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: AppColors.transparentColor,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10.0),
                borderSide: BorderSide(
                  color: AppColors.activeStateFieldOutlineColor,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10.0),
                borderSide: BorderSide(
                  color: AppColors.kProfileMenuFieldFillAndStrokeColor,
                ),
              ),
            ),
            style: TextStyle(color: AppColors.activeStateFieldTextColor),
          ),
        ],
      ),
    );
  }
}
