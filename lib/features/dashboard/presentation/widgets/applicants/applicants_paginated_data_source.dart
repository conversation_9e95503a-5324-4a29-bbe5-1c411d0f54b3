import 'package:SolidCheck/core/navigation/app_router_new.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/features/dashboard/data/models/client_applicants.dart';
import 'package:SolidCheck/features/dashboard/presentation/viewmodels/dashboard_viewmodel.dart';
import 'package:SolidCheck/features/dashboard/utils/check_icons_util.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ApplicantsPaginatedDataSource extends DataTableSource {
  final WidgetRef ref;
  final List<ClientApplicant> applicants;
  final int totalItems;
  final bool isLoading;
  final BuildContext context;

  ApplicantsPaginatedDataSource({
    required this.ref,
    required this.applicants,
    required this.totalItems,
    required this.isLoading,
    required this.context,
  });

  @override
  DataRow? getRow(int index) {
    // Only return rows for actual data, not empty rows
    if (index >= applicants.length) {
      return null;
    }

    final applicant = applicants[index];
    final isMobile = ResponsiveHelper.isMobile(context);
    final isTablet = ResponsiveHelper.isTablet(context);

    return DataRow(
      cells: _buildResponsiveCells(applicant, isMobile, isTablet),
      color: WidgetStateProperty.resolveWith<Color?>(
        (Set<WidgetState> states) {
          if (states.contains(WidgetState.hovered)) {
            return AppColors.kBlueColor.withValues(alpha: 0.08);
          }
          if (states.contains(WidgetState.selected)) {
            return AppColors.kBlueColor.withValues(alpha: 0.12);
          }
          return index.isEven
              ? Colors.grey.withValues(alpha: 0.02)
              : Colors.white;
        },
      ),
    );
  }

  List<DataCell> _buildResponsiveCells(ClientApplicant applicant, bool isMobile, bool isTablet) {
    final cells = <DataCell>[];

    void navigateToApplicant() {
      if (applicant.applicantId != null) {
        AppRouter.navigateToApplicantDetail(applicant.applicantId.toString());
      }
    }

    // Always show Applicant cell (clickable)
    cells.add(DataCell(
      InkWell(
        onTap: navigateToApplicant,
        child: Row(
          children: [
            CircleAvatar(
              radius: isMobile ? 14 : 16,
              backgroundColor: AppColors.kBlueColor.withValues(alpha: 0.1),
              child: Text(
                applicant.fullName.isNotEmpty
                    ? applicant.fullName[0].toUpperCase()
                    : 'A',
                style: TextStyle(
                  color: AppColors.kBlueColor,
                  fontWeight: FontWeight.w600,
                  fontSize: isMobile ? 12 : 14,
                ),
              ),
            ),
            SizedBox(width: isMobile ? 8 : 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    applicant.fullName,
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: isMobile ? 13 : 14,
                      color: AppColors.kBlackColor,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (applicant.email != null && !isMobile)
                    Text(
                      applicant.email!,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
      onTap: navigateToApplicant,
    ));

    // Show Reference on tablet and desktop (clickable)
    if (!isMobile) {
      cells.add(DataCell(
        InkWell(
          onTap: navigateToApplicant,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Text(
              applicant.reference ?? '',
              overflow: TextOverflow.ellipsis,
              style: TextStyle(fontSize: isTablet ? 13 : 14),
            ),
          ),
        ),
        onTap: navigateToApplicant,
      ));
    }

    // Show Organization on desktop only (clickable)
    if (!isMobile && !isTablet) {
      cells.add(DataCell(
        InkWell(
          onTap: navigateToApplicant,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Text(
              applicant.organization ?? '',
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
        onTap: navigateToApplicant,
      ));
    }

    // Always show Date Created (clickable)
    cells.add(DataCell(
      InkWell(
        onTap: navigateToApplicant,
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Text(
            _formatDate(applicant.dateCreated ?? applicant.createdAt),
            style: TextStyle(fontSize: isMobile ? 12 : 14),
          ),
        ),
      ),
      onTap: navigateToApplicant,
    ));

    // Show Requested Checks on tablet and desktop (clickable)
    if (!isMobile) {
      cells.add(DataCell(
        InkWell(
          onTap: navigateToApplicant,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: _buildRequestedChecks(applicant.requestedChecks ?? []),
          ),
        ),
        onTap: navigateToApplicant,
      ));
    }

    // Always show Status (clickable)
    cells.add(DataCell(
      InkWell(
        onTap: navigateToApplicant,
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: _buildStatusBadge(applicant.status, applicant.displayStatus),
        ),
      ),
      onTap: navigateToApplicant,
    ));

    return cells;
  }

  @override
  int get rowCount => applicants.length;

  @override
  bool get isRowCountApproximate => false;

  @override
  int get selectedRowCount => 0;

  Widget _buildRequestedChecks(List<RequestedCheck> checks) {
    if (checks.isEmpty) {
      return const Text('-');
    }

    return Wrap(
      spacing: 6,
      runSpacing: 6,
      children: [
        ...checks.take(3).map((check) {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
            decoration: BoxDecoration(
              color: CheckIconsUtil.getCheckBackgroundColor(check),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: CheckIconsUtil.getCheckBorderColor(check),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                CheckIconsUtil.getCheckIcon(check, size: 14),
                const SizedBox(width: 6),
                Text(
                  CheckIconsUtil.getCheckAbbreviation(check),
                  style: TextStyle(
                    fontSize: 11,
                    color: CheckIconsUtil.getCheckColor(check),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          );
        }),
        if (checks.length > 3)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.grey.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.more_horiz,
                  size: 14,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  '+${checks.length - 3}',
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey[700],
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildStatusBadge(String? status, String? displayStatus) {
    Color backgroundColor;
    Color textColor;
    String text = displayStatus ?? status ?? 'Unknown';

    switch (status?.toLowerCase()) {
      case 'new':
        backgroundColor = Colors.blue.withValues(alpha: 0.1);
        textColor = Colors.blue;
        break;
      case 'in_progress':
        backgroundColor = Colors.orange.withValues(alpha: 0.1);
        textColor = Colors.orange;
        break;
      case 'further_action_pending':
        backgroundColor = Colors.red.withValues(alpha: 0.1);
        textColor = Colors.red;
        break;
      case 'staff_review_pending':
        backgroundColor = Colors.purple.withValues(alpha: 0.1);
        textColor = Colors.purple;
        break;
      case 'complete':
        backgroundColor = Colors.green.withValues(alpha: 0.1);
        textColor = Colors.green;
        break;
      default:
        backgroundColor = Colors.grey.withValues(alpha: 0.1);
        textColor = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12,
          color: textColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  String _formatDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) return '-';

    try {
      final date = DateTime.parse(dateString);
      // Simple date formatting without intl package
      final months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                     'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      return '${months[date.month - 1]} ${date.day.toString().padLeft(2, '0')}, ${date.year}';
    } catch (e) {
      return dateString;
    }
  }
}

class ApplicantsPaginatedDataTableWidget extends ConsumerStatefulWidget {
  final Function(int, bool) onSort;

  const ApplicantsPaginatedDataTableWidget({
    super.key,
    required this.onSort,
  });

  @override
  ConsumerState<ApplicantsPaginatedDataTableWidget> createState() => _ApplicantsPaginatedDataTableWidgetState();
}

class _ApplicantsPaginatedDataTableWidgetState extends ConsumerState<ApplicantsPaginatedDataTableWidget> {
  int _rowsPerPage = 10;
  final List<int> _availableRowsPerPage = [5, 10, 15, 20, 25, 50];

  @override
  Widget build(BuildContext context) {
    final dashboardState = ref.watch(dashboardViewModelProvider);
    final isMobile = ResponsiveHelper.isMobile(context);
    final isTablet = ResponsiveHelper.isTablet(context);

    final dataSource = ApplicantsPaginatedDataSource(
      ref: ref,
      applicants: dashboardState.filteredApplicants,
      totalItems: dashboardState.totalItems,
      isLoading: dashboardState.isLoading || dashboardState.isLoadingMore,
      context: context,
    );

    // Responsive column spacing and margins
    final columnSpacing = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 8.0,
      tablet: 12.0,
      desktop: 16.0,
    );

    final horizontalMargin = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 12.0,
      tablet: 16.0,
      desktop: 20.0,
    );

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Column(
          children: [
            // Data table - Expanded to fill available space
            Expanded(
              child: PaginatedDataTable2(
                header: null,
                rowsPerPage: _rowsPerPage,
                availableRowsPerPage: _availableRowsPerPage,
                onRowsPerPageChanged: (value) {
                  setState(() {
                    _rowsPerPage = value ?? 10;
                  });
                },
                showCheckboxColumn: false,
                showFirstLastButtons: !isMobile,
                headingRowColor: WidgetStateProperty.all(Colors.grey.withValues(alpha: 0.05)),
                columnSpacing: columnSpacing,
                horizontalMargin: horizontalMargin,
                minWidth: isMobile ? 600 : 800,
                columns: _buildResponsiveColumns(context, isMobile, isTablet),
                source: dataSource,
                empty: _buildEmptyState(),
                renderEmptyRowsInTheEnd: false,
              ),
            ),
          ],
        ),
      ),
    );
  }



  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(40),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No applicants found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'There are no applicants matching your current filters.',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }



  List<DataColumn> _buildResponsiveColumns(BuildContext context, bool isMobile, bool isTablet) {
    final columns = <DataColumn>[];

    // Always show Applicant column
    columns.add(DataColumn2(
      label: const Text('Applicant'),
      size: ColumnSize.L,
      onSort: (columnIndex, ascending) => widget.onSort(0, ascending),
    ));

    // Show Reference on tablet and desktop
    if (!isMobile) {
      columns.add(DataColumn2(
        label: const Text('Reference'),
        size: ColumnSize.S,
        onSort: (columnIndex, ascending) => widget.onSort(1, ascending),
      ));
    }

    // Show Organization on desktop only
    if (!isMobile && !isTablet) {
      columns.add(DataColumn2(
        label: const Text('Organization'),
        size: ColumnSize.M,
        onSort: (columnIndex, ascending) => widget.onSort(2, ascending),
      ));
    }

    // Always show Date Created
    columns.add(DataColumn2(
      label: const Text('Date Created'),
      size: ColumnSize.S,
      onSort: (columnIndex, ascending) => widget.onSort(3, ascending),
    ));

    // Show Requested Checks on tablet and desktop
    if (!isMobile) {
      columns.add(const DataColumn2(
        label: Text('Requested Checks'),
        size: ColumnSize.M,
      ));
    }

    // Always show Status
    columns.add(DataColumn2(
      label: const Text('Status'),
      size: ColumnSize.S,
      onSort: (columnIndex, ascending) => widget.onSort(4, ascending),
    ));

    return columns;
  }
}
