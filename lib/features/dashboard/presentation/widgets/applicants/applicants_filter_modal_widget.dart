import 'package:SolidCheck/core/theme/colors.dart';
import 'package:flutter/material.dart';

enum SortOption {
  dateLatest,
  dateOldest,
  alphabeticalAZ,
  alphabeticalZA,
}

enum StatusFilter {
  all,
  withApplicant,
  withEmployer,
  notStarted,
  processing,
  done,
}

class ApplicantsFilterModalWidget extends StatefulWidget {
  final SortOption currentSort;
  final Set<StatusFilter> selectedStatuses;
  final Set<String> selectedOrganizations;
  final List<String> availableOrganizations;
  final Function(SortOption) onSortChanged;
  final Function(Set<StatusFilter>) onStatusFilterChanged;
  final Function(Set<String>) onOrganizationFilterChanged;
  final VoidCallback onApply;
  final VoidCallback onReset;

  const ApplicantsFilterModalWidget({
    super.key,
    required this.currentSort,
    required this.selectedStatuses,
    required this.selectedOrganizations,
    required this.availableOrganizations,
    required this.onSortChanged,
    required this.onStatusFilterChanged,
    required this.onOrganizationFilterChanged,
    required this.onApply,
    required this.onReset,
  });

  @override
  State<ApplicantsFilterModalWidget> createState() => _ApplicantsFilterModalWidgetState();
}

class _ApplicantsFilterModalWidgetState extends State<ApplicantsFilterModalWidget> {
  late SortOption _currentSort;
  late Set<StatusFilter> _selectedStatuses;
  late Set<String> _selectedOrganizations;

  @override
  void initState() {
    super.initState();
    _currentSort = widget.currentSort;
    _selectedStatuses = Set.from(widget.selectedStatuses);
    _selectedOrganizations = Set.from(widget.selectedOrganizations);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 400,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Sort & Filter',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppColors.kBlackColor,
                ),
              ),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close),
                style: IconButton.styleFrom(
                  backgroundColor: Colors.grey.withValues(alpha: 0.1),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Sort Section
          _buildSortSection(),
          const SizedBox(height: 24),

          // Filter Section
          _buildFilterSection(),
          const SizedBox(height: 32),

          // Action Buttons
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildSortSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Sort by:',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppColors.kBlackColor,
          ),
        ),
        const SizedBox(height: 16),
        _buildSortOption(SortOption.dateLatest, 'Date Created - Latest first'),
        _buildSortOption(SortOption.dateOldest, 'Date Created - Oldest first'),
        _buildSortOption(SortOption.alphabeticalAZ, 'Alphabetical (A-Z)'),
        _buildSortOption(SortOption.alphabeticalZA, 'Alphabetical (Z-A)'),
      ],
    );
  }

  Widget _buildSortOption(SortOption option, String title) {
    return InkWell(
      onTap: () {
        setState(() {
          _currentSort = option;
        });
        widget.onSortChanged(option);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          color: _currentSort == option 
              ? AppColors.kBlueColor.withValues(alpha: 0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: _currentSort == option 
                ? AppColors.kBlueColor
                : Colors.transparent,
          ),
        ),
        child: Row(
          children: [
            Icon(
              _currentSort == option 
                  ? Icons.radio_button_checked
                  : Icons.radio_button_unchecked,
              color: _currentSort == option 
                  ? AppColors.kBlueColor
                  : Colors.grey,
            ),
            const SizedBox(width: 12),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                color: _currentSort == option 
                    ? AppColors.kBlueColor
                    : AppColors.kBlackColor,
                fontWeight: _currentSort == option 
                    ? FontWeight.w500
                    : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Text(
                'Filter:',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppColors.kBlackColor,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        
        // Status Filter
        Text(
          'Status',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: AppColors.kBlackColor,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildStatusChip(StatusFilter.withApplicant, 'With applicant', Colors.orange),
            _buildStatusChip(StatusFilter.withEmployer, 'With Employer', Colors.orange),
            _buildStatusChip(StatusFilter.notStarted, 'Not started', Colors.red),
            _buildStatusChip(StatusFilter.done, 'Done', Colors.green),
            _buildStatusChip(StatusFilter.processing, 'Processing', Colors.orange),
          ],
        ),
        const SizedBox(height: 20),
        
        // Organization Filter
        Text(
          'Organization',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: AppColors.kBlackColor,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          height: 120,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: ListView.builder(
            itemCount: widget.availableOrganizations.length,
            itemBuilder: (context, index) {
              final org = widget.availableOrganizations[index];
              return CheckboxListTile(
                title: Text(org),
                value: _selectedOrganizations.contains(org),
                onChanged: (bool? value) {
                  setState(() {
                    if (value == true) {
                      _selectedOrganizations.add(org);
                    } else {
                      _selectedOrganizations.remove(org);
                    }
                  });
                  widget.onOrganizationFilterChanged(_selectedOrganizations);
                },
                activeColor: AppColors.kBlueColor,
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildStatusChip(StatusFilter filter, String label, Color color) {
    final isSelected = _selectedStatuses.contains(filter);
    return FilterChip(
      label: Text(
        label,
        style: TextStyle(
          color: isSelected ? Colors.white : color,
          fontWeight: FontWeight.w500,
        ),
      ),
      selected: isSelected,
      onSelected: (bool selected) {
        setState(() {
          if (selected) {
            _selectedStatuses.add(filter);
          } else {
            _selectedStatuses.remove(filter);
          }
        });
        widget.onStatusFilterChanged(_selectedStatuses);
      },
      backgroundColor: color.withValues(alpha: 0.1),
      selectedColor: color,
      checkmarkColor: Colors.white,
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () {
              setState(() {
                _currentSort = SortOption.dateLatest;
                _selectedStatuses.clear();
                _selectedOrganizations.clear();
              });
              widget.onReset();
            },
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              side: BorderSide(color: AppColors.kBlueColor),
            ),
            child: Text(
              'Reset',
              style: TextStyle(
                color: AppColors.kBlueColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: () {
              widget.onApply();
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.kBlueColor,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text(
              'Apply',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
