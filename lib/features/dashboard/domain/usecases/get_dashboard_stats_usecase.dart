import 'package:SolidCheck/features/dashboard/data/models/dashboard_stats.dart';
import 'package:SolidCheck/features/dashboard/data/repositories/dashboard_repository.dart';

/// Use case for getting dashboard statistics
/// Follows the Single Responsibility Principle by handling only dashboard stats logic
class GetDashboardStatsUseCase {
  final DashboardRepository _repository;

  GetDashboardStatsUseCase(this._repository);

  /// Execute the use case to get dashboard statistics
  /// Returns DashboardStatsData containing all statistics
  Future<DashboardStatsData> execute() async {
    try {
      final result = await _repository.getDashboardStats();
      return result.data ?? _getEmptyStats();
    } catch (error) {
      throw Exception('Failed to get dashboard stats: ${error.toString()}');
    }
  }

  /// Execute with fallback to calculated stats
  /// If API fails, calculates stats from applicants data
  Future<DashboardStatsData> executeWithFallback() async {
    try {
      return await execute();
    } catch (error) {
      // Fallback to calculating stats from applicants
      return await _calculateStatsFromApplicants();
    }
  }

  /// Calculate statistics from applicants data
  /// Used as fallback when API is unavailable
  Future<DashboardStatsData> _calculateStatsFromApplicants() async {
    try {
      final applicantsModel = await _repository.getApplicants();
      final applicants = applicantsModel.data ?? [];
      
      return DashboardStatsData(
        totalApplicants: applicants.length,
        inProgress: applicants.where((a) => a.status?.toLowerCase() == 'in_progress').length,
        furtherActionPending: applicants.where((a) => a.status?.toLowerCase() == 'further_action_pending').length,
        staffReviewPending: applicants.where((a) => a.status?.toLowerCase() == 'staff_review_pending').length,
        complete: applicants.where((a) => a.status?.toLowerCase() == 'complete').length,
      );
    } catch (error) {
      return _getEmptyStats();
    }
  }

  /// Get empty stats as fallback
  DashboardStatsData _getEmptyStats() {
    return DashboardStatsData(
      totalApplicants: 0,
      inProgress: 0,
      furtherActionPending: 0,
      staffReviewPending: 0,
      complete: 0,
    );
  }

  /// Get stats for specific status
  /// Returns count of applicants with specific status
  Future<int> getStatsForStatus(String status) async {
    try {
      final stats = await execute();
      
      switch (status.toLowerCase()) {
        case 'total':
          return stats.totalApplicants ?? 0;
        case 'in_progress':
          return stats.inProgress ?? 0;
        case 'further_action_pending':
          return stats.furtherActionPending ?? 0;
        case 'staff_review_pending':
          return stats.staffReviewPending ?? 0;
        case 'complete':
          return stats.complete ?? 0;
        default:
          return 0;
      }
    } catch (error) {
      return 0;
    }
  }
}
