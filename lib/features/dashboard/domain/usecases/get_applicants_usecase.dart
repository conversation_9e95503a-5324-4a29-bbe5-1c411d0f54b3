import 'package:SolidCheck/features/dashboard/data/models/client_applicants.dart';
import 'package:SolidCheck/features/dashboard/data/repositories/dashboard_repository.dart';

/// Use case for getting applicants list
/// Follows the Single Responsibility Principle by handling only applicant retrieval logic
class GetApplicantsUseCase {
  final DashboardRepository _repository;

  GetApplicantsUseCase(this._repository);

  /// Execute the use case to get applicants
  /// Returns ClientApplicantsModel containing the list of applicants
  Future<ClientApplicantsModel> execute() async {
    try {
      return await _repository.getApplicants();
    } catch (error) {
      throw Exception('Failed to get applicants: ${error.toString()}');
    }
  }

  /// Execute with search query
  /// Returns filtered applicants based on search criteria
  Future<ClientApplicantsModel> executeWithSearch(String query) async {
    try {
      final result = await _repository.getApplicants();
      
      if (query.isEmpty) {
        return result;
      }

      final filteredApplicants = result.data?.where((applicant) {
        final name = applicant.fullName.toLowerCase();
        final email = applicant.email?.toLowerCase() ?? '';
        final organization = applicant.organization?.toLowerCase() ?? '';
        final lowerQuery = query.toLowerCase();
        
        return name.contains(lowerQuery) || 
               email.contains(lowerQuery) || 
               organization.contains(lowerQuery);
      }).toList() ?? [];

      return ClientApplicantsModel(
        success: result.success,
        data: filteredApplicants,
        message: result.message,
      );
    } catch (error) {
      throw Exception('Failed to search applicants: ${error.toString()}');
    }
  }

  /// Execute with pagination and filtering
  /// Returns paginated applicants list
  Future<ClientApplicantsModel> executeWithPagination({
    int page = 1,
    int perPage = 20,
    String? search,
    String? statusFilter,
  }) async {
    try {
      return await _repository.getApplicantsWithPagination(
        page: page,
        perPage: perPage,
        search: search,
        statusFilter: statusFilter,
      );
    } catch (error) {
      throw Exception('Failed to get paginated applicants: ${error.toString()}');
    }
  }
}
