import 'package:SolidCheck/core/constants/side_bar_menu_icons.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/dashboard/data/models/client_applicants.dart';
import 'package:flutter/material.dart';

/// Utility class for handling check type icons and display
class CheckIconsUtil {
  
  /// Get icon widget for a specific check type
  static Widget getCheckIcon(RequestedCheck check, {double size = 16}) {
    final iconPath = _getIconPath(check);

    if (iconPath != null) {
      return Icon(
        _getFallbackIcon(check),
        size: size,
        color: AppColors.kBlueColor,
      );
    }

    // Fallback to Material Icons
    return Icon(
      _getFallbackIcon(check),
      size: size,
      color: AppColors.kBlueColor,
    );
  }

  /// Get abbreviated text for check type
  static String getCheckAbbreviation(RequestedCheck check) {
    final productCode = check.productCode?.toUpperCase();
    final productName = check.productName?.toUpperCase();
    final type = check.type?.toUpperCase();

    // Check by product code first
    if (productCode != null) {
      switch (productCode) {
        case 'DBSEC':
        case 'DBSSC':
        case 'DBSBC':
          return 'DBS';
        case 'RTW':
          return 'RTW';
        case 'ID':
          return 'ID';
        case 'REF':
          return 'REF';
        case 'SMC':
          return 'SMC';
        case 'AC':
        case 'CREDIT':
          return 'AC';
        default:
          break;
      }
    }

    // Check by product name
    if (productName != null) {
      if (productName.contains('DBS') || productName.contains('DISCLOSURE')) {
        return 'DBS';
      } else if (productName.contains('RIGHT TO WORK') || productName.contains('RTW')) {
        return 'RTW';
      } else if (productName.contains('ID') || productName.contains('IDENTITY')) {
        return 'ID';
      } else if (productName.contains('REFERENCE') || productName.contains('REF')) {
        return 'REF';
      } else if (productName.contains('SOCIAL MEDIA') || productName.contains('SMC')) {
        return 'SMC';
      } else if (productName.contains('CREDIT') || productName.contains('ADVERSE')) {
        return 'AC';
      }
    }

    // Check by type
    if (type != null) {
      if (type.contains('DBS') || type.contains('DISCLOSURE')) {
        return 'DBS';
      } else if (type.contains('RIGHT TO WORK') || type.contains('RTW')) {
        return 'RTW';
      } else if (type.contains('ID') || type.contains('IDENTITY')) {
        return 'ID';
      } else if (type.contains('REFERENCE') || type.contains('REF')) {
        return 'REF';
      } else if (type.contains('SOCIAL') || type.contains('SMC')) {
        return 'SMC';
      } else if (type.contains('CREDIT') || type.contains('ADVERSE')) {
        return 'AC';
      }
    }

    // Fallback to first 3 characters of display name
    final displayName = check.displayName.toUpperCase();
    return displayName.length >= 3 ? displayName.substring(0, 3) : displayName;
  }

  /// Get full display name for check type
  static String getCheckDisplayName(RequestedCheck check) {
    return check.displayName;
  }

  /// Get color for check type - always use app default blue
  static Color getCheckColor(RequestedCheck check) {
    return AppColors.kBlueColor;
  }

  /// Get background color for check badge
  static Color getCheckBackgroundColor(RequestedCheck check) {
    return AppColors.kBlueColor.withValues(alpha: 0.1);
  }

  /// Get border color for check badge
  static Color getCheckBorderColor(RequestedCheck check) {
    return AppColors.kBlueColor.withValues(alpha: 0.3);
  }

  // Private helper methods

  static String? _getIconPath(RequestedCheck check) {
    final productCode = check.productCode?.toUpperCase();
    final productName = check.productName?.toUpperCase();
    final type = check.type?.toUpperCase();

    // Check by product code first
    if (productCode != null) {
      switch (productCode) {
        case 'DBSEC':
        case 'DBSSC':
        case 'DBSBC':
          return SideBarMenuIcons.dbsCheckIcon;
        case 'RTW':
          return SideBarMenuIcons.rightToWorkIcon;
        case 'ID':
          return SideBarMenuIcons.idCheckIcon;
        case 'REF':
          return SideBarMenuIcons.refCheckIcon;
        case 'SMC':
          return SideBarMenuIcons.smcIcon;
        case 'AC':
        case 'CREDIT':
          return SideBarMenuIcons.creditCheckIcon;
        default:
          break;
      }
    }

    // Check by product name
    if (productName != null) {
      if (productName.contains('DBS') || productName.contains('DISCLOSURE')) {
        return SideBarMenuIcons.dbsCheckIcon;
      } else if (productName.contains('RIGHT TO WORK') || productName.contains('RTW')) {
        return SideBarMenuIcons.rightToWorkIcon;
      } else if (productName.contains('ID') || productName.contains('IDENTITY')) {
        return SideBarMenuIcons.idCheckIcon;
      } else if (productName.contains('REFERENCE') || productName.contains('REF')) {
        return SideBarMenuIcons.refCheckIcon;
      } else if (productName.contains('SOCIAL MEDIA') || productName.contains('SMC')) {
        return SideBarMenuIcons.smcIcon;
      } else if (productName.contains('CREDIT') || productName.contains('ADVERSE')) {
        return SideBarMenuIcons.creditCheckIcon;
      }
    }

    // Check by type
    if (type != null) {
      if (type.contains('DBS') || type.contains('DISCLOSURE')) {
        return SideBarMenuIcons.dbsCheckIcon;
      } else if (type.contains('RIGHT TO WORK') || type.contains('RTW')) {
        return SideBarMenuIcons.rightToWorkIcon;
      } else if (type.contains('ID') || type.contains('IDENTITY')) {
        return SideBarMenuIcons.idCheckIcon;
      } else if (type.contains('REFERENCE') || type.contains('REF')) {
        return SideBarMenuIcons.refCheckIcon;
      } else if (type.contains('SOCIAL') || type.contains('SMC')) {
        return SideBarMenuIcons.smcIcon;
      } else if (type.contains('CREDIT') || type.contains('ADVERSE')) {
        return SideBarMenuIcons.creditCheckIcon;
      }
    }

    return null; // Will use fallback icon
  }

  static IconData _getFallbackIcon(RequestedCheck check) {
    final productCode = check.productCode?.toUpperCase();
    final productName = check.productName?.toUpperCase();
    final type = check.type?.toUpperCase();

    // Check by product code first
    if (productCode != null) {
      switch (productCode) {
        case 'DBSEC':
        case 'DBSSC':
        case 'DBSBC':
          return Icons.security;
        case 'RTW':
          return Icons.work;
        case 'ID':
          return Icons.badge;
        case 'REF':
          return Icons.people;
        case 'SMC':
          return Icons.social_distance;
        case 'AC':
        case 'CREDIT':
          return Icons.credit_score;
        default:
          break;
      }
    }

    // Check by product name or type
    final searchText = (productName ?? type ?? '').toUpperCase();
    if (searchText.contains('DBS') || searchText.contains('DISCLOSURE')) {
      return Icons.security;
    } else if (searchText.contains('RIGHT TO WORK') || searchText.contains('RTW')) {
      return Icons.work;
    } else if (searchText.contains('ID') || searchText.contains('IDENTITY')) {
      return Icons.badge;
    } else if (searchText.contains('REFERENCE') || searchText.contains('REF')) {
      return Icons.people;
    } else if (searchText.contains('SOCIAL') || searchText.contains('SMC')) {
      return Icons.social_distance;
    } else if (searchText.contains('CREDIT') || searchText.contains('ADVERSE')) {
      return Icons.credit_score;
    }

    return Icons.check_circle; // Default fallback
  }


}
