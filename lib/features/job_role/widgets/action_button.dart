import 'package:SolidCheck/core/theme/colors.dart';
import 'package:flutter/material.dart';

class AddJobRoleActionButton extends StatefulWidget {
  final String title;
  final void Function()? onPressed;
  final bool isSaveButton;

  const AddJobRoleActionButton({
    super.key,
    required this.title,
    this.onPressed,
    required this.isSaveButton,
  });

  @override
  State<AddJobRoleActionButton> createState() => _AddJobRoleActionButtonState();
}

class _AddJobRoleActionButtonState extends State<AddJobRoleActionButton> {
  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: ButtonStyle(
          shape: WidgetStateProperty.all<RoundedRectangleBorder>(
              RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.0),
                  side: BorderSide(
                      color: widget.isSaveButton
                          ? AppColors.kBlueColor
                          : AppColors.kBlueColor))),
          backgroundColor: WidgetStateProperty.all(widget.isSaveButton == true
              ? AppColors.kBlueColor
              : AppColors.kWhiteColor)),
      onPressed: widget.onPressed,
      child: Text(
        widget.title,
        style: TextStyle(
            fontSize: 16.0,
            color: widget.isSaveButton == true
                ? AppColors.kWhiteColor
                : AppColors.kBlueColor),
      ),
    );
  }
}
