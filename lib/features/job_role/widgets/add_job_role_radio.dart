import 'package:flutter/material.dart';

class AddJobRoleRadioButton extends StatefulWidget {
  final String title;
  final String radioTitle1;
  final bool value1;
  final Object? groupValue1;
  final Function(Object?)? onChanged1;
  final String radioTitle2;
  final bool value2;
  final Object? groupValue2;
  final Function(Object?)? onChanged2;

  const AddJobRoleRadioButton({
    super.key,
    required this.title,
    required this.radioTitle1,
    required this.radioTitle2,
    required this.value1,
    this.groupValue1,
    this.onChanged1,
    required this.value2,
    this.groupValue2,
    this.onChanged2,
  });

  @override
  State<AddJobRoleRadioButton> createState() => _AddJobRoleRadioButtonState();
}

class _AddJobRoleRadioButtonState extends State<AddJobRoleRadioButton> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.title,
          style: const TextStyle(fontSize: 15.0),
        ),
        RadioListTile(
          title: Text(widget.radioTitle1),
          value: widget.value1,
          groupValue: widget.groupValue1,
          onChanged: widget.onChanged1,
        ),
        RadioListTile(
          title: Text(widget.radioTitle2),
          value: widget.value2,
          groupValue: widget.groupValue2,
          onChanged: widget.onChanged2,
        ),
      ],
    );
  }
}
