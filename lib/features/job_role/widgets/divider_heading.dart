import 'package:SolidCheck/core/theme/colors.dart';
import 'package:flutter/material.dart';

class DividerHeading extends StatelessWidget {
  final String text;
  const DividerHeading({
    super.key,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const SizedBox(
          height: 10,
        ),
        Row(
          children: [
            Expanded(
              child: Divider(
                color: AppColors.dividercolor,
                thickness: 1.0,
                endIndent: 10.0,
              ),
            ),
            Text(
              text,
              style: TextStyle(
                  color: AppColors.dividercolor,
                  fontSize: 16.0,
                  fontWeight: FontWeight.w400),
            ),
            Expanded(
              child: Divider(
                color: AppColors.dividercolor,
                thickness: 1.0,
                indent: 10.0,
              ),
            ),
          ],
        ),
        const SizedBox(
          height: 10,
        ),
      ],
    );
  }
}
