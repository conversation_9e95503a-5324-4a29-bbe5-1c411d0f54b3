import 'dart:async';
import 'package:SolidCheck/core/constants/constants.dart';
import 'package:SolidCheck/core/network/api_exceptions.dart';
import 'package:SolidCheck/core/network/http_client.dart';

class JobRoleService {
  final HttpClient _httpClient = HttpClient();

  Future<dynamic> fetchJobRolesRequest(String token) async {
    final url = '/jobRole/getJobRole/0/0';
    final params = {'token': token};
    final response = await _httpClient.fetchData(
      url,
      params: params,
      headers: {
        'Accept': 'application/json',
      },
    );

    return response;
  }
}
