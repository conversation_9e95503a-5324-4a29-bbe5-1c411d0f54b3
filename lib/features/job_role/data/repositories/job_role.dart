import 'package:SolidCheck/features/auth/data/repositories/auth.dart';
import 'package:SolidCheck/features/job_role/data/models/job_role.dart';
import 'package:SolidCheck/features/job_role/data/services/job_role.dart';

class JobRoleRepository {
  final AuthRepository authService;
  final JobRoleService _jobRoleService;

  JobRoleRepository({required this.authService})
      : _jobRoleService = JobRoleService();

  Future<List<GetJobRoleModel>> fetchJobRoles() async {
    await authService.isLoggedIn();
    final token = await authService.getToken();

    if (token == null || token.isEmpty) {
      throw Exception('Token not available');
    }

    try {
      final response = await _jobRoleService.fetchJobRolesRequest(token);

      List<dynamic> jsonData = response['data'];

      return jsonData.map((data) => GetJobRoleModel.fromJson(data)).toList();
    } catch (e) {
      throw Exception('Failed to load job roles: $e');
    }
  }
}
