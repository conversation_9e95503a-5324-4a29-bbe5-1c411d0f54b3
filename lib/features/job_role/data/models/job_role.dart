// To parse this JSON data, do
//
//     final getJobRoleModel = getJobRoleModelFromJson(jsonString);

import 'dart:convert';

GetJobRoleModel getJobRoleModelFromJson(String str) =>
    GetJobRoleModel.fromJson(json.decode(str));

String getJobRoleModelToJson(GetJobRoleModel data) =>
    json.encode(data.toJson());

class GetJobRoleModel {
  int? id;
  int? accountId;
  String? optionIds;
  String? jobLabel;
  String? jobTitle;
  String? jobWorkforce;
  String? roleDescription;
  String? disclosureType;
  String? statusType;
  String? selfPayment;
  String? employmentSector;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletionTimestamp;

  GetJobRoleModel({
    this.id,
    this.accountId,
    this.optionIds,
    this.jobLabel,
    this.jobTitle,
    this.jobWorkforce,
    this.roleDescription,
    this.disclosureType,
    this.statusType,
    this.selfPayment,
    this.employmentSector,
    this.createdAt,
    this.updatedAt,
    this.deletionTimestamp,
  });

  factory GetJobRoleModel.fromJson(Map<String, dynamic> json) =>
      GetJobRoleModel(
        id: json["id"],
        accountId: json["account_id"],
        optionIds: json["option_ids"],
        jobLabel: json["job_label"],
        jobTitle: json["job_title"],
        jobWorkforce: json["job_workforce"],
        roleDescription: json["role_description"],
        disclosureType: json["disclosure_type"],
        statusType: json["status_type"],
        selfPayment: json["self_payment"],
        employmentSector: json["employment_sector"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        deletionTimestamp: json["deletion_timestamp"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "account_id": accountId,
        "option_ids": optionIds,
        "job_label": jobLabel,
        "job_title": jobTitle,
        "job_workforce": jobWorkforce,
        "role_description": roleDescription,
        "disclosure_type": disclosureType,
        "status_type": statusType,
        "self_payment": selfPayment,
        "employment_sector": employmentSector,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "deletion_timestamp": deletionTimestamp,
      };
}
