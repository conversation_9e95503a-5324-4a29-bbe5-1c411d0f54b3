import 'package:SolidCheck/features/job_role/data/models/job_role.dart';
import 'package:flutter/material.dart';

class JobRolesDataTableSource extends DataTableSource {
  JobRolesDataTableSource(this.jobRoles);

  final List<GetJobRoleModel> jobRoles;

  @override
  DataRow? getRow(int index) {
    if (index >= jobRoles.length) return null;

    final jobRoleData = jobRoles[index];

    return DataRow.byIndex(
      index: index,
      cells: [
        _buildDataCell(jobRoleData.jobTitle ?? '—'),
        _buildDataCell(jobRoleData.roleDescription ?? '—'),
        _buildDataCell(jobRoleData.jobWorkforce ?? '—'),
        _buildDataCell(jobRoleData.disclosureType ?? '—'),
        _buildDataCell(jobRoleData.statusType ?? '—'),
        _buildDataCell(jobRoleData.selfPayment ?? '—'),
        _buildDataCell(jobRoleData.employmentSector ?? '—'),
      ],
    );
  }

  DataCell _buildDataCell(String text) {
    return DataCell(
      Text(
        text,
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
    );
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => jobRoles.length;

  @override
  int get selectedRowCount => 0;
}
