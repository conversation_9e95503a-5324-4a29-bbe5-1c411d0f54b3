import 'package:SolidCheck/features/auth/data/repositories/auth.dart';
import 'package:SolidCheck/features/job_role/data/models/job_role.dart';
import 'package:SolidCheck/features/job_role/data/repositories/job_role.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final jobRoleServiceProvider = Provider<JobRoleRepository>((ref) {
  final authRepository = ref.read(authProvider);
  return JobRoleRepository(authService: authRepository);
});

final jobRolesProvider = FutureProvider<List<GetJobRoleModel>>((ref) async {
  final service = ref.watch(jobRoleServiceProvider);
  await service.authService.isLoggedIn();
  return await service.fetchJobRoles();
});
