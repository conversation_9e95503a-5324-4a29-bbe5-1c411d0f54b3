import 'package:SolidCheck/features/dbs/services/dbs_persistence_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

enum SaveStatus {
  idle,
  saving,
  saved,
  loading,
  loaded,
  error,
}

class SaveState {
  final SaveStatus status;
  final String? message;
  final DateTime? lastSaveTime;
  final bool hasUnsavedChanges;
  final double? completionPercentage;

  SaveState({
    this.status = SaveStatus.idle,
    this.message,
    this.lastSaveTime,
    this.hasUnsavedChanges = false,
    this.completionPercentage,
  });

  SaveState copyWith({
    SaveStatus? status,
    String? message,
    DateTime? lastSaveTime,
    bool? hasUnsavedChanges,
    double? completionPercentage,
  }) {
    return SaveState(
      status: status ?? this.status,
      message: message ?? this.message,
      lastSaveTime: lastSaveTime ?? this.lastSaveTime,
      hasUnsavedChanges: hasUnsavedChanges ?? this.hasUnsavedChanges,
      completionPercentage: completionPercentage ?? this.completionPercentage,
    );
  }
}

class SaveStateNotifier extends StateNotifier<SaveState> {
  final DBSPersistenceService _persistenceService;

  SaveStateNotifier(this._persistenceService) : super(SaveState());

  void setSaving() {
    state = state.copyWith(
      status: SaveStatus.saving,
      message: 'Saving your progress...',
    );
  }

  void setSaved({DateTime? saveTime, double? completionPercentage}) {
    state = state.copyWith(
      status: SaveStatus.saved,
      message: 'Progress saved successfully',
      lastSaveTime: saveTime ?? DateTime.now(),
      hasUnsavedChanges: false,
      completionPercentage: completionPercentage,
    );
  }

  void setLoading() {
    state = state.copyWith(
      status: SaveStatus.loading,
      message: 'Loading your saved progress...',
    );
  }

  void setLoaded({DateTime? lastSaveTime, double? completionPercentage}) {
    state = state.copyWith(
      status: SaveStatus.loaded,
      message: 'Progress loaded successfully',
      lastSaveTime: lastSaveTime,
      hasUnsavedChanges: false,
      completionPercentage: completionPercentage,
    );
  }

  void setError(String errorMessage) {
    state = state.copyWith(
      status: SaveStatus.error,
      message: errorMessage,
    );
  }

  void setUnsavedChanges() {
    if (state.status != SaveStatus.saving && state.status != SaveStatus.loading) {
      state = state.copyWith(
        hasUnsavedChanges: true,
        message: null,
      );
    }
  }

  void clearState() {
    state = SaveState();
  }

  void setIdle() {
    state = state.copyWith(
      status: SaveStatus.idle,
      message: null,
    );
  }

  Future<void> checkForSavedData(String applicantId, String applicationId) async {
    try {
      final hasSaved = await _persistenceService.hasSavedData(
        applicantId: applicantId,
        applicationId: applicationId,
      );

      if (hasSaved) {
        final metadata = await _persistenceService.getFormMetadata(
          applicantId: applicantId,
          applicationId: applicationId,
        );

        if (metadata != null) {
          state = state.copyWith(
            lastSaveTime: metadata.saveTimestamp,
            completionPercentage: metadata.completionStatus,
          );
        }
      }
    } catch (e) {
      setError('Failed to check for saved data: $e');
    }
  }
}

final saveStateProvider = StateNotifierProvider<SaveStateNotifier, SaveState>((ref) {
  return SaveStateNotifier(DBSPersistenceService());
});

final autoSaveTimerProvider = Provider<void>((ref) {
  return;
});
