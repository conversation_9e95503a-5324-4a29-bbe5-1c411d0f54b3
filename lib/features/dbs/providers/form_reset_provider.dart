import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Provider to trigger form reset across all steps
/// This allows the main form screen to notify all steps to reset their data
final formResetTriggerProvider = StateNotifierProvider<FormResetNotifier, int>((ref) {
  return FormResetNotifier();
});

class FormResetNotifier extends StateNotifier<int> {
  FormResetNotifier() : super(0);

  /// Trigger a reset across all form steps
  void triggerReset() {
    state = state + 1; // Increment to trigger listeners
  }

  /// Reset the trigger state
  void reset() {
    state = 0;
  }
}
