import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Provider to control when form validation should be shown
/// This allows us to show validation errors only when user tries to proceed to next step
final formValidationModeProvider = StateNotifierProvider<FormValidationModeNotifier, AutovalidateMode>((ref) {
  return FormValidationModeNotifier();
});

class FormValidationModeNotifier extends StateNotifier<AutovalidateMode> {
  FormValidationModeNotifier() : super(AutovalidateMode.disabled);

  /// Enable validation when user clicks Next button (show ALL validations)
  void enableValidationForNextButton() {
    state = AutovalidateMode.always;
  }

  /// Disable validation completely (called when navigating to a new step)
  void disableValidation() {
    state = AutovalidateMode.disabled;
  }

  /// Enable validation only on user interaction (for individual field validation)
  void enableOnUserInteraction() {
    state = AutovalidateMode.onUserInteraction;
  }

  /// Reset validation state
  void reset() {
    state = AutovalidateMode.disabled;
  }
}

/// Provider to track which step is currently being validated
final currentValidatingStepProvider = StateProvider<int?>((ref) => null);

/// Provider to control validation per step
final stepValidationModeProvider = StateNotifierProvider.family<StepValidationNotifier, AutovalidateMode, int>((ref, stepIndex) {
  return StepValidationNotifier();
});

class StepValidationNotifier extends StateNotifier<AutovalidateMode> {
  StepValidationNotifier() : super(AutovalidateMode.disabled);

  /// Enable validation for this step
  void enableValidation() {
    state = AutovalidateMode.onUserInteraction;
  }

  /// Disable validation for this step
  void disableValidation() {
    state = AutovalidateMode.disabled;
  }

  /// Enable validation on user interaction
  void enableOnUserInteraction() {
    state = AutovalidateMode.onUserInteraction;
  }
}
