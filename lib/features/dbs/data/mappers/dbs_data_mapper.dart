import 'package:SolidCheck/features/dbs/data/models/dbs_api_request.dart';
import 'package:SolidCheck/features/dbs/data/models/dbs_form_data.dart';

/// Data mapper to convert from internal form data model to API request model
class DBSDataMapper {

  /// Convert DBSFormData to DBSApiRequest with proper field mapping and validation
  static DBSApiRequest mapToApiRequest(DBSFormData formData, {
    String unspentConvictions = 'n',
    String declarationByApplicant = 'y',
    String languagePreference = 'english',
    String identityVerified = 'y',
    String evidenceCheckedBy = 'SYSTEM',
  }) {
    return DBSApiRequest(
      applicantDetails: _mapApplicantDetails(
        formData.applicantDetails,
        unspentConvictions: unspentConvictions,
        declarationByApplicant: declarationByApplicant,
        languagePreference: languagePreference,
        identityVerified: identityVerified,
        evidenceCheckedBy: evidenceCheckedBy,
      ),
    );
  }

  /// Convert DBSFormData to flat API format for save-data endpoint
  static Map<String, dynamic> mapToSaveDataRequest({
    required int applicationId,
    required DBSFormData formData,
    String unspentConvictions = 'n',
    String declarationByApplicant = 'y',
    String languagePreference = 'english',
    String identityVerified = 'y',
    String evidenceCheckedBy = 'SYSTEM',
  }) {
    final flatFormData = <String, String>{};

    // Map applicant details
    _mapApplicantDetailsToFlat(
      formData.applicantDetails,
      flatFormData,
      unspentConvictions: unspentConvictions,
      declarationByApplicant: declarationByApplicant,
      languagePreference: languagePreference,
      identityVerified: identityVerified,
      evidenceCheckedBy: evidenceCheckedBy,
    );

    // Final sanitization - ensure all values are non-null strings
    final sanitizedFormData = <String, String>{};
    flatFormData.forEach((key, value) {
      sanitizedFormData[key] = value ?? '';
    });

    return {
      'application_id': applicationId,
      'form_data': sanitizedFormData,
    };
  }

  /// Map applicant details to flat structure
  static void _mapApplicantDetailsToFlat(
    ApplicantDetailsData data,
    Map<String, String> flatData, {
    String unspentConvictions = 'n',
    String declarationByApplicant = 'y',
    String languagePreference = 'english',
    String identityVerified = 'y',
    String evidenceCheckedBy = 'SYSTEM',
  }) {
    // Basic applicant details
    flatData['ApplicantDetails::TITLE'] = _formatForApi(data.title);
    flatData['ApplicantDetails::FORENAME'] = _formatForApi(data.forename);
    flatData['ApplicantDetails::PRESENT_SURNAME'] = _formatForApi(data.presentSurname);
    flatData['ApplicantDetails::DATE_OF_BIRTH'] = _ensureString(data.dateOfBirth);
    flatData['ApplicantDetails::GENDER'] = _ensureString(data.gender.toLowerCase());
    flatData['ApplicantDetails::NI_NUMBER'] = _ensureString(data.niNumber);

    // Current address
    _mapCurrentAddressToFlat(data.currentAddress, flatData);

    // Previous addresses
    _mapPreviousAddressesToFlat(data.previousAddresses, flatData);

    // Additional applicant details (including contact number from main data)
    _mapAdditionalDetailsToFlat(
      data.additionalApplicantDetails,
      flatData,
      contactNumber: data.contactNumber,
      fullApplicantDetails: data,
      unspentConvictions: unspentConvictions,
      declarationByApplicant: declarationByApplicant,
      languagePreference: languagePreference,
    );

    // Identity details
    _mapIdentityDetailsToFlat(
      data.applicantIdentityDetails,
      flatData,
      identityVerified: identityVerified,
      evidenceCheckedBy: evidenceCheckedBy,
    );
  }

  /// Map current address to flat structure
  static void _mapCurrentAddressToFlat(CurrentAddressData address, Map<String, String> flatData) {
    flatData['CurrentAddress::ADDRESS_LINE1'] = _formatForApi(address.addressLine1);
    flatData['CurrentAddress::ADDRESS_LINE2'] = _formatForApi(address.addressLine2);
    flatData['CurrentAddress::ADDRESS_TOWN'] = _formatForApi(address.addressTown);
    flatData['CurrentAddress::ADDRESS_COUNTY'] = _formatForApi(address.addressCounty);
    flatData['CurrentAddress::POSTCODE'] = _ensureString(address.postcode).toUpperCase();
    flatData['CurrentAddress::COUNTRY_CODE'] = _ensureString(address.countryCode).toUpperCase();
    flatData['CurrentAddress::RESIDENT_FROM_YEAR_MONTH'] = _ensureString(address.residentFromGyearMonth);
  }

  /// Map previous addresses to flat structure
  static void _mapPreviousAddressesToFlat(List<PreviousAddressData> addresses, Map<String, String> flatData) {
    for (int i = 0; i < addresses.length; i++) {
      final address = addresses[i];
      final prefix = 'PreviousAddress::$i';

      flatData['$prefix::Address::AddressLine1'] = _formatForApi(address.addressLine1);
      flatData['$prefix::Address::AddressLine2'] = _formatForApi(address.addressLine2);
      flatData['$prefix::Address::AddressTown'] = _formatForApi(address.addressTown);
      flatData['$prefix::Address::AddressCounty'] = _formatForApi(address.addressCounty);
      flatData['$prefix::Address::Postcode'] = _ensureString(address.postcode).toUpperCase();
      flatData['$prefix::Address::CountryCode'] = _ensureString(address.countryCode).toUpperCase();
      flatData['$prefix::ResidentDates::ResidentFromGyearMonth'] = _ensureString(address.residentFromGyearMonth);
      flatData['$prefix::ResidentDates::ResidentToGyearMonth'] = _ensureString(address.residentToGyearMonth);
    }
  }

  /// Map additional details to flat structure
  static void _mapAdditionalDetailsToFlat(
    AdditionalApplicantDetailsData data,
    Map<String, String> flatData, {
    required String contactNumber,
    required ApplicantDetailsData fullApplicantDetails,
    String unspentConvictions = 'n',
    String declarationByApplicant = 'y',
    String languagePreference = 'english',
  }) {
    flatData['AdditionalApplicantDetails::BIRTH_SURNAME'] = _formatForApi(data.birthSurname);
    flatData['AdditionalApplicantDetails::BIRTH_SURNAME_UNTIL'] = _ensureString(
      data.birthSurnameUntil,
      fieldName: 'AdditionalApplicantDetails::BIRTH_SURNAME_UNTIL',
      formData: DBSFormData(applicantDetails: fullApplicantDetails),
    );
    flatData['AdditionalApplicantDetails::BIRTH_TOWN'] = _formatForApi(data.birthTown);
    flatData['AdditionalApplicantDetails::BIRTH_COUNTY'] = _formatForApi(data.birthCounty);
    flatData['AdditionalApplicantDetails::BIRTH_COUNTRY'] = _ensureString(data.birthCountry).toUpperCase();
    flatData['AdditionalApplicantDetails::BIRTH_NATIONALITY'] = _formatForApi(data.birthNationality);
    flatData['AdditionalApplicantDetails::CONTACT_NUMBER'] = _ensureString(contactNumber);
    flatData['AdditionalApplicantDetails::UNSPENT_CONVICTIONS'] = _ensureString(unspentConvictions);
    flatData['AdditionalApplicantDetails::DECLARATION_BY_APPLICANT'] = _ensureString(declarationByApplicant);
    flatData['AdditionalApplicantDetails::LANGUAGE_PREFERENCE'] = _ensureString(languagePreference);
  }

  /// Map identity details to flat structure
  static void _mapIdentityDetailsToFlat(
    ApplicantIdentityDetailsData data,
    Map<String, String> flatData, {
    String identityVerified = 'y',
    String evidenceCheckedBy = 'SYSTEM',
  }) {
    flatData['ApplicantIdentityDetails::IDENTITY_VERIFIED'] = _ensureString(identityVerified);
    flatData['ApplicantIdentityDetails::EVIDENCE_CHECKED_BY'] = _ensureString(evidenceCheckedBy);
  }

  /// Map ApplicantDetailsData to ApplicantDetailsApi
  static ApplicantDetailsApi _mapApplicantDetails(
    ApplicantDetailsData data, {
    required String unspentConvictions,
    required String declarationByApplicant,
    required String languagePreference,
    required String identityVerified,
    required String evidenceCheckedBy,
  }) {
    return ApplicantDetailsApi(
      title: _formatForApi(data.title),
      forename: _formatForApi(data.forename),
      middlenames: data.middlenames.isNotEmpty 
          ? MiddlenamesApi(middlename: data.middlenames.map(_formatForApi).toList())
          : null,
      presentSurname: _formatForApi(data.presentSurname),
      dateOfBirth: data.dateOfBirth, // Already in YYYY-MM-DD format
      gender: data.gender.toLowerCase(),
      niNumber: _formatForApi(data.niNumber),
      currentAddress: _mapCurrentAddress(data.currentAddress),
      previousAddresses: data.previousAddresses.map(_mapPreviousAddress).toList(),
      additionalApplicantDetails: _mapAdditionalApplicantDetails(
        data.additionalApplicantDetails,
        contactNumber: data.contactNumber,
        unspentConvictions: unspentConvictions,
        declarationByApplicant: declarationByApplicant,
        languagePreference: languagePreference,
      ),
      applicantIdentityDetails: _mapApplicantIdentityDetails(
        data.applicantIdentityDetails,
        identityVerified: identityVerified,
        evidenceCheckedBy: evidenceCheckedBy,
      ),
    );
  }

  /// Map CurrentAddressData to CurrentAddressApi
  static CurrentAddressApi _mapCurrentAddress(CurrentAddressData data) {
    return CurrentAddressApi(
      address: AddressApi(
        addressLine1: _formatForApi(data.addressLine1),
        addressLine2: _formatForApi(data.addressLine2),
        addressTown: _formatForApi(data.addressTown),
        addressCounty: _formatForApi(data.addressCounty),
        postcode: _formatPostcodeForApi(data.postcode),
        countryCode: data.countryCode.toUpperCase(),
      ),
      residentFromGyearMonth: data.residentFromGyearMonth, // Already in YYYY-MM format
    );
  }

  /// Map PreviousAddressData to PreviousAddressApi
  static PreviousAddressApi _mapPreviousAddress(PreviousAddressData data) {
    return PreviousAddressApi(
      address: AddressApi(
        addressLine1: _formatForApi(data.addressLine1),
        addressLine2: _formatForApi(data.addressLine2),
        addressTown: _formatForApi(data.addressTown),
        addressCounty: _formatForApi(data.addressCounty),
        postcode: _formatPostcodeForApi(data.postcode),
        countryCode: data.countryCode.toUpperCase(),
      ),
      residentDates: ResidentDatesApi(
        residentFromGyearMonth: data.residentFromGyearMonth,
        residentToGyearMonth: data.residentToGyearMonth,
      ),
    );
  }

  /// Map AdditionalApplicantDetailsData to AdditionalApplicantDetailsApi
  static AdditionalApplicantDetailsApi _mapAdditionalApplicantDetails(
    AdditionalApplicantDetailsData data, {
    required String contactNumber,
    required String unspentConvictions,
    required String declarationByApplicant,
    required String languagePreference,
  }) {
    return AdditionalApplicantDetailsApi(
      birthSurname: _formatForApi(data.birthSurname),
      birthSurnameUntil: data.birthSurnameUntil, // Already in YYYY format
      otherSurnames: data.otherSurnames.isNotEmpty
          ? OtherSurnamesApi(
              otherSurname: data.otherSurnames.map(_mapOtherSurname).toList(),
            )
          : null,
      otherForenames: data.otherForenames.isNotEmpty
          ? OtherForenamesApi(
              otherForename: data.otherForenames.map(_mapOtherForename).toList(),
            )
          : null,
      birthTown: _formatForApi(data.birthTown),
      birthCounty: _formatForApi(data.birthCounty),
      birthCountry: data.birthCountry.toUpperCase(),
      birthNationality: _formatForApi(data.birthNationality),
      contactNumber: _formatContactNumberForApi(contactNumber),
      unspentConvictions: unspentConvictions,
      declarationByApplicant: declarationByApplicant,
      languagePreference: languagePreference,
    );
  }

  /// Map OtherSurnameData to OtherSurnameApi
  static OtherSurnameApi _mapOtherSurname(OtherSurnameData data) {
    return OtherSurnameApi(
      name: _formatForApi(data.name),
      usedFrom: data.usedFrom, // Already in YYYY format
      usedTo: data.usedTo, // Already in YYYY format
    );
  }

  /// Map OtherForenameData to OtherForenameApi
  static OtherForenameApi _mapOtherForename(OtherForenameData data) {
    return OtherForenameApi(
      name: _formatForApi(data.name),
      usedFrom: data.usedFrom, // Already in YYYY format
      usedTo: data.usedTo, // Already in YYYY format
    );
  }

  /// Map ApplicantIdentityDetailsData to ApplicantIdentityDetailsApi
  static ApplicantIdentityDetailsApi _mapApplicantIdentityDetails(
    ApplicantIdentityDetailsData data, {
    required String identityVerified,
    required String evidenceCheckedBy,
  }) {
    return ApplicantIdentityDetailsApi(
      identityVerified: identityVerified,
      evidenceCheckedBy: _formatForApi(evidenceCheckedBy),
      passportDetails: data.passportDetails != null
          ? _mapPassportDetails(data.passportDetails!)
          : null,
      driverLicenceDetails: data.driverLicenceDetails != null
          ? _mapDriverLicenceDetails(data.driverLicenceDetails!)
          : null,
    );
  }

  /// Map PassportDetailsData to PassportDetailsApi
  static PassportDetailsApi _mapPassportDetails(PassportDetailsData data) {
    return PassportDetailsApi(
      passportNumber: _formatDocumentNumberForApi(data.passportNumber),
      passportDob: data.passportDob, // Already in YYYY-MM-DD format
      passportNationality: _formatForApi(data.passportNationality),
      passportIssueDate: data.passportIssueDate, // Already in YYYY-MM-DD format
    );
  }

  /// Map DriverLicenceDetailsData to DriverLicenceDetailsApi
  static DriverLicenceDetailsApi _mapDriverLicenceDetails(DriverLicenceDetailsData data) {
    return DriverLicenceDetailsApi(
      driverLicenceNumber: _formatDocumentNumberForApi(data.driverLicenceNumber),
      driverLicenceDOB: data.driverLicenceDOB, // Already in YYYY-MM-DD format
      driverLicenceType: data.driverLicenceType.toLowerCase(),
      driverLicenceValidFrom: data.driverLicenceValidFrom, // Already in YYYY-MM-DD format
      driverLicenceIssueCountry: data.driverLicenceIssueCountry.toUpperCase(),
    );
  }

  /// Format text for API - uppercase and trimmed
  static String _formatForApi(String? value) {
    return _ensureString(value).trim().toUpperCase();
  }

  /// Ensure value is a string, converting null or empty to empty string
  /// For birth surname until, use smart calculation if empty
  static String _ensureString(String? value, {String? fieldName, DBSFormData? formData}) {
    if (value == null || value.isEmpty) {
      // Special handling for birth surname until field
      if (fieldName == 'AdditionalApplicantDetails::BIRTH_SURNAME_UNTIL' && formData != null) {
        return _calculateSmartBirthSurnameUntil(formData);
      }
      return '';
    }
    return value;
  }

  /// Calculate smart birth surname until value based on other names data
  static String _calculateSmartBirthSurnameUntil(DBSFormData formData) {
    final additionalDetails = formData.applicantDetails.additionalApplicantDetails;
    final currentYear = DateTime.now().year.toString();

    // If user has no other surnames, use current year
    if (additionalDetails.otherSurnames.isEmpty) {
      return currentYear;
    }

    // If user has other surnames, find the earliest "Used From" year
    String? earliestUsedFrom;
    for (final surname in additionalDetails.otherSurnames) {
      if (surname.usedFrom.isNotEmpty) {
        if (earliestUsedFrom == null ||
            (int.tryParse(surname.usedFrom) ?? 0) < (int.tryParse(earliestUsedFrom) ?? 0)) {
          earliestUsedFrom = surname.usedFrom;
        }
      }
    }

    return earliestUsedFrom ?? currentYear;
  }

  /// Format postcode for API - uppercase, trimmed, no spaces
  static String _formatPostcodeForApi(String postcode) {
    return postcode.trim().toUpperCase().replaceAll(' ', '');
  }

  /// Format contact number for API - uppercase and trimmed
  static String _formatContactNumberForApi(String contactNumber) {
    return contactNumber.trim().toUpperCase();
  }

  /// Format document number for API - uppercase and trimmed
  static String _formatDocumentNumberForApi(String documentNumber) {
    return documentNumber.trim().toUpperCase();
  }

  /// Validate required fields before API submission
  static List<String> validateForSubmission(DBSFormData formData) {
    List<String> errors = [];

    // Basic personal details
    if (formData.applicantDetails.title.isEmpty) {
      errors.add('Title is required');
    }
    if (formData.applicantDetails.forename.isEmpty) {
      errors.add('First name is required');
    }
    if (formData.applicantDetails.presentSurname.isEmpty) {
      errors.add('Surname is required');
    }
    if (formData.applicantDetails.dateOfBirth.isEmpty) {
      errors.add('Date of birth is required');
    }
    if (formData.applicantDetails.gender.isEmpty) {
      errors.add('Gender is required');
    }

    // Current address
    if (formData.applicantDetails.currentAddress.addressLine1.isEmpty) {
      errors.add('Current address line 1 is required');
    }
    if (formData.applicantDetails.currentAddress.addressTown.isEmpty) {
      errors.add('Current address town is required');
    }
    if (formData.applicantDetails.currentAddress.countryCode.isEmpty) {
      errors.add('Current address country is required');
    }
    if (formData.applicantDetails.currentAddress.residentFromGyearMonth.isEmpty) {
      errors.add('Current address resident from date is required');
    }

    // UK postcode validation
    if (formData.applicantDetails.currentAddress.countryCode.toUpperCase() == 'GB' &&
        formData.applicantDetails.currentAddress.postcode.isEmpty) {
      errors.add('Postcode is required for UK addresses');
    }

    // Birth details
    if (formData.applicantDetails.additionalApplicantDetails.birthTown.isEmpty) {
      errors.add('Birth town is required');
    }
    if (formData.applicantDetails.additionalApplicantDetails.birthCountry.isEmpty) {
      errors.add('Birth country is required');
    }
    if (formData.applicantDetails.additionalApplicantDetails.birthNationality.isEmpty) {
      errors.add('Birth nationality is required');
    }

    // Contact details
    if (formData.applicantDetails.contactNumber.isEmpty) {
      errors.add('Contact number is required');
    }

    return errors;
  }

  /// Get country code from country name (basic mapping)
  static String getCountryCode(String countryName) {
    final countryMap = {
      'UNITED KINGDOM': 'GB',
      'UK': 'GB',
      'GREAT BRITAIN': 'GB',
      'ENGLAND': 'GB',
      'SCOTLAND': 'GB',
      'WALES': 'GB',
      'NORTHERN IRELAND': 'GB',
      'IRELAND': 'IE',
      'FRANCE': 'FR',
      'GERMANY': 'DE',
      'SPAIN': 'ES',
      'ITALY': 'IT',
      'NETHERLANDS': 'NL',
      'BELGIUM': 'BE',
      'PORTUGAL': 'PT',
      'AUSTRIA': 'AT',
      'SWITZERLAND': 'CH',
      'DENMARK': 'DK',
      'SWEDEN': 'SE',
      'NORWAY': 'NO',
      'FINLAND': 'FI',
      'POLAND': 'PL',
      'CZECH REPUBLIC': 'CZ',
      'HUNGARY': 'HU',
      'SLOVAKIA': 'SK',
      'SLOVENIA': 'SI',
      'CROATIA': 'HR',
      'ROMANIA': 'RO',
      'BULGARIA': 'BG',
      'GREECE': 'GR',
      'CYPRUS': 'CY',
      'MALTA': 'MT',
      'LUXEMBOURG': 'LU',
      'ESTONIA': 'EE',
      'LATVIA': 'LV',
      'LITHUANIA': 'LT',
    };

    return countryMap[countryName.toUpperCase()] ?? 'GB'; // Default to GB
  }
}
