import 'package:SolidCheck/core/constants/constants.dart';
import 'package:SolidCheck/features/dbs/data/models/postcode_lookup_models.dart';
import 'package:dio/dio.dart';

class PostcodeLookupService {
  static late Dio _dio;

  static void initialize() {
    _dio = Dio(BaseOptions(
      baseUrl: AppConstants.postcodeServiceBaseURL,
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 10),
      sendTimeout: const Duration(seconds: 10),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      error: true,
    ));
  }

  static Dio get dio => _dio;

  static Future<PostcodeLookupResponse> lookupPostcode(String postcode) async {
    try {
      final cleanPostcode = postcode.trim().replaceAll(' ', '').toLowerCase();
      
      if (cleanPostcode.isEmpty) {
        throw Exception('Postcode cannot be empty');
      }

      final response = await _dio.get('/postcode/exact/$cleanPostcode');

      if (response.statusCode == 200) {
        return PostcodeLookupResponse.fromJson(response.data);
      } else {
        throw Exception('Failed to lookup postcode: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        throw Exception('No addresses found for this postcode');
      } else if (e.response?.statusCode == 400) {
        throw Exception('Invalid postcode format');
      } else if (e.type == DioExceptionType.connectionTimeout ||
                 e.type == DioExceptionType.receiveTimeout) {
        throw Exception('Connection timeout - please try again');
      } else if (e.type == DioExceptionType.connectionError) {
        throw Exception('Unable to connect to postcode service');
      } else {
        throw Exception('Error looking up postcode: ${e.message}');
      }
    } catch (e) {
      throw Exception('Unexpected error: ${e.toString()}');
    }
  }

  static bool isValidPostcodeFormat(String postcode) {
    if (postcode.trim().isEmpty) return false;
    
    final ukPostcodePattern = RegExp(
      r'^[A-Z]{1,2}[0-9][A-Z0-9]?\s?[0-9][A-Z]{2}$',
      caseSensitive: false,
    );
    
    return ukPostcodePattern.hasMatch(postcode.trim());
  }

  static String formatPostcodeForDisplay(String postcode) {
    final cleaned = postcode.trim().toUpperCase().replaceAll(' ', '');

    if (cleaned.length >= 5) {
      final outward = cleaned.substring(0, cleaned.length - 3);
      final inward = cleaned.substring(cleaned.length - 3);
      return '$outward $inward';
    }

    return cleaned;
  }
}
