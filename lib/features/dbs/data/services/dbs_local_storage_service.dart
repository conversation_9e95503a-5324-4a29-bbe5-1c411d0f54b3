import 'dart:convert';

import 'package:SolidCheck/features/dbs/data/models/dbs_form_data.dart';
import 'package:shared_preferences/shared_preferences.dart';

class DBSLocalStorageService {
  static const String _formDataKey = 'dbs_form_data';
  static const String _lastSavedKey = 'dbs_last_saved';
  static const String _unspentConvictionsKey = 'dbs_unspent_convictions';
  static const String _declarationKey = 'dbs_declaration';
  static const String _languagePreferenceKey = 'dbs_language_preference';

  static Future<bool> saveFormData(
    DBSFormData formData, {
    String unspentConvictions = 'n',
    String declarationByApplicant = 'n',
    String languagePreference = 'english',
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final formDataJson = jsonEncode(formData.toJson());
      await prefs.setString(_formDataKey, formDataJson);

      await prefs.setString(_unspentConvictionsKey, unspentConvictions);
      await prefs.setString(_declarationKey, declarationByApplicant);
      await prefs.setString(_languagePreferenceKey, languagePreference);

      await prefs.setString(_lastSavedKey, DateTime.now().toIso8601String());

      return true;
    } catch (e) {
      return false;
    }
  }

  static Future<Map<String, dynamic>?> loadFormData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final formDataJson = prefs.getString(_formDataKey);
      if (formDataJson == null) return null;

      final formDataMap = jsonDecode(formDataJson);
      final formData = _mapToDBSFormData(formDataMap);

      return {
        'formData': formData,
        'unspentConvictions': prefs.getString(_unspentConvictionsKey) ?? 'n',
        'declarationByApplicant': prefs.getString(_declarationKey) ?? 'n',
        'languagePreference':
            prefs.getString(_languagePreferenceKey) ?? 'english',
        'lastSaved': prefs.getString(_lastSavedKey),
      };
    } catch (e) {
      return null;
    }
  }

  static Future<bool> hasSavedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.containsKey(_formDataKey);
    } catch (e) {
      return false;
    }
  }

  static Future<DateTime?> getLastSavedTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastSavedString = prefs.getString(_lastSavedKey);
      if (lastSavedString != null) {
        return DateTime.parse(lastSavedString);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  static Future<bool> clearSavedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_formDataKey);
      await prefs.remove(_unspentConvictionsKey);
      await prefs.remove(_declarationKey);
      await prefs.remove(_languagePreferenceKey);
      await prefs.remove(_lastSavedKey);
      return true;
    } catch (e) {
      return false;
    }
  }

  static Future<void> saveFieldData(String key, String value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('dbs_field_$key', value);
    } catch (e) {
      // Ignore errors during field data saving
    }
  }

  static Future<String?> loadFieldData(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('dbs_field_$key');
    } catch (e) {
      return null;
    }
  }

  static Future<void> saveCurrentStep(int step) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('dbs_current_step', step);
    } catch (e) {
      // Ignore errors during step saving
    }
  }

  static Future<int> loadCurrentStep() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt('dbs_current_step') ?? 0;
    } catch (e) {
      return 0;
    }
  }

  static DBSFormData _mapToDBSFormData(Map<String, dynamic> json) {
    try {
      final applicantDetailsJson =
          json['ApplicantDetails'] as Map<String, dynamic>;

      return DBSFormData(
        applicantDetails: ApplicantDetailsData(
          title: applicantDetailsJson['Title'] ?? '',
          forename: applicantDetailsJson['Forename'] ?? '',
          middlenames: _parseMiddlenames(applicantDetailsJson['Middlenames']),
          presentSurname: applicantDetailsJson['PresentSurname'] ?? '',
          dateOfBirth: applicantDetailsJson['DateOfBirth'] ?? '',
          gender: applicantDetailsJson['Gender'] ?? '',
          niNumber: applicantDetailsJson['NINumber'] ?? '',
          email: applicantDetailsJson['Email'] ?? '',
          contactNumber: applicantDetailsJson['ContactNumber'] ?? '',
          currentAddress: _parseCurrentAddress(
            applicantDetailsJson['CurrentAddress'],
          ),
          previousAddresses: _parsePreviousAddresses(
            applicantDetailsJson['PreviousAddress'],
          ),
          additionalApplicantDetails: _parseAdditionalDetails(
            applicantDetailsJson['AdditionalApplicantDetails'],
          ),
          applicantIdentityDetails: _parseIdentityDetails(
            applicantDetailsJson['ApplicantIdentityDetails'],
          ),
        ),
      );
    } catch (e) {
      return DBSFormData.empty();
    }
  }

  static List<String> _parseMiddlenames(dynamic middlenamesJson) {
    if (middlenamesJson == null) return [];
    if (middlenamesJson is Map && middlenamesJson.containsKey('Middlename')) {
      final middlename = middlenamesJson['Middlename'];
      if (middlename is List) {
        return middlename.cast<String>();
      } else if (middlename is String) {
        return [middlename];
      }
    }
    return [];
  }

  static CurrentAddressData _parseCurrentAddress(dynamic addressJson) {
    if (addressJson == null) return CurrentAddressData.empty();

    final addressMap = addressJson as Map<String, dynamic>;
    final address = addressMap['Address'] as Map<String, dynamic>? ?? {};

    return CurrentAddressData(
      addressLine1: address['AddressLine1'] ?? '',
      addressLine2: address['AddressLine2'] ?? '',
      addressTown: address['AddressTown'] ?? '',
      addressCounty: address['AddressCounty'] ?? '',
      postcode: address['Postcode'] ?? '',
      countryCode: address['CountryCode'] ?? 'GB',
      residentFromGyearMonth: addressMap['ResidentFromGyearMonth'] ?? '',
    );
  }

  static List<PreviousAddressData> _parsePreviousAddresses(
    dynamic addressesJson,
  ) {
    if (addressesJson == null) return [];
    if (addressesJson is! List) return [];

    return addressesJson.map<PreviousAddressData>((addressJson) {
      final addressMap = addressJson as Map<String, dynamic>;
      final address = addressMap['Address'] as Map<String, dynamic>? ?? {};
      final residentDates =
          addressMap['ResidentDates'] as Map<String, dynamic>? ?? {};

      return PreviousAddressData(
        addressLine1: address['AddressLine1'] ?? '',
        addressLine2: address['AddressLine2'] ?? '',
        addressTown: address['AddressTown'] ?? '',
        addressCounty: address['AddressCounty'] ?? '',
        postcode: address['Postcode'] ?? '',
        countryCode: address['CountryCode'] ?? 'GB',
        residentFromGyearMonth: residentDates['ResidentFromGyearMonth'] ?? '',
        residentToGyearMonth: residentDates['ResidentToGyearMonth'] ?? '',
      );
    }).toList();
  }

  static AdditionalApplicantDetailsData _parseAdditionalDetails(
    dynamic detailsJson,
  ) {
    if (detailsJson == null) return AdditionalApplicantDetailsData.empty();

    final detailsMap = detailsJson as Map<String, dynamic>;

    return AdditionalApplicantDetailsData(
      birthSurname: detailsMap['BirthSurname'] ?? '',
      birthSurnameUntil: detailsMap['BirthSurnameUntil'] ?? '',
      otherSurnames: _parseOtherSurnames(detailsMap['OtherSurnames']),
      otherForenames: _parseOtherForenames(detailsMap['OtherForenames']),
      birthTown: detailsMap['BirthTown'] ?? '',
      birthCounty: detailsMap['BirthCounty'] ?? '',
      birthCountry: detailsMap['BirthCountry'] ?? 'GB',
      birthNationality: detailsMap['BirthNationality'] ?? 'BRITISH',
      unspentConvictions: detailsMap['UnspentConvictions'] ?? 'n',
      declarationByApplicant: detailsMap['DeclarationByApplicant'] ?? 'n',
      languagePreference: detailsMap['LanguagePreference'] ?? 'english',
    );
  }

  static List<OtherSurnameData> _parseOtherSurnames(dynamic surnamesJson) {
    if (surnamesJson == null) return [];
    if (surnamesJson is Map && surnamesJson.containsKey('OtherSurname')) {
      final surnames = surnamesJson['OtherSurname'];
      if (surnames is List) {
        return surnames.map<OtherSurnameData>((surnameJson) {
          final surnameMap = surnameJson as Map<String, dynamic>;
          return OtherSurnameData(
            name: surnameMap['Name'] ?? '',
            usedFrom: surnameMap['UsedFrom'] ?? '',
            usedTo: surnameMap['UsedTo'] ?? '',
          );
        }).toList();
      }
    }
    return [];
  }

  static List<OtherForenameData> _parseOtherForenames(dynamic forenamesJson) {
    if (forenamesJson == null) return [];
    if (forenamesJson is Map && forenamesJson.containsKey('OtherForename')) {
      final forenames = forenamesJson['OtherForename'];
      if (forenames is List) {
        return forenames.map<OtherForenameData>((forenameJson) {
          final forenameMap = forenameJson as Map<String, dynamic>;
          return OtherForenameData(
            name: forenameMap['Name'] ?? '',
            usedFrom: forenameMap['UsedFrom'] ?? '',
            usedTo: forenameMap['UsedTo'] ?? '',
          );
        }).toList();
      }
    }
    return [];
  }

  static ApplicantIdentityDetailsData _parseIdentityDetails(
    dynamic identityJson,
  ) {
    if (identityJson == null) return ApplicantIdentityDetailsData.empty();

    final identityMap = identityJson as Map<String, dynamic>;

    return ApplicantIdentityDetailsData(
      identityVerified: identityMap['IdentityVerified'] ?? 'y',
      evidenceCheckedBy: identityMap['EvidenceCheckedBy'] ?? '',
      nationalInsuranceNumber: identityMap['NationalInsuranceNumber'] ?? '',
      passportDetails: _parsePassportDetails(identityMap['PassportDetails']),
      driverLicenceDetails: _parseDriverLicenceDetails(
        identityMap['DriverLicenceDetails'],
      ),
    );
  }

  static PassportDetailsData? _parsePassportDetails(dynamic passportJson) {
    if (passportJson == null) return null;

    final passportMap = passportJson as Map<String, dynamic>;
    return PassportDetailsData(
      passportNumber: passportMap['PassportNumber'] ?? '',
      passportDob: passportMap['PassportDob'] ?? '',
      passportNationality: passportMap['PassportNationality'] ?? '',
      passportIssueDate: passportMap['PassportIssueDate'] ?? '',
    );
  }

  static DriverLicenceDetailsData? _parseDriverLicenceDetails(
    dynamic licenceJson,
  ) {
    if (licenceJson == null) return null;

    final licenceMap = licenceJson as Map<String, dynamic>;
    return DriverLicenceDetailsData(
      driverLicenceNumber: licenceMap['DriverLicenceNumber'] ?? '',
      driverLicenceDOB: licenceMap['DriverLicenceDOB'] ?? '',
      driverLicenceType: licenceMap['DriverLicenceType'] ?? 'photo',
      driverLicenceValidFrom: licenceMap['DriverLicenceValidFrom'] ?? '',
      driverLicenceIssueCountry:
          licenceMap['DriverLicenceIssueCountry'] ?? 'GB',
    );
  }
}
