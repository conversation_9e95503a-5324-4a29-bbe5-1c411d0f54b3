import 'package:SolidCheck/features/dbs/data/models/dbs_api_request.dart';
import 'package:dio/dio.dart';

/// DBS API Service for saving form data
class DBSApiService {
  static String baseUrl = 'http://localhost:8001/api/v1';
  static String saveDataEndpoint = '/applications/save-data';
  static String formDataEndpoint = '/applications/{id}/form-data';

  static late Dio _dio;

  /// Initialize Dio instance with configuration
  static void initialize() {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors for logging and error handling
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      error: true,
    ));
  }

  /// Get Dio instance (initialize if not already done)
  static Dio get dio {
    try {
      return _dio;
    } catch (e) {
      initialize();
      return _dio;
    }
  }

  /// Save DBS form data to the API using the new flat format
  static Future<Map<String, dynamic>> saveFormData(
    Map<String, dynamic> saveDataRequest, {
    required String token,
  }) async {
    try {
      // Debug: Check the form_data types before sending
      final formData = saveDataRequest['form_data'] as Map<String, dynamic>?;
      if (formData != null) {
        formData.forEach((key, value) {
        });
      }

      final response = await dio.post(
        saveDataEndpoint,
        data: saveDataRequest,
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
          },
        ),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return {
          'success': true,
          'message': 'Form data saved successfully',
          'data': response.data,
        };
      } else {
        return {
          'success': false,
          'message': 'Failed to save form data: ${response.statusCode}',
          'error': response.data,
        };
      }
    } on DioException catch (e) {
      return _handleDioError(e);
    } catch (e) {
      return {
        'success': false,
        'message': 'Unexpected error occurred',
        'error': e.toString(),
      };
    }
  }

  /// Check form completion status for an application using the enhanced form-data endpoint
  static Future<Map<String, dynamic>> checkFormCompletionStatus(
    int applicationId, {
    required String token,
  }) async {
    try {
      final endpoint = formDataEndpoint.replaceAll('{id}', applicationId.toString());

      final response = await dio.get(
        endpoint,
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
          },
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data['data'];

        // Extract completion status from the enhanced response
        final completionStatus = data['completion_status'];
        final processStampStatus = completionStatus?['process_stamp_status'];

        // Create a compatible format for the existing UI
        final formCompletionData = {
          'application_id': data['application_id'],
          'form_completion': processStampStatus ?? {
            'is_completed': completionStatus?['is_complete'] ?? false,
            'completion_status': completionStatus?['is_complete'] == true ? 'completed' : 'not_started',
            'completed_at': null,
            'completed_by': null,
            'form_data': null,
            'applicant_name': data['applicant']?['name'] ?? 'Applicant',
            'message': completionStatus?['status_text'] ?? 'Form status unknown'
          },
          'form_data': data['form_data'], // Include the form_data from API response
          'can_proceed_to_documents': data['can_proceed_to_documents'] ?? false,
          'next_step': data['next_step'] ?? 'complete_form'
        };

        return {
          'success': true,
          'data': formCompletionData,
          'message': response.data['message'] ?? 'Form completion status retrieved successfully',
        };
      } else {
        return {
          'success': false,
          'message': 'Failed to check form completion status: ${response.statusCode}',
          'error': response.data,
        };
      }
    } on DioException catch (e) {
      return _handleDioError(e);
    } catch (e) {
      return {
        'success': false,
        'message': 'Unexpected error occurred while checking form completion status',
        'error': e.toString(),
      };
    }
  }

  /// Legacy method for backward compatibility - converts to new format
  @Deprecated('Use saveFormData with flat format instead')
  static Future<Map<String, dynamic>> saveFormDataLegacy(
    DBSApiRequest request, {
    required String token,
  }) async {
    return saveFormData(request.toJson(), token: token);
  }

  /// Handle Dio-specific errors
  static Map<String, dynamic> _handleDioError(DioException e) {
    String message;
    String errorDetails = e.toString();

    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        message = 'Connection timeout. Please check your internet connection.';
        break;
      case DioExceptionType.sendTimeout:
        message = 'Request timeout. Please try again.';
        break;
      case DioExceptionType.receiveTimeout:
        message = 'Server response timeout. Please try again.';
        break;
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        if (statusCode == 400) {
          message = 'Invalid form data. Please check your entries.';
        } else if (statusCode == 401) {
          message = 'Authentication failed. Please try again.';
        } else if (statusCode == 403) {
          message = 'Access denied. Please contact support.';
        } else if (statusCode == 404) {
          message = 'Service not found. Please contact support.';
        } else if (statusCode == 500) {
          message = 'Server error. Please try again later.';
        } else {
          message = 'Server error ($statusCode). Please try again.';
        }
        errorDetails = e.response?.data?.toString() ?? e.toString();
        break;
      case DioExceptionType.cancel:
        message = 'Request was cancelled.';
        break;
      case DioExceptionType.connectionError:
        message = 'No internet connection. Please check your network.';
        break;
      case DioExceptionType.badCertificate:
        message = 'Security certificate error. Please contact support.';
        break;
      case DioExceptionType.unknown:
        message = 'Network error occurred. Please try again.';
        break;
    }

    return {
      'success': false,
      'message': message,
      'error': errorDetails,
      'statusCode': e.response?.statusCode,
    };
  }

  /// Validate form data before submission
  static Map<String, dynamic> validateFormData(DBSApiRequest request) {
    List<String> errors = [];

    // Validate required fields
    if (request.applicantDetails.title.isEmpty) {
      errors.add('Title is required');
    }
    
    if (request.applicantDetails.forename.isEmpty) {
      errors.add('Forename is required');
    }
    
    if (request.applicantDetails.presentSurname.isEmpty) {
      errors.add('Present surname is required');
    }
    
    if (request.applicantDetails.dateOfBirth.isEmpty) {
      errors.add('Date of birth is required');
    }
    
    if (request.applicantDetails.gender.isEmpty) {
      errors.add('Gender is required');
    }

    // Validate current address
    if (request.applicantDetails.currentAddress.address.addressLine1.isEmpty) {
      errors.add('Current address line 1 is required');
    }
    
    if (request.applicantDetails.currentAddress.address.addressTown.isEmpty) {
      errors.add('Current address town is required');
    }
    
    if (request.applicantDetails.currentAddress.address.countryCode.isEmpty) {
      errors.add('Current address country is required');
    }

    // Validate UK postcode if country is GB
    if (request.applicantDetails.currentAddress.address.countryCode == 'GB' &&
        request.applicantDetails.currentAddress.address.postcode.isEmpty) {
      errors.add('Postcode is required for UK addresses');
    }

    // Validate birth details
    if (request.applicantDetails.additionalApplicantDetails.birthTown.isEmpty) {
      errors.add('Birth town is required');
    }
    
    if (request.applicantDetails.additionalApplicantDetails.birthCountry.isEmpty) {
      errors.add('Birth country is required');
    }
    
    if (request.applicantDetails.additionalApplicantDetails.birthNationality.isEmpty) {
      errors.add('Birth nationality is required');
    }

    // Validate contact number
    if (request.applicantDetails.additionalApplicantDetails.contactNumber.isEmpty) {
      errors.add('Contact number is required');
    }

    // Validate declarations
    if (request.applicantDetails.additionalApplicantDetails.declarationByApplicant != 'y') {
      errors.add('Declaration by applicant must be accepted');
    }

    // Validate identity verification
    if (request.applicantDetails.applicantIdentityDetails.identityVerified != 'y') {
      errors.add('Identity must be verified');
    }

    if (request.applicantDetails.applicantIdentityDetails.evidenceCheckedBy.isEmpty) {
      errors.add('Evidence checked by field is required');
    }

    return {
      'isValid': errors.isEmpty,
      'errors': errors,
    };
  }

  /// Format date for API (YYYY-MM-DD)
  static String formatDateForApi(DateTime date) {
    return '${date.year.toString().padLeft(4, '0')}-'
           '${date.month.toString().padLeft(2, '0')}-'
           '${date.day.toString().padLeft(2, '0')}';
  }

  /// Format year-month for API (YYYY-MM)
  static String formatYearMonthForApi(DateTime date) {
    return '${date.year.toString().padLeft(4, '0')}-'
           '${date.month.toString().padLeft(2, '0')}';
  }

  /// Convert form data to uppercase as required by API
  static String toUpperCaseForApi(String value) {
    return value.trim().toUpperCase();
  }

  /// Validate and format postcode
  static String formatPostcodeForApi(String postcode) {
    return postcode.trim().toUpperCase().replaceAll(' ', '');
  }

  /// Validate NI Number format
  static bool isValidNINumber(String niNumber) {
    if (niNumber.isEmpty) return true; // Optional field
    
    // Basic NI Number validation (2 letters, 6 digits, 1 letter)
    final regex = RegExp(r'^[A-Z]{2}[0-9]{6}[A-Z]$');
    return regex.hasMatch(niNumber.toUpperCase().replaceAll(' ', ''));
  }

  /// Validate UK postcode format
  static bool isValidUKPostcode(String postcode) {
    if (postcode.isEmpty) return false;
    
    final regex = RegExp(r'^[A-Z]{1,2}[0-9][A-Z0-9]?\s?[0-9][A-Z]{2}$');
    return regex.hasMatch(postcode.toUpperCase());
  }

  /// Validate date of birth (age between 16-110)
  static bool isValidDateOfBirth(String dateOfBirth) {
    try {
      final date = DateTime.parse(dateOfBirth);
      final now = DateTime.now();
      final age = now.difference(date).inDays / 365.25;
      
      return !date.isAfter(now) && age >= 16 && age <= 110;
    } catch (e) {
      return false;
    }
  }

  /// Validate year format (YYYY)
  static bool isValidYear(String year) {
    if (year.isEmpty) return true; // Most year fields are optional
    
    final regex = RegExp(r'^\d{4}$');
    if (!regex.hasMatch(year)) return false;
    
    final yearInt = int.parse(year);
    final currentYear = DateTime.now().year;
    
    return yearInt <= currentYear && yearInt >= 1900;
  }

  /// Validate year-month format (YYYY-MM)
  static bool isValidYearMonth(String yearMonth) {
    if (yearMonth.isEmpty) return false;
    
    final regex = RegExp(r'^\d{4}-\d{2}$');
    if (!regex.hasMatch(yearMonth)) return false;
    
    try {
      final parts = yearMonth.split('-');
      final year = int.parse(parts[0]);
      final month = int.parse(parts[1]);
      
      if (month < 1 || month > 12) return false;
      
      final date = DateTime(year, month);
      return !date.isAfter(DateTime.now());
    } catch (e) {
      return false;
    }
  }

  /// Validate country code (2-letter ISO)
  static bool isValidCountryCode(String countryCode) {
    return countryCode.length == 2 && RegExp(r'^[A-Z]{2}$').hasMatch(countryCode);
  }

  /// Validate personal name format
  static bool isValidPersonalName(String name) {
    if (name.isEmpty) return false;
    if (name.length > 60) return false;

    final regex = RegExp(r"^([A-Z]+)|([A-Z][A-Z &'-]*[A-Z])$");
    return regex.hasMatch(name.toUpperCase());
  }

  /// Validate address format
  static bool isValidAddressField(String address, {int maxLength = 60}) {
    if (address.isEmpty) return false;
    if (address.length > maxLength) return false;

    final regex = RegExp(r"^([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])$");
    return regex.hasMatch(address.toUpperCase());
  }

  /// Validate contact number format
  static bool isValidContactNumber(String contactNumber) {
    if (contactNumber.isEmpty) return false;
    if (contactNumber.length > 30) return false;

    final regex = RegExp(r'^([A-Z0-9()/\-&]+)|([A-Z0-9()/\-&][A-Z0-9()/\-&]*[A-Z0-9()/\-&])$');
    return regex.hasMatch(contactNumber.toUpperCase());
  }
}
