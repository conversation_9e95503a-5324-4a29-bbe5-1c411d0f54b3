class PostcodeLookupResponse {
  final bool success;
  final int count;
  final List<AddressResult> addresses;

  PostcodeLookupResponse({
    required this.success,
    required this.count,
    required this.addresses,
  });

  factory PostcodeLookupResponse.fromJson(Map<String, dynamic> json) {
    return PostcodeLookupResponse(
      success: json['success'] ?? false,
      count: json['count'] ?? 0,
      addresses: (json['addresses'] as List<dynamic>?)
              ?.map((address) => AddressResult.fromJson(address))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'count': count,
      'addresses': addresses.map((address) => address.toJson()).toList(),
    };
  }
}

class AddressResult {
  final String addressLine1;
  final String addressLine2;
  final String townCity;
  final String postcode;
  final String udprn;

  AddressResult({
    required this.addressLine1,
    required this.addressLine2,
    required this.townCity,
    required this.postcode,
    required this.udprn,
  });

  factory AddressResult.fromJson(Map<String, dynamic> json) {
    return AddressResult(
      addressLine1: json['address_line_1'] ?? '',
      addressLine2: json['address_line_2'] ?? '',
      townCity: json['town_city'] ?? '',
      postcode: json['postcode'] ?? '',
      udprn: json['udprn'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'address_line_1': addressLine1,
      'address_line_2': addressLine2,
      'town_city': townCity,
      'postcode': postcode,
      'udprn': udprn,
    };
  }

  String get displayAddress {
    final parts = <String>[];
    
    if (addressLine1.isNotEmpty) {
      parts.add(addressLine1);
    }
    
    if (addressLine2.isNotEmpty) {
      parts.add(addressLine2);
    }
    
    if (townCity.isNotEmpty) {
      parts.add(townCity);
    }
    
    if (postcode.isNotEmpty) {
      parts.add(postcode);
    }
    
    return parts.join(', ');
  }
}
