/// DBS API Request Models
/// Contains all data models for the DBS Enhanced/Standard form API submission
/// Field names match the API specification exactly
library;

class DBSApiRequest {
  final ApplicantDetailsApi applicantDetails;
  final PotentialEmployerDetailsApi? potentialEmployerDetails;

  DBSApiRequest({
    required this.applicantDetails,
    this.potentialEmployerDetails,
  });

  Map<String, dynamic> toJson() {
    return {
      'eBulkApplication': {
        'ApplicantDetails': applicantDetails.toJson(),
        if (potentialEmployerDetails != null)
          'PotentialEmployerDetails': potentialEmployerDetails!.toJson(),
      },
    };
  }
}

/// ApplicantDetails Structure - Main applicant information
class ApplicantDetailsApi {
  final String title;
  final String forename;
  final MiddlenamesApi? middlenames;
  final String presentSurname;
  final String dateOfBirth; // YYYY-MM-DD format
  final String gender; // 'male' or 'female'
  final String niNumber;
  final CurrentAddressApi currentAddress;
  final List<PreviousAddressApi> previousAddresses;
  final AdditionalApplicantDetailsApi additionalApplicantDetails;
  final ApplicantIdentityDetailsApi applicantIdentityDetails;

  ApplicantDetailsApi({
    required this.title,
    required this.forename,
    this.middlenames,
    required this.presentSurname,
    required this.dateOfBirth,
    required this.gender,
    required this.niNumber,
    required this.currentAddress,
    required this.previousAddresses,
    required this.additionalApplicantDetails,
    required this.applicantIdentityDetails,
  });

  Map<String, dynamic> toJson() {
    return {
      'Title': title,
      'Forename': forename,
      if (middlenames != null) 'Middlenames': middlenames!.toJson(),
      'PresentSurname': presentSurname,
      'DateOfBirth': dateOfBirth,
      'Gender': gender,
      'NINumber': niNumber,
      'CurrentAddress': currentAddress.toJson(),
      if (previousAddresses.isNotEmpty)
        'PreviousAddress': previousAddresses.map((addr) => addr.toJson()).toList(),
      'AdditionalApplicantDetails': additionalApplicantDetails.toJson(),
      'ApplicantIdentityDetails': applicantIdentityDetails.toJson(),
    };
  }
}

/// Middlenames Container (1-3 middle names)
class MiddlenamesApi {
  final List<String> middlename; // 1-3 elements

  MiddlenamesApi({required this.middlename});

  Map<String, dynamic> toJson() {
    return {
      'Middlename': middlename,
    };
  }
}

/// CurrentAddress Structure
class CurrentAddressApi {
  final AddressApi address;
  final String residentFromGyearMonth; // YYYY-MM format

  CurrentAddressApi({
    required this.address,
    required this.residentFromGyearMonth,
  });

  Map<String, dynamic> toJson() {
    return {
      'Address': address.toJson(),
      'ResidentFromGyearMonth': residentFromGyearMonth,
    };
  }
}

/// Address Structure (used in Current and Previous addresses)
class AddressApi {
  final String addressLine1;
  final String addressLine2;
  final String addressTown;
  final String addressCounty;
  final String postcode;
  final String countryCode; // 2-letter ISO Country Code

  AddressApi({
    required this.addressLine1,
    required this.addressLine2,
    required this.addressTown,
    required this.addressCounty,
    required this.postcode,
    required this.countryCode,
  });

  Map<String, dynamic> toJson() {
    return {
      'AddressLine1': addressLine1,
      'AddressLine2': addressLine2,
      'AddressTown': addressTown,
      'AddressCounty': addressCounty,
      'Postcode': postcode,
      'CountryCode': countryCode,
    };
  }
}

/// PreviousAddress Structure (repeatable 0-200 times)
class PreviousAddressApi {
  final AddressApi address;
  final ResidentDatesApi residentDates;

  PreviousAddressApi({
    required this.address,
    required this.residentDates,
  });

  Map<String, dynamic> toJson() {
    return {
      'Address': address.toJson(),
      'ResidentDates': residentDates.toJson(),
    };
  }
}

/// ResidentDates Structure
class ResidentDatesApi {
  final String residentFromGyearMonth; // YYYY-MM format
  final String residentToGyearMonth; // YYYY-MM format

  ResidentDatesApi({
    required this.residentFromGyearMonth,
    required this.residentToGyearMonth,
  });

  Map<String, dynamic> toJson() {
    return {
      'ResidentFromGyearMonth': residentFromGyearMonth,
      'ResidentToGyearMonth': residentToGyearMonth,
    };
  }
}

/// AdditionalApplicantDetails Structure
class AdditionalApplicantDetailsApi {
  final String birthSurname;
  final String birthSurnameUntil; // YYYY format
  final OtherSurnamesApi? otherSurnames;
  final OtherForenamesApi? otherForenames;
  final String birthTown;
  final String birthCounty;
  final String birthCountry; // 2-letter ISO Country Code
  final String birthNationality;
  final String contactNumber;
  final String unspentConvictions; // 'y' or 'n'
  final String declarationByApplicant; // 'y' or 'n' (must be 'y')
  final String languagePreference; // 'english' or 'welsh'

  AdditionalApplicantDetailsApi({
    required this.birthSurname,
    required this.birthSurnameUntil,
    this.otherSurnames,
    this.otherForenames,
    required this.birthTown,
    required this.birthCounty,
    required this.birthCountry,
    required this.birthNationality,
    required this.contactNumber,
    required this.unspentConvictions,
    required this.declarationByApplicant,
    required this.languagePreference,
  });

  Map<String, dynamic> toJson() {
    return {
      'BirthSurname': birthSurname,
      'BirthSurnameUntil': birthSurnameUntil,
      if (otherSurnames != null) 'OtherSurnames': otherSurnames!.toJson(),
      if (otherForenames != null) 'OtherForenames': otherForenames!.toJson(),
      'BirthTown': birthTown,
      'BirthCounty': birthCounty,
      'BirthCountry': birthCountry,
      'BirthNationality': birthNationality,
      'ContactNumber': contactNumber,
      'UnspentConvictions': unspentConvictions,
      'DeclarationByApplicant': declarationByApplicant,
      'LanguagePreference': languagePreference,
    };
  }
}

/// OtherSurnames Container
class OtherSurnamesApi {
  final List<OtherSurnameApi> otherSurname; // 1-200 elements

  OtherSurnamesApi({required this.otherSurname});

  Map<String, dynamic> toJson() {
    return {
      'OtherSurname': otherSurname.map((surname) => surname.toJson()).toList(),
    };
  }
}

/// OtherSurname Structure
class OtherSurnameApi {
  final String name;
  final String usedFrom; // YYYY format
  final String usedTo; // YYYY format

  OtherSurnameApi({
    required this.name,
    required this.usedFrom,
    required this.usedTo,
  });

  Map<String, dynamic> toJson() {
    return {
      'Name': name,
      'UsedFrom': usedFrom,
      'UsedTo': usedTo,
    };
  }
}

/// OtherForenames Container
class OtherForenamesApi {
  final List<OtherForenameApi> otherForename; // 1-200 elements

  OtherForenamesApi({required this.otherForename});

  Map<String, dynamic> toJson() {
    return {
      'OtherForename': otherForename.map((forename) => forename.toJson()).toList(),
    };
  }
}

/// OtherForename Structure
class OtherForenameApi {
  final String name;
  final String usedFrom; // YYYY format
  final String usedTo; // YYYY format

  OtherForenameApi({
    required this.name,
    required this.usedFrom,
    required this.usedTo,
  });

  Map<String, dynamic> toJson() {
    return {
      'Name': name,
      'UsedFrom': usedFrom,
      'UsedTo': usedTo,
    };
  }
}

/// ApplicantIdentityDetails Structure
class ApplicantIdentityDetailsApi {
  final String identityVerified; // 'y' or 'n' (must be 'y')
  final String evidenceCheckedBy;
  final PassportDetailsApi? passportDetails;
  final DriverLicenceDetailsApi? driverLicenceDetails;

  ApplicantIdentityDetailsApi({
    required this.identityVerified,
    required this.evidenceCheckedBy,
    this.passportDetails,
    this.driverLicenceDetails,
  });

  Map<String, dynamic> toJson() {
    return {
      'IdentityVerified': identityVerified,
      'EvidenceCheckedBy': evidenceCheckedBy,
      if (passportDetails != null) 'PassportDetails': passportDetails!.toJson(),
      if (driverLicenceDetails != null) 'DriverLicenceDetails': driverLicenceDetails!.toJson(),
    };
  }
}

/// PassportDetails Structure (optional)
class PassportDetailsApi {
  final String passportNumber;
  final String passportDob; // YYYY-MM-DD format (must match DateOfBirth)
  final String passportNationality;
  final String passportIssueDate; // YYYY-MM-DD format

  PassportDetailsApi({
    required this.passportNumber,
    required this.passportDob,
    required this.passportNationality,
    required this.passportIssueDate,
  });

  Map<String, dynamic> toJson() {
    return {
      'PassportNumber': passportNumber,
      'PassportDob': passportDob,
      'PassportNationality': passportNationality,
      'PassportIssueDate': passportIssueDate,
    };
  }
}

/// DriverLicenceDetails Structure (optional)
class DriverLicenceDetailsApi {
  final String driverLicenceNumber;
  final String driverLicenceDOB; // YYYY-MM-DD format (must match DateOfBirth)
  final String driverLicenceType; // 'paper' or 'photo'
  final String driverLicenceValidFrom; // YYYY-MM-DD format
  final String driverLicenceIssueCountry; // 2-letter ISO Country Code

  DriverLicenceDetailsApi({
    required this.driverLicenceNumber,
    required this.driverLicenceDOB,
    required this.driverLicenceType,
    required this.driverLicenceValidFrom,
    required this.driverLicenceIssueCountry,
  });

  Map<String, dynamic> toJson() {
    return {
      'DriverLicenceNumber': driverLicenceNumber,
      'DriverLicenceDOB': driverLicenceDOB,
      'DriverLicenceType': driverLicenceType,
      'DriverLicenceValidFrom': driverLicenceValidFrom,
      'DriverLicenceIssueCountry': driverLicenceIssueCountry,
    };
  }
}

/// PotentialEmployerDetails Structure (optional)
class PotentialEmployerDetailsApi {
  final String positionAppliedFor;
  final String organisationName;

  PotentialEmployerDetailsApi({
    required this.positionAppliedFor,
    required this.organisationName,
  });

  Map<String, dynamic> toJson() {
    return {
      'PositionAppliedFor': positionAppliedFor,
      'OrganisationName': organisationName,
    };
  }
}
