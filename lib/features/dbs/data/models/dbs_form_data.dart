/// DBS Form Data Models
/// Contains all data models for the DBS Enhanced/Standard form
/// Field names match the API specification exactly
library;

class DBSFormData {
  final ApplicantDetailsData applicantDetails;

  DBSFormData({
    required this.applicantDetails,
  });

  DBSFormData copyWith({
    ApplicantDetailsData? applicantDetails,
  }) {
    return DBSFormData(
      applicantDetails: applicantDetails ?? this.applicantDetails,
    );
  }

  factory DBSFormData.empty() {
    return DBSFormData(
      applicantDetails: ApplicantDetailsData.empty(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ApplicantDetails': applicantDetails.toJson(),
    };
  }

  factory DBSFormData.fromJson(Map<String, dynamic> json) {
    return DBSFormData(
      applicantDetails: ApplicantDetailsData.fromJson(json['ApplicantDetails'] ?? {}),
    );
  }
}

/// ApplicantDetails Structure - Main applicant information
class ApplicantDetailsData {
  // Basic personal details
  final String title;
  final String forename;
  final List<String> middlenames; // Can have 1-3 middle names
  final String presentSurname;
  final String dateOfBirth; // YYYY-MM-DD format
  final String gender; // 'male' or 'female'
  final String niNumber;
  final String email;
  final String contactNumber;

  // Current address
  final CurrentAddressData currentAddress;

  // Previous addresses (0-200)
  final List<PreviousAddressData> previousAddresses;

  // Additional details
  final AdditionalApplicantDetailsData additionalApplicantDetails;

  // Identity details
  final ApplicantIdentityDetailsData applicantIdentityDetails;

  ApplicantDetailsData({
    required this.title,
    required this.forename,
    required this.middlenames,
    required this.presentSurname,
    required this.dateOfBirth,
    required this.gender,
    required this.niNumber,
    required this.email,
    required this.contactNumber,
    required this.currentAddress,
    required this.previousAddresses,
    required this.additionalApplicantDetails,
    required this.applicantIdentityDetails,
  });

  ApplicantDetailsData copyWith({
    String? title,
    String? forename,
    List<String>? middlenames,
    String? presentSurname,
    String? dateOfBirth,
    String? gender,
    String? niNumber,
    String? email,
    String? contactNumber,
    CurrentAddressData? currentAddress,
    List<PreviousAddressData>? previousAddresses,
    AdditionalApplicantDetailsData? additionalApplicantDetails,
    ApplicantIdentityDetailsData? applicantIdentityDetails,
  }) {
    return ApplicantDetailsData(
      title: title ?? this.title,
      forename: forename ?? this.forename,
      middlenames: middlenames ?? this.middlenames,
      presentSurname: presentSurname ?? this.presentSurname,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      niNumber: niNumber ?? this.niNumber,
      email: email ?? this.email,
      contactNumber: contactNumber ?? this.contactNumber,
      currentAddress: currentAddress ?? this.currentAddress,
      previousAddresses: previousAddresses ?? this.previousAddresses,
      additionalApplicantDetails: additionalApplicantDetails ?? this.additionalApplicantDetails,
      applicantIdentityDetails: applicantIdentityDetails ?? this.applicantIdentityDetails,
    );
  }

  factory ApplicantDetailsData.empty() {
    return ApplicantDetailsData(
      title: '',
      forename: '',
      middlenames: [],
      presentSurname: '',
      dateOfBirth: '',
      gender: '',
      niNumber: '',
      email: '',
      contactNumber: '',
      currentAddress: CurrentAddressData.empty(),
      previousAddresses: [],
      additionalApplicantDetails: AdditionalApplicantDetailsData.empty(),
      applicantIdentityDetails: ApplicantIdentityDetailsData.empty(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'Title': title,
      'Forename': forename,
      'Middlenames': middlenames.isNotEmpty ? {
        'Middlename': middlenames,
      } : null,
      'PresentSurname': presentSurname,
      'DateOfBirth': dateOfBirth,
      'Gender': gender,
      'NINumber': niNumber,
      'Email': email,
      'ContactNumber': contactNumber,
      'CurrentAddress': currentAddress.toJson(),
      'PreviousAddress': previousAddresses.map((addr) => addr.toJson()).toList(),
      'AdditionalApplicantDetails': additionalApplicantDetails.toJson(),
      'ApplicantIdentityDetails': applicantIdentityDetails.toJson(),
    };
  }

  factory ApplicantDetailsData.fromJson(Map<String, dynamic> json) {
    return ApplicantDetailsData(
      title: json['Title'] ?? '',
      forename: json['Forename'] ?? '',
      middlenames: json['Middlenames'] != null
          ? List<String>.from(json['Middlenames']['Middlename'] ?? [])
          : [],
      presentSurname: json['PresentSurname'] ?? '',
      dateOfBirth: json['DateOfBirth'] ?? '',
      gender: json['Gender'] ?? '',
      niNumber: json['NINumber'] ?? '',
      email: json['Email'] ?? '',
      contactNumber: json['ContactNumber'] ?? '',
      currentAddress: CurrentAddressData.fromJson(json['CurrentAddress'] ?? {}),
      previousAddresses: (json['PreviousAddress'] as List<dynamic>?)
          ?.map((addr) => PreviousAddressData.fromJson(addr as Map<String, dynamic>))
          .toList() ?? <PreviousAddressData>[],
      additionalApplicantDetails: AdditionalApplicantDetailsData.fromJson(
          json['AdditionalApplicantDetails'] ?? {}),
      applicantIdentityDetails: ApplicantIdentityDetailsData.fromJson(
          json['ApplicantIdentityDetails'] ?? {}),
    );
  }
}

/// CurrentAddress Structure
class CurrentAddressData {
  final String addressLine1;
  final String addressLine2;
  final String addressTown;
  final String addressCounty;
  final String postcode;
  final String countryCode; // 2-letter ISO Country Code
  final String residentFromGyearMonth; // YYYY-MM format

  CurrentAddressData({
    required this.addressLine1,
    required this.addressLine2,
    required this.addressTown,
    required this.addressCounty,
    required this.postcode,
    required this.countryCode,
    required this.residentFromGyearMonth,
  });

  CurrentAddressData copyWith({
    String? addressLine1,
    String? addressLine2,
    String? addressTown,
    String? addressCounty,
    String? postcode,
    String? countryCode,
    String? residentFromGyearMonth,
  }) {
    return CurrentAddressData(
      addressLine1: addressLine1 ?? this.addressLine1,
      addressLine2: addressLine2 ?? this.addressLine2,
      addressTown: addressTown ?? this.addressTown,
      addressCounty: addressCounty ?? this.addressCounty,
      postcode: postcode ?? this.postcode,
      countryCode: countryCode ?? this.countryCode,
      residentFromGyearMonth: residentFromGyearMonth ?? this.residentFromGyearMonth,
    );
  }

  factory CurrentAddressData.empty() {
    return CurrentAddressData(
      addressLine1: '',
      addressLine2: '',
      addressTown: '',
      addressCounty: '',
      postcode: '',
      countryCode: 'GB', // Default to GB
      residentFromGyearMonth: '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'Address': {
        'AddressLine1': addressLine1,
        'AddressLine2': addressLine2,
        'AddressTown': addressTown,
        'AddressCounty': addressCounty,
        'Postcode': postcode,
        'CountryCode': countryCode,
      },
      'ResidentFromGyearMonth': residentFromGyearMonth,
    };
  }

  factory CurrentAddressData.fromJson(Map<String, dynamic> json) {
    final address = json['Address'] ?? {};
    return CurrentAddressData(
      addressLine1: address['AddressLine1'] ?? '',
      addressLine2: address['AddressLine2'] ?? '',
      addressTown: address['AddressTown'] ?? '',
      addressCounty: address['AddressCounty'] ?? '',
      postcode: address['Postcode'] ?? '',
      countryCode: address['CountryCode'] ?? 'GB',
      residentFromGyearMonth: json['ResidentFromGyearMonth'] ?? '',
    );
  }
}

/// PreviousAddress Structure (repeatable 0-200 times)
class PreviousAddressData {
  final String addressLine1;
  final String addressLine2;
  final String addressTown;
  final String addressCounty;
  final String postcode;
  final String countryCode; // 2-letter ISO Country Code
  final String residentFromGyearMonth; // YYYY-MM format
  final String residentToGyearMonth; // YYYY-MM format

  PreviousAddressData({
    required this.addressLine1,
    required this.addressLine2,
    required this.addressTown,
    required this.addressCounty,
    required this.postcode,
    required this.countryCode,
    required this.residentFromGyearMonth,
    required this.residentToGyearMonth,
  });

  PreviousAddressData copyWith({
    String? addressLine1,
    String? addressLine2,
    String? addressTown,
    String? addressCounty,
    String? postcode,
    String? countryCode,
    String? residentFromGyearMonth,
    String? residentToGyearMonth,
  }) {
    return PreviousAddressData(
      addressLine1: addressLine1 ?? this.addressLine1,
      addressLine2: addressLine2 ?? this.addressLine2,
      addressTown: addressTown ?? this.addressTown,
      addressCounty: addressCounty ?? this.addressCounty,
      postcode: postcode ?? this.postcode,
      countryCode: countryCode ?? this.countryCode,
      residentFromGyearMonth: residentFromGyearMonth ?? this.residentFromGyearMonth,
      residentToGyearMonth: residentToGyearMonth ?? this.residentToGyearMonth,
    );
  }

  factory PreviousAddressData.empty() {
    return PreviousAddressData(
      addressLine1: '',
      addressLine2: '',
      addressTown: '',
      addressCounty: '',
      postcode: '',
      countryCode: 'GB',
      residentFromGyearMonth: '',
      residentToGyearMonth: '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'Address': {
        'AddressLine1': addressLine1,
        'AddressLine2': addressLine2,
        'AddressTown': addressTown,
        'AddressCounty': addressCounty,
        'Postcode': postcode,
        'CountryCode': countryCode,
      },
      'ResidentDates': {
        'ResidentFromGyearMonth': residentFromGyearMonth,
        'ResidentToGyearMonth': residentToGyearMonth,
      },
    };
  }

  factory PreviousAddressData.fromJson(Map<String, dynamic> json) {
    final address = json['Address'] ?? {};
    final residentDates = json['ResidentDates'] ?? {};
    return PreviousAddressData(
      addressLine1: address['AddressLine1'] ?? '',
      addressLine2: address['AddressLine2'] ?? '',
      addressTown: address['AddressTown'] ?? '',
      addressCounty: address['AddressCounty'] ?? '',
      postcode: address['Postcode'] ?? '',
      countryCode: address['CountryCode'] ?? 'GB',
      residentFromGyearMonth: residentDates['ResidentFromGyearMonth'] ?? '',
      residentToGyearMonth: residentDates['ResidentToGyearMonth'] ?? '',
    );
  }
}

/// AdditionalApplicantDetails Structure
class AdditionalApplicantDetailsData {
  final String birthSurname;
  final String birthSurnameUntil; // YYYY format
  final List<OtherSurnameData> otherSurnames; // 1-200 elements
  final List<OtherForenameData> otherForenames; // 1-200 elements
  final String birthTown;
  final String birthCounty;
  final String birthCountry; // 2-letter ISO Country Code
  final String birthNationality;
  final String unspentConvictions; // 'y' or 'n'
  final String declarationByApplicant; // 'y' or 'n' (must be 'y')
  final String languagePreference; // 'english' or 'welsh'

  AdditionalApplicantDetailsData({
    required this.birthSurname,
    required this.birthSurnameUntil,
    required this.otherSurnames,
    required this.otherForenames,
    required this.birthTown,
    required this.birthCounty,
    required this.birthCountry,
    required this.birthNationality,
    required this.unspentConvictions,
    required this.declarationByApplicant,
    required this.languagePreference,
  });

  AdditionalApplicantDetailsData copyWith({
    String? birthSurname,
    String? birthSurnameUntil,
    List<OtherSurnameData>? otherSurnames,
    List<OtherForenameData>? otherForenames,
    String? birthTown,
    String? birthCounty,
    String? birthCountry,
    String? birthNationality,
    String? unspentConvictions,
    String? declarationByApplicant,
    String? languagePreference,
  }) {
    return AdditionalApplicantDetailsData(
      birthSurname: birthSurname ?? this.birthSurname,
      birthSurnameUntil: birthSurnameUntil ?? this.birthSurnameUntil,
      otherSurnames: otherSurnames ?? this.otherSurnames,
      otherForenames: otherForenames ?? this.otherForenames,
      birthTown: birthTown ?? this.birthTown,
      birthCounty: birthCounty ?? this.birthCounty,
      birthCountry: birthCountry ?? this.birthCountry,
      birthNationality: birthNationality ?? this.birthNationality,
      unspentConvictions: unspentConvictions ?? this.unspentConvictions,
      declarationByApplicant: declarationByApplicant ?? this.declarationByApplicant,
      languagePreference: languagePreference ?? this.languagePreference,
    );
  }

  factory AdditionalApplicantDetailsData.empty() {
    return AdditionalApplicantDetailsData(
      birthSurname: '',
      birthSurnameUntil: '',
      otherSurnames: [],
      otherForenames: [],
      birthTown: '',
      birthCounty: '',
      birthCountry: 'GB',
      birthNationality: 'BRITISH',
      unspentConvictions: 'n',
      declarationByApplicant: 'n',
      languagePreference: 'english',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'BirthSurname': birthSurname,
      'BirthSurnameUntil': birthSurnameUntil,
      'OtherSurnames': otherSurnames.isNotEmpty ? {
        'OtherSurname': otherSurnames.map((surname) => surname.toJson()).toList(),
      } : null,
      'OtherForenames': otherForenames.isNotEmpty ? {
        'OtherForename': otherForenames.map((forename) => forename.toJson()).toList(),
      } : null,
      'BirthTown': birthTown,
      'BirthCounty': birthCounty,
      'BirthCountry': birthCountry,
      'BirthNationality': birthNationality,
      'UnspentConvictions': unspentConvictions,
      'DeclarationByApplicant': declarationByApplicant,
      'LanguagePreference': languagePreference,
    };
  }

  factory AdditionalApplicantDetailsData.fromJson(Map<String, dynamic> json) {
    return AdditionalApplicantDetailsData(
      birthSurname: json['BirthSurname'] ?? '',
      birthSurnameUntil: json['BirthSurnameUntil'] ?? '',
      otherSurnames: json['OtherSurnames'] != null
          ? (json['OtherSurnames']['OtherSurname'] as List<dynamic>?)
              ?.map((surname) => OtherSurnameData.fromJson(surname))
              .toList() ?? []
          : [],
      otherForenames: json['OtherForenames'] != null
          ? (json['OtherForenames']['OtherForename'] as List<dynamic>?)
              ?.map((forename) => OtherForenameData.fromJson(forename))
              .toList() ?? []
          : [],
      birthTown: json['BirthTown'] ?? '',
      birthCounty: json['BirthCounty'] ?? '',
      birthCountry: json['BirthCountry'] ?? 'GB',
      birthNationality: json['BirthNationality'] ?? 'BRITISH',
      unspentConvictions: json['UnspentConvictions'] ?? 'n',
      declarationByApplicant: json['DeclarationByApplicant'] ?? 'n',
      languagePreference: json['LanguagePreference'] ?? 'english',
    );
  }
}

/// OtherSurname Structure (repeatable 1-200 times)
class OtherSurnameData {
  final String name;
  final String usedFrom; // YYYY format
  final String usedTo; // YYYY format

  OtherSurnameData({
    required this.name,
    required this.usedFrom,
    required this.usedTo,
  });

  OtherSurnameData copyWith({
    String? name,
    String? usedFrom,
    String? usedTo,
  }) {
    return OtherSurnameData(
      name: name ?? this.name,
      usedFrom: usedFrom ?? this.usedFrom,
      usedTo: usedTo ?? this.usedTo,
    );
  }

  factory OtherSurnameData.empty() {
    return OtherSurnameData(
      name: '',
      usedFrom: '',
      usedTo: '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'Name': name,
      'UsedFrom': usedFrom,
      'UsedTo': usedTo,
    };
  }

  factory OtherSurnameData.fromJson(Map<String, dynamic> json) {
    return OtherSurnameData(
      name: json['Name'] ?? '',
      usedFrom: json['UsedFrom'] ?? '',
      usedTo: json['UsedTo'] ?? '',
    );
  }
}

/// OtherForename Structure (repeatable 1-200 times)
class OtherForenameData {
  final String name;
  final String usedFrom; // YYYY format
  final String usedTo; // YYYY format

  OtherForenameData({
    required this.name,
    required this.usedFrom,
    required this.usedTo,
  });

  OtherForenameData copyWith({
    String? name,
    String? usedFrom,
    String? usedTo,
  }) {
    return OtherForenameData(
      name: name ?? this.name,
      usedFrom: usedFrom ?? this.usedFrom,
      usedTo: usedTo ?? this.usedTo,
    );
  }

  factory OtherForenameData.empty() {
    return OtherForenameData(
      name: '',
      usedFrom: '',
      usedTo: '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'Name': name,
      'UsedFrom': usedFrom,
      'UsedTo': usedTo,
    };
  }

  factory OtherForenameData.fromJson(Map<String, dynamic> json) {
    return OtherForenameData(
      name: json['Name'] ?? '',
      usedFrom: json['UsedFrom'] ?? '',
      usedTo: json['UsedTo'] ?? '',
    );
  }
}



/// ApplicantIdentityDetails Structure
class ApplicantIdentityDetailsData {
  final String identityVerified; // 'y' or 'n' (must be 'y')
  final String evidenceCheckedBy;
  final String nationalInsuranceNumber; // National Insurance Number
  final PassportDetailsData? passportDetails; // Optional structure
  final DriverLicenceDetailsData? driverLicenceDetails; // Optional structure

  ApplicantIdentityDetailsData({
    required this.identityVerified,
    required this.evidenceCheckedBy,
    required this.nationalInsuranceNumber,
    this.passportDetails,
    this.driverLicenceDetails,
  });

  ApplicantIdentityDetailsData copyWith({
    String? identityVerified,
    String? evidenceCheckedBy,
    String? nationalInsuranceNumber,
    PassportDetailsData? passportDetails,
    DriverLicenceDetailsData? driverLicenceDetails,
  }) {
    return ApplicantIdentityDetailsData(
      identityVerified: identityVerified ?? this.identityVerified,
      evidenceCheckedBy: evidenceCheckedBy ?? this.evidenceCheckedBy,
      nationalInsuranceNumber: nationalInsuranceNumber ?? this.nationalInsuranceNumber,
      passportDetails: passportDetails ?? this.passportDetails,
      driverLicenceDetails: driverLicenceDetails ?? this.driverLicenceDetails,
    );
  }

  factory ApplicantIdentityDetailsData.empty() {
    return ApplicantIdentityDetailsData(
      identityVerified: 'y',
      evidenceCheckedBy: '',
      nationalInsuranceNumber: '',
      passportDetails: null,
      driverLicenceDetails: null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'IdentityVerified': identityVerified,
      'EvidenceCheckedBy': evidenceCheckedBy,
      'NationalInsuranceNumber': nationalInsuranceNumber,
      'PassportDetails': passportDetails?.toJson(),
      'DriverLicenceDetails': driverLicenceDetails?.toJson(),
    };
  }

  factory ApplicantIdentityDetailsData.fromJson(Map<String, dynamic> json) {
    return ApplicantIdentityDetailsData(
      identityVerified: json['IdentityVerified'] ?? 'y',
      evidenceCheckedBy: json['EvidenceCheckedBy'] ?? '',
      nationalInsuranceNumber: json['NationalInsuranceNumber'] ?? '',
      passportDetails: json['PassportDetails'] != null
          ? PassportDetailsData.fromJson(json['PassportDetails'])
          : null,
      driverLicenceDetails: json['DriverLicenceDetails'] != null
          ? DriverLicenceDetailsData.fromJson(json['DriverLicenceDetails'])
          : null,
    );
  }
}

/// PassportDetails Structure (optional)
class PassportDetailsData {
  final String passportNumber;
  final String passportDob; // YYYY-MM-DD format (must match DateOfBirth)
  final String passportNationality;
  final String passportIssueDate; // YYYY-MM-DD format

  PassportDetailsData({
    required this.passportNumber,
    required this.passportDob,
    required this.passportNationality,
    required this.passportIssueDate,
  });

  PassportDetailsData copyWith({
    String? passportNumber,
    String? passportDob,
    String? passportNationality,
    String? passportIssueDate,
  }) {
    return PassportDetailsData(
      passportNumber: passportNumber ?? this.passportNumber,
      passportDob: passportDob ?? this.passportDob,
      passportNationality: passportNationality ?? this.passportNationality,
      passportIssueDate: passportIssueDate ?? this.passportIssueDate,
    );
  }

  factory PassportDetailsData.empty() {
    return PassportDetailsData(
      passportNumber: '',
      passportDob: '',
      passportNationality: '',
      passportIssueDate: '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'PassportNumber': passportNumber,
      'PassportDob': passportDob,
      'PassportNationality': passportNationality,
      'PassportIssueDate': passportIssueDate,
    };
  }

  factory PassportDetailsData.fromJson(Map<String, dynamic> json) {
    return PassportDetailsData(
      passportNumber: json['PassportNumber'] ?? '',
      passportDob: json['PassportDob'] ?? '',
      passportNationality: json['PassportNationality'] ?? '',
      passportIssueDate: json['PassportIssueDate'] ?? '',
    );
  }
}

/// DriverLicenceDetails Structure (optional)
class DriverLicenceDetailsData {
  final String driverLicenceNumber;
  final String driverLicenceDOB; // YYYY-MM-DD format (must match DateOfBirth)
  final String driverLicenceType; // 'paper' or 'photo'
  final String driverLicenceValidFrom; // YYYY-MM-DD format
  final String driverLicenceIssueCountry; // 2-letter ISO Country Code

  DriverLicenceDetailsData({
    required this.driverLicenceNumber,
    required this.driverLicenceDOB,
    required this.driverLicenceType,
    required this.driverLicenceValidFrom,
    required this.driverLicenceIssueCountry,
  });

  DriverLicenceDetailsData copyWith({
    String? driverLicenceNumber,
    String? driverLicenceDOB,
    String? driverLicenceType,
    String? driverLicenceValidFrom,
    String? driverLicenceIssueCountry,
  }) {
    return DriverLicenceDetailsData(
      driverLicenceNumber: driverLicenceNumber ?? this.driverLicenceNumber,
      driverLicenceDOB: driverLicenceDOB ?? this.driverLicenceDOB,
      driverLicenceType: driverLicenceType ?? this.driverLicenceType,
      driverLicenceValidFrom: driverLicenceValidFrom ?? this.driverLicenceValidFrom,
      driverLicenceIssueCountry: driverLicenceIssueCountry ?? this.driverLicenceIssueCountry,
    );
  }

  factory DriverLicenceDetailsData.empty() {
    return DriverLicenceDetailsData(
      driverLicenceNumber: '',
      driverLicenceDOB: '',
      driverLicenceType: 'photo',
      driverLicenceValidFrom: '',
      driverLicenceIssueCountry: 'GB',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'DriverLicenceNumber': driverLicenceNumber,
      'DriverLicenceDOB': driverLicenceDOB,
      'DriverLicenceType': driverLicenceType,
      'DriverLicenceValidFrom': driverLicenceValidFrom,
      'DriverLicenceIssueCountry': driverLicenceIssueCountry,
    };
  }

  factory DriverLicenceDetailsData.fromJson(Map<String, dynamic> json) {
    return DriverLicenceDetailsData(
      driverLicenceNumber: json['DriverLicenceNumber'] ?? '',
      driverLicenceDOB: json['DriverLicenceDOB'] ?? '',
      driverLicenceType: json['DriverLicenceType'] ?? 'photo',
      driverLicenceValidFrom: json['DriverLicenceValidFrom'] ?? '',
      driverLicenceIssueCountry: json['DriverLicenceIssueCountry'] ?? 'GB',
    );
  }
}
