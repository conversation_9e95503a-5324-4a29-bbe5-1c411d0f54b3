import 'package:flutter/services.dart';

class FormValidationUtils {
  static String? validateRequiredField(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  static String? validateYear(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    final year = int.tryParse(value.trim());
    if (year == null || year < 1900 || year > DateTime.now().year) {
      return 'Please enter a valid year';
    }
    return null;
  }

  static String? validateOptionalYear(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return null;
    }
    final year = int.tryParse(value.trim());
    if (year == null || year < 1900 || year > DateTime.now().year) {
      return 'Please enter a valid year';
    }
    return null;
  }

  static List<TextInputFormatter> getNameFormatters() {
    return [
      FilteringTextInputFormatter.allow(RegExp(r"[A-Za-z &'-]")),
      LengthLimitingTextInputFormatter(60),
      UpperCaseTextFormatter(),
    ];
  }

  static List<TextInputFormatter> getAddressFormatters() {
    return [
      FilteringTextInputFormatter.allow(RegExp(r"[A-Za-z0-9()/\-'& ]")),
      LengthLimitingTextInputFormatter(60),
      UpperCaseTextFormatter(),
    ];
  }

  static List<TextInputFormatter> getYearFormatters() {
    return [
      FilteringTextInputFormatter.digitsOnly,
      LengthLimitingTextInputFormatter(4),
    ];
  }

  static List<TextInputFormatter> getContactNumberFormatters() {
    return [
      FilteringTextInputFormatter.allow(RegExp(r"[A-Za-z0-9()/\-& ]")),
      LengthLimitingTextInputFormatter(30),
      UpperCaseTextFormatter(),
    ];
  }

  static String formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  static DateTime? parseDate(String value) {
    if (value.isEmpty) return null;
    final parts = value.split('/');
    if (parts.length != 3) return null;
    try {
      final day = int.parse(parts[0]);
      final month = int.parse(parts[1]);
      final year = int.parse(parts[2]);
      if (year < 1900 || year > DateTime.now().year) return null;
      if (month < 1 || month > 12) return null;
      final daysInMonth = getDaysInMonth(year, month);
      if (day < 1 || day > daysInMonth) return null;
      final date = DateTime(year, month, day);
      if (date.isAfter(DateTime.now())) return null;
      return date;
    } catch (e) {
      return null;
    }
  }

  static int getDaysInMonth(int year, int month) {
    switch (month) {
      case 1: case 3: case 5: case 7: case 8: case 10: case 12:
        return 31;
      case 4: case 6: case 9: case 11:
        return 30;
      case 2:
        return ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) ? 29 : 28;
      default:
        return 31;
    }
  }
}

class UpperCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    return TextEditingValue(
      text: newValue.text.toUpperCase(),
      selection: newValue.selection,
    );
  }
}
