import 'package:SolidCheck/core/config/component_config.dart';
import 'package:SolidCheck/core/navigation/app_router_new.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/applicants/presentation/viewmodels/applicant_dashboard_viewmodel.dart';
import 'package:SolidCheck/features/auth/di/auth_providers.dart';
import 'package:SolidCheck/features/dbs/presentation/viewmodels/dbs_form_viewmodel.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/form_steps/address_details_step.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/form_steps/birth_details_form_builder_step.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/form_steps/consent_step.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/form_steps/other_names_step.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/form_steps/personal_details_form_builder_step.dart';

import 'package:SolidCheck/features/dbs/presentation/widgets/form_steps/review_step.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/form_steps/supporting_documents_form_builder_step.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_help_sidebar.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/load_saved_data_dialog.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/number_stepper.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/validation_error_dialog.dart';
import 'package:SolidCheck/features/dbs/providers/dbs_save_state_provider.dart';
import 'package:SolidCheck/features/dbs/providers/form_reset_provider.dart';
import 'package:SolidCheck/features/dbs/providers/form_validation_provider.dart';
import 'package:SolidCheck/shared/utils/responsive.dart';
import 'package:SolidCheck/shared/widgets/navigation/applicant_sidebar.dart';
import 'package:SolidCheck/shared/widgets/navigation/universal_mobile_header.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final currentStepProvider = StateProvider<int>((ref) => 1);

class DBSFormScreen extends ConsumerStatefulWidget {
  const DBSFormScreen({super.key});

  @override
  DBSFormScreenState createState() => DBSFormScreenState();
}

class DBSFormScreenState extends ConsumerState<DBSFormScreen> {
  late PageController _pageController;
  bool isSubmitted = false;
  String? applicantId;
  String? productCode;
  String? productName;
  int _formRebuildKey = 0;

  final GlobalKey<PersonalDetailsFormBuilderStepState> _personalDetailsKey =
      GlobalKey<PersonalDetailsFormBuilderStepState>();
  final GlobalKey<BirthDetailsFormBuilderStepState> _birthDetailsKey =
      GlobalKey<BirthDetailsFormBuilderStepState>();
  final GlobalKey<AddressDetailsStepState> _addressDetailsKey =
      GlobalKey<AddressDetailsStepState>();
  final GlobalKey<OtherNamesStepState> _otherNamesKey =
      GlobalKey<OtherNamesStepState>();
  final GlobalKey<SupportingDocumentsFormBuilderStepState> _supportingDocsKey =
      GlobalKey<SupportingDocumentsFormBuilderStepState>();

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: 0);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _extractRouteArguments();
      _updateSidebarState();
      _initializeFormWithPersistence();
    });
  }

  void _extractRouteArguments() {
    final args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    if (args != null) {
      applicantId = args['applicantId'] as String?;
      productCode = args['productCode'] as String?;
      productName = args['productName'] as String?;
    }
  }

  void _updateSidebarState() {
    final dashboardState = ref.read(applicantDashboardViewModelProvider);

    if (dashboardState.applicantDetails?.data.applications != null) {
      final applications = dashboardState.applicantDetails!.data.applications;

      for (int i = 0; i < applications.length; i++) {
        final app = applications[i];
        if (app.product.variant.toUpperCase() == 'DBS' &&
            app.product.code == productCode) {
          ref.read(sidebarSelectedIndexProvider.notifier).state = i + 1;
          break;
        }
      }
    }
  }

  Future<String> _getOrCreateApplicationId() async {
    final dashboardState = ref.read(applicantDashboardViewModelProvider);

    if (dashboardState.applicantDetails?.data.applications != null) {
      final applications = dashboardState.applicantDetails!.data.applications;

      for (final app in applications) {
        if (app.product.variant.toUpperCase() == 'DBS' &&
            app.product.code == productCode) {
          return app.id.toString();
        }
      }
    }

    throw Exception('Application not found for product code: $productCode');
  }

  Future<void> _initializeFormWithPersistence() async {
    if (applicantId == null) {
      ref.read(currentStepProvider.notifier).state = 1;
      return;
    }

    try {
      final applicationId = await _getOrCreateApplicationId();

      final formNotifier = ref.read(dbsFormViewModelProvider.notifier);
      formNotifier.initializeForm(applicantId!, applicationId);

      final saveStateNotifier = ref.read(saveStateProvider.notifier);
      await saveStateNotifier.checkForSavedData(applicantId!, applicationId);

      final hasSaved = await formNotifier.hasSavedData();

      if (hasSaved && mounted) {
        final metadata = await formNotifier.getFormMetadata();

        final shouldLoad = await LoadSavedDataDialog.show(
          context: context,
          lastSaveTime: metadata?.saveTimestamp,
          completionPercentage: metadata?.completionStatus,
        );

        if (shouldLoad == true) {
          saveStateNotifier.setLoading();
          final success = await formNotifier.loadSavedFormData();

          if (success && mounted) {
            final formState = ref.read(dbsFormViewModelProvider);
            final currentStep = formState.currentStep;

            ref.read(currentStepProvider.notifier).state = currentStep;
            _pageController.jumpToPage(currentStep - 1);

            saveStateNotifier.setLoaded(
              lastSaveTime: formState.lastSaveTime,
              completionPercentage: metadata?.completionStatus,
            );

            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Row(
                  children: [
                    Icon(Icons.restore, color: Colors.white),
                    SizedBox(width: 8),
                    Text('Previous progress restored successfully'),
                  ],
                ),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 3),
              ),
            );
          } else {
            saveStateNotifier.setError('Failed to load saved data');
          }
        } else {
          await formNotifier.clearSavedData();
          formNotifier.resetForm();
          ref.read(currentStepProvider.notifier).state = 1;
          _pageController.jumpToPage(0);
          ref.read(formValidationModeProvider.notifier).disableValidation();
          _resetAllSteps();

          setState(() {
            _formRebuildKey++;
          });

          saveStateNotifier.clearState();

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Row(
                  children: [
                    Icon(Icons.refresh, color: Colors.white),
                    SizedBox(width: 8),
                    Text('Starting fresh - all data cleared'),
                  ],
                ),
                backgroundColor: Colors.blue,
                duration: Duration(seconds: 2),
              ),
            );
          }
        }
      } else {
        ref.read(currentStepProvider.notifier).state = 1;
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text('Error: ${e.toString()}')),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
      ref.read(currentStepProvider.notifier).state = 1;
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<bool?> _showLoadDataDialog() async {
    final formNotifier = ref.read(dbsFormViewModelProvider.notifier);
    final lastSaveTime = await formNotifier.getLastSavedTime();

    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.restore, color: Colors.blue, size: 24),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Saved Progress Found',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (lastSaveTime != null)
                      Text(
                        'Last saved: ${_formatSaveTime(lastSaveTime)}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.normal,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.info_outline,
                      color: Colors.blue,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'We found your previously saved progress for this DBS application.',
                        style: TextStyle(fontSize: 14, color: Colors.blue[800]),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              const Text(
                'What would you like to do?',
                style: TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
              ),
              const SizedBox(height: 12),
              _buildOptionTile(
                icon: Icons.restore,
                title: 'Continue where I left off',
                subtitle:
                    'Resume your application with all previously entered data',
                color: Colors.green,
              ),
              const SizedBox(height: 8),
              _buildOptionTile(
                icon: Icons.refresh,
                title: 'Start fresh',
                subtitle: 'Begin a new application and clear all saved data',
                color: Colors.orange,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
              ),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.refresh, size: 18, color: Colors.orange),
                  SizedBox(width: 6),
                  Text(
                    'Start Fresh',
                    style: TextStyle(
                      color: Colors.orange,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.restore, size: 18),
                  SizedBox(width: 6),
                  Text(
                    'Continue Progress',
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildOptionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: color.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: color.withValues(alpha: 0.05),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                    color: color,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatSaveTime(DateTime saveTime) {
    final now = DateTime.now();
    final difference = now.difference(saveTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else {
      return '${saveTime.day}/${saveTime.month}/${saveTime.year}';
    }
  }

  Future<void> showLoadDataDialog() async {
    final formNotifier = ref.read(dbsFormViewModelProvider.notifier);
    final hasSaved = await formNotifier.hasSavedData();

    if (!hasSaved) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Row(
            children: [
              Icon(Icons.info, color: Colors.white),
              SizedBox(width: 8),
              Text('No saved data found for this application'),
            ],
          ),
          backgroundColor: Colors.blue,
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    final metadata = await formNotifier.getFormMetadata();
    final saveStateNotifier = ref.read(saveStateProvider.notifier);

    final shouldLoad = await LoadSavedDataDialog.show(
      context: context,
      lastSaveTime: metadata?.saveTimestamp,
      completionPercentage: metadata?.completionStatus,
    );

    if (shouldLoad == true) {
      saveStateNotifier.setLoading();
      final success = await formNotifier.loadSavedFormData();

      if (success && mounted) {
        final formState = ref.read(dbsFormViewModelProvider);
        final currentStep = formState.currentStep;

        ref.read(currentStepProvider.notifier).state = currentStep;
        _pageController.jumpToPage(currentStep - 1);

        saveStateNotifier.setLoaded(
          lastSaveTime: formState.lastSaveTime,
          completionPercentage: metadata?.completionStatus,
        );

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.restore, color: Colors.white),
                SizedBox(width: 8),
                Text('Previous progress restored successfully'),
              ],
            ),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      } else {
        saveStateNotifier.setError('Failed to load saved data');
      }
    } else if (shouldLoad == false) {
      await formNotifier.clearSavedData();
      formNotifier.resetForm();
      ref.read(currentStepProvider.notifier).state = 1;
      _pageController.jumpToPage(0);
      ref.read(formValidationModeProvider.notifier).disableValidation();
      _resetAllSteps();

      setState(() {
        _formRebuildKey++;
      });

      saveStateNotifier.clearState();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.refresh, color: Colors.white),
                SizedBox(width: 8),
                Text('Starting fresh - all data cleared'),
              ],
            ),
            backgroundColor: Colors.blue,
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  void _resetAllSteps() {
    ref.read(formResetTriggerProvider.notifier).triggerReset();
  }

  Future<List<String>> _validateCurrentStep(int currentStep) async {
    final errors = <String>[];

    switch (currentStep) {
      case 1:
        await Future.delayed(const Duration(milliseconds: 200));

        final personalDetailsState = _personalDetailsKey.currentState;

        if (personalDetailsState != null) {
          ref
              .read(formValidationModeProvider.notifier)
              .enableValidationForNextButton();

          await Future.delayed(const Duration(milliseconds: 100));

          final isValid = personalDetailsState.validateForm();

          if (!isValid) {
            final fieldErrors = personalDetailsState.getValidationErrors();
            fieldErrors.forEach((field, error) {
              errors.add(error);
            });
          } else {}
        } else {
          errors.add(
            'Please fix all validation issues before continuing into next step.',
          );
        }
        break;

      case 2:
        final birthDetailsState = _birthDetailsKey.currentState;
        if (birthDetailsState != null) {
          ref
              .read(formValidationModeProvider.notifier)
              .enableValidationForNextButton();

          await Future.delayed(const Duration(milliseconds: 100));

          final isValid = birthDetailsState.validateForm();

          if (!isValid) {
            final fieldErrors = birthDetailsState.getValidationErrors();
            fieldErrors.forEach((field, error) {
              errors.add(error);
            });
          }
        }
        break;

      case 5:
        final supportingDocsState = _supportingDocsKey.currentState;
        if (supportingDocsState != null) {
          ref
              .read(formValidationModeProvider.notifier)
              .enableValidationForNextButton();
          await Future.delayed(const Duration(milliseconds: 50));

          if (!supportingDocsState.validateForm()) {
            final fieldErrors = supportingDocsState.getValidationErrors();
            fieldErrors.forEach((field, error) {
              errors.add(error);
            });
          }
        }
        break;

      case 3:
        // Address Details Step - Enhanced validation
        final formNotifier = ref.read(dbsFormViewModelProvider.notifier);
        final addressValidation = formNotifier.getStepValidation(currentStep - 1);

        if (!addressValidation['isValid']) {
          // Add field-specific errors
          final fieldErrors = addressValidation['fieldErrors'] as Map<String, String>? ?? {};
          fieldErrors.forEach((field, error) {
            errors.add(error);
          });

          // Add general errors (like missing postcode messages)
          final generalErrors = addressValidation['generalErrors'] as List<String>? ?? [];
          errors.addAll(generalErrors);

          // If no specific errors, add generic message
          if (fieldErrors.isEmpty && generalErrors.isEmpty) {
            errors.add('Please complete all required fields in this step');
          }
        }
        break;

      case 4:
        // Other Names Step
        final formNotifier = ref.read(dbsFormViewModelProvider.notifier);
        final otherNamesValidation = formNotifier.getStepValidation(currentStep - 1);

        if (!otherNamesValidation['isValid']) {
          final fieldErrors = otherNamesValidation['fieldErrors'] as Map<String, String>? ?? {};
          fieldErrors.forEach((field, error) {
            errors.add(error);
          });

          final generalErrors = otherNamesValidation['generalErrors'] as List<String>? ?? [];
          errors.addAll(generalErrors);

          if (fieldErrors.isEmpty && generalErrors.isEmpty) {
            errors.add('Please complete all required fields in this step');
          }
        }
        break;

      case 5:
        // Supporting Documents Step - Enhanced validation for NI Number
        final formNotifier = ref.read(dbsFormViewModelProvider.notifier);
        final documentsValidation = formNotifier.getStepValidation(currentStep - 1);

        if (!documentsValidation['isValid']) {
          final fieldErrors = documentsValidation['fieldErrors'] as Map<String, String>? ?? {};
          fieldErrors.forEach((field, error) {
            errors.add(error);
          });

          final generalErrors = documentsValidation['generalErrors'] as List<String>? ?? [];
          errors.addAll(generalErrors);

          if (fieldErrors.isEmpty && generalErrors.isEmpty) {
            errors.add('Please complete all required fields in this step');
          }
        }
        break;

      case 6:
      case 7:
        break;
    }

    return errors;
  }

  String _getStepName(int step) {
    switch (step) {
      case 1:
        return 'Personal Details';
      case 2:
        return 'Birth Details';
      case 3:
        return 'Address Details';
      case 4:
        return 'Other Names';
      case 5:
        return 'Supporting Documents';
      case 6:
        return 'Review';
      case 7:
        return 'Consent';
      default:
        return 'Current';
    }
  }

  bool _isCurrentStepValid(int step) {
    final formNotifier = ref.read(dbsFormViewModelProvider.notifier);
    return formNotifier.validateStep(step - 1);
  }

  void _goToStep(int step) {
    ref.read(formValidationModeProvider.notifier).disableValidation();

    ref.read(currentStepProvider.notifier).state = step;
    _pageController.jumpToPage(step - 1);
  }

  void _previousStep() {
    final currentStep = ref.read(currentStepProvider);
    if (currentStep > 1) {
      ref.read(formValidationModeProvider.notifier).disableValidation();

      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<void> _nextStep() async {
    final currentStep = ref.read(currentStepProvider);
    final validationErrors = await _validateCurrentStep(currentStep);

    if (validationErrors.isNotEmpty) {
      await ValidationErrorDialog.show(
        context: context,
        errors: validationErrors,
        stepName: _getStepName(currentStep),
      );
      return;
    }

    ref.read(formValidationModeProvider.notifier).disableValidation();

    if (currentStep < 7) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<void> _saveCurrentStep() async {
    final formNotifier = ref.read(dbsFormViewModelProvider.notifier);
    final saveStateNotifier = ref.read(saveStateProvider.notifier);

    saveStateNotifier.setSaving();
    final success = await formNotifier.saveFormDataSecurely(isAutoSave: false);

    if (success && mounted) {
      final formState = ref.read(dbsFormViewModelProvider);
      saveStateNotifier.setSaved(saveTime: formState.lastSaveTime);

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.white),
              SizedBox(width: 8),
              Text('Progress saved securely'),
            ],
          ),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );
    } else if (!success && mounted) {
      final formState = ref.read(dbsFormViewModelProvider);
      saveStateNotifier.setError(formState.error ?? 'Failed to save progress');

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error, color: Colors.white),
              const SizedBox(width: 8),
              Text(formState.error ?? 'Failed to save progress'),
            ],
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  void _showValidationError(DBSFormViewModel formNotifier) {
    ref
        .read(formValidationModeProvider.notifier)
        .enableValidationForNextButton();
  }

  List<Widget> _buildStepScreens() {
    return [
      PersonalDetailsFormBuilderStep(key: _personalDetailsKey),
      BirthDetailsFormBuilderStep(key: _birthDetailsKey),
      AddressDetailsStep(key: _addressDetailsKey),
      OtherNamesStep(key: _otherNamesKey),
      SupportingDocumentsFormBuilderStep(key: _supportingDocsKey),
      ReviewStep(
        key: ValueKey('review_$_formRebuildKey'),
        onEditStep: () => _goToStep(1),
      ),
      ConsentStep(
        key: ValueKey('consent_$_formRebuildKey'),
        onSubmit: _handleFormSubmission,
      ),
    ];
  }

  void _handleFormSubmission() {
    // Navigate to success screen with all necessary data
    final formState = ref.read(dbsFormViewModelProvider);
    final dashboardState = ref.read(applicantDashboardViewModelProvider);

    // Get applicant name from dashboard state
    String? applicantName;
    if (dashboardState.applicantDetails != null) {
      final applicant = dashboardState.applicantDetails!.data.applicant;
      applicantName = '${applicant.firstName} ${applicant.lastName}';
    } else if (dashboardState.applicant != null) {
      final applicant = dashboardState.applicant!;
      applicantName = '${applicant.firstName} ${applicant.lastName}';
    }

    Navigator.of(context).pushReplacementNamed(
      '/dbs-success',
      arguments: {
        'applicantId': applicantId,
        'applicationId': formState.applicationId,
        'applicantName': applicantName,
        'productName': productName,
      },
    );
  }

  void _handleBackToDashboard() {
    ref.read(sidebarSelectedIndexProvider.notifier).state = 0;

    final authState = ref.read(authViewModelProvider);
    final userType = authState.authResult?.user?.userType;

    if (userType != 'applicant') {
      AppRouter.navigateToDashboard();
    } else {
      if (applicantId != null) {
        Navigator.of(context).pushNamedAndRemoveUntil(
          '/applicant-detail',
          (route) => route.settings.name == '/dashboard',
          arguments: {'applicantId': applicantId},
        );
      } else {
        // For applicant users without explicit applicant ID, use their user ID
        final userId = authState.authResult?.user?.id?.toString();
        if (userId != null) {
          Navigator.of(context).pushNamedAndRemoveUntil(
            '/applicant-detail',
            (route) => route.settings.name == '/dashboard',
            arguments: {'applicantId': userId},
          );
        } else {
          // Final fallback to generic applicant dashboard
          AppRouter.navigateToApplicantDashboard();
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final Size size = MediaQuery.of(context).size;
    final bool isMobile = ResponsiveUtil.isMobile(context);
    final bool isMobileLarge = ResponsiveUtil.isMobileLarge(context);
    final bool responsiveBreakPoint = isMobile || isMobileLarge;
    final double paddingValue = responsiveBreakPoint ? 10.0 : 40.0;
    final double lineWidth = responsiveBreakPoint ? 2.5 : 3.5;
    final double verticalPadding = responsiveBreakPoint ? 15.0 : 16.0;

    final currentStep = ref.watch(currentStepProvider);
    const totalSteps = 7;



    return Scaffold(
      backgroundColor: AppColors.applicationOverviewDivColor,
      drawer: isMobile
          ? ApplicantSidebar(
              applicantId: applicantId,
              showBackButton: true,
              onBackPressed: _handleBackToDashboard,
            )
          : null,
      body: _buildLayout(
        context,
        isMobile,
        size,
        responsiveBreakPoint,
        paddingValue,
        lineWidth,
        verticalPadding,
        currentStep,
        totalSteps,
      ),
    );
  }

  Widget _buildLayout(
    BuildContext context,
    bool isMobile,
    Size size,
    bool responsiveBreakPoint,
    double paddingValue,
    double lineWidth,
    double verticalPadding,
    int currentStep,
    int totalSteps,
  ) {
    if (isMobile) {
      return Column(
        children: [
          // Universal mobile header
          UniversalMobileHeader(
            showBackButton: true,
            onBackPressed: () => Navigator.of(context).pop(),
          ),
          // Content area
          Expanded(
            child: _buildContent(
              context,
              size,
              responsiveBreakPoint,
              paddingValue,
              lineWidth,
              verticalPadding,
              currentStep,
              totalSteps,
            ),
          ),
        ],
      );
    }

    return Row(
      children: [
        ApplicantSidebar(
          applicantId: applicantId,
          showBackButton: true,
          onBackPressed: _handleBackToDashboard,
        ),
        Expanded(
          child: _buildContent(
            context,
            size,
            responsiveBreakPoint,
            paddingValue,
            lineWidth,
            verticalPadding,
            currentStep,
            totalSteps,
          ),
        ),
        DBSHelpSidebar(currentStep: currentStep, isMobile: isMobile),
      ],
    );
  }

  Widget _buildContent(
    BuildContext context,
    Size size,
    bool responsiveBreakPoint,
    double paddingValue,
    double lineWidth,
    double verticalPadding,
    int currentStep,
    int totalSteps,
  ) {
    final isMobile = ResponsiveUtil.isMobile(context);
    final isTablet = ResponsiveUtil.isTablet(context);
    final isDesktop = ResponsiveUtil.isDesktop(context);

    final stepperHeight = _getStepperHeight(isMobile, isTablet);
    final buttonHeight = _getButtonHeight(isMobile, isTablet);
    final contentPadding = _getContentPadding(isMobile, isTablet, isDesktop);
    final formMargin = _getFormMargin(isMobile, isTablet);

    return Container(
      color: AppColors.applicationOverviewDivColor,
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: paddingValue,
            vertical: contentPadding,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: stepperHeight + 40,
                child: Column(
                  children: [
                    _buildStepProgressIndicator(
                      currentStep,
                      totalSteps,
                      isMobile,
                    ),
                    const SizedBox(height: 8),
                    Expanded(
                      child: NumberStepper(
                        width: size.width,
                        totalSteps: totalSteps,
                        curStep: currentStep,
                        stepCompleteColor: AppColors.sideBarMenuColor,
                        currentStepColor: AppColors.kCheckBoxLoginColor,
                        inactiveColor: AppColors.kInactiveStep,
                        lineWidth: lineWidth,
                      ),
                    ),
                  ],
                ),
              ),

              SizedBox(height: isMobile ? 12 : 8),

              Expanded(
                child: Container(
                  margin: EdgeInsets.symmetric(vertical: formMargin),
                  decoration: BoxDecoration(
                    color: AppColors.kWhiteColor,
                    borderRadius: BorderRadius.circular(isMobile ? 8 : 12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withValues(alpha: 0.1),
                        spreadRadius: 1,
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(isMobile ? 8 : 12),
                    child: PageView(
                      controller: _pageController,
                      onPageChanged: (index) {
                        ref.read(currentStepProvider.notifier).state =
                            index + 1;
                        final formNotifier = ref.read(
                          dbsFormViewModelProvider.notifier,
                        );
                        formNotifier.setCurrentStep(index);
                        ref
                            .read(formValidationModeProvider.notifier)
                            .disableValidation();
                        Future.delayed(const Duration(milliseconds: 100), () {
                          ref
                              .read(formValidationModeProvider.notifier)
                              .disableValidation();
                        });
                      },
                      children: _buildOptimizedStepScreens(
                        context,
                        size,
                        isMobile,
                        isTablet,
                        isDesktop,
                      ),
                    ),
                  ),
                ),
              ),

              SizedBox(
                height: buttonHeight,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    vertical: isMobile ? 8 : 6,
                    horizontal: isMobile ? 0 : 8,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      if (currentStep > 1)
                        _buildCompactButton(
                          title: 'Previous',
                          onPressed: () => _previousStep(),
                          isPrimary: false,
                          isMobile: isMobile,
                        )
                      else
                        const SizedBox.shrink(),

                      Row(
                        children: [
                          if (currentStep < totalSteps) ...[
                            _buildCompactButton(
                              title: 'Load',
                              onPressed: () => showLoadDataDialog(),
                              isPrimary: false,
                              isMobile: isMobile,
                              isSecondary: true,
                              icon: Icons.restore,
                            ),
                            SizedBox(width: isMobile ? 6 : 8),
                            _buildCompactButton(
                              title: 'Save',
                              onPressed: () => _saveCurrentStep(),
                              isPrimary: false,
                              isMobile: isMobile,
                              isSecondary: true,
                              icon: Icons.save,
                            ),
                            SizedBox(width: isMobile ? 8 : 10),
                            _buildCompactButton(
                              title: 'Next',
                              onPressed: () => _nextStep(),
                              isPrimary: true,
                              isMobile: isMobile,
                              icon: Icons.arrow_forward,
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStepProgressIndicator(
    int currentStep,
    int totalSteps,
    bool isMobile,
  ) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isMobile ? 16 : 24,
        vertical: isMobile ? 12 : 16,
      ),
      decoration: BoxDecoration(
        color: AppColors.kWhiteColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          _buildCurrentStepMarker(currentStep, isMobile),
          const SizedBox(width: 16),
          Expanded(child: _buildProgressBar(currentStep, totalSteps, isMobile)),

          const SizedBox(width: 16),
          _buildStepCounter(currentStep, totalSteps, isMobile),
        ],
      ),
    );
  }

  Widget _buildCurrentStepMarker(int currentStep, bool isMobile) {
    final stepInfo = _getStepInfo(currentStep);

    return Container(
      padding: EdgeInsets.all(isMobile ? 8 : 10),
      decoration: BoxDecoration(
        color: AppColors.kBlueColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.kBlueColor, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            stepInfo['icon'],
            color: AppColors.kBlueColor,
            size: isMobile ? 18 : 20,
          ),
          const SizedBox(width: 8),
          Text(
            stepInfo['title'],
            style: TextStyle(
              fontSize: isMobile ? 12 : 14,
              fontWeight: FontWeight.w600,
              color: AppColors.kBlueColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressBar(int currentStep, int totalSteps, bool isMobile) {
    final progress = currentStep / totalSteps;

    return Column(
      children: [
        Container(
          height: isMobile ? 6 : 8,
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: BorderRadius.circular(4),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: LinearProgressIndicator(
              value: progress,
              backgroundColor: Colors.transparent,
              valueColor: AlwaysStoppedAnimation<Color>(
                AppColors.kBlueColor,
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: List.generate(totalSteps, (index) {
            final stepNumber = index + 1;
            final isCompleted = stepNumber < currentStep;
            final isCurrent = stepNumber == currentStep;

            return Container(
              width: isMobile ? 8 : 10,
              height: isMobile ? 8 : 10,
              decoration: BoxDecoration(
                color: isCompleted || isCurrent
                    ? AppColors.kBlueColor
                    : Colors.grey.shade300,
                shape: BoxShape.circle,
              ),
            );
          }),
        ),
      ],
    );
  }

  Widget _buildStepCounter(int currentStep, int totalSteps, bool isMobile) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isMobile ? 10 : 12,
        vertical: isMobile ? 6 : 8,
      ),
      decoration: BoxDecoration(
        color: AppColors.kBlueColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        '$currentStep / $totalSteps',
        style: TextStyle(
          fontSize: isMobile ? 12 : 14,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
    );
  }

  Map<String, dynamic> _getStepInfo(int step) {
    switch (step) {
      case 1:
        return {
          'title': 'Personal Details',
          'icon': Icons.person,
          'color': AppColors.kBlueColor,
        };
      case 2:
        return {
          'title': 'Birth Details',
          'icon': Icons.location_on,
          'color': AppColors.kBlueColor,
        };
      case 3:
        return {
          'title': 'Address Details',
          'icon': Icons.home,
          'color': AppColors.kBlueColor,
        };
      case 4:
        return {
          'title': 'Other Names',
          'icon': Icons.badge,
          'color': AppColors.kBlueColor,
        };
      case 5:
        return {
          'title': 'Supporting Documents',
          'icon': Icons.description,
          'color': AppColors.kBlueColor,
        };
      case 6:
        return {
          'title': 'Review',
          'icon': Icons.preview,
          'color': AppColors.kBlueColor,
        };
      case 7:
        return {
          'title': 'Consent',
          'icon': Icons.check_circle,
          'color': AppColors.kBlueColor,
        };
      default:
        return {
          'title': 'Step $step',
          'icon': Icons.help,
          'color': AppColors.kBlueColor,
        };
    }
  }

  double _getStepperHeight(bool isMobile, bool isTablet) {
    if (isMobile) return 60;
    if (isTablet) return 70;
    return 80;
  }

  double _getButtonHeight(bool isMobile, bool isTablet) {
    if (isMobile) return 50;
    if (isTablet) return 55;
    return 60;
  }

  double _getContentPadding(bool isMobile, bool isTablet, bool isDesktop) {
    if (isMobile) return 6;
    if (isTablet) return 8;
    return 10;
  }

  double _getFormMargin(bool isMobile, bool isTablet) {
    if (isMobile) return 4;
    if (isTablet) return 6;
    return 8;
  }

  List<Widget> _buildOptimizedStepScreens(
    BuildContext context,
    Size size,
    bool isMobile,
    bool isTablet,
    bool isDesktop,
  ) {
    return [
      _buildOptimizedStep(
        PersonalDetailsFormBuilderStep(key: _personalDetailsKey),
        context,
        size,
        isMobile,
        isTablet,
        isDesktop,
      ),
      _buildOptimizedStep(
        BirthDetailsFormBuilderStep(key: _birthDetailsKey),
        context,
        size,
        isMobile,
        isTablet,
        isDesktop,
      ),
      _buildOptimizedStep(
        AddressDetailsStep(key: _addressDetailsKey),
        context,
        size,
        isMobile,
        isTablet,
        isDesktop,
      ),
      _buildOptimizedStep(
        OtherNamesStep(key: _otherNamesKey),
        context,
        size,
        isMobile,
        isTablet,
        isDesktop,
      ),
      _buildOptimizedStep(
        SupportingDocumentsFormBuilderStep(key: _supportingDocsKey),
        context,
        size,
        isMobile,
        isTablet,
        isDesktop,
      ),
      _buildOptimizedStep(
        ReviewStep(
          key: ValueKey('review_opt_$_formRebuildKey'),
          onEditStep: () => _goToStep(1),
        ),
        context,
        size,
        isMobile,
        isTablet,
        isDesktop,
      ),
      _buildOptimizedStep(
        ConsentStep(
          key: ValueKey('consent_opt_$_formRebuildKey'),
          onSubmit: _handleFormSubmission,
        ),
        context,
        size,
        isMobile,
        isTablet,
        isDesktop,
      ),
    ];
  }

  Widget _buildOptimizedStep(
    Widget step,
    BuildContext context,
    Size size,
    bool isMobile,
    bool isTablet,
    bool isDesktop,
  ) {
    return Container(
      constraints: BoxConstraints(
        maxWidth: _getMaxContentWidth(
          size.width,
          isMobile,
          isTablet,
          isDesktop,
        ),
      ),
      child: step,
    );
  }

  double _getMaxContentWidth(
    double screenWidth,
    bool isMobile,
    bool isTablet,
    bool isDesktop,
  ) {
    if (isMobile) return screenWidth;
    if (isTablet) return screenWidth * 0.9;
    return screenWidth * 0.8;
  }

  Widget _buildCompactButton({
    required String title,
    required VoidCallback onPressed,
    required bool isPrimary,
    required bool isMobile,
    bool isSecondary = false,
    IconData? icon,
  }) {
    Widget buttonChild = Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (icon != null) ...[Icon(icon, size: 16), const SizedBox(width: 6)],
        Text(
          title,
          style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w600),
        ),
      ],
    );

    return SizedBox(
      height: 36,
      child: isPrimary
          ? ElevatedButton(
              onPressed: onPressed,
              style: ComponentConfig.primaryButtonStyle.copyWith(
                padding: WidgetStateProperty.all(
                  EdgeInsets.symmetric(
                    horizontal: isMobile ? 12 : 16,
                    vertical: 8,
                  ),
                ),
              ),
              child: buttonChild,
            )
          : OutlinedButton(
              onPressed: onPressed,
              style: ComponentConfig.secondaryButtonStyle.copyWith(
                padding: WidgetStateProperty.all(
                  EdgeInsets.symmetric(
                    horizontal: isMobile ? 12 : 16,
                    vertical: 8,
                  ),
                ),
              ),
              child: buttonChild,
            ),
    );
  }
}
