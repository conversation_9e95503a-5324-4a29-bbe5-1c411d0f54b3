import 'package:SolidCheck/core/navigation/app_router_new.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/applicants/presentation/viewmodels/applicant_dashboard_viewmodel.dart';
import 'package:SolidCheck/features/auth/di/auth_providers.dart';
import 'package:SolidCheck/features/dbs/presentation/screens/dbs_form_success_screen.dart';
import 'package:SolidCheck/features/dbs/presentation/viewmodels/dbs_form_viewmodel.dart';
import 'package:SolidCheck/shared/utils/responsive.dart';
import 'package:SolidCheck/shared/widgets/buttons/solid_button.dart';
import 'package:SolidCheck/shared/widgets/navigation/applicant_sidebar.dart';
import 'package:SolidCheck/shared/widgets/navigation/universal_mobile_header.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// DBS Check Application Form Screen
/// Shows the initial screen before starting the DBS form
class DBSApplicationFormScreen extends ConsumerWidget {
  final String? applicantId;
  final String productCode;
  final String productName;

  const DBSApplicationFormScreen({
    super.key,
    this.applicantId,
    required this.productCode,
    required this.productName,
  });

  void _handleBackToDashboard(WidgetRef ref) {
    // Reset sidebar to Overview state
    ref.read(sidebarSelectedIndexProvider.notifier).state = 0;

    // Get the current user type to determine which dashboard to navigate to
    final authState = ref.read(authViewModelProvider);
    final userType = authState.authResult?.user?.userType;

    if (userType != 'applicant') {
      // Navigate directly to client dashboard for non-applicant users
      AppRouter.navigateToDashboard();
    } else {
      // For applicant users, navigate to their specific applicant detail page
      // using their user ID as the applicant ID
      final userId = authState.authResult?.user?.id?.toString();
      if (userId != null) {
        AppRouter.navigateToApplicantDetail(userId);
      } else {
        // Fallback to generic applicant dashboard
        AppRouter.navigateToApplicantDashboard();
      }
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isMobile = ResponsiveUtil.isMobile(context);

    return Scaffold(
      backgroundColor: AppColors.kWhiteColor,
      drawer: isMobile ? ApplicantSidebar(
        applicantId: applicantId,
        showBackButton: true,
        onBackPressed: () => _handleBackToDashboard(ref),
      ) : null,
      body: FutureBuilder<bool>(
        future: _checkFormCompletionStatus(ref),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return _buildLoadingLayout(context, isMobile);
          }

          if (snapshot.hasError) {
            return _buildErrorLayout(context, isMobile, snapshot.error.toString());
          }

          final isCompleted = snapshot.data ?? false;

          if (isCompleted) {
            return _buildCompletedLayout(context, isMobile, ref);
          } else {
            return _buildLayout(context, isMobile, ref);
          }
        },
      ),
    );
  }

  Widget _buildLayout(BuildContext context, bool isMobile, WidgetRef ref) {
    if (isMobile) {
      return Column(
        children: [
          // Universal mobile header
          UniversalMobileHeader(
            showBackButton: true,
            onBackPressed: () => _handleBackToDashboard(ref),
          ),
          // Content area
          Expanded(
            child: _buildContent(context, isMobile),
          ),
        ],
      );
    }

    // Desktop layout
    return Row(
      children: [
        // Sidebar
        ApplicantSidebar(
          applicantId: applicantId,
          showBackButton: true,
          onBackPressed: () => _handleBackToDashboard(ref),
        ),
        // Main content
        Expanded(
          child: _buildContent(context, isMobile),
        ),
      ],
    );
  }

  Widget _buildContent(BuildContext context, bool isMobile) {
    return Container(
      color: Colors.grey[50],
      child: Center(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(isMobile ? 24.0 : 48.0),
          child: Container(
            constraints: const BoxConstraints(maxWidth: 600),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Warning Icon
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: Colors.red[50],
                    borderRadius: BorderRadius.circular(60),
                    border: Border.all(
                      color: Colors.red[400]!,
                      width: 3,
                    ),
                  ),
                  child: Icon(
                    Icons.priority_high,
                    size: 60,
                    color: Colors.red[400],
                  ),
                ),
                const SizedBox(height: 40),

                // Title
                Text(
                  'DBS Check Application Form',
                  style: TextStyle(
                    fontSize: isMobile ? 24 : 28,
                    fontWeight: FontWeight.w700,
                    color: AppColors.kBlueColor,
                    letterSpacing: -0.5,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),

                // Description
                Container(
                  padding: EdgeInsets.all(isMobile ? 20 : 24),
                  decoration: BoxDecoration(
                    color: AppColors.kWhiteColor,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Text(
                        'It looks like you haven\'t filled out the DBS Check Application Form yet. Please complete this form to allow us to proceed with your background check. Your responses are crucial and will be kept confidential.',
                        style: TextStyle(
                          fontSize: isMobile ? 16 : 18,
                          color: Colors.grey[700],
                          height: 1.6,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 20),
                      Text(
                        'When you are ready, click the button below to begin filling out the form.',
                        style: TextStyle(
                          fontSize: isMobile ? 14 : 16,
                          color: Colors.grey[600],
                          height: 1.5,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 40),

                // Product Info Card
                Container(
                  padding: EdgeInsets.all(isMobile ? 16 : 20),
                  decoration: BoxDecoration(
                    color: AppColors.kBlueColor.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppColors.kBlueColor.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.kBlueColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.security,
                          color: AppColors.kBlueColor,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Check Type',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              productName,
                              style: TextStyle(
                                fontSize: 16,
                                color: AppColors.kBlueColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppColors.kBlueColor,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          productCode,
                          style: TextStyle(
                            fontSize: 12,
                            color: AppColors.kWhiteColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 40),

                // Start Button
                SolidButton(
                  text: 'Start',
                  type: SolidButtonType.primary,
                  size: SolidButtonSize.large,
                  onPressed: () => _handleStartForm(context),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _handleStartForm(BuildContext context) {
    // Navigate to appropriate form based on product code
    switch (productCode.toUpperCase()) {
      case 'DBSBC':
        // Navigate to DBS Basic form (to be implemented)
        _showComingSoonDialog(context, 'DBS Basic Form');
        break;
      case 'DBSEC':
      case 'DBSSC':
        // Navigate to DBS Enhanced/Standard form
        Navigator.pushNamed(
          context,
          '/dbs-form',
          arguments: {
            'applicantId': applicantId,
            'productCode': productCode,
            'productName': productName,
          },
        );
        break;
      default:
        // Unknown product code
        _showErrorDialog(context, 'Unknown DBS product code: $productCode');
    }
  }

  void _showComingSoonDialog(BuildContext context, String formType) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Coming Soon'),
        content: Text('$formType will be implemented in the next section.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Future<bool> _checkFormCompletionStatus(WidgetRef ref) async {
    if (applicantId == null) return false;

    try {
      final applicationId = await _getOrCreateApplicationId(ref);
      final formNotifier = ref.read(dbsFormViewModelProvider.notifier);

      formNotifier.initializeForm(applicantId!, applicationId);
      return await formNotifier.checkFormCompletionStatus();
    } catch (e) {
      return false;
    }
  }

  Future<String> _getOrCreateApplicationId(WidgetRef ref) async {
    final dashboardState = ref.read(applicantDashboardViewModelProvider);

    if (dashboardState.applicantDetails?.data.applications != null) {
      final applications = dashboardState.applicantDetails!.data.applications;

      for (final app in applications) {
        if (app.product.variant.toUpperCase() == 'DBS' &&
            app.product.code == productCode) {
          return app.id.toString();
        }
      }
    }

    throw Exception('Application not found for product code: $productCode');
  }

  Widget _buildLoadingLayout(BuildContext context, bool isMobile) {
    return Container(
      color: Colors.grey[50],
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildErrorLayout(BuildContext context, bool isMobile, String error) {
    return Container(
      color: Colors.grey[50],
      child: Center(
        child: Padding(
          padding: EdgeInsets.all(isMobile ? 24.0 : 48.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red[400],
              ),
              const SizedBox(height: 20),
              Text(
                'Error checking form status',
                style: TextStyle(
                  fontSize: isMobile ? 18 : 20,
                  fontWeight: FontWeight.w600,
                  color: Colors.red[700],
                ),
              ),
              const SizedBox(height: 10),
              Text(
                error,
                style: TextStyle(
                  fontSize: isMobile ? 14 : 16,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              SolidButton(
                text: 'Retry',
                onPressed: () {
                  // Trigger rebuild
                  (context as Element).markNeedsBuild();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCompletedLayout(BuildContext context, bool isMobile, WidgetRef ref) {
    final formState = ref.watch(dbsFormViewModelProvider);

    // Navigate to the dedicated success screen
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => DBSFormSuccessScreen(
            applicantId: applicantId!,
            applicationId: formState.applicationId ?? '',
            productCode: productCode,
            productName: productName,
            completionData: formState.formCompletionStatus,
          ),
        ),
      );
    });

    // Show loading while navigating
    return Container(
      color: Colors.grey[50],
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

}
