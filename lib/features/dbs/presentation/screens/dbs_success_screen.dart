import 'package:SolidCheck/core/navigation/app_router_new.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/features/dashboard/presentation/widgets/appbar/dashboard_appbar_widget.dart';
import 'package:SolidCheck/shared/widgets/buttons/solid_button.dart';
import 'package:SolidCheck/shared/widgets/navigation/applicant_sidebar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class DBSSuccessScreen extends ConsumerStatefulWidget {
  final String? applicantId;
  final String? applicationId;
  final String? applicantName;
  final String? productName;

  const DBSSuccessScreen({
    super.key,
    this.applicantId,
    this.applicationId,
    this.applicantName,
    this.productName,
  });

  @override
  ConsumerState<DBSSuccessScreen> createState() => _DBSSuccessScreenState();
}

class _DBSSuccessScreenState extends ConsumerState<DBSSuccessScreen> {
  @override
  Widget build(BuildContext context) {
    final bool isMobile = ResponsiveHelper.isMobile(context);
    
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: isMobile ? DashboardAppBarWidget(
        isAdminActive: false,
        showDrawerIcon: true,
        centertitle: false,
      ) : null,
      drawer: isMobile ? ApplicantSidebar(
        applicantId: widget.applicantId,
        showBackButton: true,
        onBackPressed: _handleBackToDashboard,
      ) : null,
      body: _buildLayout(context, isMobile),
    );
  }

  Widget _buildLayout(BuildContext context, bool isMobile) {
    if (isMobile) {
      return _buildContent(context, isMobile);
    }

    return Row(
      children: [
        // Sidebar
        ApplicantSidebar(
          applicantId: widget.applicantId,
          showBackButton: true,
          onBackPressed: _handleBackToDashboard,
        ),
        // Main content
        Expanded(
          child: _buildContent(context, isMobile),
        ),
      ],
    );
  }

  Widget _buildContent(BuildContext context, bool isMobile) {
    final Size size = MediaQuery.of(context).size;
    final double horizontalPadding = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 20.0,
      tablet: 40.0,
      desktop: 60.0,
    );

    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.applicationOverviewDivColor,
      ),
      child: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
          horizontal: horizontalPadding,
          vertical: ResponsiveHelper.getResponsiveValue(
            context,
            mobile: 20.0,
            tablet: 30.0,
            desktop: 40.0,
          ),
        ),
        child: Center(
          child: Container(
            constraints: BoxConstraints(
              maxWidth: ResponsiveHelper.getResponsiveValue(
                context,
                mobile: size.width,
                tablet: 600.0,
                desktop: 700.0,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Success Icon
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: AppColors.kBlueColor.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.check_circle,
                    size: 80,
                    color: AppColors.kBlueColor,
                  ),
                ),
                
                const SizedBox(height: 32),
                
                // Success Title
                Text(
                  'Form Submitted Successfully!',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getResponsiveFontSize(
                      context,
                      mobile: 24.0,
                      tablet: 28.0,
                      desktop: 32.0,
                    ),
                    fontWeight: FontWeight.bold,
                    color: AppColors.kBlueColor,
                  ),
                ),
                
                const SizedBox(height: 16),
                
                Text(
                  'Your ${widget.productName ?? 'DBS'} application has been submitted successfully.',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getResponsiveFontSize(
                      context,
                      mobile: 16.0,
                      tablet: 17.0,
                      desktop: 18.0,
                    ),
                    color: Colors.grey.shade600,
                    height: 1.5,
                  ),
                ),
                
                const SizedBox(height: 48),
                
                _buildActionButtons(context, isMobile),
                
                const SizedBox(height: 32),
                
                _buildAdditionalInfo(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, bool isMobile) {
    return Column(
      children: [
        SolidButton(
          text: 'Nominate Documents',
          type: SolidButtonType.primary,
          size: SolidButtonSize.large,
          isFullWidth: true,
          onPressed: _handleNominateDocuments,
        ),
        
        const SizedBox(height: 16),
        
        SolidButton(
          text: 'Back to Dashboard',
          type: SolidButtonType.outline,
          size: SolidButtonSize.large,
          isFullWidth: true,
          onPressed: _handleBackToDashboard,
        ),
      ],
    );
  }

  Widget _buildAdditionalInfo(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.kBlueColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.kBlueColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppColors.kBlueColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Next Steps',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.kBlueColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '• Complete document nomination to proceed with your application\n'
            '• You will receive email updates on your application status\n'
            '• Contact support if you need assistance with the process',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade700,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  void _handleNominateDocuments() {

    if (widget.applicationId != null) {
      Navigator.of(context).pushNamed(
        '/document-nomination',
        arguments: {
          'applicationId': widget.applicationId!,
          'applicantName': widget.applicantName ?? 'Applicant',
          'applicantId': widget.applicantId, // Pass applicantId to document nomination
        },
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Unable to proceed to document nomination. Application ID not found.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _handleBackToDashboard() {
    if (widget.applicantId != null) {
      Navigator.of(context).pushNamedAndRemoveUntil(
        '/applicant-detail',
        (route) => route.settings.name == '/dashboard',
        arguments: {'applicantId': widget.applicantId},
      );
    } else {
      AppRouter.navigateToApplicantDashboard();
    }
  }
}
