import 'package:SolidCheck/core/navigation/app_router_new.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/features/applicants/presentation/viewmodels/applicant_dashboard_viewmodel.dart';
import 'package:SolidCheck/features/auth/di/auth_providers.dart';
import 'package:SolidCheck/shared/widgets/buttons/solid_button.dart';
import 'package:SolidCheck/shared/widgets/navigation/applicant_sidebar.dart';
import 'package:SolidCheck/shared/widgets/navigation/universal_mobile_header.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class DBSFormSuccessScreen extends ConsumerWidget {
  final String applicantId;
  final String applicationId;
  final String productCode;
  final String productName;
  final Map<String, dynamic>? completionData;

  const DBSFormSuccessScreen({
    super.key,
    required this.applicantId,
    required this.applicationId,
    required this.productCode,
    required this.productName,
    this.completionData,
  });

  void _handleBackToDashboard(WidgetRef ref) {
    ref.read(sidebarSelectedIndexProvider.notifier).state = 0;
    final authState = ref.read(authViewModelProvider);
    final userType = authState.authResult?.user?.userType;

    if (userType != 'applicant') {
      AppRouter.navigateToDashboard();
    } else {
      // For applicant users, navigate to their specific applicant detail page
      // using their user ID as the applicant ID
      final userId = authState.authResult?.user?.id?.toString();
      if (userId != null) {
        AppRouter.navigateToApplicantDetail(userId);
      } else {
        // Fallback to generic applicant dashboard
        AppRouter.navigateToApplicantDashboard();
      }
    }
  }

  void _updateSidebarState(WidgetRef ref) {
    final dashboardState = ref.read(applicantDashboardViewModelProvider);

    if (dashboardState.applicantDetails?.data.applications != null) {
      final applications = dashboardState.applicantDetails!.data.applications;

      for (int i = 0; i < applications.length; i++) {
        final app = applications[i];
        if (app.product.variant.toUpperCase() == 'DBS' &&
            app.product.code == productCode) {
          ref.read(sidebarSelectedIndexProvider.notifier).state = i + 1;
          break;
        }
      }
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isMobile = ResponsiveHelper.isMobile(context);

    // Update sidebar to show the correct active application
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateSidebarState(ref);
    });

    return Scaffold(
      backgroundColor: AppColors.kWhiteColor,
      drawer: isMobile ? ApplicantSidebar(
        applicantId: applicantId,
        showBackButton: true,
        onBackPressed: () => _handleBackToDashboard(ref),
      ) : null,
      body: _buildLayout(context, isMobile, ref),
    );
  }

  Widget _buildLayout(BuildContext context, bool isMobile, WidgetRef ref) {
    if (isMobile) {
      return Column(
        children: [
          // Universal mobile header
          UniversalMobileHeader(
            showBackButton: true,
            onBackPressed: () => _handleBackToDashboard(ref),
          ),
          // Content area
          Expanded(
            child: _buildContent(context, isMobile, ref),
          ),
        ],
      );
    }

    return Row(
      children: [
        ApplicantSidebar(
          applicantId: applicantId,
          showBackButton: true,
          onBackPressed: () => _handleBackToDashboard(ref),
        ),
        Expanded(
          child: _buildContent(context, isMobile, ref),
        ),
      ],
    );
  }

  Widget _buildContent(BuildContext context, bool isMobile, WidgetRef ref) {
    return Container(
      color: Colors.grey[50],
      child: Center(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(isMobile ? 24.0 : 48.0),
          child: Container(
            constraints: const BoxConstraints(maxWidth: 600),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(100),
                        ),
                        child: Icon(
                          Icons.check_circle,
                          size: isMobile ? 60 : 80,
                          color: AppColors.kBlueColor,
                        ),
                      ),
                      const SizedBox(height: 30),
                      Text(
                        'Form Submitted Successfully!',
                        style: TextStyle(
                          fontSize: isMobile ? 24 : 32,
                          fontWeight: FontWeight.bold,
                          color: AppColors.kBlueColor,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 20),
                      Text(
                        'Your $productName application has been submitted successfully.',
                        style: TextStyle(
                          fontSize: isMobile ? 16 : 18,
                          color: Colors.grey[700],
                          height: 1.6,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 40),
                      SizedBox(
                        width: isMobile ? double.infinity : 400,
                        child: SolidButton(
                          text: 'Nominate Documents',
                          onPressed: () => _handleNominateDocuments(context),
                          type: SolidButtonType.primary,
                          size: SolidButtonSize.large,
                          isFullWidth: true,
                        ),
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        width: isMobile ? double.infinity : 400,
                        child: SolidButton(
                          text: 'Back to Dashboard',
                          onPressed: () => _handleBackToDashboard(ref),
                          type: SolidButtonType.outline,
                          size: SolidButtonSize.large,
                          isFullWidth: true,
                        ),
                      ),
                      const SizedBox(height: 40),
                      _buildNextStepsSection(isMobile),
                      if (completionData != null) ...[
                        const SizedBox(height: 30),
                        _buildCompletionDetails(isMobile),
                      ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNextStepsSection(bool isMobile) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppColors.kBlueColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Next Steps',
                style: TextStyle(
                  fontSize: isMobile ? 16 : 18,
                  fontWeight: FontWeight.w600,
                  color: AppColors.kBlueColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildNextStepItem('Complete document nomination to proceed with your application'),
          _buildNextStepItem('You will receive email updates on your application status'),
          _buildNextStepItem('Contact support if you need assistance with the process'),
        ],
      ),
    );
  }

  Widget _buildNextStepItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 6),
            width: 4,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.kBlueColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompletionDetails(bool isMobile) {
    final formCompletion = completionData!['form_completion'] as Map<String, dynamic>;
    final completedAt = formCompletion['completed_at'] as String?;
    final completedBy = formCompletion['completed_by'] as Map<String, dynamic>?;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Submission Details',
            style: TextStyle(
              fontSize: isMobile ? 14 : 16,
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 12),
          if (completedAt != null) ...[
            _buildDetailRow('Completed At:', _formatDateTime(completedAt), isMobile),
            const SizedBox(height: 8),
          ],
          if (completedBy != null) ...[
            _buildDetailRow('Submitted By:', _formatUserType(completedBy), isMobile),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, bool isMobile) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: isMobile ? 100 : 120,
          child: Text(
            label,
            style: TextStyle(
              fontSize: isMobile ? 12 : 14,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: isMobile ? 12 : 14,
              color: Colors.grey[800],
            ),
          ),
        ),
      ],
    );
  }

  String _formatDateTime(String dateTimeStr) {
    try {
      final dateTime = DateTime.parse(dateTimeStr);
      return '${dateTime.day}/${dateTime.month}/${dateTime.year} at ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return dateTimeStr;
    }
  }

  String _formatUserType(Map<String, dynamic> completedBy) {
    final userType = completedBy['user_type'] as String?;
    final userName = completedBy['user_name'] as String?;
    final userEmail = completedBy['user_email'] as String?;

    switch (userType?.toLowerCase()) {
      case 'applicant':
        return 'You (Applicant)';
      case 'client_user':
      case 'requester':
      case 'doc_checker':
        if (userName != null && userName.isNotEmpty) {
          return userName;
        } else if (userEmail != null && userEmail.isNotEmpty) {
          return userEmail;
        } else {
          return userType ?? 'Unknown';
        }
      default:
        return userType ?? 'Unknown';
    }
  }

  void _handleNominateDocuments(BuildContext context) {
    Navigator.pushNamed(
      context,
      '/document-nomination',
      arguments: {
        'applicationId': applicationId,
        'applicantName': _getApplicantName(),
      },
    );
  }

  String _getApplicantName() {
    // Try to get applicant name from the new API structure
    final applicantData = completionData?['applicant'] as Map<String, dynamic>?;
    final applicantName = applicantData?['name'] as String?;

    if (applicantName != null && applicantName.isNotEmpty) {
      return applicantName;
    }

    // Try the old structure as fallback
    final oldApplicantName = completionData?['applicant_name'] as String?;
    if (oldApplicantName != null && oldApplicantName.isNotEmpty) {
      return oldApplicantName;
    }

    // Final fallback
    return 'Applicant';
  }
}
