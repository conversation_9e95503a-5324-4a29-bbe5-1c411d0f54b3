import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/dbs/presentation/utils/form_validation_utils.dart';
import 'package:SolidCheck/features/dbs/providers/form_validation_provider.dart';
import 'package:SolidCheck/shared/utils/dbs_validation_rules.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:form_builder_validators/form_builder_validators.dart';

class DBSFormBuilderTextField extends ConsumerWidget {
  final String name;
  final String label;
  final String? hint;
  final String? initialValue;
  final bool isRequired;
  final bool isMultiline;
  final bool readOnly;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final List<FormFieldValidator<String>> validators;
  final void Function(String?)? onChanged;
  final void Function()? onTap;
  final Widget? suffixIcon;
  final int? maxLines;
  final int? minLines;
  final int? maxLength;
  final bool obscureText;
  final TextCapitalization textCapitalization;

  const DBSFormBuilderTextField({
    super.key,
    required this.name,
    required this.label,
    this.hint,
    this.initialValue,
    this.isRequired = false,
    this.isMultiline = false,
    this.readOnly = false,
    this.keyboardType,
    this.inputFormatters,
    this.validators = const [],
    this.onChanged,
    this.onTap,
    this.suffixIcon,
    this.maxLines,
    this.minLines,
    this.maxLength,
    this.obscureText = false,
    this.textCapitalization = TextCapitalization.none,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final validationMode = ref.watch(formValidationModeProvider);

    return FormBuilderTextField(
      name: name,
      initialValue: initialValue,
      autovalidateMode: validationMode,
      decoration: _buildInputDecoration(),
      keyboardType: keyboardType,
      inputFormatters: inputFormatters,
      maxLines: isMultiline ? (maxLines ?? 3) : 1,
      minLines: minLines,
      maxLength: maxLength,
      obscureText: obscureText,
      textCapitalization: textCapitalization,
      readOnly: readOnly,
      onTap: onTap,
      onChanged: onChanged,
      validator: FormBuilderValidators.compose([
        if (isRequired) FormBuilderValidators.required(errorText: '$label is required'),
        ...validators,
      ]),
    );
  }

  InputDecoration _buildInputDecoration() {
    return InputDecoration(
      labelText: isRequired ? '$label *' : label,
      hintText: hint,
      suffixIcon: suffixIcon,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: Colors.grey.shade300),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: Colors.grey.shade300),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: AppColors.kBlueColor, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Colors.red, width: 2),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Colors.red, width: 2),
      ),
      filled: true,
      fillColor: readOnly ? Colors.grey.shade100 : Colors.white,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      errorStyle: const TextStyle(color: Colors.red, fontSize: 12, height: 1.2),
      helperStyle: const TextStyle(fontSize: 12, height: 1.2),
    );
  }
}

class DBSFormBuilderDropdown<T> extends ConsumerWidget {
  final String name;
  final String label;
  final String? hint;
  final T? initialValue;
  final bool isRequired;
  final List<DropdownMenuItem<T>> items;
  final void Function(T?)? onChanged;
  final List<FormFieldValidator<T>> validators;

  const DBSFormBuilderDropdown({
    super.key,
    required this.name,
    required this.label,
    this.hint,
    this.initialValue,
    this.isRequired = false,
    required this.items,
    this.onChanged,
    this.validators = const [],
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final validationMode = ref.watch(formValidationModeProvider);

    return FormBuilderDropdown<T>(
      name: name,
      initialValue: initialValue,
      autovalidateMode: validationMode,
      decoration: _buildDropdownDecoration(),
      items: items,
      onChanged: onChanged,
      validator: FormBuilderValidators.compose([
        if (isRequired) FormBuilderValidators.required(errorText: '$label is required'),
        ...validators,
      ]),
    );
  }

  InputDecoration _buildDropdownDecoration() {
    return InputDecoration(
      labelText: isRequired ? '$label *' : label,
      hintText: hint,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: Colors.grey.shade300),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: Colors.grey.shade300),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: AppColors.kBlueColor, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Colors.red, width: 2),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Colors.red, width: 2),
      ),
      filled: true,
      fillColor: Colors.white,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      errorStyle: const TextStyle(color: Colors.red, fontSize: 12, height: 1.2),
      helperStyle: const TextStyle(fontSize: 12, height: 1.2),
    );
  }
}

class DBSFormBuilderDatePicker extends ConsumerWidget {
  final String name;
  final String label;
  final String? hint;
  final DateTime? initialValue;
  final bool isRequired;
  final DateTime? firstDate;
  final DateTime? lastDate;
  final void Function(DateTime?)? onChanged;
  final List<FormFieldValidator<DateTime>> validators;
  final InputType inputType;

  const DBSFormBuilderDatePicker({
    super.key,
    required this.name,
    required this.label,
    this.hint,
    this.initialValue,
    this.isRequired = false,
    this.firstDate,
    this.lastDate,
    this.onChanged,
    this.validators = const [],
    this.inputType = InputType.date,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final validationMode = ref.watch(formValidationModeProvider);

    return FormBuilderField<DateTime>(
      name: name,
      initialValue: initialValue,
      autovalidateMode: validationMode,
      validator: FormBuilderValidators.compose([
        if (isRequired) FormBuilderValidators.required(errorText: '$label is required'),
        ...validators,
      ]),
      builder: (FormFieldState<DateTime> field) {
        return _DatePickerWidget(
          field: field,
          label: label,
          hint: hint,
          isRequired: isRequired,
          firstDate: firstDate,
          lastDate: lastDate,
          onChanged: onChanged,
        );
      },
    );
  }


}

class _DatePickerWidget extends StatefulWidget {
  final FormFieldState<DateTime> field;
  final String label;
  final String? hint;
  final bool isRequired;
  final DateTime? firstDate;
  final DateTime? lastDate;
  final void Function(DateTime?)? onChanged;

  const _DatePickerWidget({
    required this.field,
    required this.label,
    this.hint,
    required this.isRequired,
    this.firstDate,
    this.lastDate,
    this.onChanged,
  });

  @override
  State<_DatePickerWidget> createState() => _DatePickerWidgetState();
}

class _DatePickerWidgetState extends State<_DatePickerWidget> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _updateControllerText();

    _controller.addListener(() {
      final text = _controller.text;
      final date = FormValidationUtils.parseDate(text);
      if (date != null) {
        widget.field.didChange(date);
        widget.onChanged?.call(date);
      } else if (text.isEmpty) {
        widget.field.didChange(null);
        widget.onChanged?.call(null);
      }
    });
  }

  @override
  void didUpdateWidget(_DatePickerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.field.value != widget.field.value) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _updateControllerText();
        }
      });
    }
  }

  void _updateControllerText() {
    final newText = widget.field.value != null ? FormValidationUtils.formatDate(widget.field.value!) : '';
    if (_controller.text != newText) {
      _controller.text = newText;
      _controller.selection = TextSelection.fromPosition(
        TextPosition(offset: _controller.text.length),
      );
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: _controller,
      decoration: InputDecoration(
        labelText: widget.isRequired ? '${widget.label} *' : widget.label,
        hintText: widget.hint ?? 'DD/MM/YYYY',
        suffixIcon: IconButton(
          icon: Icon(Icons.calendar_today, color: AppColors.kBlueColor),
          onPressed: () async {
            final selectedDate = await showDatePicker(
              context: context,
              initialDate: widget.field.value ?? DateTime.now(),
              firstDate: widget.firstDate ?? DateTime(1900),
              lastDate: widget.lastDate ?? DateTime.now(),
              builder: (context, child) {
                return Theme(
                  data: Theme.of(context).copyWith(
                    colorScheme: ColorScheme.light(
                      primary: AppColors.kBlueColor,
                      onPrimary: AppColors.kWhiteColor,
                      surface: AppColors.kWhiteColor,
                      onSurface: AppColors.kBlackColor,
                    ),
                  ),
                  child: child!,
                );
              },
            );

            if (selectedDate != null) {
              widget.field.didChange(selectedDate);
              _updateControllerText();
              widget.onChanged?.call(selectedDate);
            }
          },
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppColors.kBlueColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        filled: true,
        fillColor: Colors.white,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        errorText: widget.field.errorText,
        errorStyle: const TextStyle(
          color: Colors.red,
          fontSize: 12,
          height: 1.2,
        ),
        helperStyle: const TextStyle(
          fontSize: 12,
          height: 1.2,
        ),
      ),
      keyboardType: TextInputType.datetime,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'[0-9/]')),
        LengthLimitingTextInputFormatter(10),
        _DateInputFormatter(),
      ],
    );
  }


}

class DBSFormBuilderRadioGroup extends ConsumerWidget {
  final String name;
  final String label;
  final String? initialValue;
  final bool isRequired;
  final List<FormBuilderFieldOption<String>> options;
  final void Function(String?)? onChanged;
  final List<FormFieldValidator<String>> validators;
  final OptionsOrientation orientation;

  const DBSFormBuilderRadioGroup({
    super.key,
    required this.name,
    required this.label,
    this.initialValue,
    this.isRequired = false,
    required this.options,
    this.onChanged,
    this.validators = const [],
    this.orientation = OptionsOrientation.horizontal,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final validationMode = ref.watch(formValidationModeProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isRequired ? '$label *' : label,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        FormBuilderRadioGroup<String>(
          name: name,
          initialValue: initialValue,
          options: options,
          orientation: orientation,
          autovalidateMode: validationMode,
          onChanged: onChanged,
          validator: FormBuilderValidators.compose([
            if (isRequired) FormBuilderValidators.required(errorText: '$label is required'),
            ...validators,
          ]),
          decoration: const InputDecoration(
            border: InputBorder.none,
            contentPadding: EdgeInsets.zero,
            errorStyle: TextStyle(color: Colors.red, fontSize: 12, height: 1.2),
          ),
        ),
      ],
    );
  }
}

class DBSValidators {
  static FormFieldValidator<String> forename() {
    return (value) => DBSValidationRules.validateForename(value);
  }

  static FormFieldValidator<String> presentSurname() {
    return (value) => DBSValidationRules.validatePresentSurname(value);
  }

  static FormFieldValidator<String> middleName() {
    return (value) => DBSValidationRules.validateMiddleName(value);
  }

  static FormFieldValidator<String> contactNumber() {
    return (value) => DBSValidationRules.validateContactNumber(value);
  }

  static FormFieldValidator<String> niNumber() {
    return (value) => DBSValidationRules.validateNINumber(value);
  }

  static FormFieldValidator<String> birthTown() {
    return (value) => DBSValidationRules.validateBirthTown(value);
  }

  static FormFieldValidator<String> birthCounty() {
    return (value) => DBSValidationRules.validateBirthCounty(value);
  }

  static FormFieldValidator<String> birthNationality() {
    return (value) => DBSValidationRules.validateBirthNationality(value);
  }

  static FormFieldValidator<String> addressLine1() {
    return (value) => DBSValidationRules.validateAddressLine1(value);
  }

  static FormFieldValidator<String> addressLine2() {
    return (value) => DBSValidationRules.validateAddressLine2(value);
  }

  static FormFieldValidator<String> addressTown() {
    return (value) => DBSValidationRules.validateAddressTown(value);
  }

  static FormFieldValidator<String> addressCounty() {
    return (value) => DBSValidationRules.validateAddressCounty(value);
  }

  static FormFieldValidator<String> postcode(String? countryCode) {
    return (value) => DBSValidationRules.validatePostcode(value, countryCode);
  }

  static FormFieldValidator<DateTime> dateOfBirth() {
    return (value) {
      if (value == null) return 'Date of birth is required';

      // Enhanced date validation
      if (value.isAfter(DateTime.now())) {
        return 'Date of birth cannot be in the future';
      }

      if (value.year < 1900) {
        return 'Please enter a valid year (1900 or later)';
      }

      // Check if person is too young (less than 16 years old)
      final now = DateTime.now();
      final age = now.year - value.year;
      if (age < 16 || (age == 16 && now.month < value.month) ||
          (age == 16 && now.month == value.month && now.day < value.day)) {
        return 'Applicant must be at least 16 years old';
      }

      // Check if person is too old (more than 120 years old)
      if (age > 120) {
        return 'Please enter a valid date of birth';
      }

      return null;
    };
  }

  static FormFieldValidator<DateTime> passportDOB(DateTime? applicantDOB) {
    return (value) {
      if (value == null) return 'Passport date of birth is required';

      // Cross-field validation: passport DOB must match applicant DOB
      if (applicantDOB != null) {
        if (value.year != applicantDOB.year ||
            value.month != applicantDOB.month ||
            value.day != applicantDOB.day) {
          return 'Passport date of birth must match your date of birth from Birth Details';
        }
      }

      // Additional date validation
      if (value.isAfter(DateTime.now())) {
        return 'Date of birth cannot be in the future';
      }

      if (value.year < 1900) {
        return 'Please enter a valid year (1900 or later)';
      }

      return null;
    };
  }

  static FormFieldValidator<DateTime> driverLicenceDOB(DateTime? applicantDOB) {
    return (value) {
      if (value == null) return 'Driver licence date of birth is required';

      if (applicantDOB != null) {
        if (value.year != applicantDOB.year ||
            value.month != applicantDOB.month ||
            value.day != applicantDOB.day) {
          return 'Driver licence date of birth must match your date of birth from Birth Details';
        }
      }

      if (value.isAfter(DateTime.now())) {
        return 'Date of birth cannot be in the future';
      }

      if (value.year < 1900) {
        return 'Please enter a valid year (1900 or later)';
      }

      return null;
    };
  }

  static FormFieldValidator<String> passportNumber() {
    return (value) {
      if (value == null || value.trim().isEmpty) {
        return 'Passport number is required';
      }

      final trimmed = value.trim();
      if (trimmed.length > 11) {
        return 'Passport number must not exceed 11 characters';
      }

      final pattern = RegExp(r"^([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])$");
      if (!pattern.hasMatch(trimmed.toUpperCase())) {
        return 'Passport number contains invalid characters. Only A-Z, 0-9, (), /, -, \', & are allowed';
      }

      return null;
    };
  }

  static FormFieldValidator<String> passportNationality() {
    return (value) {
      if (value == null || value.trim().isEmpty) {
        return 'Passport nationality is required';
      }

      final trimmed = value.trim();
      if (trimmed.length > 30) {
        return 'Passport nationality must not exceed 30 characters';
      }

      final pattern = RegExp(r"^([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])$");
      if (!pattern.hasMatch(trimmed.toUpperCase())) {
        return 'Passport nationality contains invalid characters. Only A-Z, 0-9, (), /, -, \', & are allowed';
      }

      return null;
    };
  }

  static FormFieldValidator<DateTime> passportIssueDate() {
    return (value) {
      if (value == null) return 'Passport issue date is required';

      // Must not be future date
      if (value.isAfter(DateTime.now())) {
        return 'Passport issue date cannot be in the future';
      }

      return null;
    };
  }

  static FormFieldValidator<String> driverLicenceNumber() {
    return (value) {
      if (value == null || value.trim().isEmpty) {
        return 'Driving licence number is required';
      }

      final trimmed = value.trim();
      if (trimmed.length > 18) {
        return 'Driving licence number must not exceed 18 characters';
      }

      final pattern = RegExp(r"^([A-Z0-9()/\-'&]+)|([A-Z0-9()/\-'&][A-Z0-9()/\-'&]*[A-Z0-9()/\-'&])$");
      if (!pattern.hasMatch(trimmed.toUpperCase())) {
        return 'Driving licence number contains invalid characters. Only A-Z, 0-9, (), /, -, \', & are allowed';
      }

      return null;
    };
  }

  static FormFieldValidator<String> driverLicenceType() {
    return (value) {
      if (value == null || value.trim().isEmpty) {
        return 'Driving licence type is required';
      }

      final validTypes = ['paper', 'photo'];
      if (!validTypes.contains(value.toLowerCase())) {
        return 'Please select a valid licence type';
      }

      return null;
    };
  }

  static FormFieldValidator<DateTime> driverLicenceValidFrom() {
    return (value) {
      if (value == null) return 'Driving licence valid from date is required';

      if (value.isAfter(DateTime.now())) {
        return 'Driving licence valid from date cannot be in the future';
      }

      return null;
    };
  }

  static FormFieldValidator<String> driverLicenceIssueCountry() {
    return (value) {
      if (value == null || value.trim().isEmpty) {
        return 'Issue country is required';
      }

      if (value.length != 2) {
        return 'Country code must be 2 characters';
      }

      final pattern = RegExp(r'^[A-Z]{2}$');
      if (!pattern.hasMatch(value.toUpperCase())) {
        return 'Invalid country code format';
      }

      return null;
    };
  }

  static FormFieldValidator<String> birthCountry() {
    return (value) => DBSValidationRules.validateBirthCountry(value);
  }

  static FormFieldValidator<String> birthSurname({String? gender, String? title}) {
    return (value) => DBSValidationRules.validateBirthSurname(value, gender, title);
  }


}

class _DateInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text.replaceAll(RegExp(r'[^0-9]'), '');

    if (text.length > 8) {
      return oldValue;
    }

    final buffer = StringBuffer();

    for (int i = 0; i < text.length; i++) {
      if (i == 2 || i == 4) {
        buffer.write('/');
      }
      buffer.write(text[i]);
    }

    final formatted = buffer.toString();

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(
        offset: formatted.length,
      ),
    );
  }
}
