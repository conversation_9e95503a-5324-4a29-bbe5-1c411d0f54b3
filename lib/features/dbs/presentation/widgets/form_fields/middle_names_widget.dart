import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/form_fields/dbs_text_field.dart';
import 'package:SolidCheck/shared/utils/validations.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Widget for managing up to 3 middle names with validation
class MiddleNamesWidget extends StatefulWidget {
  final List<String> initialMiddleNames;
  final Function(List<String>) onChanged;
  final bool isRequired;

  const MiddleNamesWidget({
    super.key,
    required this.initialMiddleNames,
    required this.onChanged,
    this.isRequired = false,
  });

  @override
  State<MiddleNamesWidget> createState() => _MiddleNamesWidgetState();
}

class _MiddleNamesWidgetState extends State<MiddleNamesWidget> {
  late List<TextEditingController> _controllers;
  late List<String> _middleNames;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  @override
  void didUpdateWidget(MiddleNamesWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Simple approach: if the data changed significantly, update
    if (!_listsEqual(oldWidget.initialMiddleNames, widget.initialMiddleNames)) {
      // Update if we have more names than controllers, or if we're loading data
      if (widget.initialMiddleNames.length > _controllers.length ||
          (widget.initialMiddleNames.isNotEmpty && _controllers.every((c) => c.text.trim().isEmpty))) {
        _updateControllersFromNewData();
      }
    }
  }

  /// Helper method to compare two lists for equality
  bool _listsEqual<T>(List<T> list1, List<T> list2) {
    if (list1.length != list2.length) return false;
    for (int i = 0; i < list1.length; i++) {
      if (list1[i] != list2[i]) return false;
    }
    return true;
  }

  /// Update controllers when new data is received
  void _updateControllersFromNewData() {
    // Dispose existing controllers
    for (final controller in _controllers) {
      controller.dispose();
    }

    // Reinitialize with new data
    _initializeControllers();

    // Trigger a rebuild
    setState(() {});
  }

  void _initializeControllers() {
    _middleNames = List.from(widget.initialMiddleNames);

    // If we have saved middle names, create controllers for all of them
    if (_middleNames.isNotEmpty) {
      // Remove empty names from the end
      while (_middleNames.isNotEmpty && _middleNames.last.trim().isEmpty) {
        _middleNames.removeLast();
      }

      // Ensure we don't exceed 3 middle names
      while (_middleNames.length > 3) {
        _middleNames.removeLast();
      }

      // If we still have no names after cleanup, add one empty field
      if (_middleNames.isEmpty) {
        _middleNames.add('');
      }
    } else {
      // No saved middle names, start with one empty field
      _middleNames.add('');
    }

    _controllers = _middleNames.map((name) => TextEditingController(text: name)).toList();

    // Add listeners to controllers
    for (int i = 0; i < _controllers.length; i++) {
      _controllers[i].addListener(() => _onTextChanged(i));
    }
  }

  void _onTextChanged(int index) {
    if (index < _middleNames.length) {
      _middleNames[index] = _controllers[index].text;
      widget.onChanged(_middleNames.where((name) => name.trim().isNotEmpty).toList());
    }
  }

  void _addMiddleName() {
    if (_controllers.length < 3) {
      setState(() {
        _middleNames.add('');
        final newController = TextEditingController();
        final newIndex = _controllers.length; // Get the correct index before adding
        _controllers.add(newController);
        newController.addListener(() => _onTextChanged(newIndex));
      });
    }
  }

  void _removeMiddleName(int index) {
    if (_controllers.length > 1 && index < _controllers.length) {
      setState(() {
        _controllers[index].dispose();
        _controllers.removeAt(index);
        _middleNames.removeAt(index);
        
        // Re-add listeners with correct indices
        for (int i = 0; i < _controllers.length; i++) {
          _controllers[i].removeListener(() {});
          _controllers[i].addListener(() => _onTextChanged(i));
        }
      });
      widget.onChanged(_middleNames.where((name) => name.trim().isNotEmpty).toList());
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with add button
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Middle Names ${widget.isRequired ? '*' : '(Optional)'}',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.orangeColor,
              ),
            ),
            if (_controllers.length < 3)
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: AppColors.kBlueColor,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: IconButton(
                  padding: EdgeInsets.zero,
                  icon: const Icon(Icons.add, color: Colors.white, size: 18),
                  onPressed: _addMiddleName,
                  tooltip: 'Add middle name',
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        
        // Middle name fields
        ...List.generate(_controllers.length, (index) {
          return Padding(
            padding: EdgeInsets.only(bottom: index < _controllers.length - 1 ? 12 : 0),
            child: Row(
              children: [
                Expanded(
                  child: DBSTextField(
                    controller: _controllers[index],
                    label: 'Middle Name ${index + 1}',
                    isRequired: false,
                    keyboardType: TextInputType.text,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r"[A-Za-z &'-]")),
                      LengthLimitingTextInputFormatter(60),
                      _MiddleNameFormatter(),
                    ],
                    validator: (value) => DBSValidations.validateMiddleName(value),
                    hint: 'Enter middle name ${index + 1}',
                  ),
                ),
                if (_controllers.length > 1) ...[
                  const SizedBox(width: 8),
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: Colors.red.shade100,
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: Colors.red.shade300),
                    ),
                    child: IconButton(
                      padding: EdgeInsets.zero,
                      icon: Icon(Icons.remove, color: Colors.red.shade700, size: 18),
                      onPressed: () => _removeMiddleName(index),
                      tooltip: 'Remove middle name',
                    ),
                  ),
                ],
              ],
            ),
          );
        }),
        
        // Validation message
        if (_controllers.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Builder(
              builder: (context) {
                final validation = DBSValidations.validateMiddleNames(
                  _middleNames.where((name) => name.trim().isNotEmpty).toList()
                );
                if (validation != null) {
                  return Text(
                    validation,
                    style: TextStyle(
                      color: Colors.red.shade700,
                      fontSize: 12,
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
            ),
          ),
        
        // Helper text
        Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Text(
            'You can add up to 3 middle names. Only letters, spaces, apostrophes, and hyphens are allowed.',
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 11,
            ),
          ),
        ),
      ],
    );
  }
}

/// Custom input formatter for middle names
class _MiddleNameFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Convert to uppercase and clean up
    String formatted = newValue.text.toUpperCase();
    
    // Remove invalid characters (keep only A-Z, space, apostrophe, hyphen)
    formatted = formatted.replaceAll(RegExp(r"[^A-Z &'-]"), '');
    
    // Limit to 60 characters
    if (formatted.length > 60) {
      formatted = formatted.substring(0, 60);
    }
    
    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(
        offset: formatted.length.clamp(0, formatted.length),
      ),
    );
  }
}
