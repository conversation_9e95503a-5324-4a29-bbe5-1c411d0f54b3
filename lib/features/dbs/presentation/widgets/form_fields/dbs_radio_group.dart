import 'package:SolidCheck/core/theme/colors.dart';
import 'package:flutter/material.dart';

/// DBS Radio Group Widget
/// Reusable radio button group for Yes/No questions
class DBSRadioGroup extends StatelessWidget {
  final String title;
  final bool value;
  final void Function(bool) onChanged;
  final bool isRequired;

  const DBSRadioGroup({
    super.key,
    required this.title,
    required this.value,
    required this.onChanged,
    this.isRequired = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title
        RichText(
          text: TextSpan(
            text: title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: isRequired ? AppColors.orangeColor : AppColors.kBlackColor,
            ),
            children: isRequired
                ? [
                    TextSpan(
                      text: ' *',
                      style: TextStyle(
                        color: AppColors.orangeColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ]
                : null,
          ),
        ),
        
        const SizedBox(height: 12),
        
        // Radio Options
        Row(
          children: [
            // Yes Option
            GestureDetector(
              onTap: () => onChanged(true),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Radio<bool>(
                    value: true,
                    groupValue: value,
                    onChanged: (bool? newValue) {
                      if (newValue != null) {
                        onChanged(newValue);
                      }
                    },
                    activeColor: AppColors.kBlueColor,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  Text(
                    'Yes',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.kBlackColor,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(width: 32),
            
            // No Option
            GestureDetector(
              onTap: () => onChanged(false),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Radio<bool>(
                    value: false,
                    groupValue: value,
                    onChanged: (bool? newValue) {
                      if (newValue != null) {
                        onChanged(newValue);
                      }
                    },
                    activeColor: AppColors.kBlueColor,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                  Text(
                    'No',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.kBlackColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}

/// DBS Multi-Choice Radio Group Widget
/// Reusable radio button group for multiple choice questions
class DBSMultiChoiceRadioGroup<T> extends StatelessWidget {
  final String title;
  final T? value;
  final List<DBSRadioOption<T>> options;
  final void Function(T?) onChanged;
  final bool isRequired;

  const DBSMultiChoiceRadioGroup({
    super.key,
    required this.title,
    required this.value,
    required this.options,
    required this.onChanged,
    this.isRequired = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title
        RichText(
          text: TextSpan(
            text: title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: isRequired ? AppColors.orangeColor : AppColors.kBlackColor,
            ),
            children: isRequired
                ? [
                    TextSpan(
                      text: ' *',
                      style: TextStyle(
                        color: AppColors.orangeColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ]
                : null,
          ),
        ),
        
        const SizedBox(height: 12),
        
        // Radio Options
        Column(
          children: options.map((option) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: GestureDetector(
                onTap: () => onChanged(option.value),
                child: Row(
                  children: [
                    Radio<T>(
                      value: option.value,
                      groupValue: value,
                      onChanged: onChanged,
                      activeColor: AppColors.kBlueColor,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        option.label,
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.kBlackColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}

/// Radio Option Data Class
class DBSRadioOption<T> {
  final T value;
  final String label;

  DBSRadioOption({
    required this.value,
    required this.label,
  });
}

/// DBS Checkbox Group Widget
/// Reusable checkbox group for multiple selections
class DBSCheckboxGroup extends StatelessWidget {
  final String title;
  final List<DBSCheckboxOption> options;
  final bool isRequired;

  const DBSCheckboxGroup({
    super.key,
    required this.title,
    required this.options,
    this.isRequired = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title
        RichText(
          text: TextSpan(
            text: title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: isRequired ? AppColors.orangeColor : AppColors.kBlackColor,
            ),
            children: isRequired
                ? [
                    TextSpan(
                      text: ' *',
                      style: TextStyle(
                        color: AppColors.orangeColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ]
                : null,
          ),
        ),
        
        const SizedBox(height: 12),
        
        // Checkbox Options
        Column(
          children: options.map((option) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: GestureDetector(
                onTap: () => option.onChanged(!option.value),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Checkbox(
                      value: option.value,
                      onChanged: (bool? newValue) {
                        if (newValue != null) {
                          option.onChanged(newValue);
                        }
                      },
                      activeColor: AppColors.kBlueColor,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        option.label,
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.kBlackColor,
                          height: 1.4,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}

/// Checkbox Option Data Class
class DBSCheckboxOption {
  final bool value;
  final String label;
  final void Function(bool) onChanged;

  DBSCheckboxOption({
    required this.value,
    required this.label,
    required this.onChanged,
  });
}
