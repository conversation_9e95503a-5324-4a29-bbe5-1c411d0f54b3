import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_field_help_tooltip.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// DBS Text Field Widget
/// Reusable text field component for DBS forms
class DBSTextField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final String? hint;
  final bool isRequired;
  final bool isMultiline;
  final bool readOnly;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final void Function()? onTap;
  final Widget? suffixIcon;
  final int? maxLines;
  final int? minLines;
  final String? helpKey; // Key to lookup help text from DBSFieldHelp

  const DBSTextField({
    super.key,
    required this.controller,
    required this.label,
    this.hint,
    this.isRequired = false,
    this.isMultiline = false,
    this.readOnly = false,
    this.keyboardType,
    this.inputFormatters,
    this.validator,
    this.onChanged,
    this.onTap,
    this.suffixIcon,
    this.maxLines,
    this.minLines,
    this.helpKey,
  });

  @override
  Widget build(BuildContext context) {
    // Get help information if helpKey is provided
    final helpInfo = helpKey != null ? DBSFieldHelp.getFieldHelp(helpKey!) : null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label with optional help tooltip
        if (helpInfo != null)
          DBSFieldHelpTooltip(
            helpText: helpInfo['helpText'] ?? '',
            example: helpInfo['example'],
            tips: helpInfo['tips']?.cast<String>(),
            child: _buildLabelWidget(),
          )
        else
          _buildLabelWidget(),

        const SizedBox(height: 8),

        // Text Field
        TextFormField(
          controller: controller,
          readOnly: readOnly,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          validator: validator,
          onChanged: onChanged,
          onTap: onTap,
          maxLines: isMultiline ? (maxLines ?? 5) : 1,
          minLines: isMultiline ? (minLines ?? 3) : 1,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(
              color: Colors.grey[500],
              fontSize: 14,
            ),
            suffixIcon: suffixIcon,
            filled: true,
            fillColor: readOnly ? Colors.grey[100] : AppColors.kWhiteColor,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: Colors.grey[300]!,
                width: 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: Colors.grey[300]!,
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: AppColors.kBlueColor,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: Colors.red,
                width: 1,
              ),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: Colors.red,
                width: 2,
              ),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLabelWidget() {
    return Row(
      children: [
        RichText(
          text: TextSpan(
            text: label,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: isRequired ? AppColors.orangeColor : AppColors.kBlackColor,
            ),
            children: isRequired
                ? [
                    TextSpan(
                      text: ' *',
                      style: TextStyle(
                        color: AppColors.orangeColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ]
                : null,
          ),
        ),
        if (helpKey != null) ...[
          const SizedBox(width: 6),
          Icon(
            Icons.help_outline,
            size: 16,
            color: AppColors.kBlueColor.withValues(alpha: 0.7),
          ),
        ],
      ],
    );
  }
}

/// DBS Date Field Widget
/// Specialized text field for date input with date picker
class DBSDateField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final bool isRequired;
  final String? Function(String?)? validator;
  final void Function(DateTime?)? onDateSelected;
  final DateTime? firstDate;
  final DateTime? lastDate;

  const DBSDateField({
    super.key,
    required this.controller,
    required this.label,
    this.isRequired = false,
    this.validator,
    this.onDateSelected,
    this.firstDate,
    this.lastDate,
  });

  @override
  Widget build(BuildContext context) {
    return DBSTextField(
      controller: controller,
      label: label,
      isRequired: isRequired,
      readOnly: true,
      validator: validator,
      suffixIcon: Icon(
        Icons.calendar_today,
        color: AppColors.kBlueColor,
        size: 20,
      ),
      onTap: () async {
        final selectedDate = await showDatePicker(
          context: context,
          initialDate: DateTime.now(),
          firstDate: firstDate ?? DateTime(1900),
          lastDate: lastDate ?? DateTime.now(),
          builder: (context, child) {
            return Theme(
              data: Theme.of(context).copyWith(
                colorScheme: ColorScheme.light(
                  primary: AppColors.kBlueColor,
                  onPrimary: AppColors.kWhiteColor,
                  surface: AppColors.kWhiteColor,
                  onSurface: AppColors.kBlackColor,
                ),
              ),
              child: child!,
            );
          },
        );

        if (selectedDate != null) {
          controller.text = _formatDate(selectedDate);
          onDateSelected?.call(selectedDate);
        }
      },
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}

/// DBS Flexible Date Field Widget
/// Allows both manual entry and date picker selection
class DBSFlexibleDateField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final bool isRequired;
  final String? Function(String?)? validator;
  final void Function(DateTime?)? onDateSelected;
  final void Function(String)? onChanged;
  final DateTime? firstDate;
  final DateTime? lastDate;

  const DBSFlexibleDateField({
    super.key,
    required this.controller,
    required this.label,
    this.isRequired = false,
    this.validator,
    this.onDateSelected,
    this.onChanged,
    this.firstDate,
    this.lastDate,
  });

  @override
  Widget build(BuildContext context) {
    return DBSTextField(
      controller: controller,
      label: label,
      isRequired: isRequired,
      validator: validator,
      onChanged: onChanged,
      hint: 'DD/MM/YYYY',
      keyboardType: TextInputType.datetime,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'[0-9/]')),
        LengthLimitingTextInputFormatter(10),
        _DateInputFormatter(),
      ],
      suffixIcon: IconButton(
        icon: Icon(
          Icons.calendar_today,
          color: AppColors.kBlueColor,
          size: 20,
        ),
        onPressed: () async {
          final selectedDate = await showDatePicker(
            context: context,
            initialDate: _parseDate(controller.text) ?? DateTime.now(),
            firstDate: firstDate ?? DateTime(1900),
            lastDate: lastDate ?? DateTime.now(),
            builder: (context, child) {
              return Theme(
                data: Theme.of(context).copyWith(
                  colorScheme: ColorScheme.light(
                    primary: AppColors.kBlueColor,
                    onPrimary: AppColors.kWhiteColor,
                    surface: AppColors.kWhiteColor,
                    onSurface: AppColors.kBlackColor,
                  ),
                ),
                child: child!,
              );
            },
          );

          if (selectedDate != null) {
            controller.text = _formatDate(selectedDate);
            onDateSelected?.call(selectedDate);
          }
        },
      ),
    );
  }

  DateTime? _parseDate(String dateString) {
    if (dateString.isEmpty) return null;

    final parts = dateString.split('/');
    if (parts.length == 3) {
      final day = int.tryParse(parts[0]);
      final month = int.tryParse(parts[1]);
      final year = int.tryParse(parts[2]);

      if (day != null && month != null && year != null) {
        try {
          return DateTime(year, month, day);
        } catch (e) {
          return null;
        }
      }
    }
    return null;
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}

/// Custom input formatter for date fields
class _DateInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text;

    if (text.length <= 2) {
      return newValue;
    } else if (text.length <= 5) {
      if (text.length == 3 && !text.contains('/')) {
        return TextEditingValue(
          text: '${text.substring(0, 2)}/${text.substring(2)}',
          selection: TextSelection.collapsed(offset: text.length + 1),
        );
      }
      return newValue;
    } else if (text.length <= 10) {
      if (text.length == 6 && text.split('/').length == 2) {
        return TextEditingValue(
          text: '${text.substring(0, 5)}/${text.substring(5)}',
          selection: TextSelection.collapsed(offset: text.length + 1),
        );
      }
      return newValue;
    }

    return oldValue;
  }
}

/// DBS Birth Field Widget
/// Specialized text field for birth-related fields with auto-formatting
class DBSBirthField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final bool isRequired;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;

  const DBSBirthField({
    super.key,
    required this.controller,
    required this.label,
    this.isRequired = false,
    this.validator,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return DBSTextField(
      controller: controller,
      label: label,
      isRequired: isRequired,
      keyboardType: TextInputType.text,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r"[A-Za-z0-9()/\-'&\s]")),
        LengthLimitingTextInputFormatter(30),
        _BirthFieldFormatter(),
      ],
      validator: validator,
      onChanged: onChanged,
    );
  }
}

/// Custom input formatter for birth fields
class _BirthFieldFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Convert to uppercase and remove invalid characters
    String formatted = newValue.text.toUpperCase();
    formatted = formatted.replaceAll(RegExp(r"[^A-Z0-9()/\-'&]"), '');

    // Limit to 30 characters
    if (formatted.length > 30) {
      formatted = formatted.substring(0, 30);
    }

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(
        offset: formatted.length.clamp(0, formatted.length),
      ),
    );
  }
}

/// DBS Number Field Widget
/// Specialized text field for numeric input
class DBSNumberField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final bool isRequired;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final bool allowDecimals;

  const DBSNumberField({
    super.key,
    required this.controller,
    required this.label,
    this.isRequired = false,
    this.validator,
    this.onChanged,
    this.allowDecimals = false,
  });

  @override
  Widget build(BuildContext context) {
    return DBSTextField(
      controller: controller,
      label: label,
      isRequired: isRequired,
      keyboardType: TextInputType.number,
      inputFormatters: [
        if (!allowDecimals) FilteringTextInputFormatter.digitsOnly,
        if (allowDecimals) FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
      ],
      validator: validator,
      onChanged: onChanged,
    );
  }
}
