import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/dbs/presentation/viewmodels/dbs_form_viewmodel.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_save_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ConsentStep extends ConsumerStatefulWidget {
  final VoidCallback? onSubmit;

  const ConsentStep({
    super.key,
    this.onSubmit,
  });

  @override
  ConsumerState<ConsentStep> createState() => _ConsentStepState();
}

class _ConsentStepState extends ConsumerState<ConsentStep> {
  bool _hasAcceptedDeclaration = false;
  String _unspentConvictions = 'n'; // 'y' or 'n'

  @override
  void initState() {
    super.initState();
    final formState = ref.read(dbsFormViewModelProvider);
    _unspentConvictions = formState.unspentConvictions;
    _hasAcceptedDeclaration = formState.declarationByApplicant == 'y';
  }

  void _updateConsentData() {
    ref.read(dbsFormViewModelProvider.notifier).updateConsentFields(
      unspentConvictions: _unspentConvictions,
      declarationByApplicant: _hasAcceptedDeclaration ? 'y' : 'n',
      languagePreference: 'english',
    );

    // Also update the form data directly
    final formNotifier = ref.read(dbsFormViewModelProvider.notifier);
    final formState = ref.read(dbsFormViewModelProvider);
    final currentAdditionalDetails = formState.formData.applicantDetails.additionalApplicantDetails;
    final updatedAdditionalDetails = currentAdditionalDetails.copyWith(
      unspentConvictions: _unspentConvictions,
      declarationByApplicant: _hasAcceptedDeclaration ? 'y' : 'n',
      languagePreference: 'english',
    );
    formNotifier.updateAdditionalApplicantDetails(updatedAdditionalDetails);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20.0),
      decoration: BoxDecoration(
        color: AppColors.applicationOverviewDivColor,
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Consent and Declaration',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.kBlueColor,
              ),
            ),

            const SizedBox(height: 24),

            // Unspent Convictions Section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.kWhiteColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Criminal Convictions',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.kBlackColor,
                    ),
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    'Do you have any convictions, cautions, reprimands or final warnings which would not be filtered in line with current guidance?',
                    style: TextStyle(fontSize: 14, height: 1.5),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: RadioListTile<String>(
                          value: 'n',
                          groupValue: _unspentConvictions,
                          onChanged: (value) {
                            setState(() {
                              _unspentConvictions = value ?? 'n';
                            });
                            _updateConsentData();
                          },
                          title: const Text('No', style: TextStyle(fontSize: 14)),
                          contentPadding: EdgeInsets.zero,
                          activeColor: AppColors.kBlueColor,
                        ),
                      ),
                      Expanded(
                        child: RadioListTile<String>(
                          value: 'y',
                          groupValue: _unspentConvictions,
                          onChanged: (value) {
                            setState(() {
                              _unspentConvictions = value ?? 'n';
                            });
                            _updateConsentData();
                          },
                          title: const Text('Yes', style: TextStyle(fontSize: 14)),
                          contentPadding: EdgeInsets.zero,
                          activeColor: AppColors.kBlueColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.kWhiteColor,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Declaration',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.kBlackColor,
                    ),
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    'I declare that:\n\n'
                    '• The information I have provided is true and complete\n'
                    '• I understand that any false information may lead to refusal of my application\n'
                    '• I consent to enquiries being made to verify the information provided\n'
                    '• I understand that this information will be processed in accordance with data protection legislation',
                    style: TextStyle(fontSize: 14, height: 1.5),
                  ),
                  const SizedBox(height: 16),
                  CheckboxListTile(
                    value: _hasAcceptedDeclaration,
                    onChanged: (value) {
                      setState(() {
                        _hasAcceptedDeclaration = value ?? false;
                      });
                      _updateConsentData();
                    },
                    title: const Text(
                      'I confirm this declaration',
                      style: TextStyle(fontSize: 14),
                    ),
                    controlAffinity: ListTileControlAffinity.leading,
                    contentPadding: EdgeInsets.zero,
                    activeColor: AppColors.kBlueColor,
                  ),
                  if (!_hasAcceptedDeclaration)
                    const Padding(
                      padding: EdgeInsets.only(left: 16, top: 8),
                      child: Text(
                        'You must accept the declaration to submit your application',
                        style: TextStyle(
                          color: Colors.red,
                          fontSize: 12,
                        ),
                      ),
                    ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            Row(
              children: [
                const Expanded(
                  flex: 1,
                  child: DBSSaveButton(
                    customText: 'Save Progress',
                    showIcon: true,
                    padding: EdgeInsets.only(right: 8),
                  ),
                ),

                Expanded(
                  flex: 2,
                  child: Consumer(
                    builder: (context, ref, child) {
                      final formState = ref.watch(dbsFormViewModelProvider);
                      final formNotifier = ref.read(dbsFormViewModelProvider.notifier);

                      return ElevatedButton(
                        onPressed: (_hasAcceptedDeclaration && !formState.isLoading)
                            ? () async {
                                _updateConsentData();
                                await formNotifier.submitForm();

                                // Check the updated form state after submission
                                final updatedFormState = ref.read(dbsFormViewModelProvider);

                                if (updatedFormState.isSubmitted && context.mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Row(
                                        children: [
                                          Icon(Icons.check_circle, color: Colors.white),
                                          SizedBox(width: 8),
                                          Text('Application submitted successfully!'),
                                        ],
                                      ),
                                      backgroundColor: Colors.green,
                                      duration: Duration(seconds: 5),
                                    ),
                                  );

                                  if (widget.onSubmit != null) {
                                    widget.onSubmit!();
                                  }
                                } else if (updatedFormState.error != null && context.mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Row(
                                        children: [
                                          const Icon(Icons.error, color: Colors.white),
                                          const SizedBox(width: 8),
                                          Expanded(child: Text(updatedFormState.error!)),
                                        ],
                                      ),
                                      backgroundColor: Colors.red,
                                      duration: const Duration(seconds: 5),
                                    ),
                                  );
                                }
                              }
                            : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.kBlueColor,
                          foregroundColor: AppColors.kWhiteColor,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: formState.isLoading
                            ? const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  Text('Submitting...'),
                                ],
                              )
                            : const Text(
                                'Submit Application',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
