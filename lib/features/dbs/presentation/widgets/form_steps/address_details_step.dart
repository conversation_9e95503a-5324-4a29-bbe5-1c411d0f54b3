import 'dart:convert';
import 'package:SolidCheck/core/config/component_config.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/dbs/data/models/dbs_form_data.dart';
import 'package:SolidCheck/features/dbs/data/services/postcode_lookup_service.dart';
import 'package:SolidCheck/features/dbs/presentation/viewmodels/dbs_form_viewmodel.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/form_fields/dbs_form_builder_fields.dart';
import 'package:SolidCheck/features/dbs/providers/form_reset_provider.dart';
import 'package:SolidCheck/shared/data/country_data.dart';
import 'package:SolidCheck/shared/utils/responsive.dart';
import 'package:SolidCheck/shared/widgets/custom_snakbar.dart';
import 'package:SolidCheck/shared/widgets/drop_down.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AddressDetailsStep extends ConsumerStatefulWidget {
  const AddressDetailsStep({super.key});

  @override
  ConsumerState<AddressDetailsStep> createState() => AddressDetailsStepState();
}

class AddressDetailsStepState extends ConsumerState<AddressDetailsStep> {
  final GlobalKey<FormBuilderState> _formKey = GlobalKey<FormBuilderState>();

  final TextEditingController _postcodeController = TextEditingController();
  final TextEditingController _selectedAddressController =
      TextEditingController();

  final TextEditingController _manualPostcodeController =
      TextEditingController();
  final TextEditingController _line1Controller = TextEditingController();
  final TextEditingController _line2Controller = TextEditingController();
  final TextEditingController _townCityController = TextEditingController();
  final TextEditingController _countryController = TextEditingController();

  final TextEditingController _residentFromMonthController =
      TextEditingController();
  final TextEditingController _residentFromYearController =
      TextEditingController();

  final TextEditingController _residentToMonthController =
      TextEditingController();
  final TextEditingController _residentToYearController =
      TextEditingController();

  // Two-stage process state
  bool _isPostcodeLookupStage = true; // Stage 1: Postcode lookup, Stage 2: Manual entry with dates
  bool _isManualEntry = false; // Whether user chose manual entry from start

  List<DropdownMenuItem<String>> _addressDropdownItems = [];
  final List<Map<String, dynamic>> _addressHistory = [];
  String? _selectedAddress;
  String? _selectedCountry;
  bool _hasCompleted5Years = false;
  bool _wantToAddMoreAddresses = false;
  bool _isLoadingAddresses = false;

  // Address data from lookup to pre-fill manual entry
  Map<String, String> _addressFromLookup = {};

  // Store the actual address objects from API response
  final Map<String, Map<String, String>> _addressLookupData = {};

  @override
  void initState() {
    super.initState();
    _selectedCountry = 'GB'; // Default to UK ISO code
    _countryController.text = 'GB';
    _loadAndAttachListeners();
  }

  Future<void> _loadAndAttachListeners() async {
    await _loadFromViewModel();

    if (_addressHistory.isEmpty) {
      await _loadFromLocalStorage();
    }

    await _loadFromSharedPreferences();

    if (_addressHistory.isEmpty) {
      _loadCurrentFormStateFromViewModel();
    }

    _attachListener(_postcodeController, 'postcode');
    _attachListener(_selectedAddressController, 'selectedAddress');
    _attachListener(_manualPostcodeController, 'manualPostcode');
    _attachListener(_line1Controller, 'line1');
    _attachListener(_line2Controller, 'line2');
    _attachListener(_townCityController, 'townCity');
    _attachListener(_countryController, 'country');
    _attachListener(_residentFromMonthController, 'residentFromMonth');
    _attachListener(_residentFromYearController, 'residentFromYear');
    _attachListener(_residentToMonthController, 'residentToMonth');
    _attachListener(_residentToYearController, 'residentToYear');

    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(milliseconds: 200), () {
        _syncFormBuilderWithControllers();
        _checkIfCompleted5Years();
      });
    });

    setState(() {});
  }

  void _syncFormBuilderWithControllers() {
    if (_formKey.currentState != null) {
      _formKey.currentState!.patchValue({
        'postcode': _postcodeController.text,
        'manualPostcode': _manualPostcodeController.text,
        'addressLine1': _line1Controller.text,
        'addressLine2': _line2Controller.text,
        'townCity': _townCityController.text,
        'residentFromMonth': _residentFromMonthController.text,
        'residentFromYear': _residentFromYearController.text,
        'residentToMonth': _residentToMonthController.text,
        'residentToYear': _residentToYearController.text,
      });
    }
  }

  Future<void> _loadFromViewModel() async {
    final formState = ref.read(dbsFormViewModelProvider);
    final applicantDetails = formState.formData.applicantDetails;

    _addressHistory.clear();

    final currentAddress = applicantDetails.currentAddress;
    if (currentAddress.addressLine1.isNotEmpty) {
      final currentAddressEntry = _convertCurrentAddressToHistoryEntry(
        currentAddress,
      );
      if (currentAddressEntry != null) {
        _addressHistory.add(currentAddressEntry);
      }
    }

    final previousAddresses = applicantDetails.previousAddresses;
    for (final prevAddress in previousAddresses) {
      final historyEntry = _convertPreviousAddressToHistoryEntry(prevAddress);
      if (historyEntry != null) {
        _addressHistory.add(historyEntry);
      }
    }

    for (int i = 0; i < _addressHistory.length; i++) {
      final addr = _addressHistory[i];
    }

    _checkIfCompleted5Years();
  }

  Map<String, dynamic>? _convertCurrentAddressToHistoryEntry(
    CurrentAddressData currentAddress,
  ) {
    if (currentAddress.addressLine1.isEmpty) return null;

    final addressParts = <String>[];
    addressParts.add(currentAddress.addressLine1);
    if (currentAddress.addressLine2.isNotEmpty) {
      addressParts.add(currentAddress.addressLine2);
    }
    if (currentAddress.addressTown.isNotEmpty) {
      addressParts.add(currentAddress.addressTown);
    }
    if (currentAddress.addressCounty.isNotEmpty) {
      addressParts.add(currentAddress.addressCounty);
    }
    if (currentAddress.postcode.isNotEmpty) {
      addressParts.add(currentAddress.postcode);
    }

    final address = addressParts.join(', ');

    String fromDate = '';
    if (currentAddress.residentFromGyearMonth.isNotEmpty) {
      final parts = currentAddress.residentFromGyearMonth.split('-');
      if (parts.length == 2) {
        fromDate = '${parts[1]}/${parts[0]}';
      }
    }

    return {
      "address": address,
      "fromDate": fromDate,
      "toDate": "Present",
      "dateRange": "$fromDate to Present",
    };
  }

  Map<String, dynamic>? _convertPreviousAddressToHistoryEntry(
    PreviousAddressData previousAddress,
  ) {
    if (previousAddress.addressLine1.isEmpty) return null;

    final addressParts = <String>[];
    addressParts.add(previousAddress.addressLine1);
    if (previousAddress.addressLine2.isNotEmpty) {
      addressParts.add(previousAddress.addressLine2);
    }
    if (previousAddress.addressTown.isNotEmpty) {
      addressParts.add(previousAddress.addressTown);
    }
    if (previousAddress.addressCounty.isNotEmpty) {
      addressParts.add(previousAddress.addressCounty);
    }
    if (previousAddress.postcode.isNotEmpty) {
      addressParts.add(previousAddress.postcode);
    }

    final address = addressParts.join(', ');

    String fromDate = '';
    String toDate = '';

    if (previousAddress.residentFromGyearMonth.isNotEmpty) {
      final parts = previousAddress.residentFromGyearMonth.split('-');
      if (parts.length == 2) {
        fromDate = '${parts[1]}/${parts[0]}';
      }
    }

    if (previousAddress.residentToGyearMonth.isNotEmpty) {
      final parts = previousAddress.residentToGyearMonth.split('-');
      if (parts.length == 2) {
        toDate = '${parts[1]}/${parts[0]}';
      }
    }

    if (toDate.isEmpty) {
      toDate = "Present";
    }

    return {
      "address": address,
      "fromDate": fromDate,
      "toDate": toDate,
      "dateRange": "$fromDate to $toDate",
    };
  }

  void _attachListener(TextEditingController controller, String key) {
    controller.addListener(() async {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(key, controller.text);

      _saveDraftToViewModel();
    });
  }

  @override
  void dispose() {
    if (mounted) {
      _saveDraftToViewModel();
    }

    _postcodeController.dispose();
    _selectedAddressController.dispose();
    _manualPostcodeController.dispose();
    _line1Controller.dispose();
    _line2Controller.dispose();
    _townCityController.dispose();
    _countryController.dispose();
    _residentFromMonthController.dispose();
    _residentFromYearController.dispose();
    _residentToMonthController.dispose();
    _residentToYearController.dispose();
    super.dispose();
  }

  Future<void> _onFindAddressPressed() async {
    // Only allow address lookup for UK addresses
    if (_selectedCountry?.toUpperCase() != 'GB') {
      CustomSnackBar.show(
        context: context,
        message: 'Address lookup is only available for UK addresses',
        backgroundColor: AppColors.kRedColor,
        textColor: Colors.white,
        icon: Icons.error,
        duration: const Duration(seconds: 3),
      );
      return;
    }

    final postcode = _postcodeController.text.trim();

    if (postcode.isEmpty) {
      CustomSnackBar.show(
        context: context,
        message: 'Please enter a postcode',
        backgroundColor: AppColors.kRedColor,
        textColor: Colors.white,
        icon: Icons.error,
        duration: const Duration(seconds: 3),
      );
      return;
    }

    try {
      setState(() {
        _isLoadingAddresses = true;
      });

      final response = await PostcodeLookupService.lookupPostcode(postcode);

      if (response.success && response.addresses.isNotEmpty) {
        // Clear previous lookup data
        _addressLookupData.clear();

        // Store address data for later use
        for (final address in response.addresses) {
          _addressLookupData[address.displayAddress] = {
            'line1': address.addressLine1,
            'line2': address.addressLine2,
            'town': address.townCity,
            'postcode': address.postcode,
            'country': 'GB',
          };
        }

        final addresses = response.addresses
            .map((address) => address.displayAddress)
            .toList();

        setState(() {
          _addressDropdownItems = [
            const DropdownMenuItem(
              value: '',
              child: Text('Please select address'),
            ),
            ...addresses.map(
              (address) => DropdownMenuItem(
                value: address,
                child: Text(address),
              ),
            ),
          ];
          _selectedAddress = '';
          _selectedAddressController.text = '';
          _isLoadingAddresses = false;
        });
      } else {
        setState(() {
          _isLoadingAddresses = false;
        });

        if (mounted) {
          CustomSnackBar.show(
            context: context,
            message: 'No addresses found for this postcode',
            backgroundColor: AppColors.kRedColor,
            textColor: Colors.white,
            icon: Icons.error,
            duration: const Duration(seconds: 3),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isLoadingAddresses = false;
      });

      if (mounted) {
        CustomSnackBar.show(
          context: context,
          message: 'Error fetching addresses: ${e.toString()}',
          backgroundColor: AppColors.kRedColor,
          textColor: Colors.white,
          icon: Icons.error,
          duration: const Duration(seconds: 3),
        );
      }
    }
  }

  void _onAddressSelected(String? selectedAddress) {
    if (selectedAddress == null || selectedAddress.isEmpty) return;

    setState(() {
      _selectedAddress = selectedAddress;
      _selectedAddressController.text = selectedAddress;
    });

    // Parse the selected address and store for pre-filling
    _parseAndStoreAddressData(selectedAddress);

    // Proceed to manual entry stage with pre-filled data
    _proceedToManualEntry();
  }

  void _parseAndStoreAddressData(String address) {
    // Use the stored lookup data instead of parsing the display string
    if (_addressLookupData.containsKey(address)) {
      _addressFromLookup = Map<String, String>.from(_addressLookupData[address]!);
    } else {
      // Fallback to parsing if lookup data is not available
      final parts = address.split(', ');
      _addressFromLookup = {
        'postcode': _postcodeController.text.trim(),
        'line1': parts.isNotEmpty ? parts[0] : '',
        'line2': '',
        'town': parts.length > 1 ? parts[1] : '',
        'country': 'GB',
      };
    }

    // Pre-fill the manual entry fields (address only, NOT resident dates)
    _line1Controller.text = _addressFromLookup['line1'] ?? '';
    _townCityController.text = _addressFromLookup['town'] ?? '';
    _countryController.text = _addressFromLookup['country'] ?? 'GB';

    // Clear resident date fields - user should fill these
    _residentFromMonthController.clear();
    _residentFromYearController.clear();
    _residentToMonthController.clear();
    _residentToYearController.clear();
  }

  void _addToAddressHistory() {
    _syncFormBuilderWithControllers();

    final formData = _formKey.currentState?.value ?? {};

    bool hasRequiredData = false;
    String missingFields = '';

    if (_isManualEntry) {
      hasRequiredData =
          _line1Controller.text.trim().isNotEmpty &&
          _residentFromMonthController.text.trim().isNotEmpty &&
          _residentFromYearController.text.trim().isNotEmpty;

      if (_line1Controller.text.trim().isEmpty) {
        missingFields += 'Address Line 1, ';
      }
      if (_residentFromMonthController.text.trim().isEmpty) {
        missingFields += 'From Month, ';
      }
      if (_residentFromYearController.text.trim().isEmpty) {
        missingFields += 'From Year, ';
      }
    } else {
      hasRequiredData =
          _selectedAddressController.text.trim().isNotEmpty &&
          _residentFromMonthController.text.trim().isNotEmpty &&
          _residentFromYearController.text.trim().isNotEmpty;

      if (_selectedAddressController.text.trim().isEmpty) {
        missingFields += 'Selected Address, ';
      }
      if (_residentFromMonthController.text.trim().isEmpty) {
        missingFields += 'From Month, ';
      }
      if (_residentFromYearController.text.trim().isEmpty) {
        missingFields += 'From Year, ';
      }
    }

    if (!hasRequiredData) {}

    if (!hasRequiredData) {
      return;
    }

    final dateRangeError = validateDateRange();
    if (dateRangeError != null) {
      return;
    }

    final overlapError = validateAddressOverlap();
    if (overlapError != null) {
      return;
    }

    String address = '';
    String fromDate = '';
    String toDate = '';

    final isFirstAddress = _addressHistory.isEmpty;

    final hasExistingPresentAddress = _addressHistory.any(
      (addr) => addr['toDate']?.toLowerCase() == 'present',
    );

    if (_isManualEntry) {
      if (_line1Controller.text.isNotEmpty &&
          _residentFromMonthController.text.isNotEmpty &&
          _residentFromYearController.text.isNotEmpty) {
        // Build address string including postcode for manual entry
        final addressParts = <String>[];
        addressParts.add(_line1Controller.text.trim());
        if (_line2Controller.text.trim().isNotEmpty) {
          addressParts.add(_line2Controller.text.trim());
        }
        if (_townCityController.text.trim().isNotEmpty) {
          addressParts.add(_townCityController.text.trim());
        }
        if (_manualPostcodeController.text.trim().isNotEmpty) {
          addressParts.add(_manualPostcodeController.text.trim());
        }
        if (_countryController.text.trim().isNotEmpty) {
          addressParts.add(_countryController.text.trim());
        }
        address = addressParts.join(', ');
        fromDate =
            "${_residentFromMonthController.text}/${_residentFromYearController.text}";

        if (isFirstAddress) {
          toDate = "Present";
        } else {
          if (hasExistingPresentAddress &&
              (_residentToMonthController.text.isEmpty ||
                  _residentToYearController.text.isEmpty)) {
            return;
          }
          toDate =
              _residentToMonthController.text.isNotEmpty &&
                  _residentToYearController.text.isNotEmpty
              ? "${_residentToMonthController.text}/${_residentToYearController.text}"
              : "Present";
        }
      }
    } else {
      if (_selectedAddressController.text.isNotEmpty &&
          _residentFromMonthController.text.isNotEmpty &&
          _residentFromYearController.text.isNotEmpty) {
        address = _selectedAddressController.text;
        fromDate =
            "${_residentFromMonthController.text}/${_residentFromYearController.text}";

        if (isFirstAddress) {
          toDate = "Present";
        } else {
          if (hasExistingPresentAddress &&
              (_residentToMonthController.text.isEmpty ||
                  _residentToYearController.text.isEmpty)) {
            return;
          }
          toDate =
              _residentToMonthController.text.isNotEmpty &&
                  _residentToYearController.text.isNotEmpty
              ? "${_residentToMonthController.text}/${_residentToYearController.text}"
              : "Present";
        }
      }
    }

    if (address.isNotEmpty && fromDate.isNotEmpty) {
      setState(() {
        _addressHistory.add({
          "address": address,
          "fromDate": fromDate,
          "toDate": toDate,
          "dateRange": "$fromDate to $toDate",
        });
      });

      WidgetsBinding.instance.addPostFrameCallback((_) {
        _clearFormFields();
        // Automatically return to postcode lookup stage for better UX
        _backToPostcodeLookup();
      });

      _checkIfCompleted5Years();

      _saveAddressesToViewModel();

      _saveToLocalStorage();

      CustomSnackBar.show(
        context: context,
        message: 'Address added successfully',
        backgroundColor: Colors.green,
        textColor: Colors.white,
        icon: Icons.check_circle,
        duration: const Duration(seconds: 2),
      );
    }
  }

  void _saveAddressesToViewModel() {
    final viewModel = ref.read(dbsFormViewModelProvider.notifier);

    if (_addressHistory.isEmpty) {
      viewModel.updateCurrentAddress(CurrentAddressData.empty());
      final formState = ref.read(dbsFormViewModelProvider);
      final currentApplicant = formState.formData.applicantDetails;
      final updatedApplicant = currentApplicant.copyWith(
        previousAddresses: <PreviousAddressData>[],
      );
      viewModel.updateApplicantDetails(updatedApplicant);
      return;
    }

    final sortedAddresses = List<Map<String, dynamic>>.from(_addressHistory);
    sortedAddresses.sort((a, b) {
      final dateA = _parseMonthYearDate(a['fromDate']);
      final dateB = _parseMonthYearDate(b['fromDate']);
      if (dateA == null || dateB == null) return 0;
      return dateB.compareTo(dateA); // Most recent first
    });

    CurrentAddressData? currentAddress;
    final previousAddresses = <PreviousAddressData>[];

    for (final addressEntry in sortedAddresses) {
      if (addressEntry['toDate']?.toLowerCase() == 'present' &&
          currentAddress == null) {
        currentAddress = _convertHistoryEntryToCurrentAddress(addressEntry);
      } else {
        final previousAddress = _convertHistoryEntryToPreviousAddress(
          addressEntry,
        );
        if (previousAddress != null) {
          previousAddresses.add(previousAddress);
        }
      }
    }

    if (currentAddress != null) {
      viewModel.updateCurrentAddress(currentAddress);
    } else {
      if (sortedAddresses.isNotEmpty) {
        final mostRecentAddress = _convertHistoryEntryToCurrentAddress(
          sortedAddresses.first,
        );
        if (mostRecentAddress != null) {
          viewModel.updateCurrentAddress(mostRecentAddress);
        }
      }
    }

    final formState = ref.read(dbsFormViewModelProvider);
    final currentApplicant = formState.formData.applicantDetails;
    final updatedApplicant = currentApplicant.copyWith(
      previousAddresses: previousAddresses,
    );
    viewModel.updateApplicantDetails(updatedApplicant);
  }

  CurrentAddressData? _convertHistoryEntryToCurrentAddress(
    Map<String, dynamic> historyEntry,
  ) {
    final address = historyEntry['address'] as String? ?? '';
    final fromDate = historyEntry['fromDate'] as String? ?? '';

    if (address.isEmpty) return null;

    final addressParts = address.split(', ');
    String addressLine1 = '';
    String addressLine2 = '';
    String addressTown = '';
    String addressCounty = '';
    String postcode = '';

    for (int i = addressParts.length - 1; i >= 0; i--) {
      final part = addressParts[i].trim();
      if (RegExp(
        r'^[A-Z]{1,2}[0-9][A-Z0-9]?\s?[0-9][A-Z]{2}$',
        caseSensitive: false,
      ).hasMatch(part)) {
        postcode = part;
        addressParts.removeAt(i);
        break;
      }
    }

    if (addressParts.isNotEmpty) {
      addressLine1 = addressParts[0];
    }
    if (addressParts.length > 1) {
      addressLine2 = addressParts[1];
    }
    if (addressParts.length > 2) {
      addressTown = addressParts[2];
    }
    if (addressParts.length > 3) {
      addressCounty = addressParts[3];
    }

    // Convert date from MM/YYYY to YYYY-MM format
    String residentFromGyearMonth = '';
    if (fromDate.isNotEmpty) {
      final dateParts = fromDate.split('/');
      if (dateParts.length == 2) {
        final month = dateParts[0].padLeft(2, '0');
        final year = dateParts[1];
        residentFromGyearMonth = '$year-$month';
      }
    }

    return CurrentAddressData(
      addressLine1: addressLine1,
      addressLine2: addressLine2,
      addressTown: addressTown,
      addressCounty: addressCounty,
      postcode: postcode,
      countryCode: _selectedCountry ?? 'GB',
      residentFromGyearMonth: residentFromGyearMonth,
    );
  }

  /// Convert address history entry to PreviousAddressData
  PreviousAddressData? _convertHistoryEntryToPreviousAddress(
    Map<String, dynamic> historyEntry,
  ) {
    final address = historyEntry['address'] as String? ?? '';
    final fromDate = historyEntry['fromDate'] as String? ?? '';
    final toDate = historyEntry['toDate'] as String? ?? '';

    if (address.isEmpty) return null;

    // Parse address components - handle different formats
    final addressParts = address.split(', ');
    String addressLine1 = '';
    String addressLine2 = '';
    String addressTown = '';
    String addressCounty = '';
    String postcode = '';

    // Extract postcode (usually last part or contains letters/numbers pattern)
    for (int i = addressParts.length - 1; i >= 0; i--) {
      final part = addressParts[i].trim();
      // Simple postcode pattern check (UK postcodes)
      if (RegExp(
        r'^[A-Z]{1,2}[0-9][A-Z0-9]?\s?[0-9][A-Z]{2}$',
        caseSensitive: false,
      ).hasMatch(part)) {
        postcode = part;
        addressParts.removeAt(i);
        break;
      }
    }

    // Assign remaining parts
    if (addressParts.isNotEmpty) {
      addressLine1 = addressParts[0];
    }
    if (addressParts.length > 1) {
      addressLine2 = addressParts[1];
    }
    if (addressParts.length > 2) {
      addressTown = addressParts[2];
    }
    if (addressParts.length > 3) {
      addressCounty = addressParts[3];
    }

    // Convert dates from MM/YYYY to YYYY-MM format
    String residentFromGyearMonth = '';
    String residentToGyearMonth = '';

    if (fromDate.isNotEmpty) {
      final dateParts = fromDate.split('/');
      if (dateParts.length == 2) {
        final month = dateParts[0].padLeft(2, '0');
        final year = dateParts[1];
        residentFromGyearMonth = '$year-$month';
      }
    }

    if (toDate.isNotEmpty && toDate.toLowerCase() != 'present') {
      final dateParts = toDate.split('/');
      if (dateParts.length == 2) {
        final month = dateParts[0].padLeft(2, '0');
        final year = dateParts[1];
        residentToGyearMonth = '$year-$month';
      }
    }

    return PreviousAddressData(
      addressLine1: addressLine1,
      addressLine2: addressLine2,
      addressTown: addressTown,
      addressCounty: addressCounty,
      postcode: postcode,
      countryCode: _selectedCountry ?? 'GB',
      residentFromGyearMonth: residentFromGyearMonth,
      residentToGyearMonth: residentToGyearMonth,
    );
  }

  void _clearFormFields() {
    // Clear controllers
    _postcodeController.clear();
    _selectedAddressController.clear();
    _manualPostcodeController.clear();
    _line1Controller.clear();
    _line2Controller.clear();
    _townCityController.clear();
    _countryController.clear();
    _residentFromMonthController.clear();
    _residentFromYearController.clear();
    _residentToMonthController.clear();
    _residentToYearController.clear();

    // Clear state variables
    _selectedAddress = null;
    _selectedCountry = 'GB'; // Reset to default (ISO code)
    _addressDropdownItems.clear();

    // Clear FormBuilder fields
    _formKey.currentState?.patchValue({
      'postcode': '',
      'manualPostcode': '',
      'addressLine1': '',
      'addressLine2': '',
      'townCity': '',
      'residentFromMonth': '',
      'residentFromYear': '',
      'residentToMonth': '',
      'residentToYear': '',
    });

    _countryController.text = 'GB';
  }

  void _saveDraftToViewModel() {
    _formKey.currentState?.save();
    final formData = _formKey.currentState?.value ?? {};

    // Get data from form builder or fallback to controllers
    final line1 = formData['addressLine1']?.toString() ?? _line1Controller.text;
    final line2 = formData['addressLine2']?.toString() ?? _line2Controller.text;
    final townCity =
        formData['townCity']?.toString() ?? _townCityController.text;
    final selectedAddress = _selectedAddressController.text;
    final manualPostcode = formData['manualPostcode']?.toString() ?? _manualPostcodeController.text;
    final postcodeFromLookup = formData['postcode']?.toString() ?? _postcodeController.text;
    final residentFromMonth =
        formData['residentFromMonth']?.toString() ??
        _residentFromMonthController.text;
    final residentFromYear =
        formData['residentFromYear']?.toString() ??
        _residentFromYearController.text;
    final residentToMonth =
        formData['residentToMonth']?.toString() ??
        _residentToMonthController.text;
    final residentToYear =
        formData['residentToYear']?.toString() ??
        _residentToYearController.text;

    // Only save if there's meaningful data in the form
    if (line1.trim().isEmpty && selectedAddress.trim().isEmpty) {
      return;
    }

    String address = '';
    String fromDate = '';
    String toDate = 'Present'; // Default to present for draft

    if (_isManualEntry && line1.trim().isNotEmpty) {
      final addressParts = <String>[];
      addressParts.add(line1.trim());
      if (line2.trim().isNotEmpty) {
        addressParts.add(line2.trim());
      }
      if (townCity.trim().isNotEmpty) {
        addressParts.add(townCity.trim());
      }
      // Include postcode in the address string for manual entry
      if (manualPostcode.trim().isNotEmpty) {
        addressParts.add(manualPostcode.trim());
      }
      if (_countryController.text.trim().isNotEmpty) {
        addressParts.add(_countryController.text.trim());
      }
      address = addressParts.join(', ');
    } else if (!_isManualEntry && selectedAddress.trim().isNotEmpty) {
      // For postcode lookup, ensure postcode is included in the address string
      address = selectedAddress.trim();
      if (postcodeFromLookup.trim().isNotEmpty && !address.contains(postcodeFromLookup.trim())) {
        address = '$address, ${postcodeFromLookup.trim()}';
      }
    } else if (!_isManualEntry && line1.trim().isNotEmpty) {
      // Handle case where user filled manual fields but used postcode lookup
      final addressParts = <String>[];
      addressParts.add(line1.trim());
      if (line2.trim().isNotEmpty) {
        addressParts.add(line2.trim());
      }
      if (townCity.trim().isNotEmpty) {
        addressParts.add(townCity.trim());
      }
      // Include postcode from lookup
      if (postcodeFromLookup.trim().isNotEmpty) {
        addressParts.add(postcodeFromLookup.trim());
      }
      if (_countryController.text.trim().isNotEmpty) {
        addressParts.add(_countryController.text.trim());
      }
      address = addressParts.join(', ');
    }

    if (residentFromMonth.trim().isNotEmpty &&
        residentFromYear.trim().isNotEmpty) {
      fromDate = "${residentFromMonth.trim()}/${residentFromYear.trim()}";
    }

    if (residentToMonth.trim().isNotEmpty && residentToYear.trim().isNotEmpty) {
      toDate = "${residentToMonth.trim()}/${residentToYear.trim()}";
    }

    if (address.isNotEmpty && fromDate.isNotEmpty) {
      final draftAddress = _convertAddressStringToCurrentAddress(
        address,
        fromDate,
      );
      if (draftAddress != null) {
        final viewModel = ref.read(dbsFormViewModelProvider.notifier);
        viewModel.updateCurrentAddress(draftAddress);
      }
    }
  }

  CurrentAddressData? _convertAddressStringToCurrentAddress(
    String address,
    String fromDate,
  ) {
    if (address.isEmpty) return null;

    final addressParts = address.split(', ');
    String addressLine1 = '';
    String addressLine2 = '';
    String addressTown = '';
    String addressCounty = '';
    String postcode = '';

    // Extract postcode
    for (int i = addressParts.length - 1; i >= 0; i--) {
      final part = addressParts[i].trim();
      if (RegExp(
        r'^[A-Z]{1,2}[0-9][A-Z0-9]?\s?[0-9][A-Z]{2}$',
        caseSensitive: false,
      ).hasMatch(part)) {
        postcode = part;
        addressParts.removeAt(i);
        break;
      }
    }

    // Assign remaining parts
    if (addressParts.isNotEmpty) addressLine1 = addressParts[0];
    if (addressParts.length > 1) addressLine2 = addressParts[1];
    if (addressParts.length > 2) addressTown = addressParts[2];
    if (addressParts.length > 3) addressCounty = addressParts[3];

    // Convert date
    String residentFromGyearMonth = '';
    if (fromDate.isNotEmpty) {
      final dateParts = fromDate.split('/');
      if (dateParts.length == 2) {
        final month = dateParts[0].padLeft(2, '0');
        final year = dateParts[1];
        residentFromGyearMonth = '$year-$month';
      }
    }

    return CurrentAddressData(
      addressLine1: addressLine1,
      addressLine2: addressLine2,
      addressTown: addressTown,
      addressCounty: addressCounty,
      postcode: postcode,
      countryCode: _selectedCountry ?? 'GB',
      residentFromGyearMonth: residentFromGyearMonth,
    );
  }

  /// Load form fields from SharedPreferences
  Future<void> _loadFromSharedPreferences() async {
    final prefs = await SharedPreferences.getInstance();

    // Load into controllers
    if (_postcodeController.text.isEmpty) {
      _postcodeController.text = prefs.getString('postcode') ?? '';
    }
    if (_selectedAddressController.text.isEmpty) {
      _selectedAddressController.text =
          prefs.getString('selectedAddress') ?? '';
    }
    if (_manualPostcodeController.text.isEmpty) {
      _manualPostcodeController.text = prefs.getString('manualPostcode') ?? '';
    }
    if (_line1Controller.text.isEmpty) {
      _line1Controller.text = prefs.getString('line1') ?? '';
    }
    if (_line2Controller.text.isEmpty) {
      _line2Controller.text = prefs.getString('line2') ?? '';
    }
    if (_townCityController.text.isEmpty) {
      _townCityController.text = prefs.getString('townCity') ?? '';
    }
    if (_countryController.text.isEmpty) {
      _countryController.text = prefs.getString('country') ?? 'GB';
    }
    if (_residentFromMonthController.text.isEmpty) {
      _residentFromMonthController.text =
          prefs.getString('residentFromMonth') ?? '';
    }
    if (_residentFromYearController.text.isEmpty) {
      _residentFromYearController.text =
          prefs.getString('residentFromYear') ?? '';
    }
    if (_residentToMonthController.text.isEmpty) {
      _residentToMonthController.text =
          prefs.getString('residentToMonth') ?? '';
    }
    if (_residentToYearController.text.isEmpty) {
      _residentToYearController.text = prefs.getString('residentToYear') ?? '';
    }

    // Also update FormBuilder fields
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _formKey.currentState?.patchValue({
        'postcode': _postcodeController.text,
        'manualPostcode': _manualPostcodeController.text,
        'addressLine1': _line1Controller.text,
        'addressLine2': _line2Controller.text,
        'townCity': _townCityController.text,
        'residentFromMonth': _residentFromMonthController.text,
        'residentFromYear': _residentFromYearController.text,
        'residentToMonth': _residentToMonthController.text,
        'residentToYear': _residentToYearController.text,
      });
    });
  }

  /// Load current form state from view model if user is in the middle of entering an address
  void _loadCurrentFormStateFromViewModel() {
    final formState = ref.read(dbsFormViewModelProvider);
    final currentAddress = formState.formData.applicantDetails.currentAddress;

    // Only load if there's data and no address history (to avoid overwriting user input)
    if (currentAddress.addressLine1.isNotEmpty && _addressHistory.isEmpty) {
      // Update controllers
      _line1Controller.text = currentAddress.addressLine1;
      _line2Controller.text = currentAddress.addressLine2;
      _townCityController.text = currentAddress.addressTown;
      _selectedCountry = currentAddress.countryCode.isNotEmpty
          ? currentAddress.countryCode
          : 'GB';
      _countryController.text = _selectedCountry!;
      _manualPostcodeController.text = currentAddress.postcode;

      // Convert date from YYYY-MM to MM/YYYY
      String residentFromMonth = '';
      String residentFromYear = '';
      if (currentAddress.residentFromGyearMonth.isNotEmpty) {
        final parts = currentAddress.residentFromGyearMonth.split('-');
        if (parts.length == 2) {
          residentFromMonth = parts[1];
          residentFromYear = parts[0];
          _residentFromMonthController.text = residentFromMonth;
          _residentFromYearController.text = residentFromYear;
        }
      }

      // Update FormBuilder fields
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _formKey.currentState?.patchValue({
          'addressLine1': currentAddress.addressLine1,
          'addressLine2': currentAddress.addressLine2,
          'townCity': currentAddress.addressTown,
          'manualPostcode': currentAddress.postcode,
          'postcode': currentAddress.postcode,
          'residentFromMonth': residentFromMonth,
          'residentFromYear': residentFromYear,
        });
      });

      _isManualEntry = true;
    }
  }

  Future<void> _saveToLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final addressHistoryJson = _addressHistory
          .map(
            (address) => {
              'address': address['address'],
              'fromDate': address['fromDate'],
              'toDate': address['toDate'],
              'dateRange': address['dateRange'],
            },
          )
          .toList();

      await prefs.setString('address_history', jsonEncode(addressHistoryJson));
    } catch (e) {}
  }

  Future<void> _loadFromLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final addressHistoryJson = prefs.getString('address_history');

      if (addressHistoryJson != null) {
        final List<dynamic> addressList = jsonDecode(addressHistoryJson);
        _addressHistory.clear();

        for (final addressData in addressList) {
          if (addressData is Map<String, dynamic>) {
            _addressHistory.add({
              'address': addressData['address'] ?? '',
              'fromDate': addressData['fromDate'] ?? '',
              'toDate': addressData['toDate'] ?? '',
              'dateRange': addressData['dateRange'] ?? '',
            });
          }
        }

        _saveAddressesToViewModel();

        _checkIfCompleted5Years();
      }
    } catch (e) {}
  }

  void _checkIfCompleted5Years() {
    final previousStatus = _hasCompleted5Years;

    if (_addressHistory.isEmpty) {
      setState(() {
        _hasCompleted5Years = false;
        _wantToAddMoreAddresses = false;
      });
      _notifyViewModelOfAddressCompletion(false);
      return;
    }

    final now = DateTime.now();
    final fiveYearsAgo = DateTime(now.year - 5, now.month, 1);

    for (int i = 0; i < _addressHistory.length; i++) {
      final addr = _addressHistory[i];
    }

    final sortedAddresses = List<Map<String, dynamic>>.from(_addressHistory);
    sortedAddresses.sort((a, b) {
      final dateA = _parseMonthYearDate(a['fromDate']);
      final dateB = _parseMonthYearDate(b['fromDate']);
      if (dateA == null || dateB == null) return 0;
      return dateA.compareTo(dateB);
    });

    for (int i = 0; i < sortedAddresses.length; i++) {
      final addr = sortedAddresses[i];
      final fromDate = _parseMonthYearDate(addr['fromDate']);
      final toDate = _parseMonthYearDate(addr['toDate']);
    }

    bool hasCompleteHistory = _checkContinuousCoverage(
      sortedAddresses,
      fiveYearsAgo,
      now,
    );

    setState(() {
      _hasCompleted5Years = hasCompleteHistory;
      if (hasCompleteHistory && !previousStatus) {
        _wantToAddMoreAddresses = false;
      }
    });

    // Removed annoying snack toast message for 5-year completion

    _notifyViewModelOfAddressCompletion(hasCompleteHistory);
  }

  void _notifyViewModelOfAddressCompletion(bool isComplete) {
    if (mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          final viewModel = ref.read(dbsFormViewModelProvider.notifier);

          viewModel.forceStateUpdate();
        }
      });
    }
  }

  DateTime? _parseMonthYearDate(String? dateStr) {
    if (dateStr == null || dateStr.trim().isEmpty) return null;

    final trimmed = dateStr.trim();
    if (trimmed.toLowerCase() == 'present') {
      return DateTime.now();
    }

    try {
      if (trimmed.contains('/')) {
        final parts = trimmed.split('/');
        if (parts.length == 2) {
          final month = int.tryParse(parts[0]);
          final year = int.tryParse(parts[1]);
          if (month != null && year != null && month >= 1 && month <= 12) {
            return DateTime(year, month, 1);
          }
        }
      }

      if (trimmed.contains('-')) {
        final parts = trimmed.split('-');
        if (parts.length == 2) {
          final year = int.tryParse(parts[0]);
          final month = int.tryParse(parts[1]);
          if (month != null && year != null && month >= 1 && month <= 12) {
            return DateTime(year, month, 1);
          }
        }
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  bool _checkContinuousCoverage(
    List<Map<String, dynamic>> sortedAddresses,
    DateTime fiveYearsAgo,
    DateTime now,
  ) {
    if (sortedAddresses.isEmpty) {
      return false;
    }

    final earliestFromDate = _parseMonthYearDate(
      sortedAddresses.first['fromDate'],
    );

    if (earliestFromDate == null || earliestFromDate.isAfter(fiveYearsAgo)) {
      return false;
    }

    final latestAddress = sortedAddresses.last;
    final latestToDate = _parseMonthYearDate(latestAddress['toDate']);

    if (latestToDate == null) {
      return false;
    }

    if (latestAddress['toDate']?.toLowerCase() != 'present') {
      final currentMonth = DateTime(now.year, now.month, 1);
      if (latestToDate.isBefore(currentMonth)) {
        return false;
      }
    }

    for (int i = 0; i < sortedAddresses.length - 1; i++) {
      final currentToDate = _parseMonthYearDate(sortedAddresses[i]['toDate']);
      final nextFromDate = _parseMonthYearDate(
        sortedAddresses[i + 1]['fromDate'],
      );

      if (currentToDate == null || nextFromDate == null) continue;

      final currentEndMonth = currentToDate.year * 12 + currentToDate.month;
      final nextStartMonth = nextFromDate.year * 12 + nextFromDate.month;

      if (nextStartMonth - currentEndMonth > 3) {
        return false;
      } else if (nextStartMonth - currentEndMonth > 1) {}
    }

    return true;
  }

  void _toggleAddressEntry() {
    setState(() {
      _isManualEntry = !_isManualEntry;
      if (_isManualEntry) {
        // If switching to manual entry, go directly to stage 2
        _isPostcodeLookupStage = false;
      } else {
        // If switching back to lookup, go to stage 1
        _isPostcodeLookupStage = true;
      }
    });
  }

  void _proceedToManualEntry() {
    setState(() {
      _isPostcodeLookupStage = false;
    });
  }

  void _backToPostcodeLookup() {
    setState(() {
      _isPostcodeLookupStage = true;
      _isManualEntry = false;
      // Clear manual entry fields but keep postcode lookup data
      _line1Controller.clear();
      _line2Controller.clear();
      _townCityController.clear();
      // Always clear resident date fields - user should fill these fresh
      _residentFromMonthController.clear();
      _residentFromYearController.clear();
      _residentToMonthController.clear();
      _residentToYearController.clear();
      // Clear the stored address data
      _addressFromLookup.clear();
    });
  }

  bool validateForm() {
    // For the new two-stage process, validation depends on the current stage
    if (_isPostcodeLookupStage) {
      // Stage 1: Only validate if user has completed the lookup and selection
      return _addressDropdownItems.isNotEmpty && _selectedAddress != null;
    } else {
      // Stage 2: Validate the manual entry form
      _ensureCurrentAddressIsSaved();
      return _formKey.currentState?.saveAndValidate() ?? false;
    }
  }

  void _ensureCurrentAddressIsSaved() {
    // Force save current address data to form model before validation
    _formKey.currentState?.save();
    final formData = _formKey.currentState?.value ?? {};

    final postcodeFromLookup = formData['postcode']?.toString() ?? _postcodeController.text;
    final manualPostcode = formData['manualPostcode']?.toString() ?? _manualPostcodeController.text;
    final selectedAddress = _selectedAddressController.text;
    final line1 = formData['addressLine1']?.toString() ?? _line1Controller.text;
    final line2 = formData['addressLine2']?.toString() ?? _line2Controller.text;
    final townCity = formData['townCity']?.toString() ?? _townCityController.text;
    final residentFromMonth = formData['residentFromMonth']?.toString() ?? _residentFromMonthController.text;
    final residentFromYear = formData['residentFromYear']?.toString() ?? _residentFromYearController.text;

    // Check if we have address data that needs to be saved
    final hasPostcodeLookupData = postcodeFromLookup.trim().isNotEmpty && selectedAddress.trim().isNotEmpty;
    final hasManualData = line1.trim().isNotEmpty && manualPostcode.trim().isNotEmpty;
    final hasDateData = residentFromMonth.trim().isNotEmpty && residentFromYear.trim().isNotEmpty;

    if ((hasPostcodeLookupData || hasManualData) && hasDateData) {
      // Create current address data directly
      String postcode = '';
      String addressLine1 = '';
      String addressLine2 = '';
      String addressTown = '';
      String addressCounty = '';

      if (_isManualEntry) {
        // Manual entry
        postcode = manualPostcode.trim();
        addressLine1 = line1.trim();
        addressLine2 = line2.trim();
        addressTown = townCity.trim();
      } else {
        // Postcode lookup
        postcode = postcodeFromLookup.trim();
        if (selectedAddress.trim().isNotEmpty) {
          // Parse selected address
          final addressParts = selectedAddress.split(', ');
          if (addressParts.isNotEmpty) addressLine1 = addressParts[0];
          if (addressParts.length > 1) addressLine2 = addressParts[1];
          if (addressParts.length > 2) addressTown = addressParts[2];
          if (addressParts.length > 3) addressCounty = addressParts[3];
        } else {
          // Use manual fields if available
          addressLine1 = line1.trim();
          addressLine2 = line2.trim();
          addressTown = townCity.trim();
        }
      }

      // Create resident from date
      final month = residentFromMonth.trim().padLeft(2, '0');
      final year = residentFromYear.trim();
      final residentFromGyearMonth = '$year-$month';

      // Create and save current address
      final currentAddress = CurrentAddressData(
        addressLine1: addressLine1,
        addressLine2: addressLine2,
        addressTown: addressTown,
        addressCounty: addressCounty,
        postcode: postcode,
        countryCode: _selectedCountry ?? 'GB',
        residentFromGyearMonth: residentFromGyearMonth,
      );

      final viewModel = ref.read(dbsFormViewModelProvider.notifier);
      viewModel.updateCurrentAddress(currentAddress);
    }
  }

  Map<String, String> getValidationErrors() {
    final errors = <String, String>{};

    if (!_hasCompleted5Years) {
      final validationIssues = _getAddressHistoryValidationIssues();

      if (validationIssues.isNotEmpty) {
        errors['validation'] = validationIssues.join(' ');
      } else {
        errors['validation'] =
            'Please complete your 5-year address history before continuing to the next step.';
      }
    } else {
      _formKey.currentState?.save();
      final isValid = _formKey.currentState?.validate() ?? true;

      if (!isValid) {
        errors['validation'] =
            'Please fix all validation issues before continuing to the next step.';
      }
    }

    return errors;
  }

  List<String> _getAddressHistoryValidationIssues() {
    final issues = <String>[];

    if (_addressHistory.isEmpty) {
      issues.add('Please add at least one address to your history.');
      return issues;
    }

    final gapIssues = _checkForAddressGaps();
    if (gapIssues.isNotEmpty) {
      issues.addAll(gapIssues);
    }

    final overlapIssues = _checkForAddressOverlaps();
    if (overlapIssues.isNotEmpty) {
      issues.addAll(overlapIssues);
    }

    if (!_checkHistoryGoesBackFarEnough()) {
      issues.add(
        'Your address history must go back at least 5 years from today.',
      );
    }

    if (!_checkHistoryCoversPresent()) {
      issues.add(
        'Your address history must include your current address up to present.',
      );
    }

    return issues;
  }

  List<String> _checkForAddressGaps() {
    final issues = <String>[];

    if (_addressHistory.length < 2) return issues;

    final sortedAddresses = List<Map<String, dynamic>>.from(_addressHistory);
    sortedAddresses.sort((a, b) {
      final dateA = _parseMonthYearDate(a['fromDate']);
      final dateB = _parseMonthYearDate(b['fromDate']);
      if (dateA == null || dateB == null) return 0;
      return dateA.compareTo(dateB);
    });

    for (int i = 0; i < sortedAddresses.length - 1; i++) {
      final currentToDate = _parseMonthYearDate(sortedAddresses[i]['toDate']);
      final nextFromDate = _parseMonthYearDate(
        sortedAddresses[i + 1]['fromDate'],
      );

      if (currentToDate == null || nextFromDate == null) continue;

      final currentEndMonth = currentToDate.year * 12 + currentToDate.month;
      final nextStartMonth = nextFromDate.year * 12 + nextFromDate.month;
      final gapMonths = nextStartMonth - currentEndMonth;

      if (gapMonths > 3) {
        issues.add(
          'There is a $gapMonths-month gap between ${sortedAddresses[i]['toDate']} and ${sortedAddresses[i + 1]['fromDate']}.',
        );
      }
    }

    return issues;
  }

  List<String> _checkForAddressOverlaps() {
    final issues = <String>[];

    for (int i = 0; i < _addressHistory.length; i++) {
      for (int j = i + 1; j < _addressHistory.length; j++) {
        final addr1 = _addressHistory[i];
        final addr2 = _addressHistory[j];

        final date1From = _parseMonthYearDate(addr1['fromDate']);
        final date1To = _parseMonthYearDate(addr1['toDate']);
        final date2From = _parseMonthYearDate(addr2['fromDate']);
        final date2To = _parseMonthYearDate(addr2['toDate']);

        if (date1From != null &&
            date1To != null &&
            date2From != null &&
            date2To != null) {
          if (_datesOverlap(date1From, date1To, date2From, date2To)) {
            issues.add(
              'Address "${addr1['address']}" (${addr1['dateRange']}) overlaps with "${addr2['address']}" (${addr2['dateRange']}).',
            );
          }
        }
      }
    }

    return issues;
  }

  bool _checkHistoryGoesBackFarEnough() {
    if (_addressHistory.isEmpty) return false;

    final now = DateTime.now();
    final fiveYearsAgo = DateTime(now.year - 5, now.month, 1);

    final sortedAddresses = List<Map<String, dynamic>>.from(_addressHistory);
    sortedAddresses.sort((a, b) {
      final dateA = _parseMonthYearDate(a['fromDate']);
      final dateB = _parseMonthYearDate(b['fromDate']);
      if (dateA == null || dateB == null) return 0;
      return dateA.compareTo(dateB);
    });

    final earliestFromDate = _parseMonthYearDate(
      sortedAddresses.first['fromDate'],
    );
    return earliestFromDate != null && !earliestFromDate.isAfter(fiveYearsAgo);
  }

  bool _checkHistoryCoversPresent() {
    if (_addressHistory.isEmpty) return false;

    final sortedAddresses = List<Map<String, dynamic>>.from(_addressHistory);
    sortedAddresses.sort((a, b) {
      final dateA = _parseMonthYearDate(a['toDate']);
      final dateB = _parseMonthYearDate(b['toDate']);
      if (dateA == null || dateB == null) return 0;
      return dateB.compareTo(dateA);
    });

    final latestAddress = sortedAddresses.first;
    return latestAddress['toDate']?.toLowerCase() == 'present';
  }

  Map<String, dynamic> _getInitialFormValues() {
    final formState = ref.read(dbsFormViewModelProvider);
    final currentAddress = formState.formData.applicantDetails.currentAddress;

    return {
      'postcode': _postcodeController.text.isNotEmpty
          ? _postcodeController.text
          : currentAddress.postcode,
      'manualPostcode': _manualPostcodeController.text.isNotEmpty
          ? _manualPostcodeController.text
          : currentAddress.postcode,
      'addressLine1': _line1Controller.text.isNotEmpty
          ? _line1Controller.text
          : currentAddress.addressLine1,
      'addressLine2': _line2Controller.text.isNotEmpty
          ? _line2Controller.text
          : currentAddress.addressLine2,
      'townCity': _townCityController.text.isNotEmpty
          ? _townCityController.text
          : currentAddress.addressTown,
      // For the new two-stage process, resident dates should always be empty initially
      // User will fill these in the manual entry stage
      'residentFromMonth': '',
      'residentFromYear': '',
      'residentToMonth': '',
      'residentToYear': '',
    };
  }

  // Validation functions
  String? validatePostcode(String? value) {
    // Only require postcode for UK addresses
    if (_selectedCountry?.toUpperCase() == 'GB') {
      if (value == null || value.trim().isEmpty) {
        return 'Postcode is required for UK addresses';
      }

      // UK postcode regex pattern
      final postcodeRegex = RegExp(
        r'^[A-Z]{1,2}[0-9R][0-9A-Z]? [0-9][ABD-HJLNP-UW-Z]{2}$',
        caseSensitive: false,
      );

      if (!postcodeRegex.hasMatch(value.trim().toUpperCase())) {
        return 'Please enter a valid UK postcode (e.g., SW1A 1AA)';
      }
    }

    return null;
  }

  String? validateMonth(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Month is required';
    }

    final month = int.tryParse(value.trim());
    if (month == null || month < 1 || month > 12) {
      return 'Enter valid month (1-12)';
    }

    return null;
  }

  String? validateYear(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Year is required';
    }

    final year = int.tryParse(value.trim());
    final currentYear = DateTime.now().year;

    if (year == null || year < 1900 || year > currentYear) {
      return 'Enter valid year (1900-$currentYear)';
    }

    return null;
  }

  String? validateOptionalMonth(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Optional field
    }

    final month = int.tryParse(value.trim());
    if (month == null || month < 1 || month > 12) {
      return 'Enter valid month (1-12)';
    }

    return null;
  }

  String? validateOptionalYear(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Optional field
    }

    final year = int.tryParse(value.trim());
    final currentYear = DateTime.now().year;

    if (year == null || year < 1900 || year > currentYear) {
      return 'Enter valid year (1900-$currentYear)';
    }

    return null;
  }

  DateTime? _parseDate(String month, String year) {
    final monthInt = int.tryParse(month.trim());
    final yearInt = int.tryParse(year.trim());

    if (monthInt == null || yearInt == null) return null;
    if (monthInt < 1 || monthInt > 12) return null;

    return DateTime(yearInt, monthInt);
  }

  // Month/Year picker methods for resident from and to dates
  Future<void> _showResidentFromDatePicker() async {
    final selectedDate = await _showMonthYearPicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );

    if (selectedDate != null) {
      setState(() {
        _residentFromMonthController.text = selectedDate.month
            .toString()
            .padLeft(2, '0');
        _residentFromYearController.text = selectedDate.year.toString();
      });
    }
  }

  Future<void> _showResidentToDatePicker() async {
    final selectedDate = await _showMonthYearPicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );

    if (selectedDate != null) {
      setState(() {
        _residentToMonthController.text = selectedDate.month.toString().padLeft(
          2,
          '0',
        );
        _residentToYearController.text = selectedDate.year.toString();
      });
    }
  }

  // Custom month/year picker dialog
  Future<DateTime?> _showMonthYearPicker({
    required BuildContext context,
    required DateTime initialDate,
    required DateTime firstDate,
    required DateTime lastDate,
  }) async {
    int selectedMonth = initialDate.month;
    int selectedYear = initialDate.year;

    return showDialog<DateTime>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Select Month and Year'),
              content: SizedBox(
                width: 300,
                height: 200,
                child: Column(
                  children: [
                    // Year selection
                    Row(
                      children: [
                        const Text('Year: '),
                        const SizedBox(width: 10),
                        Expanded(
                          child: DropdownButton<int>(
                            value: selectedYear,
                            isExpanded: true,
                            items: List.generate(
                              lastDate.year - firstDate.year + 1,
                              (index) {
                                final year = firstDate.year + index;
                                return DropdownMenuItem(
                                  value: year,
                                  child: Text(year.toString()),
                                );
                              },
                            ),
                            onChanged: (int? newYear) {
                              if (newYear != null) {
                                setState(() {
                                  selectedYear = newYear;
                                });
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    // Month selection
                    Row(
                      children: [
                        const Text('Month: '),
                        const SizedBox(width: 10),
                        Expanded(
                          child: DropdownButton<int>(
                            value: selectedMonth,
                            isExpanded: true,
                            items: List.generate(12, (index) {
                              final month = index + 1;
                              final monthNames = [
                                'January',
                                'February',
                                'March',
                                'April',
                                'May',
                                'June',
                                'July',
                                'August',
                                'September',
                                'October',
                                'November',
                                'December',
                              ];
                              return DropdownMenuItem(
                                value: month,
                                child: Text(
                                  '${month.toString().padLeft(2, '0')} - ${monthNames[index]}',
                                ),
                              );
                            }),
                            onChanged: (int? newMonth) {
                              if (newMonth != null) {
                                setState(() {
                                  selectedMonth = newMonth;
                                });
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ComponentConfig.textButtonStyle,
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(
                      context,
                    ).pop(DateTime(selectedYear, selectedMonth));
                  },
                  style: ComponentConfig.primaryButtonStyle,
                  child: const Text('OK'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  String? validateDateRange() {
    final fromMonth = _residentFromMonthController.text.trim();
    final fromYear = _residentFromYearController.text.trim();
    final toMonth = _residentToMonthController.text.trim();
    final toYear = _residentToYearController.text.trim();

    if (fromMonth.isEmpty || fromYear.isEmpty) {
      return 'From date is required';
    }

    final fromDate = _parseDate(fromMonth, fromYear);
    if (fromDate == null) {
      return 'Invalid from date';
    }

    // If to date is provided, validate it
    if (toMonth.isNotEmpty && toYear.isNotEmpty) {
      final toDate = _parseDate(toMonth, toYear);
      if (toDate == null) {
        return 'Invalid to date';
      }

      if (toDate.isBefore(fromDate)) {
        return 'To date cannot be before from date';
      }

      if (toDate.isAfter(DateTime.now())) {
        return 'To date cannot be in the future';
      }
    }

    return null;
  }

  String? validateAddressOverlap() {
    final fromMonth = _residentFromMonthController.text.trim();
    final fromYear = _residentFromYearController.text.trim();
    final toMonth = _residentToMonthController.text.trim();
    final toYear = _residentToYearController.text.trim();

    if (fromMonth.isEmpty || fromYear.isEmpty) {
      return null; // Will be caught by date validation
    }

    final newFromDate = _parseDate(fromMonth, fromYear);
    if (newFromDate == null) return null;

    DateTime? newToDate;
    if (toMonth.isNotEmpty && toYear.isNotEmpty) {
      newToDate = _parseDate(toMonth, toYear);
    } else {
      newToDate = DateTime.now(); // Present
    }

    if (newToDate == null) return null;

    // Check for overlaps with existing addresses
    for (final address in _addressHistory) {
      final existingFromDate = _parseDateFromString(address['fromDate']);
      final existingToDate = _parseDateFromString(address['toDate']);

      if (existingFromDate == null || existingToDate == null) continue;

      // Check if dates overlap
      if (_datesOverlap(
        newFromDate,
        newToDate,
        existingFromDate,
        existingToDate,
      )) {
        return 'Address dates overlap with existing address (${address['dateRange']})';
      }
    }

    return null;
  }

  DateTime? _parseDateFromString(String? dateStr) {
    if (dateStr == null || dateStr.trim().isEmpty) return null;

    if (dateStr.toLowerCase() == 'present') {
      return DateTime.now();
    }

    final parts = dateStr.split('/');
    if (parts.length == 2) {
      final month = int.tryParse(parts[0]);
      final year = int.tryParse(parts[1]);
      if (month != null && year != null) {
        return DateTime(year, month);
      }
    }

    return null;
  }

  bool _datesOverlap(
    DateTime start1,
    DateTime end1,
    DateTime start2,
    DateTime end2,
  ) {
    return start1.isBefore(end2) && end1.isAfter(start2);
  }

  List<Widget> _buildAddressGapWarnings() {
    if (_addressHistory.length < 2) return [];

    final warnings = <Widget>[];

    // Sort addresses by from date (earliest first)
    final sortedAddresses = List<Map<String, dynamic>>.from(_addressHistory);
    sortedAddresses.sort((a, b) {
      final dateA = _parseMonthYearDate(a['fromDate']);
      final dateB = _parseMonthYearDate(b['fromDate']);
      if (dateA == null || dateB == null) return 0;
      return dateA.compareTo(dateB);
    });

    // Check for gaps between consecutive addresses
    for (int i = 0; i < sortedAddresses.length - 1; i++) {
      final currentToDate = _parseMonthYearDate(sortedAddresses[i]['toDate']);
      final nextFromDate = _parseMonthYearDate(
        sortedAddresses[i + 1]['fromDate'],
      );

      if (currentToDate == null || nextFromDate == null) continue;

      // Calculate month difference
      final currentEndMonth = currentToDate.year * 12 + currentToDate.month;
      final nextStartMonth = nextFromDate.year * 12 + nextFromDate.month;
      final gapMonths = nextStartMonth - currentEndMonth;

      if (gapMonths > 3) {
        // Significant gap - show error
        warnings.add(
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.only(bottom: 8),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.red.shade200),
            ),
            child: Row(
              children: [
                Icon(Icons.error, color: Colors.red.shade600, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Gap of $gapMonths months between ${sortedAddresses[i]['toDate']} and ${sortedAddresses[i + 1]['fromDate']}',
                    style: TextStyle(
                      color: Colors.red.shade700,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      } else if (gapMonths > 1) {
        // Small gap - show warning
        warnings.add(
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.only(bottom: 8),
            decoration: BoxDecoration(
              color: Colors.orange.shade50,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.orange.shade200),
            ),
            child: Row(
              children: [
                Icon(Icons.warning, color: Colors.orange.shade600, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Small gap of $gapMonths months between ${sortedAddresses[i]['toDate']} and ${sortedAddresses[i + 1]['fromDate']}',
                    style: TextStyle(
                      color: Colors.orange.shade700,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      }
    }

    return warnings;
  }

  @override
  Widget build(BuildContext context) {
    ref.listen<int>(formResetTriggerProvider, (previous, next) {
      if (next > 0 && previous != next) {
        resetFormData();
      }
    });

    final responsiveBreakPoint =
        ResponsiveUtil.isMobile(context) ||
        ResponsiveUtil.isMobileLarge(context);

    return FormBuilder(
      key: _formKey,
      initialValue: _getInitialFormValues(),
      onChanged: () {
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) {
            _saveDraftToViewModel();
          }
        });
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section Title
            Text(
              'Address details',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.kBlueColor,
              ),
            ),
            const SizedBox(height: 20.0),

            // Required fields notice
            const Center(
              child: Text(
                'Fields marked in orange are required',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
            ),
            const SizedBox(height: 20.0),

            if (responsiveBreakPoint) ...[
              _buildMobileLayout(),
            ] else ...[
              _buildDesktopLayout(),
            ],

            const SizedBox(height: 40.0),
          ],
        ),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return Column(
      children: [
        // Show address input fields only if 5 years not completed OR user wants to add more
        if (!_hasCompleted5Years || _wantToAddMoreAddresses) ...[
          if (_isPostcodeLookupStage) ...[
            _buildSimplePostcodeLookupStage(),
          ] else ...[
            _buildManualEntryStage(),
          ],
        ] else ...[
          // Show "Do you want to add more addresses?" question
          _buildAddMoreAddressesQuestion(),
        ],
        const SizedBox(height: 20),
        _buildAddressHistorySection(),
      ],
    );
  }

  Widget _buildDesktopLayout() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left side - Add Previous Address (conditionally shown)
        Expanded(
          flex: 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Show address input fields only if 5 years not completed OR user wants to add more
              if (!_hasCompleted5Years || _wantToAddMoreAddresses) ...[
                const Text(
                  'Add Previous Address:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 16),

                if (_isPostcodeLookupStage) ...[
                  _buildSimplePostcodeLookupStage(),
                ] else ...[
                  _buildManualEntryStage(),
                ],
              ] else ...[
                // Show "Do you want to add more addresses?" question
                _buildAddMoreAddressesQuestion(),
              ],
            ],
          ),
        ),

        const SizedBox(width: 40),

        // Right side - Address History
        Expanded(
          flex: 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Address history:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 16),
              _buildAddressHistorySection(),
            ],
          ),
        ),
      ],
    );
  }

  // Stage 1: Simple postcode lookup with address selection
  Widget _buildSimplePostcodeLookupStage() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Postcode field
        DBSFormBuilderTextField(
          name: 'postcode',
          label: 'Postcode',
          isRequired: true,
          maxLength: 10,
          validators: [
            FormBuilderValidators.required(errorText: 'Postcode is required'),
            (value) => validatePostcode(value),
          ],
          onChanged: (value) {
            _postcodeController.text = value ?? '';
          },
        ),
        const SizedBox(height: 16),

        // Find Address button
        SizedBox(
          width: double.infinity,
          height: 48,
          child: ElevatedButton(
            onPressed: _onFindAddressPressed,
            style: ComponentConfig.primaryButtonStyle,
            child: const Text('Find Address'),
          ),
        ),
        const SizedBox(height: 16),

        // Loading indicator or address dropdown
        if (_isLoadingAddresses) ...[
          const SizedBox(height: 16),
          const Center(
            child: CircularProgressIndicator(),
          ),
          const SizedBox(height: 8),
          const Center(
            child: Text('Loading addresses...'),
          ),
          const SizedBox(height: 16),
        ] else if (_addressDropdownItems.isNotEmpty) ...[
          CustomDropdownButton2(
            isRquired: true,
            title: 'Select Address',
            hint: 'Choose your address',
            dropdownItems: _addressDropdownItems,
            value: _selectedAddress,
            selectedValue: _selectedAddress,
            onChanged: _onAddressSelected,
          ),
          const SizedBox(height: 16),
        ],

        // Enter address manually button (grey style)
        SizedBox(
          width: double.infinity,
          height: 48,
          child: OutlinedButton(
            onPressed: () {
              setState(() {
                _isManualEntry = true;
                _isPostcodeLookupStage = false;
              });
            },
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.grey.shade600,
              side: BorderSide(color: Colors.grey.shade400, width: 1),
              backgroundColor: Colors.grey.shade50,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'Enter Address Manually',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPostcodeLookupSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Postcode field with FormBuilder (conditional validation)
        DBSFormBuilderTextField(
          key: const Key('postcode_lookup_field'),
          name: 'postcode',
          label: 'Postcode',
          isRequired: _selectedCountry?.toUpperCase() == 'GB',
          readOnly: false, // Explicitly enable the field
          maxLength: 10,
          validators: [
            if (_selectedCountry?.toUpperCase() == 'GB')
              FormBuilderValidators.required(errorText: 'Postcode is required for UK addresses'),
            (value) => validatePostcode(value),
          ],
          onChanged: (value) {
            _postcodeController.text = value ?? '';
          },
        ),
        const SizedBox(height: 16),

        // Find Address button (only for UK addresses)
        SizedBox(
          width: double.infinity,
          height: 48,
          child: ElevatedButton(
            onPressed: _selectedCountry?.toUpperCase() == 'GB' ? _onFindAddressPressed : null,
            style: _selectedCountry?.toUpperCase() == 'GB'
                ? ComponentConfig.primaryButtonStyle
                : ComponentConfig.primaryButtonStyle.copyWith(
                    backgroundColor: WidgetStateProperty.all(Colors.grey.shade300),
                    foregroundColor: WidgetStateProperty.all(Colors.grey.shade600),
                  ),
            child: Text(_selectedCountry?.toUpperCase() == 'GB'
                ? 'Find Address'
                : 'Address lookup only available for UK'),
          ),
        ),

        const SizedBox(height: 8),
        GestureDetector(
          onTap: _toggleAddressEntry,
          child: Text(
            'Enter address manually',
            style: TextStyle(
              color: AppColors.kBlueColor,
              decoration: TextDecoration.underline,
              fontSize: 14,
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Address dropdown
        if (_addressDropdownItems.isNotEmpty) ...[
          CustomDropdownButton2(
            isRquired: true,
            title: 'Address',
            hint: 'Select',
            dropdownItems: _addressDropdownItems,
            value:
                _selectedAddress ??
                (_addressDropdownItems.isNotEmpty
                    ? _addressDropdownItems.first.value
                    : null),
            selectedValue:
                _selectedAddress ??
                (_addressDropdownItems.isNotEmpty
                    ? _addressDropdownItems.first.value
                    : null),
            onChanged: (value) {
              setState(() {
                _selectedAddress = value;
                _selectedAddressController.text = value ?? '';
              });

              _formKey.currentState?.patchValue({'selectedAddress': value});
            },
          ),
          const SizedBox(height: 16),
        ],

        _buildDateSection(),
        const SizedBox(height: 16),
        _buildAddAddressButton(),
      ],
    );
  }

  // Stage 2: Manual entry with pre-filled data and resident dates
  Widget _buildManualEntryStage() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Back to postcode lookup button
        GestureDetector(
          onTap: _backToPostcodeLookup,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: AppColors.kBlueColor,
              borderRadius: BorderRadius.circular(5),
            ),
            child: const Text(
              'Back to Postcode Lookup',
              style: TextStyle(color: Colors.white, fontSize: 14),
            ),
          ),
        ),
        const SizedBox(height: 16),

        // Address Line 1 (pre-filled from lookup)
        DBSFormBuilderTextField(
          name: 'line1',
          label: 'Address Line 1',
          isRequired: true,
          initialValue: _line1Controller.text,
          validators: [
            FormBuilderValidators.required(errorText: 'Address Line 1 is required'),
          ],
          onChanged: (value) {
            _line1Controller.text = value ?? '';
          },
        ),
        const SizedBox(height: 16),

        // Address Line 2 (optional)
        DBSFormBuilderTextField(
          name: 'line2',
          label: 'Address Line 2 (Optional)',
          initialValue: _line2Controller.text,
          onChanged: (value) {
            _line2Controller.text = value ?? '';
          },
        ),
        const SizedBox(height: 16),

        // Town/City (pre-filled from lookup)
        DBSFormBuilderTextField(
          name: 'townCity',
          label: 'Town/City',
          isRequired: true,
          initialValue: _townCityController.text,
          validators: [
            FormBuilderValidators.required(errorText: 'Town/City is required'),
          ],
          onChanged: (value) {
            _townCityController.text = value ?? '';
          },
        ),
        const SizedBox(height: 16),

        // Postcode (pre-filled from lookup, read-only)
        DBSFormBuilderTextField(
          name: 'postcode',
          label: 'Postcode',
          isRequired: true,
          initialValue: _addressFromLookup['postcode'] ?? _postcodeController.text,
          readOnly: true,
          validators: [
            FormBuilderValidators.required(errorText: 'Postcode is required'),
          ],
        ),
        const SizedBox(height: 16),

        // Country dropdown (editable)
        CustomDropdownButton2(
          isRquired: true,
          title: 'Country',
          hint: 'Select',
          dropdownItems: Countries.getCountryDropdownItems(),
          value: _selectedCountry ?? 'GB',
          selectedValue: _selectedCountry ?? 'GB',
          onChanged: (value) {
            setState(() {
              _selectedCountry = value;
              _countryController.text = value ?? '';
            });
            // Clear postcode if switching away from UK
            if (value != 'GB') {
              _postcodeController.clear();
              _formKey.currentState?.patchValue({'postcode': ''});
            }
          },
        ),
        const SizedBox(height: 16),

        // Resident dates section
        _buildDateSection(),
        const SizedBox(height: 16),

        // Add Address button
        _buildAddAddressButton(),
      ],
    );
  }

  Widget _buildManualAddressSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: _toggleAddressEntry,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: AppColors.kBlueColor,
              borderRadius: BorderRadius.circular(5),
            ),
            child: const Text(
              'Back to Address Lookup',
              style: TextStyle(color: Colors.white, fontSize: 14),
            ),
          ),
        ),
        const SizedBox(height: 20),

        DBSFormBuilderTextField(
          key: const Key('manual_postcode_field'),
          name: 'manualPostcode',
          label: 'Postcode',
          isRequired: _selectedCountry?.toUpperCase() == 'GB',
          readOnly: false, // Explicitly enable the field
          maxLength: 10,
          validators: [
            if (_selectedCountry?.toUpperCase() == 'GB')
              FormBuilderValidators.required(errorText: 'Postcode is required for UK addresses'),
            (value) => validatePostcode(value),
          ],
          onChanged: (value) {
            _manualPostcodeController.text = value ?? '';
          },
        ),
        const SizedBox(height: 16),

        DBSFormBuilderTextField(
          name: 'addressLine1',
          label: 'Line 1',
          isRequired: true,
          maxLength: 60,
          validators: [
            FormBuilderValidators.required(
              errorText: 'Address line 1 is required',
            ),
          ],
          onChanged: (value) {
            _line1Controller.text = value ?? '';
          },
        ),
        const SizedBox(height: 16),

        DBSFormBuilderTextField(
          name: 'addressLine2',
          label: 'Line 2',
          maxLength: 60,
          onChanged: (value) {
            _line2Controller.text = value ?? '';
          },
        ),
        const SizedBox(height: 16),

        DBSFormBuilderTextField(
          name: 'townCity',
          label: 'Town/City',
          isRequired: true,
          maxLength: 30,
          validators: [
            FormBuilderValidators.required(errorText: 'Town/City is required'),
          ],
          onChanged: (value) {
            _townCityController.text = value ?? '';
          },
        ),
        const SizedBox(height: 16),

        CustomDropdownButton2(
          isRquired: true,
          title: 'Country',
          hint: 'Select',
          dropdownItems: Countries.getCountryDropdownItems(),
          value: _selectedCountry ?? 'GB',
          selectedValue: _selectedCountry ?? 'GB',
          onChanged: (value) {
            setState(() {
              _selectedCountry = value;
              _countryController.text = value ?? '';
            });
            // Clear postcode if switching away from UK
            if (value != 'GB') {
              _manualPostcodeController.clear();
              _formKey.currentState?.patchValue({'manualPostcode': ''});
            }
          },
        ),
        const SizedBox(height: 16),

        _buildDateSection(),
        const SizedBox(height: 16),
        _buildAddAddressButton(),
      ],
    );
  }

  Widget _buildDateSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Resident from section
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Resident from:',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.orangeColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: DBSFormBuilderTextField(
                          name: 'residentFromMonth',
                          label: 'MM',
                          isRequired: true,
                          keyboardType: TextInputType.number,
                          maxLength: 2,
                          validators: [
                            FormBuilderValidators.required(
                              errorText: 'Month is required',
                            ),
                            (value) => validateMonth(value),
                          ],
                          onChanged: (value) {
                            _residentFromMonthController.text = value ?? '';
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: DBSFormBuilderTextField(
                          name: 'residentFromYear',
                          label: 'YYYY',
                          isRequired: true,
                          keyboardType: TextInputType.number,
                          maxLength: 4,
                          validators: [
                            FormBuilderValidators.required(
                              errorText: 'Year is required',
                            ),
                            (value) => validateYear(value),
                          ],
                          onChanged: (value) {
                            _residentFromYearController.text = value ?? '';
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      InkWell(
                        onTap: _showResidentFromDatePicker,
                        child: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey),
                            borderRadius: BorderRadius.circular(4),
                            color: Colors.grey.shade50,
                          ),
                          child: Icon(
                            Icons.calendar_month,
                            color: AppColors.kBlueColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            // Only show "Resident till" for subsequent addresses (not the first one)
            if (_addressHistory.isNotEmpty) ...[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Resident till:',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.orangeColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: DBSFormBuilderTextField(
                            name: 'residentToMonth',
                            label: 'MM',
                            keyboardType: TextInputType.number,
                            maxLength: 2,
                            validators: [
                              (value) => validateOptionalMonth(value),
                            ],
                            onChanged: (value) {
                              _residentToMonthController.text = value ?? '';
                            },
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: DBSFormBuilderTextField(
                            name: 'residentToYear',
                            label: 'YYYY',
                            keyboardType: TextInputType.number,
                            maxLength: 4,
                            validators: [
                              (value) => validateOptionalYear(value),
                            ],
                            onChanged: (value) {
                              _residentToYearController.text = value ?? '';
                            },
                          ),
                        ),
                        const SizedBox(width: 8),
                        InkWell(
                          onTap: _showResidentToDatePicker,
                          child: Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(4),
                              color: Colors.grey.shade50,
                            ),
                            child: Icon(
                              Icons.calendar_month,
                              color: AppColors.kBlueColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ] else ...[
              // For first address, show "Present" indicator
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Resident till:',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.orangeColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.green.shade50,
                        border: Border.all(color: Colors.green.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Row(
                        children: [
                          Icon(Icons.home, color: Colors.green, size: 20),
                          SizedBox(width: 8),
                          Text(
                            'Present (Current Address)',
                            style: TextStyle(
                              color: Colors.green,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }

  Widget _buildAddAddressButton() {
    return Container(
      width: double.infinity,
      height: 48,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: OutlinedButton(
        onPressed: _addToAddressHistory,
        style: ComponentConfig.secondaryButtonStyle,
        child: const Text('Add Address'),
      ),
    );
  }

  Widget _buildAddMoreAddressesQuestion() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green.shade600, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'You have provided complete address history for the last 5 years',
                  style: TextStyle(
                    color: Colors.green.shade700,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Text(
            'Do you want to add more previous addresses?',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    setState(() {
                      _wantToAddMoreAddresses = false;
                    });
                  },
                  style: OutlinedButton.styleFrom(
                    backgroundColor: !_wantToAddMoreAddresses
                        ? Colors.grey.shade100
                        : Colors.white,
                    side: BorderSide(
                      color: !_wantToAddMoreAddresses
                          ? Colors.grey.shade400
                          : Colors.grey.shade300,
                      width: !_wantToAddMoreAddresses ? 2 : 1,
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: Text(
                    'No',
                    style: TextStyle(
                      color: !_wantToAddMoreAddresses
                          ? Colors.grey.shade700
                          : Colors.grey.shade600,
                      fontWeight: !_wantToAddMoreAddresses
                          ? FontWeight.w600
                          : FontWeight.normal,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _wantToAddMoreAddresses = true;
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _wantToAddMoreAddresses
                        ? AppColors.kBlueColor
                        : Colors.grey.shade200,
                    foregroundColor: _wantToAddMoreAddresses
                        ? Colors.white
                        : Colors.grey.shade600,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    elevation: _wantToAddMoreAddresses ? 2 : 0,
                  ),
                  child: Text(
                    'Yes',
                    style: TextStyle(
                      fontWeight: _wantToAddMoreAddresses
                          ? FontWeight.w600
                          : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAddressHistorySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_addressHistory.isEmpty) ...[
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(4),
            ),
            child: const Center(
              child: Text(
                'No previous addresses have been added',
                style: TextStyle(color: Colors.grey, fontSize: 14),
              ),
            ),
          ),
        ] else ...[
          // Clear all button and test button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${_addressHistory.length} address${_addressHistory.length == 1 ? '' : 'es'} added',
                style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
              ),
              Row(
                children: [
                  TextButton.icon(
                    onPressed: _showClearAllConfirmation,
                    style: ComponentConfig.textButtonStyle.copyWith(
                      foregroundColor: WidgetStateProperty.all(Colors.red),
                    ),
                    icon: const Icon(
                      Icons.clear_all,
                      size: 16,
                      color: Colors.red,
                    ),
                    label: const Text(
                      'Clear All',
                      style: TextStyle(color: Colors.red, fontSize: 14),
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildAddressHistoryList(),
        ],

        const SizedBox(height: 16),

        // Address gaps warning
        ..._buildAddressGapWarnings(),

        // 5 years completion status (only show if not completed)
        if (!_hasCompleted5Years) ...[
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange.shade50,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.orange.shade200),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.warning_amber,
                  color: Colors.orange.shade600,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Please ensure your address history covers the complete last 5 years with no gaps',
                    style: TextStyle(
                      color: Colors.orange.shade700,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildAddressHistoryList() {
    // Sort addresses by from date (most recent first)
    final sortedAddresses = List<Map<String, dynamic>>.from(_addressHistory);
    sortedAddresses.sort((a, b) {
      final dateA = _parseDateFromString(a['fromDate']);
      final dateB = _parseDateFromString(b['fromDate']);
      if (dateA == null || dateB == null) return 0;
      return dateB.compareTo(dateA); // Most recent first
    });

    return Column(
      children: [
        for (int i = 0; i < sortedAddresses.length; i++) ...[
          _buildTimelineItem(
            sortedAddresses[i],
            i,
            sortedAddresses.length,
            i < sortedAddresses.length - 1 ? sortedAddresses[i + 1] : null,
          ),
        ],
      ],
    );
  }

  Widget _buildTimelineItem(
    Map<String, dynamic> address,
    int index,
    int totalItems,
    Map<String, dynamic>? nextAddress,
  ) {
    final hasGap = _hasGapBetweenAddresses(address, nextAddress);

    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Left side - Date and timeline
            SizedBox(
              width: 140,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  // Date range text
                  Text(
                    _formatDateRange(address['fromDate'], address['toDate']),
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.kBlueColor,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.right,
                  ),
                  const SizedBox(height: 8),
                ],
              ),
            ),

            const SizedBox(width: 16),

            // Timeline dot and line
            SizedBox(
              height: hasGap ? 140 : 100, // Adjust height based on content
              child: Column(
                children: [
                  // Timeline dot
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: AppColors.kBlueColor,
                      shape: BoxShape.circle,
                    ),
                  ),

                  // Timeline line (if not last item)
                  if (index < totalItems - 1) ...[
                    Expanded(
                      child: Container(
                        width: 2,
                        color: hasGap
                            ? Colors.red.shade300
                            : AppColors.kBlueColor,
                      ),
                    ),
                  ],
                ],
              ),
            ),

            const SizedBox(width: 16),

            // Right side - Address details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Location title
                  Text(
                    _extractLocationFromAddress(address['address'] ?? ''),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),

                  // Full address
                  Text(
                    address['address'] ?? '',
                    style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
                  ),
                  const SizedBox(height: 12),
                ],
              ),
            ),

            // Action buttons
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Edit icon
                IconButton(
                  icon: Icon(
                    Icons.edit_outlined,
                    color: AppColors.kBlueColor,
                    size: 20,
                  ),
                  onPressed: () {
                    _editAddress(index);
                  },
                  tooltip: 'Edit address',
                ),

                // Delete icon
                IconButton(
                  icon: const Icon(
                    Icons.delete_outline,
                    color: Colors.red,
                    size: 20,
                  ),
                  onPressed: () {
                    _showDeleteConfirmation(index);
                  },
                  tooltip: 'Remove address',
                ),
              ],
            ),
          ],
        ),

        // Gap warning (if exists)
        if (hasGap && nextAddress != null) ...[
          Row(
            children: [
              const SizedBox(width: 140 + 16), // Align with timeline
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: Colors.red.shade400,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 2),
                ),
                child: const Icon(Icons.warning, size: 8, color: Colors.white),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red.shade200),
                  ),
                  child: Text(
                    'There is a gap between your residences.\nPlease add an address to explain this gap',
                    style: TextStyle(fontSize: 13, color: Colors.red.shade700),
                  ),
                ),
              ),
              const SizedBox(
                width: 88,
              ), // Space for edit and delete icons alignment
            ],
          ),
          const SizedBox(height: 20),
        ],
      ],
    );
  }

  String _formatDateRange(String? fromDate, String? toDate) {
    if (fromDate == null) return '';

    final fromParts = fromDate.split('/');
    if (fromParts.length != 2) return '';

    final fromMonth = int.tryParse(fromParts[0]);
    final fromYear = int.tryParse(fromParts[1]);

    if (fromMonth == null || fromYear == null) return '';

    final fromFormatted =
        '${fromMonth.toString().padLeft(2, '0')} ${_getMonthName(fromMonth)}, $fromYear';

    if (toDate == null || toDate.toLowerCase() == 'present') {
      return '$fromFormatted to\nPresent';
    }

    final toParts = toDate.split('/');
    if (toParts.length != 2) return '$fromFormatted to\nPresent';

    final toMonth = int.tryParse(toParts[0]);
    final toYear = int.tryParse(toParts[1]);

    if (toMonth == null || toYear == null) return '$fromFormatted to\nPresent';

    final toFormatted =
        '${toMonth.toString().padLeft(2, '0')} ${_getMonthName(toMonth)}, $toYear';
    return '$fromFormatted to\n$toFormatted';
  }

  String _getMonthName(int month) {
    const months = [
      '',
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    return months[month];
  }

  String _extractLocationFromAddress(String address) {
    // Extract city/country from address
    final parts = address.split(',');
    if (parts.length >= 2) {
      // Try to get the last two parts (city, country)
      final city = parts[parts.length - 2].trim();
      final country = parts[parts.length - 1].trim();
      return '$city, $country';
    }
    return 'London, United Kingdom'; // Default fallback
  }

  bool _hasGapBetweenAddresses(
    Map<String, dynamic> currentAddress,
    Map<String, dynamic>? nextAddress,
  ) {
    if (nextAddress == null) return false;

    // If current address goes to "Present", there's no gap
    if (currentAddress['toDate']?.toLowerCase() == 'present') return false;

    // Parse dates from MM/YYYY format
    final currentToDateStr = currentAddress['toDate'] as String?;
    final nextFromDateStr = nextAddress['fromDate'] as String?;

    if (currentToDateStr == null || nextFromDateStr == null) return false;

    final currentToParts = currentToDateStr.split('/');
    final nextFromParts = nextFromDateStr.split('/');

    if (currentToParts.length != 2 || nextFromParts.length != 2) return false;

    final currentToMonth = int.tryParse(currentToParts[0]);
    final currentToYear = int.tryParse(currentToParts[1]);
    final nextFromMonth = int.tryParse(nextFromParts[0]);
    final nextFromYear = int.tryParse(nextFromParts[1]);

    if (currentToMonth == null ||
        currentToYear == null ||
        nextFromMonth == null ||
        nextFromYear == null) {
      return false;
    }

    // Calculate the difference in months
    final currentEndMonthTotal = currentToYear * 12 + currentToMonth;
    final nextStartMonthTotal = nextFromYear * 12 + nextFromMonth;

    // There's a gap if there's more than 1 month difference
    // For example: current ends in 01/2023, next starts in 03/2023 = gap in 02/2023
    final monthsDifference = nextStartMonthTotal - currentEndMonthTotal;

    return monthsDifference > 1;
  }

  void _editAddress(int index) {
    if (index < 0 || index >= _addressHistory.length) return;

    final address = _addressHistory[index];

    final fromDateParts = address['fromDate']?.split('/');
    final toDateParts = address['toDate']?.split('/');

    _clearFormFields();
    if (fromDateParts != null && fromDateParts.length == 2) {
      _residentFromMonthController.text = fromDateParts[0];
      _residentFromYearController.text = fromDateParts[1];
    }

    if (toDateParts != null &&
        toDateParts.length == 2 &&
        address['toDate'] != 'Present') {
      _residentToMonthController.text = toDateParts[0];
      _residentToYearController.text = toDateParts[1];
    } else {
      _residentToMonthController.clear();
      _residentToYearController.clear();
    }

    final addressText = address['address'] ?? '';

    String extractedPostcode = '';
    final addressParts = addressText.split(', ');

    for (int i = addressParts.length - 1; i >= 0; i--) {
      final part = addressParts[i].trim();
      if (RegExp(
        r'^[A-Z]{1,2}[0-9][A-Z0-9]?\s?[0-9][A-Z]{2}$',
        caseSensitive: false,
      ).hasMatch(part)) {
        extractedPostcode = part;
        break;
      }
    }

    if (extractedPostcode.isNotEmpty) {
      _postcodeController.text = extractedPostcode;
    }

    if (!_isManualEntry) {
      setState(() {
        _addressDropdownItems = [
          DropdownMenuItem(value: addressText, child: Text(addressText)),
        ];
        _selectedAddress = addressText;
        _selectedAddressController.text = addressText;
      });
    } else {
      if (addressParts.isNotEmpty) {
        _line1Controller.text = addressParts[0];
        if (addressParts.length > 1) {
          _line2Controller.text = addressParts[1];
        }
        if (addressParts.length > 2) {
          _townCityController.text = addressParts[2];
        }
        if (addressParts.length > 3) {
          final countryPart = addressParts[3].trim();
          // Convert country name to ISO code if needed
          String countryCode = countryPart;
          if (countryPart == 'United Kingdom' || countryPart == 'UK') {
            countryCode = 'GB';
          }
          _countryController.text = countryCode;
          _selectedCountry = countryCode;
        }
      }
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _formKey.currentState?.patchValue({
        'postcode': _postcodeController.text,
        'manualPostcode': _manualPostcodeController.text,
        'addressLine1': _line1Controller.text,
        'addressLine2': _line2Controller.text,
        'townCity': _townCityController.text,
        'residentFromMonth': _residentFromMonthController.text,
        'residentFromYear': _residentFromYearController.text,
        'residentToMonth': _residentToMonthController.text,
        'residentToYear': _residentToYearController.text,
      });
    });

    setState(() {
      _addressHistory.removeAt(index);
      _checkIfCompleted5Years();
    });

    _saveAddressesToViewModel();

    _saveToLocalStorage();

    CustomSnackBar.show(
      context: context,
      message:
          'Address loaded for editing. Make changes and click "Add Address" to save.',
      backgroundColor: AppColors.kBlueColor,
      textColor: Colors.white,
      icon: Icons.edit,
      duration: const Duration(seconds: 3),
    );
  }

  void _showDeleteConfirmation(int index) {
    if (index < 0 || index >= _addressHistory.length) return;

    final address = _addressHistory[index];
    final location = _extractLocationFromAddress(address['address'] ?? '');

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Remove Address',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Are you sure you want to remove this address from your history?',
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      location,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      address['address'] ?? '',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      address['dateRange'] ?? '',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.kBlueColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              style: ComponentConfig.textButtonStyle.copyWith(
                foregroundColor: WidgetStateProperty.all(Colors.grey),
              ),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteAddress(index);
              },
              style: ComponentConfig.primaryButtonStyle.copyWith(
                backgroundColor: WidgetStateProperty.all(Colors.red),
              ),
              child: const Text('Remove'),
            ),
          ],
        );
      },
    );
  }

  void _deleteAddress(int index) {
    if (index < 0 || index >= _addressHistory.length) return;

    final address = _addressHistory[index];
    final location = _extractLocationFromAddress(address['address'] ?? '');

    // Store the deleted address for potential undo
    final deletedAddress = Map<String, dynamic>.from(address);
    final deletedIndex = index;

    setState(() {
      _addressHistory.removeAt(index);
      _checkIfCompleted5Years();
    });

    // Save changes to view model
    _saveAddressesToViewModel();

    // Save to local storage
    _saveToLocalStorage();

    // Show success message with undo option
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Address "$location" removed successfully'),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: 'UNDO',
          textColor: Colors.white,
          onPressed: () {
            // Restore the deleted address
            setState(() {
              _addressHistory.insert(deletedIndex, deletedAddress);
              _checkIfCompleted5Years();
            });

            // Save changes to view model after undo
            _saveAddressesToViewModel();

            // Save to local storage
            _saveToLocalStorage();

            // Show restoration confirmation
            CustomSnackBar.show(
              context: context,
              message: 'Address "$location" restored',
              backgroundColor: Colors.green,
              textColor: Colors.white,
              icon: Icons.restore,
              duration: const Duration(seconds: 2),
            );
          },
        ),
      ),
    );
  }

  void _showClearAllConfirmation() {
    if (_addressHistory.isEmpty) return;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Clear All Addresses',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Are you sure you want to remove all ${_addressHistory.length} address${_addressHistory.length == 1 ? '' : 'es'} from your history?',
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.warning_amber,
                      color: Colors.red.shade600,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'This action cannot be undone. You will need to re-add all addresses.',
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.red.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              style: ComponentConfig.textButtonStyle.copyWith(
                foregroundColor: WidgetStateProperty.all(Colors.grey),
              ),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _clearAllAddresses();
              },
              style: ComponentConfig.primaryButtonStyle.copyWith(
                backgroundColor: WidgetStateProperty.all(Colors.red),
              ),
              child: const Text('Clear All'),
            ),
          ],
        );
      },
    );
  }

  void _clearAllAddresses() {
    final count = _addressHistory.length;

    setState(() {
      _addressHistory.clear();
      _checkIfCompleted5Years();
    });

    _saveAddressesToViewModel();

    _saveToLocalStorage();

    CustomSnackBar.show(
      context: context,
      message:
          'All $count address${count == 1 ? '' : 'es'} removed successfully',
      backgroundColor: Colors.red,
      textColor: Colors.white,
      icon: Icons.clear_all,
      duration: const Duration(seconds: 3),
    );
  }

  void resetFormData() {
    setState(() {
      _addressHistory.clear();

      _hasCompleted5Years = false;
      _wantToAddMoreAddresses = false;

      _isManualEntry = false;

      _selectedAddress = null;
      _selectedCountry = 'GB'; // Use ISO code
      _addressDropdownItems.clear();
    });

    _clearFormFields();

    _saveAddressesToViewModel();
  }
}
