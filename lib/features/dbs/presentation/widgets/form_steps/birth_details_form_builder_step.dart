import 'package:SolidCheck/features/dbs/presentation/viewmodels/dbs_form_viewmodel.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/form_fields/dbs_form_builder_fields.dart';
import 'package:SolidCheck/features/dbs/providers/form_reset_provider.dart';
import 'package:SolidCheck/features/dbs/providers/form_validation_provider.dart';
import 'package:SolidCheck/shared/data/country_data.dart';
import 'package:SolidCheck/shared/utils/dbs_validation_rules.dart';
import 'package:SolidCheck/shared/utils/responsive.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:form_builder_validators/form_builder_validators.dart';

class BirthDetailsFormBuilderStep extends ConsumerStatefulWidget {
  const BirthDetailsFormBuilderStep({super.key});

  @override
  BirthDetailsFormBuilderStepState createState() =>
      BirthDetailsFormBuilderStepState();
}

class BirthDetailsFormBuilderStepState
    extends ConsumerState<BirthDetailsFormBuilderStep>
    with AutomaticKeepAliveClientMixin {
  final GlobalKey<FormBuilderState> _formKey = GlobalKey<FormBuilderState>();
  bool _hasLoadedData = false;
  bool _hasTriedToSubmit = false;
  final Set<String> _fieldsInteracted = <String>{};
  final Set<String> _fieldsWithRemovedContent = <String>{};

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadExistingData();
      _updateSmartBirthSurnameUntil();
    });
  }

  void _trackFieldInteraction(
    String fieldName,
    String? newValue,
    String? previousValue,
  ) {
    setState(() {
      _fieldsInteracted.add(fieldName);

      if (previousValue != null &&
          previousValue.isNotEmpty &&
          (newValue == null || newValue.isEmpty)) {
        _fieldsWithRemovedContent.add(fieldName);
      } else if (newValue != null && newValue.isNotEmpty) {
        _fieldsWithRemovedContent.remove(fieldName);
      }
    });
  }

  void _validateField(String fieldName) {
    final field = _formKey.currentState?.fields[fieldName];
    if (field != null) {
      field.validate();
    }
  }

  bool _shouldShowValidationError(String fieldName) {
    return _hasTriedToSubmit || _fieldsWithRemovedContent.contains(fieldName);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadExistingData();
    });
  }

  void forceReloadData() {
    _loadExistingData();
  }

  void _loadExistingData() {
    final formState = ref.read(dbsFormViewModelProvider);
    final applicant = formState.formData.applicantDetails;
    final additional = applicant.additionalApplicantDetails;

    DateTime? dateOfBirth;
    if (applicant.dateOfBirth.isNotEmpty) {
      try {
        dateOfBirth = DateTime.parse(applicant.dateOfBirth);
      } catch (e) {}
    } else {}

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted && _formKey.currentState != null) {
            final formValues = <String, dynamic>{};

            if (additional.birthSurname.isNotEmpty) {
              formValues['birthSurname'] = additional.birthSurname;
            }
            if (dateOfBirth != null) formValues['dateOfBirth'] = dateOfBirth;
            if (additional.birthSurnameUntil.isNotEmpty) {
              formValues['birthSurnameUntil'] = additional.birthSurnameUntil;
            }
            if (additional.birthTown.isNotEmpty) {
              formValues['birthTown'] = additional.birthTown;
            }
            if (additional.birthCounty.isNotEmpty) {
              formValues['birthCounty'] = additional.birthCounty;
            }

            formValues['birthCountry'] = additional.birthCountry.isNotEmpty
                ? additional.birthCountry
                : 'GB';
            formValues['birthNationality'] =
                additional.birthNationality.isNotEmpty
                ? additional.birthNationality
                : 'BRITISH';

            _formKey.currentState?.patchValue(formValues);

            Future.delayed(const Duration(milliseconds: 200), () {
              if (mounted && _formKey.currentState != null) {
                _formKey.currentState?.patchValue(formValues);

                Future.delayed(const Duration(milliseconds: 100), () {
                  if (mounted) {
                    final currentValues = _formKey.currentState?.value;

                    final dobValue = currentValues?['dateOfBirth'];
                  }
                });
              }
            });
          }
        });
      }
    });
  }

  void _updateSmartBirthSurnameUntil() {
    final viewModel = ref.read(dbsFormViewModelProvider.notifier);
    final smartValue = viewModel.calculateSmartBirthSurnameUntil();


    if (mounted && _formKey.currentState != null) {
      // Update the form field with the smart value
      _formKey.currentState?.patchValue({
        'birthSurnameUntil': smartValue,
      });

      // Save the data to persist the smart value
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          _saveData();
        }
      });
    } else {
    }
  }

  void _saveData() {
    _formKey.currentState?.save();
    final formData = _formKey.currentState?.value ?? {};
    if (formData.isNotEmpty) {
      final formNotifier = ref.read(dbsFormViewModelProvider.notifier);
      final currentApplicant = ref
          .read(dbsFormViewModelProvider)
          .formData
          .applicantDetails;

      final dateOfBirth = formData['dateOfBirth'] as DateTime?;
      if (dateOfBirth != null) {
        final dateString =
            '${dateOfBirth.year.toString().padLeft(4, '0')}-${dateOfBirth.month.toString().padLeft(2, '0')}-${dateOfBirth.day.toString().padLeft(2, '0')}';
        formNotifier.updateBasicPersonalDetails(dateOfBirth: dateString);
      }

      final updatedAdditional = currentApplicant.additionalApplicantDetails
          .copyWith(
            birthSurname: formData['birthSurname']?.toString() ?? '',
            birthSurnameUntil: formData['birthSurnameUntil']?.toString() ?? '',
            birthTown: formData['birthTown']?.toString() ?? '',
            birthCounty: formData['birthCounty']?.toString() ?? '',
            birthCountry: formData['birthCountry']?.toString() ?? '',
            birthNationality: formData['birthNationality']?.toString() ?? '',
          );

      formNotifier.updateAdditionalApplicantDetails(updatedAdditional);
    }
  }

  String? _validateBirthSurnameUntil(String? value) {
    final formState = ref.read(dbsFormViewModelProvider);
    final applicant = formState.formData.applicantDetails;
    return DBSValidationRules.validateBirthSurnameUntil(
      value,
      applicant.dateOfBirth,
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    ref.listen<int>(formResetTriggerProvider, (previous, next) {
      if (next > 0 && previous != next) {
        resetFormData();
      }
    });

    final size = MediaQuery.of(context).size;
    final isMobile = ResponsiveUtil.isMobile(context);
    final isTablet = ResponsiveUtil.isTablet(context);
    final validationMode = ref.watch(formValidationModeProvider);

    return Container(
      padding: EdgeInsets.all(isMobile ? 16 : 24),
      child: FormBuilder(
        key: _formKey,
        autovalidateMode: AutovalidateMode.disabled,
        onChanged: () {
          Future.delayed(const Duration(milliseconds: 100), () {
            _saveData();
          });
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Birth Details',
              style: TextStyle(
                fontSize: isMobile ? 20 : 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Please provide your birth information',
              style: TextStyle(
                fontSize: isMobile ? 14 : 16,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 24),

            if (isMobile)
              _buildMobileLayout()
            else if (isTablet)
              _buildTabletLayout()
            else
              _buildDesktopLayout(),
          ],
        ),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return Column(
      children: [
        _buildBirthSurnameField(),
        const SizedBox(height: 16),
        _buildDateOfBirthField(),
        const SizedBox(height: 16),
        _buildBirthNationalityDropdown(),
        const SizedBox(height: 16),
        _buildBirthCountryDropdown(),
        const SizedBox(height: 16),
        _buildBirthTownField(),
        const SizedBox(height: 16),
        _buildBirthCountyField(),
      ],
    );
  }

  Widget _buildTabletLayout() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(child: _buildBirthSurnameField()),
            const SizedBox(width: 16),
            Expanded(child: _buildDateOfBirthField()),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(child: _buildBirthNationalityDropdown()),
            const SizedBox(width: 16),
            Expanded(child: _buildBirthCountryDropdown()),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(child: _buildBirthTownField()),
            const SizedBox(width: 16),
            Expanded(child: _buildBirthCountyField()),
          ],
        ),
      ],
    );
  }

  Widget _buildDesktopLayout() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(child: _buildBirthSurnameField()),
            const SizedBox(width: 16),
            Expanded(child: _buildDateOfBirthField()),
            const SizedBox(width: 16),
            Expanded(child: _buildBirthNationalityDropdown()),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(child: _buildBirthCountryDropdown()),
            const SizedBox(width: 16),
            Expanded(child: _buildBirthTownField()),
            const SizedBox(width: 16),
            Expanded(child: _buildBirthCountyField()),
          ],
        ),
      ],
    );
  }

  Widget _buildBirthSurnameField() {
    String? previousValue;
    return DBSFormBuilderTextField(
      name: 'birthSurname',
      label: 'Birth Surname',
      isRequired: true,
      maxLength: 60,
      validators: [
        FormBuilderValidators.required(errorText: 'Birth surname is required'),
        FormBuilderValidators.maxLength(
          60,
          errorText: 'Birth surname cannot exceed 60 characters',
        ),
      ],
      onChanged: (value) {
        _trackFieldInteraction('birthSurname', value, previousValue);
        previousValue = value;
        _saveData();

        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) {
            _validateField('birthSurname');
          }
        });
      },
    );
  }

  Widget _buildDateOfBirthField() {
    return DBSFormBuilderDatePicker(
      name: 'dateOfBirth',
      label: 'Date of Birth',
      isRequired: true,
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
      validators: [
        FormBuilderValidators.required(errorText: 'Date of birth is required'),
        DBSValidators.dateOfBirth(),
      ],
      onChanged: (value) {
        setState(() {
          _fieldsInteracted.add('dateOfBirth');
        });
        _saveData();

        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) {
            _validateField('dateOfBirth');
          }
        });
      },
    );
  }

  Widget _buildBirthSurnameUntilField() {
    String? previousValue;
    return DBSFormBuilderTextField(
      name: 'birthSurnameUntil',
      label: 'Birth Surname Until (Year)',
      hint: 'YYYY',
      keyboardType: TextInputType.number,
      maxLength: 4,
      validators: [
        FormBuilderValidators.maxLength(
          4,
          errorText: 'Year cannot exceed 4 characters',
        ),
        _validateBirthSurnameUntil,
      ],
      onChanged: (value) {
        _trackFieldInteraction('birthSurnameUntil', value, previousValue);
        previousValue = value;
        _saveData();

        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) {
            _validateField('birthSurnameUntil');
          }
        });
      },
    );
  }

  Widget _buildBirthTownField() {
    String? previousValue;
    return DBSFormBuilderTextField(
      name: 'birthTown',
      label: 'Birth Town',
      isRequired: true,
      maxLength: 30,
      validators: [
        FormBuilderValidators.required(errorText: 'Birth town is required'),
        FormBuilderValidators.maxLength(
          30,
          errorText: 'Birth town cannot exceed 30 characters',
        ),
      ],
      onChanged: (value) {
        _trackFieldInteraction('birthTown', value, previousValue);
        previousValue = value;
        _saveData();

        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) {
            _validateField('birthTown');
          }
        });
      },
    );
  }

  Widget _buildBirthCountyField() {
    String? previousValue;
    return DBSFormBuilderTextField(
      name: 'birthCounty',
      label: 'Birth County',
      maxLength: 30,
      validators: [
        FormBuilderValidators.maxLength(
          30,
          errorText: 'Birth county cannot exceed 30 characters',
        ),
      ],
      onChanged: (value) {
        _trackFieldInteraction('birthCounty', value, previousValue);
        previousValue = value;
        _saveData();

        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) {
            _validateField('birthCounty');
          }
        });
      },
    );
  }

  Widget _buildBirthCountryDropdown() {
    return DBSFormBuilderDropdown<String>(
      name: 'birthCountry',
      label: 'Birth Country',
      isRequired: true,
      hint: 'Select country',
      items: Countries.getCountryDropdownItems(),
      validators: [
        FormBuilderValidators.required(errorText: 'Birth country is required'),
      ],
      onChanged: (value) {
        setState(() {
          _fieldsInteracted.add('birthCountry');
        });
        _saveData();

        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) {
            _validateField('birthCountry');
          }
        });
      },
    );
  }

  Widget _buildBirthNationalityDropdown() {
    return DBSFormBuilderDropdown<String>(
      name: 'birthNationality',
      label: 'Birth Nationality',
      isRequired: true,
      hint: 'Select nationality',
      items: Countries.getNationalityDropdownItems(),
      validators: [
        FormBuilderValidators.required(
          errorText: 'Birth nationality is required',
        ),
      ],
      onChanged: (value) {
        setState(() {
          _fieldsInteracted.add('birthNationality');
        });
        _saveData();

        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) {
            _validateField('birthNationality');
          }
        });
      },
    );
  }

  bool validateForm() {
    setState(() {
      _hasTriedToSubmit = true;
    });

    _formKey.currentState?.save();

    final isValid = _formKey.currentState?.validate() ?? false;

    return isValid;
  }

  Map<String, String> getValidationErrors() {
    final errors = <String, String>{};

    _formKey.currentState?.save();
    final isValid = _formKey.currentState?.validate() ?? true;

    if (!isValid) {
      errors['validation'] =
          'Please fix all validation issues before continuing into next step.';
    }

    return errors;
  }

  void resetFormData() {
    setState(() {
      _hasLoadedData = false;
      _hasTriedToSubmit = false;
      _fieldsInteracted.clear();
      _fieldsWithRemovedContent.clear();
    });

    _formKey.currentState?.patchValue({
      'birthSurname': '',
      'dateOfBirth': null,
      'birthSurnameUntil': '',
      'birthTown': '',
      'birthCounty': '',
      'birthCountry': 'GB',
      'birthNationality': 'BRITISH',
    });

    _formKey.currentState?.reset();

    _saveData();
  }
}
