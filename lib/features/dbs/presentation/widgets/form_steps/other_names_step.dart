import 'package:SolidCheck/core/config/component_config.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/dbs/data/models/dbs_form_data.dart';
import 'package:SolidCheck/features/dbs/presentation/viewmodels/dbs_form_viewmodel.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/form_fields/dbs_radio_group.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/form_fields/dbs_text_field.dart';
import 'package:SolidCheck/features/dbs/providers/form_reset_provider.dart';
import 'package:SolidCheck/shared/utils/responsive.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class OtherSurnameControllers {
  final TextEditingController nameController;
  final TextEditingController usedFromController;
  final TextEditingController usedToController;

  OtherSurnameControllers()
    : nameController = TextEditingController(),
      usedFromController = TextEditingController(),
      usedToController = TextEditingController();

  void dispose() {
    nameController.dispose();
    usedFromController.dispose();
    usedToController.dispose();
  }

  OtherSurnameData toData() {
    return OtherSurnameData(
      name: nameController.text.trim(),
      usedFrom: usedFromController.text.trim(),
      usedTo: usedToController.text.trim(),
    );
  }
}

/// Helper class to manage other forename controllers
class OtherForenameControllers {
  final TextEditingController nameController;
  final TextEditingController usedFromController;
  final TextEditingController usedToController;

  OtherForenameControllers()
    : nameController = TextEditingController(),
      usedFromController = TextEditingController(),
      usedToController = TextEditingController();

  void dispose() {
    nameController.dispose();
    usedFromController.dispose();
    usedToController.dispose();
  }

  OtherForenameData toData() {
    return OtherForenameData(
      name: nameController.text.trim(),
      usedFrom: usedFromController.text.trim(),
      usedTo: usedToController.text.trim(),
    );
  }
}

/// Other Names Step Widget
/// Fourth step of the DBS form for collecting other surnames and forenames used
class OtherNamesStep extends ConsumerStatefulWidget {
  const OtherNamesStep({super.key});

  @override
  ConsumerState<OtherNamesStep> createState() => OtherNamesStepState();
}

class OtherNamesStepState extends ConsumerState<OtherNamesStep> {
  final _formKey = GlobalKey<FormState>();

  // Form values
  bool _hasUsedOtherSurnames = false;
  bool _hasUsedOtherForenames = false;

  // Validation tracking like personal details
  bool _hasTriedToSubmit = false;
  final Set<String> _fieldsInteracted = <String>{};
  final Set<String> _fieldsWithRemovedContent = <String>{};

  // Controllers for other surnames
  final List<OtherSurnameControllers> _otherSurnameControllers = [];

  // Controllers for other forenames
  final List<OtherForenameControllers> _otherForenameControllers = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadExistingData();
    });
  }

  void _trackFieldInteraction(
    String fieldName,
    String? newValue,
    String? previousValue,
  ) {
    setState(() {
      _fieldsInteracted.add(fieldName);
      if (previousValue != null &&
          previousValue.isNotEmpty &&
          (newValue == null || newValue.isEmpty)) {
        _fieldsWithRemovedContent.add(fieldName);
      } else if (newValue != null && newValue.isNotEmpty) {
        _fieldsWithRemovedContent.remove(fieldName);
      }
    });
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        _validateField(fieldName);
      }
    });
  }

  void _validateField(String fieldName) {
    final field = _formKey.currentState;
    if (field != null) {
      field.validate();
    }
  }

  bool _shouldShowValidationError(String fieldName) {
    return _hasTriedToSubmit || _fieldsWithRemovedContent.contains(fieldName);
  }

  void _loadExistingData() {
    final formState = ref.read(dbsFormViewModelProvider);
    final additionalDetails =
        formState.formData.applicantDetails.additionalApplicantDetails;

    setState(() {
      _hasUsedOtherSurnames = additionalDetails.otherSurnames.isNotEmpty;
      _hasUsedOtherForenames = additionalDetails.otherForenames.isNotEmpty;

      // Load other surnames
      _otherSurnameControllers.clear();
      for (final surname in additionalDetails.otherSurnames) {
        _addOtherSurnameControllers(surname);
      }

      // Load other forenames
      _otherForenameControllers.clear();
      for (final forename in additionalDetails.otherForenames) {
        _addOtherForenameControllers(forename);
      }
    });
  }

  @override
  void dispose() {
    // Dispose surname controllers
    for (final controllers in _otherSurnameControllers) {
      controllers.dispose();
    }

    // Dispose forename controllers
    for (final controllers in _otherForenameControllers) {
      controllers.dispose();
    }

    super.dispose();
  }

  void _addOtherSurnameControllers(OtherSurnameData? existingData) {
    final controllers = OtherSurnameControllers();

    if (existingData != null) {
      controllers.nameController.text = existingData.name;
      controllers.usedFromController.text = existingData.usedFrom;
      controllers.usedToController.text = existingData.usedTo;
    }

    _otherSurnameControllers.add(controllers);
  }

  void _addOtherForenameControllers(OtherForenameData? existingData) {
    final controllers = OtherForenameControllers();

    if (existingData != null) {
      controllers.nameController.text = existingData.name;
      controllers.usedFromController.text = existingData.usedFrom;
      controllers.usedToController.text = existingData.usedTo;
    }

    _otherForenameControllers.add(controllers);
  }

  void _saveData() {
    final viewModel = ref.read(dbsFormViewModelProvider.notifier);
    final currentApplicant = ref
        .read(dbsFormViewModelProvider)
        .formData
        .applicantDetails;

    // Create other surnames list
    final otherSurnames = _hasUsedOtherSurnames
        ? _otherSurnameControllers
              .map((controllers) => controllers.toData())
              .toList()
        : <OtherSurnameData>[];

    // Create other forenames list
    final otherForenames = _hasUsedOtherForenames
        ? _otherForenameControllers
              .map((controllers) => controllers.toData())
              .toList()
        : <OtherForenameData>[];

    final updatedAdditionalDetails = currentApplicant.additionalApplicantDetails
        .copyWith(otherSurnames: otherSurnames, otherForenames: otherForenames);

    viewModel.updateAdditionalApplicantDetails(updatedAdditionalDetails);

    // Update birth surname until with smart calculation after saving other names
    viewModel.updateBirthSurnameUntilSmart();
  }

  void _saveDataAndUpdateBirthSurnameUntil() {
    // Save current data first
    _saveData();

    // Then update birth surname until with smart calculation
    Future.delayed(const Duration(milliseconds: 100), () {
      final viewModel = ref.read(dbsFormViewModelProvider.notifier);
      viewModel.updateBirthSurnameUntilSmart();
    });
  }

  @override
  Widget build(BuildContext context) {
    ref.listen<int>(formResetTriggerProvider, (previous, next) {
      if (next > 0 && previous != next) {
        resetFormData();
      }
    });

    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = ResponsiveUtil.isMobile(context);
    final isMobileLarge = ResponsiveUtil.isMobileLarge(context);
    final isTablet = ResponsiveUtil.isTablet(context);

    final horizontalPadding = _getHorizontalPadding(screenWidth);
    final verticalPadding = _getVerticalPadding(screenWidth);

    return Container(
      width: double.infinity,
      constraints: BoxConstraints(maxWidth: _getMaxWidth(screenWidth)),
      margin: EdgeInsets.symmetric(horizontal: isMobile ? 16 : 24, vertical: 8),
      padding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: verticalPadding,
      ),
      decoration: BoxDecoration(
        color: AppColors.kWhiteColor,
        borderRadius: BorderRadius.circular(isMobile ? 8 : 12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Section Title
              _buildSectionTitle(isMobile),

              SizedBox(height: isMobile ? 20 : 24),

              // Other Surnames Section
              _buildOtherSurnamesSection(isMobile, isMobileLarge, isTablet),

              SizedBox(height: isMobile ? 24 : 32),

              // Other Forenames Section
              _buildOtherForenamesSection(isMobile, isMobileLarge, isTablet),

              // Bottom spacing
              SizedBox(height: isMobile ? 16 : 24),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(bool isMobile) {
    return Container(
      padding: EdgeInsets.all(isMobile ? 16 : 20),
      decoration: BoxDecoration(
        color: AppColors.kBlueColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.kBlueColor.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          // Icon
          Icon(Icons.badge, color: AppColors.kBlueColor, size: 24),
          const SizedBox(width: 12),

          // Title and Description
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Other Names',
                  style: TextStyle(
                    fontSize: isMobile ? 16 : 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.kBlueColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'List any other surnames or forenames you have used',
                  style: TextStyle(
                    fontSize: isMobile ? 13 : 14,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  double _getHorizontalPadding(double screenWidth) {
    if (screenWidth <= 450) return 16; // Mobile
    if (screenWidth <= 800) return 20; // Mobile Large
    if (screenWidth <= 1200) return 24; // Tablet
    return 32; // Desktop
  }

  double _getVerticalPadding(double screenWidth) {
    if (screenWidth <= 450) return 20; // Mobile
    if (screenWidth <= 800) return 24; // Mobile Large
    return 28; // Tablet/Desktop
  }

  double _getMaxWidth(double screenWidth) {
    if (screenWidth <= 450) return double.infinity; // Mobile
    if (screenWidth <= 800) return 600; // Mobile Large
    if (screenWidth <= 1200) return 800; // Tablet
    return 1000; // Desktop
  }

  Widget _buildOtherSurnamesSection(
    bool isMobile,
    bool isMobileLarge,
    bool isTablet,
  ) {
    return Container(
      padding: EdgeInsets.all(isMobile ? 16 : 20),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          _buildSectionHeader('Other Surnames', Icons.badge_outlined, isMobile),

          SizedBox(height: isMobile ? 12 : 16),

          // Question: Have you used other surnames?
          DBSRadioGroup(
            title: 'Have you ever used a different surname?',
            value: _hasUsedOtherSurnames,
            onChanged: (value) {
              _trackFieldInteraction(
                'has_other_surnames',
                value.toString(),
                _hasUsedOtherSurnames.toString(),
              );
              setState(() {
                _hasUsedOtherSurnames = value;
                if (!value) {
                  // Clear all other surnames if user selects "No"
                  for (final controllers in _otherSurnameControllers) {
                    controllers.dispose();
                  }
                  _otherSurnameControllers.clear();
                } else if (_otherSurnameControllers.isEmpty) {
                  // Add first surname entry if user selects "Yes"
                  _addOtherSurnameControllers(null);
                }
              });
              // Update birth surname until when other surnames selection changes
              _saveDataAndUpdateBirthSurnameUntil();
            },
            isRequired: true,
          ),

          // Other surnames list (if applicable)
          if (_hasUsedOtherSurnames) ...[
            SizedBox(height: isMobile ? 16 : 20),

            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              child: Container(
                padding: EdgeInsets.all(isMobile ? 12 : 16),
                decoration: BoxDecoration(
                  color: AppColors.kWhiteColor,
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header with Add button
                    _buildListHeader(
                      'Other Surnames Used',
                      'Add Surname',
                      Icons.add,
                      () {
                        setState(() {
                          _addOtherSurnameControllers(null);
                        });
                        // Update birth surname until when other surnames change
                        _saveDataAndUpdateBirthSurnameUntil();
                      },
                      isMobile,
                    ),

                    SizedBox(height: isMobile ? 12 : 16),

                    // Display other surnames
                    for (
                      int i = 0;
                      i < _otherSurnameControllers.length;
                      i++
                    ) ...[
                      _buildOtherSurnameCard(
                        i,
                        isMobile,
                        isMobileLarge,
                        isTablet,
                      ),
                      if (i < _otherSurnameControllers.length - 1)
                        SizedBox(height: isMobile ? 12 : 16),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon, bool isMobile) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.kBlueColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(
            icon,
            color: AppColors.kBlueColor,
            size: isMobile ? 18 : 20,
          ),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: TextStyle(
            fontSize: isMobile ? 16 : 18,
            fontWeight: FontWeight.w600,
            color: AppColors.kBlackColor,
          ),
        ),
      ],
    );
  }

  Widget _buildListHeader(
    String title,
    String buttonText,
    IconData buttonIcon,
    VoidCallback onPressed,
    bool isMobile,
  ) {
    return Flex(
      direction: isMobile ? Axis.vertical : Axis.horizontal,
      mainAxisAlignment: isMobile
          ? MainAxisAlignment.start
          : MainAxisAlignment.spaceBetween,
      crossAxisAlignment: isMobile
          ? CrossAxisAlignment.start
          : CrossAxisAlignment.center,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: isMobile ? 13 : 14,
            fontWeight: FontWeight.w600,
            color: AppColors.kBlackColor,
          ),
        ),
        if (isMobile) const SizedBox(height: 8),
        ElevatedButton.icon(
          onPressed: onPressed,
          icon: Icon(buttonIcon, size: isMobile ? 14 : 16),
          label: Text(buttonText),
          style: ComponentConfig.primaryButtonStyle.copyWith(
            padding: WidgetStateProperty.all(
              EdgeInsets.symmetric(
                horizontal: isMobile ? 10 : 12,
                vertical: isMobile ? 6 : 8,
              ),
            ),
            textStyle: WidgetStateProperty.all(
              TextStyle(fontSize: isMobile ? 12 : 14),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildOtherForenamesSection(
    bool isMobile,
    bool isMobileLarge,
    bool isTablet,
  ) {
    return Container(
      padding: EdgeInsets.all(isMobile ? 16 : 20),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          _buildSectionHeader(
            'Other Forenames',
            Icons.person_pin_outlined,
            isMobile,
          ),

          SizedBox(height: isMobile ? 12 : 16),

          // Question: Have you used other forenames?
          DBSRadioGroup(
            title: 'Have you ever used a different forename?',
            value: _hasUsedOtherForenames,
            onChanged: (value) {
              _trackFieldInteraction(
                'has_other_forenames',
                value.toString(),
                _hasUsedOtherForenames.toString(),
              );
              setState(() {
                _hasUsedOtherForenames = value;
                if (!value) {
                  // Clear all other forenames if user selects "No"
                  for (final controllers in _otherForenameControllers) {
                    controllers.dispose();
                  }
                  _otherForenameControllers.clear();
                } else if (_otherForenameControllers.isEmpty) {
                  // Add first forename entry if user selects "Yes"
                  _addOtherForenameControllers(null);
                }
              });
              _saveData();
            },
            isRequired: true,
          ),

          // Other forenames list (if applicable)
          if (_hasUsedOtherForenames) ...[
            SizedBox(height: isMobile ? 16 : 20),

            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              child: Container(
                padding: EdgeInsets.all(isMobile ? 12 : 16),
                decoration: BoxDecoration(
                  color: AppColors.kWhiteColor,
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header with Add button
                    _buildListHeader(
                      'Other Forenames Used',
                      'Add Forename',
                      Icons.add,
                      () {
                        setState(() {
                          _addOtherForenameControllers(null);
                        });
                      },
                      isMobile,
                    ),

                    SizedBox(height: isMobile ? 12 : 16),

                    // Display other forenames
                    for (
                      int i = 0;
                      i < _otherForenameControllers.length;
                      i++
                    ) ...[
                      _buildOtherForenameCard(
                        i,
                        isMobile,
                        isMobileLarge,
                        isTablet,
                      ),
                      if (i < _otherForenameControllers.length - 1)
                        SizedBox(height: isMobile ? 12 : 16),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildOtherSurnameCard(
    int index,
    bool isMobile,
    bool isMobileLarge,
    bool isTablet,
  ) {
    final controllers = _otherSurnameControllers[index];

    return Container(
      padding: EdgeInsets.all(isMobile ? 12 : 16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.05),
            spreadRadius: 1,
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Card Header
          _buildCardHeader('Surname ${index + 1}', () {
            setState(() {
              controllers.dispose();
              _otherSurnameControllers.removeAt(index);
            });
            // Update birth surname until when other surnames change
            _saveDataAndUpdateBirthSurnameUntil();
          }, isMobile),

          SizedBox(height: isMobile ? 8 : 12),

          // Fields based on screen size
          if (isMobile) ...[
            _buildOtherSurnameFieldsMobile(controllers),
          ] else if (isMobileLarge || isTablet) ...[
            _buildOtherSurnameFieldsTablet(controllers),
          ] else ...[
            _buildOtherSurnameFieldsDesktop(controllers),
          ],
        ],
      ),
    );
  }

  Widget _buildCardHeader(String title, VoidCallback onDelete, bool isMobile) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            title,
            style: TextStyle(
              fontSize: isMobile ? 13 : 14,
              fontWeight: FontWeight.w600,
              color: AppColors.kBlueColor,
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: Colors.red.shade50,
            borderRadius: BorderRadius.circular(4),
          ),
          child: IconButton(
            onPressed: onDelete,
            icon: Icon(
              Icons.delete_outline,
              color: Colors.red.shade600,
              size: isMobile ? 18 : 20,
            ),
            padding: EdgeInsets.all(isMobile ? 4 : 6),
            constraints: const BoxConstraints(),
            tooltip: 'Remove',
          ),
        ),
      ],
    );
  }

  Widget _buildOtherForenameCard(
    int index,
    bool isMobile,
    bool isMobileLarge,
    bool isTablet,
  ) {
    final controllers = _otherForenameControllers[index];

    return Container(
      padding: EdgeInsets.all(isMobile ? 12 : 16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.05),
            spreadRadius: 1,
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Card Header
          _buildCardHeader('Forename ${index + 1}', () {
            setState(() {
              controllers.dispose();
              _otherForenameControllers.removeAt(index);
            });
            _saveData();
          }, isMobile),

          SizedBox(height: isMobile ? 8 : 12),

          // Fields based on screen size
          if (isMobile) ...[
            _buildOtherForenameFieldsMobile(controllers),
          ] else if (isMobileLarge || isTablet) ...[
            _buildOtherForenameFieldsTablet(controllers),
          ] else ...[
            _buildOtherForenameFieldsDesktop(controllers),
          ],
        ],
      ),
    );
  }

  Widget _buildOtherSurnameFieldsMobile(OtherSurnameControllers controllers) {
    return Column(
      children: [
        // Surname Name
        DBSTextField(
          controller: controllers.nameController,
          label: 'Surname',
          isRequired: true,
          onChanged: (value) {
            _trackFieldInteraction(
              'surname_name',
              value,
              controllers.nameController.text,
            );
            _saveData();
          },
          validator: (value) {
            if (!_shouldShowValidationError('surname_name')) return null;
            if (value == null || value.trim().isEmpty) {
              return 'Surname is required';
            }
            return null;
          },
        ),
        const SizedBox(height: 12),

        // Used From Year
        DBSTextField(
          controller: controllers.usedFromController,
          label: 'Used From (Year)',
          isRequired: true,
          keyboardType: TextInputType.number,
          onChanged: (value) {
            _trackFieldInteraction(
              'surname_used_from',
              value,
              controllers.usedFromController.text,
            );
            _saveData();
          },
          validator: (value) {
            if (!_shouldShowValidationError('surname_used_from')) return null;
            if (value == null || value.trim().isEmpty) {
              return 'Used from year is required';
            }
            if (int.tryParse(value) == null) {
              return 'Please enter a valid year';
            }
            return null;
          },
        ),
        const SizedBox(height: 12),

        // Used To Year
        DBSTextField(
          controller: controllers.usedToController,
          label: 'Used To (Year)',
          isRequired: true,
          keyboardType: TextInputType.number,
          onChanged: (value) {
            _trackFieldInteraction(
              'surname_used_to',
              value,
              controllers.usedToController.text,
            );
            _saveData();
          },
          validator: (value) {
            if (!_shouldShowValidationError('surname_used_to')) return null;
            if (value == null || value.trim().isEmpty) {
              return 'Used to year is required';
            }
            if (int.tryParse(value) == null) {
              return 'Please enter a valid year';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildOtherSurnameFieldsTablet(OtherSurnameControllers controllers) {
    return Column(
      children: [
        // Surname Name (full width)
        DBSTextField(
          controller: controllers.nameController,
          label: 'Surname',
          isRequired: true,
          onChanged: (value) => _saveData(),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Surname is required';
            }
            return null;
          },
        ),
        const SizedBox(height: 12),

        // Used From and Used To (side by side)
        Row(
          children: [
            Expanded(
              child: DBSTextField(
                controller: controllers.usedFromController,
                label: 'Used From (Year)',
                isRequired: true,
                keyboardType: TextInputType.number,
                onChanged: (value) => _saveData(),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Used from year is required';
                  }
                  if (int.tryParse(value) == null) {
                    return 'Please enter a valid year';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: DBSTextField(
                controller: controllers.usedToController,
                label: 'Used To (Year)',
                isRequired: true,
                keyboardType: TextInputType.number,
                onChanged: (value) => _saveData(),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Used to year is required';
                  }
                  if (int.tryParse(value) == null) {
                    return 'Please enter a valid year';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOtherSurnameFieldsDesktop(OtherSurnameControllers controllers) {
    return Row(
      children: [
        // Surname Name (40% width)
        Expanded(
          flex: 2,
          child: DBSTextField(
            controller: controllers.nameController,
            label: 'Surname',
            isRequired: true,
            onChanged: (value) => _saveData(),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Surname is required';
              }
              return null;
            },
          ),
        ),
        const SizedBox(width: 16),

        // Used From (30% width)
        Expanded(
          flex: 1,
          child: DBSTextField(
            controller: controllers.usedFromController,
            label: 'Used From (Year)',
            isRequired: true,
            keyboardType: TextInputType.number,
            onChanged: (value) => _saveData(),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Used from year is required';
              }
              if (int.tryParse(value) == null) {
                return 'Please enter a valid year';
              }
              return null;
            },
          ),
        ),
        const SizedBox(width: 16),

        // Used To (30% width)
        Expanded(
          flex: 1,
          child: DBSTextField(
            controller: controllers.usedToController,
            label: 'Used To (Year)',
            isRequired: true,
            keyboardType: TextInputType.number,
            onChanged: (value) => _saveData(),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Used to year is required';
              }
              if (int.tryParse(value) == null) {
                return 'Please enter a valid year';
              }
              return null;
            },
          ),
        ),
      ],
    );
  }

  Widget _buildOtherForenameFieldsMobile(OtherForenameControllers controllers) {
    return Column(
      children: [
        // Forename Name
        DBSTextField(
          controller: controllers.nameController,
          label: 'Forename',
          isRequired: true,
          onChanged: (value) => _saveData(),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Forename is required';
            }
            return null;
          },
        ),
        const SizedBox(height: 12),

        // Used From Year
        DBSTextField(
          controller: controllers.usedFromController,
          label: 'Used From (Year)',
          isRequired: true,
          keyboardType: TextInputType.number,
          onChanged: (value) => _saveData(),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Used from year is required';
            }
            if (int.tryParse(value) == null) {
              return 'Please enter a valid year';
            }
            return null;
          },
        ),
        const SizedBox(height: 12),

        // Used To Year
        DBSTextField(
          controller: controllers.usedToController,
          label: 'Used To (Year)',
          isRequired: true,
          keyboardType: TextInputType.number,
          onChanged: (value) => _saveData(),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Used to year is required';
            }
            if (int.tryParse(value) == null) {
              return 'Please enter a valid year';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildOtherForenameFieldsTablet(OtherForenameControllers controllers) {
    return Column(
      children: [
        // Forename Name (full width)
        DBSTextField(
          controller: controllers.nameController,
          label: 'Forename',
          isRequired: true,
          onChanged: (value) => _saveData(),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Forename is required';
            }
            return null;
          },
        ),
        const SizedBox(height: 12),

        // Used From and Used To (side by side)
        Row(
          children: [
            Expanded(
              child: DBSTextField(
                controller: controllers.usedFromController,
                label: 'Used From (Year)',
                isRequired: true,
                keyboardType: TextInputType.number,
                onChanged: (value) => _saveData(),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Used from year is required';
                  }
                  if (int.tryParse(value) == null) {
                    return 'Please enter a valid year';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: DBSTextField(
                controller: controllers.usedToController,
                label: 'Used To (Year)',
                isRequired: true,
                keyboardType: TextInputType.number,
                onChanged: (value) => _saveData(),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Used to year is required';
                  }
                  if (int.tryParse(value) == null) {
                    return 'Please enter a valid year';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOtherForenameFieldsDesktop(
    OtherForenameControllers controllers,
  ) {
    return Row(
      children: [
        // Forename Name (40% width)
        Expanded(
          flex: 2,
          child: DBSTextField(
            controller: controllers.nameController,
            label: 'Forename',
            isRequired: true,
            onChanged: (value) => _saveData(),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Forename is required';
              }
              return null;
            },
          ),
        ),
        const SizedBox(width: 16),

        // Used From (30% width)
        Expanded(
          flex: 1,
          child: DBSTextField(
            controller: controllers.usedFromController,
            label: 'Used From (Year)',
            isRequired: true,
            keyboardType: TextInputType.number,
            onChanged: (value) => _saveData(),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Used from year is required';
              }
              if (int.tryParse(value) == null) {
                return 'Please enter a valid year';
              }
              return null;
            },
          ),
        ),
        const SizedBox(width: 16),

        // Used To (30% width)
        Expanded(
          flex: 1,
          child: DBSTextField(
            controller: controllers.usedToController,
            label: 'Used To (Year)',
            isRequired: true,
            keyboardType: TextInputType.number,
            onChanged: (value) => _saveData(),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Used to year is required';
              }
              if (int.tryParse(value) == null) {
                return 'Please enter a valid year';
              }
              return null;
            },
          ),
        ),
      ],
    );
  }

  void resetFormData() {
    setState(() {
      _hasUsedOtherSurnames = false;
      _hasUsedOtherForenames = false;

      for (final controllers in _otherSurnameControllers) {
        controllers.dispose();
      }
      _otherSurnameControllers.clear();

      for (final controllers in _otherForenameControllers) {
        controllers.dispose();
      }
      _otherForenameControllers.clear();
    });

    _saveData();
  }

  bool validateForm() {
    setState(() {
      _hasTriedToSubmit = true;
    });
    _saveData();
    final isValid = _formKey.currentState?.validate() ?? false;
    return isValid;
  }

  Map<String, String> getValidationErrors() {
    final errors = <String, String>{};

    if (_hasUsedOtherSurnames) {
      for (int i = 0; i < _otherSurnameControllers.length; i++) {
        final controllers = _otherSurnameControllers[i];
        if (controllers.nameController.text.trim().isEmpty) {
          errors['validation'] =
              'Please complete all surname entries or remove empty ones.';
          break;
        }
        if (controllers.usedFromController.text.trim().isEmpty) {
          errors['validation'] =
              'Please complete all "Used From" fields for surnames.';
          break;
        }
      }
    }

    if (_hasUsedOtherForenames) {
      for (int i = 0; i < _otherForenameControllers.length; i++) {
        final controllers = _otherForenameControllers[i];
        if (controllers.nameController.text.trim().isEmpty) {
          errors['validation'] =
              'Please complete all forename entries or remove empty ones.';
          break;
        }
        if (controllers.usedFromController.text.trim().isEmpty) {
          errors['validation'] =
              'Please complete all "Used From" fields for forenames.';
          break;
        }
      }
    }

    return errors;
  }
}
