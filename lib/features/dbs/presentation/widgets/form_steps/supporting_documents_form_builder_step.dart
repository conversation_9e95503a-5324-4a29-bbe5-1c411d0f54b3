import 'package:SolidCheck/features/dbs/data/models/dbs_form_data.dart';
import 'package:SolidCheck/features/dbs/presentation/viewmodels/dbs_form_viewmodel.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/form_fields/dbs_form_builder_fields.dart';
import 'package:SolidCheck/features/dbs/providers/form_reset_provider.dart';
import 'package:SolidCheck/features/dbs/providers/form_validation_provider.dart';
import 'package:SolidCheck/shared/data/country_data.dart';
import 'package:SolidCheck/shared/utils/responsive.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:form_builder_validators/form_builder_validators.dart';

class SupportingDocumentsFormBuilderStep extends ConsumerStatefulWidget {
  const SupportingDocumentsFormBuilderStep({super.key});

  @override
  SupportingDocumentsFormBuilderStepState createState() => SupportingDocumentsFormBuilderStepState();
}

class SupportingDocumentsFormBuilderStepState extends ConsumerState<SupportingDocumentsFormBuilderStep> {
  final GlobalKey<FormBuilderState> _formKey = GlobalKey<FormBuilderState>();
  bool _hasPassport = false;
  bool _hasDrivingLicence = false;
  bool _hasNationalInsurance = false;
  
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Ensure validation is disabled when this step loads
      ref.read(formValidationModeProvider.notifier).disableValidation();

      _loadExistingData();
    });
  }

  void _loadExistingData() {
    final formState = ref.read(dbsFormViewModelProvider);
    final applicant = formState.formData.applicantDetails;
    final identityDetails = applicant.applicantIdentityDetails;
    
    setState(() {
      _hasPassport = identityDetails.passportDetails != null;
      _hasDrivingLicence = identityDetails.driverLicenceDetails != null;
      _hasNationalInsurance = identityDetails.nationalInsuranceNumber.isNotEmpty;
    });
    
    final Map<String, dynamic> formValues = <String, dynamic>{
      'hasNationalInsurance': _hasNationalInsurance ? 'y' : 'n',
      'hasPassport': _hasPassport ? 'y' : 'n',
      'hasDrivingLicence': _hasDrivingLicence ? 'y' : 'n',
    };

    // Only add National Insurance Number if it's not empty
    if (identityDetails.nationalInsuranceNumber.isNotEmpty) {
      formValues['nationalInsuranceNumber'] = identityDetails.nationalInsuranceNumber;
    }

    if (_hasPassport && identityDetails.passportDetails != null) {
      // Parse passport DOB with error handling
      DateTime? passportDob;
      if (identityDetails.passportDetails!.passportDob.isNotEmpty) {
        try {
          passportDob = DateTime.parse(identityDetails.passportDetails!.passportDob);
        } catch (e) {
          passportDob = null;
        }
      }

      // Parse passport issue date with error handling
      DateTime? passportIssueDate;
      if (identityDetails.passportDetails!.passportIssueDate.isNotEmpty) {
        try {
          passportIssueDate = DateTime.parse(identityDetails.passportDetails!.passportIssueDate);
        } catch (e) {
          passportIssueDate = null;
        }
      }

      // Only add passport values that are not empty
      if (identityDetails.passportDetails!.passportNumber.isNotEmpty) {
        formValues['passportNumber'] = identityDetails.passportDetails!.passportNumber;
      }
      if (passportDob != null) {
        formValues['passportDob'] = passportDob;
      }
      if (identityDetails.passportDetails!.passportNationality.isNotEmpty) {
        formValues['passportNationality'] = identityDetails.passportDetails!.passportNationality;
      }
      if (passportIssueDate != null) {
        formValues['passportIssueDate'] = passportIssueDate;
      }
    }

    if (_hasDrivingLicence && identityDetails.driverLicenceDetails != null) {
      // Parse driver license DOB with error handling
      DateTime? driverLicenceDOB;
      if (identityDetails.driverLicenceDetails!.driverLicenceDOB.isNotEmpty) {
        try {
          driverLicenceDOB = DateTime.parse(identityDetails.driverLicenceDetails!.driverLicenceDOB);
        } catch (e) {
          driverLicenceDOB = null;
        }
      }

      // Parse driver license valid from with error handling
      DateTime? driverLicenceValidFrom;
      if (identityDetails.driverLicenceDetails!.driverLicenceValidFrom.isNotEmpty) {
        try {
          driverLicenceValidFrom = DateTime.parse(identityDetails.driverLicenceDetails!.driverLicenceValidFrom);
        } catch (e) {
          driverLicenceValidFrom = null;
        }
      }

      // Only add driver license values that are not empty
      if (identityDetails.driverLicenceDetails!.driverLicenceNumber.isNotEmpty) {
        formValues['driverLicenceNumber'] = identityDetails.driverLicenceDetails!.driverLicenceNumber;
      }
      if (driverLicenceDOB != null) {
        formValues['driverLicenceDOB'] = driverLicenceDOB;
      }
      if (identityDetails.driverLicenceDetails!.driverLicenceType.isNotEmpty) {
        formValues['driverLicenceType'] = identityDetails.driverLicenceDetails!.driverLicenceType;
      }
      if (driverLicenceValidFrom != null) {
        formValues['driverLicenceValidFrom'] = driverLicenceValidFrom;
      }
      if (identityDetails.driverLicenceDetails!.driverLicenceIssueCountry.isNotEmpty) {
        formValues['driverLicenceIssueCountry'] = identityDetails.driverLicenceDetails!.driverLicenceIssueCountry;
      }
    }

    _formKey.currentState?.patchValue(formValues);
  }

  /// Helper method to enable responsive validation on user interaction
  void _enableValidationOnInteraction() {
    ref.read(formValidationModeProvider.notifier).enableOnUserInteraction();
  }

  void _saveData() {
    _formKey.currentState?.save();
    final formData = _formKey.currentState?.value ?? {};
    if (formData.isNotEmpty) {
      final formNotifier = ref.read(dbsFormViewModelProvider.notifier);
      
      final currentApplicant = ref.read(dbsFormViewModelProvider).formData.applicantDetails;
      final currentIdentityDetails = currentApplicant.applicantIdentityDetails;

      final updatedIdentityDetails = currentIdentityDetails.copyWith(
        evidenceCheckedBy: 'SYSTEM', // Default value since evidence verification section is removed
        nationalInsuranceNumber: _hasNationalInsurance ? formData['nationalInsuranceNumber']?.toString() ?? '' : '',
        passportDetails: _hasPassport ? PassportDetailsData(
          passportNumber: formData['passportNumber']?.toString() ?? '',
          passportDob: formData['passportDob'] != null
              ? (formData['passportDob'] as DateTime).toIso8601String().split('T')[0]
              : '',
          passportNationality: formData['passportNationality']?.toString() ?? '',
          passportIssueDate: formData['passportIssueDate'] != null
              ? (formData['passportIssueDate'] as DateTime).toIso8601String().split('T')[0]
              : '',
        ) : null,
        driverLicenceDetails: _hasDrivingLicence ? DriverLicenceDetailsData(
          driverLicenceNumber: formData['driverLicenceNumber']?.toString() ?? '',
          driverLicenceDOB: formData['driverLicenceDOB'] != null
              ? (formData['driverLicenceDOB'] as DateTime).toIso8601String().split('T')[0]
              : '',
          driverLicenceType: formData['driverLicenceType']?.toString() ?? '',
          driverLicenceValidFrom: formData['driverLicenceValidFrom'] != null
              ? (formData['driverLicenceValidFrom'] as DateTime).toIso8601String().split('T')[0]
              : '',
          driverLicenceIssueCountry: formData['driverLicenceIssueCountry']?.toString() ?? '',
        ) : null,
      );

      formNotifier.updateApplicantIdentityDetails(updatedIdentityDetails);
    }
  }

  String? _validatePassportDOB(DateTime? value) {
    if (!_hasPassport) return null;
    final formState = ref.read(dbsFormViewModelProvider);
    final applicant = formState.formData.applicantDetails;

    // Get the applicant's date of birth from birth details
    DateTime? applicantDOB;
    if (applicant.dateOfBirth.isNotEmpty) {
      try {
        applicantDOB = DateTime.parse(applicant.dateOfBirth);
      } catch (e) {
      }
    }

    return DBSValidators.passportDOB(applicantDOB)(value);
  }

  String? _validateDriverLicenceDOB(DateTime? value) {
    if (!_hasDrivingLicence) return null;
    final formState = ref.read(dbsFormViewModelProvider);
    final applicant = formState.formData.applicantDetails;

    // Get the applicant's date of birth from birth details
    DateTime? applicantDOB;
    if (applicant.dateOfBirth.isNotEmpty) {
      try {
        applicantDOB = DateTime.parse(applicant.dateOfBirth);
      } catch (e) {
      }
    }

    return DBSValidators.driverLicenceDOB(applicantDOB)(value);
  }

  String? _validateNationalInsuranceNumber(String? value) {
    if (!_hasNationalInsurance) return null;
    if (value == null || value.trim().isEmpty) {
      return 'National Insurance Number is required';
    }

    // Remove spaces and convert to uppercase
    final cleanValue = value.replaceAll(' ', '').toUpperCase();

    // UK National Insurance Number format: 2 letters, 6 digits, 1 letter
    // e.g. *********
    final niRegex = RegExp(r'^[A-Z]{2}[0-9]{6}[A-Z]$');

    if (!niRegex.hasMatch(cleanValue)) {
      return 'Please enter a valid National Insurance Number (e.g. *********)';
    }

    // Check for invalid prefixes
    final invalidPrefixes = ['BG', 'GB', 'NK', 'KN', 'TN', 'NT', 'ZZ'];
    final prefix = cleanValue.substring(0, 2);

    if (invalidPrefixes.contains(prefix)) {
      return 'Invalid National Insurance Number prefix';
    }

    return null;
  }

  @override
  Widget build(BuildContext context) {
    // Listen for reset triggers
    ref.listen<int>(formResetTriggerProvider, (previous, next) {
      if (next > 0 && previous != next) {
        resetFormData();
      }
    });

    final size = MediaQuery.of(context).size;
    final isMobile = ResponsiveUtil.isMobile(context);
    final isTablet = ResponsiveUtil.isTablet(context);
    final isDesktop = ResponsiveUtil.isDesktop(context);
    final validationMode = ref.watch(formValidationModeProvider);

    return Container(
      padding: EdgeInsets.all(isMobile ? 16 : isTablet ? 20 : 24),
      child: FormBuilder(
        key: _formKey,
        autovalidateMode: AutovalidateMode.disabled,
        onChanged: () {
          Future.delayed(const Duration(milliseconds: 100), () {
            _saveData();
          });
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Supporting Documents',
              style: TextStyle(
                fontSize: isMobile ? 20 : isTablet ? 22 : 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            SizedBox(height: isMobile ? 6 : 8),
            Text(
              'Please provide your supporting documents',
              style: TextStyle(
                fontSize: isMobile ? 14 : isTablet ? 15 : 16,
                color: Colors.grey.shade600,
              ),
            ),
            SizedBox(height: isMobile ? 20 : isTablet ? 22 : 24),

            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildNationalInsuranceSection(isMobile, isTablet, isDesktop),
                    SizedBox(height: isMobile ? 20 : isTablet ? 22 : 24),
                    _buildPassportSection(isMobile, isTablet, isDesktop),
                    SizedBox(height: isMobile ? 20 : isTablet ? 22 : 24),
                    _buildDrivingLicenceSection(isMobile, isTablet, isDesktop),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }



  Widget _buildNationalInsuranceSection(bool isMobile, bool isTablet, bool isDesktop) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'National Insurance Number',
          style: TextStyle(
            fontSize: isMobile ? 16 : isTablet ? 17 : 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        SizedBox(height: isMobile ? 12 : isTablet ? 14 : 16),

        DBSFormBuilderRadioGroup(
          name: 'hasNationalInsurance',
          label: 'Do you have a National Insurance Number?',
          isRequired: true,
          options: const [
            FormBuilderFieldOption(value: 'y', child: Text('Yes')),
            FormBuilderFieldOption(value: 'n', child: Text('No')),
          ],
          onChanged: (value) {
            // Enable responsive validation on first interaction
            ref.read(formValidationModeProvider.notifier).enableOnUserInteraction();

            setState(() {
              _hasNationalInsurance = value == 'y';
            });
            _saveData();
          },
        ),

        if (_hasNationalInsurance) ...[
          SizedBox(height: isMobile ? 12 : isTablet ? 14 : 16),
          DBSFormBuilderTextField(
            name: 'nationalInsuranceNumber',
            label: 'National Insurance Number',
            isRequired: true,
            maxLength: 13,
            hint: 'e.g. *********',
            validators: [
              FormBuilderValidators.required(errorText: 'National Insurance Number is required'),
              _validateNationalInsuranceNumber,
            ],
            onChanged: (value) {
              _enableValidationOnInteraction();
              _saveData();
            },
          ),
        ],
      ],
    );
  }

  Widget _buildPassportSection(bool isMobile, bool isTablet, bool isDesktop) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Passport',
          style: TextStyle(
            fontSize: isMobile ? 16 : isTablet ? 17 : 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        SizedBox(height: isMobile ? 12 : isTablet ? 14 : 16),

        DBSFormBuilderRadioGroup(
          name: 'hasPassport',
          label: 'Do you have a valid Passport?',
          isRequired: true,
          options: const [
            FormBuilderFieldOption(value: 'y', child: Text('Yes')),
            FormBuilderFieldOption(value: 'n', child: Text('No')),
          ],
          onChanged: (value) {
            // Enable responsive validation on first interaction
            ref.read(formValidationModeProvider.notifier).enableOnUserInteraction();

            setState(() {
              _hasPassport = value == 'y';
            });
            _saveData();
          },
        ),

        if (_hasPassport) ...[
          SizedBox(height: isMobile ? 12 : isTablet ? 14 : 16),
          _buildPassportFields(isMobile, isTablet, isDesktop),
        ],
      ],
    );
  }

  Widget _buildPassportFields(bool isMobile, bool isTablet, bool isDesktop) {
    if (isMobile) {
      return Column(
        children: [
          DBSFormBuilderTextField(
            name: 'passportNumber',
            label: 'Passport Number',
            isRequired: true,
            maxLength: 11,
            textCapitalization: TextCapitalization.characters,
            validators: [
              FormBuilderValidators.required(errorText: 'Passport number is required'),
              DBSValidators.passportNumber(),
            ],
            onChanged: (value) => _saveData(),
          ),
          const SizedBox(height: 16),
          DBSFormBuilderDatePicker(
            name: 'passportDob',
            label: 'Date of Birth (as shown on passport)',
            isRequired: true,
            validators: [_validatePassportDOB],
            onChanged: (value) => _saveData(),
          ),
          const SizedBox(height: 16),
          DBSFormBuilderDropdown<String>(
            name: 'passportNationality',
            label: 'Nationality (as shown on passport)',
            isRequired: true,
            hint: 'Select nationality',
            items: Countries.getNationalityDropdownItems(),
            validators: [
              FormBuilderValidators.required(errorText: 'Passport nationality is required'),
              DBSValidators.passportNationality(),
            ],
            onChanged: (value) => _saveData(),
          ),
          const SizedBox(height: 16),
          DBSFormBuilderDatePicker(
            name: 'passportIssueDate',
            label: 'Passport Issue Date',
            isRequired: true,
            lastDate: DateTime.now(),
            validators: [
              FormBuilderValidators.required(errorText: 'Passport issue date is required'),
              DBSValidators.passportIssueDate(),
            ],
            onChanged: (value) => _saveData(),
          ),
        ],
      );
    } else if (isTablet) {
      return Column(
        children: [
          Row(
            children: [
              Expanded(
                child: DBSFormBuilderTextField(
                  name: 'passportNumber',
                  label: 'Passport Number',
                  isRequired: true,
                  maxLength: 11,
                  textCapitalization: TextCapitalization.characters,
                  validators: [
                    FormBuilderValidators.required(errorText: 'Passport number is required'),
                    DBSValidators.passportNumber(),
                  ],
                  onChanged: (value) => _saveData(),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DBSFormBuilderDatePicker(
                  name: 'passportDob',
                  label: 'Date of Birth',
                  isRequired: true,
                  validators: [_validatePassportDOB],
                  onChanged: (value) => _saveData(),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DBSFormBuilderDropdown<String>(
                  name: 'passportNationality',
                  label: 'Nationality',
                  isRequired: true,
                  hint: 'Select nationality',
                  items: Countries.getNationalityDropdownItems(),
                  validators: [
                    FormBuilderValidators.required(errorText: 'Passport nationality is required'),
                    DBSValidators.passportNationality(),
                  ],
                  onChanged: (value) => _saveData(),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DBSFormBuilderDatePicker(
                  name: 'passportIssueDate',
                  label: 'Issue Date',
                  isRequired: true,
                  lastDate: DateTime.now(),
                  validators: [
                    FormBuilderValidators.required(errorText: 'Passport issue date is required'),
                    DBSValidators.passportIssueDate(),
                  ],
                  onChanged: (value) => _saveData(),
                ),
              ),
            ],
          ),
        ],
      );
    } else {
      return Column(
        children: [
          Row(
            children: [
              Expanded(
                child: DBSFormBuilderTextField(
                  name: 'passportNumber',
                  label: 'Passport Number',
                  isRequired: true,
                  maxLength: 11,
                  textCapitalization: TextCapitalization.characters,
                  validators: [
                    FormBuilderValidators.required(errorText: 'Passport number is required'),
                    DBSValidators.passportNumber(),
                  ],
                  onChanged: (value) => _saveData(),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DBSFormBuilderDatePicker(
                  name: 'passportDob',
                  label: 'Date of Birth',
                  isRequired: true,
                  validators: [_validatePassportDOB],
                  onChanged: (value) => _saveData(),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DBSFormBuilderDropdown<String>(
                  name: 'passportNationality',
                  label: 'Nationality',
                  isRequired: true,
                  hint: 'Select nationality',
                  items: Countries.getNationalityDropdownItems(),
                  validators: [
                    FormBuilderValidators.required(errorText: 'Passport nationality is required'),
                    DBSValidators.passportNationality(),
                  ],
                  onChanged: (value) => _saveData(),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DBSFormBuilderDatePicker(
                  name: 'passportIssueDate',
                  label: 'Issue Date',
                  isRequired: true,
                  lastDate: DateTime.now(),
                  validators: [
                    FormBuilderValidators.required(errorText: 'Passport issue date is required'),
                    DBSValidators.passportIssueDate(),
                  ],
                  onChanged: (value) => _saveData(),
                ),
              ),
              const Expanded(child: SizedBox()),
              const Expanded(child: SizedBox()),
            ],
          ),
        ],
      );
    }
  }

  Widget _buildDrivingLicenceSection(bool isMobile, bool isTablet, bool isDesktop) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Driving Licence',
          style: TextStyle(
            fontSize: isMobile ? 16 : isTablet ? 17 : 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        SizedBox(height: isMobile ? 12 : isTablet ? 14 : 16),

        DBSFormBuilderRadioGroup(
          name: 'hasDrivingLicence',
          label: 'Do you have a valid Driving Licence?',
          isRequired: true,
          options: const [
            FormBuilderFieldOption(value: 'y', child: Text('Yes')),
            FormBuilderFieldOption(value: 'n', child: Text('No')),
          ],
          onChanged: (value) {
            // Enable responsive validation on first interaction
            ref.read(formValidationModeProvider.notifier).enableOnUserInteraction();

            setState(() {
              _hasDrivingLicence = value == 'y';
            });
            _saveData();
          },
        ),

        if (_hasDrivingLicence) ...[
          SizedBox(height: isMobile ? 12 : isTablet ? 14 : 16),
          _buildDrivingLicenceFields(isMobile, isTablet, isDesktop),
        ],
      ],
    );
  }

  Widget _buildDrivingLicenceFields(bool isMobile, bool isTablet, bool isDesktop) {
    return Column(
      children: [
        if (isMobile) ...[
          DBSFormBuilderTextField(
            name: 'driverLicenceNumber',
            label: 'Driving Licence Number',
            isRequired: true,
            maxLength: 18,
            textCapitalization: TextCapitalization.characters,
            validators: [
              FormBuilderValidators.required(errorText: 'Driving licence number is required'),
              DBSValidators.driverLicenceNumber(),
            ],
            onChanged: (value) => _saveData(),
          ),
          const SizedBox(height: 16),
          DBSFormBuilderDatePicker(
            name: 'driverLicenceDOB',
            label: 'Date of Birth (as shown on licence)',
            isRequired: true,
            validators: [_validateDriverLicenceDOB],
            onChanged: (value) => _saveData(),
          ),
          const SizedBox(height: 16),
          DBSFormBuilderDropdown<String>(
            name: 'driverLicenceType',
            label: 'Licence Type',
            isRequired: true,
            items: const [
              DropdownMenuItem(value: 'paper', child: Text('Paper')),
              DropdownMenuItem(value: 'photo', child: Text('Photo')),
            ],
            validators: [
              FormBuilderValidators.required(errorText: 'Licence type is required'),
              DBSValidators.driverLicenceType(),
            ],
            onChanged: (value) => _saveData(),
          ),
          const SizedBox(height: 16),
          DBSFormBuilderDatePicker(
            name: 'driverLicenceValidFrom',
            label: 'Valid From Date',
            isRequired: true,
            lastDate: DateTime.now(),
            validators: [
              FormBuilderValidators.required(errorText: 'Valid from date is required'),
              DBSValidators.driverLicenceValidFrom(),
            ],
            onChanged: (value) => _saveData(),
          ),
          const SizedBox(height: 16),
          DBSFormBuilderDropdown<String>(
            name: 'driverLicenceIssueCountry',
            label: 'Issue Country',
            isRequired: true,
            hint: 'Select country',
            items: Countries.getCountryDropdownItems(),
            validators: [
              FormBuilderValidators.required(errorText: 'Issue country is required'),
              DBSValidators.driverLicenceIssueCountry(),
            ],
            onChanged: (value) => _saveData(),
          ),
        ] else if (isTablet) ...[
          Row(
            children: [
              Expanded(
                child: DBSFormBuilderTextField(
                  name: 'driverLicenceNumber',
                  label: 'Licence Number',
                  isRequired: true,
                  maxLength: 18,
                  textCapitalization: TextCapitalization.characters,
                  validators: [
                    FormBuilderValidators.required(errorText: 'Driving licence number is required'),
                    DBSValidators.driverLicenceNumber(),
                  ],
                  onChanged: (value) => _saveData(),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DBSFormBuilderDatePicker(
                  name: 'driverLicenceDOB',
                  label: 'Date of Birth',
                  isRequired: true,
                  validators: [_validateDriverLicenceDOB],
                  onChanged: (value) => _saveData(),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DBSFormBuilderDropdown<String>(
                  name: 'driverLicenceType',
                  label: 'Licence Type',
                  isRequired: true,
                  items: const [
                    DropdownMenuItem(value: 'paper', child: Text('Paper')),
                    DropdownMenuItem(value: 'photo', child: Text('Photo')),
                  ],
                  validators: [
                    FormBuilderValidators.required(errorText: 'Licence type is required'),
                    DBSValidators.driverLicenceType(),
                  ],
                  onChanged: (value) => _saveData(),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DBSFormBuilderDatePicker(
                  name: 'driverLicenceValidFrom',
                  label: 'Valid From',
                  isRequired: true,
                  lastDate: DateTime.now(),
                  validators: [
                    FormBuilderValidators.required(errorText: 'Valid from date is required'),
                    DBSValidators.driverLicenceValidFrom(),
                  ],
                  onChanged: (value) => _saveData(),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          DBSFormBuilderDropdown<String>(
            name: 'driverLicenceIssueCountry',
            label: 'Issue Country',
            isRequired: true,
            hint: 'Select country',
            items: Countries.getCountryDropdownItems(),
            validators: [
              FormBuilderValidators.required(errorText: 'Issue country is required'),
              DBSValidators.driverLicenceIssueCountry(),
            ],
            onChanged: (value) => _saveData(),
          ),
        ] else ...[
          // Desktop layout - 3 columns
          Row(
            children: [
              Expanded(
                child: DBSFormBuilderTextField(
                  name: 'driverLicenceNumber',
                  label: 'Licence Number',
                  isRequired: true,
                  maxLength: 18,
                  textCapitalization: TextCapitalization.characters,
                  validators: [
                    FormBuilderValidators.required(errorText: 'Driving licence number is required'),
                    DBSValidators.driverLicenceNumber(),
                  ],
                  onChanged: (value) => _saveData(),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DBSFormBuilderDatePicker(
                  name: 'driverLicenceDOB',
                  label: 'Date of Birth',
                  isRequired: true,
                  validators: [_validateDriverLicenceDOB],
                  onChanged: (value) => _saveData(),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DBSFormBuilderDropdown<String>(
                  name: 'driverLicenceType',
                  label: 'Licence Type',
                  isRequired: true,
                  items: const [
                    DropdownMenuItem(value: 'paper', child: Text('Paper')),
                    DropdownMenuItem(value: 'photo', child: Text('Photo')),
                  ],
                  validators: [
                    FormBuilderValidators.required(errorText: 'Licence type is required'),
                    DBSValidators.driverLicenceType(),
                  ],
                  onChanged: (value) => _saveData(),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DBSFormBuilderDatePicker(
                  name: 'driverLicenceValidFrom',
                  label: 'Valid From',
                  isRequired: true,
                  lastDate: DateTime.now(),
                  validators: [
                    FormBuilderValidators.required(errorText: 'Valid from date is required'),
                    DBSValidators.driverLicenceValidFrom(),
                  ],
                  onChanged: (value) => _saveData(),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DBSFormBuilderDropdown<String>(
                  name: 'driverLicenceIssueCountry',
                  label: 'Issue Country',
                  isRequired: true,
                  hint: 'Select country',
                  items: Countries.getCountryDropdownItems(),
                  validators: [
                    FormBuilderValidators.required(errorText: 'Issue country is required'),
                    DBSValidators.driverLicenceIssueCountry(),
                  ],
                  onChanged: (value) => _saveData(),
                ),
              ),
              const Expanded(child: SizedBox()),
            ],
          ),
        ],
      ],
    );
  }

  bool validateForm() {
    final formState = _formKey.currentState;
    if (formState == null) return false;

    // Save the form first to ensure all values are captured
    formState.save();

    // Then validate
    final isValid = formState.validate();

    // Debug: Print current form values
    final formValues = formState.value;

    return isValid;
  }

  Map<String, String> getValidationErrors() {
    final errors = <String, String>{};

    // Force save and validate
    _formKey.currentState?.save();
    final isValid = _formKey.currentState?.validate() ?? true;

    // If form is not valid, show the standard error message
    if (!isValid) {
      errors['validation'] = 'Please fix all validation issues before continuing into next step.';
    }

    return errors;
  }

  /// Reset all form data (called when "Start Fresh" is selected)
  void resetFormData() {

    setState(() {
      // Reset state variables
      _hasPassport = false;
      _hasDrivingLicence = false;
      _hasNationalInsurance = false;
    });

    // Clear all form fields
    _formKey.currentState?.patchValue({
      'hasNationalInsurance': 'n',
      'nationalInsuranceNumber': '',
      'hasPassport': 'n',
      'passportNumber': '',
      'passportDob': null,
      'passportNationality': null,
      'passportIssueDate': null,
      'hasDrivingLicence': 'n',
      'driverLicenceNumber': '',
      'driverLicenceDOB': null,
      'driverLicenceType': 'photo',
      'driverLicenceValidFrom': null,
      'driverLicenceIssueCountry': 'GB',
    });

    // Reset form validation state
    _formKey.currentState?.reset();

    // Save empty state to view model
    _saveData();

  }
}
