import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/dbs/presentation/viewmodels/dbs_form_viewmodel.dart';
import 'package:SolidCheck/shared/utils/responsive.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Review Step Widget
/// Sixth step of the DBS form for reviewing all entered information before submission
class ReviewStep extends ConsumerStatefulWidget {
  final VoidCallback? onEditStep;

  const ReviewStep({
    super.key,
    this.onEditStep,
  });

  @override
  ConsumerState<ReviewStep> createState() => _ReviewStepState();
}

class _ReviewStepState extends ConsumerState<ReviewStep> {
  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = ResponsiveUtil.isMobile(context);
    final isMobileLarge = ResponsiveUtil.isMobileLarge(context);
    final isTablet = ResponsiveUtil.isTablet(context);
    final formState = ref.watch(dbsFormViewModelProvider);
    final applicantDetails = formState.formData.applicantDetails;

    // Responsive padding
    final horizontalPadding = _getHorizontalPadding(screenWidth);
    final verticalPadding = _getVerticalPadding(screenWidth);

    return Container(
      width: double.infinity,
      constraints: BoxConstraints(
        maxWidth: _getMaxWidth(screenWidth),
      ),
      margin: EdgeInsets.symmetric(
        horizontal: isMobile ? 16 : 24,
        vertical: 8,
      ),
      padding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: verticalPadding,
      ),
      decoration: BoxDecoration(
        color: AppColors.kWhiteColor,
        borderRadius: BorderRadius.circular(isMobile ? 8 : 12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section Title
            _buildSectionTitle(isMobile),

            SizedBox(height: isMobile ? 6 : 8),

            _buildSubtitle(isMobile),

            SizedBox(height: isMobile ? 20 : 24),

            // Personal Details Section
            _buildReviewSection(
              title: 'Personal Details',
              stepIndex: 0,
              icon: Icons.person,
              color: Colors.blue,
              isMobile: isMobile,
              isMobileLarge: isMobileLarge,
              isTablet: isTablet,
              children: [
                _buildReviewItem('Title', applicantDetails.title, isMobile),
                _buildReviewItem('Forename', applicantDetails.forename, isMobile),
                if (applicantDetails.middlenames.isNotEmpty)
                  _buildReviewItem('Middle Names', applicantDetails.middlenames.join(', '), isMobile),
                _buildReviewItem('Present Surname', applicantDetails.presentSurname, isMobile),
                _buildReviewItem('Gender', applicantDetails.gender, isMobile),
                if (applicantDetails.niNumber.isNotEmpty)
                  _buildReviewItem('National Insurance Number', applicantDetails.niNumber, isMobile),
              ],
            ),

            SizedBox(height: isMobile ? 20 : 24),

            // Birth Details Section
            _buildReviewSection(
              title: 'Birth Details',
              stepIndex: 1,
              icon: Icons.location_on,
              color: Colors.blue,
              isMobile: isMobile,
              isMobileLarge: isMobileLarge,
              isTablet: isTablet,
              children: [
                _buildReviewItem('Date of Birth', applicantDetails.dateOfBirth, isMobile),
                _buildReviewItem('Birth Surname', applicantDetails.additionalApplicantDetails.birthSurname, isMobile),
                if (applicantDetails.additionalApplicantDetails.birthSurnameUntil.isNotEmpty)
                  _buildReviewItem('Birth Surname Until', applicantDetails.additionalApplicantDetails.birthSurnameUntil, isMobile),
                _buildReviewItem('Birth Town', applicantDetails.additionalApplicantDetails.birthTown, isMobile),
                _buildReviewItem('Birth County', applicantDetails.additionalApplicantDetails.birthCounty, isMobile),
                _buildReviewItem('Birth Country', applicantDetails.additionalApplicantDetails.birthCountry, isMobile),
                _buildReviewItem('Birth Nationality', applicantDetails.additionalApplicantDetails.birthNationality, isMobile),
                _buildReviewItem('Language Preference', applicantDetails.additionalApplicantDetails.languagePreference, isMobile),
              ],
            ),

            SizedBox(height: isMobile ? 20 : 24),

            // Address Details Section
            _buildReviewSection(
              title: 'Address Details',
              stepIndex: 2,
              icon: Icons.home,
              color: Colors.blue,
              isMobile: isMobile,
              isMobileLarge: isMobileLarge,
              isTablet: isTablet,
              children: [
                _buildReviewItem('Current Address', _formatAddress(applicantDetails.currentAddress), isMobile),
                if (applicantDetails.currentAddress.residentFromGyearMonth.isNotEmpty)
                  _buildReviewItem('Resident From', _formatYearMonth(applicantDetails.currentAddress.residentFromGyearMonth), isMobile),
                if (applicantDetails.previousAddresses.isNotEmpty) ...[
                  SizedBox(height: isMobile ? 6 : 8),
                  Text(
                    'Previous Addresses:',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: isMobile ? 13 : 14,
                    ),
                  ),
                  for (int i = 0; i < applicantDetails.previousAddresses.length; i++)
                    _buildReviewItem(
                      'Previous Address ${i + 1}',
                      _formatAddress(applicantDetails.previousAddresses[i]),
                      isMobile,
                    ),
                ],
              ],
            ),

            SizedBox(height: isMobile ? 20 : 24),

            // Other Names Section
            _buildReviewSection(
              title: 'Other Names',
              stepIndex: 3,
              icon: Icons.badge_outlined,
              color: Colors.blue,
              isMobile: isMobile,
              isMobileLarge: isMobileLarge,
              isTablet: isTablet,
              children: [
                if (applicantDetails.additionalApplicantDetails.otherSurnames.isNotEmpty) ...[
                  Text(
                    'Other Surnames:',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: isMobile ? 13 : 14,
                    ),
                  ),
                  for (final surname in applicantDetails.additionalApplicantDetails.otherSurnames)
                    _buildReviewItem(
                      surname.name,
                      '${surname.usedFrom} - ${surname.usedTo}',
                      isMobile,
                    ),
                  SizedBox(height: isMobile ? 6 : 8),
                ],
                if (applicantDetails.additionalApplicantDetails.otherForenames.isNotEmpty) ...[
                  Text(
                    'Other Forenames:',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: isMobile ? 13 : 14,
                    ),
                  ),
                  for (final forename in applicantDetails.additionalApplicantDetails.otherForenames)
                    _buildReviewItem(
                      forename.name,
                      '${forename.usedFrom} - ${forename.usedTo}',
                      isMobile,
                    ),
                ],
                if (applicantDetails.additionalApplicantDetails.otherSurnames.isEmpty &&
                    applicantDetails.additionalApplicantDetails.otherForenames.isEmpty)
                  Text(
                    'No other names provided',
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: isMobile ? 13 : 14,
                    ),
                  ),
              ],
            ),

            SizedBox(height: isMobile ? 20 : 24),

            // Supporting Documents Section
            _buildReviewSection(
              title: 'Supporting Documents',
              stepIndex: 4,
              icon: Icons.description,
              color: Colors.blue,
              isMobile: isMobile,
              isMobileLarge: isMobileLarge,
              isTablet: isTablet,
              children: [
                if (applicantDetails.niNumber.isNotEmpty)
                  _buildReviewItem('National Insurance Number', applicantDetails.niNumber, isMobile),
                if (applicantDetails.applicantIdentityDetails.passportDetails != null) ...[
                  SizedBox(height: isMobile ? 6 : 8),
                  Text(
                    'Passport Details:',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: isMobile ? 13 : 14,
                    ),
                  ),
                  _buildReviewItem('Passport Number', applicantDetails.applicantIdentityDetails.passportDetails!.passportNumber, isMobile),
                  _buildReviewItem('Passport Nationality', applicantDetails.applicantIdentityDetails.passportDetails!.passportNationality, isMobile),
                ],
                if (applicantDetails.applicantIdentityDetails.driverLicenceDetails != null) ...[
                  SizedBox(height: isMobile ? 6 : 8),
                  Text(
                    'Driving License Details:',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: isMobile ? 13 : 14,
                    ),
                  ),
                  _buildReviewItem('License Number', applicantDetails.applicantIdentityDetails.driverLicenceDetails!.driverLicenceNumber, isMobile),
                  _buildReviewItem('License Type', applicantDetails.applicantIdentityDetails.driverLicenceDetails!.driverLicenceType, isMobile),
                ],
                if (applicantDetails.niNumber.isEmpty &&
                    applicantDetails.applicantIdentityDetails.passportDetails == null &&
                    applicantDetails.applicantIdentityDetails.driverLicenceDetails == null)
                  Text(
                    'No supporting documents provided',
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: isMobile ? 13 : 14,
                    ),
                  ),
              ],
            ),

            // Bottom spacing
            SizedBox(height: isMobile ? 16 : 24),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(bool isMobile) {
    return Container(
      padding: EdgeInsets.all(isMobile ? 16 : 20),
      decoration: BoxDecoration(
        color: AppColors.kBlueColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.kBlueColor.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          // Icon
          Icon(
            Icons.preview,
            color: AppColors.kBlueColor,
            size: 24,
          ),
          const SizedBox(width: 12),

          // Title and Description
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Review Your Application',
                  style: TextStyle(
                    fontSize: isMobile ? 16 : 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.kBlueColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Check all information before submitting',
                  style: TextStyle(
                    fontSize: isMobile ? 13 : 14,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubtitle(bool isMobile) {
    return Text(
      'Please review all the information below before submitting your application.',
      style: TextStyle(
        fontSize: isMobile ? 13 : 14,
        color: Colors.grey,
      ),
    );
  }

  double _getHorizontalPadding(double screenWidth) {
    if (screenWidth <= 450) return 16; // Mobile
    if (screenWidth <= 800) return 20; // Mobile Large
    if (screenWidth <= 1200) return 24; // Tablet
    return 32; // Desktop
  }

  double _getVerticalPadding(double screenWidth) {
    if (screenWidth <= 450) return 20; // Mobile
    if (screenWidth <= 800) return 24; // Mobile Large
    return 28; // Tablet/Desktop
  }

  double _getMaxWidth(double screenWidth) {
    if (screenWidth <= 450) return double.infinity; // Mobile
    if (screenWidth <= 800) return 600; // Mobile Large
    if (screenWidth <= 1200) return 800; // Tablet
    return 1000; // Desktop
  }

  Widget _buildReviewSection({
    required String title,
    required int stepIndex,
    required IconData icon,
    required MaterialColor color,
    required bool isMobile,
    required bool isMobileLarge,
    required bool isTablet,
    required List<Widget> children,
  }) {
    return Container(
      padding: EdgeInsets.all(isMobile ? 14 : 16),
      decoration: BoxDecoration(
        color: color.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          _buildSectionHeader(title, icon, color, isMobile),
          SizedBox(height: isMobile ? 10 : 12),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon, MaterialColor color, bool isMobile) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(
            icon,
            color: color,
            size: isMobile ? 16 : 18,
          ),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Text(
            title,
            style: TextStyle(
              fontSize: isMobile ? 15 : 16,
              fontWeight: FontWeight.w600,
              color: color.shade700,
            ),
          ),
        ),
        if (widget.onEditStep != null)
          Container(
            decoration: BoxDecoration(
              color: AppColors.kBlueColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: TextButton.icon(
              onPressed: widget.onEditStep,
              icon: Icon(Icons.edit, size: isMobile ? 14 : 16),
              label: Text(
                'Edit',
                style: TextStyle(fontSize: isMobile ? 12 : 14),
              ),
              style: TextButton.styleFrom(
                foregroundColor: AppColors.kBlueColor,
                padding: EdgeInsets.symmetric(
                  horizontal: isMobile ? 6 : 8,
                  vertical: isMobile ? 2 : 4,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildReviewItem(String label, String value, bool isMobile) {
    final displayValue = value.isNotEmpty ? value : 'Not provided';

    if (isMobile) {
      // Mobile: Stack layout
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 6),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey.shade600,
                fontSize: 13,
              ),
            ),
            const SizedBox(height: 2),
            Text(
              displayValue,
              style: TextStyle(
                color: value.isNotEmpty ? AppColors.kBlackColor : Colors.grey,
                fontStyle: value.isNotEmpty ? FontStyle.normal : FontStyle.italic,
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    } else {
      // Desktop/Tablet: Side by side layout
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: 160,
              child: Text(
                '$label:',
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  color: Colors.grey.shade600,
                  fontSize: 14,
                ),
              ),
            ),
            Expanded(
              child: Text(
                displayValue,
                style: TextStyle(
                  color: value.isNotEmpty ? AppColors.kBlackColor : Colors.grey,
                  fontStyle: value.isNotEmpty ? FontStyle.normal : FontStyle.italic,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
      );
    }
  }

  String _formatAddress(dynamic address) {
    if (address == null) return 'Not provided';

    final parts = <String>[];

    if (address.addressLine1.isNotEmpty) parts.add(address.addressLine1);
    if (address.addressLine2.isNotEmpty) parts.add(address.addressLine2);
    if (address.addressTown.isNotEmpty) parts.add(address.addressTown);
    if (address.addressCounty.isNotEmpty) parts.add(address.addressCounty);
    if (address.postcode.isNotEmpty) parts.add(address.postcode);

    return parts.isNotEmpty ? parts.join(', ') : 'Not provided';
  }

  String _formatYearMonth(String yearMonth) {
    if (yearMonth.isEmpty) return 'Not provided';

    try {
      final parts = yearMonth.split('-');
      if (parts.length == 2) {
        final year = parts[0];
        final month = int.parse(parts[1]);
        final monthNames = [
          '', 'January', 'February', 'March', 'April', 'May', 'June',
          'July', 'August', 'September', 'October', 'November', 'December'
        ];
        return '${monthNames[month]} $year';
      }
    } catch (e) {
      // Handle invalid format
    }

    return yearMonth;
  }
}
