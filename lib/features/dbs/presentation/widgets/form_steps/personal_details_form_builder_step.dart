import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/dbs/presentation/viewmodels/dbs_form_viewmodel.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/form_fields/dbs_form_builder_fields.dart';
import 'package:SolidCheck/features/dbs/providers/form_reset_provider.dart';
import 'package:SolidCheck/features/dbs/providers/form_validation_provider.dart';
import 'package:SolidCheck/shared/utils/responsive.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:form_builder_validators/form_builder_validators.dart';

class PersonalDetailsFormBuilderStep extends ConsumerStatefulWidget {
  const PersonalDetailsFormBuilderStep({super.key});

  @override
  PersonalDetailsFormBuilderStepState createState() => PersonalDetailsFormBuilderStepState();
}

class PersonalDetailsFormBuilderStepState extends ConsumerState<PersonalDetailsFormBuilderStep> with AutomaticKeepAliveClientMixin {
  final GlobalKey<FormBuilderState> _formKey = GlobalKey<FormBuilderState>();
  bool isMiddleNameEnabled = false;
  List<String> middleNames = [];
  int middleNameCount = 0;
  bool _isDataLoaded = false;
  bool _isLoadingData = false;
  bool hasMiddleNames = false;
  final Set<String> _fieldsWithRemovedContent = <String>{};
  final Set<String> _fieldsInteracted = <String>{};
  bool _hasTriedToSubmit = false;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(formValidationModeProvider.notifier).disableValidation();
      if (!_isDataLoaded) {
        _loadExistingData();
        _isDataLoaded = true;
      }
    });
  }

  void forceReloadData() {
    _isDataLoaded = false;
    _loadExistingData();
    _isDataLoaded = true;
  }

  void _loadExistingData() {
    if (!mounted || _isLoadingData) return;

    _isLoadingData = true;

    final formState = ref.read(dbsFormViewModelProvider);
    final applicant = formState.formData.applicantDetails;


    setState(() {
      middleNames = List<String>.from(applicant.middlenames);
      hasMiddleNames = middleNames.isNotEmpty;
      middleNameCount = hasMiddleNames ? (middleNames.isNotEmpty ? middleNames.length : 1) : 0;
    });

    final Map<String, dynamic> formValues = <String, dynamic>{};

    if (applicant.title.isNotEmpty) formValues['title'] = applicant.title;
    if (applicant.forename.isNotEmpty) formValues['forename'] = applicant.forename;
    if (applicant.presentSurname.isNotEmpty) formValues['presentSurname'] = applicant.presentSurname;
    if (applicant.gender.isNotEmpty) formValues['gender'] = applicant.gender;
    if (applicant.contactNumber.isNotEmpty) formValues['contactNumber'] = applicant.contactNumber;
    if (applicant.email.isNotEmpty) formValues['email'] = applicant.email;

    formValues['hasMiddleNames'] = hasMiddleNames ? 'yes' : 'no';

    for (int i = 0; i < middleNames.length && i < 3; i++) {
      formValues['middlename${i + 1}'] = middleNames[i];
    }


    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _formKey.currentState?.patchValue(formValues);

        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) {
            final currentValues = _formKey.currentState?.value;
          }
        });

        Future.delayed(const Duration(milliseconds: 500), () {
          _isLoadingData = false;
        });
      }
    });
  }

  void _saveData() {
    if (_isLoadingData) return;

    _formKey.currentState?.save();
    final formData = _formKey.currentState?.value ?? {};
    if (formData.isNotEmpty) {
      final formNotifier = ref.read(dbsFormViewModelProvider.notifier);

      final formMiddleNames = <String>[];
      for (int i = 1; i <= 3; i++) {
        final middleName = formData['middlename$i']?.toString().trim();
        if (middleName?.isNotEmpty == true) {
          formMiddleNames.add(middleName!);
        }
      }

      formNotifier.updateBasicPersonalDetails(
        title: formData['title']?.toString() ?? '',
        forename: formData['forename']?.toString() ?? '',
        middlenames: formMiddleNames,
        presentSurname: formData['presentSurname']?.toString() ?? '',
        gender: formData['gender']?.toString() ?? '',
        contactNumber: formData['contactNumber']?.toString() ?? '',
        niNumber: '',
        email: formData['email']?.toString() ?? '',
      );

      formNotifier.markAsChanged();
    }
  }

  void _saveMiddleNames(List<String> names) {
    setState(() {
      middleNames = names;
    });
    _saveData();
  }

  bool validateForm() {
    setState(() {
      _hasTriedToSubmit = true;
    });

    _formKey.currentState?.save();

    final isValid = _formKey.currentState?.validate() ?? false;

    return isValid;
  }



  void _trackFieldInteraction(String fieldName, String? value, String? previousValue) {
    setState(() {
      _fieldsInteracted.add(fieldName);

      if (previousValue != null && previousValue.isNotEmpty && (value == null || value.trim().isEmpty)) {
        _fieldsWithRemovedContent.add(fieldName);
      }
    });
  }

  void _validateField(String fieldName) {
    if (_fieldsInteracted.contains(fieldName) || _hasTriedToSubmit) {
      _formKey.currentState?.fields[fieldName]?.validate();
    }
  }

  bool _shouldShowValidationError(String fieldName) {
    return _hasTriedToSubmit || _fieldsWithRemovedContent.contains(fieldName);
  }

  Map<String, String> getValidationErrors() {
    final errors = <String, String>{};

    _formKey.currentState?.save();
    final isValid = _formKey.currentState?.validate() ?? true;

    if (!isValid) {
      errors['validation'] = 'Please fix all validation issues before continuing into next step.';
    }

    return errors;
  }

  void resetFormData() {

    setState(() {
      hasMiddleNames = false;
      middleNames = [];
      middleNameCount = 0;
      _fieldsWithRemovedContent.clear();
      _fieldsInteracted.clear();
      _hasTriedToSubmit = false;
      _isDataLoaded = false;
    });

    _formKey.currentState?.patchValue({
      'gender': null,
      'title': null,
      'forename': '',
      'presentSurname': '',
      'hasMiddleNames': 'no',
      'middlename1': '',
      'middlename2': '',
      'middlename3': '',
      'email': '',
      'contactNumber': '',
    });

    _formKey.currentState?.reset();

    _saveData();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    ref.listen<int>(formResetTriggerProvider, (previous, next) {
      if (next > 0 && previous != next) {
        resetFormData();
      }
    });

    final isMobile = ResponsiveUtil.isMobile(context);

    return FormBuilder(
      key: _formKey,
      autovalidateMode: AutovalidateMode.disabled,
      onChanged: () {
        if (!_isLoadingData) {
          Future.delayed(const Duration(milliseconds: 300), () {
            if (mounted && !_isLoadingData) {
              _saveData();
            }
          });
        }
      },
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.kWhiteColor,
        ),
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Padding(
            padding: EdgeInsets.all(isMobile ? 16.0 : 20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(child: _buildGenderRadioGroup()),
                    const SizedBox(width: 16),
                    Expanded(child: _buildTitleDropdown()),
                  ],
                ),
                const SizedBox(height: 16.0),

                Row(
                  children: [
                    Expanded(child: _buildForenameField()),
                    const SizedBox(width: 16),
                    Expanded(child: _buildPresentSurnameField()),
                  ],
                ),
                const SizedBox(height: 16.0),

                _buildMiddleNamesSection(),
                const SizedBox(height: 16.0),

                Text(
                  'Contact Details:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.kBlueColor,
                  ),
                ),
                const SizedBox(height: 16.0),

                Row(
                  children: [
                    Expanded(child: _buildEmailField()),
                    const SizedBox(width: 16),
                    Expanded(child: _buildContactNumberField()),
                  ],
                ),

                const SizedBox(height: 24.0),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCompactSectionTitle(bool isMobile) {
    return Container(
      padding: EdgeInsets.all(isMobile ? 16 : 20),
      decoration: BoxDecoration(
        color: AppColors.kBlueColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.kBlueColor.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.person,
            color: AppColors.kBlueColor,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Personal Details',
                  style: TextStyle(
                    fontSize: isMobile ? 16 : 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.kBlueColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Enter your basic personal information',
                  style: TextStyle(
                    fontSize: isMobile ? 13 : 14,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGenderRadioGroup() {
    return DBSFormBuilderRadioGroup(
      name: 'gender',
      label: 'Gender',
      isRequired: true,
      validators: [
        FormBuilderValidators.required(errorText: 'Gender is required'),
      ],
      options: const [
        FormBuilderFieldOption(value: 'male', child: Text('Male')),
        FormBuilderFieldOption(value: 'female', child: Text('Female')),
      ],
      onChanged: (value) {
        setState(() {
          _fieldsInteracted.add('gender');
        });

        _saveData();

        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) {
            _validateField('gender');
            _validateField('title');
          }
        });
      },
    );
  }

  Widget _buildTitleDropdown() {
    return DBSFormBuilderDropdown<String>(
      name: 'title',
      label: 'Title',
      isRequired: true,
      hint: 'Select title',
      items: const [
        DropdownMenuItem(value: 'MR', child: Text('Mr')),
        DropdownMenuItem(value: 'MRS', child: Text('Mrs')),
        DropdownMenuItem(value: 'MISS', child: Text('Miss')),
        DropdownMenuItem(value: 'MS', child: Text('Ms')),
        DropdownMenuItem(value: 'DR', child: Text('Dr')),
        DropdownMenuItem(value: 'PROF', child: Text('Prof')),
        DropdownMenuItem(value: 'REV', child: Text('Rev')),
        DropdownMenuItem(value: 'SIR', child: Text('Sir')),
        DropdownMenuItem(value: 'LADY', child: Text('Lady')),
        DropdownMenuItem(value: 'LORD', child: Text('Lord')),
      ],
      validators: [
        FormBuilderValidators.required(errorText: 'Title is required'),
        _validateTitleGender,
      ],
      onChanged: (value) {
        setState(() {
          _fieldsInteracted.add('title');
        });

        _saveData();

        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) {
            _validateField('title');
            _validateField('gender');
          }
        });
      },
    );
  }

  Widget _buildForenameField() {
    String? previousValue;
    return DBSFormBuilderTextField(
      name: 'forename',
      label: 'First Name',
      isRequired: true,
      maxLength: 60,
      textCapitalization: TextCapitalization.none,
      validators: [
        FormBuilderValidators.required(errorText: 'First Name is required'),
        FormBuilderValidators.maxLength(60, errorText: 'First Name cannot exceed 60 characters'),
      ],
      onChanged: (value) {
        _trackFieldInteraction('forename', value, previousValue);
        previousValue = value;
        _saveData();

        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) {
            _validateField('forename');
          }
        });
      },
    );
  }

  Widget _buildPresentSurnameField() {
    String? previousValue;
    return DBSFormBuilderTextField(
      name: 'presentSurname',
      label: 'Last Name',
      isRequired: true,
      maxLength: 60,
      textCapitalization: TextCapitalization.none,
      validators: [
        FormBuilderValidators.required(errorText: 'Last Name is required'),
        FormBuilderValidators.maxLength(60, errorText: 'Last Name cannot exceed 60 characters'),
      ],
      onChanged: (value) {
        _trackFieldInteraction('presentSurname', value, previousValue);
        previousValue = value;
        _saveData();

        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) {
            _validateField('presentSurname');
          }
        });
      },
    );
  }

  Widget _buildMiddleNamesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DBSFormBuilderRadioGroup(
          name: 'hasMiddleNames',
          label: 'Do you have a middle name?',
          isRequired: false,
          options: const [
            FormBuilderFieldOption(value: 'yes', child: Text('Yes')),
            FormBuilderFieldOption(value: 'no', child: Text('No')),
          ],
          onChanged: (value) {
            setState(() {
              hasMiddleNames = value == 'yes';
              if (hasMiddleNames) {
                middleNameCount = 1;
              } else {
                middleNames = [];
                middleNameCount = 0;
                _formKey.currentState?.patchValue({
                  'middlename1': '',
                  'middlename2': '',
                  'middlename3': '',
                });
              }
            });
            _saveData();
          },
        ),

        if (hasMiddleNames) ...[
          const SizedBox(height: 16.0),
          _buildDynamicMiddleNameFields(),
        ],
      ],
    );
  }

  Widget _buildDynamicMiddleNameFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (int i = 0; i < middleNameCount; i++) ...[
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: DBSFormBuilderTextField(
                  name: 'middlename${i + 1}',
                  label: 'Middle Name ${i + 1}',
                  isRequired: i == 0,
                  maxLength: 60,
                  validators: [
                    if (i == 0) FormBuilderValidators.required(errorText: 'Please enter your middle name'),
                    FormBuilderValidators.maxLength(60, errorText: 'Middle Name cannot exceed 60 characters'),
                  ],
                  onChanged: (value) {
                    final fieldName = 'middlename${i + 1}';
                    setState(() {
                      _fieldsInteracted.add(fieldName);
                    });
                    _saveData();

                    Future.delayed(const Duration(milliseconds: 300), () {
                      if (mounted) {
                        _validateField(fieldName);
                      }
                    });
                  },
                ),
              ),
              const SizedBox(width: 8),
              Container(
                margin: const EdgeInsets.only(top: 8),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (i == middleNameCount - 1 && middleNameCount < 3) ...[
                      IconButton(
                        onPressed: _addMiddleNameField,
                        icon: const Icon(Icons.add_circle, color: Colors.blue, size: 24),
                        tooltip: 'Add another middle name',
                        padding: const EdgeInsets.all(4),
                        constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                      ),
                    ],
                    if (middleNameCount > 1) ...[
                      IconButton(
                        onPressed: () => _removeMiddleNameField(i),
                        icon: const Icon(Icons.remove_circle, color: Colors.red, size: 24),
                        tooltip: 'Remove this middle name',
                        padding: const EdgeInsets.all(4),
                        constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
          if (i < middleNameCount - 1) const SizedBox(height: 16),
        ],
      ],
    );
  }
  void _addMiddleNameField() {
    if (middleNameCount < 3) {
      setState(() {
        middleNameCount++;
      });
    }
  }

  void _removeMiddleNameField(int index) {
    if (middleNameCount > 1) {
      setState(() {
        middleNameCount--;
        _formKey.currentState?.patchValue({
          'middlename${index + 1}': '',
        });
      });
      _saveData();
    }
  }







  Widget _buildContactNumberField() {
    return FormBuilderTextField(
      name: 'contactNumber',
      initialValue: null,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      keyboardType: TextInputType.phone,
      textCapitalization: TextCapitalization.none,
      maxLength: 30,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'[0-9\+\-\s\(\)]')),
      ],
      decoration: InputDecoration(
        labelText: 'Contact Number *',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppColors.kBlueColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.red, width: 2),
        ),
        filled: true,
        fillColor: Colors.white,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        errorStyle: const TextStyle(
          color: Colors.red,
          fontSize: 12,
          height: 1.2,
        ),
      ),
      validator: FormBuilderValidators.compose([
        FormBuilderValidators.required(errorText: 'Contact Number is required'),
        FormBuilderValidators.minLength(10, errorText: 'Contact Number must be at least 10 digits'),
        FormBuilderValidators.maxLength(30, errorText: 'Contact Number cannot exceed 30 characters'),
        _validatePhoneNumber,
      ]),
      onChanged: (value) {
        setState(() {
          _fieldsInteracted.add('contactNumber');
        });
        _saveData();

        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted) {
            final field = _formKey.currentState?.fields['contactNumber'];
            if (field != null) {
              field.validate();
            }
          }
        });
      },
    );
  }



  Widget _buildEmailField() {
    return DBSFormBuilderTextField(
      name: 'email',
      label: 'Email Address',
      isRequired: true,
      keyboardType: TextInputType.emailAddress,
      validators: [
        FormBuilderValidators.required(errorText: 'Email Address is required'),
        FormBuilderValidators.email(errorText: 'Please enter a valid email address'),
      ],
      onChanged: (value) {
        setState(() {
          _fieldsInteracted.add('email');
        });
        _saveData();

        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) {
            _validateField('email');
          }
        });
      },
    );
  }

  String? _validateTitleGender(String? title) {
    if (title == null || title.isEmpty) return null;

    final formData = _formKey.currentState?.value ?? {};
    final gender = formData['gender']?.toString();

    if (gender == null || gender.isEmpty) return null;

    if (gender == 'male') {
      if (!['MR', 'DR', 'PROF', 'REV'].contains(title)) {
        return 'Please select a title appropriate for male gender';
      }
    } else if (gender == 'female') {
      if (!['MRS', 'MISS', 'MS', 'DR', 'PROF', 'REV'].contains(title)) {
        return 'Please select a title appropriate for female gender';
      }
    }

    return null;
  }

  String? _validatePhoneNumber(String? value) {
    if (value == null || value.trim().isEmpty) return null;

    final phoneNumber = value.trim();

    final digitsOnly = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');


    if (digitsOnly.length < 10) {
      return 'Phone number must contain at least 10 digits';
    }

    if (digitsOnly.length > 15) {
      return 'Phone number cannot exceed 15 digits';
    }

    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[\s\-\(\)]'), '');

    if (cleanNumber.startsWith('07') && digitsOnly.length == 11) {
      return null;
    }

    if (cleanNumber.startsWith('01') && digitsOnly.length == 11) {
      return null;
    }

    if (cleanNumber.startsWith('02') && digitsOnly.length == 11) {
      return null;
    }

    if (cleanNumber.startsWith('+44') && digitsOnly.length >= 10) {
      return null;
    }

    if (cleanNumber.startsWith('+') && digitsOnly.length >= 10) {
      return null;
    }

    return 'Please enter a valid phone number (e.g., 07123 456789, 01234 567890, or +44 7123 456789)';
  }


}
