import 'package:SolidCheck/core/theme/colors.dart';
import 'package:flutter/material.dart';

class DBSFormTextTitleTile extends StatelessWidget {
  final String title;

  const DBSFormTextTitleTile({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      style: TextStyle(
          color: AppColors.kQuestionTextColor,
          fontWeight: FontWeight.bold,
          fontSize: 16.0),
    );
  }
}
