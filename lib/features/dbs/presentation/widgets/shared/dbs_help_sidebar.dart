import 'package:SolidCheck/core/theme/colors.dart';
import 'package:flutter/material.dart';

class DBSHelpSidebar extends StatelessWidget {
  final int currentStep;
  final bool isMobile;

  const DBSHelpSidebar({
    super.key,
    required this.currentStep,
    required this.isMobile,
  });

  @override
  Widget build(BuildContext context) {
    if (isMobile) return const SizedBox.shrink();

    return Container(
      width: 300,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(
          left: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Help Header
          _buildHelpHeader(),
          const SizedBox(height: 20),

          // Current Step Help
          _buildCurrentStepHelp(),
          const SizedBox(height: 20),

          // What You'll Provide Section
          _buildWhatYoullProvideSection(),
        ],
      ),
    );
  }

  Widget _buildHelpHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.kBlueColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.help_outline,
            color: AppColors.kBlueColor,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Text(
          'Help & Guidance',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.kBlueColor,
          ),
        ),
      ],
    );
  }

  Widget _buildCurrentStepHelp() {
    final stepHelp = _getStepHelp(currentStep);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                stepHelp['icon'],
                color: AppColors.kBlueColor,
                size: 18,
              ),
              const SizedBox(width: 8),
              Text(
                stepHelp['title'],
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.kBlueColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            stepHelp['description'],
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey.shade700,
              height: 1.4,
            ),
          ),
          const SizedBox(height: 12),
          ...stepHelp['tips'].map<Widget>((tip) => _buildHelpTip(tip)).toList(),
        ],
      ),
    );
  }

  Widget _buildHelpTip(String tip) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 6),
            width: 4,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.kBlueColor,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              tip,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
                height: 1.3,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWhatYoullProvideSection() {
    final stepRequirements = _getStepRequirements(currentStep);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.checklist,
                color: AppColors.kBlueColor,
                size: 18,
              ),
              const SizedBox(width: 8),
              Text(
                'What You\'ll Provide',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.kBlueColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...stepRequirements.map<Widget>((requirement) => _buildRequirementItem(requirement)),
          if (stepRequirements.isEmpty)
            Text(
              'Complete the form step by step',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
                fontStyle: FontStyle.italic,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildRequirementItem(String requirement) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.check_circle_outline,
            size: 14,
            color: Colors.green.shade600,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              requirement,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade700,
                height: 1.3,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Map<String, dynamic> _getStepHelp(int step) {
    switch (step) {
      case 1:
        return {
          'title': 'Personal Details Help',
          'icon': Icons.person,
          'description': 'Enter your current legal name and contact information as they appear on official documents.',
          'tips': [
            'Use your full legal name as shown on your passport or birth certificate',
            'Middle names should be entered in full, not just initials',
            'Ensure your email address is correct - we\'ll send updates here',
            'Phone number should include area code',
          ],
        };
      case 2:
        return {
          'title': 'Birth Details Help',
          'icon': Icons.location_on,
          'description': 'Provide accurate birth information and nationality details.',
          'tips': [
            'Enter your date of birth exactly as shown on your birth certificate',
            'Birth town/city should be the place where you were actually born',
            'Use the current name of your birth country if it has changed',
            'Nationality refers to your citizenship, not ethnicity',
          ],
        };
      case 3:
        return {
          'title': 'Address History Help',
          'icon': Icons.home,
          'description': 'Provide a complete 5-year address history with no gaps.',
          'tips': [
            'Include all addresses where you\'ve lived for more than 2 months',
            'Ensure dates don\'t overlap or have gaps',
            'Use the postcode lookup for UK addresses when possible',
            'Include temporary addresses like student accommodation',
          ],
        };
      case 4:
        return {
          'title': 'Other Names Help',
          'icon': Icons.badge,
          'description': 'List any other names you have used or been known by.',
          'tips': [
            'Include maiden names, married names, and previous surnames',
            'Add any nicknames used in professional contexts',
            'Include names from previous marriages or partnerships',
            'Provide approximate dates when you used these names',
          ],
        };
      case 5:
        return {
          'title': 'Supporting Documents Help',
          'icon': Icons.description,
          'description': 'Provide details from identity documents you currently hold.',
          'tips': [
            'Only provide details for documents you actually have',
            'Enter information exactly as shown on the documents',
            'Passport details must match your current valid passport',
            'National Insurance number format: *********',
          ],
        };
      case 6:
        return {
          'title': 'Review Help',
          'icon': Icons.preview,
          'description': 'Carefully check all information before submitting your application.',
          'tips': [
            'Review each section for accuracy and completeness',
            'Check dates don\'t have gaps or overlaps',
            'Ensure names match across all sections',
            'Verify contact details are current and correct',
          ],
        };
      default:
        return {
          'title': 'DBS Application Help',
          'icon': Icons.help,
          'description': 'Complete each section carefully and accurately.',
          'tips': [
            'Take your time to ensure accuracy',
            'Have your documents ready before starting',
            'Save your progress regularly',
          ],
        };
    }
  }

  List<String> _getStepRequirements(int step) {
    switch (step) {
      case 1:
        return [
          'Title (Mr, Mrs, Ms, etc.)',
          'First name and surname',
          'Middle names (if applicable)',
          'Gender',
          'Email address',
          'Phone number',
        ];
      case 2:
        return [
          'Date of birth',
          'Town/city of birth',
          'County of birth',
          'Country of birth',
          'Nationality',
        ];
      case 3:
        return [
          'Current address',
          'Previous addresses (5 years)',
          'Dates at each address',
          'Address types (owned/rented)',
        ];
      case 4:
        return [
          'Any previous surnames used',
          'Any previous forenames used',
          'Dates when names were used',
          'Reason for name change (if applicable)',
        ];
      case 5:
        return [
          'National Insurance Number (if you have one)',
          'British Passport details (if you have one)',
          'UK Driving License details (if you have one)',
        ];
      case 6:
        return [
          'Review personal details',
          'Check birth information',
          'Verify address history',
          'Confirm other names',
          'Validate supporting documents',
        ];
      default:
        return [];
    }
  }
}
