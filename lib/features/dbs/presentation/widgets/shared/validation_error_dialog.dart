import 'package:SolidCheck/core/config/component_config.dart';
import 'package:flutter/material.dart';

/// Dialog to show validation errors when user tries to proceed with invalid form data
class ValidationErrorDialog extends StatelessWidget {
  final List<String> errors;
  final String stepName;

  const ValidationErrorDialog({
    super.key,
    required this.errors,
    required this.stepName,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      title: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red.shade600,
            size: 28,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Validation Errors',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red.shade700,
              ),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Please fix the following errors in the $stepName step before proceeding:',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade700,
              height: 1.4,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                for (int i = 0; i < errors.length; i++) ...[
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: const EdgeInsets.only(top: 6),
                        width: 4,
                        height: 4,
                        decoration: BoxDecoration(
                          color: Colors.red.shade600,
                          shape: BoxShape.circle,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          errors[i],
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.red.shade700,
                            height: 1.3,
                          ),
                        ),
                      ),
                    ],
                  ),
                  if (i < errors.length - 1) const SizedBox(height: 8),
                ],
              ],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          style: ComponentConfig.secondaryButtonStyle.copyWith(
            padding: WidgetStateProperty.all(
              const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            ),
          ),
          child: const Text('OK, I\'ll fix them'),
        ),
      ],
    );
  }

  /// Show the validation error dialog
  static Future<void> show({
    required BuildContext context,
    required List<String> errors,
    required String stepName,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return ValidationErrorDialog(
          errors: errors,
          stepName: stepName,
        );
      },
    );
  }
}
