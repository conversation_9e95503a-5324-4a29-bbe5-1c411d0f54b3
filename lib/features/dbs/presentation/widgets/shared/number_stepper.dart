import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/shared/utils/responsive.dart';
import 'package:flutter/material.dart';

class NumberStepper extends StatefulWidget {
  const NumberStepper({
    super.key,
    required this.width,
    required this.curStep,
    required this.stepCompleteColor,
    required this.totalSteps,
    required this.inactiveColor,
    required this.currentStepColor,
    required this.lineWidth,
  });

  final int curStep;
  final Color currentStepColor;
  final Color inactiveColor;
  final double lineWidth;
  final Color stepCompleteColor;
  final int totalSteps;
  final double width;

  @override
  NumberStepperState createState() => NumberStepperState();
}

class NumberStepperState extends State<NumberStepper> {
  late double currentStepPosition;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    currentStepPosition = 0.0;
    _scrollController = ScrollController();

    if (widget.curStep < 1 || widget.curStep > widget.totalSteps) {
      throw RangeError.range(widget.curStep, 1, widget.totalSteps, 'curStep');
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void updateStepPosition(int stepIndex) {
    setState(() {
      currentStepPosition = stepIndex.toDouble();
    });

    _scrollController.animateTo(
      (stepIndex * 80.0),
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  Widget getInnerElementOfStepper(int index) {
    if (index + 1 < widget.curStep) {
      return Icon(
        Icons.check,
        color: AppColors.refCheckDropDownUnActiveValueColor,
        size: 16.0,
      );
    } else if (index + 1 == widget.curStep) {
      return Center(
        child: Text(
          '',
          style: TextStyle(
              color: AppColors.kQuestionTextColor, fontWeight: FontWeight.bold),
        ),
      );
    } else {
      return const SizedBox();
    }
  }

  Color getCircleColor(int i) {
    if (i + 1 < widget.curStep) {
      return widget.stepCompleteColor;
    } else if (i + 1 == widget.curStep) {
      return widget.currentStepColor;
    } else {
      return AppColors.kInactiveStep;
    }
  }

  Color getBorderColor(int i) {
    if (i + 1 < widget.curStep) {
      return AppColors.kBlueColor;
    } else if (i + 1 == widget.curStep) {
      return AppColors.kBlueColor;
    } else {
      return widget.inactiveColor;
    }
  }

  Color getLineColor(int i) {
    return widget.curStep > i + 1
        ? AppColors.kBlueColor
        : AppColors.kInactiveStep;
  }

  List<Widget> _steps(BuildContext context) {
    final bool responsiveBreakPoint = ResponsiveUtil.isMobile(context) || ResponsiveUtil.isMobileLarge(context);
    final double circleSize = responsiveBreakPoint ? 24.0 : 28.0;
    final double textSize = responsiveBreakPoint ? 10.0 : 12.0;

    final Size size = MediaQuery.of(context).size;
    final double lineSpacing =
        size.width / widget.totalSteps * (responsiveBreakPoint ? 0.8 : 0.6);

    var list = <Widget>[];

    for (int i = 0; i < widget.totalSteps; i++) {
      var circleColor = getCircleColor(i);
      var borderColor = getBorderColor(i);
      var lineColor = getLineColor(i);

      list.add(
        AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          child: Column(
            children: [
              Container(
                width: circleSize,
                height: circleSize,
                decoration: BoxDecoration(
                  color: circleColor,
                  borderRadius: BorderRadius.circular(25.0),
                  border: Border.all(
                    color: borderColor,
                    width: i + 1 == widget.curStep ? 5.0 : 0.0,
                  ),
                ),
                child: getInnerElementOfStepper(i),
              ),
              const SizedBox(height: 8.0),
              AnimatedOpacity(
                duration: const Duration(milliseconds: 300),
                opacity: widget.curStep == i + 1 ? 1.0 : 0.5,
                child: Text(
                  _getStepTitle(i),
                  style: TextStyle(
                    color: widget.curStep > i ? Colors.black : Colors.grey,
                    fontSize: textSize,
                  ),
                ),
              ),
            ],
          ),
        ),
      );

      if (i != widget.totalSteps - 1) {
        list.add(
          SizedBox(
            width: lineSpacing,
            child: Center(
              child: Container(
                height: widget.lineWidth,
                color: lineColor,
              ),
            ),
          ),
        );
      }
    }

    return list;
  }

  String _getStepTitle(int index) {
    const stepTitles = [
      'Personal Details',
      'Birth Details',
      'Address Details',
      'Other Names',
      'Supporting Documents',
      'Review',
      'Consent',
    ];

    if (index < 0 || index >= stepTitles.length) {
      return 'Step ${index + 1}';
    }

    return stepTitles[index];
  }

  @override
  Widget build(BuildContext context) {
    final bool isMobile = ResponsiveUtil.isMobile(context);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'DBS Form',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.kBlueColor,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Thank you for taking the time to complete this form. Your responses are crucial for our background check process and will be kept confidential. Please provide accurate information as you fill out each section.',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
              height: 1.4,
            ),
          ),
          const SizedBox(height: 24),

          // Stepper
          if (isMobile)
            _buildMobileStepper()
          else
            _buildDesktopStepper(),

          const SizedBox(height: 16),

          // Required fields notice
          RichText(
            text: TextSpan(
              style: const TextStyle(fontSize: 12, color: Colors.grey),
              children: [
                const TextSpan(text: 'Fields marked in '),
                TextSpan(
                  text: 'orange',
                  style: TextStyle(color: AppColors.orangeColor, fontWeight: FontWeight.bold),
                ),
                const TextSpan(text: ' are required'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDesktopStepper() {
    return Row(
      children: List.generate(widget.totalSteps, (index) {
        final isActive = index + 1 == widget.curStep;
        final isCompleted = index + 1 < widget.curStep;
        final isLast = index == widget.totalSteps - 1;

        return Expanded(
          child: Row(
            children: [
              Expanded(
                child: Column(
                  children: [
                    // Step circle
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: isCompleted
                          ? AppColors.kBlueColor
                          : isActive
                            ? AppColors.kBlueColor
                            : Colors.grey.withValues(alpha: 0.3),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: isCompleted
                          ? const Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 18,
                            )
                          : Text(
                              '${index + 1}',
                              style: TextStyle(
                                color: isActive ? Colors.white : Colors.grey,
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    // Step title
                    Text(
                      _getStepTitle(index),
                      style: TextStyle(
                        fontSize: 12,
                        color: isActive ? AppColors.kBlueColor : Colors.grey,
                        fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              // Connecting line
              if (!isLast)
                Expanded(
                  child: Container(
                    height: 2,
                    margin: const EdgeInsets.only(bottom: 24),
                    color: isCompleted
                      ? AppColors.kBlueColor
                      : Colors.grey.withValues(alpha: 0.3),
                  ),
                ),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildMobileStepper() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: List.generate(widget.totalSteps, (index) {
          final isActive = index + 1 == widget.curStep;
          final isCompleted = index + 1 < widget.curStep;
          final isLast = index == widget.totalSteps - 1;

          return Row(
            children: [
              Column(
                children: [
                  // Step circle
                  Container(
                    width: 28,
                    height: 28,
                    decoration: BoxDecoration(
                      color: isCompleted
                        ? AppColors.kBlueColor
                        : isActive
                          ? AppColors.kBlueColor
                          : Colors.grey.withValues(alpha: 0.3),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: isCompleted
                        ? const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 16,
                          )
                        : Text(
                            '${index + 1}',
                            style: TextStyle(
                              color: isActive ? Colors.white : Colors.grey,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                    ),
                  ),
                  const SizedBox(height: 6),
                  // Step title
                  SizedBox(
                    width: 80,
                    child: Text(
                      _getStepTitle(index),
                      style: TextStyle(
                        fontSize: 10,
                        color: isActive ? AppColors.kBlueColor : Colors.grey,
                        fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              // Connecting line
              if (!isLast)
                Container(
                  width: 40,
                  height: 2,
                  margin: const EdgeInsets.only(bottom: 20),
                  color: isCompleted
                    ? AppColors.kBlueColor
                    : Colors.grey.withValues(alpha: 0.3),
                ),
            ],
          );
        }),
      ),
    );
  }
}
