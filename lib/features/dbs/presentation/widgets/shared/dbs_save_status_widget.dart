import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/dbs/providers/dbs_save_state_provider.dart';
import 'package:SolidCheck/shared/utils/responsive.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class DBSSaveStatusWidget extends ConsumerWidget {
  final bool showCompactVersion;

  const DBSSaveStatusWidget({
    super.key,
    this.showCompactVersion = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final saveState = ref.watch(saveStateProvider);
    final isMobile = ResponsiveUtil.isMobile(context);

    if (saveState.status == SaveStatus.idle && saveState.lastSaveTime == null) {
      return const SizedBox.shrink();
    }

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      padding: const EdgeInsets.symmetric(
        horizontal: isMobile ? 8 : 12,
        vertical: isMobile ? 4 : 6,
      ),
      decoration: BoxDecoration(
        color: _getStatusColor(saveState.status).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getStatusColor(saveState.status).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: showCompactVersion ? _buildCompactStatus(saveState, isMobile) : _buildFullStatus(saveState, isMobile),
    );
  }

  Widget _buildCompactStatus(SaveState saveState, bool isMobile) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildStatusIcon(saveState.status),
        if (saveState.status == SaveStatus.saving || saveState.status == SaveStatus.loading) ...[
          const SizedBox(width: 6),
          const SizedBox(
            width: 12,
            height: 12,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(_getStatusColor(saveState.status)),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildFullStatus(SaveState saveState, bool isMobile) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildStatusIcon(saveState.status),
        const SizedBox(width: 8),
        Flexible(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                _getStatusText(saveState),
                style: TextStyle(
                  fontSize: isMobile ? 12 : 13,
                  fontWeight: FontWeight.w500,
                  color: _getStatusColor(saveState.status),
                ),
                overflow: TextOverflow.ellipsis,
              ),
              if (saveState.lastSaveTime != null) ...[
                const SizedBox(height: 2),
                Text(
                  _formatLastSaveTime(saveState.lastSaveTime!),
                  style: TextStyle(
                    fontSize: isMobile ? 10 : 11,
                    color: Colors.grey[600],
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              if (saveState.completionPercentage != null) ...[
                const SizedBox(height: 4),
                _buildProgressBar(saveState.completionPercentage!, isMobile),
              ],
            ],
          ),
        ),
        if (saveState.status == SaveStatus.saving || saveState.status == SaveStatus.loading) ...[
          const SizedBox(width: 8),
          const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(_getStatusColor(saveState.status)),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildStatusIcon(SaveStatus status) {
    IconData iconData;
    switch (status) {
      case SaveStatus.saving:
        iconData = Icons.save;
        break;
      case SaveStatus.saved:
        iconData = Icons.check_circle;
        break;
      case SaveStatus.loading:
        iconData = Icons.download;
        break;
      case SaveStatus.loaded:
        iconData = Icons.restore;
        break;
      case SaveStatus.error:
        iconData = Icons.error;
        break;
      default:
        iconData = Icons.info;
    }

    return Icon(
      iconData,
      size: 16,
      color: _getStatusColor(status),
    );
  }

  Widget _buildProgressBar(double percentage, bool isMobile) {
    return Container(
      width: isMobile ? 60 : 80,
      height: 4,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(2),
      ),
      child: FractionallySizedBox(
        alignment: Alignment.centerLeft,
        widthFactor: percentage.clamp(0.0, 1.0),
        child: Container(
          decoration: BoxDecoration(
            color: AppColors.kBlueColor,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(SaveStatus status) {
    switch (status) {
      case SaveStatus.saving:
        return Colors.orange;
      case SaveStatus.saved:
        return Colors.green;
      case SaveStatus.loading:
        return Colors.blue;
      case SaveStatus.loaded:
        return Colors.green;
      case SaveStatus.error:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(SaveState saveState) {
    if (saveState.message != null) {
      return saveState.message!;
    }

    switch (saveState.status) {
      case SaveStatus.saving:
        return 'Saving...';
      case SaveStatus.saved:
        return 'Saved';
      case SaveStatus.loading:
        return 'Loading...';
      case SaveStatus.loaded:
        return 'Loaded';
      case SaveStatus.error:
        return 'Error';
      default:
        return '';
    }
  }

  String _formatLastSaveTime(DateTime lastSave) {
    final now = DateTime.now();
    final difference = now.difference(lastSave);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}

class DBSSaveIndicator extends ConsumerWidget {
  const DBSSaveIndicator({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final saveState = ref.watch(saveStateProvider);
    
    if (saveState.status != SaveStatus.saving && saveState.status != SaveStatus.loading) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusColor(saveState.status).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getStatusColor(saveState.status).withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(
            width: 12,
            height: 12,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(_getStatusColor(saveState.status)),
            ),
          ),
          const SizedBox(width: 6),
          Text(
            saveState.status == SaveStatus.saving ? 'Saving...' : 'Loading...',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: _getStatusColor(saveState.status),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(SaveStatus status) {
    switch (status) {
      case SaveStatus.saving:
        return Colors.orange;
      case SaveStatus.loading:
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }
}
