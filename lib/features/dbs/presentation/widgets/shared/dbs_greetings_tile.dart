import 'package:SolidCheck/core/constants/constants.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/shared/utils/responsive.dart';
import 'package:flutter/material.dart';

class DBSGreetingsTile extends StatefulWidget {
  const DBSGreetingsTile({super.key});

  @override
  State<DBSGreetingsTile> createState() => _DBSGreetingsTileState();
}

class _DBSGreetingsTileState extends State<DBSGreetingsTile> {
  @override
  Widget build(BuildContext context) {
    final responsiveBreakPoint = !ResponsiveUtil.isMobile(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'DBS Form',
          textAlign: TextAlign.left,
          style: TextStyle(
              color: AppColors.kBlueColor,
              fontSize: 20.0,
              fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 10.0),
        Text(
          AppConstants.dbsFormScreenGreetings,
          textAlign: responsiveBreakPoint ? TextAlign.left : TextAlign.center,
          style: const TextStyle(fontSize: 14.0),
        ),
      ],
    );
  }
}
