import 'package:SolidCheck/core/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

enum FieldInputType {
  normal,
  digitsOnly,
  lettersOnly,
  emailLike,
}

Widget buildTextField({
  required TextEditingController controller,
  required String title,
  required bool isFieldRequired,
  required String? Function(String?)? validator,
  FieldInputType fieldInputType = FieldInputType.normal,
  List<TextInputFormatter>? customInputFormatters,
  bool isCommentFieldRequired = false,
  IconButton? suffixIcon,
  bool isdigitOnly = false,
  bool readOnly = false,
  final TextInputType? keyboardType,
}) {
  final FocusNode focusNode = FocusNode();

  List<TextInputFormatter>? getFormatters(FieldInputType type) {
    switch (type) {
      case FieldInputType.digitsOnly:
        return [FilteringTextInputFormatter.digitsOnly];
      case FieldInputType.lettersOnly:
        return [
          FilteringTextInputFormatter.allow(RegExp(r"[a-zA-Z '\-]")),
        ];
      case FieldInputType.emailLike:
        return [
          FilteringTextInputFormatter.allow(RegExp(r"[a-zA-Z0-9@._\-]")),
        ];
      case FieldInputType.normal:
        return null;
    }
  }

  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Align(
        alignment: Alignment.topLeft,
        child: Text(
          title,
          style: TextStyle(
            color:
                isFieldRequired ? AppColors.orangeColor : AppColors.kBlackColor,
          ),
        ),
      ),
      const SizedBox(height: 8.0),
      Column(
        children: [
          SizedBox(
            height: isCommentFieldRequired ? 150.0 : null,
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: TextFormField(
                    inputFormatters: getFormatters(fieldInputType),
                    controller: controller,
                    maxLines: isCommentFieldRequired ? 5 : 1,
                    focusNode: focusNode,
                    textInputAction: TextInputAction.next,
                    keyboardType: keyboardType,
                    readOnly: readOnly,
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                        borderSide: BorderSide(
                          color: AppColors.referenceCheckFieldBorderColor,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                        borderSide: BorderSide(
                          color: AppColors.referenceCheckFieldBorderColor,
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                        borderSide: BorderSide(
                          color: AppColors.referenceCheckFieldBorderColor,
                        ),
                      ),
                      errorBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                        borderSide: BorderSide(
                          color: AppColors.kRedTextColor,
                        ),
                      ),
                      focusedErrorBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8.0),
                        borderSide: BorderSide(
                          color: AppColors.kRedTextColor,
                        ),
                      ),
                      fillColor: AppColors.applicationOverviewDivColor,
                      filled: true,
                      errorStyle: TextStyle(
                          color: AppColors.kRedTextColor,
                          fontSize: 11.0,
                          fontWeight: FontWeight.w500,
                          height: 0.8),
                      suffixIcon: suffixIcon != null
                          ? Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Container(
                                decoration: BoxDecoration(
                                  color:
                                      AppColors.kQuestionContainerButtonColor,
                                  border:
                                      Border.all(color: AppColors.kWhiteColor),
                                  borderRadius: BorderRadius.circular(8.0),
                                ),
                                child: suffixIcon,
                              ),
                            )
                          : null,
                    ),
                    validator: validator,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16.0),
        ],
      ),
    ],
  );
}

class DBSFormTextField extends StatelessWidget {
  const DBSFormTextField({
    super.key,
    required this.controller,
    required this.isNumeric,
    required this.title,
    required this.isFieldRequired,
    this.validator,
  });

  final String? Function(String?)? validator;
  final TextEditingController controller;
  final bool isFieldRequired;
  final bool isNumeric;
  final String title;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              color: isFieldRequired
                  ? AppColors.orangeColor
                  : AppColors.kBlackColor,
            ),
          ),
          const SizedBox(height: 10.0),
          TextFormField(
            autovalidateMode: AutovalidateMode.onUserInteraction,
            controller: controller,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
            ),
            keyboardType: isNumeric ? TextInputType.number : TextInputType.text,
            validator: isFieldRequired == true ? validator : null,
          ),
        ],
      ),
    );
  }
}
