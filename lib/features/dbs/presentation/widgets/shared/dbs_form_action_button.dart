import 'package:SolidCheck/core/config/component_config.dart';
import 'package:SolidCheck/shared/utils/responsive.dart';
import 'package:flutter/material.dart';

class DBSFormActionButton extends StatelessWidget {
  final String buttonTitle;
  final void Function()? onPressed;
  final bool isSaveButton;
  final bool useTransparentStyle;

  const DBSFormActionButton({
    super.key,
    required this.buttonTitle,
    required this.onPressed,
    required this.isSaveButton,
    this.useTransparentStyle = false,
  });

  @override
  Widget build(BuildContext context) {
    final bool responsiveBreakPoint = !ResponsiveUtil.isMobile(context);

    // Determine button style based on type
    ButtonStyle buttonStyle;
    if (useTransparentStyle) {
      buttonStyle = ComponentConfig.textButtonStyle;
    } else if (isSaveButton) {
      buttonStyle = ComponentConfig.secondaryButtonStyle;
    } else {
      buttonStyle = ComponentConfig.primaryButtonStyle;
    }

    return SizedBox(
      height: responsiveBreakPoint ? 50.0 : 50.0,
      width: responsiveBreakPoint ? 200 : 100,
      child: ElevatedButton(
        onPressed: onPressed,
        style: buttonStyle,
        child: Text(
          buttonTitle,
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: responsiveBreakPoint ? 12.0 : 16.0,
          ),
        ),
      ),
    );
  }
}
