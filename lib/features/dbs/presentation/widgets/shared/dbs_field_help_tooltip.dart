import 'package:SolidCheck/core/theme/colors.dart';
import 'package:flutter/material.dart';

class DB<PERSON>ieldHelpTooltip extends StatefulWidget {
  final String helpText;
  final Widget child;
  final String? example;
  final List<String>? tips;

  const DBSFieldHelpTooltip({
    super.key,
    required this.helpText,
    required this.child,
    this.example,
    this.tips,
  });

  @override
  State<DBSFieldHelpTooltip> createState() => _DBSFieldHelpTooltipState();
}

class _DBSFieldHelpTooltipState extends State<DBSFieldHelpTooltip> {
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();
  bool _isShowing = false;

  @override
  void dispose() {
    _removeTooltip();
    super.dispose();
  }

  void _showTooltip() {
    if (_isShowing) return;

    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
    _isShowing = true;
  }

  void _removeTooltip() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
      _isShowing = false;
    }
  }

  OverlayEntry _createOverlayEntry() {
    RenderBox renderBox = context.findRenderObject() as RenderBox;
    Size size = renderBox.size;
    Offset offset = renderBox.localToGlobal(Offset.zero);

    return OverlayEntry(
      builder: (context) => Positioned(
        left: offset.dx,
        top: offset.dy + size.height + 8,
        child: Material(
          color: Colors.transparent,
          child: Container(
            constraints: const BoxConstraints(maxWidth: 300),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade900,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  spreadRadius: 1,
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Main help text
                Text(
                  widget.helpText,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 13,
                    height: 1.4,
                  ),
                ),
                
                // Example if provided
                if (widget.example != null) ...[
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.lightbulb_outline,
                          size: 14,
                          color: Colors.yellow.shade300,
                        ),
                        const SizedBox(width: 6),
                        Expanded(
                          child: Text(
                            'Example: ${widget.example}',
                            style: TextStyle(
                              color: Colors.yellow.shade100,
                              fontSize: 12,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
                
                // Tips if provided
                if (widget.tips != null && widget.tips!.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  ...widget.tips!.map((tip) => Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          margin: const EdgeInsets.only(top: 6),
                          width: 3,
                          height: 3,
                          decoration: const BoxDecoration(
                            color: Colors.white70,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            tip,
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 12,
                              height: 1.3,
                            ),
                          ),
                        ),
                      ],
                    ),
                  )),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: MouseRegion(
        onEnter: (_) => _showTooltip(),
        onExit: (_) => _removeTooltip(),
        child: widget.child,
      ),
    );
  }
}

// Helper widget to easily add help to form field labels
class DBSFieldLabel extends StatelessWidget {
  final String label;
  final bool isRequired;
  final String? helpText;
  final String? example;
  final List<String>? tips;

  const DBSFieldLabel({
    super.key,
    required this.label,
    this.isRequired = false,
    this.helpText,
    this.example,
    this.tips,
  });

  @override
  Widget build(BuildContext context) {
    Widget labelWidget = RichText(
      text: TextSpan(
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.grey.shade800,
        ),
        children: [
          TextSpan(text: label),
          if (isRequired)
            TextSpan(
              text: ' *',
              style: TextStyle(
                color: Colors.red.shade600,
                fontWeight: FontWeight.bold,
              ),
            ),
        ],
      ),
    );

    if (helpText != null) {
      return DBSFieldHelpTooltip(
        helpText: helpText!,
        example: example,
        tips: tips,
        child: Row(
          children: [
            labelWidget,
            const SizedBox(width: 6),
            Icon(
              Icons.help_outline,
              size: 16,
              color: AppColors.kBlueColor.withValues(alpha: 0.7),
            ),
          ],
        ),
      );
    }

    return labelWidget;
  }
}

// Field help data for different form fields
class DBSFieldHelp {
  static Map<String, Map<String, dynamic>> fieldHelp = {
    'title': {
      'helpText': 'Select your title as it appears on official documents.',
      'tips': ['Choose the title you use most commonly', 'This should match your passport or ID'],
    },
    'firstName': {
      'helpText': 'Enter your first name exactly as shown on your birth certificate or passport.',
      'example': 'John, Mary, Mohammed',
      'tips': ['Use your full legal first name', 'Don\'t use nicknames or shortened versions'],
    },
    'surname': {
      'helpText': 'Enter your current legal surname (family name).',
      'example': 'Smith, Johnson, Patel',
      'tips': ['Use your current legal surname', 'If married, use your current married name'],
    },
    'middleName': {
      'helpText': 'Enter any middle names in full, not just initials.',
      'example': 'James, Elizabeth, Kumar',
      'tips': ['Enter full middle names, not initials', 'Leave blank if you don\'t have middle names'],
    },
    'email': {
      'helpText': 'Enter a valid email address where we can contact you.',
      'example': '<EMAIL>',
      'tips': ['Use an email you check regularly', 'We\'ll send important updates here'],
    },
    'phone': {
      'helpText': 'Enter your phone number including the area code.',
      'example': '01234 567890, 07700 900123',
      'tips': ['Include area code for landlines', 'Mobile numbers start with 07'],
    },
    'dateOfBirth': {
      'helpText': 'Enter your date of birth exactly as shown on your birth certificate.',
      'example': '15/03/1990',
      'tips': ['Use DD/MM/YYYY format', 'Must match your birth certificate'],
    },
    'birthTown': {
      'helpText': 'Enter the town or city where you were born.',
      'example': 'London, Manchester, Birmingham',
      'tips': ['Use the current name of the place', 'This is where you were physically born'],
    },
    'nationality': {
      'helpText': 'Enter your nationality (citizenship), not your ethnicity.',
      'example': 'British, Irish, American',
      'tips': ['This refers to your passport/citizenship', 'Not your ethnic background'],
    },
    'niNumber': {
      'helpText': 'Enter your National Insurance number in the format *********.',
      'example': '*********',
      'tips': ['Two letters, six numbers, one letter', 'Found on your NI card or payslip'],
    },
    'passportNumber': {
      'helpText': 'Enter your passport number exactly as shown on your passport.',
      'example': '*********',
      'tips': ['Usually 9 digits for UK passports', 'Must be your current valid passport'],
    },
    'postcode': {
      'helpText': 'Enter a valid UK postcode to find your address.',
      'example': 'SW1A 1AA, M1 1AA',
      'tips': ['Include the space in the middle', 'Use capital letters'],
    },
  };

  static Map<String, dynamic>? getFieldHelp(String fieldKey) {
    return fieldHelp[fieldKey];
  }
}
