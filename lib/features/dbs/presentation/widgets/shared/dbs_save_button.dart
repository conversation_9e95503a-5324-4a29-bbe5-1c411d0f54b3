import 'package:SolidCheck/core/config/component_config.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/dbs/presentation/viewmodels/dbs_form_viewmodel.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// DBS Save Button Widget
/// Provides save functionality to store form data locally
class DBSSaveButton extends ConsumerWidget {
  final bool showIcon;
  final String? customText;
  final EdgeInsets? padding;
  final bool isCompact;

  const DBSSaveButton({
    super.key,
    this.showIcon = true,
    this.customText,
    this.padding,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formState = ref.watch(dbsFormViewModelProvider);
    final formNotifier = ref.read(dbsFormViewModelProvider.notifier);

    return Container(
      padding: padding ?? EdgeInsets.symmetric(
        horizontal: isCompact ? 8 : 16,
        vertical: isCompact ? 4 : 8,
      ),
      child: ElevatedButton.icon(
        onPressed: formState.isSaving
            ? null
            : () async {
                final success = await formNotifier.saveToLocalStorage();
                if (success && context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Row(
                        children: [
                          const Icon(
                            Icons.check_circle,
                            color: Colors.white,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(formState.saveMessage ?? 'Form saved successfully'),
                        ],
                      ),
                      backgroundColor: Colors.green,
                      duration: const Duration(seconds: 3),
                    ),
                  );
                } else if (!success && context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Row(
                        children: [
                          const Icon(
                            Icons.error,
                            color: Colors.white,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(formState.error ?? 'Failed to save form'),
                        ],
                      ),
                      backgroundColor: Colors.red,
                      duration: const Duration(seconds: 3),
                    ),
                  );
                }
              },
        icon: formState.isSaving
            ? SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    isCompact ? AppColors.kBlueColor : Colors.white,
                  ),
                ),
              )
            : showIcon
                ? Icon(
                    Icons.save,
                    size: isCompact ? 16 : 20,
                  )
                : const SizedBox.shrink(),
        label: Text(
          formState.isSaving
              ? 'Saving...'
              : customText ?? 'Save Progress',
          style: TextStyle(
            fontSize: isCompact ? 12 : 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        style: isCompact
            ? ComponentConfig.secondaryButtonStyle.copyWith(
                backgroundColor: WidgetStateProperty.all(Colors.grey.shade100),
                padding: WidgetStateProperty.all(const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                )),
              )
            : ComponentConfig.primaryButtonStyle.copyWith(
                padding: WidgetStateProperty.all(const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                )),
              ),
      ),
    );
  }
}

/// DBS Save Status Widget
/// Shows the last saved time and provides quick save access
class DBSSaveStatus extends ConsumerStatefulWidget {
  const DBSSaveStatus({super.key});

  @override
  ConsumerState<DBSSaveStatus> createState() => _DBSSaveStatusState();
}

class _DBSSaveStatusState extends ConsumerState<DBSSaveStatus> {
  DateTime? lastSavedTime;

  @override
  void initState() {
    super.initState();
    _loadLastSavedTime();
  }

  Future<void> _loadLastSavedTime() async {
    final formNotifier = ref.read(dbsFormViewModelProvider.notifier);
    final savedTime = await formNotifier.getLastSavedTime();
    if (mounted) {
      setState(() {
        lastSavedTime = savedTime;
      });
    }
  }

  String _formatLastSavedTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} minutes ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} hours ago';
    } else {
      return '${difference.inDays} days ago';
    }
  }

  @override
  Widget build(BuildContext context) {
    final formState = ref.watch(dbsFormViewModelProvider);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Icon(
            Icons.save,
            size: 16,
            color: Colors.grey.shade600,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Auto-save Status',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey.shade700,
                  ),
                ),
                Text(
                  lastSavedTime != null
                      ? 'Last saved: ${_formatLastSavedTime(lastSavedTime!)}'
                      : 'No saved data',
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          const DBSSaveButton(
            isCompact: true,
            customText: 'Save',
            showIcon: false,
          ),
        ],
      ),
    );
  }
}

/// DBS Load Data Dialog
/// Shows option to load previously saved data
class DBSLoadDataDialog extends ConsumerStatefulWidget {
  const DBSLoadDataDialog({super.key});

  @override
  ConsumerState<DBSLoadDataDialog> createState() => _DBSLoadDataDialogState();
}

class _DBSLoadDataDialogState extends ConsumerState<DBSLoadDataDialog> {
  DateTime? lastSavedTime;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadLastSavedTime();
  }

  Future<void> _loadLastSavedTime() async {
    final formNotifier = ref.read(dbsFormViewModelProvider.notifier);
    final savedTime = await formNotifier.getLastSavedTime();
    if (mounted) {
      setState(() {
        lastSavedTime = savedTime;
        isLoading = false;
      });
    }
  }

  String _formatLastSavedTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return 'More than a week ago';
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;
    final isMobile = screenWidth < 600;
    final isTablet = screenWidth >= 600 && screenWidth < 1024;

    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.symmetric(
        horizontal: isMobile ? 20 : 24,
        vertical: isMobile ? 40 : 40,
      ),
      child: Container(
        width: isMobile ? screenWidth - 40 : (isTablet ? 500 : 600),
        constraints: BoxConstraints(
          maxHeight: isMobile ? screenHeight * 0.85 : screenHeight * 0.8,
          minHeight: isMobile ? 400 : 300,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(isMobile ? 12 : 16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: EdgeInsets.all(isMobile ? 16 : 24),
              decoration: BoxDecoration(
                color: AppColors.kBlueColor.withValues(alpha: 0.05),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(isMobile ? 12 : 16),
                  topRight: Radius.circular(isMobile ? 12 : 16),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(isMobile ? 6 : 8),
                    decoration: BoxDecoration(
                      color: AppColors.kBlueColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      Icons.restore,
                      color: AppColors.kBlueColor,
                      size: isMobile ? 18 : 24,
                    ),
                  ),
                  SizedBox(width: isMobile ? 8 : 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Saved Data Found',
                          style: TextStyle(
                            fontSize: isMobile ? 16 : 20,
                            fontWeight: FontWeight.w600,
                            color: AppColors.kBlackColor,
                          ),
                        ),
                        if (!isLoading && lastSavedTime != null) ...[
                          SizedBox(height: isMobile ? 2 : 4),
                          Text(
                            'Last saved: ${_formatLastSavedTime(lastSavedTime!)}',
                            style: TextStyle(
                              fontSize: isMobile ? 11 : 14,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Flexible(
              child: Padding(
                padding: EdgeInsets.all(isMobile ? 16 : 24),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Icon and message
                    Container(
                      padding: EdgeInsets.all(isMobile ? 12 : 16),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(isMobile ? 8 : 12),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.assignment_outlined,
                            size: isMobile ? 32 : 48,
                            color: Colors.green.shade600,
                          ),
                          SizedBox(height: isMobile ? 8 : 12),
                          Text(
                            'We found previously saved form data on your device.',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: isMobile ? 13 : 16,
                              color: AppColors.kBlackColor,
                              height: 1.4,
                            ),
                          ),
                          SizedBox(height: isMobile ? 6 : 8),
                          Text(
                            'Would you like to continue where you left off?',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: isMobile ? 12 : 15,
                              color: Colors.grey.shade700,
                              height: 1.3,
                            ),
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: isMobile ? 16 : 24),

                    // Benefits of loading data (only show on larger screens)
                    if (!isMobile) ...[
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey.shade200),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Loading saved data will restore:',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: AppColors.kBlackColor,
                              ),
                            ),
                            const SizedBox(height: 8),
                            ...[
                              'All previously entered form information',
                              'Your current progress through the form',
                              'Any uploaded documents or selections',
                            ].map((benefit) => Padding(
                              padding: const EdgeInsets.only(bottom: 4),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.check_circle_outline,
                                    size: 16,
                                    color: Colors.green.shade600,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      benefit,
                                      style: TextStyle(
                                        fontSize: 13,
                                        color: Colors.grey.shade700,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            )),
                          ],
                        ),
                      ),
                    ] else ...[
                      // Mobile: Simplified benefits
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade50,
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(color: Colors.grey.shade200),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              size: 16,
                              color: AppColors.kBlueColor,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'Your progress and entered data will be restored',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey.shade700,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),

            // Actions
            Container(
              padding: EdgeInsets.all(isMobile ? 12 : 20),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(isMobile ? 12 : 16),
                  bottomRight: Radius.circular(isMobile ? 12 : 16),
                ),
              ),
              child: isMobile
                  ? Column(
                      children: [
                        // Load Data button (primary)
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton.icon(
                            onPressed: () {
                              Navigator.of(context).pop(true);
                            },
                            icon: const Icon(Icons.restore, size: 16),
                            label: const Text(
                              'Continue with Saved Data',
                              style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.kBlueColor,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(6),
                              ),
                              elevation: 0,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        // Start Fresh button (secondary)
                        SizedBox(
                          width: double.infinity,
                          child: OutlinedButton.icon(
                            onPressed: () {
                              Navigator.of(context).pop(false);
                            },
                            icon: const Icon(Icons.refresh, size: 16),
                            label: const Text(
                              'Start Fresh',
                              style: TextStyle(fontSize: 14),
                            ),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: Colors.grey.shade700,
                              side: BorderSide(color: Colors.grey.shade300),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(6),
                              ),
                            ),
                          ),
                        ),
                      ],
                    )
                  : Row(
                      children: [
                        // Start Fresh button (secondary)
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: () {
                              Navigator.of(context).pop(false);
                            },
                            icon: const Icon(Icons.refresh, size: 18),
                            label: const Text('Start Fresh'),
                            style: ComponentConfig.secondaryButtonStyle.copyWith(
                              foregroundColor: WidgetStateProperty.all(Colors.grey.shade700),
                              side: WidgetStateProperty.all(BorderSide(color: Colors.grey.shade300)),
                              padding: WidgetStateProperty.all(const EdgeInsets.symmetric(vertical: 14)),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        // Load Data button (primary)
                        Expanded(
                          flex: 2,
                          child: ElevatedButton.icon(
                            onPressed: () {
                              Navigator.of(context).pop(true);
                            },
                            icon: const Icon(Icons.restore, size: 18),
                            label: const Text('Continue with Saved Data'),
                            style: ComponentConfig.primaryButtonStyle.copyWith(
                              padding: WidgetStateProperty.all(const EdgeInsets.symmetric(vertical: 14)),
                            ),
                          ),
                        ),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Helper function to show load data dialog
Future<bool?> showLoadDataDialog(BuildContext context) {
  return showDialog<bool>(
    context: context,
    barrierDismissible: false,
    builder: (context) => const DBSLoadDataDialog(),
  );
}
