import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/shared/utils/responsive.dart';
import 'package:flutter/material.dart';

class DBSFormCenterTitleDiv extends StatefulWidget {
  const DBSFormCenterTitleDiv({super.key});

  @override
  State<DBSFormCenterTitleDiv> createState() => _DBSFormCenterTitleDivState();
}

class _DBSFormCenterTitleDivState extends State<DBSFormCenterTitleDiv> {
  @override
  Widget build(BuildContext context) {
    final isResponsive = ResponsiveUtil.isMobile(context) ||
        ResponsiveUtil.isMobileLarge(context);
    return ListTile(
      title: Center(
        child: Text.rich(
          TextSpan(
            text: 'FIELDS MARKED IN ',
            children: [
              TextSpan(
                text: 'ORANGE',
                style: TextStyle(
                  color: AppColors.orangeColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const TextSpan(text: ' ARE REQUIRED'),
            ],
          ),
          style: const TextStyle(fontWeight: FontWeight.bold),
          textAlign: isResponsive ? TextAlign.center : TextAlign.left,
        ),
      ),
    );
  }
}
