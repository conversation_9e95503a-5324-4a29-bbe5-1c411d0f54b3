import 'package:SolidCheck/core/theme/colors.dart';
import 'package:flutter/material.dart';

class ValidationErrorDisplay extends StatelessWidget {
  final Map<String, String> fieldErrors;
  final List<String> generalErrors;

  const ValidationErrorDisplay({
    super.key,
    required this.fieldErrors,
    required this.generalErrors,
  });

  @override
  Widget build(BuildContext context) {
    if (fieldErrors.isEmpty && generalErrors.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.kRedColor.withValues(alpha: 0.1),
        border: Border.all(color: AppColors.kRedColor.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.error_outline,
                color: AppColors.kRedColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Please correct the following errors:',
                style: TextStyle(
                  color: AppColors.kRedColor,
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          if (generalErrors.isNotEmpty) ...[
            for (final error in generalErrors)
              Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '• ',
                      style: TextStyle(
                        color: AppColors.kRedColor,
                        fontSize: 14,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        error,
                        style: TextStyle(
                          color: AppColors.kRedColor,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
          
          if (fieldErrors.isNotEmpty) ...[
            for (final entry in fieldErrors.entries)
              Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '• ',
                      style: TextStyle(
                        color: AppColors.kRedColor,
                        fontSize: 14,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        entry.value,
                        style: TextStyle(
                          color: AppColors.kRedColor,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ],
      ),
    );
  }
}

class ValidationErrorBanner extends StatelessWidget {
  final String message;
  final VoidCallback? onDismiss;

  const ValidationErrorBanner({
    super.key,
    required this.message,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.kRedColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.error,
            color: Colors.white,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (onDismiss != null) ...[
            const SizedBox(width: 8),
            GestureDetector(
              onTap: onDismiss,
              child: const Icon(
                Icons.close,
                color: Colors.white,
                size: 18,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
