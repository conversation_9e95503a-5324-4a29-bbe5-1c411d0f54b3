import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/shared/widgets/buttons/solid_button.dart';
import 'package:flutter/material.dart';

class FormCompletionSuccessWidget extends StatelessWidget {
  final String applicantId;
  final String applicationId;
  final String productCode;
  final String productName;
  final Map<String, dynamic>? completionData;
  final VoidCallback? onProceedToDocuments;

  const FormCompletionSuccessWidget({
    super.key,
    required this.applicantId,
    required this.applicationId,
    required this.productCode,
    required this.productName,
    this.completionData,
    this.onProceedToDocuments,
  });

  @override
  Widget build(BuildContext context) {
    final isMobile = ResponsiveHelper.isMobile(context);
    
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(isMobile ? 20 : 40),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.green.shade50,
              borderRadius: BorderRadius.circular(100),
            ),
            child: Icon(
              Icons.check_circle,
              size: isMobile ? 60 : 80,
              color: Colors.green.shade600,
            ),
          ),
          const SizedBox(height: 30),
          Text(
            'Form Submitted Successfully!',
            style: TextStyle(
              fontSize: isMobile ? 24 : 32,
              fontWeight: FontWeight.bold,
              color: AppColors.kBlueColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: isMobile ? 20 : 40,
              vertical: 20,
            ),
            child: Column(
              children: [
                Text(
                  'Your $productName application form has been completed and submitted successfully.',
                  style: TextStyle(
                    fontSize: isMobile ? 16 : 18,
                    color: Colors.grey[700],
                    height: 1.6,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 15),
                Text(
                  'You can now proceed to nominate your documents for verification.',
                  style: TextStyle(
                    fontSize: isMobile ? 14 : 16,
                    color: Colors.grey[600],
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          if (completionData != null) ...[
            const SizedBox(height: 20),
            _buildCompletionDetails(isMobile),
          ],
          const SizedBox(height: 40),
          SizedBox(
            width: isMobile ? double.infinity : 300,
            child: SolidButton(
              text: 'Proceed to Document Nomination',
              onPressed: onProceedToDocuments ?? () => _handleProceedToDocuments(context),
              type: SolidButtonType.primary,
              size: SolidButtonSize.large,
              isFullWidth: isMobile,
            ),
          ),
          const SizedBox(height: 20),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Back to Dashboard',
              style: TextStyle(
                color: AppColors.kBlueColor,
                fontSize: isMobile ? 14 : 16,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompletionDetails(bool isMobile) {
    final formCompletion = completionData!['form_completion'] as Map<String, dynamic>;
    final completedAt = formCompletion['completed_at'] as String?;
    final completedBy = formCompletion['completed_by'] as Map<String, dynamic>?;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Submission Details',
            style: TextStyle(
              fontSize: isMobile ? 14 : 16,
              fontWeight: FontWeight.w600,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 12),
          if (completedAt != null) ...[
            _buildDetailRow(
              'Completed At:',
              _formatDateTime(completedAt),
              isMobile,
            ),
            const SizedBox(height: 8),
          ],
          if (completedBy != null) ...[
            _buildDetailRow(
              'Submitted By:',
              _formatUserType(completedBy['user_type'] as String?),
              isMobile,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, bool isMobile) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: isMobile ? 100 : 120,
          child: Text(
            label,
            style: TextStyle(
              fontSize: isMobile ? 12 : 14,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: isMobile ? 12 : 14,
              color: Colors.grey[800],
            ),
          ),
        ),
      ],
    );
  }

  String _formatDateTime(String dateTimeStr) {
    try {
      final dateTime = DateTime.parse(dateTimeStr);
      return '${dateTime.day}/${dateTime.month}/${dateTime.year} at ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return dateTimeStr;
    }
  }

  String _formatUserType(String? userType) {
    switch (userType?.toLowerCase()) {
      case 'applicant':
        return 'You (Applicant)';
      case 'client_user':
        return _getClientUserName();
      case 'requester':
        return _getRequesterName();
      case 'doc_checker':
        return _getDocCheckerName();
      default:
        return userType ?? 'Unknown';
    }
  }

  String _getClientUserName() {
    final completedBy = completionData?['form_completion']?['completed_by'] as Map<String, dynamic>?;
    final userName = completedBy?['user_name'] as String?;
    final userEmail = completedBy?['user_email'] as String?;

    if (userName != null && userName.isNotEmpty) {
      return userName;
    } else if (userEmail != null && userEmail.isNotEmpty) {
      return userEmail;
    } else {
      final userId = completedBy?['user_id'];
      return userId != null ? 'Client User (ID: $userId)' : 'Client User';
    }
  }

  String _getRequesterName() {
    final completedBy = completionData?['form_completion']?['completed_by'] as Map<String, dynamic>?;
    final userName = completedBy?['user_name'] as String?;
    final userEmail = completedBy?['user_email'] as String?;

    if (userName != null && userName.isNotEmpty) {
      return userName;
    } else if (userEmail != null && userEmail.isNotEmpty) {
      return userEmail;
    } else {
      final userId = completedBy?['user_id'];
      return userId != null ? 'Requester (ID: $userId)' : 'Requester';
    }
  }

  String _getDocCheckerName() {
    final completedBy = completionData?['form_completion']?['completed_by'] as Map<String, dynamic>?;
    final userName = completedBy?['user_name'] as String?;
    final userEmail = completedBy?['user_email'] as String?;

    if (userName != null && userName.isNotEmpty) {
      return userName;
    } else if (userEmail != null && userEmail.isNotEmpty) {
      return userEmail;
    } else {
      final userId = completedBy?['user_id'];
      return userId != null ? 'Document Checker (ID: $userId)' : 'Document Checker';
    }
  }

  void _handleProceedToDocuments(BuildContext context) {
    Navigator.pushNamed(
      context,
      '/document-nomination',
      arguments: {
        'applicantId': applicantId,
        'applicationId': applicationId,
        'productCode': productCode,
        'productName': productName,
      },
    );
  }
}
