import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/dbs/presentation/viewmodels/dbs_form_viewmodel.dart';
import 'package:SolidCheck/shared/utils/responsive.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class DBSFormStepper extends ConsumerWidget {
  final int currentStep;
  final Function(int)? onStepTapped;

  const DBSFormStepper({
    super.key,
    required this.currentStep,
    this.onStepTapped,
  });

  static List<String> stepTitles = [
    'Personal details',
    'Birth details',
    'Address details',
    'Other names',
    'Supporting documents',
    'Review',
    'Consent',
  ];

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isMobile = ResponsiveUtil.isMobile(context);

    if (isMobile) {
      return _buildMobileStepper(context, ref);
    } else {
      return _buildDesktopStepper(context, ref);
    }
  }

  Widget _buildMobileStepper(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.kWhiteColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          LinearProgressIndicator(
            value: (currentStep + 1) / stepTitles.length,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              AppColors.kBlueColor,
            ),
          ),
          const SizedBox(height: 12),

          Text(
            'Step ${currentStep + 1} of ${stepTitles.length}',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.kBlueColor,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            stepTitles[currentStep],
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.kBlackColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDesktopStepper(BuildContext context, WidgetRef ref) {
    final formState = ref.watch(dbsFormViewModelProvider);

    final viewModel = ref.read(dbsFormViewModelProvider.notifier);
    final stepCompletionStatus = viewModel.getStepCompletionStatus();

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.kWhiteColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: List.generate(stepTitles.length, (index) {
              return Expanded(
                child: _buildStepIndicator(
                  index: index,
                  title: stepTitles[index],
                  isActive: index == currentStep,
                  isCompleted: stepCompletionStatus.length > index
                      ? stepCompletionStatus[index]
                      : false,
                  isLast: index == stepTitles.length - 1,
                ),
              );
            }),
          ),

          const SizedBox(height: 16),

          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange[200]!),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.orange[700], size: 16),
                const SizedBox(width: 8),
                Text(
                  'Fields marked in ',
                  style: TextStyle(fontSize: 14, color: Colors.grey[700]),
                ),
                Text(
                  'orange',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.orange[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  ' are required',
                  style: TextStyle(fontSize: 14, color: Colors.grey[700]),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepIndicator({
    required int index,
    required String title,
    required bool isActive,
    required bool isCompleted,
    required bool isLast,
  }) {
    return GestureDetector(
      onTap: onStepTapped != null ? () => onStepTapped!(index) : null,
      child: Row(
        children: [
          Expanded(
            child: Column(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: isCompleted
                        ? AppColors.kBlueColor
                        : isActive
                        ? AppColors.kBlueColor
                        : AppColors.kInactiveStep,
                    border: Border.all(
                      color: isActive || isCompleted
                          ? AppColors.kBlueColor
                          : AppColors.kInactiveStep,
                      width: 2,
                    ),
                  ),
                  child: Center(
                    child: isCompleted
                        ? Icon(
                            Icons.check,
                            color: AppColors.kWhiteColor,
                            size: 20,
                          )
                        : Text(
                            '${index + 1}',
                            style: TextStyle(
                              color: isActive
                                  ? AppColors.kWhiteColor
                                  : Colors.grey[600],
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                  ),
                ),

                const SizedBox(height: 8),

                Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                    color: isActive || isCompleted
                        ? AppColors.kBlueColor
                        : Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),

          if (!isLast)
            Expanded(
              child: Container(
                height: 2,
                margin: const EdgeInsets.only(bottom: 32),
                color: isCompleted
                    ? AppColors.kBlueColor
                    : AppColors.kInactiveStep,
              ),
            ),
        ],
      ),
    );
  }
}
