import 'package:SolidCheck/features/auth/data/repositories/auth.dart';
import 'package:SolidCheck/features/dbs/data/mappers/dbs_data_mapper.dart';
import 'package:SolidCheck/features/dbs/data/models/dbs_form_data.dart';
import 'package:SolidCheck/features/dbs/data/services/dbs_api_service.dart';
import 'package:SolidCheck/features/dbs/data/services/dbs_local_storage_service.dart';
import 'package:SolidCheck/features/dbs/services/dbs_form_validation_service.dart';
import 'package:SolidCheck/features/dbs/services/dbs_persistence_service.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// DBS Form State
class DBSFormState {
  final DBSFormData formData;
  final int currentStep;
  final bool isLoading;
  final String? error;
  final bool isSubmitted;
  final bool isSaving;
  final String? saveMessage;
  final String unspentConvictions;
  final String declarationByApplicant;
  final String languagePreference;
  final Map<String, String> validationErrors;
  final List<String> generalErrors;
  final bool canProceedToNextStep;
  final bool isLoadingData;
  final DateTime? lastSaveTime;
  final bool hasUnsavedChanges;
  final String? applicantId;
  final String? applicationId;
  final Map<String, dynamic>? formCompletionStatus;
  final bool isCheckingCompletion;

  DBSFormState({
    required this.formData,
    required this.currentStep,
    required this.isLoading,
    this.error,
    required this.isSubmitted,
    required this.isSaving,
    this.saveMessage,
    required this.unspentConvictions,
    required this.declarationByApplicant,
    required this.languagePreference,
    this.validationErrors = const {},
    this.generalErrors = const [],
    this.canProceedToNextStep = true,
    this.isLoadingData = false,
    this.lastSaveTime,
    this.hasUnsavedChanges = false,
    this.applicantId,
    this.applicationId,
    this.formCompletionStatus,
    this.isCheckingCompletion = false,
  });

  DBSFormState copyWith({
    DBSFormData? formData,
    int? currentStep,
    bool? isLoading,
    String? error,
    bool? isSubmitted,
    bool? isSaving,
    String? saveMessage,
    String? unspentConvictions,
    String? declarationByApplicant,
    String? languagePreference,
    Map<String, String>? validationErrors,
    List<String>? generalErrors,
    bool? canProceedToNextStep,
    bool? isLoadingData,
    DateTime? lastSaveTime,
    bool? hasUnsavedChanges,
    String? applicantId,
    String? applicationId,
    Map<String, dynamic>? formCompletionStatus,
    bool? isCheckingCompletion,
  }) {
    return DBSFormState(
      formData: formData ?? this.formData,
      currentStep: currentStep ?? this.currentStep,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      isSubmitted: isSubmitted ?? this.isSubmitted,
      isSaving: isSaving ?? this.isSaving,
      saveMessage: saveMessage ?? this.saveMessage,
      unspentConvictions: unspentConvictions ?? this.unspentConvictions,
      declarationByApplicant: declarationByApplicant ?? this.declarationByApplicant,
      languagePreference: languagePreference ?? this.languagePreference,
      validationErrors: validationErrors ?? this.validationErrors,
      generalErrors: generalErrors ?? this.generalErrors,
      canProceedToNextStep: canProceedToNextStep ?? this.canProceedToNextStep,
      isLoadingData: isLoadingData ?? this.isLoadingData,
      lastSaveTime: lastSaveTime ?? this.lastSaveTime,
      hasUnsavedChanges: hasUnsavedChanges ?? this.hasUnsavedChanges,
      applicantId: applicantId ?? this.applicantId,
      applicationId: applicationId ?? this.applicationId,
      formCompletionStatus: formCompletionStatus ?? this.formCompletionStatus,
      isCheckingCompletion: isCheckingCompletion ?? this.isCheckingCompletion,
    );
  }

  factory DBSFormState.initial() {
    return DBSFormState(
      formData: DBSFormData.empty(),
      currentStep: 0,
      isLoading: false,
      error: null,
      isSubmitted: false,
      isSaving: false,
      saveMessage: null,
      unspentConvictions: 'n',
      declarationByApplicant: 'n',
      languagePreference: 'english',
      validationErrors: {},
      generalErrors: [],
      canProceedToNextStep: true,
      isLoadingData: false,
      lastSaveTime: null,
      hasUnsavedChanges: false,
      applicantId: null,
      applicationId: null,
    );
  }
}

/// DBS Form ViewModel
class DBSFormViewModel extends StateNotifier<DBSFormState> {
  final DBSPersistenceService _persistenceService = DBSPersistenceService();
  final AuthRepository _authRepository;

  DBSFormViewModel(this._authRepository) : super(DBSFormState.initial());

  /// Navigate to next step
  void nextStep() {
    if (state.currentStep < 6 && state.canProceedToNextStep) {
      state = state.copyWith(currentStep: state.currentStep + 1);
      validateCurrentStep();
    }
  }

  /// Navigate to previous step
  void previousStep() {
    if (state.currentStep > 0) {
      state = state.copyWith(currentStep: state.currentStep - 1);
    }
  }

  /// Navigate to specific step
  void goToStep(int step) {
    if (step >= 0 && step <= 6) {
      state = state.copyWith(currentStep: step);
      validateCurrentStep();
    }
  }

  void setCurrentStep(int step) {
    if (step >= 0 && step <= 6) {
      state = state.copyWith(currentStep: step);
    }
  }

  /// Initialize form with applicant and application IDs
  void initializeForm(String applicantId, String applicationId) {
    state = state.copyWith(
      applicantId: applicantId,
      applicationId: applicationId,
    );
  }

  /// Update applicant details
  void updateApplicantDetails(ApplicantDetailsData applicantDetails) {
    final updatedFormData = state.formData.copyWith(applicantDetails: applicantDetails);
    state = state.copyWith(
      formData: updatedFormData,
      hasUnsavedChanges: true,
    );
    validateCurrentStep();
    _triggerAutoSave();
  }

  /// Update basic personal details (Title, Forename, PresentSurname, etc.)
  void updateBasicPersonalDetails({
    String? title,
    String? forename,
    List<String>? middlenames,
    String? presentSurname,
    String? dateOfBirth,
    String? gender,
    String? niNumber,
    String? email,
    String? contactNumber,
  }) {
    final currentApplicant = state.formData.applicantDetails;
    final updatedApplicant = currentApplicant.copyWith(
      title: title ?? currentApplicant.title,
      forename: forename ?? currentApplicant.forename,
      middlenames: middlenames ?? currentApplicant.middlenames,
      presentSurname: presentSurname ?? currentApplicant.presentSurname,
      dateOfBirth: dateOfBirth ?? currentApplicant.dateOfBirth,
      gender: gender ?? currentApplicant.gender,
      niNumber: niNumber ?? currentApplicant.niNumber,
      email: email ?? currentApplicant.email,
      contactNumber: contactNumber ?? currentApplicant.contactNumber,
    );
    updateApplicantDetails(updatedApplicant);
  }

  /// Update current address
  void updateCurrentAddress(CurrentAddressData currentAddress) {
    final currentApplicant = state.formData.applicantDetails;
    final updatedApplicant = currentApplicant.copyWith(currentAddress: currentAddress);
    updateApplicantDetails(updatedApplicant);
  }

  /// Update additional applicant details (birth details, other names, etc.)
  void updateAdditionalApplicantDetails(AdditionalApplicantDetailsData additionalDetails) {
    final currentApplicant = state.formData.applicantDetails;
    final updatedApplicant = currentApplicant.copyWith(additionalApplicantDetails: additionalDetails);
    updateApplicantDetails(updatedApplicant);
  }

  /// Calculate smart birth surname until value based on other names data
  String calculateSmartBirthSurnameUntil() {
    final additionalDetails = state.formData.applicantDetails.additionalApplicantDetails;
    final currentYear = DateTime.now().year.toString();


    // If user has no other surnames, use current year
    if (additionalDetails.otherSurnames.isEmpty) {
      return currentYear;
    }

    // If user has other surnames, find the earliest "Used From" year
    String? earliestUsedFrom;
    for (final surname in additionalDetails.otherSurnames) {
      if (surname.usedFrom.isNotEmpty) {
        if (earliestUsedFrom == null ||
            (int.tryParse(surname.usedFrom) ?? 0) < (int.tryParse(earliestUsedFrom) ?? 0)) {
          earliestUsedFrom = surname.usedFrom;
        }
      }
    }

    final result = earliestUsedFrom ?? currentYear;

    // Return the earliest used from year, or current year if none found
    return result;
  }

  /// Update birth surname until with smart calculation
  void updateBirthSurnameUntilSmart() {
    final currentAdditionalDetails = state.formData.applicantDetails.additionalApplicantDetails;

    final smartValue = calculateSmartBirthSurnameUntil();

    final updatedAdditionalDetails = currentAdditionalDetails.copyWith(
      birthSurnameUntil: smartValue,
    );

    updateAdditionalApplicantDetails(updatedAdditionalDetails);

    // Verify the update
    final verifyDetails = state.formData.applicantDetails.additionalApplicantDetails;
  }

  /// Update identity details
  void updateApplicantIdentityDetails(ApplicantIdentityDetailsData identityDetails) {
    final currentApplicant = state.formData.applicantDetails;
    final updatedApplicant = currentApplicant.copyWith(applicantIdentityDetails: identityDetails);
    updateApplicantDetails(updatedApplicant);
  }

  bool validateCurrentStep() {
    final validation = DBSFormValidationService.validateStep(state.currentStep, state.formData);

    state = state.copyWith(
      validationErrors: validation['fieldErrors'],
      generalErrors: validation['generalErrors'],
      canProceedToNextStep: validation['isValid'],
    );

    return validation['isValid'];
  }

  bool validateStep(int stepIndex) {
    final validation = DBSFormValidationService.validateStep(stepIndex, state.formData);
    return validation['isValid'];
  }

  Map<String, dynamic> getStepValidation(int stepIndex) {
    return DBSFormValidationService.validateStep(stepIndex, state.formData);
  }

  Map<String, String> getCurrentStepErrors() {
    return state.validationErrors;
  }

  List<String> getCurrentGeneralErrors() {
    return state.generalErrors;
  }

  bool canProceedToNext() {
    return state.canProceedToNextStep;
  }

  /// Get completion status for all steps
  List<bool> getStepCompletionStatus() {
    return List.generate(7, (index) => validateStep(index));
  }

  /// Force a state update to trigger UI rebuilds (useful for stepper updates)
  void forceStateUpdate() {
    // Create a new state object to trigger rebuilds
    state = state.copyWith();
  }

  /// Prepare form for submission by ensuring all steps have saved their data
  Future<void> _prepareFormForSubmission() async {
    // Ensure current address has postcode if it's a GB address
    _ensureCurrentAddressHasPostcode();

    // Add a small delay to ensure any pending saves are completed
    await Future.delayed(const Duration(milliseconds: 100));
  }

  /// Ensure current address has postcode for GB addresses
  void _ensureCurrentAddressHasPostcode() {
    // No longer needed since final validation has been removed
    // Individual step validations ensure data integrity
  }

  bool _validatePersonalDetails() {
    final applicant = state.formData.applicantDetails;
    return applicant.title.isNotEmpty &&
           applicant.forename.isNotEmpty &&
           applicant.presentSurname.isNotEmpty &&
           applicant.contactNumber.isNotEmpty;
  }

  bool _validateBirthDetails() {
    final applicant = state.formData.applicantDetails;
    final additional = applicant.additionalApplicantDetails;
    return applicant.dateOfBirth.isNotEmpty &&
           additional.birthSurname.isNotEmpty &&
           additional.birthNationality.isNotEmpty &&
           additional.birthCountry.isNotEmpty &&
           additional.birthTown.isNotEmpty;
  }

  bool _validateAddressDetails() {
    final applicant = state.formData.applicantDetails;
    final currentAddress = applicant.currentAddress;

    // Check if 5-year address history is complete
    final has5YearHistory = _validate5YearAddressHistory();

    // If 5-year history is complete, validation passes regardless of current address form fields
    if (has5YearHistory) {
      return true;
    }

    // If 5-year history is not complete, check if current address has basic required fields
    final hasCurrentAddress = currentAddress.postcode.isNotEmpty &&
                             currentAddress.addressLine1.isNotEmpty &&
                             currentAddress.addressTown.isNotEmpty &&
                             currentAddress.countryCode.isNotEmpty;

    return hasCurrentAddress;
  }

  /// Validate that address history covers the last 5 years
  bool _validate5YearAddressHistory() {
    final applicant = state.formData.applicantDetails;
    final currentAddress = applicant.currentAddress;
    final previousAddresses = applicant.previousAddresses;

    // If no addresses at all, not valid
    if (currentAddress.addressLine1.isEmpty && previousAddresses.isEmpty) {
      return false;
    }

    final now = DateTime.now();
    final fiveYearsAgo = DateTime(now.year - 5, now.month, 1);

    // Collect all addresses with their date ranges
    final allAddresses = <Map<String, dynamic>>[];

    // Add current address
    if (currentAddress.addressLine1.isNotEmpty) {
      DateTime? fromDate;
      if (currentAddress.residentFromGyearMonth.isNotEmpty) {
        final parts = currentAddress.residentFromGyearMonth.split('-');
        if (parts.length == 2) {
          final year = int.tryParse(parts[0]);
          final month = int.tryParse(parts[1]);
          if (year != null && month != null) {
            fromDate = DateTime(year, month, 1);
          }
        }
      }

      allAddresses.add({
        'fromDate': fromDate,
        'toDate': now, // Current address goes to present
      });
    }

    // Add previous addresses
    for (final prevAddress in previousAddresses) {
      DateTime? fromDate;
      DateTime? toDate;

      if (prevAddress.residentFromGyearMonth.isNotEmpty) {
        final parts = prevAddress.residentFromGyearMonth.split('-');
        if (parts.length == 2) {
          final year = int.tryParse(parts[0]);
          final month = int.tryParse(parts[1]);
          if (year != null && month != null) {
            fromDate = DateTime(year, month, 1);
          }
        }
      }

      if (prevAddress.residentToGyearMonth.isNotEmpty) {
        final parts = prevAddress.residentToGyearMonth.split('-');
        if (parts.length == 2) {
          final year = int.tryParse(parts[0]);
          final month = int.tryParse(parts[1]);
          if (year != null && month != null) {
            toDate = DateTime(year, month, 1);
          }
        }
      }

      if (fromDate != null) {
        allAddresses.add({
          'fromDate': fromDate,
          'toDate': toDate ?? now,
        });
      }
    }

    // Sort addresses by from date
    allAddresses.sort((a, b) {
      final dateA = a['fromDate'] as DateTime?;
      final dateB = b['fromDate'] as DateTime?;
      if (dateA == null || dateB == null) return 0;
      return dateA.compareTo(dateB);
    });

    // Check if we have continuous coverage from 5 years ago to present
    return _checkContinuousAddressCoverage(allAddresses, fiveYearsAgo, now);
  }

  /// Check if addresses provide continuous coverage for the required period
  bool _checkContinuousAddressCoverage(List<Map<String, dynamic>> addresses, DateTime fiveYearsAgo, DateTime now) {
    if (addresses.isEmpty) return false;

    // Check if the earliest address starts at or before 5 years ago
    final earliestFromDate = addresses.first['fromDate'] as DateTime?;
    if (earliestFromDate == null || earliestFromDate.isAfter(fiveYearsAgo)) {
      return false; // Doesn't go back far enough
    }

    // Check if the latest address covers up to present
    final latestAddress = addresses.last;
    final latestToDate = latestAddress['toDate'] as DateTime?;
    if (latestToDate == null) return false;

    // Check for gaps between consecutive addresses
    for (int i = 0; i < addresses.length - 1; i++) {
      final currentToDate = addresses[i]['toDate'] as DateTime?;
      final nextFromDate = addresses[i + 1]['fromDate'] as DateTime?;

      if (currentToDate == null || nextFromDate == null) continue;

      // Calculate month difference
      final currentEndMonth = currentToDate.year * 12 + currentToDate.month;
      final nextStartMonth = nextFromDate.year * 12 + nextFromDate.month;

      // There should be no gap (difference should be 1 month or less)
      if (nextStartMonth - currentEndMonth > 1) {
        return false; // Gap found
      }
    }

    return true;
  }

  bool _validateOtherNames() {
    final additional = state.formData.applicantDetails.additionalApplicantDetails;
    // If no other surnames or forenames, validation passes
    if (additional.otherSurnames.isEmpty && additional.otherForenames.isEmpty) {
      return true;
    }

    // Validate other surnames
    for (final surname in additional.otherSurnames) {
      if (surname.name.isEmpty || surname.usedFrom.isEmpty || surname.usedTo.isEmpty) {
        return false;
      }
    }

    // Validate other forenames
    for (final forename in additional.otherForenames) {
      if (forename.name.isEmpty || forename.usedFrom.isEmpty || forename.usedTo.isEmpty) {
        return false;
      }
    }

    return true;
  }

  bool _validateSupportingDocuments() {
    final identity = state.formData.applicantDetails.applicantIdentityDetails;
    final applicant = state.formData.applicantDetails;

    // National Insurance validation (if provided)
    if (applicant.niNumber.isNotEmpty) {
      // Basic NI number format validation
      final niPattern = RegExp(r'^[A-Z]{2}[0-9]{6}[A-D]$');
      if (!niPattern.hasMatch(applicant.niNumber.toUpperCase())) {
        return false;
      }
    }

    // Passport validation (if provided)
    if (identity.passportDetails != null) {
      final passport = identity.passportDetails!;
      if (passport.passportNumber.isEmpty ||
          passport.passportDob.isEmpty ||
          passport.passportNationality.isEmpty ||
          passport.passportIssueDate.isEmpty) {
        return false;
      }
    }

    // Driving license validation (if provided)
    if (identity.driverLicenceDetails != null) {
      final license = identity.driverLicenceDetails!;
      if (license.driverLicenceNumber.isEmpty ||
          license.driverLicenceDOB.isEmpty ||
          license.driverLicenceType.isEmpty ||
          license.driverLicenceValidFrom.isEmpty ||
          license.driverLicenceIssueCountry.isEmpty) {
        return false;
      }
    }

    // At least one form of identification should be provided
    // (National Insurance, Passport, or Driving License)
    return applicant.niNumber.isNotEmpty ||
           identity.passportDetails != null ||
           identity.driverLicenceDetails != null;
  }

  bool _validateConsent() {
    final additional = state.formData.applicantDetails.additionalApplicantDetails;
    return additional.declarationByApplicant == 'y';
  }

  /// Update consent and declaration fields
  void updateConsentFields({
    String? unspentConvictions,
    String? declarationByApplicant,
    String? languagePreference,
  }) {
    state = state.copyWith(
      unspentConvictions: unspentConvictions,
      declarationByApplicant: declarationByApplicant,
      languagePreference: languagePreference,
    );
  }

  /// Save form data to local storage
  Future<bool> saveToLocalStorage() async {
    state = state.copyWith(isSaving: true, saveMessage: null, error: null);

    try {
      final success = await DBSLocalStorageService.saveFormData(
        state.formData,
        unspentConvictions: state.unspentConvictions,
        declarationByApplicant: state.declarationByApplicant,
        languagePreference: state.languagePreference,
      );

      // Also save current step
      await DBSLocalStorageService.saveCurrentStep(state.currentStep);

      if (success) {
        state = state.copyWith(
          isSaving: false,
          saveMessage: 'Form data saved successfully to your device',
        );
        return true;
      } else {
        state = state.copyWith(
          isSaving: false,
          error: 'Failed to save form data to local storage',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isSaving: false,
        error: 'Error saving form data: ${e.toString()}',
      );
      return false;
    }
  }

  /// Load form data from local storage
  Future<bool> loadFromLocalStorage() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final savedData = await DBSLocalStorageService.loadFormData();

      if (savedData != null) {
        final formData = savedData['formData'] as DBSFormData;
        final currentStep = await DBSLocalStorageService.loadCurrentStep();

        state = state.copyWith(
          isLoading: false,
          formData: formData,
          currentStep: currentStep,
          unspentConvictions: savedData['unspentConvictions'] ?? 'n',
          declarationByApplicant: savedData['declarationByApplicant'] ?? 'n',
          languagePreference: savedData['languagePreference'] ?? 'english',
          saveMessage: 'Form data loaded from local storage',
        );
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          saveMessage: 'No saved form data found',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Error loading form data: ${e.toString()}',
      );
      return false;
    }
  }





  /// Submit form to API (final submission)
  Future<void> submitForm() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Prepare form for submission by ensuring all data is saved
      await _prepareFormForSubmission();

      // Skip final validation since each step has already been validated
      // Individual step validations ensure data integrity

      // Validate required fields
      if (state.applicationId == null) {
        state = state.copyWith(
          isLoading: false,
          error: 'Application ID is required for submission.',
        );
        return;
      }

      // Parse application ID to integer
      int applicationId;
      try {
        applicationId = int.parse(state.applicationId!);
      } catch (e) {
        state = state.copyWith(
          isLoading: false,
          error: 'Invalid application ID format: ${state.applicationId}. Please ensure you have a valid application.',
        );
        return;
      }

      // Map form data to save-data API request format
      final saveDataRequest = DBSDataMapper.mapToSaveDataRequest(
        applicationId: applicationId,
        formData: state.formData,
        unspentConvictions: state.unspentConvictions,
        declarationByApplicant: state.declarationByApplicant,
        languagePreference: state.languagePreference,
        identityVerified: 'y',
        evidenceCheckedBy: 'SYSTEM',
      );

      // Get authentication token
      final token = await _authRepository.getToken();
      if (token == null) {
        state = state.copyWith(
          isLoading: false,
          error: 'Authentication required. Please log in again.',
        );
        return;
      }

      // Submit to API
      final result = await DBSApiService.saveFormData(saveDataRequest, token: token);

      if (result['success']) {
        // Clear local storage after successful submission
        await DBSLocalStorageService.clearSavedData();

        state = state.copyWith(
          isLoading: false,
          isSubmitted: true,
          saveMessage: 'Application submitted successfully!',
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: 'Failed to submit application: ${result['message']}',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Network error: ${e.toString()}',
      );
    }
  }

  /// Save form data with encryption
  Future<bool> saveFormDataSecurely({bool isAutoSave = false}) async {
    if (state.applicantId == null || state.applicationId == null) {
      state = state.copyWith(error: 'Cannot save: Missing applicant or application ID');
      return false;
    }

    state = state.copyWith(isSaving: true, error: null);

    try {
      final success = await _persistenceService.saveFormData(
        applicantId: state.applicantId!,
        applicationId: state.applicationId!,
        formData: state.formData,
        currentStep: state.currentStep + 1,
        isAutoSave: isAutoSave,
      );

      if (success) {
        state = state.copyWith(
          isSaving: false,
          hasUnsavedChanges: false,
          lastSaveTime: DateTime.now(),
          saveMessage: isAutoSave ? 'Auto-saved' : 'Progress saved successfully',
        );
        return true;
      } else {
        state = state.copyWith(
          isSaving: false,
          error: 'Failed to save form data',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isSaving: false,
        error: 'Error saving form data: ${e.toString()}',
      );
      return false;
    }
  }

  /// Load saved form data
  Future<bool> loadSavedFormData() async {
    if (state.applicantId == null || state.applicationId == null) {
      return false;
    }

    state = state.copyWith(isLoadingData: true, error: null);

    try {
      final savedData = await _persistenceService.loadFormData(
        applicantId: state.applicantId!,
        applicationId: state.applicationId!,
      );

      if (savedData != null) {
        final metadata = await _persistenceService.getFormMetadata(
          applicantId: state.applicantId!,
          applicationId: state.applicationId!,
        );

        state = state.copyWith(
          formData: savedData,
          currentStep: metadata?.currentStep ?? 1,
          isLoadingData: false,
          hasUnsavedChanges: false,
          lastSaveTime: metadata?.saveTimestamp,
          saveMessage: 'Previous progress loaded successfully',
        );
        return true;
      } else {
        state = state.copyWith(isLoadingData: false);
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoadingData: false,
        error: 'Error loading saved data: ${e.toString()}',
      );
      return false;
    }
  }

  /// Check if saved data exists
  Future<bool> hasSavedData() async {
    if (state.applicantId == null || state.applicationId == null) {
      return false;
    }

    try {
      return await _persistenceService.hasSavedData(
        applicantId: state.applicantId!,
        applicationId: state.applicationId!,
      );
    } catch (e) {
      return false;
    }
  }

  /// Clear saved data
  Future<bool> clearSavedData() async {
    if (state.applicantId == null || state.applicationId == null) {
      return false;
    }

    try {
      final success = await _persistenceService.clearSavedData(
        applicantId: state.applicantId!,
        applicationId: state.applicationId!,
      );

      if (success) {
        state = state.copyWith(
          hasUnsavedChanges: false,
          lastSaveTime: null,
          saveMessage: 'Saved data cleared',
        );
      }

      return success;
    } catch (e) {
      state = state.copyWith(error: 'Error clearing saved data: ${e.toString()}');
      return false;
    }
  }

  /// Trigger auto-save with debouncing
  void _triggerAutoSave() {
    Future.delayed(const Duration(seconds: 2), () {
      if (state.hasUnsavedChanges && !state.isSaving) {
        saveFormDataSecurely(isAutoSave: true);
      }
    });
  }

  /// Mark form as having unsaved changes
  void markAsChanged() {
    if (!state.hasUnsavedChanges) {
      state = state.copyWith(hasUnsavedChanges: true);
    }
  }

  /// Get last saved time
  Future<DateTime?> getLastSavedTime() async {
    if (state.applicantId == null || state.applicationId == null) {
      return null;
    }

    try {
      return await _persistenceService.getLastSaveTime(
        applicantId: state.applicantId!,
        applicationId: state.applicationId!,
      );
    } catch (e) {
      return null;
    }
  }

  /// Get form metadata
  Future<FormSaveMetadata?> getFormMetadata() async {
    if (state.applicantId == null || state.applicationId == null) {
      return null;
    }

    try {
      return await _persistenceService.getFormMetadata(
        applicantId: state.applicantId!,
        applicationId: state.applicationId!,
      );
    } catch (e) {
      return null;
    }
  }

  /// Check if form is already completed
  Future<bool> checkFormCompletionStatus() async {
    if (state.applicationId == null) {
      return false;
    }

    state = state.copyWith(isCheckingCompletion: true);

    try {
      final token = await _authRepository.getToken();
      if (token == null) {
        state = state.copyWith(
          isCheckingCompletion: false,
          error: 'Authentication token not found',
        );
        return false;
      }

      final applicationId = int.parse(state.applicationId!);
      final result = await DBSApiService.checkFormCompletionStatus(
        applicationId,
        token: token,
      );

      if (result['success']) {
        final completionData = result['data'] as Map<String, dynamic>;
        state = state.copyWith(
          isCheckingCompletion: false,
          formCompletionStatus: completionData,
        );

        final formCompletion = completionData['form_completion'] as Map<String, dynamic>;
        return formCompletion['is_completed'] as bool;
      } else {
        state = state.copyWith(
          isCheckingCompletion: false,
          error: 'Failed to check form completion status: ${result['message']}',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isCheckingCompletion: false,
        error: 'Error checking form completion: ${e.toString()}',
      );
      return false;
    }
  }

  /// Get form completion status data
  Map<String, dynamic>? get formCompletionData => state.formCompletionStatus;

  /// Check if form is completed
  bool get isFormCompleted {
    if (state.formCompletionStatus == null) return false;
    final formCompletion = state.formCompletionStatus!['form_completion'] as Map<String, dynamic>?;
    return formCompletion?['is_completed'] as bool? ?? false;
  }

  /// Check if user can proceed to document nomination
  bool get canProceedToDocuments {
    return state.formCompletionStatus?['can_proceed_to_documents'] as bool? ?? false;
  }

  /// Reset form
  void resetForm() {
    final currentApplicantId = state.applicantId;
    final currentApplicationId = state.applicationId;

    state = DBSFormState.initial().copyWith(
      applicantId: currentApplicantId,
      applicationId: currentApplicationId,
      currentStep: 1, // Start at step 1, not 0
    );
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Provider for DBS Form ViewModel
final dbsFormViewModelProvider = StateNotifierProvider<DBSFormViewModel, DBSFormState>((ref) {
  final authRepository = ref.read(authProvider);
  return DBSFormViewModel(authRepository);
});
