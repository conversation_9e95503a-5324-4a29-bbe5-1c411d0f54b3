import 'package:SolidCheck/features/dbs/data/models/dbs_form_data.dart';
import 'package:SolidCheck/shared/utils/dbs_validation_rules.dart';

class DBSFormValidationService {
  
  static Map<String, dynamic> createValidationResult({
    required bool isValid,
    required Map<String, String> fieldErrors,
    List<String> generalErrors = const [],
  }) {
    return {
      'isValid': isValid,
      'fieldErrors': fieldErrors,
      'generalErrors': generalErrors,
    };
  }

  static Map<String, dynamic> validatePersonalDetails(ApplicantDetailsData applicant) {
    final Map<String, String> fieldErrors = {};
    
    final titleError = DBSValidationRules.validateTitle(applicant.title);
    if (titleError != null) fieldErrors['title'] = titleError;
    
    final forenameError = DBSValidationRules.validateForename(applicant.forename);
    if (forenameError != null) fieldErrors['forename'] = forenameError;
    
    final middleNames = applicant.middlenames;
    if (middleNames.isNotEmpty) {
      if (middleNames.length > 3) {
        fieldErrors['middleNames'] = 'Maximum 3 middle names allowed';
      } else {
        for (int i = 0; i < middleNames.length; i++) {
          final middleNameError = DBSValidationRules.validateMiddleName(middleNames[i]);
          if (middleNameError != null) {
            fieldErrors['middleName$i'] = middleNameError;
          }
        }
      }
    }
    
    final surnameError = DBSValidationRules.validatePresentSurname(applicant.presentSurname);
    if (surnameError != null) fieldErrors['presentSurname'] = surnameError;

    final genderError = DBSValidationRules.validateGender(applicant.gender);
    if (genderError != null) fieldErrors['gender'] = genderError;
    
    final contactError = DBSValidationRules.validateContactNumber(applicant.contactNumber);
    if (contactError != null) fieldErrors['contactNumber'] = contactError;
    
    final niError = DBSValidationRules.validateNINumber(applicant.niNumber);
    if (niError != null) fieldErrors['niNumber'] = niError;
    
    return createValidationResult(
      isValid: fieldErrors.isEmpty,
      fieldErrors: fieldErrors,
    );
  }

  static Map<String, dynamic> validateBirthDetails(ApplicantDetailsData applicant) {
    final Map<String, String> fieldErrors = {};
    final additional = applicant.additionalApplicantDetails;

    final dobError = DBSValidationRules.validateDateOfBirth(applicant.dateOfBirth);
    if (dobError != null) fieldErrors['dateOfBirth'] = dobError;

    final birthSurnameError = DBSValidationRules.validateBirthSurname(
      additional.birthSurname,
      applicant.gender,
      applicant.title,
    );
    if (birthSurnameError != null) fieldErrors['birthSurname'] = birthSurnameError;
    
    final birthSurnameUntilError = DBSValidationRules.validateBirthSurnameUntil(
      additional.birthSurnameUntil,
      applicant.dateOfBirth,
    );
    if (birthSurnameUntilError != null) fieldErrors['birthSurnameUntil'] = birthSurnameUntilError;
    
    final birthTownError = DBSValidationRules.validateBirthTown(additional.birthTown);
    if (birthTownError != null) fieldErrors['birthTown'] = birthTownError;
    
    final birthCountyError = DBSValidationRules.validateBirthCounty(additional.birthCounty);
    if (birthCountyError != null) fieldErrors['birthCounty'] = birthCountyError;
    
    final birthCountryError = DBSValidationRules.validateBirthCountry(additional.birthCountry);
    if (birthCountryError != null) fieldErrors['birthCountry'] = birthCountryError;
    
    final birthNationalityError = DBSValidationRules.validateBirthNationality(additional.birthNationality);
    if (birthNationalityError != null) fieldErrors['birthNationality'] = birthNationalityError;
    
    return createValidationResult(
      isValid: fieldErrors.isEmpty,
      fieldErrors: fieldErrors,
    );
  }

  /// Validate Address Details Step (Step 2)
  static Map<String, dynamic> validateAddressDetails(ApplicantDetailsData applicant) {
    final Map<String, String> fieldErrors = {};
    final List<String> generalErrors = [];

    // Check if 5-year address history is complete first
    final has5YearHistory = _validate5YearAddressHistory(applicant);

    // If 5-year history is complete, validation passes regardless of current address form fields
    if (has5YearHistory) {
      return createValidationResult(
        isValid: true,
        fieldErrors: {},
        generalErrors: [],
      );
    }

    // If 5-year history is not complete, validate current address fields
    final currentAddress = applicant.currentAddress;

    // Address line 1 validation
    final line1Error = DBSValidationRules.validateAddressLine1(currentAddress.addressLine1);
    if (line1Error != null) fieldErrors['addressLine1'] = line1Error;

    // Address line 2 validation (optional)
    final line2Error = DBSValidationRules.validateAddressLine2(currentAddress.addressLine2);
    if (line2Error != null) fieldErrors['addressLine2'] = line2Error;

    // Address town validation
    final townError = DBSValidationRules.validateAddressTown(currentAddress.addressTown);
    if (townError != null) fieldErrors['addressTown'] = townError;

    // Address county validation (optional)
    final countyError = DBSValidationRules.validateAddressCounty(currentAddress.addressCounty);
    if (countyError != null) fieldErrors['addressCounty'] = countyError;

    // Country code validation
    final countryError = DBSValidationRules.validateCountryCode(currentAddress.countryCode);
    if (countryError != null) fieldErrors['countryCode'] = countryError;

    // Postcode validation (mandatory for GB)
    final postcodeError = DBSValidationRules.validatePostcode(
      currentAddress.postcode,
      currentAddress.countryCode,
    );
    if (postcodeError != null) {
      fieldErrors['postcode'] = postcodeError;
      // Add specific error message for missing postcode in UK addresses
      if (currentAddress.countryCode.toUpperCase() == 'GB' &&
          (currentAddress.postcode.isEmpty || currentAddress.postcode.trim().isEmpty)) {
        generalErrors.add('Postcode is required for UK addresses. Please enter a valid UK postcode before proceeding.');
      }
    }

    // Resident from date validation
    final residentFromError = DBSValidationRules.validateResidentFromYearMonth(
      currentAddress.residentFromGyearMonth,
      applicant.dateOfBirth,
    );
    if (residentFromError != null) fieldErrors['residentFromGyearMonth'] = residentFromError;
    
    // Previous addresses validation
    final previousAddresses = applicant.previousAddresses;
    for (int i = 0; i < previousAddresses.length; i++) {
      final prevAddress = previousAddresses[i];
      
      // Validate each previous address
      final prevLine1Error = DBSValidationRules.validateAddressLine1(prevAddress.addressLine1);
      if (prevLine1Error != null) fieldErrors['prevAddressLine1_$i'] = prevLine1Error;
      
      final prevTownError = DBSValidationRules.validateAddressTown(prevAddress.addressTown);
      if (prevTownError != null) fieldErrors['prevAddressTown_$i'] = prevTownError;
      
      final prevCountryError = DBSValidationRules.validateCountryCode(prevAddress.countryCode);
      if (prevCountryError != null) fieldErrors['prevCountryCode_$i'] = prevCountryError;
      
      final prevPostcodeError = DBSValidationRules.validatePostcode(
        prevAddress.postcode,
        prevAddress.countryCode,
      );
      if (prevPostcodeError != null) {
        fieldErrors['prevPostcode_$i'] = prevPostcodeError;
        // Add specific error message for missing postcode in UK previous addresses
        if (prevAddress.countryCode.toUpperCase() == 'GB' &&
            (prevAddress.postcode.isEmpty || prevAddress.postcode.trim().isEmpty)) {
          generalErrors.add('Postcode is required for UK addresses in previous address ${i + 1}. Please enter a valid UK postcode.');
        }
      }
      
      final prevFromError = DBSValidationRules.validateResidentFromYearMonth(
        prevAddress.residentFromGyearMonth,
        applicant.dateOfBirth,
      );
      if (prevFromError != null) fieldErrors['prevResidentFrom_$i'] = prevFromError;
      
      final prevToError = DBSValidationRules.validateResidentToYearMonth(
        prevAddress.residentToGyearMonth,
        prevAddress.residentFromGyearMonth,
        applicant.dateOfBirth,
      );
      if (prevToError != null) fieldErrors['prevResidentTo_$i'] = prevToError;
    }

    // If we reach here, 5-year history is not complete, so add general error
    generalErrors.add('Address history must cover a full 5 years up to current date with no gaps');

    return createValidationResult(
      isValid: fieldErrors.isEmpty && generalErrors.isEmpty,
      fieldErrors: fieldErrors,
      generalErrors: generalErrors,
    );
  }

  /// Validate Other Names Step (Step 3)
  static Map<String, dynamic> validateOtherNames(ApplicantDetailsData applicant) {
    final Map<String, String> fieldErrors = {};
    final additional = applicant.additionalApplicantDetails;
    
    // Other surnames validation
    final otherSurnames = additional.otherSurnames;
    for (int i = 0; i < otherSurnames.length; i++) {
      final surname = otherSurnames[i];
      
      // Name validation
      final nameError = DBSValidationRules.validatePresentSurname(surname.name);
      if (nameError != null) fieldErrors['otherSurnameName_$i'] = nameError;
      
      // Used from validation
      final usedFromError = DBSValidationRules.validateBirthSurnameUntil(
        surname.usedFrom,
        applicant.dateOfBirth,
      );
      if (usedFromError != null) fieldErrors['otherSurnameFrom_$i'] = usedFromError;
      
      // Used to validation
      final usedToError = DBSValidationRules.validateBirthSurnameUntil(
        surname.usedTo,
        applicant.dateOfBirth,
      );
      if (usedToError != null) fieldErrors['otherSurnameTo_$i'] = usedToError;
      
      // Used to must be >= used from
      if (surname.usedFrom.isNotEmpty && surname.usedTo.isNotEmpty) {
        try {
          final fromYear = int.parse(surname.usedFrom);
          final toYear = int.parse(surname.usedTo);
          if (toYear < fromYear) {
            fieldErrors['otherSurnameTo_$i'] = 'Used to year must be same as or later than used from year';
          }
        } catch (e) {
          // Error will be caught by individual field validation
        }
      }
    }
    
    // Other forenames validation
    final otherForenames = additional.otherForenames;
    for (int i = 0; i < otherForenames.length; i++) {
      final forename = otherForenames[i];
      
      // Name validation
      final nameError = DBSValidationRules.validateForename(forename.name);
      if (nameError != null) fieldErrors['otherForenameName_$i'] = nameError;
      
      // Used from validation
      final usedFromError = DBSValidationRules.validateBirthSurnameUntil(
        forename.usedFrom,
        applicant.dateOfBirth,
      );
      if (usedFromError != null) fieldErrors['otherForenameFrom_$i'] = usedFromError;
      
      // Used to validation
      final usedToError = DBSValidationRules.validateBirthSurnameUntil(
        forename.usedTo,
        applicant.dateOfBirth,
      );
      if (usedToError != null) fieldErrors['otherForenameTo_$i'] = usedToError;
      
      // Used to must be >= used from
      if (forename.usedFrom.isNotEmpty && forename.usedTo.isNotEmpty) {
        try {
          final fromYear = int.parse(forename.usedFrom);
          final toYear = int.parse(forename.usedTo);
          if (toYear < fromYear) {
            fieldErrors['otherForenameTo_$i'] = 'Used to year must be same as or later than used from year';
          }
        } catch (e) {
          // Error will be caught by individual field validation
        }
      }
    }
    
    return createValidationResult(
      isValid: fieldErrors.isEmpty,
      fieldErrors: fieldErrors,
    );
  }

  /// Helper method to validate 5-year address history
  static bool _validate5YearAddressHistory(ApplicantDetailsData applicant) {
    final currentAddress = applicant.currentAddress;
    final previousAddresses = applicant.previousAddresses;

    // If no addresses at all, not valid
    if (currentAddress.addressLine1.isEmpty && previousAddresses.isEmpty) {
      return false;
    }

    final now = DateTime.now();
    final fiveYearsAgo = DateTime(now.year - 5, now.month, 1);

    // Collect all addresses with their date ranges
    final allAddresses = <Map<String, dynamic>>[];

    // Add current address
    if (currentAddress.addressLine1.isNotEmpty) {
      DateTime? fromDate;
      if (currentAddress.residentFromGyearMonth.isNotEmpty) {
        final parts = currentAddress.residentFromGyearMonth.split('-');
        if (parts.length == 2) {
          final year = int.tryParse(parts[0]);
          final month = int.tryParse(parts[1]);
          if (year != null && month != null) {
            fromDate = DateTime(year, month, 1);
          }
        }
      }

      allAddresses.add({
        'fromDate': fromDate,
        'toDate': now, // Current address goes to present
      });
    }

    // Add previous addresses
    for (final prevAddress in previousAddresses) {
      DateTime? fromDate;
      DateTime? toDate;

      if (prevAddress.residentFromGyearMonth.isNotEmpty) {
        final parts = prevAddress.residentFromGyearMonth.split('-');
        if (parts.length == 2) {
          final year = int.tryParse(parts[0]);
          final month = int.tryParse(parts[1]);
          if (year != null && month != null) {
            fromDate = DateTime(year, month, 1);
          }
        }
      }

      if (prevAddress.residentToGyearMonth.isNotEmpty) {
        final parts = prevAddress.residentToGyearMonth.split('-');
        if (parts.length == 2) {
          final year = int.tryParse(parts[0]);
          final month = int.tryParse(parts[1]);
          if (year != null && month != null) {
            toDate = DateTime(year, month, 1);
          }
        }
      }

      if (fromDate != null) {
        allAddresses.add({
          'fromDate': fromDate,
          'toDate': toDate ?? now,
        });
      }
    }

    // Sort addresses by from date
    allAddresses.sort((a, b) {
      final dateA = a['fromDate'] as DateTime?;
      final dateB = b['fromDate'] as DateTime?;
      if (dateA == null || dateB == null) return 0;
      return dateA.compareTo(dateB);
    });

    // Check if we have continuous coverage from 5 years ago to present
    return _checkContinuousAddressCoverage(allAddresses, fiveYearsAgo, now);
  }

  /// Check if addresses provide continuous coverage for the required period
  static bool _checkContinuousAddressCoverage(List<Map<String, dynamic>> addresses, DateTime fiveYearsAgo, DateTime now) {
    if (addresses.isEmpty) return false;

    // Check if the earliest address starts at or before 5 years ago
    final earliestFromDate = addresses.first['fromDate'] as DateTime?;
    if (earliestFromDate == null || earliestFromDate.isAfter(fiveYearsAgo)) {
      return false; // Doesn't go back far enough
    }

    // Check if the latest address covers up to present
    final latestAddress = addresses.last;
    final latestToDate = latestAddress['toDate'] as DateTime?;
    if (latestToDate == null) return false;

    // Check for gaps between consecutive addresses
    for (int i = 0; i < addresses.length - 1; i++) {
      final currentToDate = addresses[i]['toDate'] as DateTime?;
      final nextFromDate = addresses[i + 1]['fromDate'] as DateTime?;

      if (currentToDate == null || nextFromDate == null) continue;

      // Calculate month difference
      final currentEndMonth = currentToDate.year * 12 + currentToDate.month;
      final nextStartMonth = nextFromDate.year * 12 + nextFromDate.month;

      // There should be no gap (difference should be 1 month or less)
      if (nextStartMonth - currentEndMonth > 1) {
        return false; // Gap found
      }
    }

    return true;
  }

  /// Validate Supporting Documents Step (Step 4)
  static Map<String, dynamic> validateSupportingDocuments(ApplicantDetailsData applicant) {
    final Map<String, String> fieldErrors = {};
    final List<String> generalErrors = [];
    final identityDetails = applicant.applicantIdentityDetails;

    // Identity verified validation (ABV: Must be 'y')
    if (identityDetails.identityVerified != 'y') {
      fieldErrors['identityVerified'] = 'Identity must be verified to proceed';
    }

    // National Insurance Number validation
    // If user has provided a NI Number, it means they selected "Yes" and it must be valid
    if (identityDetails.nationalInsuranceNumber.isNotEmpty) {
      final niError = DBSValidationRules.validateNINumber(identityDetails.nationalInsuranceNumber);
      if (niError != null) {
        fieldErrors['nationalInsuranceNumber'] = niError;
        generalErrors.add('Please provide a valid National Insurance Number or select "No" if you do not have one.');
      }
    }

    // Evidence checked by validation
    if (identityDetails.evidenceCheckedBy.trim().isEmpty) {
      fieldErrors['evidenceCheckedBy'] = 'Evidence checked by is required';
    } else {
      final trimmed = identityDetails.evidenceCheckedBy.trim();
      if (trimmed.length > 60) {
        fieldErrors['evidenceCheckedBy'] = 'Evidence checked by must not exceed 60 characters';
      }

      // Pattern validation
      final pattern = RegExp(r"^([A-Z0-9\-']+)$");
      if (!pattern.hasMatch(trimmed.toUpperCase())) {
        fieldErrors['evidenceCheckedBy'] = 'Evidence checked by contains invalid characters. Only A-Z, 0-9, -, \' are allowed';
      }
    }

    // Passport details validation (if provided)
    final passportDetails = identityDetails.passportDetails;
    if (passportDetails != null) {
      final passportNumberError = DBSValidationRules.validatePassportNumber(passportDetails.passportNumber);
      if (passportNumberError != null) fieldErrors['passportNumber'] = passportNumberError;

      final passportDOBError = DBSValidationRules.validatePassportDOB(
        passportDetails.passportDob,
        applicant.dateOfBirth,
      );
      if (passportDOBError != null) fieldErrors['passportDob'] = passportDOBError;

      final passportNationalityError = DBSValidationRules.validatePassportNationality(passportDetails.passportNationality);
      if (passportNationalityError != null) fieldErrors['passportNationality'] = passportNationalityError;

      final passportIssueDateError = DBSValidationRules.validatePassportIssueDate(passportDetails.passportIssueDate);
      if (passportIssueDateError != null) fieldErrors['passportIssueDate'] = passportIssueDateError;
    }

    // Driver licence details validation (if provided)
    final driverLicenceDetails = identityDetails.driverLicenceDetails;
    if (driverLicenceDetails != null) {
      final licenceNumberError = DBSValidationRules.validateDriverLicenceNumber(driverLicenceDetails.driverLicenceNumber);
      if (licenceNumberError != null) fieldErrors['driverLicenceNumber'] = licenceNumberError;

      final licenceDOBError = DBSValidationRules.validateDriverLicenceDOB(
        driverLicenceDetails.driverLicenceDOB,
        applicant.dateOfBirth,
      );
      if (licenceDOBError != null) fieldErrors['driverLicenceDOB'] = licenceDOBError;

      final licenceTypeError = DBSValidationRules.validateDriverLicenceType(driverLicenceDetails.driverLicenceType);
      if (licenceTypeError != null) fieldErrors['driverLicenceType'] = licenceTypeError;

      final licenceValidFromError = DBSValidationRules.validateDriverLicenceValidFrom(driverLicenceDetails.driverLicenceValidFrom);
      if (licenceValidFromError != null) fieldErrors['driverLicenceValidFrom'] = licenceValidFromError;

      final licenceIssueCountryError = DBSValidationRules.validateCountryCode(driverLicenceDetails.driverLicenceIssueCountry);
      if (licenceIssueCountryError != null) fieldErrors['driverLicenceIssueCountry'] = licenceIssueCountryError;
    }

    return createValidationResult(
      isValid: fieldErrors.isEmpty && generalErrors.isEmpty,
      fieldErrors: fieldErrors,
      generalErrors: generalErrors,
    );
  }

  /// Validate Review Step (Step 5)
  static Map<String, dynamic> validateReview(DBSFormData formData) {
    final Map<String, String> fieldErrors = {};
    final List<String> generalErrors = [];

    // Validate all previous steps
    final personalValidation = validatePersonalDetails(formData.applicantDetails);
    final birthValidation = validateBirthDetails(formData.applicantDetails);
    final addressValidation = validateAddressDetails(formData.applicantDetails);
    final otherNamesValidation = validateOtherNames(formData.applicantDetails);
    final documentsValidation = validateSupportingDocuments(formData.applicantDetails);

    // Collect all errors
    if (!personalValidation['isValid']) {
      generalErrors.add('Personal details contain errors');
      fieldErrors.addAll(personalValidation['fieldErrors']);
    }

    if (!birthValidation['isValid']) {
      generalErrors.add('Birth details contain errors');
      fieldErrors.addAll(birthValidation['fieldErrors']);
    }

    if (!addressValidation['isValid']) {
      generalErrors.add('Address details contain errors');
      fieldErrors.addAll(addressValidation['fieldErrors']);
    }

    if (!otherNamesValidation['isValid']) {
      generalErrors.add('Other names contain errors');
      fieldErrors.addAll(otherNamesValidation['fieldErrors']);
    }

    if (!documentsValidation['isValid']) {
      generalErrors.add('Supporting documents contain errors');
      fieldErrors.addAll(documentsValidation['fieldErrors']);
    }

    return createValidationResult(
      isValid: fieldErrors.isEmpty && generalErrors.isEmpty,
      fieldErrors: fieldErrors,
      generalErrors: generalErrors,
    );
  }

  static Map<String, dynamic> validateConsent(ApplicantDetailsData applicant) {
    final Map<String, String> fieldErrors = {};
    final additional = applicant.additionalApplicantDetails;

    final convictionsError = DBSValidationRules.validateUnspentConvictions(additional.unspentConvictions);
    if (convictionsError != null) fieldErrors['unspentConvictions'] = convictionsError;

    final declarationError = DBSValidationRules.validateDeclarationByApplicant(additional.declarationByApplicant);
    if (declarationError != null) fieldErrors['declarationByApplicant'] = declarationError;

    final languageError = DBSValidationRules.validateLanguagePreference(additional.languagePreference);
    if (languageError != null) fieldErrors['languagePreference'] = languageError;

    return createValidationResult(
      isValid: fieldErrors.isEmpty,
      fieldErrors: fieldErrors,
    );
  }

  static Map<String, dynamic> validateStep(int stepIndex, DBSFormData formData) {
    switch (stepIndex) {
      case 0:
        return validatePersonalDetails(formData.applicantDetails);
      case 1:
        return validateBirthDetails(formData.applicantDetails);
      case 2:
        return validateAddressDetails(formData.applicantDetails);
      case 3:
        return validateOtherNames(formData.applicantDetails);
      case 4:
        return validateSupportingDocuments(formData.applicantDetails);
      case 5:
        return validateReview(formData);
      case 6:
        return validateConsent(formData.applicantDetails);
      default:
        return createValidationResult(
          isValid: false,
          fieldErrors: {'general': 'Invalid step index'},
        );
    }
  }

  /// Get all validation errors for the entire form
  static Map<String, dynamic> validateEntireForm(DBSFormData formData) {
    final Map<String, String> allFieldErrors = {};
    final List<String> allGeneralErrors = [];
    bool isFormValid = true;

    for (int i = 0; i <= 6; i++) {
      final stepValidation = validateStep(i, formData);
      if (!stepValidation['isValid']) {
        isFormValid = false;
        allFieldErrors.addAll(stepValidation['fieldErrors']);
        allGeneralErrors.addAll(stepValidation['generalErrors']);
      }
    }

    return createValidationResult(
      isValid: isFormValid,
      fieldErrors: allFieldErrors,
      generalErrors: allGeneralErrors,
    );
  }
}
