import 'dart:convert';

import 'package:SolidCheck/features/dbs/data/models/dbs_form_data.dart';
import 'package:SolidCheck/features/dbs/services/dbs_encryption_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

class DBSPersistenceService {
  static final String _dataPrefix = 'dbs_form_data_';
  static final String _metadataPrefix = 'dbs_form_metadata_';
  static final String _lastSavePrefix = 'dbs_last_save_';
  
  final DBSEncryptionService _encryptionService;
  late final SharedPreferences _prefs;
  bool _isInitialized = false;
  int totalSteps = 7;

  DBSPersistenceService._internal() : _encryptionService = DBSEncryptionService();
  static final DBSPersistenceService _instance = DBSPersistenceService._internal();
  factory DBSPersistenceService() => _instance;

  Future<void> initialize() async {
    if (!_isInitialized) {
      _prefs = await SharedPreferences.getInstance();
      _isInitialized = true;
    }
  }

  String _getDataKey(String applicantId, String applicationId) {
    return '$_dataPrefix${applicantId}_$applicationId';
  }

  String _getMetadataKey(String applicantId, String applicationId) {
    return '$_metadataPrefix${applicantId}_$applicationId';
  }

  String _getLastSaveKey(String applicantId, String applicationId) {
    return '$_lastSavePrefix${applicantId}_$applicationId';
  }

  Future<bool> saveFormData({
    required String applicantId,
    required String applicationId,
    required DBSFormData formData,
    required int currentStep,
    bool isAutoSave = false,
  }) async {
    try {
      await initialize();
      
      // Initialize encryption service
      try {
        _encryptionService.initialize(applicantId, applicationId);
      } catch (e) {
        throw PersistenceException('Failed to initialize encryption: $e');
      }

      final formDataMap = formData.toJson();
      // For now, store data without encryption to avoid complexity
      final encryptedData = formDataMap;
      
      final metadata = FormSaveMetadata(
        applicantId: applicantId,
        applicationId: applicationId,
        currentStep: currentStep,
        saveTimestamp: DateTime.now(),
        isAutoSave: isAutoSave,
        dataVersion: '1.0',
        completionStatus: _calculateCompletionStatus(formData, currentStep),
      );

      final dataKey = _getDataKey(applicantId, applicationId);
      final metadataKey = _getMetadataKey(applicantId, applicationId);
      final lastSaveKey = _getLastSaveKey(applicantId, applicationId);

      final success = await _prefs.setString(dataKey, jsonEncode(encryptedData)) &&
                     await _prefs.setString(metadataKey, jsonEncode(metadata.toJson())) &&
                     await _prefs.setString(lastSaveKey, DateTime.now().toIso8601String());

      return success;
    } catch (e) {
      throw PersistenceException('Failed to save form data: $e');
    }
  }

  Future<DBSFormData?> loadFormData({
    required String applicantId,
    required String applicationId,
  }) async {
    try {
      await initialize();
      
      final dataKey = _getDataKey(applicantId, applicationId);
      final encryptedDataString = _prefs.getString(dataKey);
      
      if (encryptedDataString == null) {
        return null;
      }

      // Initialize encryption service
      try {
        _encryptionService.initialize(applicantId, applicationId);
      } catch (e) {
        throw PersistenceException('Failed to initialize encryption: $e');
      }

      final formDataMap = jsonDecode(encryptedDataString) as Map<String, dynamic>;
      // For now, load data without decryption

      return DBSFormData.fromJson(formDataMap);
    } catch (e) {
      throw PersistenceException('Failed to load form data: $e');
    }
  }

  Future<FormSaveMetadata?> getFormMetadata({
    required String applicantId,
    required String applicationId,
  }) async {
    try {
      await initialize();
      
      final metadataKey = _getMetadataKey(applicantId, applicationId);
      final metadataString = _prefs.getString(metadataKey);
      
      if (metadataString == null) {
        return null;
      }

      final metadataMap = jsonDecode(metadataString) as Map<String, dynamic>;
      return FormSaveMetadata.fromJson(metadataMap);
    } catch (e) {
      throw PersistenceException('Failed to load form metadata: $e');
    }
  }

  Future<bool> hasSavedData({
    required String applicantId,
    required String applicationId,
  }) async {
    await initialize();
    final dataKey = _getDataKey(applicantId, applicationId);
    return _prefs.containsKey(dataKey);
  }

  Future<DateTime?> getLastSaveTime({
    required String applicantId,
    required String applicationId,
  }) async {
    await initialize();
    final lastSaveKey = _getLastSaveKey(applicantId, applicationId);
    final lastSaveString = _prefs.getString(lastSaveKey);
    
    if (lastSaveString == null) return null;
    
    try {
      return DateTime.parse(lastSaveString);
    } catch (e) {
      return null;
    }
  }

  Future<bool> clearSavedData({
    required String applicantId,
    required String applicationId,
  }) async {
    try {
      await initialize();
      
      final dataKey = _getDataKey(applicantId, applicationId);
      final metadataKey = _getMetadataKey(applicantId, applicationId);
      final lastSaveKey = _getLastSaveKey(applicantId, applicationId);

      final success = await _prefs.remove(dataKey) &&
                     await _prefs.remove(metadataKey) &&
                     await _prefs.remove(lastSaveKey);

      _encryptionService.clearEncryptionData();
      
      return success;
    } catch (e) {
      throw PersistenceException('Failed to clear saved data: $e');
    }
  }

  Future<List<FormSaveMetadata>> getAllSavedForms() async {
    await initialize();
    
    final allKeys = _prefs.getKeys();
    final metadataKeys = allKeys.where((key) => key.startsWith(_metadataPrefix));
    
    final savedForms = <FormSaveMetadata>[];
    
    for (final key in metadataKeys) {
      try {
        final metadataString = _prefs.getString(key);
        if (metadataString != null) {
          final metadataMap = jsonDecode(metadataString) as Map<String, dynamic>;
          savedForms.add(FormSaveMetadata.fromJson(metadataMap));
        }
      } catch (e) {
        continue;
      }
    }
    
    return savedForms;
  }

  double _calculateCompletionStatus(DBSFormData formData, int currentStep) {
    totalSteps = 7;
    double stepProgress = currentStep / totalSteps;
    
    double dataCompleteness = 0.0;
    int completedSections = 0;
    
    if (_isPersonalDetailsComplete(formData.applicantDetails)) completedSections++;
    if (_isBirthDetailsComplete(formData.applicantDetails)) completedSections++;
    if (_isAddressDetailsComplete(formData.applicantDetails)) completedSections++;
    if (_isOtherNamesComplete(formData.applicantDetails)) completedSections++;
    if (_isSupportingDocsComplete(formData.applicantDetails)) completedSections++;
    
    dataCompleteness = completedSections / 5.0;
    
    return (stepProgress + dataCompleteness) / 2.0;
  }

  bool _isPersonalDetailsComplete(ApplicantDetailsData applicant) {
    return applicant.title.isNotEmpty &&
           applicant.forename.isNotEmpty &&
           applicant.presentSurname.isNotEmpty &&
           applicant.gender.isNotEmpty &&
           applicant.contactNumber.isNotEmpty;
  }

  bool _isBirthDetailsComplete(ApplicantDetailsData applicant) {
    final additional = applicant.additionalApplicantDetails;
    return additional.birthTown.isNotEmpty &&
           additional.birthCounty.isNotEmpty &&
           additional.birthCountry.isNotEmpty &&
           additional.birthNationality.isNotEmpty;
  }

  bool _isAddressDetailsComplete(ApplicantDetailsData applicant) {
    final currentAddress = applicant.currentAddress;
    return currentAddress.addressLine1.isNotEmpty &&
           currentAddress.addressTown.isNotEmpty &&
           currentAddress.postcode.isNotEmpty &&
           currentAddress.countryCode.isNotEmpty;
  }

  bool _isOtherNamesComplete(ApplicantDetailsData applicant) {
    return true;
  }

  bool _isSupportingDocsComplete(ApplicantDetailsData applicant) {
    final identity = applicant.applicantIdentityDetails;
    return identity.nationalInsuranceNumber.isNotEmpty ||
           identity.passportDetails != null ||
           identity.driverLicenceDetails != null;
  }
}

class FormSaveMetadata {
  final String applicantId;
  final String applicationId;
  final int currentStep;
  final DateTime saveTimestamp;
  final bool isAutoSave;
  final String dataVersion;
  final double completionStatus;

  FormSaveMetadata({
    required this.applicantId,
    required this.applicationId,
    required this.currentStep,
    required this.saveTimestamp,
    required this.isAutoSave,
    required this.dataVersion,
    required this.completionStatus,
  });

  Map<String, dynamic> toJson() => {
    'applicantId': applicantId,
    'applicationId': applicationId,
    'currentStep': currentStep,
    'saveTimestamp': saveTimestamp.toIso8601String(),
    'isAutoSave': isAutoSave,
    'dataVersion': dataVersion,
    'completionStatus': completionStatus,
  };

  factory FormSaveMetadata.fromJson(Map<String, dynamic> json) => FormSaveMetadata(
    applicantId: json['applicantId'],
    applicationId: json['applicationId'],
    currentStep: json['currentStep'],
    saveTimestamp: DateTime.parse(json['saveTimestamp']),
    isAutoSave: json['isAutoSave'],
    dataVersion: json['dataVersion'],
    completionStatus: json['completionStatus'],
  );
}

class PersistenceException implements Exception {
  final String message;
  PersistenceException(this.message);
  
  @override
  String toString() => 'PersistenceException: $message';
}
