import 'dart:convert';
import 'dart:math';

class DBSEncryptionService {
  String? _sessionKey;
  String? _sessionId;
  Set<String> sensitiveFields = {};

  DBSEncryptionService._internal();
  static final DBSEncryptionService _instance = DBSEncryptionService._internal();
  factory DBSEncryptionService() => _instance;

  void initialize(String applicantId, String applicationId) {
    _sessionId = '${applicantId}_$applicationId';
    _sessionKey = _generateSessionKey(_sessionId!);
  }

  bool get isInitialized => _sessionKey != null && _sessionId != null;

  String _generateSessionKey(String sessionId) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final combined = '$sessionId$timestamp';
    final bytes = utf8.encode(combined);

    // Simple hash-based key generation
    int hash = 0;
    for (int byte in bytes) {
      hash = ((hash << 5) - hash + byte) & 0xFFFFFFFF;
    }

    return hash.toRadixString(16).padLeft(8, '0');
  }

  String encryptSensitiveData(String data) {
    if (data.isEmpty) return data;
    if (!isInitialized) {
      throw EncryptionException('Encryption service not initialized. Call initialize() first.');
    }

    try {
      final dataBytes = utf8.encode(data);
      final keyBytes = utf8.encode(_sessionKey!);
      final encrypted = <int>[];

      for (int i = 0; i < dataBytes.length; i++) {
        encrypted.add(dataBytes[i] ^ keyBytes[i % keyBytes.length]);
      }

      return base64.encode(encrypted);
    } catch (e) {
      throw EncryptionException('Failed to encrypt data: $e');
    }
  }

  String decryptSensitiveData(String encryptedData) {
    if (encryptedData.isEmpty) return encryptedData;
    if (!isInitialized) {
      throw DecryptionException('Encryption service not initialized. Call initialize() first.');
    }

    try {
      final encryptedBytes = base64.decode(encryptedData);
      final keyBytes = utf8.encode(_sessionKey!);
      final decrypted = <int>[];

      for (int i = 0; i < encryptedBytes.length; i++) {
        decrypted.add(encryptedBytes[i] ^ keyBytes[i % keyBytes.length]);
      }

      return utf8.decode(decrypted);
    } catch (e) {
      throw DecryptionException('Failed to decrypt data: $e');
    }
  }

  Map<String, dynamic> encryptFormData(Map<String, dynamic> formData) {
    if (!isInitialized) {
      throw EncryptionException('Encryption service not initialized. Call initialize() first.');
    }

    final encryptedData = <String, dynamic>{};

    for (final entry in formData.entries) {
      if (_isSensitiveField(entry.key)) {
        if (entry.value is String) {
          encryptedData[entry.key] = encryptSensitiveData(entry.value);
        } else if (entry.value is Map<String, dynamic>) {
          encryptedData[entry.key] = encryptFormData(entry.value);
        } else if (entry.value is List) {
          encryptedData[entry.key] = _encryptList(entry.value);
        } else {
          encryptedData[entry.key] = entry.value;
        }
      } else {
        encryptedData[entry.key] = entry.value;
      }
    }
    
    return encryptedData;
  }

  Map<String, dynamic> decryptFormData(Map<String, dynamic> encryptedData) {
    if (!isInitialized) {
      throw DecryptionException('Encryption service not initialized. Call initialize() first.');
    }

    final decryptedData = <String, dynamic>{};

    for (final entry in encryptedData.entries) {
      if (_isSensitiveField(entry.key)) {
        if (entry.value is String) {
          decryptedData[entry.key] = decryptSensitiveData(entry.value);
        } else if (entry.value is Map<String, dynamic>) {
          decryptedData[entry.key] = decryptFormData(entry.value);
        } else if (entry.value is List) {
          decryptedData[entry.key] = _decryptList(entry.value);
        } else {
          decryptedData[entry.key] = entry.value;
        }
      } else {
        decryptedData[entry.key] = entry.value;
      }
    }
    
    return decryptedData;
  }

  List<dynamic> _encryptList(List<dynamic> list) {
    return list.map((item) {
      if (item is String) {
        return encryptSensitiveData(item);
      } else if (item is Map<String, dynamic>) {
        return encryptFormData(item);
      }
      return item;
    }).toList();
  }

  List<dynamic> _decryptList(List<dynamic> list) {
    return list.map((item) {
      if (item is String) {
        return decryptSensitiveData(item);
      } else if (item is Map<String, dynamic>) {
        return decryptFormData(item);
      }
      return item;
    }).toList();
  }

  bool _isSensitiveField(String fieldName) {
    sensitiveFields = {
      'title', 'forename', 'middleNames', 'presentSurname',
      'dateOfBirth', 'gender', 'contactNumber', 'niNumber',
      'birthTown', 'birthCounty', 'birthCountry', 'birthNationality',
      'birthSurname', 'addressLine1', 'addressLine2', 'addressTown',
      'addressCounty', 'postcode', 'country', 'passportNumber',
      'passportNationality', 'passportDateOfBirth', 'passportIssueDate',
      'driverLicenceNumber', 'driverLicenceType', 'driverLicenceValidFrom',
      'driverLicenceIssueCountry', 'driverLicenceDateOfBirth',
      'nationalInsuranceNumber', 'otherSurnames', 'otherForenames',
      'addressHistory', 'name', 'usedFrom', 'usedTo'
    };
    
    return sensitiveFields.contains(fieldName.toLowerCase()) ||
           fieldName.toLowerCase().contains('name') ||
           fieldName.toLowerCase().contains('address') ||
           fieldName.toLowerCase().contains('birth') ||
           fieldName.toLowerCase().contains('passport') ||
           fieldName.toLowerCase().contains('licence') ||
           fieldName.toLowerCase().contains('insurance');
  }

  void clearEncryptionData() {
    _sessionId = null;
    _sessionKey = null;
  }
}

class EncryptionException implements Exception {
  final String message;
  EncryptionException(this.message);
  
  @override
  String toString() => 'EncryptionException: $message';
}

class DecryptionException implements Exception {
  final String message;
  DecryptionException(this.message);
  
  @override
  String toString() => 'DecryptionException: $message';
}
