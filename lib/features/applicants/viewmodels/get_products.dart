import 'package:SolidCheck/features/applicants/data/models/products.dart';
import 'package:SolidCheck/features/applicants/data/repositories/products.dart';
import 'package:SolidCheck/features/applicants/viewmodels/job_role_name_list.dart';
import 'package:SolidCheck/features/auth/data/repositories/auth.dart';
// import 'package:SolidCheck/features/dashboard/viewmodels/get_applicants_list.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final productsServiceProvider = Provider<ProductsRepository>((ref) {
  final authRepository = ref.watch(authProvider);
  return ProductsRepository(authRepository);
});

final productsProvider =
    FutureProvider.family<Products, int>((ref, optionId) async {
  final service = ref.read(productsServiceProvider);
  try {
    final products = await service.getProducts(optionId);
    return products;
  } catch (e) {
    throw Exception('Failed to fetch products: $e');
  }
});

final selectedProductProvider = FutureProvider<Products>((ref) async {
  final selectedOptionId = ref.watch(selectedOptionIdProvider);
  if (selectedOptionId != null) {
    return ref.watch(productsProvider(selectedOptionId).future);
  } else {
    throw Exception('No job role selected');
  }
});
