import 'package:SolidCheck/features/applicants/data/models/get_clients.dart';
import 'package:SolidCheck/features/applicants/data/repositories/get_clients.dart';
import 'package:SolidCheck/features/auth/data/repositories/auth.dart';
// import 'package:SolidCheck/features/dashboard/viewmodels/get_applicants_list.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final clientsRepositoryProvider = Provider<ClientsRepository>((ref) {
  final authRepository = ref.watch(authProvider);
  return ClientsRepository(authRepository);
});

final clientsListProvider = FutureProvider<List<GetClients>>((ref) async {
  final repository = ref.read(clientsRepositoryProvider);
  try {
    final clients = await repository.getClients();
    return clients;
  } catch (e) {
    throw Exception('Failed to fetch clients: $e');
  }
});
