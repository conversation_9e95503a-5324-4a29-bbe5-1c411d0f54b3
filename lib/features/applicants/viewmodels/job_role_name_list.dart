import 'package:SolidCheck/features/applicants/data/models/job_role_name_list.dart';
import 'package:SolidCheck/features/applicants/data/repositories/job_role_name_list.dart';
import 'package:SolidCheck/features/auth/data/repositories/auth.dart';
// import 'package:SolidCheck/features/dashboard/viewmodels/get_applicants_list.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final jobRoleNameServiceProvider = Provider<JobRoleRepository>((ref) {
  final authRepository = ref.watch(authProvider);
  return JobRoleRepository(authRepository);
});

final jobRoleNameListProvider =
    FutureProvider.family<List<JobRoleNameListModel>, int>(
        (ref, accountId) async {
  final service = ref.watch(jobRoleNameServiceProvider);
  return await service.getJobRoleList(accountId);
});

final selectedOptionIdProvider = StateProvider<int?>((ref) => null);

final jobRoleOptionIdsProvider =
    FutureProvider.family<List<int>, int>((ref, accountId) async {
  final jobRoles = await ref.watch(jobRoleNameListProvider(accountId).future);

  if (jobRoles.isNotEmpty) {
    return jobRoles.first.optionIds ?? [];
  }
  return [];
});
