import 'package:SolidCheck/features/applicants/data/models/job_role_name_list.dart';
import 'package:SolidCheck/features/applicants/data/services/job_role_name_list.dart';
import 'package:SolidCheck/features/auth/data/repositories/auth.dart';

class JobRoleRepository {
  final AuthRepository authRepository;
  final JobRolelistNameService _jobRoleService;

  JobRoleRepository(this.authRepository)
      : _jobRoleService = JobRolelistNameService();

  Future<List<JobRoleNameListModel>> getJobRoleList(int accountId) async {
    await authRepository.isLoggedIn();
    final token = await authRepository.getToken();

    if (token == null || token.isEmpty) {
      throw Exception('No authentication token found.');
    }

    try {
      final jsonResponse =
          await _jobRoleService.fetchJobRoleListRequest(token, accountId);

      return (jsonResponse as List<dynamic>)
          .map((job) => JobRoleNameListModel.fromJson(job))
          .toList();
    } catch (e) {
      if (e.toString().contains('401')) {
        await authRepository.refreshToken();
        return getJobRoleList(accountId);
      }

      throw Exception('Failed to fetch job roles: ${e.toString()}');
    }
  }
}
