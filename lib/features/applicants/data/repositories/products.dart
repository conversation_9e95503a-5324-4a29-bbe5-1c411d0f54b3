import 'package:SolidCheck/features/applicants/data/models/products.dart';
import 'package:SolidCheck/features/applicants/data/services/products.dart';
import 'package:SolidCheck/features/auth/data/repositories/auth.dart';

class ProductsRepository {
  final AuthRepository authRepository;

  ProductsRepository(this.authRepository);

  Future<Products> getProducts(int optionId) async {
    await authRepository.isLoggedIn();
    final token = await authRepository.getToken();

    if (token == null) {
      throw Exception('No authentication token found.');
    }

    final service = ProductsService(token);

    try {
      return await service.fetchProducts(optionId);
    } on Exception catch (e) {
      if (e.toString().contains('401')) {
        await authRepository.refreshToken();
        return getProducts(optionId);
      } else {
        rethrow;
      }
    }
  }
}
