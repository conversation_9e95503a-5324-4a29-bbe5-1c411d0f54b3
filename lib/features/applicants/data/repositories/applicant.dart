import 'dart:convert';

import 'package:SolidCheck/features/applicants/data/models/add_applicant_request.dart';
import 'package:SolidCheck/features/applicants/data/models/add_applicant_response.dart';
import 'package:SolidCheck/features/applicants/data/models/applicant.dart';
import 'package:SolidCheck/features/applicants/data/services/applicant_api.dart';
import 'package:SolidCheck/features/applicants/data/services/applicant_api_service.dart' as detailed_api;
import 'package:SolidCheck/features/auth/data/repositories/auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Removed duplicate authProvider - using the one from auth.dart

var formStateProvider = StateProvider<List<Map<String, String?>>>(
  (ref) => [],
);

final applicantRepositoryProvider = Provider<ApplicantRepository>((ref) {
  return ApplicantRepository(
    ref.read(authProvider),
    ApplicantApiService(),
    detailed_api.ApplicantApiService(),
  );
});

class ApplicantRepository {
  final AuthRepository authRepository;
  final ApplicantApiService apiService;
  final detailed_api.ApplicantApiService detailedApiService;

  ApplicantRepository(this.authRepository, this.apiService, this.detailedApiService);

  Future<String> addApplicant(ApplicantModel applicantModel) async {
    try {
      await authRepository.isLoggedIn();
      final token = await authRepository.getToken();

      if (token == null) {
        throw Exception('No authentication token found.');
      }

      final response = await apiService.addApplicant(token, applicantModel);
      final responseData = response;

      return responseData['message'] ?? 'Applicant created successfully.';
    } catch (error) {
      final errorMessage = _extractErrorMessage(error);
      throw Exception('Failed to add applicant: $errorMessage');
    }
  }

  /// Add new applicant using the new API endpoint
  Future<AddApplicantResponse> addNewApplicant(AddApplicantRequest request) async {
    try {
      final token = await authRepository.getToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      final response = await detailedApiService.addApplicant(token, request);
      return response;
    } catch (e) {
      throw Exception('Failed to add applicant: $e');
    }
  }

  String _extractErrorMessage(dynamic error) {
    try {
      final decoded = jsonDecode(error.toString());
      if (decoded is Map<String, dynamic>) {
        if (decoded.containsKey('errors')) {
          final errors = decoded['errors'] as Map<String, dynamic>;
          return errors.entries.map((entry) {
            final field = entry.key;
            final messages = (entry.value as List).join(', ');
            return '$field: $messages';
          }).join('\n');
        } else if (decoded.containsKey('message')) {
          return decoded['message'];
        }
      }
    } catch (_) {}
    return error.toString();
  }
}
