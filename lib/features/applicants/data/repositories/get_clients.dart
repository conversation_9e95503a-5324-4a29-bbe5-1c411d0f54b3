import 'package:SolidCheck/features/applicants/data/models/get_clients.dart';
import 'package:SolidCheck/features/applicants/data/services/get_clients.dart';
import 'package:SolidCheck/features/auth/data/repositories/auth.dart';

class ClientsRepository {
  final AuthRepository authRepository;

  ClientsRepository(this.authRepository);

  Future<List<GetClients>> getClients() async {
    await authRepository.isLoggedIn();
    final token = await authRepository.getToken();

    if (token == null) {
      throw Exception('No authentication token found.');
    }

    final service = ClientsService(token);

    try {
      return await service.fetchClients();
    } catch (e) {
      final errorMsg = e.toString();

      if (errorMsg.contains('401')) {
        await authRepository.refreshToken();
        return getClients();
      }

      throw Exception('Failed to fetch clients: $errorMsg');
    }
  }
}
