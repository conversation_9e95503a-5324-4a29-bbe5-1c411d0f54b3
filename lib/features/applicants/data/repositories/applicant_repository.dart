import 'package:SolidCheck/features/applicants/data/models/applicant_detail_model.dart';
import 'package:SolidCheck/features/applicants/data/models/applicant_detail_response.dart';
import 'package:SolidCheck/features/applicants/data/services/applicant_api_service.dart';
import 'package:SolidCheck/features/auth/data/repositories/auth.dart';
import 'package:SolidCheck/features/auth/di/auth_providers.dart';

/// Applicant Repository
/// Handles data operations for applicant-related functionality
class ApplicantRepository {
  final AuthRepository _authRepository;
  late final ApplicantApiService _apiService;

  ApplicantRepository(this._authRepository) {
    _apiService = ApplicantApiService();
  }

  /// Get applicant details by ID using new API
  Future<ApplicantDetailResponse> getApplicantDetails(String applicantId) async {
    try {
      await _authRepository.isLoggedIn();
      final token = await _authRepository.getToken();

      if (token == null) {
        throw Exception('No authentication token found');
      }

      return await _apiService.getApplicantDetails(token, applicantId);
    } catch (error) {
      throw Exception('Failed to get applicant details: ${error.toString()}');
    }
  }

  /// Get applicant details by ID (legacy method for backward compatibility)
  Future<ApplicantDetailModel> getApplicantById(String applicantId) async {
    try {
      await _authRepository.isLoggedIn();
      final token = await _authRepository.getToken();

      if (token == null) {
        throw Exception('No authentication token found');
      }

      return await _apiService.getApplicantById(token, applicantId);
    } catch (error) {
      throw Exception('Failed to get applicant details: ${error.toString()}');
    }
  }

  /// Get current user's applicant details (for applicant users)
  Future<ApplicantDetailModel> getCurrentApplicantDetails() async {
    try {
      await _authRepository.isLoggedIn();
      final token = await _authRepository.getToken();

      if (token == null) {
        throw Exception('No authentication token found');
      }

      // Get current user ID from auth repository
      final userResult = await _authRepository.getCurrentUser();

      final currentUserId = userResult.fold(
        (failure) => throw Exception('Failed to get current user: ${failure.toString()}'),
        (authResult) {
          if (authResult?.user?.id == null) {
            throw Exception('Current user ID not found');
          }
          return authResult!.user!.id.toString();
        },
      );

      return await _apiService.getCurrentApplicantDetails(token, currentUserId);
    } catch (error) {
      throw Exception('Failed to get current applicant details: ${error.toString()}');
    }
  }

  /// Update applicant information
  Future<ApplicantDetailModel> updateApplicant(String applicantId, Map<String, dynamic> updates) async {
    try {
      await _authRepository.isLoggedIn();
      final token = await _authRepository.getToken();

      if (token == null) {
        throw Exception('No authentication token found');
      }

      return await _apiService.updateApplicant(token, applicantId, updates);
    } catch (error) {
      throw Exception('Failed to update applicant: ${error.toString()}');
    }
  }

  /// Get applicant activity log
  Future<List<ApplicantActivityModel>> getApplicantActivities(String applicantId) async {
    try {
      await _authRepository.isLoggedIn();
      final token = await _authRepository.getToken();

      if (token == null) {
        throw Exception('No authentication token found');
      }

      return await _apiService.getApplicantActivities(token, applicantId);
    } catch (error) {
      throw Exception('Failed to get applicant activities: ${error.toString()}');
    }
  }
}
