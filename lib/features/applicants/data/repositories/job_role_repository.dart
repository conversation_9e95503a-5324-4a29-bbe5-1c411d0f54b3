import 'package:SolidCheck/features/applicants/data/models/job_role_model.dart';
import 'package:SolidCheck/features/applicants/data/services/job_role_api_service.dart';
import 'package:SolidCheck/features/auth/data/repositories/auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Job Role Repository
/// Handles business logic for job roles and organizations
class JobRoleRepository {
  final JobRoleApiService _apiService;
  final AuthRepository _authRepository;

  JobRoleRepository(this._apiService, this._authRepository);

  /// Get all job roles
  Future<JobRoleResponse> getJobRoles() async {
    final token = await _authRepository.getToken();
    if (token == null) {
      throw Exception('No authentication token found');
    }
    return await _apiService.getJobRoles(token);
  }

  /// Get unique organizations
  List<OrganizationModel> getUniqueOrganizations(List<JobRoleData> jobRoles) {
    return _apiService.getUniqueOrganizations(jobRoles);
  }

  /// Get job roles for organization
  List<JobRoleData> getJobRolesForOrganization(
    List<JobRoleData> allJobRoles,
    int entityId,
  ) {
    return _apiService.getJobRolesForOrganization(allJobRoles, entityId);
  }

  /// Get products for job role
  List<ProductData> getProductsForJobRole(JobRoleData jobRole) {
    return _apiService.getProductsForJobRole(jobRole);
  }
}

/// Providers for dependency injection
final jobRoleApiServiceProvider = Provider<JobRoleApiService>((ref) {
  return JobRoleApiService();
});

final jobRoleRepositoryProvider = Provider<JobRoleRepository>((ref) {
  final apiService = ref.watch(jobRoleApiServiceProvider);
  final authRepository = ref.watch(authProvider);
  return JobRoleRepository(apiService, authRepository);
});
