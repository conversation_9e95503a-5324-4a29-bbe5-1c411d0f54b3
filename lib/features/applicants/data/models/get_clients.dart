import 'dart:convert';

List<GetClients> getClientsFromJson(String str) =>
    List<GetClients>.from(json.decode(str).map((x) => GetClients.fromJson(x)));

String getClientsToJson(List<GetClients> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class GetClients {
  int? id;
  String? accountType;
  String? accountName;

  GetClients({
    this.id,
    this.accountType,
    this.accountName,
  });

  factory GetClients.fromJson(Map<String, dynamic> json) => GetClients(
        id: json["id"],
        accountType: json["account_type"],
        accountName: json["account_name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "account_type": accountType,
        "account_name": accountName,
      };
}
