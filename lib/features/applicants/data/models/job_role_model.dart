import 'dart:convert';

/// Job Role API Response Model
class JobRoleResponse {
  final bool success;
  final List<JobRoleData> data;
  final String message;

  JobRoleResponse({
    required this.success,
    required this.data,
    required this.message,
  });

  factory JobRoleResponse.fromJson(Map<String, dynamic> json) {
    return JobRoleResponse(
      success: json['success'] ?? false,
      data: (json['data'] as List<dynamic>?)
              ?.map((item) => JobRoleData.fromJson(item))
              .toList() ??
          [],
      message: json['message'] ?? '',
    );
  }

  factory JobRoleResponse.fromRawJson(String str) =>
      JobRoleResponse.fromJson(json.decode(str));

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data.map((item) => item.toJson()).toList(),
      'message': message,
    };
  }

  String toRawJson() => json.encode(toJson());
}

/// Job Role Data Model
class JobRoleData {
  final int id;
  final String jobLabel;
  final String jobTitle;
  final String jobWorkforce;
  final String roleDescription;
  final bool selfPayment;
  final String employmentSector;
  final String entityName;
  final String entityCode;
  final String jobRoleName;
  final String organisationName;
  final int entityId;
  final List<ProductData> products;

  JobRoleData({
    required this.id,
    required this.jobLabel,
    required this.jobTitle,
    required this.jobWorkforce,
    required this.roleDescription,
    required this.selfPayment,
    required this.employmentSector,
    required this.entityName,
    required this.entityCode,
    required this.jobRoleName,
    required this.organisationName,
    required this.entityId,
    required this.products,
  });

  factory JobRoleData.fromJson(Map<String, dynamic> json) {
    return JobRoleData(
      id: json['id'] ?? 0,
      jobLabel: json['job_label'] ?? '',
      jobTitle: json['job_title'] ?? '',
      jobWorkforce: json['job_workforce'] ?? '',
      roleDescription: json['role_description'] ?? '',
      selfPayment: json['self_payment'] ?? false,
      employmentSector: json['employment_sector'] ?? '',
      entityName: json['entity_name'] ?? '',
      entityCode: json['entity_code'] ?? '',
      jobRoleName: json['job_role_name'] ?? '',
      organisationName: json['organisation_name'] ?? '',
      entityId: json['entity_id'] ?? 0,
      products: (json['products'] as List<dynamic>?)
              ?.map((item) => ProductData.fromJson(item))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'job_label': jobLabel,
      'job_title': jobTitle,
      'job_workforce': jobWorkforce,
      'role_description': roleDescription,
      'self_payment': selfPayment,
      'employment_sector': employmentSector,
      'entity_name': entityName,
      'entity_code': entityCode,
      'job_role_name': jobRoleName,
      'organisation_name': organisationName,
      'entity_id': entityId,
      'products': products.map((item) => item.toJson()).toList(),
    };
  }
}

/// Product Data Model
class ProductData {
  final int id;
  final String name;
  final String productCode;
  final double price;
  final double adminFee;
  final double supplierFee;
  final String pricingSource;
  final String inheritanceType;
  final String currency;

  ProductData({
    required this.id,
    required this.name,
    required this.productCode,
    required this.price,
    required this.adminFee,
    required this.supplierFee,
    required this.pricingSource,
    required this.inheritanceType,
    required this.currency,
  });

  factory ProductData.fromJson(Map<String, dynamic> json) {
    return ProductData(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      productCode: json['product_code'] ?? '',
      price: (json['price'] ?? 0).toDouble(),
      adminFee: (json['admin_fee'] ?? 0).toDouble(),
      supplierFee: (json['supplier_fee'] ?? 0).toDouble(),
      pricingSource: json['pricing_source'] ?? '',
      inheritanceType: json['inheritance_type'] ?? '',
      currency: json['currency'] ?? 'GBP',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'product_code': productCode,
      'price': price,
      'admin_fee': adminFee,
      'supplier_fee': supplierFee,
      'pricing_source': pricingSource,
      'inheritance_type': inheritanceType,
      'currency': currency,
    };
  }
}

/// Organization Model for dropdown
class OrganizationModel {
  final int entityId;
  final String entityName;
  final String entityCode;
  final String organisationName;

  OrganizationModel({
    required this.entityId,
    required this.entityName,
    required this.entityCode,
    required this.organisationName,
  });

  factory OrganizationModel.fromJobRoleData(JobRoleData jobRole) {
    return OrganizationModel(
      entityId: jobRole.entityId,
      entityName: jobRole.entityName,
      entityCode: jobRole.entityCode,
      organisationName: jobRole.organisationName,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OrganizationModel && other.entityId == entityId;
  }

  @override
  int get hashCode => entityId.hashCode;

  @override
  String toString() => organisationName.isNotEmpty ? organisationName : entityName;
}
