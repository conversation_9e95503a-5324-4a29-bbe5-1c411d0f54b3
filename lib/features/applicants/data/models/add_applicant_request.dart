import 'dart:convert';

/// Add Applicant Request Model
class AddApplicantRequest {
  final String firstName;
  final String lastName;
  final String email;
  final String phone;
  final int jobRoleId;

  AddApplicantRequest({
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.phone,
    required this.jobRoleId,
  });

  Map<String, dynamic> toJson() {
    return {
      'first_name': firstName,
      'last_name': lastName,
      'email': email,
      'phone': phone,
      'job_role_id': jobRoleId,
    };
  }

  String toRawJson() => json.encode(toJson());

  @override
  String toString() {
    return 'AddApplicantRequest(firstName: $firstName, lastName: $lastName, email: $email, phone: $phone, jobRoleId: $jobRoleId)';
  }
}
