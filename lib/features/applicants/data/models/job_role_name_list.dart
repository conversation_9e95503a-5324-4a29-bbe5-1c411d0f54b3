import 'dart:convert';

List<JobRoleNameListModel> jobRoleNameListModelFromJson(String str) =>
    List<JobRoleNameListModel>.from(
        json.decode(str).map((x) => JobRoleNameListModel.fromJson(x)));

String jobRoleNameListModelToJson(List<JobRoleNameListModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class JobRoleNameListModel {
  int? id;
  int? accountId;
  String? jobTitle;
  List<int>? optionIds;

  JobRoleNameListModel({
    this.id,
    this.accountId,
    this.jobTitle,
    this.optionIds,
  });

  factory JobRoleNameListModel.fromJson(Map<String, dynamic> json) =>
      JobRoleNameListModel(
        id: json["id"],
        accountId: json["account_id"],
        jobTitle: json["job_title"],
        optionIds: json["option_ids"] == null
            ? []
            : List<int>.from(json["option_ids"]!.map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "account_id": accountId,
        "job_title": jobTitle,
        "option_ids": optionIds == null
            ? []
            : List<dynamic>.from(optionIds!.map((x) => x)),
      };
}
