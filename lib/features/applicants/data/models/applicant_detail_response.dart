/// Applicant Detail API Response Models
/// Models for the new applicant detail API endpoint
library;

class ApplicantDetailResponse {
  final bool success;
  final ApplicantDetailData data;
  final String message;

  ApplicantDetailResponse({
    required this.success,
    required this.data,
    required this.message,
  });

  factory ApplicantDetailResponse.fromJson(Map<String, dynamic> json) {
    return ApplicantDetailResponse(
      success: json['success'] ?? false,
      data: ApplicantDetailData.fromJson(json['data'] ?? {}),
      message: json['message'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data.toJson(),
      'message': message,
    };
  }
}

class ApplicantDetailData {
  final ApplicantInfo applicant;
  final List<ApplicationInfo> applications;
  final int totalApplications;

  ApplicantDetailData({
    required this.applicant,
    required this.applications,
    required this.totalApplications,
  });

  factory ApplicantDetailData.fromJson(Map<String, dynamic> json) {
    return ApplicantDetailData(
      applicant: ApplicantInfo.fromJson(json['applicant'] ?? {}),
      applications: (json['applications'] as List<dynamic>?)
          ?.map((app) => ApplicationInfo.fromJson(app))
          .toList() ?? [],
      totalApplications: json['total_applications'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'applicant': applicant.toJson(),
      'applications': applications.map((app) => app.toJson()).toList(),
      'total_applications': totalApplications,
    };
  }
}

class ApplicantInfo {
  final int id;
  final String email;
  final String userType;
  final ApplicantProfile profile;
  final List<dynamic> entities;

  ApplicantInfo({
    required this.id,
    required this.email,
    required this.userType,
    required this.profile,
    required this.entities,
  });

  factory ApplicantInfo.fromJson(Map<String, dynamic> json) {
    return ApplicantInfo(
      id: json['id'] ?? 0,
      email: json['email'] ?? '',
      userType: json['user_type'] ?? '',
      profile: ApplicantProfile.fromJson(json['profile'] ?? {}),
      entities: json['entities'] ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'user_type': userType,
      'profile': profile.toJson(),
      'entities': entities,
    };
  }

  // Helper getters for compatibility with existing code
  String get fullName => profile.fullName;
  String get firstName => profile.firstName;
  String get lastName => profile.lastName;
  String get phone => profile.telephone;
  String? get address => profile.address;
  String? get dateOfBirth => profile.dateOfBirth;
}

class ApplicantProfile {
  final String firstName;
  final String lastName;
  final String fullName;
  final String telephone;
  final String? address;
  final String? dateOfBirth;

  ApplicantProfile({
    required this.firstName,
    required this.lastName,
    required this.fullName,
    required this.telephone,
    this.address,
    this.dateOfBirth,
  });

  factory ApplicantProfile.fromJson(Map<String, dynamic> json) {
    return ApplicantProfile(
      firstName: json['first_name'] ?? '',
      lastName: json['last_name'] ?? '',
      fullName: json['full_name'] ?? '',
      telephone: json['telephone'] ?? '',
      address: json['address'],
      dateOfBirth: json['date_of_birth'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'first_name': firstName,
      'last_name': lastName,
      'full_name': fullName,
      'telephone': telephone,
      'address': address,
      'date_of_birth': dateOfBirth,
    };
  }
}

class ApplicationInfo {
  final int id;
  final String? externalReference;
  final String status;
  final String? result;
  final String? consentDate;
  final String createdAt;
  final ProductInfo product;
  final CompletionStatus completionStatus;
  final SubmittedBy submittedBy;

  ApplicationInfo({
    required this.id,
    this.externalReference,
    required this.status,
    this.result,
    this.consentDate,
    required this.createdAt,
    required this.product,
    required this.completionStatus,
    required this.submittedBy,
  });

  factory ApplicationInfo.fromJson(Map<String, dynamic> json) {
    return ApplicationInfo(
      id: json['id'] ?? 0,
      externalReference: json['external_reference'],
      status: json['status'] ?? '',
      result: json['result'],
      consentDate: json['consent_date'],
      createdAt: json['created_at'] ?? '',
      product: ProductInfo.fromJson(json['product'] ?? {}),
      completionStatus: CompletionStatus.fromJson(json['completion_status'] ?? {}),
      submittedBy: SubmittedBy.fromJson(json['submitted_by'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'external_reference': externalReference,
      'status': status,
      'result': result,
      'consent_date': consentDate,
      'created_at': createdAt,
      'product': product.toJson(),
      'completion_status': completionStatus.toJson(),
      'submitted_by': submittedBy.toJson(),
    };
  }
}

class ProductInfo {
  final int id;
  final String name;
  final String code;
  final String variant;

  ProductInfo({
    required this.id,
    required this.name,
    required this.code,
    required this.variant,
  });

  factory ProductInfo.fromJson(Map<String, dynamic> json) {
    return ProductInfo(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      code: json['code'] ?? '',
      variant: json['variant'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'variant': variant,
    };
  }
}

class CompletionStatus {
  final bool isStarted;
  final bool isComplete;
  final String statusText;

  CompletionStatus({
    required this.isStarted,
    required this.isComplete,
    required this.statusText,
  });

  factory CompletionStatus.fromJson(Map<String, dynamic> json) {
    return CompletionStatus(
      isStarted: json['is_started'] ?? false,
      isComplete: json['is_complete'] ?? false,
      statusText: json['status_text'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'is_started': isStarted,
      'is_complete': isComplete,
      'status_text': statusText,
    };
  }
}

class SubmittedBy {
  final int id;
  final String email;
  final String name;

  SubmittedBy({
    required this.id,
    required this.email,
    required this.name,
  });

  factory SubmittedBy.fromJson(Map<String, dynamic> json) {
    return SubmittedBy(
      id: json['id'] ?? 0,
      email: json['email'] ?? '',
      name: json['name'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
    };
  }
}
