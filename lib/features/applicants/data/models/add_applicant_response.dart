import 'dart:convert';

/// Add Applicant API Response Model
class AddApplicantResponse {
  final bool success;
  final AddApplicantData data;
  final String message;

  AddApplicantResponse({
    required this.success,
    required this.data,
    required this.message,
  });

  factory AddApplicantResponse.fromJson(Map<String, dynamic> json) {
    return AddApplicantResponse(
      success: json['success'] ?? false,
      data: AddApplicantData.from<PERSON>son(json['data'] ?? {}),
      message: json['message'] ?? '',
    );
  }

  factory AddApplicantResponse.fromRawJson(String str) =>
      AddApplicantResponse.fromJson(json.decode(str));
}

/// Add Applicant Data Model
class AddApplicantData {
  final ApplicantInfo applicant;
  final EntityInfo entity;
  final JobRoleInfo jobRole;
  final List<ApplicationInfo> applications;
  final String generatedPassword;
  final int totalApplications;
  final int productsAutoSelected;

  AddApplicantData({
    required this.applicant,
    required this.entity,
    required this.jobRole,
    required this.applications,
    required this.generatedPassword,
    required this.totalApplications,
    required this.productsAutoSelected,
  });

  factory AddApplicantData.fromJson(Map<String, dynamic> json) {
    return AddApplicantData(
      applicant: ApplicantInfo.fromJson(json['applicant'] ?? {}),
      entity: EntityInfo.fromJson(json['entity'] ?? {}),
      jobRole: JobRoleInfo.fromJson(json['job_role'] ?? {}),
      applications: (json['applications'] as List<dynamic>?)
              ?.map((item) => ApplicationInfo.fromJson(item))
              .toList() ??
          [],
      generatedPassword: json['generated_password'] ?? '',
      totalApplications: json['total_applications'] ?? 0,
      productsAutoSelected: json['products_auto_selected'] ?? 0,
    );
  }
}

/// Applicant Info Model
class ApplicantInfo {
  final int id;
  final String email;
  final String firstName;
  final String lastName;
  final String phone;
  final String fullName;

  ApplicantInfo({
    required this.id,
    required this.email,
    required this.firstName,
    required this.lastName,
    required this.phone,
    required this.fullName,
  });

  factory ApplicantInfo.fromJson(Map<String, dynamic> json) {
    return ApplicantInfo(
      id: json['id'] ?? 0,
      email: json['email'] ?? '',
      firstName: json['first_name'] ?? '',
      lastName: json['last_name'] ?? '',
      phone: json['phone'] ?? '',
      fullName: json['full_name'] ?? '',
    );
  }
}

/// Entity Info Model
class EntityInfo {
  final int id;
  final String name;
  final String entityCode;

  EntityInfo({
    required this.id,
    required this.name,
    required this.entityCode,
  });

  factory EntityInfo.fromJson(Map<String, dynamic> json) {
    return EntityInfo(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      entityCode: json['entity_code'] ?? '',
    );
  }
}

/// Job Role Info Model
class JobRoleInfo {
  final int id;
  final String jobLabel;
  final String jobTitle;

  JobRoleInfo({
    required this.id,
    required this.jobLabel,
    required this.jobTitle,
  });

  factory JobRoleInfo.fromJson(Map<String, dynamic> json) {
    return JobRoleInfo(
      id: json['id'] ?? 0,
      jobLabel: json['job_label'] ?? '',
      jobTitle: json['job_title'] ?? '',
    );
  }
}

/// Application Info Model
class ApplicationInfo {
  final int id;
  final int productId;
  final String status;
  final BillingInfo billing;

  ApplicationInfo({
    required this.id,
    required this.productId,
    required this.status,
    required this.billing,
  });

  factory ApplicationInfo.fromJson(Map<String, dynamic> json) {
    return ApplicationInfo(
      id: json['id'] ?? 0,
      productId: json['product_id'] ?? 0,
      status: json['status'] ?? '',
      billing: BillingInfo.fromJson(json['billing'] ?? {}),
    );
  }
}

/// Billing Info Model
class BillingInfo {
  final String adminFee;
  final String supplierFee;
  final double totalFee;
  final bool selfPayment;
  final String currency;

  BillingInfo({
    required this.adminFee,
    required this.supplierFee,
    required this.totalFee,
    required this.selfPayment,
    required this.currency,
  });

  factory BillingInfo.fromJson(Map<String, dynamic> json) {
    return BillingInfo(
      adminFee: json['admin_fee'] ?? '0.00',
      supplierFee: json['supplier_fee'] ?? '0.00',
      totalFee: (json['total_fee'] ?? 0).toDouble(),
      selfPayment: json['self_payment'] ?? false,
      currency: json['currency'] ?? 'GBP',
    );
  }
}
