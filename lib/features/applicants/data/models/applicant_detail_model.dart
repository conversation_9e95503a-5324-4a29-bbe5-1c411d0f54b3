import 'dart:convert';

/// Applicant Detail Model
/// Represents detailed information about an applicant for the dashboard
class ApplicantDetailModel {
  final String? id;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? phone;
  final String? jobRole;
  final String? organization;
  final String? reference;
  final String? status;
  final String? statusDisplay;
  final DateTime? dateCreated;
  final DateTime? lastUpdated;
  final List<ApplicantCheckModel>? requestedChecks;
  final List<ApplicantActivityModel>? activities;

  ApplicantDetailModel({
    this.id,
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
    this.jobRole,
    this.organization,
    this.reference,
    this.status,
    this.statusDisplay,
    this.dateCreated,
    this.lastUpdated,
    this.requestedChecks,
    this.activities,
  });

  String get fullName {
    return '${firstName ?? ''} ${lastName ?? ''}'.trim();
  }

  String get displayStatus {
    if (statusDisplay != null && statusDisplay!.isNotEmpty) {
      return statusDisplay!;
    }
    switch (status?.toLowerCase()) {
      case 'new':
        return 'New';
      case 'in_progress':
        return 'In Progress';
      case 'further_action_pending':
        return 'Further Action Pending';
      case 'staff_review_pending':
        return 'Staff Review Pending';
      case 'complete':
        return 'Complete';
      default:
        return status ?? 'Unknown';
    }
  }

  factory ApplicantDetailModel.fromJson(Map<String, dynamic> json) {
    return ApplicantDetailModel(
      id: json['id']?.toString(),
      firstName: json['first_name'],
      lastName: json['last_name'],
      email: json['email'],
      phone: json['phone'],
      jobRole: json['job_role'],
      organization: json['organization'],
      reference: json['reference'],
      status: json['status'],
      statusDisplay: json['status_display'],
      dateCreated: json['date_created'] != null 
          ? DateTime.tryParse(json['date_created'])
          : null,
      lastUpdated: json['last_updated'] != null 
          ? DateTime.tryParse(json['last_updated'])
          : null,
      requestedChecks: json['requested_checks'] != null
          ? List<ApplicantCheckModel>.from(
              json['requested_checks'].map((x) => ApplicantCheckModel.fromJson(x))
            )
          : null,
      activities: json['activities'] != null
          ? List<ApplicantActivityModel>.from(
              json['activities'].map((x) => ApplicantActivityModel.fromJson(x))
            )
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'first_name': firstName,
      'last_name': lastName,
      'email': email,
      'phone': phone,
      'job_role': jobRole,
      'organization': organization,
      'reference': reference,
      'status': status,
      'status_display': statusDisplay,
      'date_created': dateCreated?.toIso8601String(),
      'last_updated': lastUpdated?.toIso8601String(),
      'requested_checks': requestedChecks?.map((x) => x.toJson()).toList(),
      'activities': activities?.map((x) => x.toJson()).toList(),
    };
  }
}

/// Applicant Check Model
/// Represents a check requested for an applicant
class ApplicantCheckModel {
  final String? id;
  final String? type;
  final String? name;
  final String? status;
  final DateTime? dateRequested;
  final DateTime? dateCompleted;

  ApplicantCheckModel({
    this.id,
    this.type,
    this.name,
    this.status,
    this.dateRequested,
    this.dateCompleted,
  });

  factory ApplicantCheckModel.fromJson(Map<String, dynamic> json) {
    return ApplicantCheckModel(
      id: json['id']?.toString(),
      type: json['type'],
      name: json['name'],
      status: json['status'],
      dateRequested: json['date_requested'] != null 
          ? DateTime.tryParse(json['date_requested'])
          : null,
      dateCompleted: json['date_completed'] != null 
          ? DateTime.tryParse(json['date_completed'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'name': name,
      'status': status,
      'date_requested': dateRequested?.toIso8601String(),
      'date_completed': dateCompleted?.toIso8601String(),
    };
  }
}

/// Applicant Activity Model
/// Represents an activity log entry for an applicant
class ApplicantActivityModel {
  final String? id;
  final String? type;
  final String? description;
  final String? checkType;
  final DateTime? timestamp;
  final String? performedBy;

  ApplicantActivityModel({
    this.id,
    this.type,
    this.description,
    this.checkType,
    this.timestamp,
    this.performedBy,
  });

  factory ApplicantActivityModel.fromJson(Map<String, dynamic> json) {
    return ApplicantActivityModel(
      id: json['id']?.toString(),
      type: json['type'],
      description: json['description'],
      checkType: json['check_type'],
      timestamp: json['timestamp'] != null 
          ? DateTime.tryParse(json['timestamp'])
          : null,
      performedBy: json['performed_by'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'description': description,
      'check_type': checkType,
      'timestamp': timestamp?.toIso8601String(),
      'performed_by': performedBy,
    };
  }
}
