import 'package:SolidCheck/core/constants/side_bar_menu_icons.dart';

class AddApplicantModel {
  final String prefixIcon;
  final String title;
  final double? price;

  AddApplicantModel({
    required this.prefixIcon,
    required this.title,
    required this.price,
  });
}

List<AddApplicantModel> addApplicantModel = [
  AddApplicantModel(
    prefixIcon: AddApplicantProIcons.dbsCheckIcon,
    title: 'DBS - Basic Check',
    price: 12.0,
  ),
  AddApplicantModel(
    prefixIcon: AddApplicantProIcons.dbsCheckIcon,
    title: 'DBS - Standard Check',
    price: 12.0,
  ),
  AddApplicantModel(
    prefixIcon: AddApplicantProIcons.dbsCheckIcon,
    title: 'DBS - Enhanced Check',
    price: 12.0,
  ),
  AddApplicantModel(
    prefixIcon: AddApplicantProIcons.refCheckIcon,
    title: 'Reference Check',
    price: 12.0,
  ),
  AddApplicantModel(
    prefixIcon: AddApplicantProIcons.smcIcon,
    title: 'Social Media Screening',
    price: 12.0,
  ),
  AddApplicantModel(
    prefixIcon: AddApplicantProIcons.rightToWorkIcon,
    title: 'Right to Work Check',
    price: 12.0,
  ),
  AddApplicantModel(
    prefixIcon: AddApplicantProIcons.creditCheckIcon,
    title: 'Adverse Credit Check',
    price: 12.0,
  ),
  AddApplicantModel(
    prefixIcon: AddApplicantProIcons.idCheckIcon,
    title: 'ID Check',
    price: 12.0,
  )
];
