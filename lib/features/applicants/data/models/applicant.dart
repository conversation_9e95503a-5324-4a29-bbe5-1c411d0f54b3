import 'dart:convert';

class ApplicantModel {
  ApplicantModel({
    this.data,
    this.message,
  });

  factory ApplicantModel.fromJson(Map<String, dynamic> json) => ApplicantModel(
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
        message: json["message"],
      );

  factory ApplicantModel.fromRawJson(String str) =>
      ApplicantModel.fromJson(json.decode(str));

  final Data? data;
  final String? message;

  ApplicantModel copyWith({
    Data? data,
    String? message,
  }) =>
      ApplicantModel(
        data: data ?? this.data,
        message: message ?? this.message,
      );

  String toRawJson() => json.encode(toJson());

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
        "message": message,
      };
}

class Data {
  Data({
    this.accountId,
    this.forename,
    this.surName,
    this.contact,
    this.company,
    this.email,
    this.jobRole,
    this.jobRolePeriod,
    this.products,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        accountId: json["account_id"],
        forename: json["applicant_name_forename"],
        surName: json["applicant_name_surname"],
        contact: json["contact_number"],
        company: json["company_name"],
        email: json["email"],
        jobRole: json["jobrole"],
        jobRolePeriod: json["jobrole_period"],
        products: json["products"] == null
            ? null
            : List<String>.from(json["products"].map((x) => x)),
      );

  factory Data.fromRawJson(String str) => Data.fromJson(json.decode(str));

  final int? accountId;
  final String? forename;
  final String? surName;
  final String? contact;
  final String? company;
  final String? email;
  final String? jobRole;
  final int? jobRolePeriod;
  final List<String>? products;

  Data copyWith({
    int? accountId,
    String? forename,
    String? surName,
    String? contact,
    String? company,
    String? email,
    String? jobRole,
    int? jobRolePeriod,
    List<String>? products,
  }) =>
      Data(
        accountId: accountId ?? this.accountId,
        forename: forename ?? this.forename,
        surName: surName ?? this.surName,
        contact: contact ?? this.contact,
        company: company ?? this.company,
        email: email ?? this.email,
        jobRole: jobRole ?? this.jobRole,
        jobRolePeriod: jobRolePeriod ?? this.jobRolePeriod,
        products: products ?? this.products,
      );

  String toRawJson() => json.encode(toJson());

  Map<String, dynamic> toJson() => {
        "account_id": accountId,
        "applicant_name_forename": forename,
        "applicant_name_surname": surName,
        "contact_number": contact,
        "company_name": company,
        "email": email,
        "jobrole": jobRole,
        "jobrole_period": jobRolePeriod,
        "products": products == null
            ? null
            : List<dynamic>.from(products!.map((x) => x)),
      };
}
