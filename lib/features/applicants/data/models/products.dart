import 'dart:convert';

Products productsFromJson(String str) => Products.fromJson(json.decode(str));

String productsToJson(Products data) => json.encode(data.toJson());

class Products {
  PData? data;

  Products({
    this.data,
  });

  factory Products.fromJson(Map<String, dynamic> json) => Products(
        data: json["data"] == null ? null : PData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
      };
}

class PData {
  int? id;
  String? optionName;
  String? type;
  String? status;
  String? createdAt;
  String? updatedAt;

  PData({
    this.id,
    this.optionName,
    this.type,
    this.status,
    this.createdAt,
    this.updatedAt,
  });

  factory PData.fromJson(Map<String, dynamic> json) => PData(
        id: json["id"],
        optionName: json["option_name"],
        type: json["type"],
        status: json["status"],
        createdAt: json["created_at"],
        updatedAt: json["updated_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "option_name": optionName,
        "type": type,
        "status": status,
        "created_at": createdAt,
        "updated_at": updatedAt,
      };
}
