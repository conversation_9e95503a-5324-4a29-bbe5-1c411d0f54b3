import 'package:SolidCheck/core/constants/constants.dart';
import 'package:SolidCheck/features/applicants/data/models/job_role_model.dart';
import 'package:dio/dio.dart';

class JobRoleApiService {
  final Dio _dio;

  JobRoleApiService() : _dio = Dio() {
    _dio.options.baseUrl = AppConstants.baseURL;
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
    _dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    _dio.interceptors.add(
      LogInterceptor(
        requestBody: true,
        responseBody: true,
      ),
    );
  }

  Future<JobRoleResponse> getJobRoles(String token) async {
    try {
      final response = await _dio.get(
        '/job-roles',
        options: Options(headers: {'Authorization': 'Bearer $token'}),
      );

      if (response.statusCode == 200) {
        return JobRoleResponse.fromJson(response.data);
      } else {
        throw Exception('Failed to load job roles: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw Exception('Unauthorized: Please login again');
      } else if (e.response?.statusCode == 404) {
        throw Exception('Job roles endpoint not found');
      } else if (e.response?.statusCode == 500) {
        throw Exception('Server error: Please try again later');
      }

      throw Exception('Network error: ${e.message}');
    } catch (error) {
      throw Exception('Network error: ${error.toString()}');
    }
  }

  List<OrganizationModel> getUniqueOrganizations(List<JobRoleData> jobRoles) {
    final Map<int, OrganizationModel> uniqueOrgs = {};

    for (final jobRole in jobRoles) {
      if (!uniqueOrgs.containsKey(jobRole.entityId)) {
        uniqueOrgs[jobRole.entityId] = OrganizationModel.fromJobRoleData(
          jobRole,
        );
      }
    }

    return uniqueOrgs.values.toList()
      ..sort((a, b) => a.toString().compareTo(b.toString()));
  }

  List<JobRoleData> getJobRolesForOrganization(
    List<JobRoleData> allJobRoles,
    int entityId,
  ) {
    return allJobRoles.where((jobRole) => jobRole.entityId == entityId).toList()
      ..sort((a, b) => a.jobRoleName.compareTo(b.jobRoleName));
  }

  List<ProductData> getProductsForJobRole(JobRoleData jobRole) {
    return jobRole.products;
  }
}
