import 'package:SolidCheck/core/constants/constants.dart';
import 'package:SolidCheck/features/applicants/data/models/add_applicant_request.dart';
import 'package:SolidCheck/features/applicants/data/models/add_applicant_response.dart';
import 'package:SolidCheck/features/applicants/data/models/applicant_detail_model.dart';
import 'package:SolidCheck/features/applicants/data/models/applicant_detail_response.dart';
import 'package:dio/dio.dart';

class ApplicantApiService {
  final Dio _dio;

  ApplicantApiService() : _dio = Dio() {
    _dio.options.baseUrl = AppConstants.baseURL;
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
  }

  Future<ApplicantDetailModel> getApplicantById(
    String token,
    String applicantId,
  ) async {
    try {
      final response = await _dio.get(
        '/applicants/$applicantId',
        options: Options(headers: {'Authorization': 'Bearer $token'}),
      );

      if (response.statusCode == 200) {
        final data = response.data['data'] ?? response.data;
        return ApplicantDetailModel.fromJson(data);
      } else {
        throw Exception('Failed to load applicant: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        return await _getApplicantFromClientEndpoint(token, applicantId);
      } else if (e.response?.statusCode == 500) {
        // Check if it's a database error
        final responseData = e.response?.data;
        if (responseData is Map && responseData['message'] != null) {
          final message = responseData['message'].toString();
          if (message.contains('product_form_fields') && message.contains("doesn't exist")) {
            throw Exception('Database configuration error: Missing required table. Please contact system administrator.');
          } else if (message.contains('SQLSTATE')) {
            throw Exception('Database error: Please contact system administrator.');
          }
        }
        throw Exception('Server error: Please try again later or contact support');
      }

      throw Exception('Network error: ${e.message}');
    } catch (error) {
      throw Exception('Network error: ${error.toString()}');
    }
  }

  Future<ApplicantDetailModel> _getApplicantFromClientEndpoint(
    String token,
    String applicantId,
  ) async {
    try {
      final response = await _dio.get(
        '/client-applicants/$applicantId',
        options: Options(headers: {'Authorization': 'Bearer $token'}),
      );

      if (response.statusCode == 200) {
        final data = response.data['data'] ?? response.data;

        return ApplicantDetailModel(
          id: data['applicant_id']?.toString() ?? data['id']?.toString(),
          firstName: data['forename'] ?? data['applicant_name_forename'],
          lastName: data['surname'] ?? data['applicant_name_surname'],
          email: data['email'],
          phone: data['contact_number'],
          jobRole: data['jobrole'],
          organization: data['organization'],
          reference: data['reference'],
          status: data['status'],
          statusDisplay: data['status_display'],
          dateCreated: data['created_at'] != null
              ? DateTime.tryParse(data['created_at'])
              : null,
          lastUpdated: data['updated_at'] != null
              ? DateTime.tryParse(data['updated_at'])
              : null,
          requestedChecks: data['requested_checks'] != null
              ? _convertRequestedChecks(data['requested_checks'])
              : [],
        );
      } else {
        throw Exception(
          'Failed to load applicant from fallback endpoint: ${response.statusCode}',
        );
      }
    } catch (error) {
      throw Exception('Failed to load applicant: ${error.toString()}');
    }
  }

  List<ApplicantCheckModel> _convertRequestedChecks(dynamic requestedChecks) {
    try {
      if (requestedChecks is List) {
        return requestedChecks.map((check) {
          if (check is Map<String, dynamic>) {
            return ApplicantCheckModel(
              id: check['id']?.toString(),
              type: check['product_code'] ?? check['type'],
              name: check['product_name'] ?? check['name'],
              status: check['status'] ?? 'pending',
              dateRequested: check['created_at'] != null
                  ? DateTime.tryParse(check['created_at'])
                  : null,
            );
          }
          return ApplicantCheckModel(
            id: '1',
            type: 'unknown',
            name: 'Unknown Check',
            status: 'pending',
            dateRequested: DateTime.now(),
          );
        }).toList();
      }
      return [];
    } catch (e) {
      return [];
    }
  }

  Future<ApplicantDetailModel> getCurrentApplicantDetails(String token, String currentUserId) async {
    try {
      final response = await _dio.get(
        '/applicants/$currentUserId',
        options: Options(headers: {'Authorization': 'Bearer $token'}),
      );

      if (response.statusCode == 200) {
        final data = response.data['data'] ?? response.data;
        return ApplicantDetailModel.fromJson(data);
      } else {
        throw Exception(
          'Failed to load current applicant: ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw Exception('Unauthorized: Please login again');
      } else if (e.response?.statusCode == 404) {
        throw Exception('Applicant not found');
      } else if (e.response?.statusCode == 403) {
        throw Exception('Access denied: ${e.response?.data}');
      } else if (e.response?.statusCode == 500) {
        // Check if it's a database error
        final responseData = e.response?.data;
        if (responseData is Map && responseData['message'] != null) {
          final message = responseData['message'].toString();
          if (message.contains('product_form_fields') && message.contains("doesn't exist")) {
            throw Exception('Database configuration error: Missing required table. Please contact system administrator.');
          } else if (message.contains('SQLSTATE')) {
            throw Exception('Database error: Please contact system administrator.');
          }
        }
        throw Exception('Server error: Please try again later or contact support');
      }

      throw Exception('Network error: ${e.message}');
    } catch (error) {
      throw Exception('Network error: ${error.toString()}');
    }
  }

  Future<ApplicantDetailModel> updateApplicant(
    String token,
    String applicantId,
    Map<String, dynamic> updates,
  ) async {
    try {
      final response = await _dio.put(
        '/applicants/$applicantId',
        data: updates,
        options: Options(headers: {'Authorization': 'Bearer $token'}),
      );

      if (response.statusCode == 200) {
        final data = response.data['data'] ?? response.data;
        return ApplicantDetailModel.fromJson(data);
      } else {
        throw Exception('Failed to update applicant: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (error) {
      throw Exception('Network error: ${error.toString()}');
    }
  }

  Future<List<ApplicantActivityModel>> getApplicantActivities(
    String token,
    String applicantId,
  ) async {
    try {
      final response = await _dio.get(
        '/applicants/$applicantId/activities',
        options: Options(headers: {'Authorization': 'Bearer $token'}),
      );

      if (response.statusCode == 200) {
        final data = response.data['data'] ?? response.data;
        if (data is List) {
          return data
              .map((item) => ApplicantActivityModel.fromJson(item))
              .toList();
        }
        return [];
      } else {
        throw Exception('Failed to load activities: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        return _getMockActivities();
      }
      throw Exception('Network error: ${e.message}');
    } catch (error) {
      throw Exception('Network error: ${error.toString()}');
    }
  }

  List<ApplicantActivityModel> _getMockActivities() {
    return [
      ApplicantActivityModel(
        id: '1',
        type: 'edit',
        description:
            'You edited Other Last Name from "Smith" to "Alex" at 12:54 p.m',
        checkType: 'dbs',
        timestamp: DateTime.parse('2024-07-08T12:54:00Z'),
        performedBy: 'John Smith',
      ),
      ApplicantActivityModel(
        id: '2',
        type: 'edit',
        description:
            'You edited Used from from 4 June, 1985 to 4 June, 1984 on 8 July, 2024 at 09:54 a.m',
        checkType: 'dbs',
        timestamp: DateTime.parse('2024-07-08T09:54:00Z'),
        performedBy: 'John Smith',
      ),
      ApplicantActivityModel(
        id: '3',
        type: 'form_filled',
        description: 'You filled form on 6 July, 2024 at 03:44 p.m',
        checkType: 'dbs',
        timestamp: DateTime.parse('2024-07-06T15:44:00Z'),
        performedBy: 'John Smith',
      ),
    ];
  }

  Future<ApplicantDetailResponse> getApplicantDetails(
    String token,
    String applicantId,
  ) async {
    try {
      final response = await _dio.get(
        '/applicants/$applicantId',
        options: Options(headers: {'Authorization': 'Bearer $token'}),
      );

      if (response.statusCode == 200) {
        return ApplicantDetailResponse.fromJson(response.data);
      } else {
        throw Exception(
          'Failed to load applicant details: ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw Exception('Unauthorized: Please login again');
      } else if (e.response?.statusCode == 404) {
        throw Exception('Applicant not found');
      } else if (e.response?.statusCode == 403) {
        throw Exception('Access denied: ${e.response?.data}');
      } else if (e.response?.statusCode == 500) {
        // Check if it's a database error
        final responseData = e.response?.data;
        if (responseData is Map && responseData['message'] != null) {
          final message = responseData['message'].toString();
          if (message.contains('product_form_fields') && message.contains("doesn't exist")) {
            throw Exception('Database configuration error: Missing required table. Please contact system administrator.');
          } else if (message.contains('SQLSTATE')) {
            throw Exception('Database error: Please contact system administrator.');
          }
        }
        throw Exception('Server error: Please try again later or contact support');
      }

      throw Exception('Network error: ${e.message}');
    } catch (error) {
      throw Exception('Failed to load applicant details: ${error.toString()}');
    }
  }

  Future<AddApplicantResponse> addApplicant(
    String token,
    AddApplicantRequest request,
  ) async {
    try {
      final response = await _dio.post(
        '/addapplicant',
        data: request.toJson(),
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return AddApplicantResponse.fromJson(response.data);
      } else {
        throw Exception('Failed to create applicant: ${response.statusCode}');
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        throw Exception('Unauthorized: Please login again');
      } else if (e.response?.statusCode == 422) {
        final errorData = e.response?.data;
        if (errorData != null && errorData['message'] != null) {
          throw Exception('Validation error: ${errorData['message']}');
        }
        throw Exception('Validation error: Please check your input data');
      } else if (e.response?.statusCode == 500) {
        throw Exception('Server error: Please try again later');
      }

      throw Exception('Network error: ${e.message}');
    } catch (error) {
      throw Exception('Failed to create applicant: ${error.toString()}');
    }
  }
}
