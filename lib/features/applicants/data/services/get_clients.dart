import 'package:SolidCheck/core/network/http_client.dart';
import 'package:SolidCheck/features/applicants/data/models/get_clients.dart';

class ClientsService {
  final HttpClient _httpClient = HttpClient();
  final String token;

  ClientsService(this.token);

  Future<List<GetClients>> fetchClients() async {
    final url = '/account/getclients';
    final headers = {'Authorization': 'Bearer $token'};

    final response =
        await _httpClient.fetchData('$url?token=$token', headers: headers);
    return (response as List).map((json) => GetClients.fromJson(json)).toList();
  }
}
