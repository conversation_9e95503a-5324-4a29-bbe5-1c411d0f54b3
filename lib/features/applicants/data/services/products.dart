import 'package:SolidCheck/core/network/http_client.dart';
import 'package:SolidCheck/features/applicants/data/models/products.dart';

class ProductsService {
  final HttpClient _httpClient = HttpClient();
  final String token;

  ProductsService(this.token);

  Future<Products> fetchProducts(int optionId) async {
    final url = '/account/getOption/$optionId';
    final headers = {'Authorization': 'Bearer $token'};

    final response = await _httpClient.fetchData(url, headers: headers);
    return Products.fromJson(response);
  }
}
