import 'package:SolidCheck/features/applicants/data/models/applicant.dart';
import 'package:SolidCheck/features/applicants/data/repositories/applicant.dart';
import 'package:SolidCheck/features/applicants/data/services/applicant_api.dart';
import 'package:SolidCheck/features/applicants/data/services/applicant_api_service.dart' as detailed_api;
import 'package:SolidCheck/features/auth/data/repositories/auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// State class for Add Applicant
class AddApplicantState {
  final bool isLoading;
  final String? error;
  final String? successMessage;

  AddApplicantState({
    this.isLoading = false,
    this.error,
    this.successMessage,
  });

  AddApplicantState copyWith({
    bool? isLoading,
    String? error,
    String? successMessage,
  }) {
    return AddApplicantState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      successMessage: successMessage,
    );
  }
}

// ViewModel for Add Applicant functionality
class AddApplicantViewModel extends StateNotifier<AddApplicantState> {
  final ApplicantRepository _applicantRepository;

  AddApplicantViewModel(this._applicantRepository) : super(AddApplicantState());

  Future<void> addApplicant(Map<String, dynamic> applicantData) async {
    state = state.copyWith(isLoading: true, error: null, successMessage: null);

    try {
      // Create ApplicantModel from the provided data
      final applicantModel = ApplicantModel(
        data: Data(
          forename: applicantData['first_name'],
          surName: applicantData['last_name'],
          email: applicantData['email'],
          contact: applicantData['phone'],
          company: applicantData['organization'],
          products: List<String>.from(applicantData['requested_checks'] ?? []),
        ),
      );

      final message = await _applicantRepository.addApplicant(applicantModel);
      
      state = state.copyWith(
        isLoading: false,
        successMessage: message,
      );
    } catch (error) {
      state = state.copyWith(
        isLoading: false,
        error: error.toString(),
      );
      rethrow;
    }
  }

  void clearState() {
    state = AddApplicantState();
  }
}

// Provider for Add Applicant ViewModel
final addApplicantViewModelProvider = StateNotifierProvider<AddApplicantViewModel, AddApplicantState>((ref) {
  final authRepository = ref.read(authProvider);
  final applicantApiService = ApplicantApiService();
  final detailedApiService = detailed_api.ApplicantApiService();
  final applicantRepository = ApplicantRepository(authRepository, applicantApiService, detailedApiService);

  return AddApplicantViewModel(applicantRepository);
});
