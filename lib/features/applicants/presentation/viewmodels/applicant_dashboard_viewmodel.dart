import 'package:SolidCheck/features/applicants/data/models/applicant_detail_model.dart';
import 'package:SolidCheck/features/applicants/data/models/applicant_detail_response.dart';
import 'package:SolidCheck/features/applicants/data/repositories/applicant_repository.dart';
import 'package:SolidCheck/features/auth/data/repositories/auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Applicant Dashboard State
class ApplicantDashboardState {
  final bool isLoading;
  final ApplicantDetailModel? applicant;
  final ApplicantDetailResponse? applicantDetails;
  final List<ApplicantActivityModel> activities;
  final String? error;

  ApplicantDashboardState({
    this.isLoading = false,
    this.applicant,
    this.applicantDetails,
    this.activities = const [],
    this.error,
  });

  ApplicantDashboardState copyWith({
    bool? isLoading,
    ApplicantDetailModel? applicant,
    ApplicantDetailResponse? applicantDetails,
    List<ApplicantActivityModel>? activities,
    String? error,
  }) {
    return ApplicantDashboardState(
      isLoading: isLoading ?? this.isLoading,
      applicant: applicant ?? this.applicant,
      applicantDetails: applicantDetails ?? this.applicantDetails,
      activities: activities ?? this.activities,
      error: error ?? this.error,
    );
  }
}

/// Applicant Dashboard ViewModel
class ApplicantDashboardViewModel extends StateNotifier<ApplicantDashboardState> {
  final ApplicantRepository _repository;

  ApplicantDashboardViewModel(this._repository) : super(ApplicantDashboardState());

  /// Load applicant details using new API (preferred method)
  Future<void> loadApplicantDetails(String applicantId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final applicantDetails = await _repository.getApplicantDetails(applicantId);

      // Convert new API response to legacy format for compatibility
      final legacyApplicant = _convertToLegacyFormat(applicantDetails);

      state = state.copyWith(
        isLoading: false,
        applicantDetails: applicantDetails,
        applicant: legacyApplicant,
        activities: [], // Activities will be loaded separately if needed
      );
    } catch (error) {
      // Fallback to legacy API if new API fails
      try {
        await loadApplicantById(applicantId);
      } catch (fallbackError) {
        state = state.copyWith(
          isLoading: false,
          error: error.toString(),
        );
      }
    }
  }

  /// Load applicant data by ID (legacy method for backward compatibility)
  Future<void> loadApplicantById(String applicantId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final applicant = await _repository.getApplicantById(applicantId);
      final activities = await _repository.getApplicantActivities(applicantId);

      state = state.copyWith(
        isLoading: false,
        applicant: applicant,
        activities: activities,
      );
    } catch (error) {
      state = state.copyWith(
        isLoading: false,
        error: error.toString(),
      );
    }
  }

  /// Load current user's applicant data (for applicant users)
  Future<void> loadCurrentApplicant() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final applicant = await _repository.getCurrentApplicantDetails();
      final activities = applicant.id != null 
          ? await _repository.getApplicantActivities(applicant.id!)
          : <ApplicantActivityModel>[];
      
      state = state.copyWith(
        isLoading: false,
        applicant: applicant,
        activities: activities,
      );
    } catch (error) {
      state = state.copyWith(
        isLoading: false,
        error: error.toString(),
      );
    }
  }

  /// Update applicant information
  Future<void> updateApplicant(String applicantId, Map<String, dynamic> updates) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final updatedApplicant = await _repository.updateApplicant(applicantId, updates);
      
      state = state.copyWith(
        isLoading: false,
        applicant: updatedApplicant,
      );
    } catch (error) {
      state = state.copyWith(
        isLoading: false,
        error: error.toString(),
      );
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Reset state
  void reset() {
    state = ApplicantDashboardState();
  }

  /// Convert new API response to legacy format for compatibility
  ApplicantDetailModel _convertToLegacyFormat(ApplicantDetailResponse response) {
    final applicant = response.data.applicant;
    final applications = response.data.applications;

    // Convert applications to requested checks format
    final requestedChecks = applications.map((app) => ApplicantCheckModel(
      id: app.id.toString(),
      type: app.product.variant,
      name: app.product.name,
      status: app.status,
      dateRequested: DateTime.tryParse(app.createdAt),
      dateCompleted: app.completionStatus.isComplete ? DateTime.tryParse(app.createdAt) : null,
    )).toList();

    // Parse dates
    DateTime? parseDate(String? dateStr) {
      if (dateStr == null || dateStr.isEmpty) return null;
      return DateTime.tryParse(dateStr);
    }

    // Create legacy format
    return ApplicantDetailModel(
      id: applicant.id.toString(),
      firstName: applicant.firstName,
      lastName: applicant.lastName,
      email: applicant.email,
      phone: applicant.phone,
      organization: '', // Not available in new API
      jobRole: '', // Not available in new API
      reference: '', // Not available in new API
      dateCreated: applications.isNotEmpty ? parseDate(applications.first.createdAt) : null,
      lastUpdated: applications.isNotEmpty ? parseDate(applications.first.createdAt) : null,
      status: applications.isNotEmpty ? applications.first.status : 'unknown',
      statusDisplay: applications.isNotEmpty ? applications.first.completionStatus.statusText : 'Unknown',
      requestedChecks: requestedChecks,
      activities: [], // Will be populated separately if needed
    );
  }
}

/// Provider for ApplicantRepository
final applicantRepositoryProvider = Provider<ApplicantRepository>((ref) {
  final authRepository = AuthRepository(ref);
  return ApplicantRepository(authRepository);
});

/// Provider for ApplicantDashboardViewModel
final applicantDashboardViewModelProvider = 
    StateNotifierProvider<ApplicantDashboardViewModel, ApplicantDashboardState>((ref) {
  final repository = ref.read(applicantRepositoryProvider);
  return ApplicantDashboardViewModel(repository);
});

/// Provider for filtered activities by check type
final filteredActivitiesProvider = Provider.family<List<ApplicantActivityModel>, String?>((ref, checkType) {
  final state = ref.watch(applicantDashboardViewModelProvider);
  
  if (checkType == null || checkType.isEmpty) {
    return state.activities;
  }
  
  return state.activities.where((activity) => 
    activity.checkType?.toLowerCase() == checkType.toLowerCase()
  ).toList();
});
