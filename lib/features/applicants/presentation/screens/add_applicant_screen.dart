import 'package:SolidCheck/core/config/component_config.dart';
import 'package:SolidCheck/core/config/design_config.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/features/applicants/data/models/add_applicant.dart';
import 'package:SolidCheck/features/applicants/data/models/add_applicant_request.dart';
import 'package:SolidCheck/features/applicants/data/models/add_applicant_response.dart';
import 'package:SolidCheck/features/applicants/data/models/job_role_model.dart';
import 'package:SolidCheck/features/applicants/data/repositories/applicant.dart';
import 'package:SolidCheck/features/applicants/presentation/providers/job_role_provider.dart';
import 'package:SolidCheck/features/applicants/presentation/widgets/applicant_success_popup.dart';
import 'package:SolidCheck/features/dashboard/presentation/widgets/appbar/dashboard_appbar_widget.dart';
import 'package:SolidCheck/shared/utils/validations.dart';
import 'package:SolidCheck/shared/widgets/custom_snakbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

const _kProductCardColor = Color(0xFFF1F9FF);

class AddApplicantScreen extends ConsumerStatefulWidget {
  const AddApplicantScreen({super.key});

  @override
  ConsumerState<AddApplicantScreen> createState() => _AddApplicantScreenState();
}

class _AddApplicantScreenState extends ConsumerState<AddApplicantScreen> {
  late final GlobalKey<FormState> _formKey;
  late final TextEditingController _firstNameController;
  late final TextEditingController _lastNameController;
  late final TextEditingController _emailController;
  late final TextEditingController _phoneController;

  final Set<int> _selectedItems = <int>{};
  OrganizationModel? _selectedOrganization;
  JobRoleData? _selectedJobRole;
  bool _isLoading = false;
  String? _emailErrorMessage;

  @override
  void initState() {
    super.initState();
    _formKey = GlobalKey<FormState>();
    _firstNameController = TextEditingController();
    _lastNameController = TextEditingController();
    _emailController = TextEditingController();
    _phoneController = TextEditingController();

    _emailController.addListener(_validateEmailField);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(jobRoleProvider.notifier).loadJobRoles();
    });
  }

  @override
  void dispose() {
    _emailController.removeListener(_validateEmailField);
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  void _validateEmailField() {
    final error = _validateEmail(_emailController.text);
    if (_emailErrorMessage != error) {
      setState(() => _emailErrorMessage = error);
    }
  }

  String? _validateEmail(String? value) {
    const emailPattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
    return (value == null || value.isEmpty)
        ? 'This field is required'
        : (!RegExp(emailPattern).hasMatch(value))
        ? 'Please enter a valid email address'
        : null;
  }

  Future<void> _handleFormSubmission() async {
    if (!(_formKey.currentState?.validate() ?? false)) return;

    if (_selectedOrganization == null) {
      _showSnackBar('Please select an organization', isError: true);
      return;
    }

    if (_selectedJobRole == null) {
      _showSnackBar('Please select a job role', isError: true);
      return;
    }

    setState(() => _isLoading = true);

    try {
      final request = AddApplicantRequest(
        firstName: _firstNameController.text.trim(),
        lastName: _lastNameController.text.trim(),
        email: _emailController.text.trim(),
        phone: _phoneController.text.trim(),
        jobRoleId: _selectedJobRole!.id,
      );

      final repository = ref.read(applicantRepositoryProvider);
      final response = await repository.addNewApplicant(request);

      if (!mounted) return;

      setState(() => _isLoading = false);

      await _showSuccessPopup(response);

      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/dashboard');
      }
    } catch (error) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
        final errorMessage = error.toString();
        if (errorMessage.contains('email')) {
          _emailErrorMessage =
              'This email address is already associated with an existing applicant.';
        }
        _formKey.currentState?.validate();
      });

      _showSnackBar(_emailErrorMessage ?? 'Error: $error', isError: true);
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showSnackBar(String message, {required bool isError}) {
    if (!mounted) return;
    CustomSnackBar.show(
      context: context,
      message: message,
      backgroundColor: isError ? AppColors.kRedColor : AppColors.kBlueColor,
      textColor: Colors.white,
      icon: isError ? Icons.info : Icons.check,
      duration: const Duration(seconds: 3),
    );
  }

  Future<void> _showSuccessPopup(AddApplicantResponse response) async {
    if (!mounted) return;

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => ApplicantSuccessPopup(
        response: response,
        onClose: () => Navigator.of(context).pop(),
      ),
    );
  }

  void _handleSelectionChanged(int index) {
    setState(() {
      if (_selectedItems.contains(index)) {
        _selectedItems.remove(index);
      } else {
        _selectedItems.add(index);
      }
    });
  }

  double _calculateTotalPrice() {
    final jobRoleState = ref.read(jobRoleProvider);

    final products = jobRoleState.availableProducts.isNotEmpty
        ? jobRoleState.availableProducts
              .map(
                (product) => AddApplicantModel(
                  prefixIcon: '',
                  title: product.name,
                  price: product.price,
                ),
              )
              .toList()
        : addApplicantModel;

    double total = 0.0;
    for (int index in _selectedItems) {
      if (index < products.length) {
        final product = products[index];
        total += product.price ?? 0.0;
      }
    }
    return total;
  }

  @override
  Widget build(BuildContext context) {
    final isMobile = context.isMobile;
    final isTablet = context.isTablet;
    final isDesktop = context.isDesktop;

    final horizontalPadding = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 16.0,
      tablet: 32.0,
      desktop: 40.0,
    );

    final headerVerticalPadding = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 12.0,
      tablet: 16.0,
      desktop: 20.0,
    );

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: DashboardAppBarWidget(
        isAdminActive: false,
        showDrawerIcon: false,
        centertitle: false,
      ),
      body: Container(
        color: Colors.white,
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: horizontalPadding,
                vertical: headerVerticalPadding,
              ),
              child: Row(
                children: [
                  ComponentConfig.getIconButton(
                    Icons.arrow_back,
                    onPressed: () => Navigator.of(context).pop(),
                    size: DesignConfig.iconSizeSM,
                    tooltip: 'Go back',
                  ),
                  SizedBox(width: DesignConfig.spaceSM),
                  Text(
                    'Add Applicant',
                    style: DesignConfig.headingMedium.copyWith(
                      fontSize: DesignConfig.getResponsiveFontSize(
                        context,
                        DesignConfig.fontSizeXL,
                      ),
                      color: DesignConfig.primaryColor,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: _isLoading
                  ? Center(child: ComponentConfig.loadingIndicator)
                  : Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: horizontalPadding,
                        vertical: 0,
                      ),
                      child: Form(
                        key: _formKey,
                        child: _buildResponsiveLayout(
                          isMobile,
                          isTablet,
                          isDesktop,
                        ),
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResponsiveLayout(bool isMobile, bool isTablet, bool isDesktop) {
    if (isMobile) {
      return _buildMobileLayout();
    } else if (isTablet) {
      return _buildTabletLayout();
    } else {
      return _buildDesktopLayout();
    }
  }

  Widget _buildDesktopLayout() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(flex: 1, child: _buildApplicantDetailsSection()),
        SizedBox(
          width: ResponsiveHelper.getResponsiveValue(
            context,
            mobile: 20.0,
            tablet: 40.0,
            desktop: 60.0,
          ),
        ),
        Expanded(flex: 1, child: _buildProductsSection()),
      ],
    );
  }

  Widget _buildTabletLayout() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(flex: 3, child: _buildApplicantDetailsSection()),
        SizedBox(
          width: ResponsiveHelper.getResponsiveValue(
            context,
            mobile: 20.0,
            tablet: 40.0,
            desktop: 60.0,
          ),
        ),
        Expanded(flex: 2, child: _buildProductsSection()),
      ],
    );
  }

  Widget _buildMobileLayout() {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildApplicantDetailsSection(),
          SizedBox(
            height: ResponsiveHelper.getResponsiveValue(
              context,
              mobile: 16.0,
              tablet: 24.0,
              desktop: 32.0,
            ),
          ),
          _buildProductsSection(),
        ],
      ),
    );
  }

  Widget _buildApplicantDetailsSection() {


    final noteSpacing = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 16.0,
      tablet: 20.0,
      desktop: 20.0,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Applicant Details',
          style: DesignConfig.headingMedium.copyWith(
            fontSize: DesignConfig.getResponsiveFontSize(
              context,
              DesignConfig.fontSizeLG,
            ),
          ),
        ),
        SizedBox(
          height: DesignConfig.getResponsiveSpacing(
            context,
            DesignConfig.sectionSpacing,
          ),
        ),

        _buildFormField(
          label: 'First Name *',
          controller: _firstNameController,
          placeholder: 'John',
          validator: validateRequired,
        ),

        _buildFormField(
          label: 'Last Name *',
          controller: _lastNameController,
          placeholder: 'Smith',
          validator: validateRequired,
        ),

        _buildOrganizationField(),

        _buildJobRoleField(),

        _buildFormField(
          label: 'Email Address *',
          controller: _emailController,
          placeholder: '<EMAIL>',
          validator: _validateEmail,
        ),

        if (_emailErrorMessage != null)
          Padding(
            padding: EdgeInsets.only(
              left: DesignConfig.spaceSM,
              top: DesignConfig.spaceXS,
              bottom: DesignConfig.spaceLG,
            ),
            child: Text(
              _emailErrorMessage!,
              style: DesignConfig.caption.copyWith(
                color: DesignConfig.errorColor,
                fontSize: DesignConfig.getResponsiveFontSize(
                  context,
                  DesignConfig.fontSizeXS,
                ),
              ),
            ),
          ),

        _buildFormField(
          label: 'Phone Number *',
          controller: _phoneController,
          placeholder: '+44 123 123 123',
          validator: validateAlphanumeric,
        ),

        SizedBox(height: noteSpacing),
        Text(
          '*Required checks are automatically selected based on your chosen job role',
          style: DesignConfig.caption.copyWith(
            fontSize: DesignConfig.getResponsiveFontSize(
              context,
              DesignConfig.fontSizeXS,
            ),
            color: DesignConfig.primaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildFormField({
    required String label,
    required TextEditingController controller,
    required String placeholder,
    String? Function(String?)? validator,
  }) {
    final fieldSpacing = DesignConfig.getResponsiveSpacing(
      context,
      DesignConfig.spaceXL,
    );

    return Padding(
      padding: EdgeInsets.only(bottom: fieldSpacing),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: DesignConfig.bodySmall.copyWith(
              fontSize: DesignConfig.getResponsiveFontSize(
                context,
                DesignConfig.fontSizeSM,
              ),
              color: DesignConfig.secondaryTextColor,
            ),
          ),
          SizedBox(height: DesignConfig.spaceSM),
          TextFormField(
            controller: controller,
            validator: validator,
            style: DesignConfig.bodyMedium.copyWith(
              fontSize: DesignConfig.getResponsiveFontSize(
                context,
                DesignConfig.fontSizeMD,
              ),
            ),
            decoration: ComponentConfig.getInputDecoration(
              hintText: placeholder,
              labelText: null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrganizationField() {
    final jobRoleState = ref.watch(jobRoleProvider);

    final fieldSpacing = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 20.0,
      tablet: 22.0,
      desktop: 24.0,
    );

    final fieldHeight = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 44.0,
      tablet: 42.0,
      desktop: 40.0,
    );

    final horizontalPadding = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 10.0,
      tablet: 11.0,
      desktop: 12.0,
    );

    return Padding(
      padding: EdgeInsets.only(bottom: fieldSpacing),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Organization *',
            style: TextStyle(
              fontSize: ResponsiveHelper.getResponsiveFontSize(
                context,
                mobile: 12.0,
                tablet: 13.0,
                desktop: 13.0,
              ),
              fontWeight: FontWeight.w400,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            height: fieldHeight,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300, width: 1),
              borderRadius: BorderRadius.circular(4),
              color: Colors.white,
            ),
            child: jobRoleState.isLoading
                ? Center(
                    child: SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: AppColors.kBlueColor,
                      ),
                    ),
                  )
                : DropdownButtonHideUnderline(
                    child: DropdownButton<OrganizationModel>(
                      value: _selectedOrganization,
                      isExpanded: true,
                      hint: Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: horizontalPadding,
                        ),
                        child: Text(
                          'Select Organization',
                          style: TextStyle(
                            fontSize: ResponsiveHelper.getResponsiveFontSize(
                              context,
                              mobile: 13.0,
                              tablet: 14.0,
                              desktop: 14.0,
                            ),
                            color: Colors.grey.shade500,
                          ),
                        ),
                      ),
                      icon: const Icon(
                        Icons.keyboard_arrow_down,
                        color: Colors.grey,
                      ),
                      style: TextStyle(
                        fontSize: ResponsiveHelper.getResponsiveFontSize(
                          context,
                          mobile: 13.0,
                          tablet: 14.0,
                          desktop: 14.0,
                        ),
                        color: Colors.black87,
                      ),
                      items: jobRoleState.organizations.map((org) {
                        return DropdownMenuItem<OrganizationModel>(
                          value: org,
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: horizontalPadding,
                            ),
                            child: Text(org.toString()),
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedOrganization = value;
                          _selectedJobRole = null;
                          _selectedItems.clear();
                        });
                        ref
                            .read(jobRoleProvider.notifier)
                            .selectOrganization(value);
                      },
                    ),
                  ),
          ),
          if (jobRoleState.error != null)
            Padding(
              padding: EdgeInsets.only(left: horizontalPadding, top: 4),
              child: Text(
                jobRoleState.error!,
                style: const TextStyle(color: Colors.red, fontSize: 12),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildJobRoleField() {
    final jobRoleState = ref.watch(jobRoleProvider);

    final fieldSpacing = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 20.0,
      tablet: 22.0,
      desktop: 24.0,
    );

    final fieldHeight = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 44.0,
      tablet: 42.0,
      desktop: 40.0,
    );

    final horizontalPadding = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 10.0,
      tablet: 11.0,
      desktop: 12.0,
    );

    return Padding(
      padding: EdgeInsets.only(bottom: fieldSpacing),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Job Role *',
            style: TextStyle(
              fontSize: ResponsiveHelper.getResponsiveFontSize(
                context,
                mobile: 12.0,
                tablet: 13.0,
                desktop: 13.0,
              ),
              fontWeight: FontWeight.w400,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            height: fieldHeight,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300, width: 1),
              borderRadius: BorderRadius.circular(4),
              color: Colors.white,
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<JobRoleData>(
                value: _selectedJobRole,
                isExpanded: true,
                hint: Padding(
                  padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
                  child: Text(
                    _selectedOrganization == null
                        ? 'Select Organization first'
                        : 'Select Job Role',
                    style: TextStyle(
                      fontSize: ResponsiveHelper.getResponsiveFontSize(
                        context,
                        mobile: 13.0,
                        tablet: 14.0,
                        desktop: 14.0,
                      ),
                      color: Colors.grey.shade500,
                    ),
                  ),
                ),
                icon: const Icon(Icons.keyboard_arrow_down, color: Colors.grey),
                style: TextStyle(
                  fontSize: ResponsiveHelper.getResponsiveFontSize(
                    context,
                    mobile: 13.0,
                    tablet: 14.0,
                    desktop: 14.0,
                  ),
                  color: Colors.black87,
                ),
                items: jobRoleState.filteredJobRoles.map((jobRole) {
                  return DropdownMenuItem<JobRoleData>(
                    value: jobRole,
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: horizontalPadding,
                      ),
                      child: Text(jobRole.jobRoleName),
                    ),
                  );
                }).toList(),
                onChanged: _selectedOrganization == null
                    ? null
                    : (value) {
                        setState(() {
                          _selectedJobRole = value;
                          _selectedItems.clear();
                        });
                        ref.read(jobRoleProvider.notifier).selectJobRole(value);

                        if (value != null) {
                          final jobRoleState = ref.read(jobRoleProvider);
                          final products = jobRoleState.availableProducts;

                          setState(() {
                            for (int i = 0; i < products.length; i++) {
                              _selectedItems.add(i);
                            }
                          });
                        }
                      },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductsSection() {
    final jobRoleState = ref.watch(jobRoleProvider);

    final products = jobRoleState.availableProducts.isNotEmpty
        ? jobRoleState.availableProducts
              .map(
                (product) => AddApplicantModel(
                  prefixIcon: '',
                  title: product.name,
                  price: product.price,
                ),
              )
              .toList()
        : addApplicantModel;

    final crossAxisCount = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 1,
      tablet: 2,
      desktop: 3,
    );

    final crossAxisSpacing = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 12.0,
      tablet: 16.0,
      desktop: 16.0,
    );

    final mainAxisSpacing = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 12.0,
      tablet: 16.0,
      desktop: 12.0,
    );

    final childAspectRatio = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 3.5,
      tablet: 2.8,
      desktop: 3.2,
    );

    final gridHeight = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: products.length * 90.0,
      tablet: 400.0,
      desktop: 280.0,
    );

    final sectionSpacing = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 20.0,
      tablet: 24.0,
      desktop: 24.0,
    );

    final summarySpacing = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 8.0,
      tablet: 20.0,
      desktop: 20.0,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Required Checks',
          style: DesignConfig.headingMedium.copyWith(
            fontSize: DesignConfig.getResponsiveFontSize(
              context,
              DesignConfig.fontSizeLG,
            ),
          ),
        ),
        SizedBox(height: sectionSpacing),

        SizedBox(
          height: gridHeight,
          child: _selectedJobRole == null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.check_circle_outline,
                        size: 48,
                        color: Colors.grey.shade400,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No Checks Selected',
                        style: TextStyle(
                          fontSize: ResponsiveHelper.getResponsiveFontSize(
                            context,
                            mobile: 16.0,
                            tablet: 17.0,
                            desktop: 18.0,
                          ),
                          fontWeight: FontWeight.w600,
                          color: Colors.grey.shade600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _selectedOrganization == null
                            ? 'Please select an organization and job role to view available checks'
                            : 'Please select a job role to view available checks',
                        style: TextStyle(
                          fontSize: ResponsiveHelper.getResponsiveFontSize(
                            context,
                            mobile: 12.0,
                            tablet: 13.0,
                            desktop: 14.0,
                          ),
                          color: Colors.grey.shade500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                )
              : products.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 48,
                        color: Colors.grey.shade400,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No checks available for this job role',
                        style: TextStyle(
                          fontSize: ResponsiveHelper.getResponsiveFontSize(
                            context,
                            mobile: 14.0,
                            tablet: 15.0,
                            desktop: 16.0,
                          ),
                          color: Colors.grey.shade600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                )
              : GridView.builder(
                  physics: context.isMobile
                      ? const AlwaysScrollableScrollPhysics()
                      : const NeverScrollableScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: crossAxisCount,
                    crossAxisSpacing: crossAxisSpacing,
                    mainAxisSpacing: mainAxisSpacing,
                    childAspectRatio: childAspectRatio,
                  ),
                  itemCount: products.length,
                  itemBuilder: (context, index) {
                    final product = products[index];

                    return _buildProductCard(
                      product,
                      true,
                      index,
                      isInteractive: false,
                    );
                  },
                ),
        ),

        SizedBox(height: summarySpacing),

        if (_selectedItems.isNotEmpty) _buildSelectedProductsSection(),

        SizedBox(height: summarySpacing),

        _buildActionButtons(),
      ],
    );
  }

  Widget _buildActionButtons() {
    final buttonSpacing = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 12.0,
      tablet: 14.0,
      desktop: 16.0,
    );

    final buttonPadding = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 14.0,
      tablet: 15.0,
      desktop: 16.0,
    );



    if (context.isMobile) {
      return Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: () {
                _handleFormSubmission();
              },
              style: ComponentConfig.secondaryButtonStyle.copyWith(
                padding: WidgetStateProperty.all(
                  EdgeInsets.symmetric(vertical: buttonPadding),
                ),
              ),
              child: const Text('Start Application'),
            ),
          ),
          SizedBox(height: buttonSpacing),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isLoading
                  ? null
                  : () {
                      _handleFormSubmission();
                    },
              style: ComponentConfig.primaryButtonStyle.copyWith(
                padding: WidgetStateProperty.all(
                  EdgeInsets.symmetric(vertical: buttonPadding),
                ),
              ),
              child: _isLoading
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ComponentConfig.smallLoadingIndicator,
                        SizedBox(width: DesignConfig.spaceSM),
                        const Text('Sending...'),
                      ],
                    )
                  : const Text('Send form to applicant'),
            ),
          ),
        ],
      );
    } else {
      return Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () {
                _handleFormSubmission();
              },
              style: ComponentConfig.secondaryButtonStyle.copyWith(
                padding: WidgetStateProperty.all(
                  EdgeInsets.symmetric(vertical: buttonPadding),
                ),
              ),
              child: const Text('Start Application'),
            ),
          ),
          SizedBox(width: buttonSpacing),
          Expanded(
            child: ElevatedButton(
              onPressed: _isLoading
                  ? null
                  : () {
                      _handleFormSubmission();
                    },
              style: ComponentConfig.primaryButtonStyle.copyWith(
                padding: WidgetStateProperty.all(
                  EdgeInsets.symmetric(vertical: buttonPadding),
                ),
              ),
              child: _isLoading
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ComponentConfig.smallLoadingIndicator,
                        SizedBox(width: DesignConfig.spaceSM),
                        const Text('Sending...'),
                      ],
                    )
                  : const Text('Send form to applicant'),
            ),
          ),
        ],
      );
    }
  }

  Widget _buildProductCard(
    AddApplicantModel product,
    bool isSelected,
    int index, {
    bool isInteractive = true,
  }) {
    return _OptimizedProductCard(
      product: product,
      isSelected: isSelected,
      onTap: isInteractive ? () => _handleSelectionChanged(index) : null,
      isInteractive: isInteractive,
    );
  }

  Widget _buildSelectedProductsSection() {
    final jobRoleState = ref.watch(jobRoleProvider);

    final products = jobRoleState.availableProducts.isNotEmpty
        ? jobRoleState.availableProducts
              .map(
                (product) => AddApplicantModel(
                  prefixIcon: '',
                  title: product.name,
                  price: product.price,
                ),
              )
              .toList()
        : addApplicantModel;

    final selectedProducts = _selectedItems
        .map((index) => products[index])
        .toList();
    final total = _calculateTotalPrice();

    final containerPadding = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 12.0,
      tablet: 14.0,
      desktop: 16.0,
    );

    final titleFontSize = ResponsiveHelper.getResponsiveFontSize(
      context,
      mobile: 13.0,
      tablet: 14.0,
      desktop: 14.0,
    );

    final itemFontSize = ResponsiveHelper.getResponsiveFontSize(
      context,
      mobile: 11.0,
      tablet: 12.0,
      desktop: 12.0,
    );

    final totalFontSize = ResponsiveHelper.getResponsiveFontSize(
      context,
      mobile: 13.0,
      tablet: 14.0,
      desktop: 14.0,
    );

    return Container(
      padding: EdgeInsets.all(containerPadding),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Selected Items:',
            style: TextStyle(
              fontSize: titleFontSize,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          SizedBox(
            height: ResponsiveHelper.getResponsiveValue(
              context,
              mobile: 10.0,
              tablet: 12.0,
              desktop: 12.0,
            ),
          ),
          ...selectedProducts.map((product) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      product.title,
                      style: TextStyle(fontSize: itemFontSize),
                    ),
                  ),
                  Text(
                    '${product.price?.toStringAsFixed(0) ?? '0'}£',
                    style: TextStyle(
                      fontSize: itemFontSize,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            );
          }),
          const Divider(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total',
                style: TextStyle(
                  fontSize: totalFontSize,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '${total.toStringAsFixed(0)}£',
                style: TextStyle(
                  fontSize: totalFontSize,
                  fontWeight: FontWeight.bold,
                  color: AppColors.kBlueColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _OptimizedProductCard extends StatefulWidget {
  final AddApplicantModel product;
  final bool isSelected;
  final VoidCallback? onTap;
  final bool isInteractive;

  const _OptimizedProductCard({
    required this.product,
    required this.isSelected,
    this.onTap,
    this.isInteractive = true,
  });

  @override
  State<_OptimizedProductCard> createState() => _OptimizedProductCardState();
}

class _OptimizedProductCardState extends State<_OptimizedProductCard> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    final isMobile = context.isMobile;
    final isTablet = context.isTablet;
    final isDesktop = context.isDesktop;

    final cardHeight = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 80.0,
      tablet: 75.0,
      desktop: 70.0,
    );

    final horizontalMargin = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 2.0,
      tablet: 3.0,
      desktop: 2.0,
    );

    final cardPadding = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 6.0,
      tablet: 6.0,
      desktop: 6.0,
    );

    final iconSpacing = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 8.0,
      tablet: 10.0,
      desktop: 12.0,
    );

    return MouseRegion(
      onEnter: widget.isInteractive
          ? (_) => setState(() => _isHovered = true)
          : null,
      onExit: widget.isInteractive
          ? (_) => setState(() => _isHovered = false)
          : null,
      child: GestureDetector(
        onTap: widget.isInteractive ? widget.onTap : null,
        child: Container(
          height: cardHeight,
          margin: EdgeInsets.symmetric(horizontal: horizontalMargin),
          decoration: BoxDecoration(
            color: _kProductCardColor,
            border: Border.all(
              color: widget.isSelected
                  ? AppColors.kBlueColor
                  : Colors.transparent,
              width: widget.isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(8.0),
            boxShadow: (widget.isInteractive && _isHovered)
                ? _buildHoverShadow()
                : null,
          ),
          child: Stack(
            children: [
              Padding(
                padding: EdgeInsets.all(cardPadding),
                child: Row(
                  children: [
                    _buildIconContainer(isMobile, isTablet, isDesktop),
                    SizedBox(width: iconSpacing),
                    Expanded(
                      child: _buildTextContent(isMobile, isTablet, isDesktop),
                    ),
                  ],
                ),
              ),
              if (widget.isSelected) _buildSelectionIndicator(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIconContainer(bool isMobile, bool isTablet, bool isDesktop) {
    final iconSize = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 35.0,
      tablet: 40.0,
      desktop: 45.0,
    );

    final iconInnerSize = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 18.0,
      tablet: 20.0,
      desktop: 22.0,
    );

    return Container(
      width: iconSize,
      height: iconSize,
      decoration: BoxDecoration(
        color: widget.isSelected ? AppColors.kBlueColor : Colors.grey.shade400,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Icon(
        _getProductIcon(widget.product.title),
        color: Colors.white,
        size: iconInnerSize,
      ),
    );
  }

  Widget _buildTextContent(bool isMobile, bool isTablet, bool isDesktop) {
    final titleFontSize = ResponsiveHelper.getResponsiveFontSize(
      context,
      mobile: 11.0,
      tablet: 12.0,
      desktop: 13.0,
    );

    final priceFontSize = ResponsiveHelper.getResponsiveFontSize(
      context,
      mobile: 13.0,
      tablet: 14.0,
      desktop: 15.0,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          widget.product.title,
          style: TextStyle(
            fontSize: titleFontSize,
            color: AppColors.kQuestionTextColor,
            fontWeight: FontWeight.w400,
          ),
          maxLines: isMobile ? 2 : 1,
          overflow: TextOverflow.ellipsis,
        ),
        SizedBox(
          height: ResponsiveHelper.getResponsiveValue(
            context,
            mobile: 2.0,
            tablet: 3.0,
            desktop: 4.0,
          ),
        ),
        Text(
          '${widget.product.price?.toStringAsFixed(0) ?? '0'}£',
          style: TextStyle(
            fontWeight: FontWeight.w400,
            fontSize: priceFontSize,
            color: AppColors.kBlueColor,
          ),
        ),
      ],
    );
  }

  Widget _buildSelectionIndicator() {
    return Positioned(
      top: 4.0,
      right: 4.0,
      child: CircleAvatar(
        radius: 10,
        backgroundColor: AppColors.kBlueColor,
        child: const Icon(Icons.check, color: Colors.white, size: 14),
      ),
    );
  }

  List<BoxShadow> _buildHoverShadow() {
    return [
      BoxShadow(
        color: Colors.black.withValues(alpha: 0.25),
        spreadRadius: 2,
        blurRadius: 4,
        offset: const Offset(0, 4),
      ),
    ];
  }

  static IconData _getProductIcon(String productName) {
    final name = productName.toLowerCase();
    if (name.contains('dbs')) return Icons.security;
    if (name.contains('reference')) return Icons.assignment;
    if (name.contains('social')) return Icons.share;
    if (name.contains('work')) return Icons.work;
    if (name.contains('credit')) return Icons.credit_card;
    if (name.contains('id')) return Icons.badge;
    return Icons.check_circle;
  }
}
