import 'package:SolidCheck/core/navigation/app_router_new.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/applicants/presentation/viewmodels/applicant_dashboard_viewmodel.dart';
import 'package:SolidCheck/features/applicants/presentation/widgets/overview/applicant_overview_content.dart';
import 'package:SolidCheck/shared/utils/responsive.dart';
import 'package:SolidCheck/shared/widgets/navigation/applicant_sidebar.dart';
import 'package:SolidCheck/shared/widgets/navigation/universal_mobile_header.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Applicant Dashboard Screen
/// This screen serves dual purposes:
/// 1. Main dashboard for applicant users when they log in
/// 2. Detail view when client users click on a specific applicant from the data table
class ApplicantDashboardScreen extends ConsumerStatefulWidget {
  final String? applicantId;
  
  const ApplicantDashboardScreen({
    super.key,
    this.applicantId,
  });

  @override
  ConsumerState<ApplicantDashboardScreen> createState() => _ApplicantDashboardScreenState();
}

class _ApplicantDashboardScreenState extends ConsumerState<ApplicantDashboardScreen> {
  @override
  void initState() {
    super.initState();

    // Load applicant data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Reset sidebar to Overview when navigating to applicant dashboard
      ref.read(sidebarSelectedIndexProvider.notifier).state = 0;

      final viewModel = ref.read(applicantDashboardViewModelProvider.notifier);
      if (widget.applicantId != null) {
        // Load specific applicant data (for client users) - use new API
        viewModel.loadApplicantDetails(widget.applicantId!);
      } else {
        // Load current user's applicant data (for applicant users)
        viewModel.loadCurrentApplicant();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final isMobile = ResponsiveUtil.isMobile(context);
    final dashboardState = ref.watch(applicantDashboardViewModelProvider);

    return Scaffold(
      backgroundColor: AppColors.kWhiteColor,
      drawer: isMobile ? ApplicantSidebar(
        applicantId: widget.applicantId,
        showBackButton: widget.applicantId != null,
        onBackPressed: widget.applicantId != null
            ? () => AppRouter.navigateToDashboard()
            : null,
      ) : null,
      body: dashboardState.isLoading
          ? const Center(child: CircularProgressIndicator())
          : dashboardState.error != null
              ? _buildErrorWidget(dashboardState.error!)
              : _buildSidebarLayout(context, isMobile),
    );
  }



  Widget _buildErrorWidget(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          const Text(
            'Error loading applicant data',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              final viewModel = ref.read(applicantDashboardViewModelProvider.notifier);
              if (widget.applicantId != null) {
                viewModel.loadApplicantDetails(widget.applicantId!);
              } else {
                viewModel.loadCurrentApplicant();
              }
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }





  Widget _buildSidebarLayout(BuildContext context, bool isMobile) {
    if (isMobile) {
      // Mobile layout with universal header
      return Column(
        children: [
          // Universal mobile header
          const UniversalMobileHeader(),
          // Content area
          Expanded(
            child: ApplicantOverviewContent(applicantId: widget.applicantId),
          ),
        ],
      );
    }

    // Desktop layout - sidebar + content
    return Row(
      children: [
        // Sidebar
        ApplicantSidebar(
          applicantId: widget.applicantId,
          showBackButton: widget.applicantId != null,
          onBackPressed: widget.applicantId != null
              ? () => AppRouter.navigateToDashboard()
              : null,
        ),

        // Main content
        Expanded(
          child: ApplicantOverviewContent(applicantId: widget.applicantId),
        ),
      ],
    );
  }
}
