import 'package:SolidCheck/features/applicants/data/models/job_role_model.dart';
import 'package:SolidCheck/features/applicants/data/repositories/job_role_repository.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Job Role State
class JobRoleState {
  final bool isLoading;
  final List<JobRoleData> allJobRoles;
  final List<OrganizationModel> organizations;
  final List<JobRoleData> filteredJobRoles;
  final List<ProductData> availableProducts;
  final OrganizationModel? selectedOrganization;
  final JobRoleData? selectedJobRole;
  final String? error;

  JobRoleState({
    this.isLoading = false,
    this.allJobRoles = const [],
    this.organizations = const [],
    this.filteredJobRoles = const [],
    this.availableProducts = const [],
    this.selectedOrganization,
    this.selectedJobRole,
    this.error,
  });

  JobRoleState copyWith({
    bool? isLoading,
    List<JobRoleData>? allJobRoles,
    List<OrganizationModel>? organizations,
    List<JobRoleData>? filteredJobRoles,
    List<ProductData>? availableProducts,
    OrganizationModel? selectedOrganization,
    JobRoleData? selectedJobRole,
    String? error,
    bool clearSelectedOrganization = false,
    bool clearSelectedJobRole = false,
    bool clearError = false,
  }) {
    return JobRoleState(
      isLoading: isLoading ?? this.isLoading,
      allJobRoles: allJobRoles ?? this.allJobRoles,
      organizations: organizations ?? this.organizations,
      filteredJobRoles: filteredJobRoles ?? this.filteredJobRoles,
      availableProducts: availableProducts ?? this.availableProducts,
      selectedOrganization: clearSelectedOrganization ? null : (selectedOrganization ?? this.selectedOrganization),
      selectedJobRole: clearSelectedJobRole ? null : (selectedJobRole ?? this.selectedJobRole),
      error: clearError ? null : (error ?? this.error),
    );
  }
}

/// Job Role Notifier
class JobRoleNotifier extends StateNotifier<JobRoleState> {
  final JobRoleRepository _repository;

  JobRoleNotifier(this._repository) : super(JobRoleState());

  /// Load job roles from API
  Future<void> loadJobRoles() async {
    state = state.copyWith(isLoading: true, clearError: true);

    try {
      final response = await _repository.getJobRoles();
      
      if (response.success) {
        final organizations = _repository.getUniqueOrganizations(response.data);
        
        state = state.copyWith(
          isLoading: false,
          allJobRoles: response.data,
          organizations: organizations,
          filteredJobRoles: [], // Empty until organization is selected
          availableProducts: [], // Empty until job role is selected
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message.isNotEmpty ? response.message : 'Failed to load job roles',
        );
      }
    } catch (error) {
      state = state.copyWith(
        isLoading: false,
        error: error.toString(),
      );
    }
  }

  /// Select organization and filter job roles
  void selectOrganization(OrganizationModel? organization) {
    if (organization == null) {
      state = state.copyWith(
        clearSelectedOrganization: true,
        clearSelectedJobRole: true,
        filteredJobRoles: [],
        availableProducts: [],
      );
      return;
    }

    final filteredJobRoles = _repository.getJobRolesForOrganization(
      state.allJobRoles,
      organization.entityId,
    );

    state = state.copyWith(
      selectedOrganization: organization,
      clearSelectedJobRole: true, // Clear job role when organization changes
      filteredJobRoles: filteredJobRoles,
      availableProducts: [], // Clear products when organization changes
    );
  }

  /// Select job role and load products
  void selectJobRole(JobRoleData? jobRole) {
    if (jobRole == null) {
      state = state.copyWith(
        clearSelectedJobRole: true,
        availableProducts: [],
      );
      return;
    }

    final products = _repository.getProductsForJobRole(jobRole);

    state = state.copyWith(
      selectedJobRole: jobRole,
      availableProducts: products,
    );
  }

  /// Clear all selections
  void clearSelections() {
    state = state.copyWith(
      clearSelectedOrganization: true,
      clearSelectedJobRole: true,
      filteredJobRoles: [],
      availableProducts: [],
    );
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(clearError: true);
  }
}

/// Provider for Job Role State
final jobRoleProvider = StateNotifierProvider<JobRoleNotifier, JobRoleState>((ref) {
  final repository = ref.watch(jobRoleRepositoryProvider);
  return JobRoleNotifier(repository);
});
