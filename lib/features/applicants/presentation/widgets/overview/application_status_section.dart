import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/applicants/presentation/viewmodels/applicant_dashboard_viewmodel.dart';
import 'package:SolidCheck/features/dbs/presentation/screens/dbs_application_form_screen.dart';
import 'package:SolidCheck/shared/utils/responsive.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Application Status section showing status of different checks
class ApplicationStatusSection extends ConsumerWidget {
  final dynamic applicant;

  const ApplicationStatusSection({
    super.key,
    required this.applicant,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isMobile = ResponsiveUtil.isMobile(context);
    final dashboardState = ref.watch(applicantDashboardViewModelProvider);

    // Use new API data if available, otherwise fall back to legacy data
    final applications = dashboardState.applicantDetails?.data.applications ?? [];
    final requestedChecks = applicant?.requestedChecks ?? [];

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.kWhiteColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.grey[300]!,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(isMobile ? 12.0 : 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Compact header
            Row(
              children: [
                Icon(
                  Icons.track_changes_outlined,
                  color: AppColors.kBlueColor,
                  size: 16,
                ),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Application Progress',
                    style: TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.w600,
                      fontSize: isMobile ? 14.0 : 16.0,
                    ),
                  ),
                ),
                // Compact progress indicator
                if (applications.isNotEmpty || requestedChecks.isNotEmpty)
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.kBlueColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${_getCompletedCount(applications, requestedChecks)}/${_getTotalCount(applications, requestedChecks)}',
                      style: TextStyle(
                        color: AppColors.kBlueColor,
                        fontSize: isMobile ? 10 : 11,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
              ],
            ),
            SizedBox(height: 12),

            // Show applications from new API if available
            if (applications.isNotEmpty) ...[
              ...applications.map((app) => _buildEnhancedStatusItem(
                context,
                ref,
                app.product.name,
                app.product.variant,
                app.completionStatus.statusText,
                app.completionStatus.isComplete,
                isMobile,
                app: app,
              )),
            ] else if (requestedChecks.isNotEmpty) ...[
              // Fallback to legacy data
              ...requestedChecks.map((check) => _buildEnhancedStatusItem(
                context,
                ref,
                check.name ?? 'Unknown Check',
                check.type ?? '',
                check.status ?? 'Unknown',
                false,
                isMobile,
              )),
            ] else ...[
              // Enhanced empty state
              _buildEmptyState(isMobile),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedStatusItem(
    BuildContext context,
    WidgetRef ref,
    String name,
    String variant,
    String status,
    bool isComplete,
    bool isMobile, {
    dynamic app,
  }) {
    final icon = _getCheckIcon(variant, name);
    final statusColor = _getStatusColor(status, isComplete);
    final statusBgColor = statusColor.withValues(alpha: 0.1);
    final progress = isComplete ? 1.0 : (status.toLowerCase() == 'processing' ? 0.5 : 0.0);

    return GestureDetector(
      onTap: () => _handleApplicationTap(context, app, ref),
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: EdgeInsets.all(isMobile ? 10 : 12),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: Colors.grey[200]!,
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Row(
              children: [
                // Compact check icon
                Icon(
                  icon,
                  color: AppColors.kBlueColor,
                  size: 16,
                ),
                const SizedBox(width: 8),

                // Compact check details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        name,
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: isMobile ? 12 : 13,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      if (variant.isNotEmpty) ...[
                        const SizedBox(height: 1),
                        Text(
                          variant,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: isMobile ? 10 : 11,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                // Compact status badge
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: statusBgColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    status,
                    style: TextStyle(
                      color: statusColor,
                      fontSize: isMobile ? 9 : 10,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),

                // Compact click indicator
                const SizedBox(width: 4),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 12,
                  color: Colors.grey[400],
                ),
              ],
            ),

            // Compact progress bar
            const SizedBox(height: 8),
            Container(
              height: 3,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(2),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: progress,
                child: Container(
                  decoration: BoxDecoration(
                    color: statusColor,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(bool isMobile) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(isMobile ? 16 : 20),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: Colors.grey[200]!,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.track_changes_outlined,
            color: Colors.grey[500],
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            'No applications in progress',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: isMobile ? 12 : 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Your application progress will appear here once checks are initiated',
            style: TextStyle(
              color: Colors.grey[500],
              fontSize: isMobile ? 10 : 12,
              fontWeight: FontWeight.w400,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  int _getCompletedCount(List<dynamic> applications, List<dynamic> requestedChecks) {
    if (applications.isNotEmpty) {
      return applications.where((app) => app.completionStatus.isComplete).length;
    } else if (requestedChecks.isNotEmpty) {
      return requestedChecks.where((check) => check.status?.toLowerCase() == 'completed').length;
    }
    return 0;
  }

  int _getTotalCount(List<dynamic> applications, List<dynamic> requestedChecks) {
    if (applications.isNotEmpty) {
      return applications.length;
    } else if (requestedChecks.isNotEmpty) {
      return requestedChecks.length;
    }
    return 0;
  }

  IconData _getCheckIcon(String variant, String name) {
    final lowerName = name.toLowerCase();
    final lowerVariant = variant.toLowerCase();

    if (lowerVariant.contains('dbs') || lowerName.contains('dbs')) {
      return Icons.security;
    } else if (lowerName.contains('reference')) {
      return Icons.people_outline;
    } else if (lowerName.contains('right to work') || lowerName.contains('eligibility')) {
      return Icons.work_outline;
    } else if (lowerName.contains('qualification') || lowerName.contains('education')) {
      return Icons.school_outlined;
    } else if (lowerName.contains('id') || lowerName.contains('identity')) {
      return Icons.badge_outlined;
    } else if (lowerName.contains('disclosure')) {
      return Icons.description_outlined;
    } else if (lowerName.contains('medical') || lowerName.contains('health')) {
      return Icons.local_hospital;
    } else {
      return Icons.check_circle_outline;
    }
  }

  Color _getStatusColor(String status, bool isComplete) {
    if (isComplete) {
      return Colors.green[700]!;
    }

    switch (status.toLowerCase()) {
      case 'processing':
      case 'in progress':
        return Colors.orange[700]!;
      case 'not started':
      case 'draft':
        return Colors.red[700]!;
      case 'completed':
      case 'complete':
        return Colors.green[700]!;
      case 'pending':
        return Colors.blue[700]!;
      case 'review':
        return Colors.purple[700]!;
      default:
        return Colors.grey[700]!;
    }
  }

  void _handleApplicationTap(BuildContext context, dynamic app, WidgetRef ref) {
    if (app == null) return;

    // Check if it's a DBS product
    if (app.product.variant.toUpperCase() == 'DBS') {
      // Navigate directly to DBS form screen (original behavior)
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => DBSApplicationFormScreen(
            applicantId: applicant?.id?.toString(),
            productCode: app.product.code,
            productName: app.product.name,
          ),
        ),
      );
    } else {
      // For non-DBS products, show coming soon message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${app.product.name} form coming soon'),
          duration: const Duration(seconds: 2),
          backgroundColor: AppColors.kBlueColor,
        ),
      );
    }
  }


}
