import 'package:SolidCheck/features/applicants/presentation/viewmodels/applicant_dashboard_viewmodel.dart';
import 'package:SolidCheck/features/applicants/presentation/widgets/overview/activity_log_section.dart';
import 'package:SolidCheck/features/applicants/presentation/widgets/overview/application_status_section.dart';
import 'package:SolidCheck/features/applicants/presentation/widgets/overview/overview_cards_section.dart';
import 'package:SolidCheck/features/applicants/presentation/widgets/overview/overview_header_section.dart';
import 'package:SolidCheck/shared/utils/responsive.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Main content area for applicant overview
/// Contains all the overview sections in a scrollable layout
class ApplicantOverviewContent extends ConsumerWidget {
  final String? applicantId;

  const ApplicantOverviewContent({
    super.key,
    this.applicantId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dashboardState = ref.watch(applicantDashboardViewModelProvider);
    final applicant = dashboardState.applicant;
    final isMobile = ResponsiveUtil.isMobile(context);

    if (applicant == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return Container(
      color: Colors.white,
      child: SingleChildScrollView(
        padding: EdgeInsets.fromLTRB(
          isMobile ? 16.0 : 16.0,
          isMobile ? 16.0 : 16.0, // Top padding for mobile to account for header
          isMobile ? 16.0 : 16.0,
          isMobile ? 16.0 : 16.0,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Compact header
            OverviewHeaderSection(applicant: applicant),
            const SizedBox(height: 16),

            // Compact overview cards
            OverviewCardsSection(applicant: applicant),
            const SizedBox(height: 16),

            // Compact application status section
            ApplicationStatusSection(applicant: applicant),
            const SizedBox(height: 16),

            // Compact activity log section
            ActivityLogSection(applicantId: applicantId),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }


}
