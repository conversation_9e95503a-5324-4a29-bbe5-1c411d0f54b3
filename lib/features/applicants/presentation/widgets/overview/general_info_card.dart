import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/shared/utils/responsive.dart';
import 'package:flutter/material.dart';

/// General Information Card Widget
/// Displays job role and employer company information
class GeneralInfoCard extends StatelessWidget {
  final String applyingToJob;
  final String employerCompany;

  const GeneralInfoCard({
    super.key,
    required this.applyingToJob,
    required this.employerCompany,
  });

  @override
  Widget build(BuildContext context) {
    final isMobile = ResponsiveUtil.isMobile(context);

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.kWhiteColor,
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(
          color: Colors.grey[300]!,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(isMobile ? 12.0 : 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Compact header
            Row(
              children: [
                Icon(
                  Icons.person_outline,
                  color: AppColors.kBlueColor,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  'General Information',
                  style: TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.w600,
                    fontSize: isMobile ? 14.0 : 16.0,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12.0),

            // Enhanced info items
            _buildEnhancedInfoItem(
              Icons.work_outline,
              'Position',
              applyingToJob.isNotEmpty ? applyingToJob : 'Not specified',
              isMobile,
            ),
            const SizedBox(height: 8.0),
            _buildEnhancedInfoItem(
              Icons.business_outlined,
              'Company',
              employerCompany.isNotEmpty ? employerCompany : 'Not specified',
              isMobile,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedInfoItem(
    IconData icon,
    String label,
    String value,
    bool isMobile,
  ) {
    return Container(
      padding: EdgeInsets.all(isMobile ? 8 : 10),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: Colors.grey[200]!,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: AppColors.kBlueColor,
            size: 14,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: isMobile ? 10 : 11,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: isMobile ? 12 : 13,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
