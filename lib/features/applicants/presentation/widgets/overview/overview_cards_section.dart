import 'package:SolidCheck/features/applicants/presentation/widgets/overview/applicant_status_card.dart';
import 'package:SolidCheck/features/applicants/presentation/widgets/overview/general_info_card.dart';
import 'package:SolidCheck/shared/utils/responsive.dart';
import 'package:flutter/material.dart';

/// Overview cards section containing General and Applicant status cards
class OverviewCardsSection extends StatelessWidget {
  final dynamic applicant;

  const OverviewCardsSection({
    super.key,
    required this.applicant,
  });

  @override
  Widget build(BuildContext context) {
    final isMobile = ResponsiveUtil.isMobile(context);
    final isTablet = ResponsiveUtil.isTablet(context);

    if (isMobile) {
      return _buildMobileLayout();
    } else if (isTablet) {
      return _buildTabletLayout();
    } else {
      return _buildDesktopLayout();
    }
  }

  Widget _buildDesktopLayout() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: GeneralInfoCard(
            applyingToJob: applicant?.jobRole ?? 'N/A',
            employerCompany: applicant?.organization ?? 'N/A',
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ApplicantStatusCard(
            status: applicant?.displayStatus ?? 'Unknown',
            dateStarted: applicant?.dateCreated?.toString() ?? '',
            dateUpdated: applicant?.lastUpdated?.toString() ?? '',
          ),
        ),
      ],
    );
  }

  Widget _buildTabletLayout() {
    return Row(
      children: [
        Expanded(
          child: GeneralInfoCard(
            applyingToJob: applicant?.jobRole ?? 'N/A',
            employerCompany: applicant?.organization ?? 'N/A',
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ApplicantStatusCard(
            status: applicant?.displayStatus ?? 'Unknown',
            dateStarted: applicant?.dateCreated?.toString() ?? '',
            dateUpdated: applicant?.lastUpdated?.toString() ?? '',
          ),
        ),
      ],
    );
  }

  Widget _buildMobileLayout() {
    return Column(
      children: [
        GeneralInfoCard(
          applyingToJob: applicant?.jobRole ?? 'N/A',
          employerCompany: applicant?.organization ?? 'N/A',
        ),
        const SizedBox(height: 12),
        ApplicantStatusCard(
          status: applicant?.displayStatus ?? 'Unknown',
          dateStarted: applicant?.dateCreated?.toString() ?? '',
          dateUpdated: applicant?.lastUpdated?.toString() ?? '',
        ),
      ],
    );
  }
}
