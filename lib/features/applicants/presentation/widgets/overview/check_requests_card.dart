import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/applicants/presentation/viewmodels/applicant_dashboard_viewmodel.dart';
import 'package:SolidCheck/shared/utils/responsive.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Enhanced Check Requests Card Widget
/// Displays the checks requested for an applicant with modern design
class CheckRequestsCard extends ConsumerWidget {
  final List<dynamic> checks;

  const CheckRequestsCard({
    super.key,
    required this.checks,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isMobile = ResponsiveUtil.isMobile(context);
    final dashboardState = ref.watch(applicantDashboardViewModelProvider);

    // Get applications from new API if available
    final applications = dashboardState.applicantDetails?.data.applications ?? [];
    final requestedChecks = checks.isNotEmpty ? checks : [];

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.kWhiteColor,
        borderRadius: BorderRadius.circular(20.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 6,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(isMobile ? 24.0 : 28.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with icon
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.kBlueColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.assignment_outlined,
                    color: AppColors.kBlueColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Checks Requested',
                    style: TextStyle(
                      color: AppColors.kBlueColor,
                      fontWeight: FontWeight.w700,
                      fontSize: 18.0,
                      letterSpacing: -0.5,
                    ),
                  ),
                ),
                // Count badge
                if (applications.isNotEmpty || requestedChecks.isNotEmpty)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.kBlueColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${applications.isNotEmpty ? applications.length : requestedChecks.length}',
                      style: TextStyle(
                        color: AppColors.kWhiteColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 24.0),

            // Checks list
            if (applications.isNotEmpty) ...[
              // Use new API data
              ...applications.map((app) => _buildEnhancedCheckItem(
                app.product.name,
                app.product.variant,
                app.completionStatus.statusText,
                app.completionStatus.isComplete,
                isMobile,
              )),
            ] else if (requestedChecks.isNotEmpty) ...[
              // Use legacy data
              ...requestedChecks.map((check) => _buildEnhancedCheckItem(
                check.name ?? 'Unknown Check',
                check.type ?? '',
                check.status ?? 'Unknown',
                false,
                isMobile,
              )),
            ] else ...[
              // Empty state
              _buildEmptyState(isMobile),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedCheckItem(
    String name,
    String variant,
    String status,
    bool isComplete,
    bool isMobile,
  ) {
    final icon = _getCheckIcon(variant, name);
    final statusColor = _getStatusColor(status, isComplete);
    final statusBgColor = statusColor.withValues(alpha: 0.1);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: EdgeInsets.all(isMobile ? 16 : 18),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey[200]!,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Check icon
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: AppColors.kBlueColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: AppColors.kBlueColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),

          // Check details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: TextStyle(
                    color: AppColors.kBlackColor,
                    fontSize: isMobile ? 14 : 15,
                    fontWeight: FontWeight.w600,
                    height: 1.2,
                  ),
                ),
                if (variant.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    variant,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: isMobile ? 12 : 13,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ],
            ),
          ),

          // Status badge
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: statusBgColor,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              status,
              style: TextStyle(
                color: statusColor,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(bool isMobile) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(isMobile ? 24 : 32),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey[200]!,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(50),
            ),
            child: Icon(
              Icons.assignment_outlined,
              color: Colors.grey[500],
              size: 32,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'No checks requested',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: isMobile ? 14 : 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Checks will appear here once they are assigned',
            style: TextStyle(
              color: Colors.grey[500],
              fontSize: isMobile ? 12 : 14,
              fontWeight: FontWeight.w400,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  IconData _getCheckIcon(String variant, String name) {
    final lowerName = name.toLowerCase();
    final lowerVariant = variant.toLowerCase();

    if (lowerVariant.contains('dbs') || lowerName.contains('dbs')) {
      return Icons.security;
    } else if (lowerName.contains('reference')) {
      return Icons.people_outline;
    } else if (lowerName.contains('right to work') || lowerName.contains('eligibility')) {
      return Icons.work_outline;
    } else if (lowerName.contains('qualification') || lowerName.contains('education')) {
      return Icons.school_outlined;
    } else if (lowerName.contains('id') || lowerName.contains('identity')) {
      return Icons.badge_outlined;
    } else if (lowerName.contains('disclosure')) {
      return Icons.description_outlined;
    } else if (lowerName.contains('medical') || lowerName.contains('health')) {
      return Icons.local_hospital;
    } else {
      return Icons.check_circle_outline;
    }
  }

  Color _getStatusColor(String status, bool isComplete) {
    if (isComplete) {
      return Colors.green[700]!;
    }

    switch (status.toLowerCase()) {
      case 'processing':
      case 'in progress':
        return Colors.orange[700]!;
      case 'not started':
      case 'draft':
        return Colors.red[700]!;
      case 'completed':
      case 'complete':
        return Colors.green[700]!;
      case 'pending':
        return Colors.blue[700]!;
      case 'review':
        return Colors.purple[700]!;
      default:
        return Colors.grey[700]!;
    }
  }
}
