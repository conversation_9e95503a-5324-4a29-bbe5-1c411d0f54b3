import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/applicants/presentation/viewmodels/applicant_dashboard_viewmodel.dart';
import 'package:SolidCheck/shared/utils/responsive.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Activity Log section with tabbed interface
class ActivityLogSection extends ConsumerStatefulWidget {
  final String? applicantId;

  const ActivityLogSection({
    super.key,
    this.applicantId,
  });

  @override
  ConsumerState<ActivityLogSection> createState() => _ActivityLogSectionState();
}

class _ActivityLogSectionState extends ConsumerState<ActivityLogSection> {
  int _selectedTabIndex = 0;

  @override
  Widget build(BuildContext context) {
    final dashboardState = ref.watch(applicantDashboardViewModelProvider);
    final activities = dashboardState.activities;
    final isMobile = ResponsiveUtil.isMobile(context);

    // Generate dynamic tabs based on applications/checks
    final tabs = _generateDynamicTabs(dashboardState);

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.kWhiteColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.grey[300]!,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(isMobile ? 12.0 : 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Compact header
            Row(
              children: [
                Icon(
                  Icons.history_outlined,
                  color: AppColors.kBlueColor,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Activity Timeline',
                    style: TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.w600,
                      fontSize: isMobile ? 14.0 : 16.0,
                    ),
                  ),
                ),
                // Compact activity count badge
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.kBlueColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${tabs.length}',
                    style: TextStyle(
                      color: AppColors.kBlueColor,
                      fontSize: isMobile ? 10 : 11,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Compact tab bar
            _buildEnhancedTabBar(tabs),
            const SizedBox(height: 12),

            // Compact activity content
            SizedBox(
              height: 200,
              child: _buildActivityContent(activities, tabs),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedTabBar(List<String> tabs) {
    return Container(
      padding: const EdgeInsets.all(2),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: tabs.asMap().entries.map((entry) {
            final index = entry.key;
            final tab = entry.value;
            final isSelected = index == _selectedTabIndex;

            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedTabIndex = index;
                });
              },
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                margin: const EdgeInsets.only(right: 2),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                decoration: BoxDecoration(
                  color: isSelected ? AppColors.kBlueColor : Colors.transparent,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  tab,
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                    color: isSelected ? AppColors.kWhiteColor : Colors.grey[600],
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildActivityContent(List<dynamic> activities, List<String> tabs) {
    // Generate dynamic activity data based on selected tab and available checks
    final selectedTab = tabs.isNotEmpty ? tabs[_selectedTabIndex] : 'All';
    final mockActivities = _generateActivitiesForTab(selectedTab);

    if (mockActivities.isEmpty) {
      return _buildEmptyActivityState(tabs);
    }

    return ListView.builder(
      padding: const EdgeInsets.only(top: 8),
      itemCount: mockActivities.length,
      itemBuilder: (context, index) {
        final activity = mockActivities[index];
        final isLast = index == mockActivities.length - 1;
        return _buildEnhancedActivityItem(activity, isLast);
      },
    );
  }

  Widget _buildEnhancedActivityItem(Map<String, dynamic> activity, bool isLast) {
    final type = activity['type'] as String;
    final color = _getActivityColor(type);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Compact timeline indicator
          Column(
            children: [
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    color: color.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Icon(
                  activity['icon'] as IconData,
                  size: 12,
                  color: color,
                ),
              ),
              if (!isLast) ...[
                Container(
                  width: 1,
                  height: 24,
                  margin: const EdgeInsets.symmetric(vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(1),
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(width: 8),

          // Compact activity content
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(6),
                border: Border.all(
                  color: Colors.grey[200]!,
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          activity['text'] as String,
                          style: const TextStyle(
                            fontSize: 11,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Text(
                        activity['time'] as String,
                        style: TextStyle(
                          fontSize: 9,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 2),
                  Text(
                    activity['description'] as String,
                    style: TextStyle(
                      fontSize: 9,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      activity['date'] as String,
                      style: TextStyle(
                        fontSize: 8,
                        color: color,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyActivityState(List<String> tabs) {
    final selectedTab = tabs.isNotEmpty ? tabs[_selectedTabIndex] : 'All';

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history_outlined,
            size: 24,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 8),
          Text(
            'No activity for $selectedTab',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Activity will appear here as you progress',
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[500],
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  Color _getActivityColor(String type) {
    switch (type) {
      case 'edit':
        return Colors.blue[600]!;
      case 'upload':
        return Colors.green[600]!;
      case 'complete':
        return Colors.purple[600]!;
      case 'status':
        return Colors.orange[600]!;
      default:
        return Colors.grey[600]!;
    }
  }

  List<String> _generateDynamicTabs(dynamic dashboardState) {
    final tabs = <String>['All'];

    // Add tabs based on applications from new API
    if (dashboardState.applicantDetails?.data.applications != null) {
      final applications = dashboardState.applicantDetails!.data.applications;
      for (final app in applications) {
        final tabName = _getTabNameFromProduct(app.product.name);
        if (!tabs.contains(tabName)) {
          tabs.add(tabName);
        }
      }
    } else if (dashboardState.applicant?.requestedChecks != null) {
      // Fallback to legacy data
      final checks = dashboardState.applicant!.requestedChecks!;
      for (final check in checks) {
        final tabName = _getTabNameFromProduct(check.name ?? '');
        if (!tabs.contains(tabName)) {
          tabs.add(tabName);
        }
      }
    }

    return tabs;
  }

  String _getTabNameFromProduct(String productName) {
    final lowerName = productName.toLowerCase();

    if (lowerName.contains('dbs')) {
      return 'DBS';
    } else if (lowerName.contains('reference')) {
      return 'Reference';
    } else if (lowerName.contains('right to work') || lowerName.contains('eligibility')) {
      return 'Right to Work';
    } else if (lowerName.contains('qualification') || lowerName.contains('education')) {
      return 'Qualification';
    } else if (lowerName.contains('id') || lowerName.contains('identity')) {
      return 'ID Verification';
    } else if (lowerName.contains('disclosure')) {
      return 'Disclosure';
    } else if (lowerName.contains('medical') || lowerName.contains('health')) {
      return 'Medical';
    } else {
      return 'Other';
    }
  }

  List<Map<String, dynamic>> _generateActivitiesForTab(String selectedTab) {
    // Base activities that apply to all tabs
    final baseActivities = [
      {
        'icon': Icons.person_add_outlined,
        'text': 'Application created',
        'description': 'Your application has been successfully created',
        'time': '10:30 AM',
        'date': '5 days ago',
        'type': 'status',
      },
      {
        'icon': Icons.email_outlined,
        'text': 'Welcome email sent',
        'description': 'Confirmation email sent to your registered address',
        'time': '10:35 AM',
        'date': '5 days ago',
        'type': 'status',
      },
    ];

    // Tab-specific activities
    final tabSpecificActivities = <Map<String, dynamic>>[];

    switch (selectedTab.toLowerCase()) {
      case 'all':
        tabSpecificActivities.addAll([
          {
            'icon': Icons.edit_outlined,
            'text': 'Personal details updated',
            'description': 'Contact information has been updated',
            'time': '2:15 PM',
            'date': 'Today',
            'type': 'edit',
          },
          {
            'icon': Icons.security,
            'text': 'DBS form started',
            'description': 'Enhanced DBS application form initiated',
            'time': '11:20 AM',
            'date': 'Yesterday',
            'type': 'complete',
          },
          {
            'icon': Icons.upload_file_outlined,
            'text': 'Document uploaded',
            'description': 'ID verification document submitted',
            'time': '3:45 PM',
            'date': '2 days ago',
            'type': 'upload',
          },
        ]);
        break;
      case 'dbs':
        tabSpecificActivities.addAll([
          {
            'icon': Icons.security,
            'text': 'DBS form started',
            'description': 'Enhanced DBS application form initiated',
            'time': '11:20 AM',
            'date': 'Yesterday',
            'type': 'complete',
          },
          {
            'icon': Icons.upload_file_outlined,
            'text': 'Supporting documents uploaded',
            'description': 'Identity documents submitted for DBS check',
            'time': '2:30 PM',
            'date': '2 days ago',
            'type': 'upload',
          },
          {
            'icon': Icons.notifications_outlined,
            'text': 'DBS application submitted',
            'description': 'Application sent to DBS for processing',
            'time': '4:15 PM',
            'date': '3 days ago',
            'type': 'status',
          },
        ]);
        break;
      case 'reference':
        tabSpecificActivities.addAll([
          {
            'icon': Icons.people_outline,
            'text': 'Reference request sent',
            'description': 'Reference request sent to your previous employer',
            'time': '9:30 AM',
            'date': 'Today',
            'type': 'status',
          },
          {
            'icon': Icons.edit_outlined,
            'text': 'Reference details updated',
            'description': 'Contact information for referee updated',
            'time': '4:20 PM',
            'date': 'Yesterday',
            'type': 'edit',
          },
        ]);
        break;
      case 'right to work':
        tabSpecificActivities.addAll([
          {
            'icon': Icons.work_outline,
            'text': 'Right to work check initiated',
            'description': 'Eligibility verification process started',
            'time': '1:45 PM',
            'date': 'Today',
            'type': 'status',
          },
          {
            'icon': Icons.upload_file_outlined,
            'text': 'Work authorization uploaded',
            'description': 'Passport and visa documents submitted',
            'time': '10:15 AM',
            'date': 'Yesterday',
            'type': 'upload',
          },
        ]);
        break;
      default:
        // For other tabs, show generic activities
        tabSpecificActivities.addAll([
          {
            'icon': Icons.assignment_outlined,
            'text': '$selectedTab check initiated',
            'description': 'Verification process has been started',
            'time': '12:00 PM',
            'date': 'Today',
            'type': 'status',
          },
        ]);
    }

    // Combine and sort by recency (most recent first)
    final allActivities = [...tabSpecificActivities, ...baseActivities];
    return allActivities;
  }
}
