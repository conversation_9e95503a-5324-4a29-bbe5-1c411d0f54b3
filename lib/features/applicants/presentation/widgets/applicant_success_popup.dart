import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/core/utils/responsive_helper.dart';
import 'package:SolidCheck/features/applicants/data/models/add_applicant_response.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Success popup widget for newly created applicant
class ApplicantSuccessPopup extends StatelessWidget {
  final AddApplicantResponse response;
  final VoidCallback onClose;

  const ApplicantSuccessPopup({
    super.key,
    required this.response,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {

    
    final dialogWidth = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: MediaQuery.of(context).size.width * 0.9,
      tablet: 500.0,
      desktop: 550.0,
    );

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: dialogWidth,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(context),
            _buildContent(context),
            _buildActions(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(ResponsiveHelper.getResponsiveValue(
        context,
        mobile: 20.0,
        tablet: 24.0,
        desktop: 24.0,
      )),
      decoration: BoxDecoration(
        color: AppColors.kBlueColor.withValues(alpha: 0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.green,
              borderRadius: BorderRadius.circular(50),
            ),
            child: const Icon(
              Icons.check,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Applicant Created Successfully!',
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getResponsiveFontSize(
                      context,
                      mobile: 18.0,
                      tablet: 20.0,
                      desktop: 20.0,
                    ),
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  response.message,
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getResponsiveFontSize(
                      context,
                      mobile: 12.0,
                      tablet: 14.0,
                      desktop: 14.0,
                    ),
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: onClose,
            icon: const Icon(Icons.close, color: Colors.grey),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    final contentPadding = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 20.0,
      tablet: 24.0,
      desktop: 24.0,
    );

    return Padding(
      padding: EdgeInsets.all(contentPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDetailRow(
            context,
            'Applicant Name',
            response.data.applicant.fullName,
            Icons.person,
          ),
          _buildDetailRow(
            context,
            'Email Address',
            response.data.applicant.email,
            Icons.email,
          ),
          _buildDetailRow(
            context,
            'Phone Number',
            response.data.applicant.phone,
            Icons.phone,
          ),
          _buildDetailRow(
            context,
            'Organization',
            response.data.entity.name,
            Icons.business,
          ),
          _buildDetailRow(
            context,
            'Job Role',
            response.data.jobRole.jobTitle,
            Icons.work,
          ),
          _buildDetailRow(
            context,
            'Applications Created',
            '${response.data.totalApplications} checks auto-generated',
            Icons.assignment,
          ),
          const SizedBox(height: 16),
          _buildPasswordSection(context),
        ],
      ),
    );
  }

  Widget _buildDetailRow(BuildContext context, String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 18,
            color: AppColors.kBlueColor,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getResponsiveFontSize(
                      context,
                      mobile: 12.0,
                      tablet: 13.0,
                      desktop: 13.0,
                    ),
                    fontWeight: FontWeight.w500,
                    color: Colors.grey.shade600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getResponsiveFontSize(
                      context,
                      mobile: 14.0,
                      tablet: 15.0,
                      desktop: 15.0,
                    ),
                    fontWeight: FontWeight.w400,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPasswordSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.amber.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.amber.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.key,
                size: 18,
                color: Colors.amber.shade700,
              ),
              const SizedBox(width: 8),
              Text(
                'Generated Password',
                style: TextStyle(
                  fontSize: ResponsiveHelper.getResponsiveFontSize(
                    context,
                    mobile: 13.0,
                    tablet: 14.0,
                    desktop: 14.0,
                  ),
                  fontWeight: FontWeight.w600,
                  color: Colors.amber.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    response.data.generatedPassword,
                    style: TextStyle(
                      fontSize: ResponsiveHelper.getResponsiveFontSize(
                        context,
                        mobile: 14.0,
                        tablet: 15.0,
                        desktop: 15.0,
                      ),
                      fontWeight: FontWeight.w500,
                      fontFamily: 'monospace',
                      color: Colors.black87,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () {
                    Clipboard.setData(ClipboardData(text: response.data.generatedPassword));
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Password copied to clipboard'),
                        duration: Duration(seconds: 2),
                      ),
                    );
                  },
                  icon: const Icon(Icons.copy, size: 18),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Copy password',
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'This password has been automatically generated for the applicant. Make sure to share it securely.',
            style: TextStyle(
              fontSize: ResponsiveHelper.getResponsiveFontSize(
                context,
                mobile: 11.0,
                tablet: 12.0,
                desktop: 12.0,
              ),
              color: Colors.amber.shade700,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActions(BuildContext context) {
    final actionPadding = ResponsiveHelper.getResponsiveValue(
      context,
      mobile: 20.0,
      tablet: 24.0,
      desktop: 24.0,
    );

    final isMobile = context.isMobile;

    return Padding(
      padding: EdgeInsets.all(actionPadding),
      child: Column(
        children: [
          if (response.data.applications.isNotEmpty) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.kBlueColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.kBlueColor.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.description,
                    color: AppColors.kBlueColor,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Next: Nominate documents for identity verification',
                      style: TextStyle(
                        fontSize: ResponsiveHelper.getResponsiveFontSize(
                          context,
                          mobile: 13.0,
                          tablet: 14.0,
                          desktop: 14.0,
                        ),
                        color: AppColors.kBlueColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],
          if (isMobile) ...[
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                onPressed: onClose,
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: AppColors.kBlueColor),
                  padding: EdgeInsets.symmetric(
                    vertical: ResponsiveHelper.getResponsiveValue(
                      context,
                      mobile: 14.0,
                      tablet: 16.0,
                      desktop: 16.0,
                    ),
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'Skip to Dashboard',
                  style: TextStyle(
                    color: AppColors.kBlueColor,
                    fontWeight: FontWeight.w600,
                    fontSize: ResponsiveHelper.getResponsiveFontSize(
                      context,
                      mobile: 14.0,
                      tablet: 15.0,
                      desktop: 15.0,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => _navigateToDocumentNomination(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.kBlueColor,
                  padding: EdgeInsets.symmetric(
                    vertical: ResponsiveHelper.getResponsiveValue(
                      context,
                      mobile: 14.0,
                      tablet: 16.0,
                      desktop: 16.0,
                    ),
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'Nominate Documents',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: ResponsiveHelper.getResponsiveFontSize(
                      context,
                      mobile: 14.0,
                      tablet: 15.0,
                      desktop: 15.0,
                    ),
                  ),
                ),
              ),
            ),
          ] else ...[
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: onClose,
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: AppColors.kBlueColor),
                      padding: EdgeInsets.symmetric(
                        vertical: ResponsiveHelper.getResponsiveValue(
                          context,
                          mobile: 14.0,
                          tablet: 16.0,
                          desktop: 16.0,
                        ),
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'Skip to Dashboard',
                      style: TextStyle(
                        color: AppColors.kBlueColor,
                        fontWeight: FontWeight.w600,
                        fontSize: ResponsiveHelper.getResponsiveFontSize(
                          context,
                          mobile: 14.0,
                          tablet: 15.0,
                          desktop: 15.0,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _navigateToDocumentNomination(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.kBlueColor,
                      padding: EdgeInsets.symmetric(
                        vertical: ResponsiveHelper.getResponsiveValue(
                          context,
                          mobile: 14.0,
                          tablet: 16.0,
                          desktop: 16.0,
                        ),
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'Nominate Documents',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                        fontSize: ResponsiveHelper.getResponsiveFontSize(
                          context,
                          mobile: 14.0,
                          tablet: 15.0,
                          desktop: 15.0,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  void _navigateToDocumentNomination(BuildContext context) {
    if (response.data.applications.isNotEmpty) {
      final firstApplication = response.data.applications.first;
      Navigator.of(context).pushReplacementNamed(
        '/document-nomination',
        arguments: {
          'applicationId': firstApplication.id.toString(),
          'applicantName': response.data.applicant.fullName,
        },
      );
    } else {
      onClose();
    }
  }
}
