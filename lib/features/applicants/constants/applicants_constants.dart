/// Applicants module specific constants
class ApplicantsConstants {
  // API Endpoints
  static String addApplicantEndpoint = '/applicant/addApplicant';
  static String getApplicantsEndpoint = '/client-applicants';
  static String updateApplicantEndpoint = '/applicant/update';
  static String deleteApplicantEndpoint = '/applicant/delete';

  // Routes
  static String addApplicantRoute = '/add-applicant';

  // Form field labels
  static String firstNameLabel = 'First Name';
  static String lastNameLabel = 'Last Name';
  static String emailLabel = 'Email Address';
  static String phoneLabel = 'Phone Number';
  static String organizationLabel = 'Organization';
  static String jobRoleLabel = 'Job Role';
  static String addressLabel = 'Address';
  static String cityLabel = 'City';
  static String postalCodeLabel = 'Postal Code';
  static String countryLabel = 'Country';

  // Validation messages
  static String firstNameRequired = 'First name is required';
  static String lastNameRequired = 'Last name is required';
  static String emailRequired = 'Email address is required';
  static String invalidEmail = 'Please enter a valid email address';
  static String phoneRequired = 'Phone number is required';
  static String invalidPhone = 'Please enter a valid phone number';
  static String organizationRequired = 'Organization is required';
  static String jobRoleRequired = 'Job role is required';
  static String addressRequired = 'Address is required';
  static String cityRequired = 'City is required';
  static String postalCodeRequired = 'Postal code is required';
  static String countryRequired = 'Country is required';

  // Success messages
  static String applicantAddedSuccess = 'Applicant added successfully';
  static String applicantUpdatedSuccess = 'Applicant updated successfully';
  static String applicantDeletedSuccess = 'Applicant deleted successfully';

  // Error messages
  static String addApplicantError = 'Failed to add applicant';
  static String updateApplicantError = 'Failed to update applicant';
  static String deleteApplicantError = 'Failed to delete applicant';
  static String loadApplicantsError = 'Failed to load applicants';
  static String networkError = 'Network connection error';

  // UI Text
  static String addApplicantTitle = 'Add New Applicant';
  static String editApplicantTitle = 'Edit Applicant';
  static String applicantDetailsTitle = 'Applicant Details';
  static String personalInformationSection = 'Personal Information';
  static String contactInformationSection = 'Contact Information';
  static String organizationInformationSection = 'Organization Information';
  static String addressInformationSection = 'Address Information';

  // Button text
  static String addApplicantButton = 'Add Applicant';
  static String updateApplicantButton = 'Update Applicant';
  static String cancelButton = 'Cancel';
  static String saveButton = 'Save';
  static String deleteButton = 'Delete';
  static String editButton = 'Edit';
  static String viewButton = 'View';

  // Loading states
  static String addingApplicantText = 'Adding applicant...';
  static String updatingApplicantText = 'Updating applicant...';
  static String deletingApplicantText = 'Deleting applicant...';
  static String loadingApplicantsText = 'Loading applicants...';

  // Validation patterns
  static String emailPattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static String phonePattern = r'^\+?[\d\s\-\(\)]+$';
  static String postalCodePattern = r'^[A-Za-z0-9\s\-]+$';

  // Form constraints
  static int maxFirstNameLength = 50;
  static int maxLastNameLength = 50;
  static int maxEmailLength = 100;
  static int maxPhoneLength = 20;
  static int maxOrganizationLength = 100;
  static int maxJobRoleLength = 100;
  static int maxAddressLength = 200;
  static int maxCityLength = 50;
  static int maxPostalCodeLength = 20;
  static int maxCountryLength = 50;

  // Default values
  static String defaultCountry = 'United Kingdom';
  static List<String> commonCountries = [
    'United Kingdom',
    'United States',
    'Canada',
    'Australia',
    'Germany',
    'France',
    'Spain',
    'Italy',
    'Netherlands',
    'Ireland',
  ];

  // Job roles
  static List<String> commonJobRoles = [
    'Software Developer',
    'Project Manager',
    'Business Analyst',
    'Quality Assurance',
    'DevOps Engineer',
    'Data Analyst',
    'UX/UI Designer',
    'Product Manager',
    'Sales Representative',
    'Marketing Specialist',
    'HR Specialist',
    'Finance Analyst',
    'Customer Support',
    'Operations Manager',
    'Consultant',
  ];

  // File upload
  static List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif'];
  static List<String> allowedDocumentTypes = ['pdf', 'doc', 'docx', 'txt'];
  static int maxFileSize = 10 * 1024 * 1024; // 10MB
  static String fileUploadError = 'File upload failed';
  static String fileSizeError = 'File size exceeds maximum limit';
  static String fileTypeError = 'File type not supported';

  // Pagination
  static int defaultPageSize = 20;
  static int maxPageSize = 100;

  // Search
  static String searchPlaceholder = 'Search applicants...';
  static int minSearchLength = 2;

  // Status options
  static List<String> statusOptions = [
    'Active',
    'Inactive',
    'Pending',
    'Completed',
    'Cancelled',
  ];

  // Animation durations
  static Duration defaultAnimationDuration = const Duration(milliseconds: 300);
  static Duration loadingAnimationDuration = const Duration(milliseconds: 500);

  // Spacing
  static double smallSpacing = 8;
  static double mediumSpacing = 16;
  static double largeSpacing = 24;
  static double extraLargeSpacing = 32;

  // Form styling
  static double formFieldHeight = 56;
  static double formFieldBorderRadius = 12;
  static double buttonHeight = 48;
  static double buttonBorderRadius = 12;
}
