import 'package:SolidCheck/core/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class ReferenceCheckCompleted extends ConsumerWidget {
  const ReferenceCheckCompleted({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: SizedBox(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 650,
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.all(15.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Center(
                                // child: SvgPicture.asset(
                                //   "assets/images/done_icon_for_result_page.svg",
                                // ),
                              ),
                              Center(
                                child: Text(
                                  'Done!',
                                  style: TextStyle(
                                    fontSize: 22.0,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.kBlueColor,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 20.0),
                        Text(
                          'Your Reference Check Form has been successfully submitted.',
                          style: TextStyle(
                            fontSize: 20.0,
                            fontWeight: FontWeight.w500,
                            color: AppColors.kQuestionTextColor,
                          ),
                        ),
                        const SizedBox(height: 30.0),
                        const Row(
                          children: [
                            // Expanded(
                            //   child: PaymentProcessActionButton(
                            //     buttonTitle: 'Go to Home',
                            //     onPressed: () {
                            //       context.go('/home');
                            //     },
                            //   ),
                            // ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
