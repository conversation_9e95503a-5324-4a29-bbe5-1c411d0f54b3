import 'package:SolidCheck/core/constants/constants.dart';
import 'package:SolidCheck/core/constants/ref_screen_icons.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/reference_check/views/views/declaration_form.dart';
import 'package:SolidCheck/features/reference_check/views/views/education.dart';
import 'package:SolidCheck/features/reference_check/views/views/employement.dart';
import 'package:SolidCheck/features/reference_check/views/views/employement_gap.dart';
import 'package:SolidCheck/features/reference_check/views/views/personal.dart';
import 'package:SolidCheck/features/reference_check/views/views/self_employement.dart';
import 'package:SolidCheck/features/reference_check/widgets/header.dart';
import 'package:SolidCheck/features/reference_check/widgets/icon_div.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:responsive_framework/responsive_framework.dart';

final selectedOptionProvider = StateProvider<String?>((ref) => null);
final selectedScreenProvider = StateProvider<String?>((ref) => null);

class RefCheckMainScreen extends ConsumerWidget {
  const RefCheckMainScreen({super.key});

  Widget _buildSelectedScreen(String? option) {
    switch (option) {
      case AppConstants.kAddEducationReferee:
        return const EducationReferenceScreen();
      case AppConstants.kAddEmploymentReferee:
        return const EmployementReferenceScreen();
      case AppConstants.kAddPersonalReferee:
        return const PersonalReferenceScreen();
      case AppConstants.kAddSelfEmploymentReferee:
        return const SelfEmploymentReferenceScreen();
      case AppConstants.kAddEmploymentGap:
        return const EmploymentGapReferenceScreen();
      case AppConstants.kDeclarationForm:
        return const DeclarationForm();
      default:
        return const Center(
          child: Text(
            'Please select an option from above.',
            style: TextStyle(fontSize: 16.0, color: Colors.grey),
          ),
        );
    }
  }

  Widget _buildIconMenu(Size size, WidgetRef ref, BuildContext context) {
    final isTab = ResponsiveBreakpoints.of(context).largerThan(TABLET);
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListTile(
            title: Text(
              'Select One',
              style: TextStyle(
                color: AppColors.kBlueColor,
                fontWeight: FontWeight.bold,
                fontSize: 20.0,
              ),
            ),
          ),
          isTab
              ? SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    spacing: 10,
                    children: [
                      buildReferenceCheckIconDiv(
                        size,
                        RefCheckScreenIcon.icon1,
                        AppConstants.kAddEducationReferee,
                        ref,
                        context,
                      ),
                      buildReferenceCheckIconDiv(
                        size,
                        RefCheckScreenIcon.icon2,
                        AppConstants.kAddEmploymentReferee,
                        ref,
                        context,
                      ),
                      buildReferenceCheckIconDiv(
                        size,
                        RefCheckScreenIcon.icon3,
                        AppConstants.kAddPersonalReferee,
                        ref,
                        context,
                      ),
                      buildReferenceCheckIconDiv(
                        size,
                        RefCheckScreenIcon.icon4,
                        AppConstants.kAddSelfEmploymentReferee,
                        ref,
                        context,
                      ),
                      buildReferenceCheckIconDiv(
                        size,
                        RefCheckScreenIcon.icon5,
                        AppConstants.kAddEmploymentGap,
                        ref,
                        context,
                      ),
                    ],
                  ),
                )
              : GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 10.0,
                    mainAxisSpacing: 10.0,
                  ),
                  itemCount: 5,
                  itemBuilder: (context, index) {
                    List<Widget> items = [
                      buildReferenceCheckIconDiv(
                        size,
                        RefCheckScreenIcon.icon1,
                        AppConstants.kAddEducationReferee,
                        ref,
                        context,
                      ),
                      buildReferenceCheckIconDiv(
                        size,
                        RefCheckScreenIcon.icon2,
                        AppConstants.kAddEmploymentReferee,
                        ref,
                        context,
                      ),
                      buildReferenceCheckIconDiv(
                        size,
                        RefCheckScreenIcon.icon3,
                        AppConstants.kAddPersonalReferee,
                        ref,
                        context,
                      ),
                      buildReferenceCheckIconDiv(
                        size,
                        RefCheckScreenIcon.icon4,
                        AppConstants.kAddSelfEmploymentReferee,
                        ref,
                        context,
                      ),
                      buildReferenceCheckIconDiv(
                        size,
                        RefCheckScreenIcon.icon5,
                        AppConstants.kAddEmploymentGap,
                        ref,
                        context,
                      ),
                    ];
                    return items[index];
                  },
                ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final Size size = MediaQuery.of(context).size;
    final selectedScreen = ref.watch(selectedScreenProvider);

    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 10.0),
          child: Column(
            spacing: 10,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                spacing: 10,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  if (selectedScreen != null)
                    IconButton(
                      icon: Icon(
                        Icons.arrow_back_outlined,
                        color: AppColors.kBlueColor,
                      ),
                      onPressed: () {
                        ref.read(selectedScreenProvider.notifier).state = null;
                      },
                    ),
                  Text(
                    'Reference Check',
                    style: TextStyle(
                      fontSize: 20.0,
                      fontWeight: FontWeight.bold,
                      color: AppColors.kBlueColor,
                    ),
                  ),
                ],
              ),
              const ReferenceCheckHeaderCard(),
              SizedBox(
                height: selectedScreen == null ? null : size.height * 1,
                child: selectedScreen == null
                    ? _buildIconMenu(size, ref, context)
                    : _buildSelectedScreen(selectedScreen),
              ),
              if (selectedScreen == null) ...[
                SizedBox(height: size.height * 0.1),
                Align(
                  alignment: Alignment.center,
                  child: GestureDetector(
                    onTap: () {
                      ref.read(selectedScreenProvider.notifier).state =
                          AppConstants.kDeclarationForm;
                    },
                    child: Container(
                      height: 50.0,
                      width: 200,
                      decoration: BoxDecoration(
                        color: const Color(0XFFB6B5B5),
                        borderRadius: BorderRadius.circular(10.0),
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        'Consent & Submit',
                        style: TextStyle(
                          color: AppColors.activityLogContentCircleColor,
                          fontSize: 20.0,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
