import 'package:SolidCheck/core/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:responsive_framework/responsive_framework.dart';

class ReferenceCheckFormScreen extends StatelessWidget {
  const ReferenceCheckFormScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final isTab = ResponsiveBreakpoints.of(context).largerThan(TABLET);

    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: isTab ? 50.0 : 20.0,
            vertical: isTab ? 30.0 : 20.0,
          ),
          child: Center(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(16.0),
                  width: isTab
                      ? MediaQuery.of(context).size.width * 0.7
                      : MediaQuery.of(context).size.width * 0.9,
                  decoration: BoxDecoration(
                    color: AppColors.kQuestionContainerColor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // SvgPicture.asset(
                      //   'assets/logos/solidcheck-logo-horizontal.svg',
                      //   height: 46,
                      //   width: 200,
                      //   semanticsLabel: 'logo',
                      //   placeholderBuilder: (BuildContext context) =>
                      //       CircularProgressIndicator(),
                      // ),
                      Center(
                        child: Text(
                          'Reference Check Form',
                          style: TextStyle(
                            fontSize: isTab ? 48 : 18,
                            fontWeight: FontWeight.w600,
                            color: AppColors.kBlueColor,
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      Center(
                        child: SizedBox(
                          width: isTab
                              ? MediaQuery.of(context).size.width * 0.5
                              : MediaQuery.of(context).size.width * 0.9,
                          child: Text(
                            'We would appreciate it if you could answer a few questions to provide insights into [Applicant’s Name]’s skills, work ethic, and overall character. The information you provide will remain confidential and will only be used for evaluation purposes.',
                            style: TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w400,
                              color: AppColors.kBlackColor,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                Container(
                  padding: const EdgeInsets.all(20.0),
                  width: isTab
                      ? MediaQuery.of(context).size.width * 0.7
                      : MediaQuery.of(context).size.width * 0.9,
                  decoration: BoxDecoration(
                    color: AppColors.kQuestionContainerColor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      FormHeading(isTab: isTab, title: 'About The Candidate'),
                      const SizedBox(height: 16),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  'Name:',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontSize: isTab ? 14 : 11,
                                    color: AppColors.kBlueColor,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  'Applying for job:',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontSize: isTab ? 14 : 11,
                                    color: AppColors.kBlueColor,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  'Company:',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontSize: isTab ? 14 : 11,
                                    color: AppColors.kBlueColor,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  'John Smith',
                                  style: TextStyle(
                                    fontSize: isTab ? 19 : 14,
                                    fontWeight: FontWeight.w500,
                                    color: AppColors.kBlackColor,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  'IT Manager',
                                  style: TextStyle(
                                    fontSize: isTab ? 19 : 14,
                                    fontWeight: FontWeight.w500,
                                    color: AppColors.kBlackColor,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  'Big Company',
                                  style: TextStyle(
                                    fontSize: isTab ? 19 : 14,
                                    fontWeight: FontWeight.w500,
                                    color: AppColors.kBlackColor,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      FormHeading(isTab: isTab, title: 'About The Referee'),
                      const SizedBox(height: 16),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  'Name:',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontSize: isTab ? 14 : 11,
                                    color: AppColors.kBlueColor,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  'Job Title:',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontSize: isTab ? 14 : 11,
                                    color: AppColors.kBlueColor,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  'Company:',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontSize: isTab ? 14 : 11,
                                    color: AppColors.kBlueColor,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  'Ronald Weasely',
                                  style: TextStyle(
                                    fontSize: isTab ? 19 : 14,
                                    fontWeight: FontWeight.w500,
                                    color: AppColors.kBlackColor,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  'HR Manager',
                                  style: TextStyle(
                                    fontSize: isTab ? 19 : 14,
                                    fontWeight: FontWeight.w500,
                                    color: AppColors.kBlackColor,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Text(
                                  'IT Company',
                                  style: TextStyle(
                                    fontSize: isTab ? 19 : 14,
                                    fontWeight: FontWeight.w500,
                                    color: AppColors.kBlackColor,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      FormHeading(
                        isTab: isTab,
                        title: 'Questions About The Candidate',
                      ),
                      const SizedBox(height: 16),
                      QuestionField(
                        isTab: isTab,
                        question: 'How long have you known the candidate?',
                      ),
                      const SizedBox(height: 16),
                      QuestionField(
                        isTab: isTab,
                        question:
                            'How was your experience working with the candidate?',
                      ),
                      const SizedBox(height: 16),
                      QuestionField(
                        isTab: isTab,
                        question:
                            'How was your experience working with the candidate?',
                      ),
                      const SizedBox(height: 16),
                      Align(
                        alignment: Alignment.center,
                        child: GestureDetector(
                          onTap: () {},
                          child: Container(
                            height: 50.0,
                            width: 200,
                            decoration: BoxDecoration(
                              color: AppColors.kBlueColor,
                              borderRadius: BorderRadius.circular(10.0),
                            ),
                            alignment: Alignment.center,
                            child: Text(
                              'Submit',
                              style: TextStyle(
                                color: AppColors.activityLogContentCircleColor,
                                fontSize: 20.0,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class QuestionField extends StatelessWidget {
  const QuestionField({super.key, required this.isTab, required this.question});

  final bool isTab;
  final String question;

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 5,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          question,
          style: TextStyle(
            fontSize: isTab ? 15 : 12,
            fontWeight: FontWeight.w500,
            color: AppColors.kBlackColor,
          ),
        ),
        TextField(
          maxLines: 3,
          decoration: InputDecoration(
            labelStyle: TextStyle(
              fontSize: isTab ? 14 : 11,
              color: AppColors.kBlueColor,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: AppColors.kBlueColor,
                width: 1.5,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class FormHeading extends StatelessWidget {
  const FormHeading({super.key, required this.isTab, required this.title});

  final bool isTab;
  final String title;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: isTab ? 23 : 16,
            fontWeight: FontWeight.w600,
            color: AppColors.kBlueColor,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Divider(color: AppColors.kBlueColor, thickness: 1.5),
        ),
      ],
    );
  }
}
