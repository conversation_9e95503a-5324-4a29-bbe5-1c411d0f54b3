import 'package:SolidCheck/core/constants/constants.dart';
import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/reference_check/views/screens/main_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:responsive_framework/responsive_framework.dart';

final currentScreenIndexProvider = StateProvider<int>((ref) => 0);

class ReferenceCheckInitialScreen extends ConsumerWidget {
  const ReferenceCheckInitialScreen({super.key});

  Widget _replaceScreen(BuildContext context, int index, WidgetRef ref) {
    switch (index) {
      case 0:
        return _buildMainScreen(context, ref);
      case 1:
        return const RefCheckMainScreen();
      default:
        return _buildMainScreen(context, ref);
    }
  }

  Widget _buildMainScreen(BuildContext context, WidgetRef ref) {
    final Size size = MediaQuery.of(context).size;
    final responsiveBreakpoint = ResponsiveBreakpoints.of(
      context,
    ).largerThan(TABLET);

    return SizedBox(
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(
              child: Center(
                child: CircleAvatar(
                  backgroundColor: AppColors.transparentColor,
                  radius: responsiveBreakpoint ? 100.0 : 80.0,
                  //   child: SvgPicture.asset(AppConstants.errorSignForDbsSvg),
                ),
              ),
            ),
            SizedBox(
              child: Center(
                child: Text(
                  'Reference Check Application Form',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: responsiveBreakpoint ? 25.0 : 20.0,
                    color: AppColors.kBlueColor,
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 10.0),
            SizedBox(
              child: Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: responsiveBreakpoint ? 100.0 : 50.0,
                  ),
                  child: Text(
                    AppConstants.refCheckInitScreenText,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: responsiveBreakpoint ? 17.0 : 15.0,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20.0),
            SizedBox(
              child: MaterialButton(
                height: responsiveBreakpoint ? 60.0 : 50.0,
                minWidth: responsiveBreakpoint
                    ? size.width * 0.15
                    : size.width * 0.50,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10.0),
                ),
                color: AppColors.kBlueColor,
                child: Text(
                  'Start',
                  style: TextStyle(
                    color: AppColors.kWhiteColor,
                    fontSize: 17.0,
                  ),
                ),
                onPressed: () {
                  _replaceScreenWithStateManagement(context, ref);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _replaceScreenWithStateManagement(BuildContext context, WidgetRef ref) {
    ref.read(currentScreenIndexProvider.notifier).state = 1;
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final Size size = MediaQuery.of(context).size;
    final int currentIndex = ref.watch(currentScreenIndexProvider);

    return SizedBox(
      height: size.height,
      width: size.width,
      child: Center(child: _replaceScreen(context, currentIndex, ref)),
    );
  }
}
