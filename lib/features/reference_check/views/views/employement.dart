import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_build_text_field.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_form_center_title_div.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_form_text_title_tile.dart';

import 'package:SolidCheck/features/reference_check/widgets/employement.dart';
import 'package:SolidCheck/features/reference_check/widgets/view_button.dart';
import 'package:SolidCheck/shared/utils/validations.dart';
import 'package:SolidCheck/shared/widgets/date_picker_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:responsive_framework/responsive_framework.dart';

class EmployementReferenceScreen extends ConsumerStatefulWidget {
  const EmployementReferenceScreen({super.key});

  @override
  EmployementReferenceScreenState createState() =>
      EmployementReferenceScreenState();
}

class EmployementReferenceScreenState
    extends ConsumerState<EmployementReferenceScreen> {
  final List<List<TextEditingController>> _referenceControllers = [];

  @override
  void dispose() {
    for (var controllers in _referenceControllers) {
      for (var controller in controllers) {
        controller.dispose();
      }
    }
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _addReference();
  }

  void _addReference() {
    setState(() {
      _referenceControllers.add(
        List.generate(12, (_) => TextEditingController()),
      );
    });
  }

  Widget _buildWebLayOut(int index) {
    return Container(
      padding: const EdgeInsets.all(10.0),
      decoration: BoxDecoration(
        color: AppColors.refCheckDivColor,
        borderRadius: BorderRadius.circular(10.0),
      ),
      child: Column(
        spacing: 10,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const DBSFormCenterTitleDiv(),
          const DBSFormTextTitleTile(title: 'Applicant\'s Information'),
          Row(
            children: [
              Expanded(
                child: buildTextField(
                  controller: _referenceControllers[index][0],
                  title: 'Previous Company Name:',
                  isFieldRequired: true,
                  validator: validateRequired,
                ),
              ),
              const SizedBox(width: 20.0),
              Expanded(
                child: buildTextField(
                  controller: _referenceControllers[index][1],
                  title: 'Previous Job Role:',
                  isFieldRequired: true,
                  validator: validateRequired,
                ),
              ),
            ],
          ),
          Row(
            children: [
              Expanded(
                child: DatePickerField(
                  dayController: _referenceControllers[index][2],
                  monthController: _referenceControllers[index][3],
                  yearController: _referenceControllers[index][4],
                  prefixIcon: Icons.calendar_month,
                  title: 'Employed From',
                  isFieldRequired: true,
                  dayValidator: validateRequired,
                  monthValidator: validateRequired,
                  yearValidator: validateRequired,
                ),
              ),
              const SizedBox(width: 20.0),
              Expanded(
                child: DatePickerField(
                  dayController: _referenceControllers[index][5],
                  monthController: _referenceControllers[index][6],
                  yearController: _referenceControllers[index][7],
                  prefixIcon: Icons.calendar_month,
                  title: 'Employed Till',
                  isFieldRequired: false,
                  dayValidator: validateRequired,
                  monthValidator: validateRequired,
                  yearValidator: validateRequired,
                  isCheckBoxRequired: true,
                  checkBoxTitle: 'Still Employed here',
                ),
              ),
            ],
          ),
          const DBSFormTextTitleTile(title: 'Referee\'s Information'),
          Row(
            children: [
              Expanded(
                child: buildTextField(
                  controller: _referenceControllers[index][8],
                  title: 'Referee Name:',
                  isFieldRequired: true,
                  validator: validateRequired,
                ),
              ),
              const SizedBox(width: 20.0),
              Expanded(
                child: buildTextField(
                  controller: _referenceControllers[index][9],
                  title: 'Referee Job Role:',
                  isFieldRequired: true,
                  validator: validateRequired,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final responsive = ResponsiveBreakpoints.of(context).largerThan(TABLET);

    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.only(bottom: 20.0, top: 5.0),
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  'Add Employment Reference',
                  style: TextStyle(
                    color: AppColors.kBlueColor,
                    fontSize: 20.0,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            for (int i = 0; i < _referenceControllers.length; i++)
              responsive
                  ? _buildWebLayOut(i)
                  : ReferenceCheckEmployementView(
                      referenceControllers: _referenceControllers,
                    ),
            const SizedBox(height: 15.0),
            responsive
                ? Padding(
                    padding: const EdgeInsets.only(right: 30.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        ReferenceCheckViewButton(
                          title: '+ Add another Employment Reference',
                          onPressed: _addReference,
                          isSavedButton: false,
                        ),
                        const SizedBox(width: 10.0),
                        ReferenceCheckViewButton(
                          title: 'Save',
                          onPressed: () {},
                          isSavedButton: true,
                        ),
                      ],
                    ),
                  )
                : Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10.0),
                    child: Column(
                      children: [
                        ReferenceCheckViewButton(
                          title: '+ Add another Employment Reference',
                          onPressed: _addReference,
                          isSavedButton: false,
                        ),
                        const SizedBox(height: 10.0),
                        ReferenceCheckViewButton(
                          title: 'Save',
                          onPressed: () {},
                          isSavedButton: true,
                        ),
                      ],
                    ),
                  ),
            const SizedBox(height: 10.0),
          ],
        ),
      ),
    );
  }
}
