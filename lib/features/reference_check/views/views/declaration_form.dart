import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/reference_check/views/screens/reference_check_completed.dart';
import 'package:SolidCheck/shared/widgets/custom_check_box.dart';
import 'package:flutter/material.dart';

class DeclarationForm extends StatefulWidget {
  const DeclarationForm({super.key});

  @override
  State<DeclarationForm> createState() => _DeclarationFormState();
}

class _DeclarationFormState extends State<DeclarationForm> {
  bool noMoreReferences = false;
  bool giveConsent = false;

  bool get isFormValid => noMoreReferences && giveConsent;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Declaration',
            style: TextStyle(
              fontSize: 23,
              fontWeight: FontWeight.w600,
              color: AppColors.kBlueColor,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.kTaskTileColor,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                'The applicant must have completed this declaration and given consent for the check. '
                'If you are completing this form on behalf of or with the applicant, please ensure that '
                'you have obtained this consent or ask them to complete this section.',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppColors.kQuestionTextColor,
                ),
              ),
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.kTaskTileColor,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                'Any details saved or submitted may be used to progress the reference application as requested by your organisation.',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: AppColors.kQuestionTextColor,
                ),
              ),
            ),
          ),
          const SizedBox(height: 24),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.applicationOverviewDivColor,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              spacing: 20,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Text(
                    'Please check both boxes to continue',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppColors.kQuestionTextColor,
                    ),
                  ),
                ),
                CustomCheckBoxMain(
                  value: noMoreReferences,
                  onChanged: (value) {
                    setState(() {
                      noMoreReferences = value ?? false;
                    });
                  },
                  title: 'I cannot provide any more references',
                ),
                CustomCheckBoxMain(
                  value: giveConsent,
                  onChanged: (value) {
                    setState(() {
                      giveConsent = value ?? false;
                    });
                  },
                  title:
                      'I hear by consent to reference information being released to SolidCheck for the purposes of a preemployment check. Information may be transferred to SolidCheck via email, letter or the solid check portal at that convenience of the person providing the information',
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          Align(
            alignment: Alignment.center,
            child: GestureDetector(
              onTap: isFormValid
                  ? () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const ReferenceCheckCompleted(),
                        ),
                      );
                    }
                  : null,
              child: Container(
                height: 50.0,
                width: 200,
                decoration: BoxDecoration(
                  color: isFormValid
                      ? AppColors.kBlueColor
                      : AppColors.hintTextLoginFieldColor,
                  borderRadius: BorderRadius.circular(10.0),
                ),
                alignment: Alignment.center,
                child: Text(
                  'Submit',
                  style: TextStyle(
                    color: AppColors.activityLogContentCircleColor,
                    fontSize: 20.0,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
