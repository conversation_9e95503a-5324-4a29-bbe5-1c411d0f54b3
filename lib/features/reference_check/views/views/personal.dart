import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_build_text_field.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_form_center_title_div.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_form_text_title_tile.dart';
import 'package:SolidCheck/features/reference_check/widgets/personal.dart';
import 'package:SolidCheck/features/reference_check/widgets/view_button.dart';
import 'package:SolidCheck/shared/utils/validations.dart';
import 'package:SolidCheck/shared/widgets/date_picker_field.dart';
import 'package:SolidCheck/shared/widgets/drop_down.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:responsive_framework/responsive_framework.dart';

class PersonalReferenceScreen extends ConsumerStatefulWidget {
  const PersonalReferenceScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _PersonalReferenceScreenState();
}

class _PersonalReferenceScreenState
    extends ConsumerState<PersonalReferenceScreen> {
  final List<List<TextEditingController>> _referenceControllers = [];

  @override
  void dispose() {
    for (var controllers in _referenceControllers) {
      for (var controller in controllers) {
        controller.dispose();
      }
    }
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _addNewReference();
  }

  Widget _buildWebLayOut(List<TextEditingController> controllers) {
    return Container(
      padding: const EdgeInsets.all(10.0),
      decoration: BoxDecoration(
        color: AppColors.refCheckDivColor,
        borderRadius: BorderRadius.circular(10.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const DBSFormCenterTitleDiv(),
          const DBSFormTextTitleTile(title: 'Referee\'s Information'),
          const SizedBox(height: 10.0),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            spacing: 20.0,
            children: [
              Expanded(
                child: buildTextField(
                  controller: controllers[0],
                  title: 'Referee Name:',
                  isFieldRequired: true,
                  validator: validateRequired,
                ),
              ),
              Expanded(
                child: CustomDropdownButton2(
                  title: 'Referee Profession:',
                  dropdownItems: const [
                    DropdownMenuItem(
                      value: 'Option 1',
                      child: Text('Option 1'),
                    ),
                  ],
                  onChanged: (value) {},
                  hint: 'Please select an organization type',
                  isRquired: true,
                ),
              ),
            ],
          ),
          Row(
            spacing: 20.0,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: buildTextField(
                  controller: controllers[2],
                  title: 'Relation with the referee:',
                  isFieldRequired: true,
                  validator: validateRequired,
                ),
              ),
              Expanded(
                child: DatePickerField(
                  dayController: controllers[3],
                  monthController: controllers[4],
                  yearController: controllers[5],
                  prefixIcon: Icons.calendar_month,
                  title: 'Known Since:',
                  isFieldRequired: true,
                  dayValidator: validateRequired,
                  monthValidator: validateRequired,
                  yearValidator: validateRequired,
                ),
              ),
            ],
          ),
          Row(
            spacing: 20,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: buildTextField(
                  controller: controllers[6],
                  title: 'Referee Contact Email:',
                  isFieldRequired: true,
                  validator: validateRequired,
                ),
              ),
              Expanded(
                child: buildTextField(
                  controller: controllers[7],
                  title: 'Referee Contact Number:',
                  isFieldRequired: true,
                  validator: validateRequired,
                ),
              ),
            ],
          ),
          Row(
            children: [
              Expanded(
                child: buildTextField(
                  controller: controllers[8],
                  title: 'Comments:',
                  isFieldRequired: false,
                  validator: validateRequired,
                  isCommentFieldRequired: true,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _addNewReference() {
    setState(() {
      _referenceControllers.add(
        List.generate(9, (_) => TextEditingController()),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    final responsive = ResponsiveBreakpoints.of(context).largerThan(TABLET);

    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          spacing: 10,
          children: [
            Container(
              padding: const EdgeInsets.only(bottom: 20.0, top: 5.0),
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  'Add Personal Referee',
                  style: TextStyle(
                    color: AppColors.kBlueColor,
                    fontSize: 20.0,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            for (var fieldControllers in _referenceControllers)
              responsive
                  ? _buildWebLayOut(fieldControllers)
                  : ReferenceCheckPersonalView(controllers: fieldControllers),
            responsive
                ? Row(
                    spacing: 20,
                    mainAxisAlignment: MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      ReferenceCheckViewButton(
                        title: '+ Add another Referee',
                        onPressed: _addNewReference,
                        isSavedButton: false,
                      ),
                      ReferenceCheckViewButton(
                        title: 'Save',
                        onPressed: () {},
                        isSavedButton: true,
                      ),
                    ],
                  )
                : Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10.0),
                    child: Column(
                      spacing: 10,
                      children: [
                        ReferenceCheckViewButton(
                          title: '+ Add another Referee',
                          onPressed: _addNewReference,
                          isSavedButton: false,
                        ),
                        ReferenceCheckViewButton(
                          title: 'Save',
                          onPressed: () {},
                          isSavedButton: true,
                        ),
                      ],
                    ),
                  ),
            const SizedBox(height: 10.0),
          ],
        ),
      ),
    );
  }
}
