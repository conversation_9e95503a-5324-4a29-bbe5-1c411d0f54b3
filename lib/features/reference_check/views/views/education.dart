import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_build_text_field.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_form_center_title_div.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_form_text_title_tile.dart';
import 'package:SolidCheck/features/reference_check/widgets/education.dart';
import 'package:SolidCheck/features/reference_check/widgets/view_button.dart';
import 'package:SolidCheck/shared/utils/validations.dart';
import 'package:SolidCheck/shared/widgets/date_picker_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:responsive_framework/responsive_framework.dart';

class EducationReferenceScreen extends ConsumerStatefulWidget {
  const EducationReferenceScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _EducationReferenceScreenState();
}

class _EducationReferenceScreenState
    extends ConsumerState<EducationReferenceScreen> {
  List<List<TextEditingController>> controllersList = [];
  final List<bool> _stillEnrolledList = [];
  int formCount = 1;

  @override
  void initState() {
    super.initState();
    controllersList.add(_createControllers());
    _stillEnrolledList.add(false);
  }

  List<TextEditingController> _createControllers() {
    return [
      TextEditingController(),
      TextEditingController(),
      TextEditingController(),
      TextEditingController(),
      TextEditingController(),
      TextEditingController(),
      TextEditingController(),
      TextEditingController(),
      TextEditingController(),
      TextEditingController(),
      TextEditingController(),
    ];
  }

  Widget _buildWebLayOut(int index) {
    return Container(
      padding: const EdgeInsets.all(10.0),
      decoration: BoxDecoration(
        color: AppColors.refCheckDivColor,
        borderRadius: BorderRadius.circular(10.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const DBSFormCenterTitleDiv(),
          const DBSFormTextTitleTile(title: 'Applicant\'s Information'),
          const SizedBox(height: 10.0),
          Row(
            children: [
              Expanded(
                child: buildTextField(
                  controller: controllersList[index][0],
                  title: 'Education Institute’s name:',
                  isFieldRequired: true,
                  validator: validateRequired,
                ),
              ),
              const SizedBox(width: 20.0),
              const Expanded(child: SizedBox()),
              const SizedBox(width: 20.0),
            ],
          ),
          const SizedBox(height: 10.0),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: DatePickerField(
                  dayController: controllersList[index][1],
                  monthController: controllersList[index][2],
                  yearController: controllersList[index][3],
                  prefixIcon: Icons.calendar_month,
                  title: 'Enrollment from:',
                  isFieldRequired: true,
                  dayValidator: validateRequired,
                  monthValidator: validateRequired,
                  yearValidator: validateRequired,
                ),
              ),
              const SizedBox(width: 20.0),
              Expanded(
                child: DatePickerField(
                  dayController: controllersList[index][4],
                  monthController: controllersList[index][5],
                  yearController: controllersList[index][6],
                  prefixIcon: Icons.calendar_month,
                  title: 'Enrollment Till:',
                  isFieldRequired: !_stillEnrolledList[index],
                  dayValidator: validateRequired,
                  monthValidator: validateRequired,
                  yearValidator: validateRequired,
                  isCheckBoxRequired: true,
                  checkBoxTitle: 'Still Enrolled here',
                  isChecked: _stillEnrolledList[index],
                  onCheckedChanged: (value) {
                    setState(() {
                      _stillEnrolledList[index] = value;
                    });
                  },
                ),
              ),
              const SizedBox(width: 20.0),
            ],
          ),
          const SizedBox(height: 10.0),
          const DBSFormTextTitleTile(title: 'Referee\'s Information'),
          const SizedBox(height: 10.0),
          Row(
            children: [
              Expanded(
                child: buildTextField(
                  controller: controllersList[index][7],
                  title: 'Referee Name:',
                  isFieldRequired: true,
                  validator: validateRequired,
                ),
              ),
              const SizedBox(width: 20.0),
              Expanded(
                child: buildTextField(
                  controller: controllersList[index][8],
                  title: 'Referee Job Role:',
                  isFieldRequired: true,
                  validator: validateRequired,
                ),
              ),
              const SizedBox(width: 20.0),
            ],
          ),
          const SizedBox(height: 10.0),
          Row(
            children: [
              Expanded(
                child: buildTextField(
                  controller: controllersList[index][9],
                  title: 'Referee Contact Email',
                  isFieldRequired: true,
                  validator: validateRequired,
                ),
              ),
              const SizedBox(width: 20.0),
              Expanded(
                child: buildTextField(
                  controller: controllersList[index][10],
                  title: 'Referee Contact Number',
                  isFieldRequired: true,
                  validator: validateRequired,
                ),
              ),
              const SizedBox(width: 20.0),
            ],
          ),
        ],
      ),
    );
  }

  void _addReferenceForm() {
    setState(() {
      formCount++;
      controllersList.add(_createControllers());
      _stillEnrolledList.add(false);
    });
  }

  @override
  Widget build(BuildContext context) {
    final responsive = ResponsiveBreakpoints.of(context).largerThan(TABLET);

    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.only(bottom: 20.0, top: 5.0),
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  'Add Education Referee',
                  style: TextStyle(
                    color: AppColors.kBlueColor,
                    fontSize: 20.0,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            for (int i = 0; i < formCount; i++)
              responsive
                  ? _buildWebLayOut(i)
                  : ReferenceCheckEducationView(
                      controllers: controllersList[i],
                    ),
            const SizedBox(height: 15.0),
            responsive
                ? Row(
                    spacing: 20,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      ReferenceCheckViewButton(
                        title: '+ Add another Education Reference',
                        onPressed: _addReferenceForm,
                        isSavedButton: false,
                      ),
                      const SizedBox(width: 10.0),
                      ReferenceCheckViewButton(
                        title: 'Save',
                        onPressed: () {},
                        isSavedButton: true,
                      ),
                    ],
                  )
                : Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10.0),
                    child: Column(
                      children: [
                        ReferenceCheckViewButton(
                          title: '+ Add another Education Reference',
                          onPressed: _addReferenceForm,
                          isSavedButton: false,
                        ),
                        const SizedBox(height: 10.0),
                        ReferenceCheckViewButton(
                          title: 'Save',
                          onPressed: () {},
                          isSavedButton: true,
                        ),
                      ],
                    ),
                  ),
            const SizedBox(height: 10.0),
          ],
        ),
      ),
    );
  }
}
