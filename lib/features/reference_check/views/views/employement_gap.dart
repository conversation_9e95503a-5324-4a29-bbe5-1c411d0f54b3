import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_build_text_field.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_form_center_title_div.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_form_text_title_tile.dart';
import 'package:SolidCheck/features/reference_check/widgets/employement_gap.dart';
import 'package:SolidCheck/features/reference_check/widgets/view_button.dart';
import 'package:SolidCheck/shared/widgets/date_picker_field.dart';
import 'package:SolidCheck/shared/widgets/drop_down.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:responsive_framework/responsive_framework.dart';

class EmploymentGapReferenceScreen extends ConsumerStatefulWidget {
  const EmploymentGapReferenceScreen({super.key});

  @override
  EmploymentGapReferenceScreenState createState() =>
      EmploymentGapReferenceScreenState();
}

class EmploymentGapReferenceScreenState
    extends ConsumerState<EmploymentGapReferenceScreen> {
  final List<Map<String, TextEditingController>> _gaps = [
    {
      'enrollmentDay': TextEditingController(),
      'enrollmentMonth': TextEditingController(),
      'enrollmentYear': TextEditingController(),
      'enrollmentTillDay': TextEditingController(),
      'enrollmentTillMonth': TextEditingController(),
      'enrollmentTillYear': TextEditingController(),
      'gapReason': TextEditingController(),
      'gapType': TextEditingController(),
    },
  ];

  @override
  void dispose() {
    for (var gap in _gaps) {
      for (var controller in gap.values) {
        controller.dispose();
      }
    }

    super.dispose();
  }

  String? _validateRequiredField(String? value) {
    return value == null || value.isEmpty ? 'This field is required' : null;
  }

  String? _validateDate(String? day, String? month, String? year) {
    if (day == null ||
        month == null ||
        year == null ||
        day.isEmpty ||
        month.isEmpty ||
        year.isEmpty) {
      return 'Date is required';
    }
    if (int.tryParse(day) == null ||
        int.tryParse(month) == null ||
        int.tryParse(year) == null) {
      return 'Invalid date format';
    }
    return null;
  }

  Widget _buildWebLayOut(Map<String, TextEditingController> gap) {
    return Container(
      padding: const EdgeInsets.all(10.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.0),
        color: AppColors.refCheckDivColor,
      ),
      child: SingleChildScrollView(
        child: Column(
          spacing: 10,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const DBSFormCenterTitleDiv(),
            const DBSFormTextTitleTile(title: 'Referee\'s Information'),
            Row(
              children: [
                SizedBox(
                  width: MediaQuery.of(context).size.width * 0.5,
                  child: CustomDropdownButton2(
                    isRquired: true,
                    title: 'Gap Type:',
                    dropdownItems: const [
                      DropdownMenuItem(
                        value: 'value1',
                        child: Text('Option 1'),
                      ),
                      DropdownMenuItem(
                        value: 'value2',
                        child: Text('Option 2'),
                      ),
                      DropdownMenuItem(
                        value: 'value3',
                        child: Text('Option 3'),
                      ),
                    ],
                    onChanged: (v) {},
                    hint: 'Please select a gap type',
                  ),
                ),
              ],
            ),
            Row(
              spacing: 60,
              children: [
                DatePickerField(
                  dayController: gap['enrollmentDay']!,
                  monthController: gap['enrollmentMonth']!,
                  yearController: gap['enrollmentYear']!,
                  prefixIcon: Icons.date_range,
                  title: 'From:',
                  isFieldRequired: true,
                  dayValidator: (day) => _validateDate(
                    day,
                    gap['enrollmentMonth']!.text,
                    gap['enrollmentYear']!.text,
                  ),
                  monthValidator: (month) => _validateDate(
                    gap['enrollmentDay']!.text,
                    month,
                    gap['enrollmentYear']!.text,
                  ),
                  yearValidator: (year) => _validateDate(
                    gap['enrollmentDay']!.text,
                    gap['enrollmentMonth']!.text,
                    year,
                  ),
                ),
                DatePickerField(
                  dayController: gap['enrollmentTillDay']!,
                  monthController: gap['enrollmentTillMonth']!,
                  yearController: gap['enrollmentTillYear']!,
                  prefixIcon: Icons.date_range,
                  title: 'Till:',
                  isFieldRequired: true,
                  dayValidator: null,
                  monthValidator: null,
                  yearValidator: null,
                ),
              ],
            ),
            Row(
              children: [
                Expanded(
                  child: buildTextField(
                    controller: gap['gapReason']!,
                    title: 'Employment Gap Reason:',
                    isFieldRequired: true,
                    validator: _validateRequiredField,
                    isCommentFieldRequired: true,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _addGap() {
    setState(() {
      _gaps.add({
        'enrollmentDay': TextEditingController(),
        'enrollmentMonth': TextEditingController(),
        'enrollmentYear': TextEditingController(),
        'enrollmentTillDay': TextEditingController(),
        'enrollmentTillMonth': TextEditingController(),
        'enrollmentTillYear': TextEditingController(),
        'gapReason': TextEditingController(),
        'gapType': TextEditingController(),
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final res = ResponsiveBreakpoints.of(context).largerThan(TABLET);

    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.only(bottom: 20.0, top: 5.0),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    'Add Employment Gap',
                    style: TextStyle(
                      color: AppColors.kBlueColor,
                      fontSize: 20.0,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              ..._gaps.map(
                (gap) => res
                    ? _buildWebLayOut(gap)
                    : ReferenceCheckEmployementGapView(gap: gap),
              ),
              const SizedBox(height: 15.0),
              res
                  ? Row(
                      spacing: 20,
                      mainAxisAlignment: MainAxisAlignment.end,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        ReferenceCheckViewButton(
                          title: '+ Add another Employment Gap Reference',
                          onPressed: _addGap,
                          isSavedButton: false,
                        ),
                        ReferenceCheckViewButton(
                          title: 'Save',
                          onPressed: () {},
                          isSavedButton: true,
                        ),
                      ],
                    )
                  : Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          ReferenceCheckViewButton(
                            title: '+ Add another Employment Gap Reference',
                            onPressed: _addGap,
                            isSavedButton: false,
                          ),
                          const SizedBox(height: 10.0),
                          ReferenceCheckViewButton(
                            title: 'Save',
                            onPressed: () {},
                            isSavedButton: true,
                          ),
                        ],
                      ),
                    ),
              const SizedBox(height: 10.0),
            ],
          ),
        ),
      ),
    );
  }
}
