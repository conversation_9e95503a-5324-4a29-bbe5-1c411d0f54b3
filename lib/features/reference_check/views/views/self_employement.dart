import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_build_text_field.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_form_center_title_div.dart';
import 'package:SolidCheck/features/reference_check/widgets/employement_gap.dart';
import 'package:SolidCheck/features/reference_check/widgets/view_button.dart';
import 'package:SolidCheck/shared/utils/validations.dart';
import 'package:SolidCheck/shared/widgets/date_picker_field.dart';
import 'package:SolidCheck/shared/widgets/drop_down.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:responsive_framework/responsive_framework.dart';

class SelfEmploymentReferenceScreen extends ConsumerStatefulWidget {
  const SelfEmploymentReferenceScreen({super.key});

  @override
  SelfEmploymentReferenceScreenState createState() =>
      SelfEmploymentReferenceScreenState();
}

class SelfEmploymentReferenceScreenState
    extends ConsumerState<SelfEmploymentReferenceScreen> {
  final List<Map<String, TextEditingController>> _controllers = [
    {
      'activitiesUndertaken': TextEditingController(),
      'organizationType': TextEditingController(),
      'refereeName': TextEditingController(),
      'selfEmployedFromDay': TextEditingController(),
      'selfEmployedFromMonth': TextEditingController(),
      'selfEmployedFromYear': TextEditingController(),
      'selfEmployedTillDay': TextEditingController(),
      'selfEmployedTillMonth': TextEditingController(),
      'selfEmployedTillYear': TextEditingController(),
      'verificationDetails': TextEditingController(),
    },
  ];

  @override
  void dispose() {
    for (var map in _controllers) {
      for (var controller in map.values) {
        controller.dispose();
      }
    }

    super.dispose();
  }

  Widget _buildField<T>({
    required String key,
    required Widget Function(
      BuildContext context,
      TextEditingController controller,
    )
    builder,
  }) {
    final controller = _controllers[0][key];
    if (controller != null) {
      return builder(context, controller);
    } else {
      return const SizedBox.shrink();
    }
  }

  Widget _buildContentDiv(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(10.0),
      decoration: BoxDecoration(
        color: AppColors.refCheckDivColor,
        borderRadius: BorderRadius.circular(10.0),
      ),
      child: Column(
        spacing: 10,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const DBSFormCenterTitleDiv(),
          const Text(
            'Referee\'s Information',
            style: TextStyle(fontSize: 16.0, fontWeight: FontWeight.bold),
          ),
          Row(
            spacing: 20,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: _buildField(
                  key: 'refereeName',
                  builder: (context, controller) => buildTextField(
                    controller: controller,
                    title: 'Referee Name:',
                    isFieldRequired: true,
                    validator: validateRequired,
                    isCommentFieldRequired: false,
                  ),
                ),
              ),
              Expanded(
                child: CustomDropdownButton2(
                  isRquired: true,
                  title: 'Organization Type:',
                  dropdownItems: const [
                    DropdownMenuItem(value: 'value1', child: Text('Option 1')),
                    DropdownMenuItem(value: 'value2', child: Text('Option 2')),
                    DropdownMenuItem(value: 'value3', child: Text('Option 3')),
                  ],
                  onChanged: (value) {},
                  hint: 'Select an organization type',
                ),
              ),
            ],
          ),
          Row(
            spacing: 20,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: _buildDatePicker(
                  'selfEmployedFrom',
                  'Self-Employed From:',
                  context,
                  false,
                ),
              ),
              Expanded(
                child: _buildDatePicker(
                  'selfEmployedTill',
                  'Self-Employed Till:',
                  context,
                  true,
                ),
              ),
            ],
          ),
          Row(
            spacing: 20,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: _buildField(
                  key: 'activitiesUndertaken',
                  builder: (context, controller) => buildTextField(
                    controller: controller,
                    title: 'Activities Undertaken:',
                    isFieldRequired: false,
                    validator: validateRequired,
                    isCommentFieldRequired: true,
                  ),
                ),
              ),
              Expanded(
                child: _buildField(
                  key: 'verificationDetails',
                  builder: (context, controller) => buildTextField(
                    controller: controller,
                    title: 'Verification Details:',
                    isFieldRequired: false,
                    validator: validateRequired,
                    isCommentFieldRequired: true,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDatePicker(
    String prefix,
    String title,
    BuildContext context,
    bool isRequired,
  ) {
    return DatePickerField(
      dayController: _controllers[0]['${prefix}Day']!,
      monthController: _controllers[0]['${prefix}Month']!,
      yearController: _controllers[0]['${prefix}Year']!,
      prefixIcon: Icons.date_range,
      title: title,
      isFieldRequired: true,
      dayValidator: (day) => validateDate(
        day,
        _controllers[0]['${prefix}Month']!.text,
        _controllers[0]['${prefix}Year']!.text,
      ),
      monthValidator: (month) => validateDate(
        _controllers[0]['${prefix}Day']!.text,
        month,
        _controllers[0]['${prefix}Year']!.text,
      ),
      yearValidator: (year) => validateDate(
        _controllers[0]['${prefix}Day']!.text,
        _controllers[0]['${prefix}Month']!.text,
        year,
      ),
      isCheckBoxRequired: isRequired,
      checkBoxTitle: 'Still Enrolled here',
    );
  }

  void _addGap() {
    setState(() {
      _controllers.add({
        'activitiesUndertaken': TextEditingController(),
        'organizationType': TextEditingController(),
        'refereeName': TextEditingController(),
        'selfEmployedFromDay': TextEditingController(),
        'selfEmployedFromMonth': TextEditingController(),
        'selfEmployedFromYear': TextEditingController(),
        'selfEmployedTillDay': TextEditingController(),
        'selfEmployedTillMonth': TextEditingController(),
        'selfEmployedTillYear': TextEditingController(),
        'verificationDetails': TextEditingController(),
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final res = ResponsiveBreakpoints.of(context).largerThan(TABLET);

    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.only(bottom: 20.0, top: 5.0),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    'Add Self-Employment Rerence',
                    style: TextStyle(
                      color: AppColors.kBlueColor,
                      fontSize: 20.0,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              ..._controllers.map(
                (gap) => res
                    ? _buildContentDiv(context)
                    : ReferenceCheckEmployementGapView(gap: gap),
              ),
              const SizedBox(height: 15.0),
              res
                  ? Row(
                      spacing: 20,
                      mainAxisAlignment: MainAxisAlignment.end,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        ReferenceCheckViewButton(
                          title: '+ Add another self-Employment Referee',
                          onPressed: _addGap,
                          isSavedButton: false,
                        ),
                        ReferenceCheckViewButton(
                          title: 'Save',
                          onPressed: () {},
                          isSavedButton: true,
                        ),
                      ],
                    )
                  : Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          ReferenceCheckViewButton(
                            title: '+ Add another self-Employment Referee',
                            onPressed: _addGap,
                            isSavedButton: false,
                          ),
                          const SizedBox(height: 10.0),
                          ReferenceCheckViewButton(
                            title: 'Save',
                            onPressed: () {},
                            isSavedButton: true,
                          ),
                        ],
                      ),
                    ),
              const SizedBox(height: 10.0),
            ],
          ),
        ),
      ),
    );
  }
}
