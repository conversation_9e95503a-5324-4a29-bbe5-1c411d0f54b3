import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_build_text_field.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_form_center_title_div.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_form_text_title_tile.dart';
import 'package:SolidCheck/shared/utils/validations.dart';
import 'package:SolidCheck/shared/widgets/date_picker_field.dart';
import 'package:SolidCheck/shared/widgets/drop_down.dart';
import 'package:flutter/material.dart';

class ReferenceCheckPersonalView extends StatefulWidget {
  const ReferenceCheckPersonalView({super.key, required this.controllers});

  final List<TextEditingController> controllers;

  @override
  State<ReferenceCheckPersonalView> createState() =>
      _ReferenceCheckPersonalViewState();
}

class _ReferenceCheckPersonalViewState
    extends State<ReferenceCheckPersonalView> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: AppColors.refCheckDivColor,
        borderRadius: BorderRadius.circular(10.0),
      ),
      child: Column(
        spacing: 10,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const DBSFormCenterTitleDiv(),
          const DBSFormTextTitleTile(title: 'Referee\'s Information'),
          buildTextField(
            controller: widget.controllers[0],
            title: 'Referee Name:',
            isFieldRequired: true,
            validator: validateRequired,
          ),
          CustomDropdownButton2(
            isRquired: true,
            title: 'Referee Profession:',
            dropdownItems: const [
              DropdownMenuItem(value: 'value1', child: Text('Option 1')),
            ],
            onChanged: (v) {},
            hint: 'Please select an organization type',
          ),
          buildTextField(
            controller: widget.controllers[2],
            title: 'Relation with the referee:',
            isFieldRequired: true,
            validator: validateRequired,
          ),
          DatePickerField(
            dayController: widget.controllers[3],
            monthController: widget.controllers[4],
            yearController: widget.controllers[5],
            prefixIcon: Icons.calendar_month,
            title: 'Known Since',
            isFieldRequired: true,
            dayValidator: validateRequired,
            monthValidator: validateRequired,
            yearValidator: validateRequired,
          ),
          buildTextField(
            controller: widget.controllers[6],
            title: 'Referee Contact Email:',
            isFieldRequired: true,
            validator: validateRequired,
          ),
          buildTextField(
            controller: widget.controllers[7],
            title: 'Referee Contact Number:',
            isFieldRequired: true,
            validator: validateRequired,
          ),
          buildTextField(
            controller: widget.controllers[8],
            title: 'Comments:',
            isFieldRequired: false,
            validator: validateRequired,
            isCommentFieldRequired: true,
          ),
        ],
      ),
    );
  }
}
