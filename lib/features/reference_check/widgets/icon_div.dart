import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/reference_check/views/screens/main_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:responsive_framework/responsive_framework.dart';

Widget buildReferenceCheckIconDiv(
  Size size,
  String assetPath,
  String title,
  WidgetRef ref,
  BuildContext context,
) {
  final isSelected = ref.watch(selectedOptionProvider) == title;

  final responsiveBreakPoint =
      ResponsiveBreakpoints.of(context).largerThan(TABLET);

  Color thisDivColor = const Color(0XFFF1F9FF);

  return InkWell(
    onTap: () {
      ref.read(selectedScreenProvider.notifier).state = title;
    },
    child: Padding(
      padding: const EdgeInsets.symmetric(horizontal: 5.0),
      child: Container(
        decoration: BoxDecoration(
          color: thisDivColor,
          borderRadius: BorderRadius.circular(10.0),
          border: isSelected
              ? Border.all(color: AppColors.kBlueColor, width: 2.0)
              : null,
        ),
        height: responsiveBreakPoint ? size.height * 0.18 : size.height * 0.18,
        width: responsiveBreakPoint ? size.width * 0.17500 : size.width * 0.45,
        child: Stack(
          children: [
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(assetPath),
                  const SizedBox(height: 10.0),
                  Padding(
                    padding: const EdgeInsets.all(2.0),
                    child: Text(
                      title,
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 13.0),
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Positioned(
                top: 0,
                right: 0,
                child: ClipRRect(
                  borderRadius: BorderRadius.zero,
                  child: CircleAvatar(
                    backgroundColor: AppColors.kBlueColor,
                    child: Icon(
                      Icons.check,
                      color: AppColors.kWhiteColor,
                      size: 20.0,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    ),
  );
}
