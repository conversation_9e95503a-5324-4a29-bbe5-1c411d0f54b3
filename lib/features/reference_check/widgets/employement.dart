import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_build_text_field.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_form_center_title_div.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_form_text_title_tile.dart';

import 'package:SolidCheck/shared/utils/validations.dart';
import 'package:SolidCheck/shared/widgets/date_picker_field.dart';
import 'package:flutter/material.dart';

class ReferenceCheckEmployementView extends StatefulWidget {
  final List<List<TextEditingController>> _referenceControllers;

  const ReferenceCheckEmployementView({
    super.key,
    required List<List<TextEditingController>> referenceControllers,
  }) : _referenceControllers = referenceControllers,
       super();

  @override
  State<ReferenceCheckEmployementView> createState() =>
      _ReferenceCheckEmployementViewState();
}

class _ReferenceCheckEmployementViewState
    extends State<ReferenceCheckEmployementView> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: AppColors.refCheckDivColor,
        borderRadius: BorderRadius.circular(10.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const DBSFormCenterTitleDiv(),
          const DBSFormTextTitleTile(title: 'Applicant\'s Information'),
          const SizedBox(height: 10.0),
          buildTextField(
            controller: widget._referenceControllers[0][0],
            title: 'Previous Company Name:',
            isFieldRequired: true,
            validator: validateRequired,
          ),
          buildTextField(
            controller: widget._referenceControllers[0][1],
            title: 'Previous Job Role:',
            isFieldRequired: true,
            validator: validateRequired,
          ),
          const SizedBox(height: 10.0),
          DatePickerField(
            dayController: widget._referenceControllers[0][2],
            monthController: widget._referenceControllers[0][3],
            yearController: widget._referenceControllers[0][4],
            prefixIcon: Icons.calendar_month,
            title: 'Employed From',
            isFieldRequired: true,
            dayValidator: validateRequired,
            monthValidator: validateRequired,
            yearValidator: validateRequired,
          ),
          DatePickerField(
            dayController: widget._referenceControllers[0][5],
            monthController: widget._referenceControllers[0][6],
            yearController: widget._referenceControllers[0][7],
            prefixIcon: Icons.calendar_month,
            title: 'Employed Till',
            isFieldRequired: true,
            dayValidator: validateRequired,
            monthValidator: validateRequired,
            yearValidator: validateRequired,
            isCheckBoxRequired: true,
            checkBoxTitle: 'Still Employed here',
          ),
          const SizedBox(height: 10.0),
          const DBSFormTextTitleTile(title: 'Referee\'s Information'),
          const SizedBox(height: 10.0),
          buildTextField(
            controller: widget._referenceControllers[0][8],
            title: 'Referee Name:',
            isFieldRequired: true,
            validator: validateRequired,
          ),
          buildTextField(
            controller: widget._referenceControllers[0][9],
            title: 'Referee Job Role:',
            isFieldRequired: true,
            validator: validateRequired,
          ),
        ],
      ),
    );
  }
}
