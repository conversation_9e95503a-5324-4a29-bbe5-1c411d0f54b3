import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/reference_check/widgets/row_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:responsive_framework/responsive_framework.dart';

class ReferenceCheckHeaderCard extends ConsumerWidget {
  const ReferenceCheckHeaderCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final Size size = MediaQuery.of(context).size;
    final bool isLargerThanTablet =
        ResponsiveBreakpoints.of(context).largerThan(TABLET);

    return Container(
      width: isLargerThanTablet ? size.width * 0.45 : size.width,
      decoration: BoxDecoration(
        color: const Color(0XFFE6EDF3),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(5.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildProfileSection(),
            const Divider(),
            _buildInfoRows(isLargerThanTablet),
          ],
        ),
      ),
    );
  }
}

Widget _buildProfileSection() {
  return Row(
    spacing: 10,
    crossAxisAlignment: CrossAxisAlignment.center,
    mainAxisAlignment: MainAxisAlignment.start,
    children: [
      Padding(
        padding: const EdgeInsets.only(top: 5.0, left: 10.0),
        child: CircleAvatar(
          radius: 30.0,
          backgroundColor: AppColors.kBlueColor,
          child: Text(
            'JS',
            style: TextStyle(color: AppColors.kWhiteColor),
          ),
        ),
      ),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'John Smith',
            style: TextStyle(
              color: AppColors.kBlackColor,
              fontSize: 16.0,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            'Applicant ID: 122353',
            style: TextStyle(color: AppColors.kBlueColor, fontSize: 12.0),
          ),
        ],
      )
    ],
  );
}

Widget _buildInfoRows(bool isLargerThanTablet) {
  return Column(
    spacing: 10,
    crossAxisAlignment: CrossAxisAlignment.start,
    children: isLargerThanTablet
        ? [
            const Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: ReferenceCheckRowText(
                    title1: 'Job Role',
                    title2: 'Cleaner',
                  ),
                ),
                Expanded(
                  child: ReferenceCheckRowText(
                    title1: 'Email Address',
                    title2: '<EMAIL>',
                  ),
                ),
              ],
            ),
            const Row(
              spacing: 5,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: ReferenceCheckRowText(
                    title1: 'Date of Birth',
                    title2: '19-02-1998',
                  ),
                ),
                Expanded(
                  child: ReferenceCheckRowText(
                    title1: 'Phone Number',
                    title2: '+123 12312 3121',
                  ),
                ),
              ],
            ),
          ]
        : [
            const ReferenceCheckRowText(
              title1: 'Job Role',
              title2: 'Cleaner',
            ),
            const ReferenceCheckRowText(
              title1: 'Email Address',
              title2: '<EMAIL>',
            ),
            const ReferenceCheckRowText(
              title1: 'Date of Birth',
              title2: '19-02-1998',
            ),
            const ReferenceCheckRowText(
              title1: 'Phone Number',
              title2: '+123 12312 3121',
            ),
          ],
  );
}
