import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_build_text_field.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_form_center_title_div.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_form_text_title_tile.dart';
import 'package:SolidCheck/shared/widgets/date_picker_field.dart';
import 'package:SolidCheck/shared/widgets/drop_down.dart';
import 'package:flutter/material.dart';

class ReferenceCheckEmployementGapView extends StatefulWidget {
  final Map<String, TextEditingController> gap;

  const ReferenceCheckEmployementGapView({super.key, required this.gap});

  @override
  State<ReferenceCheckEmployementGapView> createState() =>
      _ReferenceCheckEmployementGapViewState();
}

class _ReferenceCheckEmployementGapViewState
    extends State<ReferenceCheckEmployementGapView> {
  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 10,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const DBSFormCenterTitleDiv(),
        const DBSFormTextTitleTile(title: 'Referee\'s Information'),
        CustomDropdownButton2(
          isRquired: true,
          title: 'Gap Type:',
          dropdownItems: const [
            DropdownMenuItem(value: 'value1', child: Text('Option 1')),
          ],
          onChanged: (value) {
            setState(() {
              widget.gap['gapType']!.text = value!;
            });
          },
          hint: 'Please select a gap type',
        ),
        DatePickerField(
          dayController: widget.gap['enrollmentDay']!,
          monthController: widget.gap['enrollmentMonth']!,
          yearController: widget.gap['enrollmentYear']!,
          prefixIcon: Icons.date_range,
          title: 'From:',
          isFieldRequired: true,
          dayValidator: (day) => _validateDate(
            day,
            widget.gap['enrollmentMonth']!.text,
            widget.gap['enrollmentYear']!.text,
          ),
          monthValidator: (month) => _validateDate(
            widget.gap['enrollmentDay']!.text,
            month,
            widget.gap['enrollmentYear']!.text,
          ),
          yearValidator: (year) => _validateDate(
            widget.gap['enrollmentDay']!.text,
            widget.gap['enrollmentMonth']!.text,
            year,
          ),
        ),
        DatePickerField(
          dayController: widget.gap['enrollmentTillDay']!,
          monthController: widget.gap['enrollmentTillMonth']!,
          yearController: widget.gap['enrollmentTillYear']!,
          prefixIcon: Icons.date_range,
          title: 'Till:',
          isFieldRequired: true,
          dayValidator: null,
          monthValidator: null,
          yearValidator: null,
        ),
        buildTextField(
          controller: widget.gap['gapReason']!,
          title: 'Employment Gap Reason:',
          isFieldRequired: true,
          validator: _validateRequiredField,
          isCommentFieldRequired: true,
        ),
      ],
    );
  }

  String? _validateRequiredField(String? value) {
    return (value == null || value.isEmpty) ? 'This field is required' : null;
  }

  String? _validateDate(String? day, String? month, String? year) {
    if (day == null ||
        month == null ||
        year == null ||
        day.isEmpty ||
        month.isEmpty ||
        year.isEmpty) {
      return 'Date is required';
    }
    if (int.tryParse(day) == null ||
        int.tryParse(month) == null ||
        int.tryParse(year) == null) {
      return 'Invalid date format';
    }
    return null;
  }
}
