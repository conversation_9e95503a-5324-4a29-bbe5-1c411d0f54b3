import 'package:SolidCheck/core/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:responsive_framework/responsive_framework.dart';

class ReferenceCheckViewButton extends StatefulWidget {
  final String title;
  final void Function()? onPressed;
  final bool isSavedButton;

  const ReferenceCheckViewButton(
      {super.key,
      required this.title,
      this.onPressed,
      required this.isSavedButton});

  @override
  State<ReferenceCheckViewButton> createState() =>
      _ReferenceCheckViewButtonState();
}

class _ReferenceCheckViewButtonState extends State<ReferenceCheckViewButton> {
  @override
  Widget build(BuildContext context) {
    final res = ResponsiveBreakpoints.of(context).largerThan(TABLET);
    final Size size = MediaQuery.of(context).size;
    Color thisSaveButtonColor = const Color(0XFFD3D2D2);
    double buttonWidth;

    if (widget.isSavedButton == false) {
      buttonWidth = res ? size.width * 0.24 : size.width;
    } else {
      buttonWidth = res ? size.width * 0.12 : size.width;
    }

    return SizedBox(
      height: res ? size.height * 0.06 : size.height * 0.06,
      width: buttonWidth,
      child: ElevatedButton(
        style: ButtonStyle(
            shape: WidgetStateProperty.all(RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0))),
            backgroundColor: WidgetStateProperty.all(
                widget.isSavedButton == false
                    ? AppColors.kBlueColor
                    : thisSaveButtonColor)),
        onPressed: widget.onPressed,
        child: Text(widget.title,
            textAlign: res ? TextAlign.center : TextAlign.left,
            overflow: res ? null : TextOverflow.ellipsis,
            style: TextStyle(
                color: widget.isSavedButton == false
                    ? AppColors.kWhiteColor
                    : AppColors.kWhiteColor)),
      ),
    );
  }
}
