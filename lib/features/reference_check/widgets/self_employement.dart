import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_build_text_field.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_form_center_title_div.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_form_text_title_tile.dart';
import 'package:SolidCheck/shared/utils/validations.dart';
import 'package:SolidCheck/shared/widgets/date_picker_field.dart';
import 'package:SolidCheck/shared/widgets/drop_down.dart';
import 'package:flutter/material.dart';

class ReferenceCheckSelfEmployementView extends StatefulWidget {
  final Map<String, TextEditingController> controllers;

  const ReferenceCheckSelfEmployementView({
    super.key,
    required this.controllers,
  });

  @override
  State<ReferenceCheckSelfEmployementView> createState() =>
      _ReferenceCheckSelfEmployementViewState();
}

class _ReferenceCheckSelfEmployementViewState
    extends State<ReferenceCheckSelfEmployementView> {
  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 10,
      children: [
        const DBSFormCenterTitleDiv(),
        const DBSFormTextTitleTile(title: 'Referee\'s Information'),
        buildTextField(
          controller: widget.controllers['refereeName']!,
          title: 'Referee Name',
          isFieldRequired: true,
          validator: (value) => validateRequired(value),
        ),
        CustomDropdownButton2(
          isRquired: true,
          title: 'Organization Type',
          dropdownItems: const [
            DropdownMenuItem(value: 'value1', child: Text('Option 1')),
            DropdownMenuItem(value: 'value2', child: Text('Option 2')),
            DropdownMenuItem(value: 'value3', child: Text('Option 3')),
          ],
          onChanged: (v) {},
          hint: 'Please select an organization type',
        ),
        DatePickerField(
          dayController: widget.controllers['selfEmployedFromDay']!,
          monthController: widget.controllers['selfEmployedFromMonth']!,
          yearController: widget.controllers['selfEmployedFromYear']!,
          prefixIcon: Icons.date_range,
          title: 'Self-Employed From',
          isFieldRequired: true,
          dayValidator: (day) => validateDate(
            day,
            widget.controllers['selfEmployedFromMonth']!.text,
            widget.controllers['selfEmployedFromYear']!.text,
          ),
          monthValidator: (month) => validateDate(
            widget.controllers['selfEmployedFromDay']!.text,
            month,
            widget.controllers['selfEmployedFromYear']!.text,
          ),
          yearValidator: (year) => validateDate(
            widget.controllers['selfEmployedFromDay']!.text,
            widget.controllers['selfEmployedFromMonth']!.text,
            year,
          ),
        ),
        DatePickerField(
          dayController: widget.controllers['selfEmployedTillDay']!,
          monthController: widget.controllers['selfEmployedTillMonth']!,
          yearController: widget.controllers['selfEmployedTillYear']!,
          prefixIcon: Icons.date_range,
          title: 'Self-Employed Till',
          isFieldRequired: true,
          dayValidator: (day) => validateDate(
            day,
            widget.controllers['selfEmployedTillMonth']!.text,
            widget.controllers['selfEmployedTillYear']!.text,
          ),
          monthValidator: (month) => validateDate(
            widget.controllers['selfEmployedTillDay']!.text,
            month,
            widget.controllers['selfEmployedTillYear']!.text,
          ),
          yearValidator: (year) => validateDate(
            widget.controllers['selfEmployedTillDay']!.text,
            widget.controllers['selfEmployedTillMonth']!.text,
            year,
          ),
          isCheckBoxRequired: true,
          checkBoxTitle: 'Still Enrolled here',
        ),
        buildTextField(
          controller: widget.controllers['activitiesUndertaken']!,
          title: 'Activities Undertaken',
          isFieldRequired: false,
          validator: null,
          isCommentFieldRequired: true,
        ),
        buildTextField(
          controller: widget.controllers['verificationDetails']!,
          title: 'Details of Any Verification Available',
          isFieldRequired: false,
          validator: null,
          isCommentFieldRequired: true,
        ),
      ],
    );
  }
}
