import 'package:SolidCheck/core/theme/colors.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_build_text_field.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_form_center_title_div.dart';
import 'package:SolidCheck/features/dbs/presentation/widgets/shared/dbs_form_text_title_tile.dart';
import 'package:SolidCheck/shared/utils/validations.dart';
import 'package:SolidCheck/shared/widgets/date_picker_field.dart';
import 'package:flutter/material.dart';

class ReferenceCheckEducationView extends StatefulWidget {
  final List<TextEditingController> controllers;
  final List<bool> _stillEnrolledList = [false];

  ReferenceCheckEducationView({super.key, required this.controllers});

  @override
  State<ReferenceCheckEducationView> createState() =>
      _ReferenceCheckEducationViewState();
}

class _ReferenceCheckEducationViewState
    extends State<ReferenceCheckEducationView> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(5.0),
      decoration: BoxDecoration(
        color: AppColors.refCheckDivColor,
        borderRadius: BorderRadius.circular(10.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const DBSFormCenterTitleDiv(),
          const DBSFormTextTitleTile(title: 'Applicant\'s Information'),
          const SizedBox(height: 10.0),
          buildTextField(
            controller: widget.controllers[0],
            title: 'Education Institute’s name:',
            isFieldRequired: true,
            validator: validateRequired,
          ),
          const SizedBox(height: 10.0),
          DatePickerField(
            dayController: widget.controllers[1],
            monthController: widget.controllers[2],
            yearController: widget.controllers[3],
            prefixIcon: Icons.calendar_month,
            title: 'Enrollment from:',
            isFieldRequired: true,
            dayValidator: validateRequired,
            monthValidator: validateRequired,
            yearValidator: validateRequired,
          ),
          DatePickerField(
            dayController: widget.controllers[4],
            monthController: widget.controllers[5],
            yearController: widget.controllers[6],
            prefixIcon: Icons.calendar_month,
            title: 'Enrollment Till:',
            isFieldRequired: true,
            dayValidator: validateRequired,
            monthValidator: validateRequired,
            yearValidator: validateRequired,
            isCheckBoxRequired: true,
            checkBoxTitle: 'Still Enrolled here',
            isChecked: widget._stillEnrolledList[0],
            onCheckedChanged: (value) {
              setState(() {
                widget._stillEnrolledList[0] = value;
              });
            },
          ),
          const SizedBox(height: 10.0),
          const DBSFormTextTitleTile(title: 'Referee\'s Information'),
          const SizedBox(height: 10.0),
          buildTextField(
            controller: widget.controllers[7],
            title: 'Referee Name:',
            isFieldRequired: true,
            validator: validateRequired,
          ),
          buildTextField(
            controller: widget.controllers[8],
            title: 'Referee Job Role:',
            isFieldRequired: true,
            validator: validateRequired,
          ),
          const SizedBox(height: 10.0),
          buildTextField(
            controller: widget.controllers[9],
            title: 'Referee Contact Email',
            isFieldRequired: true,
            validator: validateRequired,
          ),
          buildTextField(
            controller: widget.controllers[10],
            title: 'Referee Contact Number',
            isFieldRequired: true,
            validator: validateRequired,
          ),
        ],
      ),
    );
  }
}
