import 'package:SolidCheck/core/theme/colors.dart';
import 'package:flutter/material.dart';

class ReferenceCheckRowText extends StatefulWidget {
  final String title1;
  final String title2;

  const ReferenceCheckRowText({
    super.key,
    required this.title1,
    required this.title2,
  });

  @override
  State<ReferenceCheckRowText> createState() => _ReferenceCheckRowTextState();
}

class _ReferenceCheckRowTextState extends State<ReferenceCheckRowText> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0),
        child: Row(
          children: [
            Expanded(
                child: Text('${widget.title1}:',
                    textAlign: TextAlign.start,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(color: AppColors.kBlueColor))),
            Expanded(
                child: Text('${widget.title2}:',
                    textAlign: TextAlign.start,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(color: AppColors.kBlackColor))),
          ],
        ),
      ),
    );
  }
}
