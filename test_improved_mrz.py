#!/usr/bin/env python3
"""
Test script for improved FastMRZ implementation
"""

import requests
import base64
import json
from pathlib import Path

def test_passport_processing():
    """Test passport processing with improved FastMRZ"""
    
    # Read the passport image (the one from the user's screenshot)
    # We'll create a test image path - user should replace with actual path
    passport_image_path = "/tmp/passport_test.jpg"
    
    # Check if image exists
    if not Path(passport_image_path).exists():
        print("❌ Passport image not found. Please save the passport image to /tmp/passport_test.jpg")
        print("   You can use the UK specimen passport from the screenshot")
        return
    
    # Read image as bytes
    with open(passport_image_path, 'rb') as f:
        image_data = f.read()
    
    # Prepare multipart form data
    files = {
        'file': ('passport.jpg', image_data, 'image/jpeg')
    }
    
    data = {
        'document_type': 'passport',
        'application_id': 'test_123'
    }
    
    # Test the SolidTech API
    url = 'http://127.0.0.1:9000/api/v1/documents/ai-extract'
    
    print("🔄 Testing improved FastMRZ implementation...")
    print(f"📤 Sending request to: {url}")
    
    try:
        response = requests.post(url, files=files, data=data, timeout=30)
        
        print(f"📥 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ SUCCESS! FastMRZ processing completed")
            print("\n📋 Extracted Fields:")
            print("=" * 50)
            
            extracted_fields = result.get('extracted_fields', {})
            
            # Display key fields
            key_fields = [
                'passport_number', 'first_name', 'last_name', 
                'date_of_birth', 'expiry_date', 'nationality', 
                'gender', 'issuing_country'
            ]
            
            for field in key_fields:
                if field in extracted_fields:
                    field_data = extracted_fields[field]
                    value = field_data.get('value', 'N/A')
                    confidence = field_data.get('confidence', 0)
                    method = field_data.get('extraction_method', 'unknown')
                    
                    print(f"  {field:15}: {value} (confidence: {confidence:.2f}, method: {method})")
                else:
                    print(f"  {field:15}: NOT EXTRACTED")
            
            print("\n🔍 Processing Details:")
            print(f"  Processing method: {result.get('processing_method', 'unknown')}")
            print(f"  Overall confidence: {result.get('confidence', 0):.2f}")
            
            # Show fraud analysis
            fraud_analysis = result.get('fraud_analysis', {})
            print(f"\n🛡️ Fraud Analysis:")
            print(f"  Is authentic: {fraud_analysis.get('is_authentic', 'unknown')}")
            print(f"  Confidence score: {fraud_analysis.get('confidence_score', 0):.2f}")
            
            # Show full response for debugging
            print(f"\n🔧 Full Response (for debugging):")
            print(json.dumps(result, indent=2))
            
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def create_test_image():
    """Helper to create a test image from base64 data"""
    # This would contain the base64 data of the passport image
    # For now, just print instructions
    print("📝 To test the improved FastMRZ:")
    print("1. Save the UK specimen passport image to /tmp/passport_test.jpg")
    print("2. Run this script: python3 test_improved_mrz.py")
    print("3. Check the extracted MRZ data")

if __name__ == "__main__":
    print("🧪 FastMRZ Improvement Test")
    print("=" * 40)
    
    # Check if server is running
    try:
        health_response = requests.get('http://127.0.0.1:9000/health', timeout=5)
        if health_response.status_code == 200:
            print("✅ SolidTech server is running")
            test_passport_processing()
        else:
            print("❌ SolidTech server health check failed")
    except:
        print("❌ SolidTech server is not running")
        print("   Start it with: cd ../SolidTech && python3 mvvm_server.py")
