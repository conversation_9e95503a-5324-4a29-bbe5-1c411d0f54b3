"""
Authentication data models
"""

from typing import Optional

from pydantic import BaseModel


class Token(BaseModel):
    """JWT token response model"""
    access_token: str
    token_type: str
    expires_in: int


class TokenData(BaseModel):
    """Token payload data"""
    username: Optional[str] = None
    user_id: Optional[str] = None
    scopes: list[str] = []


class User(BaseModel):
    """User model"""
    id: str
    username: str
    email: str
    is_active: bool = True
    is_admin: bool = False
