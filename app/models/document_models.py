"""
Document Models - Data structures for document processing
Following MVVM pattern with clean data models
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from enum import Enum


class DocumentType(Enum):
    """Supported document types"""
    PASSPORT = "passport"
    PASSPORT_ANY = "passport_any"
    UK_DRIVING_LICENSE = "photocard_drivers_licence_uk"
    P60 = "p60"
    P45 = "p45"
    P45_P60 = "p45_p60"
    BANK_STATEMENT = "bank_statement"


class ProcessingStatus(Enum):
    """Document processing status"""
    PENDING = "pending"
    PROCESSING = "processing"
    SUCCESS = "success"
    FAILED = "failed"
    REQUIRES_REVIEW = "requires_review"


class ExtractionMethod(Enum):
    """Method used for field extraction"""
    FAST_MRZ = "fast_mrz"
    TROCR = "trocr"
    PADDLE_OCR = "paddle_ocr"
    TESSERACT = "tesseract"
    YOLO_DETECTION = "yolo_detection"
    PATTERN_MATCHING = "pattern_matching"
    MANUAL_ENTRY = "manual_entry"


@dataclass
class ExtractedField:
    """Individual extracted field with metadata"""
    value: str
    confidence: float
    extraction_method: ExtractionMethod
    field_type: str  # text, date, number, etc.
    required: bool
    editable: bool
    validation_errors: List[str] = field(default_factory=list)
    validation_warnings: List[str] = field(default_factory=list)
    bounding_box: Optional[Dict[str, float]] = None  # x, y, width, height


@dataclass
class FraudAnalysis:
    """Fraud detection results"""
    is_authentic: bool
    is_fraudulent: bool
    is_altered: bool
    confidence_score: float
    authenticity_score: float
    alteration_score: float
    fraud_indicators: List[str] = field(default_factory=list)
    security_features_verified: List[str] = field(default_factory=list)
    yolo_detections: List[Dict[str, Any]] = field(default_factory=list)
    hologram_validation: bool = False
    depth_analysis: str = ""
    requires_manual_review: bool = False


@dataclass
class FaceAnalysis:
    """Face recognition and liveness detection results"""
    face_detected: bool
    face_count: int
    face_confidence: float
    liveness_score: float
    is_live: bool
    face_embedding: Optional[List[float]] = None
    face_bounding_box: Optional[Dict[str, float]] = None
    portrait_base64: Optional[str] = None
    portrait_filename: Optional[str] = None


@dataclass
class ValidationResult:
    """Data validation results"""
    is_valid: bool
    is_consistent: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    field_validations: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    cross_field_validations: List[str] = field(default_factory=list)


@dataclass
class ProcessingMetadata:
    """Processing metadata and performance metrics"""
    processing_method: str
    processing_time: float
    model_version: str
    engine_versions: Dict[str, str] = field(default_factory=dict)
    processing_timestamp: datetime = field(default_factory=datetime.now)
    request_id: str = ""
    application_id: str = ""
    file_info: Dict[str, Any] = field(default_factory=dict)


@dataclass
class DocumentExtractionResult:
    """Complete document extraction result"""
    status: ProcessingStatus
    document_type: DocumentType
    extracted_fields: Dict[str, ExtractedField]
    fraud_analysis: FraudAnalysis
    face_analysis: Optional[FaceAnalysis]
    validation_result: ValidationResult
    processing_metadata: ProcessingMetadata
    recommendations: List[str] = field(default_factory=list)
    confidence: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API response"""
        return {
            "status": self.status.value,
            "document_type": self.document_type.value,
            "extracted_fields": {
                name: {
                    "value": field.value,
                    "confidence": field.confidence,
                    "extraction_method": field.extraction_method.value,
                    "field_type": field.field_type,
                    "required": field.required,
                    "editable": field.editable,
                    "validation_errors": field.validation_errors,
                    "validation_warnings": field.validation_warnings,
                    "bounding_box": field.bounding_box
                }
                for name, field in self.extracted_fields.items()
            },
            "fraud_analysis": {
                "is_authentic": self.fraud_analysis.is_authentic,
                "is_fraudulent": self.fraud_analysis.is_fraudulent,
                "is_altered": self.fraud_analysis.is_altered,
                "confidence_score": self.fraud_analysis.confidence_score,
                "authenticity_score": self.fraud_analysis.authenticity_score,
                "alteration_score": self.fraud_analysis.alteration_score,
                "fraud_indicators": self.fraud_analysis.fraud_indicators,
                "security_features_verified": self.fraud_analysis.security_features_verified,
                "yolo_detections": self.fraud_analysis.yolo_detections,
                "hologram_validation": self.fraud_analysis.hologram_validation,
                "depth_analysis": self.fraud_analysis.depth_analysis,
                "requires_manual_review": self.fraud_analysis.requires_manual_review
            },
            "face_analysis": {
                "face_detected": self.face_analysis.face_detected,
                "face_count": self.face_analysis.face_count,
                "face_confidence": self.face_analysis.face_confidence,
                "liveness_score": self.face_analysis.liveness_score,
                "is_live": self.face_analysis.is_live,
                "portrait_base64": self.face_analysis.portrait_base64,
                "portrait_filename": self.face_analysis.portrait_filename
            } if self.face_analysis else None,
            "validation_result": {
                "is_valid": self.validation_result.is_valid,
                "is_consistent": self.validation_result.is_consistent,
                "errors": self.validation_result.errors,
                "warnings": self.validation_result.warnings,
                "field_validations": self.validation_result.field_validations,
                "cross_field_validations": self.validation_result.cross_field_validations
            },
            "processing_metadata": {
                "processing_method": self.processing_metadata.processing_method,
                "processing_time": self.processing_metadata.processing_time,
                "model_version": self.processing_metadata.model_version,
                "engine_versions": self.processing_metadata.engine_versions,
                "processing_timestamp": self.processing_metadata.processing_timestamp.isoformat(),
                "request_id": self.processing_metadata.request_id,
                "application_id": self.processing_metadata.application_id,
                "file_info": self.processing_metadata.file_info
            },
            "recommendations": self.recommendations,
            "confidence": self.confidence
        }


@dataclass
class DocumentProcessingRequest:
    """Document processing request model"""
    document_type: DocumentType
    image_data: bytes
    filename: str
    application_id: str
    request_id: str
    application_data: Optional[Dict[str, Any]] = None
    processing_options: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate request data"""
        if not self.image_data:
            raise ValueError("Image data is required")
        if not self.filename:
            raise ValueError("Filename is required")
        if len(self.image_data) == 0:
            raise ValueError("Image data cannot be empty")
        if len(self.image_data) > 50 * 1024 * 1024:  # 50MB limit
            raise ValueError("Image data too large (max 50MB)")


@dataclass
class ApplicationData:
    """Application data for cross-validation"""
    full_name: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    date_of_birth: Optional[str] = None
    current_address: Optional[str] = None
    postcode: Optional[str] = None
    ni_number: Optional[str] = None
    nationality: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "full_name": self.full_name,
            "first_name": self.first_name,
            "last_name": self.last_name,
            "date_of_birth": self.date_of_birth,
            "current_address": self.current_address,
            "postcode": self.postcode,
            "ni_number": self.ni_number,
            "nationality": self.nationality
        }
