"""
Verification result models
"""

from typing import List, Optional

from pydantic import BaseModel, Field


class VerificationResult(BaseModel):
    """Overall document verification result"""
    is_authentic: bool
    is_original: bool
    is_unaltered: bool
    data_matches: bool
    overall_score: float = Field(ge=0.0, le=1.0, default=0.0)
    risk_level: str = Field(default="unknown")  # low, medium, high, critical
    
    def __post_init__(self):
        """Calculate overall score and risk level"""
        score = 0.0
        
        if self.is_authentic:
            score += 0.3
        if self.is_original:
            score += 0.25
        if self.is_unaltered:
            score += 0.25
        if self.data_matches:
            score += 0.2
        
        self.overall_score = score
        
        # Determine risk level
        if score >= 0.9:
            self.risk_level = "low"
        elif score >= 0.7:
            self.risk_level = "medium"
        elif score >= 0.5:
            self.risk_level = "high"
        else:
            self.risk_level = "critical"


class VerificationFlags(BaseModel):
    """Specific verification flags and warnings"""
    document_expired: bool = False
    poor_image_quality: bool = False
    missing_security_features: bool = False
    inconsistent_formatting: bool = False
    suspicious_alterations: bool = False
    data_mismatches: List[str] = []
    warnings: List[str] = []
    errors: List[str] = []
