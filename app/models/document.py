"""
Document processing data models
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class DocumentAnalysisRequest(BaseModel):
    """Document analysis request model"""
    request_id: str
    document_type: str
    file_name: str
    file_size: int
    application_data: Dict[str, Any]
    user_id: str
    timestamp: datetime


class FraudAnalysis(BaseModel):
    """Fraud detection analysis results"""
    is_authentic: bool
    is_copy: bool
    is_altered: bool
    authenticity_score: float = Field(ge=0.0, le=1.0)
    alteration_indicators: List[str] = []
    copy_indicators: List[str] = []
    security_features_detected: List[str] = []


class OCRResults(BaseModel):
    """OCR extraction results"""
    full_name: Optional[str] = None
    date_of_birth: Optional[str] = None
    address: Optional[str] = None
    ni_number: Optional[str] = None
    document_number: Optional[str] = None
    expiry_date: Optional[str] = None
    issue_date: Optional[str] = None
    issuing_authority: Optional[str] = None
    confidence_scores: Dict[str, float] = {}
    raw_text: str = ""


class DataMatching(BaseModel):
    """Data matching results"""
    name_match: Optional[bool] = None
    name_similarity: Optional[float] = None
    dob_match: Optional[bool] = None
    address_match: Optional[bool] = None
    address_similarity: Optional[float] = None
    ni_match: Optional[bool] = None
    overall_match: bool
    match_score: float = Field(ge=0.0, le=1.0)


class SecurityChecks(BaseModel):
    """Security validation results"""
    file_integrity: bool
    metadata_analysis: Dict[str, Any] = {}
    digital_signature: Optional[bool] = None
    exif_data: Dict[str, Any] = {}
    file_format_valid: bool
    suspicious_patterns: List[str] = []
    security_score: float = Field(ge=0.0, le=1.0)


class DocumentAnalysisResponse(BaseModel):
    """Complete document analysis response"""
    request_id: str
    status: str
    processing_time: float
    verification_result: 'VerificationResult'
    fraud_analysis: FraudAnalysis
    ocr_results: OCRResults
    data_matching: DataMatching
    security_checks: SecurityChecks
    confidence_score: float = Field(ge=0.0, le=1.0)
    recommendations: List[str]
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class DocumentMetadata(BaseModel):
    """Document metadata for database storage"""
    id: str
    request_id: str
    user_id: str
    document_type: str
    file_name: str
    file_size: int
    processing_status: str
    confidence_score: Optional[float] = None
    verification_passed: Optional[bool] = None
    created_at: datetime
    updated_at: datetime
    expires_at: Optional[datetime] = None
