<?php

declare(strict_types=1);

namespace App\Core\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class ThrottlePasswordChange
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @param int $maxAttempts
     * @param int $decayMinutes
     * @return mixed
     */
    public function handle(Request $request, Closure $next, int $maxAttempts = 5, int $decayMinutes = 15): mixed
    {
        $key = $this->resolveRequestSignature($request);

        if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            $seconds = RateLimiter::availableIn($key);
            
            return response()->json([
                'success' => false,
                'message' => 'Too many password change attempts. Please try again later.',
                'error_code' => 'RATE_LIMIT_EXCEEDED',
                'retry_after' => $seconds,
                'data' => [
                    'max_attempts' => $maxAttempts,
                    'window_minutes' => $decayMinutes,
                    'retry_after_seconds' => $seconds
                ]
            ], ResponseAlias::HTTP_TOO_MANY_REQUESTS);
        }

        $response = $next($request);

        // Only increment attempts on failed password changes
        if ($response->getStatusCode() === 422 || $response->getStatusCode() === 401) {
            RateLimiter::hit($key, $decayMinutes * 60);
        }

        // Clear rate limit on successful password change
        if ($response->getStatusCode() === 200) {
            RateLimiter::clear($key);
        }

        return $response;
    }

    /**
     * Resolve request signature for rate limiting
     */
    protected function resolveRequestSignature(Request $request): string
    {
        $user = $request->user();
        $userId = $user ? $user->id : 'guest';
        $ip = $request->ip();
        
        return "password_change:{$userId}:{$ip}";
    }
}
