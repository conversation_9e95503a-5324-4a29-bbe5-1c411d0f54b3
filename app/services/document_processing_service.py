"""
Document Processing Service - Main service following MVVM architecture
Coordinates OCR engines, fraud detection, and validation
"""

import numpy as np
import cv2
from typing import Dict, List, Optional, Any, Tuple
import logging
from datetime import datetime
import uuid
import time

from app.core.interfaces import (
    IDocumentProcessingService,
    IOCREngine,
    IFraudDetector,
    IFaceProcessor,
    IFieldValidator,
    IDocumentRepository,
    IConfigurationService,
    ILoggingService,
    IMetricsCollector,
    IDocumentProcessor
)
from app.models.document_models import (
    DocumentProcessingRequest,
    DocumentExtractionResult,
    DocumentType,
    ProcessingStatus,
    ExtractedField,
    FraudAnalysis,
    FaceAnalysis,
    ValidationResult,
    ProcessingMetadata
)


class DocumentProcessingService(IDocumentProcessingService):
    """Main document processing service implementing MVVM pattern"""
    
    def __init__(
        self,
        ocr_engines: Dict[str, IOCREngine],
        fraud_detector: IFraudDetector,
        face_processor: IFaceProcessor,
        field_validator: IFieldValidator,
        repository: IDocumentRepository,
        config_service: IConfigurationService,
        logging_service: ILoggingService,
        metrics_collector: IMetricsCollector,
        specialized_processors: Optional[Dict[DocumentType, IDocumentProcessor]] = None
    ):
        """Initialize with dependency injection"""
        self.ocr_engines = ocr_engines
        self.fraud_detector = fraud_detector
        self.face_processor = face_processor
        self.field_validator = field_validator
        self.repository = repository
        self.config_service = config_service
        self.logging_service = logging_service
        self.metrics_collector = metrics_collector
        self.specialized_processors = specialized_processors or {}
        self.logger = logging.getLogger(__name__)
        
        # Engine priority for different document types
        self.engine_priority = {
            DocumentType.PASSPORT: ['fast_mrz', 'trocr', 'tesseract'],
            DocumentType.PASSPORT_ANY: ['fast_mrz', 'trocr', 'tesseract'],
            DocumentType.UK_DRIVING_LICENSE: ['trocr', 'tesseract', 'paddle_ocr'],
            DocumentType.P60: ['trocr', 'tesseract', 'paddle_ocr'],
            DocumentType.P45: ['trocr', 'tesseract', 'paddle_ocr'],
            DocumentType.P45_P60: ['trocr', 'tesseract', 'paddle_ocr'],
            DocumentType.BANK_STATEMENT: ['trocr', 'tesseract', 'paddle_ocr']
        }
    
    def process_document(self, request: DocumentProcessingRequest) -> DocumentExtractionResult:
        """Main document processing method"""
        start_time = time.time()
        
        try:
            # Log processing start
            self.logging_service.log_processing_start(request)
            
            # Validate request
            is_valid, errors = self.validate_request(request)
            if not is_valid:
                return self._create_error_result(request, errors)
            
            # Convert image data to numpy array
            image = self._bytes_to_image(request.image_data)
            if image is None:
                return self._create_error_result(request, ["Invalid image data"])
            
            # Preprocess image
            processed_image = self._preprocess_image(image, request.document_type)
            
            # Check if we have a specialized processor for this document type
            if request.document_type in self.specialized_processors:
                self.logger.info(f"🎯 Using specialized processor for {request.document_type.value}")
                specialized_result = self.specialized_processors[request.document_type].process_document(request)

                # Update processing metadata
                specialized_result.processing_metadata.processing_time = time.time() - start_time

                # Log completion and metrics
                self.logging_service.log_processing_complete(specialized_result)
                self.metrics_collector.record_processing_time(request.document_type, specialized_result.processing_metadata.processing_time)
                self.metrics_collector.record_accuracy_score(request.document_type, specialized_result.confidence)
                self.metrics_collector.record_fraud_detection(request.document_type, specialized_result.fraud_analysis.is_fraudulent)

                return specialized_result

            # Fallback to generic processing
            self.logger.info(f"🔄 Using generic processing for {request.document_type.value}")

            # Extract fields using appropriate OCR engine
            extracted_fields = self._extract_fields(processed_image, request.document_type)

            # Perform fraud analysis
            fraud_analysis = self._analyze_fraud(processed_image, request.document_type)

            # Extract face if applicable
            face_analysis = self._extract_face(processed_image, request.document_type)

            # Validate extracted fields
            validation_result = self._validate_fields(extracted_fields, request)
            
            # Calculate overall confidence
            confidence = self._calculate_overall_confidence(extracted_fields, fraud_analysis)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(
                extracted_fields, fraud_analysis, validation_result
            )
            
            # Create processing metadata
            processing_time = time.time() - start_time
            metadata = ProcessingMetadata(
                processing_method="custom_ocr_mvvm",
                processing_time=processing_time,
                model_version="1.0.0",
                engine_versions={name: engine.get_engine_info()["version"] for name, engine in self.ocr_engines.items()},
                request_id=request.request_id,
                application_id=request.application_id,
                file_info={
                    "filename": request.filename,
                    "size": len(request.image_data),
                    "format": self._detect_image_format(request.image_data)
                }
            )
            
            # Create result
            result = DocumentExtractionResult(
                status=ProcessingStatus.SUCCESS,
                document_type=request.document_type,
                extracted_fields=extracted_fields,
                fraud_analysis=fraud_analysis,
                face_analysis=face_analysis,
                validation_result=validation_result,
                processing_metadata=metadata,
                recommendations=recommendations,
                confidence=confidence
            )
            
            # Save result
            self.repository.save_processing_result(result)
            
            # Log completion
            self.logging_service.log_processing_complete(result)
            
            # Record metrics
            self.metrics_collector.record_processing_time(request.document_type, processing_time)
            self.metrics_collector.record_accuracy_score(request.document_type, confidence)
            self.metrics_collector.record_fraud_detection(request.document_type, fraud_analysis.is_fraudulent)
            
            self.logger.info(f"✅ Document processed successfully: {request.request_id}")
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Document processing failed: {e}")
            self.logging_service.log_error(e, {"request_id": request.request_id})
            return self._create_error_result(request, [str(e)])
    
    def get_supported_document_types(self) -> List[DocumentType]:
        """Get list of supported document types"""
        return list(DocumentType)
    
    def validate_request(self, request: DocumentProcessingRequest) -> Tuple[bool, List[str]]:
        """Validate processing request"""
        errors = []
        
        # Check document type support
        if request.document_type not in self.get_supported_document_types():
            errors.append(f"Unsupported document type: {request.document_type.value}")
        
        # Check image data
        if not request.image_data:
            errors.append("Image data is required")
        elif len(request.image_data) == 0:
            errors.append("Image data cannot be empty")
        elif len(request.image_data) > 50 * 1024 * 1024:  # 50MB
            errors.append("Image data too large (max 50MB)")
        
        # Check filename
        if not request.filename:
            errors.append("Filename is required")
        
        # Check request ID
        if not request.request_id:
            errors.append("Request ID is required")
        
        return len(errors) == 0, errors
    
    def get_processing_status(self, request_id: str) -> Optional[Dict[str, Any]]:
        """Get processing status by request ID"""
        try:
            result = self.repository.get_processing_result(request_id)
            if result:
                return {
                    "request_id": request_id,
                    "status": result.status.value,
                    "confidence": result.confidence,
                    "processing_time": result.processing_metadata.processing_time,
                    "timestamp": result.processing_metadata.processing_timestamp.isoformat()
                }
            return None
        except Exception as e:
            self.logger.error(f"❌ Failed to get processing status: {e}")
            return None
    
    def _extract_fields(self, image: np.ndarray, document_type: DocumentType) -> Dict[str, ExtractedField]:
        """Extract fields using appropriate OCR engine"""
        engines_to_try = self.engine_priority.get(document_type, ['trocr', 'tesseract'])
        
        for engine_name in engines_to_try:
            if engine_name in self.ocr_engines:
                try:
                    engine = self.ocr_engines[engine_name]
                    fields = engine.extract_fields(image, document_type)
                    
                    if fields:  # If we got results, use them
                        self.logger.info(f"✅ Used {engine_name} engine for {document_type.value}")
                        return fields
                        
                except Exception as e:
                    self.logger.warning(f"⚠️ {engine_name} engine failed: {e}")
                    continue
        
        # If all engines failed, return empty dict
        self.logger.warning(f"⚠️ All OCR engines failed for {document_type.value}")
        return {}
    
    def _analyze_fraud(self, image: np.ndarray, document_type: DocumentType) -> FraudAnalysis:
        """Perform fraud analysis"""
        try:
            return self.fraud_detector.analyze_document(image, document_type)
        except Exception as e:
            self.logger.error(f"❌ Fraud analysis failed: {e}")
            # Return default fraud analysis
            return FraudAnalysis(
                is_authentic=True,
                is_fraudulent=False,
                is_altered=False,
                confidence_score=0.5,
                authenticity_score=0.5,
                alteration_score=0.0,
                requires_manual_review=True
            )
    
    def _extract_face(self, image: np.ndarray, document_type: DocumentType) -> Optional[FaceAnalysis]:
        """Extract face from document if applicable"""
        if document_type not in [DocumentType.PASSPORT, DocumentType.PASSPORT_ANY, DocumentType.UK_DRIVING_LICENSE]:
            return None
        
        try:
            return self.face_processor.extract_face(image)
        except Exception as e:
            self.logger.error(f"❌ Face extraction failed: {e}")
            return None
    
    def _validate_fields(self, fields: Dict[str, ExtractedField], request: DocumentProcessingRequest) -> ValidationResult:
        """Validate extracted fields"""
        try:
            return self.field_validator.validate_field(
                fields, request.document_type, request.application_data
            )
        except Exception as e:
            self.logger.error(f"❌ Field validation failed: {e}")
            return ValidationResult(
                is_valid=False,
                is_consistent=False,
                errors=[f"Validation failed: {str(e)}"]
            )
    
    def _calculate_overall_confidence(self, fields: Dict[str, ExtractedField], fraud_analysis: FraudAnalysis) -> float:
        """Calculate overall confidence score"""
        if not fields:
            return 0.0
        
        # Average field confidence
        field_confidences = [field.confidence for field in fields.values()]
        avg_field_confidence = sum(field_confidences) / len(field_confidences)
        
        # Factor in fraud analysis
        fraud_confidence = fraud_analysis.confidence_score
        
        # Weighted average (70% field confidence, 30% fraud confidence)
        overall_confidence = (avg_field_confidence * 0.7) + (fraud_confidence * 0.3)
        
        return min(0.99, max(0.01, overall_confidence))
    
    def _generate_recommendations(
        self, 
        fields: Dict[str, ExtractedField], 
        fraud_analysis: FraudAnalysis, 
        validation_result: ValidationResult
    ) -> List[str]:
        """Generate processing recommendations"""
        recommendations = []
        
        # Field-based recommendations
        if not fields:
            recommendations.append("⚠️ No fields extracted - manual entry required")
        else:
            low_confidence_fields = [name for name, field in fields.items() if field.confidence < 0.7]
            if low_confidence_fields:
                recommendations.append(f"📋 Review low confidence fields: {', '.join(low_confidence_fields)}")
        
        # Fraud-based recommendations
        if fraud_analysis.is_fraudulent:
            recommendations.append("🚨 Document shows signs of fraud - manual verification required")
        elif fraud_analysis.requires_manual_review:
            recommendations.append("👁️ Manual review recommended for authenticity verification")
        
        # Validation-based recommendations
        if validation_result.errors:
            recommendations.append("❌ Validation errors found - review highlighted fields")
        elif validation_result.warnings:
            recommendations.append("⚠️ Validation warnings - check flagged fields")
        
        # Default recommendation
        if not recommendations:
            recommendations.append("✅ Document processing completed successfully")
        
        return recommendations
    
    def _bytes_to_image(self, image_data: bytes) -> Optional[np.ndarray]:
        """Convert bytes to OpenCV image"""
        try:
            nparr = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            return image
        except Exception as e:
            self.logger.error(f"❌ Failed to decode image: {e}")
            return None
    
    def _preprocess_image(self, image: np.ndarray, document_type: DocumentType) -> np.ndarray:
        """Preprocess image for better OCR results"""
        try:
            # Basic preprocessing
            processed = image.copy()
            
            # Resize if too large
            max_size = 2048
            height, width = processed.shape[:2]
            if max(height, width) > max_size:
                scale = max_size / max(height, width)
                new_width = int(width * scale)
                new_height = int(height * scale)
                processed = cv2.resize(processed, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
            
            return processed
            
        except Exception as e:
            self.logger.error(f"❌ Image preprocessing failed: {e}")
            return image
    
    def _detect_image_format(self, image_data: bytes) -> str:
        """Detect image format from bytes"""
        if image_data.startswith(b'\xff\xd8\xff'):
            return "JPEG"
        elif image_data.startswith(b'\x89PNG'):
            return "PNG"
        elif image_data.startswith(b'GIF'):
            return "GIF"
        elif image_data.startswith(b'BM'):
            return "BMP"
        else:
            return "Unknown"
    
    def _create_error_result(self, request: DocumentProcessingRequest, errors: List[str]) -> DocumentExtractionResult:
        """Create error result"""
        return DocumentExtractionResult(
            status=ProcessingStatus.FAILED,
            document_type=request.document_type,
            extracted_fields={},
            fraud_analysis=FraudAnalysis(
                is_authentic=False,
                is_fraudulent=False,
                is_altered=False,
                confidence_score=0.0,
                authenticity_score=0.0,
                alteration_score=0.0,
                requires_manual_review=True
            ),
            face_analysis=None,
            validation_result=ValidationResult(
                is_valid=False,
                is_consistent=False,
                errors=errors
            ),
            processing_metadata=ProcessingMetadata(
                processing_method="error",
                processing_time=0.0,
                model_version="1.0.0",
                request_id=request.request_id,
                application_id=request.application_id
            ),
            recommendations=["❌ Processing failed - see errors for details"],
            confidence=0.0
        )
