from typing import Dict, List, Optional, Any
import asyncio
import json
import logging
from datetime import datetime

import aiohttp
from aiohttp import ClientTimeout, ClientError

from app.core.config import Settings

logger = logging.getLogger(__name__)

class LaravelAPIService:
    def __init__(self, settings: Settings):
        self.settings = settings
        self.base_url = settings.LARAVEL_API_BASE_URL
        self.timeout = ClientTimeout(total=settings.LARAVEL_API_TIMEOUT)
        
    async def save_document_nomination(
        self,
        document_data: Dict[str, Any],
        user_token: str
    ) -> Dict[str, Any]:
        try:
            logger.info(f"Saving document nomination to Laravel API: {document_data.get('document_type_key')}")
            
            headers = {
                'Authorization': f'Bearer {user_token}',
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            
            # Format data for Laravel API
            api_payload = {
                'application_id': document_data.get('application_id'),
                'document_type_key': document_data.get('document_type_key'),
                'document_data': document_data.get('document_data', {}),
                'processing_method': document_data.get('processing_method', 'manual'),
                'validation_results': document_data.get('validation_results', {}),
                'confidence_score': document_data.get('confidence_score', 1.0),
                'extraction_metadata': {
                    'extraction_confidence': document_data.get('extraction_confidence'),
                    'fraud_analysis': document_data.get('fraud_analysis'),
                    'processing_timestamp': datetime.utcnow().isoformat()
                }
            }
            
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                url = f"{self.base_url}/documents/nominate"
                
                async with session.post(url, json=api_payload, headers=headers) as response:
                    response_data = await response.json()
                    
                    if response.status == 200:
                        logger.info(f"Document nomination saved successfully: {response_data}")
                        return {
                            'success': True,
                            'data': response_data,
                            'status_code': response.status
                        }
                    else:
                        logger.error(f"Laravel API error: {response.status} - {response_data}")
                        return {
                            'success': False,
                            'error': response_data,
                            'status_code': response.status
                        }
                        
        except ClientError as e:
            logger.error(f"HTTP client error saving document nomination: {str(e)}")
            return {
                'success': False,
                'error': f'HTTP client error: {str(e)}',
                'status_code': 500
            }
        except Exception as e:
            logger.error(f"Unexpected error saving document nomination: {str(e)}")
            return {
                'success': False,
                'error': f'Unexpected error: {str(e)}',
                'status_code': 500
            }
    
    async def save_document_file(
        self,
        file_data: bytes,
        filename: str,
        application_id: int,
        document_type: str,
        user_token: str
    ) -> Dict[str, Any]:
        try:
            logger.info(f"Uploading document file to Laravel API: {filename}")
            
            headers = {
                'Authorization': f'Bearer {user_token}',
                'Accept': 'application/json'
            }
            
            # Create multipart form data
            data = aiohttp.FormData()
            data.add_field('application_id', str(application_id))
            data.add_field('document_type', document_type)
            data.add_field('document_file', file_data, filename=filename, content_type='image/jpeg')
            
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                url = f"{self.base_url}/documents/upload-file"
                
                async with session.post(url, data=data, headers=headers) as response:
                    response_data = await response.json()
                    
                    if response.status == 200:
                        logger.info(f"Document file uploaded successfully: {response_data}")
                        return {
                            'success': True,
                            'data': response_data,
                            'status_code': response.status
                        }
                    else:
                        logger.error(f"Laravel API file upload error: {response.status} - {response_data}")
                        return {
                            'success': False,
                            'error': response_data,
                            'status_code': response.status
                        }
                        
        except ClientError as e:
            logger.error(f"HTTP client error uploading document file: {str(e)}")
            return {
                'success': False,
                'error': f'HTTP client error: {str(e)}',
                'status_code': 500
            }
        except Exception as e:
            logger.error(f"Unexpected error uploading document file: {str(e)}")
            return {
                'success': False,
                'error': f'Unexpected error: {str(e)}',
                'status_code': 500
            }
    
    async def get_application_data(
        self,
        application_id: int,
        user_token: str
    ) -> Dict[str, Any]:
        try:
            logger.info(f"Fetching application data from Laravel API: {application_id}")
            
            headers = {
                'Authorization': f'Bearer {user_token}',
                'Accept': 'application/json'
            }
            
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                url = f"{self.base_url}/applications/{application_id}/form-data"
                
                async with session.get(url, headers=headers) as response:
                    response_data = await response.json()
                    
                    if response.status == 200:
                        logger.info(f"Application data fetched successfully")
                        return {
                            'success': True,
                            'data': response_data,
                            'status_code': response.status
                        }
                    else:
                        logger.error(f"Laravel API error fetching application data: {response.status} - {response_data}")
                        return {
                            'success': False,
                            'error': response_data,
                            'status_code': response.status
                        }
                        
        except ClientError as e:
            logger.error(f"HTTP client error fetching application data: {str(e)}")
            return {
                'success': False,
                'error': f'HTTP client error: {str(e)}',
                'status_code': 500
            }
        except Exception as e:
            logger.error(f"Unexpected error fetching application data: {str(e)}")
            return {
                'success': False,
                'error': f'Unexpected error: {str(e)}',
                'status_code': 500
            }
    
    async def validate_user_access(
        self,
        application_id: int,
        user_token: str
    ) -> Dict[str, Any]:
        try:
            logger.info(f"Validating user access for application: {application_id}")
            
            headers = {
                'Authorization': f'Bearer {user_token}',
                'Accept': 'application/json'
            }
            
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                url = f"{self.base_url}/applications/{application_id}/access-check"
                
                async with session.get(url, headers=headers) as response:
                    response_data = await response.json()
                    
                    if response.status == 200:
                        logger.info(f"User access validated successfully")
                        return {
                            'success': True,
                            'has_access': response_data.get('has_access', False),
                            'user_type': response_data.get('user_type'),
                            'status_code': response.status
                        }
                    else:
                        logger.error(f"Laravel API error validating user access: {response.status} - {response_data}")
                        return {
                            'success': False,
                            'has_access': False,
                            'error': response_data,
                            'status_code': response.status
                        }
                        
        except ClientError as e:
            logger.error(f"HTTP client error validating user access: {str(e)}")
            return {
                'success': False,
                'has_access': False,
                'error': f'HTTP client error: {str(e)}',
                'status_code': 500
            }
        except Exception as e:
            logger.error(f"Unexpected error validating user access: {str(e)}")
            return {
                'success': False,
                'has_access': False,
                'error': f'Unexpected error: {str(e)}',
                'status_code': 500
            }
    
    async def get_document_types(
        self,
        application_id: int,
        user_token: str
    ) -> Dict[str, Any]:
        try:
            logger.info(f"Fetching document types from Laravel API for application: {application_id}")
            
            headers = {
                'Authorization': f'Bearer {user_token}',
                'Accept': 'application/json'
            }
            
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                url = f"{self.base_url}/applications/{application_id}/documents"
                
                async with session.get(url, headers=headers) as response:
                    response_data = await response.json()
                    
                    if response.status == 200:
                        logger.info(f"Document types fetched successfully")
                        return {
                            'success': True,
                            'data': response_data,
                            'status_code': response.status
                        }
                    else:
                        logger.error(f"Laravel API error fetching document types: {response.status} - {response_data}")
                        return {
                            'success': False,
                            'error': response_data,
                            'status_code': response.status
                        }
                        
        except ClientError as e:
            logger.error(f"HTTP client error fetching document types: {str(e)}")
            return {
                'success': False,
                'error': f'HTTP client error: {str(e)}',
                'status_code': 500
            }
        except Exception as e:
            logger.error(f"Unexpected error fetching document types: {str(e)}")
            return {
                'success': False,
                'error': f'Unexpected error: {str(e)}',
                'status_code': 500
            }
    
    async def health_check(self) -> Dict[str, Any]:
        try:
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                url = f"{self.base_url}/health"
                
                async with session.get(url) as response:
                    if response.status == 200:
                        return {
                            'success': True,
                            'status': 'healthy',
                            'status_code': response.status
                        }
                    else:
                        return {
                            'success': False,
                            'status': 'unhealthy',
                            'status_code': response.status
                        }
                        
        except Exception as e:
            logger.error(f"Laravel API health check failed: {str(e)}")
            return {
                'success': False,
                'status': 'unhealthy',
                'error': str(e),
                'status_code': 500
            }
