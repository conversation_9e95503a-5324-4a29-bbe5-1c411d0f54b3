"""
Document Training Service - Comprehensive ML model training for document processing
Handles data labeling, training data collection, and model retraining
"""

import asyncio
import json
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import uuid

import cv2
import numpy as np
import structlog
import torch
import torch.nn as nn
import torch.optim as optim
from PIL import Image
from sklearn.model_selection import train_test_split
from torch.utils.data import DataLoader, Dataset
from torchvision import transforms

from app.core.config import get_settings
from app.database.connection import database_manager
from app.ml.training_manager import training_manager
from app.models.document_models import DocumentType, ExtractedField, ExtractionMethod

logger = structlog.get_logger()


class DocumentTrainingService:
    """Service for training ML models with labeled document data"""
    
    def __init__(self):
        self.settings = get_settings()
        self.training_data_path = Path(self.settings.TRAINING_DATA_PATH)
        self.labeled_data_path = self.training_data_path / "labeled"
        self.model_path = Path(self.settings.MODEL_PATH)
        
        # Create directories
        self.training_data_path.mkdir(parents=True, exist_ok=True)
        self.labeled_data_path.mkdir(parents=True, exist_ok=True)
        self.model_path.mkdir(parents=True, exist_ok=True)
        
        # Training configuration
        self.min_samples_per_document_type = 10
        self.validation_split = 0.2
        self.batch_size = 8
        self.learning_rate = 0.001
        self.epochs = 50
        
    async def create_labeling_session(self, document_type: str, user_id: int) -> Dict[str, Any]:
        """Create a new document labeling session"""
        
        session_id = str(uuid.uuid4())
        session_data = {
            'session_id': session_id,
            'document_type': document_type,
            'user_id': user_id,
            'created_at': datetime.now().isoformat(),
            'status': 'active',
            'labeled_count': 0,
            'target_count': 20,  # Target number of documents to label
            'instructions': self._get_labeling_instructions(document_type)
        }
        
        # Save session data
        session_file = self.labeled_data_path / f"session_{session_id}.json"
        with open(session_file, 'w') as f:
            json.dump(session_data, f, indent=2)
        
        logger.info("Created labeling session", 
                   session_id=session_id, 
                   document_type=document_type)
        
        return session_data
    
    async def submit_labeled_document(self, session_id: str, document_data: Dict[str, Any]) -> Dict[str, Any]:
        """Submit a labeled document for training"""
        
        try:
            # Load session data
            session_file = self.labeled_data_path / f"session_{session_id}.json"
            if not session_file.exists():
                raise ValueError(f"Session {session_id} not found")
            
            with open(session_file, 'r') as f:
                session_data = json.load(f)
            
            # Validate document data
            self._validate_labeled_document(document_data, session_data['document_type'])
            
            # Save labeled document
            document_id = str(uuid.uuid4())
            labeled_document = {
                'document_id': document_id,
                'session_id': session_id,
                'document_type': session_data['document_type'],
                'labeled_at': datetime.now().isoformat(),
                'user_id': session_data['user_id'],
                'image_path': document_data['image_path'],
                'labeled_fields': document_data['labeled_fields'],
                'bounding_boxes': document_data.get('bounding_boxes', {}),
                'quality_score': document_data.get('quality_score', 1.0),
                'notes': document_data.get('notes', ''),
                'validation_status': 'pending'
            }
            
            # Save labeled document
            document_file = self.labeled_data_path / f"labeled_{document_id}.json"
            with open(document_file, 'w') as f:
                json.dump(labeled_document, f, indent=2)
            
            # Update session
            session_data['labeled_count'] += 1
            session_data['last_labeled'] = datetime.now().isoformat()
            
            with open(session_file, 'w') as f:
                json.dump(session_data, f, indent=2)
            
            # Check if we should trigger training
            await self._check_training_trigger(session_data['document_type'])
            
            logger.info("Labeled document submitted", 
                       document_id=document_id,
                       session_id=session_id)
            
            return {
                'success': True,
                'document_id': document_id,
                'session_progress': {
                    'labeled_count': session_data['labeled_count'],
                    'target_count': session_data['target_count'],
                    'completion_percentage': (session_data['labeled_count'] / session_data['target_count']) * 100
                }
            }
            
        except Exception as e:
            logger.error("Failed to submit labeled document", 
                        session_id=session_id, 
                        error=str(e))
            raise
    
    async def get_training_statistics(self) -> Dict[str, Any]:
        """Get training data statistics"""
        
        stats = {
            'total_labeled_documents': 0,
            'by_document_type': {},
            'training_readiness': {},
            'recent_activity': [],
            'model_performance': {}
        }
        
        # Count labeled documents
        for labeled_file in self.labeled_data_path.glob("labeled_*.json"):
            try:
                with open(labeled_file, 'r') as f:
                    data = json.load(f)
                
                doc_type = data['document_type']
                stats['total_labeled_documents'] += 1
                
                if doc_type not in stats['by_document_type']:
                    stats['by_document_type'][doc_type] = 0
                stats['by_document_type'][doc_type] += 1
                
            except Exception as e:
                logger.warning(f"Failed to read labeled file {labeled_file}: {e}")
        
        # Check training readiness
        for doc_type, count in stats['by_document_type'].items():
            stats['training_readiness'][doc_type] = {
                'current_samples': count,
                'required_samples': self.min_samples_per_document_type,
                'ready_for_training': count >= self.min_samples_per_document_type,
                'completion_percentage': min(100, (count / self.min_samples_per_document_type) * 100)
            }
        
        return stats
    
    async def train_document_model(self, document_type: str, force: bool = False) -> Dict[str, Any]:
        """Train ML model for specific document type"""
        
        logger.info("Starting document model training", document_type=document_type)
        
        try:
            # Check if we have enough data
            labeled_files = list(self.labeled_data_path.glob("labeled_*.json"))
            document_samples = []
            
            for labeled_file in labeled_files:
                with open(labeled_file, 'r') as f:
                    data = json.load(f)
                if data['document_type'] == document_type:
                    document_samples.append(data)
            
            if len(document_samples) < self.min_samples_per_document_type and not force:
                raise ValueError(f"Insufficient training data: {len(document_samples)} samples, need {self.min_samples_per_document_type}")
            
            # Prepare training dataset
            training_dataset = await self._prepare_training_dataset(document_samples)
            
            # Split into train/validation
            train_data, val_data = train_test_split(
                training_dataset, 
                test_size=self.validation_split, 
                random_state=42
            )
            
            # Create data loaders
            train_loader = self._create_data_loader(train_data, shuffle=True)
            val_loader = self._create_data_loader(val_data, shuffle=False)
            
            # Initialize model
            model = self._create_document_model(document_type)
            
            # Train model
            training_results = await self._train_model(
                model, train_loader, val_loader, document_type
            )
            
            # Save trained model
            model_path = await self._save_trained_model(model, document_type, training_results)
            
            # Update model registry
            await self._update_model_registry(document_type, model_path, training_results)
            
            logger.info("Document model training completed", 
                       document_type=document_type,
                       samples_used=len(document_samples))
            
            return {
                'success': True,
                'document_type': document_type,
                'training_samples': len(document_samples),
                'model_path': str(model_path),
                'training_results': training_results,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error("Document model training failed", 
                        document_type=document_type, 
                        error=str(e))
            raise
    
    def _get_labeling_instructions(self, document_type: str) -> Dict[str, Any]:
        """Get labeling instructions for document type"""
        
        instructions = {
            'p60': {
                'title': 'P60 Tax Document Labeling',
                'description': 'Label key fields in P60 tax documents for training',
                'required_fields': [
                    'employee_name', 'employer_name', 'tax_year', 
                    'total_pay', 'tax_deducted', 'ni_number', 'employee_address'
                ],
                'field_descriptions': {
                    'employee_name': 'Full name of the employee (usually after "To employee:")',
                    'employer_name': 'Name of the employer/company',
                    'tax_year': 'Tax year (e.g., 2023-24)',
                    'total_pay': 'Total pay for the year',
                    'tax_deducted': 'Total tax deducted',
                    'ni_number': 'National Insurance number',
                    'employee_address': 'Employee address including postcode'
                },
                'tips': [
                    'Look for "To employee:" section for employee details',
                    'Tax year is usually at the top of the document',
                    'Pay and tax figures are in the main table',
                    'Address should include postcode for validation'
                ]
            },
            'p45': {
                'title': 'P45 Tax Document Labeling',
                'description': 'Label key fields in P45 tax documents for training',
                'required_fields': [
                    'employee_name', 'employer_name', 'leaving_date',
                    'total_pay', 'tax_deducted', 'ni_number'
                ],
                'field_descriptions': {
                    'employee_name': 'Full name of the employee',
                    'employer_name': 'Name of the employer/company',
                    'leaving_date': 'Date employee left the job',
                    'total_pay': 'Total pay to leaving date',
                    'tax_deducted': 'Total tax deducted to leaving date',
                    'ni_number': 'National Insurance number'
                }
            },
            'passport': {
                'title': 'Passport Document Labeling',
                'description': 'Label key fields in passport documents for training',
                'required_fields': [
                    'passport_number', 'surname', 'given_names', 
                    'date_of_birth', 'issue_date', 'expiry_date', 'nationality'
                ]
            }
        }
        
        return instructions.get(document_type, {
            'title': f'{document_type.upper()} Document Labeling',
            'description': f'Label key fields in {document_type} documents for training',
            'required_fields': [],
            'tips': ['Ensure all text is clearly readable', 'Mark uncertain fields with low confidence']
        })
    
    def _validate_labeled_document(self, document_data: Dict[str, Any], document_type: str):
        """Validate labeled document data"""
        
        required_keys = ['image_path', 'labeled_fields']
        for key in required_keys:
            if key not in document_data:
                raise ValueError(f"Missing required key: {key}")
        
        # Validate image path exists
        image_path = Path(document_data['image_path'])
        if not image_path.exists():
            raise ValueError(f"Image file not found: {image_path}")
        
        # Validate labeled fields
        labeled_fields = document_data['labeled_fields']
        if not isinstance(labeled_fields, dict):
            raise ValueError("labeled_fields must be a dictionary")
        
        # Check for required fields based on document type
        instructions = self._get_labeling_instructions(document_type)
        required_fields = instructions.get('required_fields', [])
        
        missing_fields = []
        for field in required_fields:
            if field not in labeled_fields or not labeled_fields[field].get('value'):
                missing_fields.append(field)
        
        if missing_fields:
            logger.warning("Missing required fields", 
                          missing_fields=missing_fields,
                          document_type=document_type)
    
    async def _check_training_trigger(self, document_type: str):
        """Check if we should trigger training for document type"""
        
        # Count labeled documents for this type
        labeled_files = list(self.labeled_data_path.glob("labeled_*.json"))
        count = 0
        
        for labeled_file in labeled_files:
            try:
                with open(labeled_file, 'r') as f:
                    data = json.load(f)
                if data['document_type'] == document_type:
                    count += 1
            except:
                continue
        
        # Trigger training if we have enough samples
        if count >= self.min_samples_per_document_type:
            logger.info("Triggering automatic training", 
                       document_type=document_type, 
                       sample_count=count)
            
            # Schedule training in background
            asyncio.create_task(self.train_document_model(document_type))


    async def _prepare_training_dataset(self, document_samples: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Prepare training dataset from labeled samples"""

        training_data = []

        for sample in document_samples:
            try:
                # Load image
                image_path = Path(sample['image_path'])
                image = cv2.imread(str(image_path))
                if image is None:
                    logger.warning(f"Could not load image: {image_path}")
                    continue

                # Prepare training sample
                training_sample = {
                    'image': image,
                    'document_type': sample['document_type'],
                    'labeled_fields': sample['labeled_fields'],
                    'bounding_boxes': sample.get('bounding_boxes', {}),
                    'quality_score': sample.get('quality_score', 1.0)
                }

                training_data.append(training_sample)

            except Exception as e:
                logger.warning(f"Failed to prepare training sample: {e}")
                continue

        return training_data

    def _create_data_loader(self, dataset: List[Dict[str, Any]], shuffle: bool = True) -> DataLoader:
        """Create PyTorch data loader"""

        class DocumentDataset(Dataset):
            def __init__(self, data, transform=None):
                self.data = data
                self.transform = transform or transforms.Compose([
                    transforms.ToPILImage(),
                    transforms.Resize((224, 224)),
                    transforms.ToTensor(),
                    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
                ])

            def __len__(self):
                return len(self.data)

            def __getitem__(self, idx):
                sample = self.data[idx]
                image = sample['image']

                # Convert BGR to RGB
                if len(image.shape) == 3:
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

                # Apply transforms
                image_tensor = self.transform(image)

                return {
                    'image': image_tensor,
                    'labeled_fields': sample['labeled_fields'],
                    'document_type': sample['document_type'],
                    'quality_score': sample['quality_score']
                }

        dataset_obj = DocumentDataset(dataset)
        return DataLoader(dataset_obj, batch_size=self.batch_size, shuffle=shuffle)

    def _create_document_model(self, document_type: str) -> nn.Module:
        """Create neural network model for document processing"""

        class DocumentFieldExtractor(nn.Module):
            def __init__(self, num_classes=10):  # Adjust based on field types
                super().__init__()

                # Feature extraction backbone
                self.backbone = nn.Sequential(
                    nn.Conv2d(3, 64, 3, padding=1),
                    nn.ReLU(),
                    nn.MaxPool2d(2),
                    nn.Conv2d(64, 128, 3, padding=1),
                    nn.ReLU(),
                    nn.MaxPool2d(2),
                    nn.Conv2d(128, 256, 3, padding=1),
                    nn.ReLU(),
                    nn.MaxPool2d(2),
                    nn.AdaptiveAvgPool2d((7, 7))
                )

                # Classification head
                self.classifier = nn.Sequential(
                    nn.Flatten(),
                    nn.Linear(256 * 7 * 7, 512),
                    nn.ReLU(),
                    nn.Dropout(0.5),
                    nn.Linear(512, 256),
                    nn.ReLU(),
                    nn.Dropout(0.3),
                    nn.Linear(256, num_classes)
                )

            def forward(self, x):
                features = self.backbone(x)
                output = self.classifier(features)
                return output

        return DocumentFieldExtractor()

    async def _train_model(self, model: nn.Module, train_loader: DataLoader,
                          val_loader: DataLoader, document_type: str) -> Dict[str, Any]:
        """Train the document processing model"""

        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = model.to(device)

        criterion = nn.CrossEntropyLoss()
        optimizer = optim.Adam(model.parameters(), lr=self.learning_rate)
        scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=10, gamma=0.1)

        training_history = {
            'train_losses': [],
            'val_losses': [],
            'train_accuracies': [],
            'val_accuracies': []
        }

        best_val_loss = float('inf')

        for epoch in range(self.epochs):
            # Training phase
            model.train()
            train_loss = 0.0
            train_correct = 0
            train_total = 0

            for batch in train_loader:
                images = batch['image'].to(device)
                # For now, use quality score as target (simplified)
                targets = torch.tensor([sample['quality_score'] for sample in batch['labeled_fields']],
                                     dtype=torch.long).to(device)

                optimizer.zero_grad()
                outputs = model(images)
                loss = criterion(outputs, targets)
                loss.backward()
                optimizer.step()

                train_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                train_total += targets.size(0)
                train_correct += (predicted == targets).sum().item()

            # Validation phase
            model.eval()
            val_loss = 0.0
            val_correct = 0
            val_total = 0

            with torch.no_grad():
                for batch in val_loader:
                    images = batch['image'].to(device)
                    targets = torch.tensor([sample['quality_score'] for sample in batch['labeled_fields']],
                                         dtype=torch.long).to(device)

                    outputs = model(images)
                    loss = criterion(outputs, targets)

                    val_loss += loss.item()
                    _, predicted = torch.max(outputs.data, 1)
                    val_total += targets.size(0)
                    val_correct += (predicted == targets).sum().item()

            # Calculate metrics
            avg_train_loss = train_loss / len(train_loader)
            avg_val_loss = val_loss / len(val_loader)
            train_accuracy = 100 * train_correct / train_total if train_total > 0 else 0
            val_accuracy = 100 * val_correct / val_total if val_total > 0 else 0

            # Store history
            training_history['train_losses'].append(avg_train_loss)
            training_history['val_losses'].append(avg_val_loss)
            training_history['train_accuracies'].append(train_accuracy)
            training_history['val_accuracies'].append(val_accuracy)

            # Update learning rate
            scheduler.step()

            # Save best model
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss

            logger.info(f"Epoch {epoch+1}/{self.epochs}",
                       train_loss=avg_train_loss,
                       val_loss=avg_val_loss,
                       train_acc=train_accuracy,
                       val_acc=val_accuracy)

        return {
            'final_train_loss': training_history['train_losses'][-1],
            'final_val_loss': training_history['val_losses'][-1],
            'final_train_accuracy': training_history['train_accuracies'][-1],
            'final_val_accuracy': training_history['val_accuracies'][-1],
            'best_val_loss': best_val_loss,
            'training_history': training_history,
            'epochs_trained': self.epochs
        }

    async def _save_trained_model(self, model: nn.Module, document_type: str,
                                 training_results: Dict[str, Any]) -> Path:
        """Save trained model to disk"""

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        model_filename = f"{document_type}_model_{timestamp}.pth"
        model_path = self.model_path / model_filename

        # Save model state
        torch.save({
            'model_state_dict': model.state_dict(),
            'document_type': document_type,
            'training_results': training_results,
            'timestamp': datetime.now().isoformat(),
            'model_version': '1.0.0'
        }, model_path)

        # Create symlink to latest model
        latest_path = self.model_path / f"{document_type}_latest.pth"
        if latest_path.exists():
            latest_path.unlink()
        latest_path.symlink_to(model_filename)

        logger.info("Model saved", model_path=str(model_path))
        return model_path

    async def _update_model_registry(self, document_type: str, model_path: Path,
                                   training_results: Dict[str, Any]):
        """Update model registry with new trained model"""

        registry_file = self.model_path / "model_registry.json"

        # Load existing registry
        registry = {}
        if registry_file.exists():
            with open(registry_file, 'r') as f:
                registry = json.load(f)

        # Update registry
        registry[document_type] = {
            'model_path': str(model_path),
            'trained_at': datetime.now().isoformat(),
            'training_results': training_results,
            'status': 'active',
            'version': '1.0.0'
        }

        # Save registry
        with open(registry_file, 'w') as f:
            json.dump(registry, f, indent=2)

        logger.info("Model registry updated", document_type=document_type)


# Global training service instance
document_training_service = DocumentTrainingService()
