from typing import Dict, List, Optional, Any, Tuple
import asyncio
import json
import logging
from datetime import datetime
from pathlib import Path

import numpy as np
import cv2
from PIL import Image

from app.core.config import Settings
from app.ml.ocr_engine import OCREngine
from app.ml.fraud_detector import FraudDetector
from app.ml.model_manager import ModelManager
from app.services.laravel_api_service import LaravelAPIService
from app.utils.file_handler import FileHandler

logger = logging.getLogger(__name__)

class DocumentExtractionService:
    def __init__(self, settings: Settings):
        self.settings = settings
        self.ocr_engine = OCREngine(settings)
        self.fraud_detector = FraudDetector(settings)
        self.model_manager = ModelManager(settings)
        self.laravel_api = LaravelAPIService(settings)
        self.file_handler = FileHandler(settings)
        
    async def process_document_ai_path(
        self,
        document_image: np.ndarray,
        document_type: str,
        application_data: Dict[str, Any],
        user_token: str
    ) -> Dict[str, Any]:
        try:
            logger.info(f"Starting AI document processing for type: {document_type}")
            
            # Step 1: Fraud Detection and Authenticity Check
            fraud_analysis = await self.fraud_detector.analyze_document(
                document_image, document_type
            )
            
            # Step 2: OCR Data Extraction
            ocr_results = await self.ocr_engine.extract_document_data(
                document_image, document_type
            )
            
            # Step 3: Data Validation and Matching
            extracted_data = ocr_results.get('extracted_data', {})
            validation_results = await self._validate_extracted_data(
                extracted_data, application_data, document_type
            )
            
            # Step 4: Generate Smart Recommendations
            recommendations = await self._generate_recommendations(
                extracted_data, validation_results, fraud_analysis
            )
            
            # Step 5: Prepare Review Data for User
            review_data = {
                'document_type': document_type,
                'extraction_confidence': ocr_results.get('confidence', 0.0),
                'fraud_analysis': fraud_analysis,
                'extracted_fields': self._format_extracted_fields(extracted_data, document_type),
                'validation_results': validation_results,
                'recommendations': recommendations,
                'requires_manual_review': self._requires_manual_review(fraud_analysis, validation_results),
                'processing_timestamp': datetime.utcnow().isoformat()
            }
            
            logger.info(f"AI document processing completed for type: {document_type}")
            return review_data
            
        except Exception as e:
            logger.error(f"Error in AI document processing: {str(e)}")
            raise
    
    async def process_document_manual_path(
        self,
        document_type: str,
        manual_data: Dict[str, Any],
        application_data: Dict[str, Any],
        user_token: str,
        document_file: Optional[bytes] = None
    ) -> Dict[str, Any]:
        try:
            logger.info(f"Starting manual document processing for type: {document_type}")
            
            # Step 1: Validate Manual Input
            validation_results = await self._validate_manual_data(
                manual_data, application_data, document_type
            )
            
            # Step 2: Store Document File (if provided)
            file_path = None
            if document_file:
                file_path = await self._store_document_file(
                    document_file, document_type, application_data.get('application_id')
                )
            
            # Step 3: Prepare Data for Laravel API
            formatted_data = self._format_manual_data_for_api(
                manual_data, document_type, validation_results, file_path
            )
            
            # Step 4: Send to Laravel API Interface
            api_response = await self.laravel_api.save_document_nomination(
                formatted_data, user_token
            )
            
            result = {
                'document_type': document_type,
                'processing_method': 'manual',
                'validation_results': validation_results,
                'api_response': api_response,
                'file_stored': file_path is not None,
                'processing_timestamp': datetime.utcnow().isoformat()
            }
            
            logger.info(f"Manual document processing completed for type: {document_type}")
            return result
            
        except Exception as e:
            logger.error(f"Error in manual document processing: {str(e)}")
            raise
    
    async def submit_ai_reviewed_document(
        self,
        review_data: Dict[str, Any],
        user_corrections: Dict[str, Any],
        application_data: Dict[str, Any],
        user_token: str
    ) -> Dict[str, Any]:
        try:
            logger.info("Submitting AI-reviewed document with user corrections")
            
            # Step 1: Merge AI extracted data with user corrections
            final_data = self._merge_ai_and_user_data(
                review_data['extracted_fields'], user_corrections
            )
            
            # Step 2: Final validation
            validation_results = await self._validate_final_data(
                final_data, application_data, review_data['document_type']
            )
            
            # Step 3: Store training data for quarterly model improvement
            await self._store_training_data(
                review_data, user_corrections, final_data, validation_results
            )
            
            # Step 4: Format for Laravel API
            formatted_data = self._format_ai_data_for_api(
                final_data, review_data, validation_results
            )
            
            # Step 5: Send to Laravel API Interface
            api_response = await self.laravel_api.save_document_nomination(
                formatted_data, user_token
            )
            
            result = {
                'document_type': review_data['document_type'],
                'processing_method': 'ai_assisted',
                'final_data': final_data,
                'validation_results': validation_results,
                'api_response': api_response,
                'training_data_stored': True,
                'processing_timestamp': datetime.utcnow().isoformat()
            }
            
            logger.info("AI-reviewed document submission completed")
            return result
            
        except Exception as e:
            logger.error(f"Error in AI-reviewed document submission: {str(e)}")
            raise
    
    async def _validate_extracted_data(
        self,
        extracted_data: Dict[str, Any],
        application_data: Dict[str, Any],
        document_type: str
    ) -> Dict[str, Any]:
        validation_results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'field_matches': {}
        }
        
        # Validate against application data
        for field, value in extracted_data.items():
            if field in application_data:
                app_value = application_data[field]
                match_score = self._calculate_field_match_score(value, app_value)
                validation_results['field_matches'][field] = {
                    'extracted': value,
                    'application': app_value,
                    'match_score': match_score,
                    'matches': match_score > 0.8
                }
                
                if match_score < 0.5:
                    validation_results['errors'].append(
                        f"{field}: Extracted value '{value}' doesn't match application data '{app_value}'"
                    )
                    validation_results['is_valid'] = False
                elif match_score < 0.8:
                    validation_results['warnings'].append(
                        f"{field}: Extracted value '{value}' partially matches application data '{app_value}'"
                    )
        
        return validation_results
    
    async def _validate_manual_data(
        self,
        manual_data: Dict[str, Any],
        application_data: Dict[str, Any],
        document_type: str
    ) -> Dict[str, Any]:
        validation_results = {
            'is_valid': True,
            'errors': [],
            'warnings': []
        }
        
        # Basic validation for required fields
        required_fields = self._get_required_fields(document_type)
        for field in required_fields:
            if field not in manual_data or not manual_data[field]:
                validation_results['errors'].append(f"Required field '{field}' is missing")
                validation_results['is_valid'] = False
        
        return validation_results
    
    async def _validate_final_data(
        self,
        final_data: Dict[str, Any],
        application_data: Dict[str, Any],
        document_type: str
    ) -> Dict[str, Any]:
        return await self._validate_extracted_data(final_data, application_data, document_type)
    
    def _format_extracted_fields(
        self,
        extracted_data: Dict[str, Any],
        document_type: str
    ) -> Dict[str, Any]:
        formatted_fields = {}
        field_config = self._get_document_field_config(document_type)
        
        for field, value in extracted_data.items():
            if field in field_config:
                formatted_fields[field] = {
                    'value': value,
                    'label': field_config[field].get('label', field.replace('_', ' ').title()),
                    'type': field_config[field].get('type', 'text'),
                    'required': field_config[field].get('required', False),
                    'editable': True
                }
        
        return formatted_fields
    
    def _format_manual_data_for_api(
        self,
        manual_data: Dict[str, Any],
        document_type: str,
        validation_results: Dict[str, Any],
        file_path: Optional[str]
    ) -> Dict[str, Any]:
        return {
            'document_type_key': document_type,
            'document_data': manual_data,
            'processing_method': 'manual',
            'validation_results': validation_results,
            'file_path': file_path,
            'confidence_score': 1.0  # Manual entry assumed to be accurate
        }
    
    def _format_ai_data_for_api(
        self,
        final_data: Dict[str, Any],
        review_data: Dict[str, Any],
        validation_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        return {
            'document_type_key': review_data['document_type'],
            'document_data': final_data,
            'processing_method': 'ai_assisted',
            'validation_results': validation_results,
            'extraction_confidence': review_data.get('extraction_confidence', 0.0),
            'fraud_analysis': review_data.get('fraud_analysis', {}),
            'confidence_score': validation_results.get('overall_confidence', 0.8)
        }
    
    def _merge_ai_and_user_data(
        self,
        ai_extracted: Dict[str, Any],
        user_corrections: Dict[str, Any]
    ) -> Dict[str, Any]:
        final_data = {}
        
        # Start with AI extracted data
        for field, field_data in ai_extracted.items():
            final_data[field] = field_data['value']
        
        # Apply user corrections
        for field, corrected_value in user_corrections.items():
            if corrected_value is not None and corrected_value != '':
                final_data[field] = corrected_value
        
        return final_data
    
    async def _store_training_data(
        self,
        review_data: Dict[str, Any],
        user_corrections: Dict[str, Any],
        final_data: Dict[str, Any],
        validation_results: Dict[str, Any]
    ) -> None:
        try:
            training_record = {
                'timestamp': datetime.utcnow().isoformat(),
                'document_type': review_data['document_type'],
                'ai_extracted': review_data['extracted_fields'],
                'user_corrections': user_corrections,
                'final_data': final_data,
                'validation_results': validation_results,
                'fraud_analysis': review_data.get('fraud_analysis', {}),
                'extraction_confidence': review_data.get('extraction_confidence', 0.0)
            }
            
            # Store in training data directory
            training_dir = Path(self.settings.TRAINING_DATA_PATH)
            training_dir.mkdir(parents=True, exist_ok=True)
            
            filename = f"training_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.json"
            file_path = training_dir / filename
            
            with open(file_path, 'w') as f:
                json.dump(training_record, f, indent=2)
            
            logger.info(f"Training data stored: {file_path}")
            
        except Exception as e:
            logger.error(f"Error storing training data: {str(e)}")
    
    async def _store_document_file(
        self,
        document_file: bytes,
        document_type: str,
        application_id: Optional[int]
    ) -> str:
        try:
            # Generate unique filename
            timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
            filename = f"{document_type}_{application_id}_{timestamp}.jpg"
            
            # Store file using file handler
            file_path = await self.file_handler.save_document(document_file, filename)
            
            logger.info(f"Document file stored: {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"Error storing document file: {str(e)}")
            raise
    
    def _calculate_field_match_score(self, value1: str, value2: str) -> float:
        if not value1 or not value2:
            return 0.0
        
        # Simple fuzzy matching - can be enhanced with more sophisticated algorithms
        value1_clean = str(value1).lower().strip()
        value2_clean = str(value2).lower().strip()
        
        if value1_clean == value2_clean:
            return 1.0
        
        # Calculate similarity based on common characters
        common_chars = set(value1_clean) & set(value2_clean)
        total_chars = set(value1_clean) | set(value2_clean)
        
        if not total_chars:
            return 0.0
        
        return len(common_chars) / len(total_chars)
    
    def _get_required_fields(self, document_type: str) -> List[str]:
        field_configs = {
            'passport': ['document_number', 'expiry_date'],
            'driving_licence': ['licence_number'],
            'birth_certificate': ['full_name', 'date_of_birth'],
            'utility_bill': ['account_holder_name', 'address', 'bill_date']
        }
        return field_configs.get(document_type, [])
    
    def _get_document_field_config(self, document_type: str) -> Dict[str, Dict[str, Any]]:
        configs = {
            'passport': {
                'document_number': {'label': 'Passport Number', 'type': 'text', 'required': True},
                'expiry_date': {'label': 'Expiry Date', 'type': 'date', 'required': True},
                'full_name': {'label': 'Full Name', 'type': 'text', 'required': True},
                'date_of_birth': {'label': 'Date of Birth', 'type': 'date', 'required': True},
                'nationality': {'label': 'Nationality', 'type': 'text', 'required': False}
            },
            'driving_licence': {
                'licence_number': {'label': 'Licence Number', 'type': 'text', 'required': True},
                'full_name': {'label': 'Full Name', 'type': 'text', 'required': True},
                'date_of_birth': {'label': 'Date of Birth', 'type': 'date', 'required': True},
                'address': {'label': 'Address', 'type': 'textarea', 'required': True}
            }
        }
        return configs.get(document_type, {})
    
    async def _generate_recommendations(
        self,
        extracted_data: Dict[str, Any],
        validation_results: Dict[str, Any],
        fraud_analysis: Dict[str, Any]
    ) -> List[str]:
        recommendations = []
        
        if not validation_results['is_valid']:
            recommendations.append("Please review and correct the highlighted fields")
        
        if fraud_analysis.get('authenticity_score', 1.0) < 0.7:
            recommendations.append("Document authenticity is questionable - manual verification recommended")
        
        if fraud_analysis.get('copy_probability', 0.0) > 0.3:
            recommendations.append("Document may be a copy - original document verification recommended")
        
        return recommendations
    
    def _requires_manual_review(
        self,
        fraud_analysis: Dict[str, Any],
        validation_results: Dict[str, Any]
    ) -> bool:
        return (
            not validation_results['is_valid'] or
            fraud_analysis.get('authenticity_score', 1.0) < 0.7 or
            fraud_analysis.get('copy_probability', 0.0) > 0.3
        )
