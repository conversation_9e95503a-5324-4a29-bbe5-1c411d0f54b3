from typing import Dict, List, Optional, Any, Tuple
import asyncio
import json
import logging
import shutil
from datetime import datetime, timedelta
from pathlib import Path
import schedule
import time
from threading import Thread

import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, Dataset
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

from app.core.config import Settings
from app.ml.model_manager import ModelManager

logger = logging.getLogger(__name__)

class TrainingDataset(Dataset):
    def __init__(self, data: List[Dict[str, Any]], labels: List[int]):
        self.data = data
        self.labels = labels
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        return self.data[idx], self.labels[idx]

class ModelTrainingService:
    def __init__(self, settings: Settings):
        self.settings = settings
        self.model_manager = ModelManager(settings)
        self.training_data_path = Path(settings.TRAINING_DATA_PATH)
        self.model_backup_path = Path(settings.MODEL_BACKUP_PATH)
        self.is_training = False
        
        # Create directories
        self.training_data_path.mkdir(parents=True, exist_ok=True)
        self.model_backup_path.mkdir(parents=True, exist_ok=True)
        
        # Schedule quarterly training if enabled
        if settings.QUARTERLY_TRAINING_ENABLED:
            self._schedule_quarterly_training()
    
    def _schedule_quarterly_training(self):
        schedule.every(3).months.do(self._run_quarterly_training)
        
        # Start scheduler in background thread
        def run_scheduler():
            while True:
                schedule.run_pending()
                time.sleep(3600)  # Check every hour
        
        scheduler_thread = Thread(target=run_scheduler, daemon=True)
        scheduler_thread.start()
        logger.info("Quarterly training scheduler started")
    
    async def trigger_manual_training(self, user_id: Optional[int] = None) -> Dict[str, Any]:
        if self.is_training:
            return {
                'success': False,
                'message': 'Training is already in progress',
                'status': 'busy'
            }
        
        try:
            logger.info(f"Manual training triggered by user: {user_id}")
            result = await self._run_training_pipeline()
            
            return {
                'success': True,
                'message': 'Training completed successfully',
                'result': result,
                'triggered_by': user_id,
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Manual training failed: {str(e)}")
            return {
                'success': False,
                'message': f'Training failed: {str(e)}',
                'error': str(e),
                'triggered_by': user_id,
                'timestamp': datetime.utcnow().isoformat()
            }
    
    def _run_quarterly_training(self):
        logger.info("Starting scheduled quarterly training")
        asyncio.create_task(self._run_training_pipeline())
    
    async def _run_training_pipeline(self) -> Dict[str, Any]:
        if self.is_training:
            raise Exception("Training is already in progress")
        
        self.is_training = True
        training_start = datetime.utcnow()
        
        try:
            logger.info("Starting model training pipeline")
            
            # Step 1: Collect and prepare training data
            training_data = await self._collect_training_data()
            
            if len(training_data) < self.settings.MIN_TRAINING_SAMPLES:
                raise Exception(f"Insufficient training data: {len(training_data)} samples (minimum: {self.settings.MIN_TRAINING_SAMPLES})")
            
            # Step 2: Backup current models
            backup_info = await self._backup_current_models()
            
            # Step 3: Prepare datasets
            datasets = await self._prepare_training_datasets(training_data)
            
            # Step 4: Train models
            training_results = {}
            
            # Train fraud detection model
            fraud_results = await self._train_fraud_detection_model(datasets['fraud_detection'])
            training_results['fraud_detection'] = fraud_results
            
            # Train OCR accuracy model
            ocr_results = await self._train_ocr_accuracy_model(datasets['ocr_accuracy'])
            training_results['ocr_accuracy'] = ocr_results
            
            # Train data validation model
            validation_results = await self._train_data_validation_model(datasets['data_validation'])
            training_results['data_validation'] = validation_results
            
            # Step 5: Evaluate and validate new models
            evaluation_results = await self._evaluate_trained_models(training_results)
            
            # Step 6: Deploy new models if they perform better
            deployment_results = await self._deploy_models_if_better(training_results, evaluation_results)
            
            training_end = datetime.utcnow()
            training_duration = (training_end - training_start).total_seconds()
            
            result = {
                'training_duration_seconds': training_duration,
                'training_samples_count': len(training_data),
                'backup_info': backup_info,
                'training_results': training_results,
                'evaluation_results': evaluation_results,
                'deployment_results': deployment_results,
                'models_updated': deployment_results.get('models_deployed', 0) > 0,
                'timestamp': training_end.isoformat()
            }
            
            logger.info(f"Model training pipeline completed successfully in {training_duration:.2f} seconds")
            return result
            
        except Exception as e:
            logger.error(f"Model training pipeline failed: {str(e)}")
            raise
        finally:
            self.is_training = False
    
    async def _collect_training_data(self) -> List[Dict[str, Any]]:
        training_data = []
        
        # Collect all training data files
        for file_path in self.training_data_path.glob("training_*.json"):
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                    training_data.append(data)
            except Exception as e:
                logger.warning(f"Failed to load training data from {file_path}: {str(e)}")
        
        logger.info(f"Collected {len(training_data)} training samples")
        return training_data
    
    async def _backup_current_models(self) -> Dict[str, Any]:
        backup_timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
        backup_dir = self.model_backup_path / f"backup_{backup_timestamp}"
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        # Backup current models
        model_path = Path(self.settings.MODEL_PATH)
        if model_path.exists():
            shutil.copytree(model_path, backup_dir / "models", dirs_exist_ok=True)
        
        backup_info = {
            'backup_timestamp': backup_timestamp,
            'backup_path': str(backup_dir),
            'models_backed_up': list(model_path.glob("*.pkl")) if model_path.exists() else []
        }
        
        logger.info(f"Models backed up to: {backup_dir}")
        return backup_info
    
    async def _prepare_training_datasets(self, training_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        datasets = {
            'fraud_detection': [],
            'ocr_accuracy': [],
            'data_validation': []
        }
        
        for sample in training_data:
            # Prepare fraud detection data
            fraud_sample = {
                'document_type': sample.get('document_type'),
                'fraud_analysis': sample.get('fraud_analysis', {}),
                'user_corrections': sample.get('user_corrections', {}),
                'final_validation': sample.get('validation_results', {})
            }
            datasets['fraud_detection'].append(fraud_sample)
            
            # Prepare OCR accuracy data
            ocr_sample = {
                'document_type': sample.get('document_type'),
                'ai_extracted': sample.get('ai_extracted', {}),
                'user_corrections': sample.get('user_corrections', {}),
                'extraction_confidence': sample.get('extraction_confidence', 0.0)
            }
            datasets['ocr_accuracy'].append(ocr_sample)
            
            # Prepare data validation data
            validation_sample = {
                'document_type': sample.get('document_type'),
                'extracted_data': sample.get('final_data', {}),
                'validation_results': sample.get('validation_results', {}),
                'user_corrections_count': len(sample.get('user_corrections', {}))
            }
            datasets['data_validation'].append(validation_sample)
        
        logger.info(f"Prepared datasets: fraud_detection={len(datasets['fraud_detection'])}, "
                   f"ocr_accuracy={len(datasets['ocr_accuracy'])}, "
                   f"data_validation={len(datasets['data_validation'])}")
        
        return datasets
    
    async def _train_fraud_detection_model(self, dataset: List[Dict[str, Any]]) -> Dict[str, Any]:
        logger.info("Training fraud detection model")
        
        # Mock training implementation - replace with actual ML training
        training_metrics = {
            'samples_trained': len(dataset),
            'accuracy': 0.92 + (len(dataset) * 0.0001),  # Simulate improvement with more data
            'precision': 0.89 + (len(dataset) * 0.0001),
            'recall': 0.91 + (len(dataset) * 0.0001),
            'f1_score': 0.90 + (len(dataset) * 0.0001),
            'training_epochs': self.settings.TRAINING_EPOCHS,
            'batch_size': self.settings.TRAINING_BATCH_SIZE
        }
        
        # Simulate model improvement based on user corrections
        correction_rate = sum(1 for sample in dataset if sample.get('user_corrections')) / len(dataset)
        training_metrics['improvement_factor'] = 1.0 + (correction_rate * 0.1)
        
        logger.info(f"Fraud detection model training completed: accuracy={training_metrics['accuracy']:.3f}")
        return training_metrics
    
    async def _train_ocr_accuracy_model(self, dataset: List[Dict[str, Any]]) -> Dict[str, Any]:
        logger.info("Training OCR accuracy model")
        
        # Mock training implementation - replace with actual ML training
        training_metrics = {
            'samples_trained': len(dataset),
            'accuracy': 0.88 + (len(dataset) * 0.0001),
            'character_accuracy': 0.94 + (len(dataset) * 0.0001),
            'word_accuracy': 0.87 + (len(dataset) * 0.0001),
            'confidence_correlation': 0.85 + (len(dataset) * 0.0001),
            'training_epochs': self.settings.TRAINING_EPOCHS,
            'batch_size': self.settings.TRAINING_BATCH_SIZE
        }
        
        # Calculate improvement based on user corrections
        total_corrections = sum(len(sample.get('user_corrections', {})) for sample in dataset)
        avg_corrections = total_corrections / len(dataset) if dataset else 0
        training_metrics['avg_corrections_per_document'] = avg_corrections
        training_metrics['improvement_factor'] = 1.0 + (avg_corrections * 0.05)
        
        logger.info(f"OCR accuracy model training completed: accuracy={training_metrics['accuracy']:.3f}")
        return training_metrics
    
    async def _train_data_validation_model(self, dataset: List[Dict[str, Any]]) -> Dict[str, Any]:
        logger.info("Training data validation model")
        
        # Mock training implementation - replace with actual ML training
        training_metrics = {
            'samples_trained': len(dataset),
            'validation_accuracy': 0.91 + (len(dataset) * 0.0001),
            'false_positive_rate': max(0.05 - (len(dataset) * 0.00001), 0.01),
            'false_negative_rate': max(0.08 - (len(dataset) * 0.00001), 0.02),
            'precision': 0.93 + (len(dataset) * 0.0001),
            'recall': 0.89 + (len(dataset) * 0.0001),
            'training_epochs': self.settings.TRAINING_EPOCHS,
            'batch_size': self.settings.TRAINING_BATCH_SIZE
        }
        
        # Calculate validation improvement
        validation_errors = sum(1 for sample in dataset if not sample.get('validation_results', {}).get('is_valid', True))
        error_rate = validation_errors / len(dataset) if dataset else 0
        training_metrics['historical_error_rate'] = error_rate
        training_metrics['improvement_factor'] = 1.0 + (error_rate * 0.1)
        
        logger.info(f"Data validation model training completed: accuracy={training_metrics['validation_accuracy']:.3f}")
        return training_metrics
    
    async def _evaluate_trained_models(self, training_results: Dict[str, Any]) -> Dict[str, Any]:
        logger.info("Evaluating trained models")
        
        evaluation_results = {}
        
        for model_type, metrics in training_results.items():
            # Mock evaluation - replace with actual model evaluation
            evaluation_results[model_type] = {
                'performance_improvement': metrics.get('improvement_factor', 1.0) - 1.0,
                'meets_quality_threshold': metrics.get('accuracy', 0.0) > 0.85,
                'recommended_for_deployment': metrics.get('improvement_factor', 1.0) > 1.02,
                'evaluation_score': metrics.get('accuracy', 0.0) * metrics.get('improvement_factor', 1.0)
            }
        
        logger.info(f"Model evaluation completed: {evaluation_results}")
        return evaluation_results
    
    async def _deploy_models_if_better(
        self,
        training_results: Dict[str, Any],
        evaluation_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        logger.info("Evaluating models for deployment")
        
        deployment_results = {
            'models_evaluated': len(evaluation_results),
            'models_deployed': 0,
            'deployment_details': {}
        }
        
        for model_type, evaluation in evaluation_results.items():
            if evaluation['recommended_for_deployment']:
                # Mock deployment - replace with actual model deployment
                deployment_results['deployment_details'][model_type] = {
                    'deployed': True,
                    'deployment_timestamp': datetime.utcnow().isoformat(),
                    'performance_improvement': evaluation['performance_improvement'],
                    'previous_model_backed_up': True
                }
                deployment_results['models_deployed'] += 1
                logger.info(f"Model {model_type} deployed with {evaluation['performance_improvement']:.2%} improvement")
            else:
                deployment_results['deployment_details'][model_type] = {
                    'deployed': False,
                    'reason': 'Insufficient improvement or quality threshold not met',
                    'performance_improvement': evaluation['performance_improvement']
                }
                logger.info(f"Model {model_type} not deployed: insufficient improvement")
        
        return deployment_results
    
    async def get_training_status(self) -> Dict[str, Any]:
        return {
            'is_training': self.is_training,
            'training_data_samples': len(list(self.training_data_path.glob("training_*.json"))),
            'last_training_check': datetime.utcnow().isoformat(),
            'quarterly_training_enabled': self.settings.QUARTERLY_TRAINING_ENABLED,
            'min_training_samples': self.settings.MIN_TRAINING_SAMPLES
        }
    
    async def get_training_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        # Mock implementation - replace with actual training history storage
        return [
            {
                'training_id': f"training_{i}",
                'timestamp': (datetime.utcnow() - timedelta(days=i*90)).isoformat(),
                'type': 'quarterly' if i > 0 else 'manual',
                'samples_count': 150 + (i * 25),
                'models_updated': i % 2 == 0,
                'duration_seconds': 1800 + (i * 300)
            }
            for i in range(min(limit, 4))
        ]
