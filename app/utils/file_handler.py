"""
Secure file handling utilities
"""

import hashlib
import mimetypes
import os
import tempfile
from pathlib import Path
from typing import List, Optional

import structlog
from fastapi import HTT<PERSON>Ex<PERSON>, UploadFile, status

from app.core.config import get_settings

logger = structlog.get_logger()


class FileHandler:
    """Secure file handling with validation and cleanup"""
    
    def __init__(self):
        self.settings = get_settings()
        self.allowed_extensions = {'.jpg', '.jpeg', '.png', '.pdf', '.tiff', '.bmp'}
        self.allowed_mime_types = {
            'image/jpeg', 'image/png', 'image/tiff', 'image/bmp', 'application/pdf'
        }
        self.temp_files: List[str] = []
    
    async def validate_upload(self, file: UploadFile) -> None:
        """Comprehensive file validation"""
        
        logger.info("🔍 Validating uploaded file", filename=file.filename)
        
        # Check file size
        if file.size > self.settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"File too large. Maximum size: {self.settings.MAX_FILE_SIZE} bytes"
            )
        
        # Check file extension
        if file.filename:
            file_ext = Path(file.filename).suffix.lower()
            if file_ext not in self.allowed_extensions:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"File type not allowed. Allowed types: {', '.join(self.allowed_extensions)}"
                )
        
        # Check MIME type
        if file.content_type not in self.allowed_mime_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"MIME type not allowed: {file.content_type}"
            )
        
        # Validate file content (magic bytes)
        await self._validate_file_content(file)
        
        logger.info("✅ File validation passed", filename=file.filename)
    
    async def _validate_file_content(self, file: UploadFile) -> None:
        """Validate file content using magic bytes"""
        
        # Read first few bytes to check file signature
        file_start = await file.read(32)
        await file.seek(0)  # Reset file pointer
        
        # Check magic bytes for common file types
        magic_bytes = {
            b'\xff\xd8\xff': 'image/jpeg',
            b'\x89PNG\r\n\x1a\n': 'image/png',
            b'%PDF': 'application/pdf',
            b'II*\x00': 'image/tiff',
            b'MM\x00*': 'image/tiff',
            b'BM': 'image/bmp'
        }
        
        file_type_detected = None
        for magic, mime_type in magic_bytes.items():
            if file_start.startswith(magic):
                file_type_detected = mime_type
                break
        
        if not file_type_detected:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid file format or corrupted file"
            )
        
        # Verify MIME type matches content
        if file_type_detected != file.content_type:
            logger.warning("⚠️ MIME type mismatch", 
                          declared=file.content_type, 
                          detected=file_type_detected)
    
    async def save_temp_file(self, file_content: bytes, request_id: str, 
                           file_extension: str = '.tmp') -> str:
        """Save file content to temporary location"""
        
        # Create secure temporary file
        temp_dir = Path(tempfile.gettempdir()) / "solidtech"
        temp_dir.mkdir(exist_ok=True)
        
        temp_filename = f"{request_id}_{hashlib.md5(file_content).hexdigest()[:8]}{file_extension}"
        temp_path = temp_dir / temp_filename
        
        # Write file securely
        with open(temp_path, 'wb') as f:
            f.write(file_content)
        
        # Set restrictive permissions
        os.chmod(temp_path, 0o600)
        
        # Track for cleanup
        self.temp_files.append(str(temp_path))
        
        logger.info("💾 Temporary file saved", path=str(temp_path))
        
        return str(temp_path)
    
    async def cleanup_temp_files(self, request_id: str) -> None:
        """Clean up temporary files for a request"""
        
        files_to_remove = [f for f in self.temp_files if request_id in f]
        
        for file_path in files_to_remove:
            try:
                if os.path.exists(file_path):
                    # Secure deletion (overwrite before delete)
                    await self._secure_delete(file_path)
                    self.temp_files.remove(file_path)
                    logger.info("🗑️ Temporary file deleted", path=file_path)
            except Exception as e:
                logger.error("❌ Failed to delete temp file", path=file_path, error=str(e))
    
    async def _secure_delete(self, file_path: str) -> None:
        """Securely delete file by overwriting before deletion"""
        
        try:
            # Get file size
            file_size = os.path.getsize(file_path)
            
            # Overwrite with random data
            with open(file_path, 'r+b') as f:
                f.write(os.urandom(file_size))
                f.flush()
                os.fsync(f.fileno())
            
            # Delete file
            os.remove(file_path)
            
        except Exception as e:
            logger.error("❌ Secure deletion failed", path=file_path, error=str(e))
            # Fallback to regular deletion
            try:
                os.remove(file_path)
            except:
                pass
    
    def calculate_file_hash(self, file_content: bytes, algorithm: str = 'sha256') -> str:
        """Calculate file hash for integrity verification"""
        
        hash_func = getattr(hashlib, algorithm)()
        hash_func.update(file_content)
        return hash_func.hexdigest()
    
    def get_file_info(self, file_content: bytes, filename: str) -> dict:
        """Get comprehensive file information"""
        
        file_info = {
            'filename': filename,
            'size': len(file_content),
            'mime_type': mimetypes.guess_type(filename)[0],
            'sha256': self.calculate_file_hash(file_content, 'sha256'),
            'md5': self.calculate_file_hash(file_content, 'md5')
        }
        
        return file_info
    
    async def cleanup_all_temp_files(self) -> None:
        """Clean up all tracked temporary files"""
        
        logger.info("🧹 Cleaning up all temporary files")
        
        for file_path in self.temp_files.copy():
            try:
                if os.path.exists(file_path):
                    await self._secure_delete(file_path)
                self.temp_files.remove(file_path)
            except Exception as e:
                logger.error("❌ Failed to cleanup temp file", path=file_path, error=str(e))
        
        logger.info("✅ Temporary files cleanup completed")
