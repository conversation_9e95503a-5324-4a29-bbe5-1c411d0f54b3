<?php

use App\Services\CentLogClient;

if (!function_exists('CentLog')) {
    function CentLog(string $category, $userId, array $metadata = [], string $severity = 'info')
    {
        $client = app(CentLogClient::class);
        $client->log($category, $userId, $metadata, $severity);
    }
}

if (!function_exists('CentLogBatch')) {
    function CentLogBatch(array $logs)
    {
        $client = app(CentLogClient::class);
        $client->batchLog($logs);
    }
}
