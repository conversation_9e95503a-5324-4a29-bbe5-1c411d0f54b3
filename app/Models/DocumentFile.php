<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class DocumentFile extends Model
{
    use HasFactory;

    protected $fillable = [
        'document_nomination_id',
        'original_filename',
        's3_key',
        's3_bucket',
        'file_size',
        'mime_type',
        'file_hash',
        'uploaded_by'
    ];

    protected $casts = [
        'file_size' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    public function documentNomination(): BelongsTo
    {
        return $this->belongsTo(DocumentNomination::class);
    }

    public function uploadedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\PortalUser::class, 'uploaded_by');
    }

    public function getSecureUrl(int $expirationMinutes = 5): string
    {
        return Storage::disk('secure_documents')->temporaryUrl(
            $this->s3_key,
            now()->addMinutes($expirationMinutes)
        );
    }

    public function exists(): bool
    {
        return Storage::disk('secure_documents')->exists($this->s3_key);
    }

    public function delete(): bool
    {
        // Delete from S3 first
        if ($this->exists()) {
            Storage::disk('secure_documents')->delete($this->s3_key);
        }

        // Then delete database record
        return parent::delete();
    }

    public function getFileSizeFormatted(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    public function getFileExtension(): string
    {
        return pathinfo($this->original_filename, PATHINFO_EXTENSION);
    }

    public function isImage(): bool
    {
        return in_array($this->mime_type, [
            'image/jpeg',
            'image/png',
            'image/tiff',
            'image/bmp'
        ]);
    }

    public function isPdf(): bool
    {
        return $this->mime_type === 'application/pdf';
    }

    public function verifyIntegrity(): bool
    {
        if (!$this->exists()) {
            return false;
        }

        $fileContent = Storage::disk('secure_documents')->get($this->s3_key);
        $currentHash = hash('sha256', $fileContent);
        
        return $currentHash === $this->file_hash;
    }

    public function scopeForNomination($query, int $documentNominationId)
    {
        return $query->where('document_nomination_id', $documentNominationId);
    }

    public function scopeByMimeType($query, string $mimeType)
    {
        return $query->where('mime_type', $mimeType);
    }

    public function scopeByUploader($query, int $uploaderId)
    {
        return $query->where('uploaded_by', $uploaderId);
    }
}
