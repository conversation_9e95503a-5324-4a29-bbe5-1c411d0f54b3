<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Modules\Applications\Models\Application;

class DocumentNomination extends Model
{
    use HasFactory;

    protected $fillable = [
        'application_id',
        'document_type_key',
        'document_name',
        'document_group',
        'route_number',
        'key_values',
        'confirms_address',
        'status',
        'nominated_by',
        'verified_by_type',
        'verified_by_user_id',
        'verified_at'
    ];

    protected $casts = [
        'key_values' => 'array',
        'confirms_address' => 'boolean',
        'verified_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    public function application(): BelongsTo
    {
        return $this->belongsTo(Application::class);
    }

    public function nominatedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\PortalUser::class, 'nominated_by');
    }

    public function documentFiles(): HasMany
    {
        return $this->hasMany(DocumentFile::class);
    }

    public function verifiedByUser(): BelongsTo
    {
        return $this->belongsTo(\App\Models\PortalUser::class, 'verified_by_user_id');
    }

    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }

    public function isPending(): bool
    {
        return in_array($this->status, ['nominated', 'under_review']);
    }

    public function requiresResubmission(): bool
    {
        return $this->status === 'resubmission_required';
    }

    public function hasFiles(): bool
    {
        return $this->documentFiles()->exists();
    }

    public function getFieldValue(string $fieldKey): mixed
    {
        return $this->key_values[$fieldKey] ?? null;
    }

    public function setFieldValue(string $fieldKey, mixed $value): void
    {
        $keyValues = $this->key_values;
        $keyValues[$fieldKey] = $value;
        $this->key_values = $keyValues;
    }

    public function markAsUnderReview(): void
    {
        $this->update(['status' => 'under_review']);
    }

    public function approve(int $verifiedByUserId, string $verifiedByType = 'admin_user'): void
    {
        $this->update([
            'status' => 'approved',
            'verified_by_user_id' => $verifiedByUserId,
            'verified_by_type' => $verifiedByType,
            'verified_at' => now()
        ]);
    }

    public function reject(int $verifiedByUserId, string $verifiedByType = 'admin_user'): void
    {
        $this->update([
            'status' => 'rejected',
            'verified_by_user_id' => $verifiedByUserId,
            'verified_by_type' => $verifiedByType,
            'verified_at' => now()
        ]);
    }

    public function requireResubmission(int $verifiedByUserId, string $verifiedByType = 'admin_user'): void
    {
        $this->update([
            'status' => 'resubmission_required',
            'verified_by_user_id' => $verifiedByUserId,
            'verified_by_type' => $verifiedByType,
            'verified_at' => now()
        ]);
    }

    public function scopeForApplication($query, int $applicationId)
    {
        return $query->where('application_id', $applicationId);
    }

    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByRoute($query, int $routeNumber)
    {
        return $query->where('route_number', $routeNumber);
    }

    public function scopeByDocumentType($query, string $documentTypeKey)
    {
        return $query->where('document_type_key', $documentTypeKey);
    }
}
