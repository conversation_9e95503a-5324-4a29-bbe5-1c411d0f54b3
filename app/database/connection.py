"""
Database connection management
"""

import asyncio
from typing import Optional

import structlog
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from app.core.config import get_settings

logger = structlog.get_logger()


class DatabaseManager:
    """Async database connection manager"""
    
    def __init__(self):
        self.settings = get_settings()
        self.engine = None
        self.session_factory = None
    
    async def connect(self) -> None:
        """Initialize database connections"""
        
        logger.info("📊 Connecting to database...")
        
        try:
            # Create async engine
            self.engine = create_async_engine(
                self.settings.DATABASE_URL,
                echo=self.settings.DEBUG,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
                pool_recycle=3600
            )
            
            # Create session factory
            self.session_factory = sessionmaker(
                self.engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            # Test connection
            async with self.engine.begin() as conn:
                await conn.execute("SELECT 1")
            
            logger.info("✅ Database connection established")
            
        except Exception as e:
            logger.error("❌ Database connection failed", error=str(e))
            raise
    
    async def disconnect(self) -> None:
        """Close database connections"""
        
        if self.engine:
            logger.info("📊 Closing database connections...")
            await self.engine.dispose()
            logger.info("✅ Database connections closed")
    
    def get_session(self) -> AsyncSession:
        """Get database session"""
        
        if not self.session_factory:
            raise RuntimeError("Database not initialized")
        
        return self.session_factory()
    
    async def health_check(self) -> bool:
        """Check database health"""
        
        try:
            if not self.engine:
                return False
            
            async with self.engine.begin() as conn:
                await conn.execute("SELECT 1")
            
            return True
            
        except Exception as e:
            logger.error("❌ Database health check failed", error=str(e))
            return False


# Global database manager instance
database_manager = DatabaseManager()
