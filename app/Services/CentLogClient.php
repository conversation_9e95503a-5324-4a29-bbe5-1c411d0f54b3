<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;

class CentLogClient
{
    private $client;
    private $baseUrl;
    private $applicationName;
    private $enabled;

    public function __construct()
    {
        $this->baseUrl = env('CENTLOG_URL', 'http://localhost:8004/api');
        $this->applicationName = env('CENTLOG_APP_NAME', 'unknown_app');
        $this->enabled = env('CENTLOG_ENABLED', true);
        
        $this->client = new Client([
            'timeout' => 5,
            'connect_timeout' => 2,
        ]);
    }

    public function log(string $category, $userId, array $metadata = [], string $severity = 'info')
    {
        if (!$this->enabled) {
            return;
        }

        $logData = [
            'action_name' => $this->getCategoryActionName($category, $metadata),
            'action_type' => strtolower($category),
            'user_id' => (string) $userId,
            'application' => $this->applicationName,
            'severity' => $severity,
            'action_message' => $metadata,
            'metadata' => array_merge($metadata, [
                'ip_address' => $this->getClientIp(),
                'user_agent' => $this->getUserAgent(),
                'timestamp' => now()->toISOString(),
                'category' => $category
            ]),
            'tags' => $this->generateTags($category, $metadata)
        ];

        $this->sendLog($logData);
    }

    public function batchLog(array $logs)
    {
        if (!$this->enabled || empty($logs)) {
            return;
        }

        $processedLogs = [];
        foreach ($logs as $log) {
            $processedLogs[] = [
                'action_name' => $this->getCategoryActionName($log['category'], $log['metadata'] ?? []),
                'action_type' => strtolower($log['category']),
                'user_id' => (string) $log['user_id'],
                'application' => $this->applicationName,
                'severity' => $log['severity'] ?? 'info',
                'action_message' => $log['metadata'] ?? [],
                'metadata' => array_merge($log['metadata'] ?? [], [
                    'ip_address' => $this->getClientIp(),
                    'user_agent' => $this->getUserAgent(),
                    'timestamp' => now()->toISOString(),
                    'category' => $log['category']
                ]),
                'tags' => $this->generateTags($log['category'], $log['metadata'] ?? [])
            ];
        }

        $this->sendBatchLogs($processedLogs);
    }

    public function getNotes(string $entityType, int $entityId): array
    {
        if (!$this->enabled) {
            return [];
        }

        try {
            $response = $this->client->get($this->baseUrl . '/logs/notes', [
                'query' => [
                    'entity_type' => $entityType,
                    'entity_id' => $entityId,
                    'application' => $this->applicationName
                ],
                'headers' => [
                    'Accept' => 'application/json'
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);
            return $data['data'] ?? [];
        } catch (RequestException $e) {
            Log::warning('CentLog failed to retrieve notes', [
                'error' => $e->getMessage(),
                'entity_type' => $entityType,
                'entity_id' => $entityId
            ]);
            return [];
        }
    }

    public function getRecentNotes(string $entityType, int $entityId, int $limit = 10): array
    {
        if (!$this->enabled) {
            return [];
        }

        try {
            $response = $this->client->get($this->baseUrl . '/logs/notes/recent', [
                'query' => [
                    'entity_type' => $entityType,
                    'entity_id' => $entityId,
                    'limit' => $limit,
                    'application' => $this->applicationName
                ],
                'headers' => [
                    'Accept' => 'application/json'
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);
            return $data['data'] ?? [];
        } catch (RequestException $e) {
            Log::warning('CentLog failed to retrieve recent notes', [
                'error' => $e->getMessage(),
                'entity_type' => $entityType,
                'entity_id' => $entityId,
                'limit' => $limit
            ]);
            return [];
        }
    }

    private function getCategoryActionName(string $category, array $metadata): string
    {
        switch (strtolower($category)) {
            case 'auth':
            case 'authentication':
                if (isset($metadata['status'])) {
                    switch ($metadata['status']) {
                        case 'failed':
                            return 'user_login_failed';
                        case 'logout':
                            return 'user_logout';
                        case 'success':
                        default:
                            return 'user_login';
                    }
                }
                return 'user_login';

            case 'notes':
                if (isset($metadata['action'])) {
                    return 'note_' . $metadata['action'];
                }
                return 'note_created';

            case 'process_stamp':
                if (isset($metadata['action'])) {
                    return 'process_stamp_' . $metadata['action'];
                }
                return 'process_stamp_action';

            default:
                return strtolower($category) . '_action';
        }
    }

    private function generateTags(string $category, array $metadata): array
    {
        $tags = [strtolower($category)];

        if (isset($metadata['status'])) {
            $tags[] = $metadata['status'];
        }

        if (strtolower($category) === 'auth') {
            $tags[] = 'authentication';
        }

        if (strtolower($category) === 'notes') {
            $tags[] = 'communication';
            if (isset($metadata['note_category'])) {
                $tags[] = $metadata['note_category'];
            }
        }

        if (strtolower($category) === 'process_stamp') {
            $tags[] = 'workflow';
            $tags[] = 'criminal_record';
        }

        return $tags;
    }

    private function getClientIp(): string
    {
        return request()->ip() ?? 'unknown';
    }

    private function getUserAgent(): string
    {
        return request()->userAgent() ?? 'unknown';
    }

    private function sendLog(array $logData)
    {
        try {
            $this->client->post($this->baseUrl . '/logs', [
                'json' => $logData,
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json'
                ]
            ]);
        } catch (RequestException $e) {
            Log::warning('CentLog failed to send log', [
                'error' => $e->getMessage(),
                'log_data' => $logData
            ]);
        }
    }

    private function sendBatchLogs(array $logs)
    {
        try {
            $this->client->post($this->baseUrl . '/logs/batch', [
                'json' => ['logs' => $logs],
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json'
                ]
            ]);
        } catch (RequestException $e) {
            Log::warning('CentLog failed to send batch logs', [
                'error' => $e->getMessage(),
                'log_count' => count($logs)
            ]);
        }
    }
}
