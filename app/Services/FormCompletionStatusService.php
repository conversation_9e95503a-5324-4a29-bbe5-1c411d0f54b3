<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Support\Facades\DB;

class FormCompletionStatusService
{
    private ProcessStampService $processStampService;

    public function __construct(ProcessStampService $processStampService)
    {
        $this->processStampService = $processStampService;
    }

    public function checkFormCompletionStatus(int $applicationId): array
    {
        $stamp = $this->getApplicantFormCompletedStamp($applicationId);
        $applicantInfo = $this->getApplicantInfo($applicationId);

        if (!$stamp) {
            return [
                'is_completed' => false,
                'completion_status' => 'not_started',
                'completed_at' => null,
                'completed_by' => null,
                'form_data' => null,
                'applicant_name' => $applicantInfo['name'] ?? 'Applicant',
                'message' => 'Form has not been started yet'
            ];
        }

        $isCompleted = $stamp->status === 'completed';
        $fieldData = $stamp->field_data ? json_decode($stamp->field_data, true) : null;

        return [
            'is_completed' => $isCompleted,
            'completion_status' => $stamp->status,
            'completed_at' => $stamp->completed_at,
            'completed_by' => $this->getCompletedByInfo($fieldData),
            'form_data' => $fieldData,
            'applicant_name' => $applicantInfo['name'] ?? 'Applicant',
            'message' => $this->getStatusMessage($stamp->status)
        ];
    }

    public function isFormCompleted(int $applicationId): bool
    {
        $stamp = $this->getApplicantFormCompletedStamp($applicationId);
        return $stamp && $stamp->status === 'completed';
    }

    public function getFormCompletionDetails(int $applicationId): ?array
    {
        $stamp = $this->getApplicantFormCompletedStamp($applicationId);
        
        if (!$stamp || $stamp->status !== 'completed') {
            return null;
        }

        $fieldData = $stamp->field_data ? json_decode($stamp->field_data, true) : null;

        return [
            'completed_at' => $stamp->completed_at,
            'completed_by' => $this->getCompletedByInfo($fieldData),
            'form_data' => $fieldData
        ];
    }

    private function getApplicantFormCompletedStamp(int $applicationId): ?object
    {
        return DB::table('processed_stamps')
            ->join('process_stamps_main', 'processed_stamps.stamp_id', '=', 'process_stamps_main.SAMP_ID')
            ->where('processed_stamps.application_id', $applicationId)
            ->where('process_stamps_main.STAMP_TAG', 'APPLICANT_FORM_COMPLETED')
            ->select('processed_stamps.*')
            ->first();
    }

    private function getCompletedByInfo(?array $fieldData): ?array
    {
        if (!$fieldData || !isset($fieldData['saved_by_user_id'])) {
            return null;
        }

        $userId = $fieldData['saved_by_user_id'];
        $userType = $fieldData['saved_by_user_type'] ?? 'unknown';

        // Fetch user details
        $userDetails = $this->getUserDetails($userId);

        return [
            'user_id' => $userId,
            'user_type' => $userType,
            'user_name' => $userDetails['name'] ?? null,
            'user_email' => $userDetails['email'] ?? null,
            'saved_at' => $fieldData['saved_at'] ?? null
        ];
    }

    private function getUserDetails(int $userId): array
    {
        $user = DB::table('portal_users')
            ->leftJoin('portal_user_profiles', 'portal_users.id', '=', 'portal_user_profiles.user_id')
            ->where('portal_users.id', $userId)
            ->select(
                'portal_users.email',
                'portal_user_profiles.first_name',
                'portal_user_profiles.last_name'
            )
            ->first();

        if (!$user) {
            return [];
        }

        $name = trim(($user->first_name ?? '') . ' ' . ($user->last_name ?? ''));

        return [
            'name' => !empty($name) ? $name : null,
            'email' => $user->email
        ];
    }

    private function getApplicantInfo(int $applicationId): array
    {
        $application = DB::table('applications')
            ->leftJoin('portal_users', 'applications.applicant_id', '=', 'portal_users.id')
            ->leftJoin('portal_user_profiles', 'portal_users.id', '=', 'portal_user_profiles.user_id')
            ->where('applications.id', $applicationId)
            ->select(
                'portal_user_profiles.first_name',
                'portal_user_profiles.last_name',
                'portal_users.email'
            )
            ->first();

        if (!$application) {
            return [];
        }

        $name = trim(($application->first_name ?? '') . ' ' . ($application->last_name ?? ''));

        return [
            'name' => !empty($name) ? $name : null,
            'email' => $application->email
        ];
    }

    private function getStatusMessage(string $status): string
    {
        return match ($status) {
            'completed' => 'Application form has been completed successfully',
            'pending' => 'Application form is in progress',
            'skipped' => 'Application form was skipped',
            default => 'Application form status is unknown'
        };
    }
}
