<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;

class ProcessStampService
{
    public function getStampsForProduct(int $productId, ?string $category = null): Collection
    {
        $query = DB::table('process_stamps_main')
            ->join('process_stamp_links', 'process_stamps_main.SAMP_ID', '=', 'process_stamp_links.STAMP_ID')
            ->where('process_stamp_links.PRODUCT_ID', $productId)
            ->select('process_stamps_main.*')
            ->orderBy('process_stamps_main.ORDER');
        
        if ($category) {
            $query->where('process_stamps_main.CATEGORY', $category);
        }

        return collect($query->get());
    }

    public function getStampsForApplication(int $applicationId, int $productId): Collection
    {
        $stamps = $this->getStampsForProduct($productId);
        $processedStamps = DB::table('processed_stamps')
            ->where('application_id', $applicationId)
            ->get()
            ->keyBy('stamp_id');

        return $stamps->map(function ($stamp) use ($processedStamps) {
            $processed = $processedStamps->get($stamp->SAMP_ID);
            
            return [
                'stamp' => $stamp,
                'processed' => $processed,
                'status' => $processed?->status ?? 'pending',
                'field_data' => $processed?->field_data ?? [],
                'completed_at' => $processed?->completed_at,
                'user' => $processed?->user ?? null
            ];
        });
    }

    public function initializeStampsForApplication(int $applicationId, int $productId): bool
    {
        $stamps = $this->getStampsForProduct($productId);
        
        DB::beginTransaction();
        
        try {
            foreach ($stamps as $stamp) {
                $exists = DB::table('processed_stamps')
                    ->where('application_id', $applicationId)
                    ->where('stamp_id', $stamp->SAMP_ID)
                    ->exists();

                if (!$exists) {
                    DB::table('processed_stamps')->insert([
                        'application_id' => $applicationId,
                        'stamp_id' => $stamp->SAMP_ID,
                        'user_id' => 1,
                        'status' => 'pending',
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
            }

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            return false;
        }
    }

    public function completeStamp(int $applicationId, string $stampTag, int $userId, array $fieldData = [], ?string $comments = null): bool
    {
        $stamp = DB::table('process_stamps_main')
            ->where('STAMP_TAG', $stampTag)
            ->first();

        if (!$stamp) {
            return false;
        }

        $processedStamp = DB::table('processed_stamps')
            ->where('application_id', $applicationId)
            ->where('stamp_id', $stamp->SAMP_ID)
            ->first();

        if (!$processedStamp) {
            $this->initializeStampForApplication($applicationId, $stamp->SAMP_ID, $userId);
        }

        DB::beginTransaction();
        
        try {
            $success = DB::table('processed_stamps')
                ->where('application_id', $applicationId)
                ->where('stamp_id', $stamp->SAMP_ID)
                ->update([
                    'status' => 'completed',
                    'user_id' => $userId,
                    'field_data' => !empty($fieldData) ? json_encode($fieldData) : null,
                    'completed_at' => now(),
                    'updated_at' => now()
                ]);

            DB::commit();
            return $success > 0;
        } catch (\Exception $e) {
            DB::rollBack();
            return false;
        }
    }

    private function initializeStampForApplication(int $applicationId, int $stampId, int $userId): void
    {
        DB::table('processed_stamps')->insert([
            'application_id' => $applicationId,
            'stamp_id' => $stampId,
            'user_id' => $userId,
            'status' => 'pending',
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }

    public function skipStamp(int $applicationId, string $stampTag, int $userId): bool
    {
        $stamp = DB::table('process_stamps_main')
            ->where('STAMP_TAG', $stampTag)
            ->first();

        if (!$stamp) {
            return false;
        }

        $processedStamp = DB::table('processed_stamps')
            ->where('application_id', $applicationId)
            ->where('stamp_id', $stamp->SAMP_ID)
            ->first();

        if (!$processedStamp) {
            return false;
        }

        DB::beginTransaction();
        
        try {
            $success = DB::table('processed_stamps')
                ->where('application_id', $applicationId)
                ->where('stamp_id', $stamp->SAMP_ID)
                ->update([
                    'status' => 'skipped',
                    'user_id' => $userId,
                    'completed_at' => now(),
                    'updated_at' => now()
                ]);
            
            DB::commit();
            return $success > 0;
        } catch (\Exception $e) {
            DB::rollBack();
            return false;
        }
    }

    public function resetStamp(int $applicationId, string $stampTag, int $userId): bool
    {
        $stamp = DB::table('process_stamps_main')
            ->where('STAMP_TAG', $stampTag)
            ->first();

        if (!$stamp) {
            return false;
        }

        $processedStamp = DB::table('processed_stamps')
            ->where('application_id', $applicationId)
            ->where('stamp_id', $stamp->SAMP_ID)
            ->first();

        if (!$processedStamp) {
            return false;
        }

        DB::beginTransaction();
        
        try {
            $success = DB::table('processed_stamps')
                ->where('application_id', $applicationId)
                ->where('stamp_id', $stamp->SAMP_ID)
                ->update([
                    'status' => 'pending',
                    'field_data' => null,
                    'completed_at' => null,
                    'updated_at' => now()
                ]);
            
            DB::commit();
            return $success > 0;
        } catch (\Exception $e) {
            DB::rollBack();
            return false;
        }
    }

    public function getApplicationProgress(int $applicationId): array
    {
        $processedStamps = DB::table('processed_stamps')
            ->where('application_id', $applicationId)
            ->get();

        $total = $processedStamps->count();
        $completed = $processedStamps->where('status', 'completed')->count();
        $skipped = $processedStamps->where('status', 'skipped')->count();
        $pending = $processedStamps->where('status', 'pending')->count();

        return [
            'total' => $total,
            'completed' => $completed,
            'skipped' => $skipped,
            'pending' => $pending,
            'completion_percentage' => $total > 0 ? round(($completed / $total) * 100, 2) : 0
        ];
    }
}
