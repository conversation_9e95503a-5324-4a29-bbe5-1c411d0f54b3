<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Modules\Documents\Models\DocumentNomination;
use App\Services\SecureDocumentStorageService;

/**
 * Test controller for document upload functionality
 */
class TestUploadController extends Controller
{
    private SecureDocumentStorageService $storageService;

    public function __construct(SecureDocumentStorageService $storageService)
    {
        $this->storageService = $storageService;
    }

    /**
     * Test document upload endpoint
     */
    public function testUpload(Request $request): JsonResponse
    {
        Log::info('🧪 Test upload endpoint called', [
            'has_file' => $request->hasFile('file'),
            'files_count' => count($request->allFiles()),
            'content_length' => $request->header('Content-Length'),
            'content_type' => $request->header('Content-Type')
        ]);

        try {
            // Validate request
            $validator = Validator::make($request->all(), [
                'file' => 'required|file|mimes:pdf,png,jpg,jpeg|max:10240', // 10MB max
                'application_id' => 'required|integer',
                'nomination_id' => 'required|integer',
            ]);

            if ($validator->fails()) {
                Log::warning('❌ Test upload validation failed', ['errors' => $validator->errors()]);
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $applicationId = (int) $request->input('application_id');
            $nominationId = (int) $request->input('nomination_id');

            Log::info('✅ Test upload validation passed', [
                'application_id' => $applicationId,
                'nomination_id' => $nominationId
            ]);

            // Find or create a test nomination
            $nomination = DocumentNomination::find($nominationId);
            
            if (!$nomination) {
                // Create a test nomination
                $nomination = DocumentNomination::create([
                    'application_id' => $applicationId,
                    'document_type_key' => 'test_document',
                    'document_name' => 'Test Document',
                    'document_group' => '1',
                    'route_number' => 1,
                    'confirms_address' => false,
                    'status' => 'pending',
                    'key_values' => json_encode(['test' => 'data']),
                    'nominated_by' => 1, // Test user ID
                ]);
                
                Log::info('📄 Created test nomination', ['nomination_id' => $nomination->id]);
            }

            // Upload file using the storage service
            $file = $request->file('file');
            $documentFile = $this->storageService->storeDocumentFile($file, $nomination, 1); // Test user ID

            Log::info('🎉 Test upload completed successfully', [
                'file_id' => $documentFile->id,
                's3_key' => $documentFile->s3_key,
                'file_size' => $documentFile->file_size
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Test upload successful',
                'data' => [
                    'file_id' => $documentFile->id,
                    'filename' => $documentFile->original_filename,
                    'file_size' => $documentFile->file_size,
                    's3_key' => $documentFile->s3_key,
                    's3_bucket' => $documentFile->s3_bucket,
                    'nomination_id' => $nomination->id,
                    'application_id' => $applicationId
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('💥 Test upload failed', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Test upload failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check document files in database
     */
    public function checkFiles(Request $request): JsonResponse
    {
        try {
            $files = \App\Modules\Documents\Models\DocumentFile::with('documentNomination')
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();

            $fileData = $files->map(function ($file) {
                return [
                    'id' => $file->id,
                    'filename' => $file->original_filename,
                    'file_size' => $file->file_size,
                    's3_key' => $file->s3_key,
                    's3_bucket' => $file->s3_bucket,
                    'nomination_id' => $file->document_nomination_id,
                    'application_id' => $file->documentNomination->application_id ?? null,
                    'created_at' => $file->created_at->toISOString(),
                ];
            });

            return response()->json([
                'success' => true,
                'message' => 'Files retrieved successfully',
                'data' => [
                    'total_files' => $files->count(),
                    'files' => $fileData
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('💥 Check files failed', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to check files',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
