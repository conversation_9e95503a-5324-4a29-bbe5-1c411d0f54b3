"""
Custom exceptions for SolidTech system
"""

from datetime import datetime
from typing import Any, Dict, Optional


class SolidTechException(Exception):
    """Base exception for SolidTech system"""
    
    def __init__(
        self,
        message: str,
        error_code: str = "SOLIDTECH_ERROR",
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details or {}
        self.timestamp = datetime.utcnow()
        super().__init__(self.message)


class ValidationError(SolidTechException):
    """Validation error"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            status_code=400,
            details=details
        )


class AuthenticationError(SolidTechException):
    """Authentication error"""
    
    def __init__(self, message: str = "Authentication failed"):
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_ERROR",
            status_code=401
        )


class AuthorizationError(SolidTechException):
    """Authorization error"""
    
    def __init__(self, message: str = "Access denied"):
        super().__init__(
            message=message,
            error_code="AUTHORIZATION_ERROR",
            status_code=403
        )


class DocumentProcessingError(SolidTechException):
    """Document processing error"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="DOCUMENT_PROCESSING_ERROR",
            status_code=422,
            details=details
        )


class MLModelError(SolidTechException):
    """ML model error"""
    
    def __init__(self, message: str, model_name: str = "unknown"):
        super().__init__(
            message=message,
            error_code="ML_MODEL_ERROR",
            status_code=500,
            details={"model_name": model_name}
        )


class SecurityError(SolidTechException):
    """Security-related error"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="SECURITY_ERROR",
            status_code=403,
            details=details
        )
