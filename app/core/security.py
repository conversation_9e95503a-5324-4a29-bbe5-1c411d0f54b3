"""
Comprehensive security system with RSA/AES hybrid encryption
"""

import hashlib
import hmac
import json
import secrets
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, Optional, Tuple

import jwt
import structlog
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import padding, rsa
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from fastapi import HTTPException, Request, Response, status
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from passlib.context import CryptContext

from app.core.config import get_settings

logger = structlog.get_logger()


async def verify_security_config():
    """Verify security configuration is properly set up"""

    settings = get_settings()

    # Check if RSA keys exist
    private_key_path = Path(settings.RSA_PRIVATE_KEY_PATH)
    public_key_path = Path(settings.RSA_PUBLIC_KEY_PATH)

    if not private_key_path.exists() or not public_key_path.exists():
        logger.warning("🔑 RSA keys not found, generating new key pair...")
        security_manager = SecurityManager()
        await security_manager.generate_rsa_keys()

    logger.info("✅ Security configuration verified")


class SecurityManager:
    """Centralized security management with hybrid encryption"""
    
    def __init__(self):
        self.settings = get_settings()
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        self._rsa_private_key: Optional[rsa.RSAPrivateKey] = None
        self._rsa_public_key: Optional[rsa.RSAPublicKey] = None
        self._fernet: Optional[Fernet] = None
        
    async def initialize(self):
        """Initialize security components"""
        await self._load_rsa_keys()
        self._setup_symmetric_encryption()
        logger.info("🔒 Security manager initialized")
    
    async def _load_rsa_keys(self):
        """Load RSA key pair for asymmetric encryption"""
        try:
            private_key_path = self.settings.get_rsa_private_key_path()
            public_key_path = self.settings.get_rsa_public_key_path()
            
            if not private_key_path.exists() or not public_key_path.exists():
                logger.warning("RSA keys not found, generating new key pair")
                await self._generate_rsa_keys()
                return
            
            # Load private key
            with open(private_key_path, "rb") as f:
                self._rsa_private_key = serialization.load_pem_private_key(
                    f.read(), password=None
                )
            
            # Load public key
            with open(public_key_path, "rb") as f:
                self._rsa_public_key = serialization.load_pem_public_key(f.read())
            
            logger.info("✅ RSA keys loaded successfully")
            
        except Exception as e:
            logger.error("❌ Failed to load RSA keys", error=str(e))
            raise
    
    async def _generate_rsa_keys(self):
        """Generate new RSA key pair"""
        try:
            # Generate private key
            private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=4096,
            )
            
            # Get public key
            public_key = private_key.public_key()
            
            # Serialize private key
            private_pem = private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            )
            
            # Serialize public key
            public_pem = public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            )
            
            # Ensure directories exist
            private_key_path = self.settings.get_rsa_private_key_path()
            public_key_path = self.settings.get_rsa_public_key_path()
            
            private_key_path.parent.mkdir(parents=True, exist_ok=True)
            public_key_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Save keys
            with open(private_key_path, "wb") as f:
                f.write(private_pem)
            
            with open(public_key_path, "wb") as f:
                f.write(public_pem)
            
            # Set restrictive permissions
            private_key_path.chmod(0o600)
            public_key_path.chmod(0o644)
            
            self._rsa_private_key = private_key
            self._rsa_public_key = public_key
            
            logger.info("✅ New RSA key pair generated and saved")
            
        except Exception as e:
            logger.error("❌ Failed to generate RSA keys", error=str(e))
            raise
    
    def _setup_symmetric_encryption(self):
        """Setup Fernet for symmetric encryption"""
        try:
            # Use the encryption key from settings
            key = self.settings.ENCRYPTION_KEY.encode()[:32]  # Ensure 32 bytes
            key = key.ljust(32, b'0')  # Pad if necessary
            
            # Generate Fernet key from our key
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=b'solidtech_salt',  # Fixed salt for consistency
                iterations=100000,
            )
            fernet_key = kdf.derive(key)
            import base64
            fernet_key_b64 = base64.urlsafe_b64encode(fernet_key)
            self._fernet = Fernet(fernet_key_b64)
            
            logger.info("✅ Symmetric encryption initialized")
            
        except Exception as e:
            logger.error("❌ Failed to setup symmetric encryption", error=str(e))
            raise
    
    def encrypt_data(self, data: bytes) -> bytes:
        """Encrypt data using AES-256"""
        if self.settings.SKIP_ENCRYPTION:
            return data
        
        if not self._fernet:
            raise RuntimeError("Symmetric encryption not initialized")
        
        return self._fernet.encrypt(data)
    
    def decrypt_data(self, encrypted_data: bytes) -> bytes:
        """Decrypt data using AES-256"""
        if self.settings.SKIP_ENCRYPTION:
            return encrypted_data
        
        if not self._fernet:
            raise RuntimeError("Symmetric encryption not initialized")
        
        return self._fernet.decrypt(encrypted_data)
    
    def encrypt_with_rsa(self, data: bytes) -> bytes:
        """Encrypt data using RSA public key"""
        if not self._rsa_public_key:
            raise RuntimeError("RSA public key not loaded")
        
        return self._rsa_public_key.encrypt(
            data,
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
    
    def decrypt_with_rsa(self, encrypted_data: bytes) -> bytes:
        """Decrypt data using RSA private key"""
        if not self._rsa_private_key:
            raise RuntimeError("RSA private key not loaded")
        
        return self._rsa_private_key.decrypt(
            encrypted_data,
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None
            )
        )
    
    def get_public_key_pem(self) -> str:
        """Get RSA public key in PEM format"""
        if not self._rsa_public_key:
            raise RuntimeError("RSA public key not loaded")
        
        pem = self._rsa_public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        return pem.decode('utf-8')
    
    def create_jwt_token(self, data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """Create JWT token with RSA signing"""
        if not self._rsa_private_key:
            raise RuntimeError("RSA private key not loaded")
        
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.settings.JWT_EXPIRE_MINUTES)
        
        to_encode.update({"exp": expire, "iat": datetime.utcnow()})
        
        return jwt.encode(
            to_encode,
            self._rsa_private_key,
            algorithm=self.settings.JWT_ALGORITHM
        )
    
    def verify_jwt_token(self, token: str) -> Dict[str, Any]:
        """Verify JWT token with RSA public key"""
        if not self._rsa_public_key:
            raise RuntimeError("RSA public key not loaded")
        
        try:
            payload = jwt.decode(
                token,
                self._rsa_public_key,
                algorithms=[self.settings.JWT_ALGORITHM]
            )
            return payload
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired"
            )
        except jwt.JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
    
    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt"""
        return self.pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify password against hash"""
        return self.pwd_context.verify(plain_password, hashed_password)
    
    def generate_secure_token(self, length: int = 32) -> str:
        """Generate cryptographically secure random token"""
        return secrets.token_urlsafe(length)
    
    def create_hmac_signature(self, data: bytes, key: Optional[bytes] = None) -> str:
        """Create HMAC signature for data integrity"""
        if key is None:
            key = self.settings.SECRET_KEY.encode()
        
        signature = hmac.new(key, data, hashlib.sha256)
        return signature.hexdigest()
    
    def verify_hmac_signature(self, data: bytes, signature: str, key: Optional[bytes] = None) -> bool:
        """Verify HMAC signature"""
        if key is None:
            key = self.settings.SECRET_KEY.encode()
        
        expected_signature = self.create_hmac_signature(data, key)
        return hmac.compare_digest(signature, expected_signature)


# Global security manager instance
security_manager = SecurityManager()


async def verify_security_config():
    """Verify security configuration on startup"""
    await security_manager.initialize()


class SecurityMiddleware:
    """Security middleware for request/response processing"""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            request = Request(scope, receive)
            
            # IP whitelist check
            client_ip = request.client.host if request.client else None
            settings = get_settings()
            
            if settings.is_production() and client_ip not in settings.IP_WHITELIST:
                response = {
                    "type": "http.response.start",
                    "status": 403,
                    "headers": [[b"content-type", b"application/json"]],
                }
                await send(response)
                
                body = json.dumps({"error": "Access denied"}).encode()
                await send({
                    "type": "http.response.body",
                    "body": body,
                })
                return
        
        await self.app(scope, receive, send)


class JWTBearer(HTTPBearer):
    """JWT Bearer token authentication"""
    
    def __init__(self, auto_error: bool = True):
        super().__init__(auto_error=auto_error)
    
    async def __call__(self, request: Request) -> Optional[str]:
        credentials: HTTPAuthorizationCredentials = await super().__call__(request)
        
        if credentials:
            if not credentials.scheme == "Bearer":
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid authentication scheme"
                )
            
            payload = security_manager.verify_jwt_token(credentials.credentials)
            return payload
        
        return None


# JWT dependency
jwt_bearer = JWTBearer()
