"""
Core Interfaces - Abstract base classes for MVVM architecture
Following Google engineering standards with dependency injection
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Tuple
import numpy as np
from app.models.document_models import (
    DocumentExtractionResult, 
    DocumentProcessingRequest,
    ExtractedField,
    FraudAnalysis,
    FaceAnalysis,
    ValidationResult,
    DocumentType
)


class IOCREngine(ABC):
    """Abstract OCR engine interface"""
    
    @abstractmethod
    def extract_text(self, image: np.ndarray, **kwargs) -> str:
        """Extract raw text from image"""
        pass
    
    @abstractmethod
    def extract_fields(self, image: np.ndarray, document_type: DocumentType, **kwargs) -> Dict[str, ExtractedField]:
        """Extract structured fields from image"""
        pass
    
    @abstractmethod
    def get_confidence(self) -> float:
        """Get overall confidence score"""
        pass
    
    @abstractmethod
    def get_engine_info(self) -> Dict[str, str]:
        """Get engine version and metadata"""
        pass


class IFraudDetector(ABC):
    """Abstract fraud detection interface"""
    
    @abstractmethod
    def analyze_document(self, image: np.ndarray, document_type: DocumentType) -> FraudAnalysis:
        """Analyze document for fraud indicators"""
        pass
    
    @abstractmethod
    def detect_objects(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """Detect objects using YOLO"""
        pass
    
    @abstractmethod
    def validate_hologram(self, image: np.ndarray) -> bool:
        """Validate hologram presence"""
        pass
    
    @abstractmethod
    def analyze_depth(self, image: np.ndarray) -> str:
        """Analyze 3D depth characteristics"""
        pass


class IFaceProcessor(ABC):
    """Abstract face processing interface"""
    
    @abstractmethod
    def extract_face(self, image: np.ndarray) -> Optional[FaceAnalysis]:
        """Extract face from document"""
        pass
    
    @abstractmethod
    def compare_faces(self, face1: np.ndarray, face2: np.ndarray) -> float:
        """Compare two faces and return similarity score"""
        pass
    
    @abstractmethod
    def detect_liveness(self, images: List[np.ndarray]) -> bool:
        """Detect if faces are live (not spoofed)"""
        pass


class IDocumentProcessor(ABC):
    """Abstract document processor interface"""
    
    @abstractmethod
    def process_document(self, request: DocumentProcessingRequest) -> DocumentExtractionResult:
        """Process document and return extraction result"""
        pass
    
    @abstractmethod
    def validate_fields(self, fields: Dict[str, ExtractedField], document_type: DocumentType) -> ValidationResult:
        """Validate extracted fields"""
        pass
    
    @abstractmethod
    def supports_document_type(self, document_type: DocumentType) -> bool:
        """Check if processor supports document type"""
        pass


class IImagePreprocessor(ABC):
    """Abstract image preprocessing interface"""
    
    @abstractmethod
    def preprocess(self, image: np.ndarray, document_type: DocumentType) -> np.ndarray:
        """Preprocess image for better OCR results"""
        pass
    
    @abstractmethod
    def enhance_quality(self, image: np.ndarray) -> np.ndarray:
        """Enhance image quality"""
        pass
    
    @abstractmethod
    def detect_orientation(self, image: np.ndarray) -> float:
        """Detect image orientation angle"""
        pass
    
    @abstractmethod
    def correct_perspective(self, image: np.ndarray) -> np.ndarray:
        """Correct perspective distortion"""
        pass


class IFieldValidator(ABC):
    """Abstract field validation interface"""
    
    @abstractmethod
    def validate_field(self, field_name: str, field_value: str, document_type: DocumentType) -> Tuple[bool, List[str], List[str]]:
        """Validate individual field - returns (is_valid, errors, warnings)"""
        pass
    
    @abstractmethod
    def validate_cross_fields(self, fields: Dict[str, ExtractedField], document_type: DocumentType) -> List[str]:
        """Validate relationships between fields"""
        pass
    
    @abstractmethod
    def validate_against_application(self, fields: Dict[str, ExtractedField], application_data: Dict[str, Any]) -> List[str]:
        """Validate extracted fields against application data"""
        pass


class IDocumentRepository(ABC):
    """Abstract document repository interface"""
    
    @abstractmethod
    def save_processing_result(self, result: DocumentExtractionResult) -> str:
        """Save processing result and return ID"""
        pass
    
    @abstractmethod
    def get_processing_result(self, result_id: str) -> Optional[DocumentExtractionResult]:
        """Get processing result by ID"""
        pass
    
    @abstractmethod
    def save_training_data(self, image_data: bytes, metadata: Dict[str, Any]) -> str:
        """Save training data for model improvement"""
        pass
    
    @abstractmethod
    def get_training_statistics(self) -> Dict[str, Any]:
        """Get training data statistics"""
        pass


class IConfigurationService(ABC):
    """Abstract configuration service interface"""
    
    @abstractmethod
    def get_ocr_config(self, document_type: DocumentType) -> Dict[str, Any]:
        """Get OCR configuration for document type"""
        pass
    
    @abstractmethod
    def get_fraud_detection_config(self) -> Dict[str, Any]:
        """Get fraud detection configuration"""
        pass
    
    @abstractmethod
    def get_validation_rules(self, document_type: DocumentType) -> Dict[str, Any]:
        """Get validation rules for document type"""
        pass
    
    @abstractmethod
    def get_processing_options(self) -> Dict[str, Any]:
        """Get general processing options"""
        pass


class ILoggingService(ABC):
    """Abstract logging service interface"""
    
    @abstractmethod
    def log_processing_start(self, request: DocumentProcessingRequest) -> None:
        """Log processing start"""
        pass
    
    @abstractmethod
    def log_processing_complete(self, result: DocumentExtractionResult) -> None:
        """Log processing completion"""
        pass
    
    @abstractmethod
    def log_error(self, error: Exception, context: Dict[str, Any]) -> None:
        """Log error with context"""
        pass
    
    @abstractmethod
    def log_performance_metrics(self, metrics: Dict[str, float]) -> None:
        """Log performance metrics"""
        pass


class IMetricsCollector(ABC):
    """Abstract metrics collection interface"""
    
    @abstractmethod
    def record_processing_time(self, document_type: DocumentType, processing_time: float) -> None:
        """Record processing time metric"""
        pass
    
    @abstractmethod
    def record_accuracy_score(self, document_type: DocumentType, accuracy: float) -> None:
        """Record accuracy metric"""
        pass
    
    @abstractmethod
    def record_fraud_detection(self, document_type: DocumentType, is_fraudulent: bool) -> None:
        """Record fraud detection metric"""
        pass
    
    @abstractmethod
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get metrics summary"""
        pass


class IDocumentProcessingService(ABC):
    """Main document processing service interface"""
    
    @abstractmethod
    def process_document(self, request: DocumentProcessingRequest) -> DocumentExtractionResult:
        """Main document processing method"""
        pass
    
    @abstractmethod
    def get_supported_document_types(self) -> List[DocumentType]:
        """Get list of supported document types"""
        pass
    
    @abstractmethod
    def validate_request(self, request: DocumentProcessingRequest) -> Tuple[bool, List[str]]:
        """Validate processing request"""
        pass
    
    @abstractmethod
    def get_processing_status(self, request_id: str) -> Optional[Dict[str, Any]]:
        """Get processing status by request ID"""
        pass
