"""
Training Configuration for ML Models
"""

import os
from pathlib import Path
from typing import Dict, List, Any

class TrainingConfig:
    """Configuration for ML model training"""
    
    # Base paths
    BASE_DIR = Path(__file__).parent.parent.parent
    TRAINING_DATA_PATH = BASE_DIR / "data" / "training"
    MODEL_PATH = BASE_DIR / "models"
    LOGS_PATH = BASE_DIR / "logs" / "training"
    
    # Training parameters
    MIN_SAMPLES_PER_DOCUMENT_TYPE = 10
    VALIDATION_SPLIT = 0.2
    BATCH_SIZE = 8
    LEARNING_RATE = 0.001
    EPOCHS = 50
    
    # Model parameters
    IMAGE_SIZE = (224, 224)
    MAX_IMAGE_SIZE = (1920, 1080)
    SUPPORTED_IMAGE_FORMATS = ['.jpg', '.jpeg', '.png', '.tiff', '.bmp']
    
    # Document type configurations
    DOCUMENT_TYPES = {
        'p60': {
            'name': 'P60 Tax Certificate',
            'required_fields': [
                'employee_name', 'employer_name', 'tax_year', 
                'total_pay', 'tax_deducted', 'ni_number', 'employee_address'
            ],
            'field_types': {
                'employee_name': 'name',
                'employer_name': 'text',
                'tax_year': 'text',
                'total_pay': 'currency',
                'tax_deducted': 'currency',
                'ni_number': 'text',
                'employee_address': 'address'
            },
            'validation_rules': {
                'ni_number': r'^[A-Z]{2}[0-9]{6}[A-Z]$',
                'tax_year': r'^\d{4}-\d{2}$',
                'total_pay': r'^\d+\.\d{2}$',
                'tax_deducted': r'^\d+\.\d{2}$'
            },
            'extraction_hints': {
                'employee_name': 'Usually found after "To employee:" text',
                'tax_year': 'Usually at the top of the document',
                'employee_address': 'Should include postcode for validation'
            }
        },
        'p45': {
            'name': 'P45 Tax Form',
            'required_fields': [
                'employee_name', 'employer_name', 'leaving_date',
                'total_pay', 'tax_deducted', 'ni_number'
            ],
            'field_types': {
                'employee_name': 'name',
                'employer_name': 'text',
                'leaving_date': 'date',
                'total_pay': 'currency',
                'tax_deducted': 'currency',
                'ni_number': 'text'
            },
            'validation_rules': {
                'ni_number': r'^[A-Z]{2}[0-9]{6}[A-Z]$',
                'leaving_date': r'^\d{2}/\d{2}/\d{4}$',
                'total_pay': r'^\d+\.\d{2}$',
                'tax_deducted': r'^\d+\.\d{2}$'
            }
        },
        'passport': {
            'name': 'Passport',
            'required_fields': [
                'passport_number', 'surname', 'given_names', 
                'date_of_birth', 'issue_date', 'expiry_date', 'nationality'
            ],
            'field_types': {
                'passport_number': 'text',
                'surname': 'name',
                'given_names': 'name',
                'date_of_birth': 'date',
                'issue_date': 'date',
                'expiry_date': 'date',
                'nationality': 'text'
            },
            'validation_rules': {
                'passport_number': r'^[0-9]{9}$',
                'date_of_birth': r'^\d{2}/\d{2}/\d{4}$',
                'issue_date': r'^\d{2}/\d{2}/\d{4}$',
                'expiry_date': r'^\d{2}/\d{2}/\d{4}$'
            }
        },
        'driving_licence': {
            'name': 'UK Driving Licence',
            'required_fields': [
                'licence_number', 'surname', 'given_names',
                'date_of_birth', 'issue_date', 'expiry_date', 'address'
            ],
            'field_types': {
                'licence_number': 'text',
                'surname': 'name',
                'given_names': 'name',
                'date_of_birth': 'date',
                'issue_date': 'date',
                'expiry_date': 'date',
                'address': 'address'
            },
            'validation_rules': {
                'licence_number': r'^[A-Z0-9]{16}$',
                'date_of_birth': r'^\d{2}/\d{2}/\d{4}$',
                'issue_date': r'^\d{2}/\d{2}/\d{4}$',
                'expiry_date': r'^\d{2}/\d{2}/\d{4}$'
            }
        },
        'bank_statement': {
            'name': 'Bank Statement',
            'required_fields': [
                'account_holder_name', 'account_number', 'sort_code',
                'statement_date', 'address', 'bank_name'
            ],
            'field_types': {
                'account_holder_name': 'name',
                'account_number': 'number',
                'sort_code': 'text',
                'statement_date': 'date',
                'address': 'address',
                'bank_name': 'text'
            },
            'validation_rules': {
                'account_number': r'^\d{8}$',
                'sort_code': r'^\d{2}-\d{2}-\d{2}$',
                'statement_date': r'^\d{2}/\d{2}/\d{4}$'
            }
        },
        'utility_bill': {
            'name': 'Utility Bill',
            'required_fields': [
                'account_holder_name', 'service_address', 'bill_date',
                'due_date', 'amount_due', 'supplier_name'
            ],
            'field_types': {
                'account_holder_name': 'name',
                'service_address': 'address',
                'bill_date': 'date',
                'due_date': 'date',
                'amount_due': 'currency',
                'supplier_name': 'text'
            },
            'validation_rules': {
                'bill_date': r'^\d{2}/\d{2}/\d{4}$',
                'due_date': r'^\d{2}/\d{2}/\d{4}$',
                'amount_due': r'^\d+\.\d{2}$'
            }
        }
    }
    
    # Training quality thresholds
    QUALITY_THRESHOLDS = {
        'minimum_confidence': 0.5,
        'good_confidence': 0.7,
        'excellent_confidence': 0.9,
        'minimum_quality_score': 0.6
    }
    
    # Model performance targets
    PERFORMANCE_TARGETS = {
        'minimum_accuracy': 0.8,
        'target_accuracy': 0.9,
        'maximum_loss': 0.3,
        'target_loss': 0.1
    }
    
    # Training triggers
    AUTO_TRAINING_TRIGGERS = {
        'new_samples_threshold': 5,  # Trigger training after N new samples
        'quality_improvement_threshold': 0.05,  # Retrain if quality improves by N
        'max_days_without_training': 30  # Force retrain after N days
    }
    
    @classmethod
    def get_document_config(cls, document_type: str) -> Dict[str, Any]:
        """Get configuration for specific document type"""
        return cls.DOCUMENT_TYPES.get(document_type, {})
    
    @classmethod
    def get_required_fields(cls, document_type: str) -> List[str]:
        """Get required fields for document type"""
        config = cls.get_document_config(document_type)
        return config.get('required_fields', [])
    
    @classmethod
    def get_field_type(cls, document_type: str, field_name: str) -> str:
        """Get field type for specific field"""
        config = cls.get_document_config(document_type)
        field_types = config.get('field_types', {})
        return field_types.get(field_name, 'text')
    
    @classmethod
    def get_validation_rule(cls, document_type: str, field_name: str) -> str:
        """Get validation rule for specific field"""
        config = cls.get_document_config(document_type)
        validation_rules = config.get('validation_rules', {})
        return validation_rules.get(field_name, '')
    
    @classmethod
    def is_supported_document_type(cls, document_type: str) -> bool:
        """Check if document type is supported"""
        return document_type in cls.DOCUMENT_TYPES
    
    @classmethod
    def get_supported_document_types(cls) -> List[str]:
        """Get list of supported document types"""
        return list(cls.DOCUMENT_TYPES.keys())
    
    @classmethod
    def validate_field_value(cls, document_type: str, field_name: str, value: str) -> bool:
        """Validate field value against rules"""
        import re
        
        rule = cls.get_validation_rule(document_type, field_name)
        if not rule:
            return True  # No validation rule, assume valid
        
        try:
            return bool(re.match(rule, value))
        except re.error:
            return True  # Invalid regex, assume valid
    
    @classmethod
    def get_training_instructions(cls, document_type: str) -> Dict[str, Any]:
        """Get training instructions for document type"""
        config = cls.get_document_config(document_type)
        
        return {
            'title': f'{config.get("name", document_type)} Labeling',
            'description': f'Label key fields in {config.get("name", document_type)} documents for training',
            'required_fields': config.get('required_fields', []),
            'field_descriptions': cls._get_field_descriptions(document_type),
            'tips': cls._get_labeling_tips(document_type)
        }
    
    @classmethod
    def _get_field_descriptions(cls, document_type: str) -> Dict[str, str]:
        """Get field descriptions for document type"""
        config = cls.get_document_config(document_type)
        
        # Default descriptions based on field type
        descriptions = {}
        for field in config.get('required_fields', []):
            field_type = cls.get_field_type(document_type, field)
            descriptions[field] = cls._get_default_field_description(field, field_type)
        
        # Override with specific hints if available
        extraction_hints = config.get('extraction_hints', {})
        descriptions.update(extraction_hints)
        
        return descriptions
    
    @classmethod
    def _get_default_field_description(cls, field_name: str, field_type: str) -> str:
        """Get default description for field"""
        field_descriptions = {
            'name': f'Full name as it appears on the document',
            'date': f'Date in DD/MM/YYYY format',
            'currency': f'Amount in pounds and pence (e.g., 1234.56)',
            'address': f'Full address including postcode',
            'text': f'Text value as it appears on the document',
            'number': f'Numeric value only'
        }
        
        return field_descriptions.get(field_type, f'{field_name.replace("_", " ").title()} as shown on document')
    
    @classmethod
    def _get_labeling_tips(cls, document_type: str) -> List[str]:
        """Get labeling tips for document type"""
        general_tips = [
            'Ensure all text is clearly readable in the image',
            'Mark uncertain fields with lower confidence scores',
            'Double-check extracted values for accuracy',
            'Include all relevant information for each field'
        ]
        
        document_specific_tips = {
            'p60': [
                'Look for "To employee:" section for employee details',
                'Tax year is usually at the top of the document',
                'Pay and tax figures are in the main table',
                'Address should include postcode for validation'
            ],
            'p45': [
                'Employee details are usually in the top section',
                'Leaving date is prominently displayed',
                'Pay figures are cumulative to leaving date'
            ],
            'passport': [
                'Personal details are on the photo page',
                'Dates are in DD/MM/YYYY format',
                'Passport number is usually 9 digits'
            ],
            'driving_licence': [
                'Photo card has personal details on front',
                'Address is on the back of photo card',
                'Licence number is alphanumeric'
            ]
        }
        
        specific_tips = document_specific_tips.get(document_type, [])
        return general_tips + specific_tips
