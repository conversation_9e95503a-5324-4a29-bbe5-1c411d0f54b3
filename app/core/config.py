"""
Core configuration management with security-first approach
"""

import os
from functools import lru_cache
from pathlib import Path
from typing import List, Optional

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with comprehensive security configuration"""
    
    # Application
    APP_NAME: str = "SolidTech Document Verification"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    ENVIRONMENT: str = "production"
    
    # Server
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    WORKERS: int = 4
    RELOAD: bool = False
    
    # Security
    SECRET_KEY: str = Field(..., min_length=32)
    JWT_SECRET_KEY: str = Field(..., min_length=32)
    JWT_ALGORITHM: str = "RS256"
    JWT_EXPIRE_MINUTES: int = 30
    ENCRYPTION_KEY: str = Field(..., min_length=32)
    
    # RSA Keys
    RSA_PRIVATE_KEY_PATH: str = "./config/keys/private_key.pem"
    RSA_PUBLIC_KEY_PATH: str = "./config/keys/public_key.pem"
    
    # SSL Configuration
    SSL_KEY_FILE: Optional[str] = None
    SSL_CERT_FILE: Optional[str] = None
    
    # Database
    DATABASE_URL: str = Field(..., regex=r"^postgresql\+asyncpg://")
    DATABASE_POOL_SIZE: int = 20
    DATABASE_MAX_OVERFLOW: int = 30
    DATABASE_POOL_TIMEOUT: int = 30
    
    # Redis
    REDIS_URL: str = "redis://localhost:6379/0"
    REDIS_PASSWORD: Optional[str] = None
    REDIS_MAX_CONNECTIONS: int = 20
    
    # ML Configuration
    MODEL_PATH: str = "./models/trained"
    DEVICE: str = "cuda"
    BATCH_SIZE: int = 32
    MAX_IMAGE_SIZE: int = 2048
    SUPPORTED_FORMATS: List[str] = ["jpg", "jpeg", "png", "pdf", "tiff"]
    
    # OCR Configuration
    TESSERACT_PATH: str = "/usr/bin/tesseract"
    OCR_LANGUAGES: List[str] = ["eng", "fra", "deu", "spa"]
    OCR_CONFIDENCE_THRESHOLD: int = 60
    
    # Document Processing
    MAX_FILE_SIZE: int = 10485760  # 10MB
    PROCESSING_TIMEOUT: int = 300  # 5 minutes
    TEMP_STORAGE_PATH: str = "./temp"

    # Laravel API Interface Integration
    LARAVEL_API_BASE_URL: str = "http://localhost:8001/api/v1"
    LARAVEL_API_TIMEOUT: int = 30

    # Model Training Configuration
    TRAINING_SCHEDULE_ENABLED: bool = True
    TRAINING_DATA_PATH: str = "./training_data"
    MODEL_BACKUP_PATH: str = "./models/backups"
    MIN_TRAINING_SAMPLES: int = 100
    TRAINING_BATCH_SIZE: int = 32
    TRAINING_EPOCHS: int = 50
    QUARTERLY_TRAINING_ENABLED: bool = True
    CLEANUP_INTERVAL: int = 3600  # 1 hour
    
    # Security Limits
    RATE_LIMIT_PER_MINUTE: int = 60
    MAX_CONCURRENT_UPLOADS: int = 10
    IP_WHITELIST: List[str] = ["127.0.0.1", "::1"]
    
    # CORS and Trusted Hosts
    CORS_ORIGINS: List[str] = ["http://localhost:3000", "https://localhost:3000"]
    ALLOWED_HOSTS: List[str] = ["localhost", "127.0.0.1"]
    
    # Monitoring
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"
    SENTRY_DSN: Optional[str] = None
    PROMETHEUS_PORT: int = 9090
    
    # External Services
    NOTIFICATION_SERVICE_URL: Optional[str] = None
    AUDIT_SERVICE_URL: Optional[str] = None
    
    # Development Flags
    MOCK_ML_RESPONSES: bool = False
    SKIP_ENCRYPTION: bool = False
    ALLOW_HTTP: bool = False
    
    @validator("SUPPORTED_FORMATS", pre=True)
    def parse_supported_formats(cls, v):
        if isinstance(v, str):
            return [fmt.strip().lower() for fmt in v.split(",")]
        return v
    
    @validator("OCR_LANGUAGES", pre=True)
    def parse_ocr_languages(cls, v):
        if isinstance(v, str):
            return [lang.strip() for lang in v.split(",")]
        return v
    
    @validator("IP_WHITELIST", pre=True)
    def parse_ip_whitelist(cls, v):
        if isinstance(v, str):
            return [ip.strip() for ip in v.split(",")]
        return v
    
    @validator("CORS_ORIGINS", pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("ALLOWED_HOSTS", pre=True)
    def parse_allowed_hosts(cls, v):
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v
    
    @validator("DEVICE")
    def validate_device(cls, v):
        if v not in ["cuda", "cpu"]:
            raise ValueError("DEVICE must be either 'cuda' or 'cpu'")
        return v
    
    @validator("LOG_LEVEL")
    def validate_log_level(cls, v):
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"LOG_LEVEL must be one of {valid_levels}")
        return v.upper()
    
    @validator("LOG_FORMAT")
    def validate_log_format(cls, v):
        if v not in ["json", "text"]:
            raise ValueError("LOG_FORMAT must be either 'json' or 'text'")
        return v
    
    def get_rsa_private_key_path(self) -> Path:
        """Get the absolute path to the RSA private key"""
        return Path(self.RSA_PRIVATE_KEY_PATH).resolve()
    
    def get_rsa_public_key_path(self) -> Path:
        """Get the absolute path to the RSA public key"""
        return Path(self.RSA_PUBLIC_KEY_PATH).resolve()
    
    def get_model_path(self) -> Path:
        """Get the absolute path to the ML models directory"""
        return Path(self.MODEL_PATH).resolve()
    
    def get_temp_storage_path(self) -> Path:
        """Get the absolute path to the temporary storage directory"""
        return Path(self.TEMP_STORAGE_PATH).resolve()
    
    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self.ENVIRONMENT.lower() == "production"
    
    def is_development(self) -> bool:
        """Check if running in development environment"""
        return self.ENVIRONMENT.lower() in ["development", "dev"]
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings"""
    return Settings()
