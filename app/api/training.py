"""
Training management API endpoints
"""

from datetime import datetime
from typing import Dict, Optional

import structlog
from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel

from app.ml.training_manager import training_manager
from app.ml.training_scheduler import training_scheduler

logger = structlog.get_logger()

router = APIRouter(prefix="/training", tags=["training"])


class TrainingStatusResponse(BaseModel):
    """Training status response model"""
    is_training: bool
    last_training: Optional[datetime]
    training_samples: int
    next_scheduled_training: Optional[datetime]
    scheduler_status: Dict


class TrainingTriggerRequest(BaseModel):
    """Training trigger request model"""
    force: bool = False
    training_type: str = "manual"


class TrainingDataCollectionRequest(BaseModel):
    """Training data collection request model"""
    document_type: str
    extracted_data: Dict
    fraud_analysis: Dict
    user_feedback: Optional[Dict] = None


@router.get("/status", response_model=TrainingStatusResponse)
async def get_training_status():
    """Get current training system status"""
    
    try:
        # Get training data count
        metadata_files = list((training_manager.training_data_path / "metadata").glob("*.json"))
        training_samples = len(metadata_files)
        
        # Get scheduler status
        scheduler_status = training_scheduler.get_scheduler_status()
        
        # Check if training is currently running
        # TODO: Implement training status tracking
        is_training = False
        
        # Get last training timestamp
        # TODO: Implement training history tracking
        last_training = None
        
        return TrainingStatusResponse(
            is_training=is_training,
            last_training=last_training,
            training_samples=training_samples,
            next_scheduled_training=scheduler_status.get('next_training'),
            scheduler_status=scheduler_status
        )
        
    except Exception as e:
        logger.error("Failed to get training status", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve training status"
        )


@router.post("/trigger")
async def trigger_training(request: TrainingTriggerRequest):
    """Trigger immediate model training"""
    
    try:
        logger.info("Training trigger requested", 
                   force=request.force, 
                   training_type=request.training_type)
        
        # Check if we have sufficient data (unless forced)
        if not request.force:
            metadata_files = list((training_manager.training_data_path / "metadata").glob("*.json"))
            
            if len(metadata_files) < training_manager.settings.MIN_TRAINING_SAMPLES:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Insufficient training data. Need at least {training_manager.settings.MIN_TRAINING_SAMPLES} samples, have {len(metadata_files)}"
                )
        
        # Trigger training
        await training_scheduler.trigger_immediate_training()
        
        return {
            "success": True,
            "message": "Training triggered successfully",
            "training_type": request.training_type,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to trigger training", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to trigger training"
        )


@router.post("/collect-data")
async def collect_training_data(request: TrainingDataCollectionRequest):
    """Collect training data for model improvement"""
    
    try:
        # Note: This endpoint is for metadata collection only
        # The actual image data should be collected during document processing
        
        logger.info("Training data collection requested", 
                   document_type=request.document_type)
        
        # TODO: Store training metadata for future use
        # This could be used to update existing training data with user feedback
        
        return {
            "success": True,
            "message": "Training data metadata collected",
            "document_type": request.document_type,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to collect training data", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to collect training data"
        )


@router.get("/data-stats")
async def get_training_data_stats():
    """Get training data statistics"""
    
    try:
        metadata_dir = training_manager.training_data_path / "metadata"
        
        if not metadata_dir.exists():
            return {
                "total_samples": 0,
                "document_types": {},
                "recent_samples": 0
            }
        
        # Count total samples
        metadata_files = list(metadata_dir.glob("*.json"))
        total_samples = len(metadata_files)
        
        # Count by document type
        document_types = {}
        recent_samples = 0
        recent_cutoff = datetime.now().timestamp() - (7 * 24 * 3600)  # 7 days ago
        
        import json
        for metadata_file in metadata_files:
            try:
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                
                doc_type = metadata.get('document_type', 'unknown')
                document_types[doc_type] = document_types.get(doc_type, 0) + 1
                
                # Check if recent
                if metadata_file.stat().st_mtime > recent_cutoff:
                    recent_samples += 1
                    
            except Exception as e:
                logger.warning(f"Failed to read metadata from {metadata_file}", error=str(e))
                continue
        
        return {
            "total_samples": total_samples,
            "document_types": document_types,
            "recent_samples": recent_samples,
            "min_required_samples": training_manager.settings.MIN_TRAINING_SAMPLES
        }
        
    except Exception as e:
        logger.error("Failed to get training data stats", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve training data statistics"
        )


@router.delete("/data/cleanup")
async def cleanup_training_data():
    """Manually trigger training data cleanup"""
    
    try:
        # Trigger cleanup job
        await training_scheduler._cleanup_job()
        
        return {
            "success": True,
            "message": "Training data cleanup completed",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to cleanup training data", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cleanup training data"
        )


@router.get("/models/info")
async def get_model_info():
    """Get information about trained models"""
    
    try:
        model_path = training_manager.model_path
        models_info = []
        
        if model_path.exists():
            for model_file in model_path.glob("*.pth"):
                try:
                    import torch
                    model_data = torch.load(model_file, map_location='cpu')
                    
                    models_info.append({
                        "filename": model_file.name,
                        "timestamp": model_data.get('timestamp'),
                        "training_samples": model_data.get('training_samples', 0),
                        "file_size": model_file.stat().st_size,
                        "modified": datetime.fromtimestamp(model_file.stat().st_mtime).isoformat()
                    })
                    
                except Exception as e:
                    logger.warning(f"Failed to read model info from {model_file}", error=str(e))
                    continue
        
        return {
            "models": models_info,
            "model_path": str(model_path)
        }
        
    except Exception as e:
        logger.error("Failed to get model info", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve model information"
        )


@router.post("/scheduler/start")
async def start_scheduler():
    """Start the training scheduler"""
    
    try:
        if training_scheduler.is_running:
            return {
                "success": True,
                "message": "Training scheduler is already running"
            }
        
        await training_scheduler.start()
        
        return {
            "success": True,
            "message": "Training scheduler started successfully",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to start scheduler", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start training scheduler"
        )


@router.post("/scheduler/stop")
async def stop_scheduler():
    """Stop the training scheduler"""
    
    try:
        if not training_scheduler.is_running:
            return {
                "success": True,
                "message": "Training scheduler is already stopped"
            }
        
        await training_scheduler.stop()
        
        return {
            "success": True,
            "message": "Training scheduler stopped successfully",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to stop scheduler", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to stop training scheduler"
        )
