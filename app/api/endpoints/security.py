"""
Security endpoints for key exchange and encryption
"""

import structlog
from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException, status

from app.core.security import jwt_bearer, security_manager

logger = structlog.get_logger()
router = APIRouter()


@router.get("/public-key")
async def get_public_key():
    """
    Get RSA public key for client-side encryption
    """
    
    try:
        public_key_pem = security_manager.get_public_key_pem()
        
        return {
            "public_key": public_key_pem,
            "algorithm": "RSA-4096",
            "usage": "encryption"
        }
        
    except Exception as e:
        logger.error("❌ Failed to retrieve public key", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve public key"
        )


@router.post("/verify-signature")
async def verify_signature(
    data: str,
    signature: str,
    current_user: dict = Depends(jwt_bearer)
):
    """
    Verify HMAC signature for data integrity
    """
    
    try:
        is_valid = security_manager.verify_hmac_signature(
            data.encode(), signature
        )
        
        return {
            "valid": is_valid,
            "message": "Signature verified" if is_valid else "Invalid signature"
        }
        
    except Exception as e:
        logger.error("❌ Signature verification failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Signature verification failed"
        )
