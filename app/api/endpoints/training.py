"""
Document Training API Endpoints
Handles document labeling, training data collection, and model training
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any
import uuid

import structlog
from fastapi import APIRouter, HTTPException, status, UploadFile, File, Form, Depends
from pydantic import BaseModel

from app.core.security import jwt_bearer
from app.services.document_training_service import document_training_service
from app.utils.file_handler import FileHandler

logger = structlog.get_logger()
router = APIRouter(prefix="/training", tags=["training"])


class LabelingSessionRequest(BaseModel):
    """Request to create labeling session"""
    document_type: str
    target_count: Optional[int] = 20


class LabelingSessionResponse(BaseModel):
    """Labeling session response"""
    session_id: str
    document_type: str
    created_at: str
    status: str
    labeled_count: int
    target_count: int
    instructions: Dict[str, Any]


class LabeledFieldData(BaseModel):
    """Labeled field data"""
    value: str
    confidence: float
    bounding_box: Optional[Dict[str, float]] = None
    field_type: str = "text"
    notes: Optional[str] = None


class SubmitLabeledDocumentRequest(BaseModel):
    """Request to submit labeled document"""
    session_id: str
    labeled_fields: Dict[str, LabeledFieldData]
    bounding_boxes: Optional[Dict[str, Dict[str, float]]] = None
    quality_score: Optional[float] = 1.0
    notes: Optional[str] = None


class TrainingStatsResponse(BaseModel):
    """Training statistics response"""
    total_labeled_documents: int
    by_document_type: Dict[str, int]
    training_readiness: Dict[str, Dict[str, Any]]
    recent_activity: List[Dict[str, Any]]
    model_performance: Dict[str, Any]


class TrainModelRequest(BaseModel):
    """Request to train model"""
    document_type: str
    force: bool = False


@router.post("/sessions", response_model=LabelingSessionResponse)
async def create_labeling_session(
    request: LabelingSessionRequest,
    current_user: Dict = Depends(jwt_bearer)
):
    """Create a new document labeling session"""
    
    try:
        logger.info("Creating labeling session", 
                   document_type=request.document_type,
                   user_id=current_user.get('user_id'))
        
        session_data = await document_training_service.create_labeling_session(
            document_type=request.document_type,
            user_id=current_user.get('user_id')
        )
        
        # Update target count if provided
        if request.target_count:
            session_data['target_count'] = request.target_count
        
        return LabelingSessionResponse(**session_data)
        
    except Exception as e:
        logger.error("Failed to create labeling session", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create labeling session"
        )


@router.post("/sessions/{session_id}/documents")
async def upload_document_for_labeling(
    session_id: str,
    file: UploadFile = File(...),
    current_user: Dict = Depends(jwt_bearer)
):
    """Upload a document for labeling"""
    
    try:
        # Validate file type
        if not file.content_type.startswith('image/'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Only image files are supported"
            )
        
        # Save uploaded file
        file_handler = FileHandler()
        file_content = await file.read()
        
        # Generate unique filename
        file_extension = file.filename.split('.')[-1] if '.' in file.filename else 'jpg'
        unique_filename = f"training_{session_id}_{uuid.uuid4().hex}.{file_extension}"
        
        # Save file to training directory
        training_data_path = document_training_service.training_data_path / "images"
        training_data_path.mkdir(exist_ok=True)
        
        file_path = training_data_path / unique_filename
        with open(file_path, 'wb') as f:
            f.write(file_content)
        
        logger.info("Document uploaded for labeling", 
                   session_id=session_id,
                   filename=unique_filename)
        
        return {
            'success': True,
            'file_path': str(file_path),
            'filename': unique_filename,
            'message': 'Document uploaded successfully'
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to upload document", 
                    session_id=session_id, 
                    error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload document"
        )


@router.post("/sessions/{session_id}/submit")
async def submit_labeled_document(
    session_id: str,
    request: SubmitLabeledDocumentRequest,
    current_user: Dict = Depends(jwt_bearer)
):
    """Submit a labeled document for training"""
    
    try:
        logger.info("Submitting labeled document", 
                   session_id=session_id,
                   user_id=current_user.get('user_id'))
        
        # Convert Pydantic models to dict
        document_data = {
            'image_path': request.image_path if hasattr(request, 'image_path') else '',
            'labeled_fields': {
                field_name: {
                    'value': field_data.value,
                    'confidence': field_data.confidence,
                    'bounding_box': field_data.bounding_box,
                    'field_type': field_data.field_type,
                    'notes': field_data.notes
                }
                for field_name, field_data in request.labeled_fields.items()
            },
            'bounding_boxes': request.bounding_boxes or {},
            'quality_score': request.quality_score or 1.0,
            'notes': request.notes or ''
        }
        
        result = await document_training_service.submit_labeled_document(
            session_id=session_id,
            document_data=document_data
        )
        
        return result
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Failed to submit labeled document", 
                    session_id=session_id, 
                    error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to submit labeled document"
        )


@router.get("/statistics", response_model=TrainingStatsResponse)
async def get_training_statistics(
    current_user: Dict = Depends(jwt_bearer)
):
    """Get training data statistics"""
    
    try:
        stats = await document_training_service.get_training_statistics()
        return TrainingStatsResponse(**stats)
        
    except Exception as e:
        logger.error("Failed to get training statistics", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get training statistics"
        )


@router.post("/models/train")
async def train_document_model(
    request: TrainModelRequest,
    current_user: Dict = Depends(jwt_bearer)
):
    """Train ML model for specific document type"""
    
    try:
        logger.info("Starting model training", 
                   document_type=request.document_type,
                   force=request.force,
                   user_id=current_user.get('user_id'))
        
        # Start training in background
        training_task = asyncio.create_task(
            document_training_service.train_document_model(
                document_type=request.document_type,
                force=request.force
            )
        )
        
        return {
            'success': True,
            'message': 'Model training started',
            'document_type': request.document_type,
            'timestamp': datetime.now().isoformat()
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Failed to start model training", 
                    document_type=request.document_type, 
                    error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start model training"
        )


@router.get("/models/{document_type}/status")
async def get_model_training_status(
    document_type: str,
    current_user: Dict = Depends(jwt_bearer)
):
    """Get training status for specific document type"""
    
    try:
        # Check if model exists and get info
        model_registry_file = document_training_service.model_path / "model_registry.json"
        
        if not model_registry_file.exists():
            return {
                'document_type': document_type,
                'status': 'not_trained',
                'message': 'No trained model found'
            }
        
        import json
        with open(model_registry_file, 'r') as f:
            registry = json.load(f)
        
        if document_type not in registry:
            return {
                'document_type': document_type,
                'status': 'not_trained',
                'message': 'No trained model found for this document type'
            }
        
        model_info = registry[document_type]
        
        return {
            'document_type': document_type,
            'status': model_info.get('status', 'unknown'),
            'trained_at': model_info.get('trained_at'),
            'training_results': model_info.get('training_results', {}),
            'version': model_info.get('version', '1.0.0'),
            'model_path': model_info.get('model_path')
        }
        
    except Exception as e:
        logger.error("Failed to get model status", 
                    document_type=document_type, 
                    error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get model status"
        )


@router.get("/sessions/{session_id}")
async def get_labeling_session(
    session_id: str,
    current_user: Dict = Depends(jwt_bearer)
):
    """Get labeling session details"""
    
    try:
        session_file = document_training_service.labeled_data_path / f"session_{session_id}.json"
        
        if not session_file.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )
        
        import json
        with open(session_file, 'r') as f:
            session_data = json.load(f)
        
        return session_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get labeling session", 
                    session_id=session_id, 
                    error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get labeling session"
        )
