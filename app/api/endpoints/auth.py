"""
Authentication endpoints
"""

from datetime import timedelta
from typing import Dict

import structlog
from fastapi import <PERSON>Router, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm

from app.core.security import security_manager
from app.models.auth import Token, TokenData

logger = structlog.get_logger()
router = APIRouter()


@router.post("/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    """
    OAuth2 compatible token login endpoint
    """
    
    # TODO: Implement user authentication against database
    # For now, using hardcoded credentials for development
    
    if form_data.username == "admin" and form_data.password == "secure_password":
        access_token_expires = timedelta(minutes=30)
        access_token = security_manager.create_jwt_token(
            data={"sub": form_data.username, "type": "access"},
            expires_delta=access_token_expires
        )
        
        logger.info("✅ User authenticated successfully", username=form_data.username)
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": 1800
        }
    
    logger.warning("❌ Authentication failed", username=form_data.username)
    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Incorrect username or password",
        headers={"WWW-Authenticate": "Bearer"},
    )


@router.post("/refresh")
async def refresh_token(current_user: Dict = Depends(security_manager.verify_jwt_token)):
    """
    Refresh access token
    """
    
    access_token_expires = timedelta(minutes=30)
    access_token = security_manager.create_jwt_token(
        data={"sub": current_user["sub"], "type": "access"},
        expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": 1800
    }
