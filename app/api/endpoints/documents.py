"""
Document processing endpoints with secure upload and ML analysis
"""

import asyncio
import uuid
from datetime import datetime
from typing import Dict, List, Optional

import structlog
from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile, status
from fastapi.responses import J<PERSON><PERSON>esponse

from app.core.security import jwt_bearer, security_manager
from app.ml.document_processor import DocumentProcessor
from app.ml.fraud_detector import FraudDetector
from app.ml.ocr_engine import OCREngine
from app.models.document import DocumentAnalysisRequest, DocumentAnalysisResponse
from app.models.verification import VerificationResult
from app.utils.file_handler import FileHandler
from app.services.document_extraction_service import DocumentExtractionService
from app.services.laravel_api_service import LaravelAPIService
from app.services.model_training_service import ModelTrainingService

logger = structlog.get_logger()
router = APIRouter()

# Initialize processors and services
document_processor = DocumentProcessor()
fraud_detector = FraudDetector()
ocr_engine = OCREngine()
file_handler = FileHandler()
document_extraction_service = DocumentExtractionService()
laravel_api_service = LaravelAPIService()
model_training_service = ModelTrainingService()


@router.post("/upload", response_model=DocumentAnalysisResponse)
async def upload_document(
    file: UploadFile = File(...),
    application_data: str = Form(...),
    document_type: str = Form(...),
    current_user: Dict = Depends(jwt_bearer)
):
    """
    Secure document upload with immediate ML analysis
    
    Features:
    - End-to-end encryption
    - Real-time fraud detection
    - OCR data extraction
    - Application data validation
    - Comprehensive security checks
    """
    
    request_id = str(uuid.uuid4())
    logger.info("📄 Document upload initiated", request_id=request_id, 
                document_type=document_type, user_id=current_user.get("sub"))
    
    try:
        # Validate file
        await file_handler.validate_upload(file)
        
        # Read and encrypt file content
        file_content = await file.read()
        encrypted_content = security_manager.encrypt_data(file_content)
        
        # Parse application data
        import json
        app_data = json.loads(application_data)
        
        # Create analysis request
        analysis_request = DocumentAnalysisRequest(
            request_id=request_id,
            document_type=document_type,
            file_name=file.filename,
            file_size=len(file_content),
            application_data=app_data,
            user_id=current_user.get("sub"),
            timestamp=datetime.utcnow()
        )
        
        # Process document through ML pipeline
        results = await _process_document_pipeline(
            encrypted_content, analysis_request
        )
        
        # Create response
        response = DocumentAnalysisResponse(
            request_id=request_id,
            status="completed",
            processing_time=results["processing_time"],
            verification_result=results["verification"],
            fraud_analysis=results["fraud_analysis"],
            ocr_results=results["ocr_results"],
            data_matching=results["data_matching"],
            security_checks=results["security_checks"],
            confidence_score=results["confidence_score"],
            recommendations=results["recommendations"]
        )
        
        logger.info("✅ Document analysis completed", 
                   request_id=request_id, 
                   confidence_score=results["confidence_score"])
        
        return response
        
    except Exception as e:
        logger.error("❌ Document processing failed", 
                    request_id=request_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Document processing failed: {str(e)}"
        )
    
    finally:
        # Cleanup temporary files
        await file_handler.cleanup_temp_files(request_id)


@router.get("/status/{request_id}")
async def get_processing_status(
    request_id: str,
    current_user: Dict = Depends(jwt_bearer)
):
    """Get document processing status"""
    
    # TODO: Implement status tracking with Redis/Database
    return {
        "request_id": request_id,
        "status": "completed",
        "progress": 100,
        "estimated_completion": None
    }


@router.get("/results/{request_id}", response_model=DocumentAnalysisResponse)
async def get_analysis_results(
    request_id: str,
    current_user: Dict = Depends(jwt_bearer)
):
    """Retrieve document analysis results"""
    
    # TODO: Implement result retrieval from database
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="Results not found or expired"
    )


@router.delete("/results/{request_id}")
async def delete_analysis_results(
    request_id: str,
    current_user: Dict = Depends(jwt_bearer)
):
    """Securely delete analysis results and associated data"""
    
    logger.info("🗑️ Deleting analysis results", request_id=request_id)
    
    # TODO: Implement secure deletion
    return {"message": "Results deleted successfully"}


async def _process_document_pipeline(
    encrypted_content: bytes, 
    request: DocumentAnalysisRequest
) -> Dict:
    """
    Comprehensive document processing pipeline
    """
    
    start_time = datetime.utcnow()
    
    try:
        # Decrypt content for processing
        file_content = security_manager.decrypt_data(encrypted_content)
        
        # Step 1: Document preprocessing and quality assessment
        logger.info("🔍 Starting document preprocessing", request_id=request.request_id)
        preprocessed_image = await document_processor.preprocess_document(
            file_content, request.document_type
        )
        
        # Step 2: Fraud detection analysis
        logger.info("🚨 Running fraud detection", request_id=request.request_id)
        fraud_analysis = await fraud_detector.analyze_document(
            preprocessed_image, request.document_type
        )
        
        # Step 3: OCR and data extraction
        logger.info("📝 Extracting text data", request_id=request.request_id)
        ocr_results = await ocr_engine.extract_data(
            preprocessed_image, request.document_type
        )
        
        # Step 4: Data matching with application
        logger.info("🔗 Matching extracted data", request_id=request.request_id)
        data_matching = await _match_extracted_data(
            ocr_results, request.application_data
        )
        
        # Step 5: Security checks
        logger.info("🛡️ Running security checks", request_id=request.request_id)
        security_checks = await _run_security_checks(
            file_content, preprocessed_image, request
        )
        
        # Step 6: Calculate overall confidence score
        confidence_score = _calculate_confidence_score(
            fraud_analysis, data_matching, security_checks
        )
        
        # Step 7: Generate recommendations
        recommendations = _generate_recommendations(
            fraud_analysis, data_matching, security_checks, confidence_score
        )
        
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        
        return {
            "processing_time": processing_time,
            "verification": VerificationResult(
                is_authentic=confidence_score > 0.8,
                is_original=not fraud_analysis.get("is_copy", False),
                is_unaltered=not fraud_analysis.get("is_altered", False),
                data_matches=data_matching.get("overall_match", False)
            ),
            "fraud_analysis": fraud_analysis,
            "ocr_results": ocr_results,
            "data_matching": data_matching,
            "security_checks": security_checks,
            "confidence_score": confidence_score,
            "recommendations": recommendations
        }
        
    except Exception as e:
        logger.error("❌ Pipeline processing failed", 
                    request_id=request.request_id, error=str(e))
        raise


async def _match_extracted_data(ocr_results: Dict, application_data: Dict) -> Dict:
    """Match extracted OCR data with application form data"""
    
    matches = {}
    
    # Name matching with fuzzy logic
    extracted_name = ocr_results.get("full_name", "").lower().strip()
    app_name = f"{application_data.get('forename', '')} {application_data.get('surname', '')}".lower().strip()
    
    if extracted_name and app_name:
        from difflib import SequenceMatcher
        name_similarity = SequenceMatcher(None, extracted_name, app_name).ratio()
        matches["name_match"] = name_similarity > 0.85
        matches["name_similarity"] = name_similarity
    
    # Date of birth matching
    extracted_dob = ocr_results.get("date_of_birth")
    app_dob = application_data.get("date_of_birth")
    matches["dob_match"] = extracted_dob == app_dob if extracted_dob and app_dob else None
    
    # Address matching
    extracted_address = ocr_results.get("address", "").lower().strip()
    app_address = application_data.get("current_address", "").lower().strip()
    
    if extracted_address and app_address:
        address_similarity = SequenceMatcher(None, extracted_address, app_address).ratio()
        matches["address_match"] = address_similarity > 0.8
        matches["address_similarity"] = address_similarity
    
    # National Insurance Number matching
    extracted_ni = ocr_results.get("ni_number", "").replace(" ", "").upper()
    app_ni = application_data.get("ni_number", "").replace(" ", "").upper()
    matches["ni_match"] = extracted_ni == app_ni if extracted_ni and app_ni else None
    
    # Calculate overall match score
    valid_matches = [v for v in matches.values() if isinstance(v, bool)]
    matches["overall_match"] = sum(valid_matches) / len(valid_matches) if valid_matches else False
    
    return matches


async def _run_security_checks(file_content: bytes, image, request: DocumentAnalysisRequest) -> Dict:
    """Run comprehensive security checks"""
    
    checks = {
        "file_integrity": True,
        "metadata_analysis": {},
        "digital_signature": None,
        "exif_data": {},
        "file_format_valid": True,
        "suspicious_patterns": []
    }
    
    # TODO: Implement detailed security checks
    # - EXIF data analysis
    # - Metadata examination
    # - File format validation
    # - Digital signature verification
    # - Suspicious pattern detection
    
    return checks


def _calculate_confidence_score(fraud_analysis: Dict, data_matching: Dict, security_checks: Dict) -> float:
    """Calculate overall confidence score (0.0 to 1.0)"""
    
    score = 1.0
    
    # Fraud analysis impact
    if fraud_analysis.get("is_copy", False):
        score -= 0.5
    if fraud_analysis.get("is_altered", False):
        score -= 0.4
    
    # Data matching impact
    if not data_matching.get("overall_match", True):
        score -= 0.3
    
    # Security checks impact
    if security_checks.get("suspicious_patterns"):
        score -= 0.2
    
    return max(0.0, min(1.0, score))


def _generate_recommendations(fraud_analysis: Dict, data_matching: Dict, 
                            security_checks: Dict, confidence_score: float) -> List[str]:
    """Generate actionable recommendations based on analysis"""
    
    recommendations = []
    
    if confidence_score < 0.5:
        recommendations.append("REJECT: Document fails multiple verification checks")
    elif confidence_score < 0.7:
        recommendations.append("MANUAL_REVIEW: Document requires human verification")
    elif confidence_score < 0.9:
        recommendations.append("ACCEPT_WITH_CAUTION: Document passes basic checks")
    else:
        recommendations.append("ACCEPT: Document passes all verification checks")
    
    # Specific recommendations
    if fraud_analysis.get("is_copy"):
        recommendations.append("Document appears to be a copy, request original")
    
    if fraud_analysis.get("is_altered"):
        recommendations.append("Document shows signs of alteration")
    
    if not data_matching.get("name_match", True):
        recommendations.append("Name on document does not match application")
    
    if not data_matching.get("address_match", True):
        recommendations.append("Address on document does not match application")
    
    return recommendations


@router.post("/ai-extract")
async def ai_document_extraction(
    file: UploadFile = File(...),
    document_type: str = Form(...),
    application_id: int = Form(...),
    current_user: Dict = Depends(jwt_bearer)
):
    """
    AI-powered document data extraction with user review workflow
    """
    request_id = str(uuid.uuid4())

    try:
        logger.info("Starting AI document extraction",
                   request_id=request_id,
                   document_type=document_type,
                   application_id=application_id,
                   user_id=current_user.get("user_id"))

        # Validate file
        await file_handler.validate_upload(file)

        # Read and process image
        file_content = await file.read()
        document_image = await document_processor.preprocess_image(file_content)

        # Get application data from Laravel API
        user_token = current_user.get("token")
        app_data_response = await laravel_api_service.get_application_data(application_id, user_token)

        if not app_data_response['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed to fetch application data: {app_data_response.get('error')}"
            )

        application_data = app_data_response['data']
        application_data['application_id'] = application_id

        # Process document using AI extraction service
        review_data = await document_extraction_service.process_document_ai_path(
            document_image=document_image,
            document_type=document_type,
            application_data=application_data,
            user_token=user_token
        )

        # Collect training data for ML model improvement
        try:
            await document_processor.collect_training_data(
                file_content=file_content,
                document_type=document_type,
                extracted_data=review_data.get('extracted_fields', {}),
                fraud_analysis=review_data.get('fraud_analysis', {}),
                user_feedback=None  # Will be updated when user provides feedback
            )
            logger.info("Training data collected for AI extraction",
                       request_id=request_id, document_type=document_type)
        except Exception as e:
            logger.warning("Failed to collect training data",
                          request_id=request_id, error=str(e))

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "request_id": request_id,
                "processing_method": "ai_extraction",
                "review_data": review_data,
                "message": "Document processed successfully. Please review extracted data."
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error("AI document extraction failed", error=str(e), request_id=request_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI document extraction failed: {str(e)}"
        )


@router.post("/manual-entry")
async def manual_document_entry(
    document_type: str = Form(...),
    application_id: int = Form(...),
    document_data: str = Form(...),  # JSON string of manual data
    document_file: Optional[UploadFile] = File(None),
    current_user: Dict = Depends(jwt_bearer)
):
    """
    Manual document data entry with direct Laravel API integration
    """
    request_id = str(uuid.uuid4())

    try:
        logger.info("Starting manual document entry",
                   request_id=request_id,
                   document_type=document_type,
                   application_id=application_id,
                   user_id=current_user.get("user_id"))

        # Parse manual data
        import json
        try:
            manual_data = json.loads(document_data)
        except json.JSONDecodeError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid document_data JSON format"
            )

        # Get application data from Laravel API
        user_token = current_user.get("token")
        app_data_response = await laravel_api_service.get_application_data(application_id, user_token)

        if not app_data_response['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed to fetch application data: {app_data_response.get('error')}"
            )

        application_data = app_data_response['data']
        application_data['application_id'] = application_id

        # Process document file if provided
        document_file_bytes = None
        if document_file:
            await file_handler.validate_upload(document_file)
            document_file_bytes = await document_file.read()

        # Process using manual entry service
        result = await document_extraction_service.process_document_manual_path(
            document_type=document_type,
            manual_data=manual_data,
            application_data=application_data,
            user_token=user_token,
            document_file=document_file_bytes
        )

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "request_id": request_id,
                "processing_method": "manual_entry",
                "result": result,
                "message": "Document data saved successfully via manual entry."
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Manual document entry failed", error=str(e), request_id=request_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Manual document entry failed: {str(e)}"
        )


@router.post("/submit-ai-review")
async def submit_ai_reviewed_document(
    request_id: str = Form(...),
    user_corrections: str = Form(...),  # JSON string of user corrections
    application_id: int = Form(...),
    current_user: Dict = Depends(jwt_bearer)
):
    """
    Submit AI-extracted document data after user review and corrections
    """
    try:
        logger.info("Submitting AI-reviewed document",
                   request_id=request_id,
                   application_id=application_id,
                   user_id=current_user.get("user_id"))

        # Parse user corrections
        import json
        try:
            corrections = json.loads(user_corrections)
        except json.JSONDecodeError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid user_corrections JSON format"
            )

        # Get application data from Laravel API
        user_token = current_user.get("token")
        app_data_response = await laravel_api_service.get_application_data(application_id, user_token)

        if not app_data_response['success']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed to fetch application data: {app_data_response.get('error')}"
            )

        application_data = app_data_response['data']
        application_data['application_id'] = application_id

        # Retrieve review data (in production, get from Redis/database using request_id)
        # For now, we'll need the review data to be passed or stored
        review_data = {
            'document_type': 'passport',  # This should come from stored data
            'extracted_fields': {},  # This should come from stored data
            'extraction_confidence': 0.85,
            'fraud_analysis': {}
        }

        # Process submission using AI review service
        result = await document_extraction_service.submit_ai_reviewed_document(
            review_data=review_data,
            user_corrections=corrections,
            application_data=application_data,
            user_token=user_token
        )

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "request_id": request_id,
                "processing_method": "ai_assisted",
                "result": result,
                "message": "AI-reviewed document submitted successfully."
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error("AI review submission failed", error=str(e), request_id=request_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI review submission failed: {str(e)}"
        )


@router.post("/trigger-training")
async def trigger_model_training(
    current_user: Dict = Depends(jwt_bearer)
):
    """
    Manually trigger model training with accumulated user data
    """
    try:
        logger.info("Manual model training triggered",
                   user_id=current_user.get("user_id"))

        # Check if user has admin privileges (implement proper authorization)
        user_type = current_user.get("user_type", "")
        if user_type not in ["admin", "super_admin"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient privileges to trigger model training"
            )

        # Trigger training
        result = await model_training_service.trigger_manual_training(
            user_id=current_user.get("user_id")
        )

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": result['success'],
                "message": result['message'],
                "training_result": result.get('result'),
                "triggered_by": current_user.get("user_id"),
                "timestamp": result.get('timestamp')
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Model training trigger failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Model training trigger failed: {str(e)}"
        )


@router.get("/training-status")
async def get_training_status(
    current_user: Dict = Depends(jwt_bearer)
):
    """
    Get current model training status and statistics
    """
    try:
        status_info = await model_training_service.get_training_status()

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "training_status": status_info,
                "message": "Training status retrieved successfully"
            }
        )

    except Exception as e:
        logger.error("Failed to get training status", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get training status: {str(e)}"
        )


@router.get("/training-history")
async def get_training_history(
    limit: int = 10,
    current_user: Dict = Depends(jwt_bearer)
):
    """
    Get model training history
    """
    try:
        history = await model_training_service.get_training_history(limit=limit)

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "success": True,
                "training_history": history,
                "message": "Training history retrieved successfully"
            }
        )

    except Exception as e:
        logger.error("Failed to get training history", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get training history: {str(e)}"
        )
