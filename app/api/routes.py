"""
Main API router configuration
"""

from fastapi import APIRouter

from app.api.endpoints import auth, documents, security, health
from app.api import training

# Create main API router
api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["Authentication"]
)

api_router.include_router(
    security.router,
    prefix="/security",
    tags=["Security"]
)

api_router.include_router(
    documents.router,
    prefix="/documents",
    tags=["Document Processing"]
)

api_router.include_router(
    health.router,
    prefix="/health",
    tags=["Health Checks"]
)

api_router.include_router(
    training.router,
    tags=["ML Training"]
)
