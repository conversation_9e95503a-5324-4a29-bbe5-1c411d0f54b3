<?php

declare(strict_types=1);

namespace App\Providers;

use <PERSON>do<PERSON>\Scramble\Scramble;
use Dedoc\Scramble\Support\Generator\InfoObject;
use Dedoc\Scramble\Support\Generator\OpenApi;
use Dedoc\Scramble\Support\Generator\SecurityScheme;
use Illuminate\Support\ServiceProvider;

class ScrambleServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        Scramble::afterOpenApiGenerated(function (OpenApi $openApi) {
            // Add security scheme
            $openApi->secure(
                SecurityScheme::http('bearer', 'sanctum')
                    ->setDescription('Laravel Sanctum Bearer Token Authentication. Use the token received from the login endpoint.')
            );

            // Set API info
            $info = new InfoObject('API Interface Documentation', '1.0.0');
            $info->setDescription('API interface for Flutter application to manage entities, applications, and user authentication for portal users.');
            $openApi->setInfo($info);
        });
    }
}
