<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Database\Eloquent\Relations\Relation;
use App\Modules\Auth\Models\PortalUser;
use App\Modules\Applications\Services\ApplicationDashboardService;
use App\Modules\Applications\ViewModels\ApplicationDashboardViewModel;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register Application Dashboard services
        $this->app->singleton(ApplicationDashboardViewModel::class);
        $this->app->singleton(ApplicationDashboardService::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Define morph map for cleaner polymorphic type names
        Relation::enforceMorphMap([
            'PortalUser' => PortalUser::class,
        ]);
    }
}
