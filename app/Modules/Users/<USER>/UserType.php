<?php

declare(strict_types=1);

namespace App\Modules\Users\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Modules\Auth\Models\PortalUser;

class UserType extends Model
{
    protected $table = 'user_types';

    protected $fillable = [
        'code',
        'name',
        'description'
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function users(): HasMany
    {
        return $this->hasMany(PortalUser::class, 'user_type', 'code');
    }
}
