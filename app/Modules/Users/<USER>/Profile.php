<?php

declare(strict_types=1);

namespace App\Modules\Users\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Modules\Auth\Models\PortalUser;

class Profile extends Model
{
    protected $table = 'portal_user_profiles';

    protected $fillable = [
        'user_id',
        'first_name',
        'last_name',
        'telephone',
        'address',
        'active',
        'two_factor_enabled',
        'two_factor_secret'
    ];

    protected $casts = [
        'active' => 'boolean',
        'two_factor_enabled' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(PortalUser::class, 'user_id');
    }

    public function getFullNameAttribute(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }
}
