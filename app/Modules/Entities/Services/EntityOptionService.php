<?php

declare(strict_types=1);

namespace App\Modules\Entities\Services;

use App\Modules\Entities\Models\Entity;
use App\Modules\Entities\Models\EntityOption;
use App\Modules\Entities\Models\Option;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class EntityOptionService
{
    public function getOptionValue(string $optionName, int $entityId): mixed
    {
        try {
            $entity = Entity::find($entityId);
            
            if (!$entity) {
                return null;
            }

            $optionValue = $this->getOptionWithHierarchy($optionName, $entity);
            
            return $this->formatOptionValue($optionValue);
            
        } catch (\Exception $e) {
            Log::error('Error getting option value', [
                'option_name' => $optionName,
                'entity_id' => $entityId,
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }

    public function getOptionWithSource(string $optionName, int $entityId): array
    {
        try {
            $entity = Entity::find($entityId);
            
            if (!$entity) {
                return [
                    'value' => null,
                    'source' => 'entity_not_found',
                    'source_id' => null,
                    'source_name' => null,
                    'hierarchy_path' => []
                ];
            }

            $result = $this->getOptionWithHierarchyAndSource($optionName, $entity);
            
            return [
                'value' => $this->formatOptionValue($result['value']),
                'source' => $result['source'],
                'source_id' => $result['source_id'],
                'source_name' => $result['source_name'],
                'hierarchy_path' => $result['hierarchy_path']
            ];
            
        } catch (\Exception $e) {
            Log::error('Error getting option with source', [
                'option_name' => $optionName,
                'entity_id' => $entityId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'value' => null,
                'source' => 'error',
                'source_id' => null,
                'source_name' => 'Error retrieving option',
                'hierarchy_path' => []
            ];
        }
    }

    private function getOptionWithHierarchy(string $optionName, Entity $entity): ?string
    {
        $option = Option::where('name', $optionName)->first();
        if (!$option) {
            return null;
        }

        $entityOption = EntityOption::where('entity_id', $entity->id)
            ->where('option_id', $option->id)
            ->first();

        if ($entityOption && $entityOption->is_overridden) {
            return $entityOption->value;
        }

        $parentEntity = $this->getParentEntity($entity->id);

        if ($parentEntity) {
            $parentValue = $this->getOptionWithHierarchy($optionName, $parentEntity);
            if ($parentValue !== null) {
                return $parentValue;
            }
        }

        return $entityOption ? $entityOption->value : null;
    }

    private function getOptionWithHierarchyAndSource(string $optionName, Entity $entity): array
    {
        $option = Option::where('name', $optionName)->first();
        if (!$option) {
            return [
                'value' => null,
                'source' => 'not_found',
                'source_id' => null,
                'source_name' => null,
                'hierarchy_path' => []
            ];
        }

        $hierarchyPath = [];
        $currentEntity = $entity;

        while ($currentEntity) {
            $hierarchyPath[] = [
                'id' => $currentEntity->id,
                'name' => $currentEntity->name,
                'type' => $currentEntity->entity_type->value
            ];

            $entityOption = EntityOption::where('entity_id', $currentEntity->id)
                ->where('option_id', $option->id)
                ->first();

            if ($entityOption && $entityOption->is_overridden) {
                return [
                    'value' => $entityOption->value,
                    'source' => 'entity',
                    'source_id' => $currentEntity->id,
                    'source_name' => $currentEntity->name,
                    'hierarchy_path' => $hierarchyPath
                ];
            }

            $currentEntity = $this->getParentEntity($currentEntity->id);
        }

        $lastEntityOption = EntityOption::where('entity_id', $entity->id)
            ->where('option_id', $option->id)
            ->first();

        return [
            'value' => $lastEntityOption ? $lastEntityOption->value : null,
            'source' => $lastEntityOption ? 'entity_default' : 'not_found',
            'source_id' => $lastEntityOption ? $entity->id : null,
            'source_name' => $lastEntityOption ? $entity->name : null,
            'hierarchy_path' => $hierarchyPath
        ];
    }

    private function getParentEntity(int $entityId): ?Entity
    {
        $parentRelation = DB::table('entity_relationships')
            ->where('child_entity_id', $entityId)
            ->first();

        if (!$parentRelation) {
            return null;
        }

        return Entity::find($parentRelation->parent_entity_id);
    }

    private function formatOptionValue(?string $value): mixed
    {
        if ($value === null) {
            return null;
        }

        if ($value === 'true' || $value === '1') {
            return true;
        }
        
        if ($value === 'false' || $value === '0') {
            return false;
        }

        if (is_numeric($value)) {
            return str_contains($value, '.') ? (float) $value : (int) $value;
        }

        if ($this->isJson($value)) {
            return json_decode($value, true);
        }

        return $value;
    }

    private function isJson(string $string): bool
    {
        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }

    public function getUserAccessibleEntityIds(int $userId): array
    {
        $directEntityIds = DB::table('entity_user_links')
            ->where('user_id', $userId)
            ->pluck('entity_id')
            ->toArray();

        $allAccessibleIds = $directEntityIds;

        foreach ($directEntityIds as $entityId) {
            $childEntities = \SolidFuse\Modules\Entities\Helpers\EntityHelper::getChildEntities($entityId);
            $childEntityIds = $childEntities->pluck('id')->toArray();
            $allAccessibleIds = array_merge($allAccessibleIds, $childEntityIds);

            $this->addChildEntitiesRecursively($childEntityIds, $allAccessibleIds);
        }

        return array_unique($allAccessibleIds);
    }

    private function addChildEntitiesRecursively(array $entityIds, array &$allAccessibleIds): void
    {
        foreach ($entityIds as $entityId) {
            $childEntities = \SolidFuse\Modules\Entities\Helpers\EntityHelper::getChildEntities($entityId);
            $childEntityIds = $childEntities->pluck('id')->toArray();

            if (!empty($childEntityIds)) {
                $allAccessibleIds = array_merge($allAccessibleIds, $childEntityIds);
                $this->addChildEntitiesRecursively($childEntityIds, $allAccessibleIds);
            }
        }
    }
}
