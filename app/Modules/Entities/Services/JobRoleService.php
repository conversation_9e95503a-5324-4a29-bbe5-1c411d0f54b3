<?php

declare(strict_types=1);

namespace App\Modules\Entities\Services;

use App\Modules\Entities\Contracts\JobRoleServiceInterface;
use App\Modules\Entities\Contracts\JobRoleRepositoryInterface;
use App\Modules\Entities\Models\JobRole;
use App\Modules\Entities\Models\Entity;
use SolidFuse\Modules\Billing\Services\BillingService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class JobRoleService implements JobRoleServiceInterface
{
    private BillingService $billingService;
    private JobRoleRepositoryInterface $jobRoleRepository;

    public function __construct(
        BillingService $billingService,
        JobRoleRepositoryInterface $jobRoleRepository
    ) {
        $this->billingService = $billingService;
        $this->jobRoleRepository = $jobRoleRepository;
    }
    public function getJobRolesWithInheritanceAndPricing(int $entityId): Collection
    {
        $result = collect();

        try {
            $ownJobRoles = $this->getJobRolesForEntity($entityId, 'self', $entityId, 'Self');
            $result = $result->merge($ownJobRoles);

            $entity = Entity::with(['parentEntity'])->find($entityId);

            if (!$entity) {
                return $result;
            }

            if ($entity->parentEntity->isNotEmpty()) {
                $parentEntity = $entity->parentEntity->first();
                $parentEntityId = $parentEntity->id;
                $parentEntityName = $parentEntity->name;

                $parentJobRoles = $this->getJobRolesForEntity(
                    $parentEntityId,
                    'parent',
                    $parentEntityId,
                    $parentEntityName,
                    $entityId
                );
                $result = $result->merge($parentJobRoles);

                $parentEntityWithParent = Entity::with(['parentEntity'])->find($parentEntityId);
                if ($parentEntityWithParent && $parentEntityWithParent->parentEntity->isNotEmpty()) {
                    $superGroupEntity = $parentEntityWithParent->parentEntity->first();
                    $superGroupId = $superGroupEntity->id;
                    $superGroupName = $superGroupEntity->name;

                    $superGroupJobRoles = $this->getJobRolesForEntity(
                        $superGroupId,
                        'super_group',
                        $superGroupId,
                        $superGroupName,
                        $entityId
                    );
                    $result = $result->merge($superGroupJobRoles);
                }
            }

            return $result;

        } catch (\Exception $e) {
            Log::error('Error getting job roles with inheritance and pricing', [
                'entityId' => $entityId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return collect();
        }
    }

    private function getJobRolesForEntity(
        int $sourceEntityId,
        string $source,
        int $sourceId,
        string $sourceName,
        ?int $pricingEntityId = null
    ): Collection {
        $pricingEntityId = $pricingEntityId ?? $sourceEntityId;

        return $this->jobRoleRepository->getJobRolesWithProducts($sourceEntityId)
            ->map(function ($jobRole) use ($source, $sourceId, $sourceName, $pricingEntityId) {
                return [
                    'job_role' => $this->formatJobRoleWithPricing($jobRole, $pricingEntityId),
                    'source' => $source,
                    'source_id' => $sourceId,
                    'source_name' => $sourceName,
                    'is_inherited' => $source !== 'self'
                ];
            });
    }

    private function formatJobRoleWithPricing(JobRole $jobRole, int $entityId): array
    {
        $allProductPricing = $this->billingService->getProductPricingForEntity($entityId);

        $products = $jobRole->products->map(function ($product) use ($allProductPricing) {
            $pricingInfo = $allProductPricing->firstWhere('product_id', $product->id);

            return [
                'id' => $product->id,
                'name' => $product->name,
                'code' => $product->code,
                'description' => $product->description,
                'pricing' => [
                    'admin_fee' => $pricingInfo['admin_fee'] ?? 0,
                    'supplier_fee' => $pricingInfo['supplier_fee'] ?? 0,
                    'total_fee' => ($pricingInfo['admin_fee'] ?? 0) + ($pricingInfo['supplier_fee'] ?? 0),
                    'source' => $pricingInfo['pricing_source'] ?? 'Default',
                    'source_name' => $pricingInfo['source_entity_name'] ?? null,
                    'inheritance_type' => $pricingInfo['inheritance_type'] ?? 'default',
                    'is_enabled' => $pricingInfo['is_enabled'] ?? false,
                    'has_fees' => $pricingInfo['has_fees'] ?? false,
                    'custom_admin_fee' => $pricingInfo['custom_admin_fee'] ?? null
                ]
            ];
        });

        return [
            'id' => $jobRole->id,
            'entity_id' => $jobRole->entity_id,
            'job_label' => $jobRole->job_label,
            'job_title' => $jobRole->job_title,
            'job_workforce' => $jobRole->job_workforce,
            'role_description' => $jobRole->role_description,
            'self_payment' => $jobRole->self_payment,
            'employment_sector' => $jobRole->employment_sector,
            'products' => $products->toArray(),
            'created_at' => $jobRole->created_at,
            'updated_at' => $jobRole->updated_at
        ];
    }

    public function getEntityJobRolesWithPricing(int $entityId): Collection
    {
        try {
            $jobRoles = $this->jobRoleRepository->getJobRolesWithProducts($entityId)
                ->map(function ($jobRole) use ($entityId) {
                    return $this->formatJobRoleWithPricing($jobRole, $entityId);
                });

            return $jobRoles;

        } catch (\Exception $e) {
            Log::error('Error getting entity job roles with pricing', [
                'entityId' => $entityId,
                'error' => $e->getMessage()
            ]);

            return collect();
        }
    }
}
