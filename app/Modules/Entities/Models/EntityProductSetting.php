<?php

declare(strict_types=1);

namespace App\Modules\Entities\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Modules\Products\Models\Product;

class EntityProductSetting extends Model
{
    protected $table = 'entity_product_settings';

    protected $fillable = [
        'entity_id',
        'product_id',
        'custom_admin_fee',
        'inheritance_type'
    ];

    protected $casts = [
        'custom_admin_fee' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function entity(): BelongsTo
    {
        return $this->belongsTo(Entity::class, 'entity_id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }
}
