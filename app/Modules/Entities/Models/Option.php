<?php

declare(strict_types=1);

namespace App\Modules\Entities\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Option extends Model
{
    protected $table = 'options';
    
    protected $fillable = [
        'name',
        'description',
        'sort_order',
        'option_type',
        'section',
        'option_value_type',
        'option_config'
    ];
    
    protected $casts = [
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    
    public function entityOptions(): HasMany
    {
        return $this->hasMany(EntityOption::class, 'option_id');
    }
}
