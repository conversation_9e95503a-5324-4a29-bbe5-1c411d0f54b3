<?php

declare(strict_types=1);

namespace App\Modules\Entities\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EntityOption extends Model
{
    protected $table = 'entity_options';

    public $timestamps = false;

    protected $fillable = [
        'entity_id',
        'option_id',
        'value',
        'is_overridden'
    ];

    protected $casts = [
        'is_overridden' => 'boolean',
    ];
    
    public function entity(): BelongsTo
    {
        return $this->belongsTo(Entity::class, 'entity_id');
    }

    public function option(): BelongsTo
    {
        return $this->belongsTo(Option::class, 'option_id');
    }
}
