<?php

declare(strict_types=1);

namespace App\Modules\Entities\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Modules\Auth\Models\PortalUser;
use SolidFuse\Modules\Entities\Enums\EntityType;

class Entity extends Model
{
    use HasFactory;

    protected $table = 'entities';

    protected $fillable = [
        'name',
        'entity_code',
        'entity_type',
        'status'
    ];

    protected $casts = [
        'status' => 'boolean',
        'entity_type' => EntityType::class,
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(
            PortalUser::class,
            'entity_user_links',
            'entity_id',
            'user_id'
        )->withPivot('role')->withTimestamps();
    }

    public function profile(): HasOne
    {
        return $this->hasOne(EntityProfile::class, 'entity_id');
    }

    public function parentEntities(): BelongsToMany
    {
        return $this->belongsToMany(
            Entity::class,
            'entity_relationships',
            'child_entity_id',
            'parent_entity_id'
        );
    }

    public function childEntities(): BelongsToMany
    {
        return $this->belongsToMany(
            Entity::class,
            'entity_relationships',
            'parent_entity_id',
            'child_entity_id'
        );
    }

    public function options(): HasMany
    {
        return $this->hasMany(EntityOption::class, 'entity_id');
    }

    public function productSettings(): HasMany
    {
        return $this->hasMany(EntityProductSetting::class, 'entity_id');
    }

    /**
     * Get entity option value with inheritance using SolidFuse
     */
    public function getOptionValue(string $optionName): mixed
    {
        return \SolidFuse\Modules\Entities\Helpers\EntityHelper::getOption($optionName, $this->id);
    }

    protected static function newFactory()
    {
        return \Database\Factories\EntityFactory::new();
    }
}
