<?php

declare(strict_types=1);

namespace App\Modules\Entities\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use App\Modules\Products\Models\Product;

class JobRole extends Model
{
    protected $table = 'job_roles';
    
    protected $fillable = [
        'entity_id',
        'job_label',
        'job_title',
        'job_workforce',
        'role_description',
        'self_payment',
        'employment_sector'
    ];
    
    protected $casts = [
        'self_payment' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    
    public function entity(): BelongsTo
    {
        return $this->belongsTo(Entity::class, 'entity_id');
    }
    
    public function products(): BelongsToMany
    {
        return $this->belongsToMany(
            Product::class,
            'job_role_product',
            'job_role_id',
            'product_id'
        );
    }
}
