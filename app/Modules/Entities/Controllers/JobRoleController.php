<?php

declare(strict_types=1);

namespace App\Modules\Entities\Controllers;

use App\Core\BaseApiController;
use App\Modules\Entities\Services\JobRoleService;
use App\Modules\Entities\Models\Entity;
use Illuminate\Http\JsonResponse;

class JobRoleController extends BaseApiController
{
    protected JobRoleService $jobRoleService;

    public function __construct(JobRoleService $jobRoleService)
    {
        $this->jobRoleService = $jobRoleService;
    }

    public function getJobRoles(int $entityId): JsonResponse
    {
        try {
            $entity = Entity::find($entityId);

            if (!$entity) {
                return $this->sendNotFound('Entity not found');
            }

            $user = auth()->user();
            if (!$user->hasAccessToEntity($entityId)) {
                return $this->sendForbidden('Access denied to this entity');
            }

            $jobRoles = $this->jobRoleService->getJobRolesWithInheritanceAndPricing($entityId);

            $response = [
                'entity' => [
                    'id' => $entity->id,
                    'name' => $entity->name,
                    'entity_code' => $entity->entity_code,
                    'entity_type' => $entity->entity_type
                ],
                'job_roles' => $jobRoles->toArray(),
                'total_count' => $jobRoles->count(),
                'inheritance_info' => [
                    'self_count' => $jobRoles->where('source', 'self')->count(),
                    'parent_count' => $jobRoles->where('source', 'parent')->count(),
                    'super_group_count' => $jobRoles->where('source', 'super_group')->count()
                ]
            ];

            return $this->sendResponse($response, 'Job roles retrieved successfully');

        } catch (\Exception $e) {
            return $this->sendServerError('Failed to retrieve job roles: ' . $e->getMessage());
        }
    }

    public function getEntityJobRoles(int $entityId): JsonResponse
    {
        try {
            $entity = Entity::find($entityId);

            if (!$entity) {
                return $this->sendNotFound('Entity not found');
            }

            $user = auth()->user();
            if (!$user->hasAccessToEntity($entityId)) {
                return $this->sendForbidden('Access denied to this entity');
            }

            $jobRoles = $this->jobRoleService->getEntityJobRolesWithPricing($entityId);

            $response = [
                'entity' => [
                    'id' => $entity->id,
                    'name' => $entity->name,
                    'entity_code' => $entity->entity_code,
                    'entity_type' => $entity->entity_type
                ],
                'job_roles' => $jobRoles->toArray(),
                'total_count' => $jobRoles->count()
            ];

            return $this->sendResponse($response, 'Entity job roles retrieved successfully');

        } catch (\Exception $e) {
            return $this->sendServerError('Failed to retrieve entity job roles: ' . $e->getMessage());
        }
    }

    public function getJobRole(int $jobRoleId): JsonResponse
    {
        try {
            $jobRole = \App\Modules\Entities\Models\JobRole::with(['products.fees', 'entity'])
                ->find($jobRoleId);

            if (!$jobRole) {
                return $this->sendNotFound('Job role not found');
            }

            $user = auth()->user();
            if (!$user->hasAccessToEntity($jobRole->entity_id)) {
                return $this->sendForbidden('Access denied to this job role');
            }

            $formattedJobRole = $this->jobRoleService->getEntityJobRolesWithPricing($jobRole->entity_id)
                ->firstWhere('id', $jobRoleId);

            if (!$formattedJobRole) {
                return $this->sendNotFound('Job role not found');
            }

            return $this->sendResponse($formattedJobRole, 'Job role retrieved successfully');

        } catch (\Exception $e) {
            return $this->sendServerError('Failed to retrieve job role: ' . $e->getMessage());
        }
    }
}
