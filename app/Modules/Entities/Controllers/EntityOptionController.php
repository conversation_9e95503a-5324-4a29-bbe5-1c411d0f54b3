<?php

declare(strict_types=1);

namespace App\Modules\Entities\Controllers;

use App\Core\BaseApiController;
use App\Modules\Entities\Services\EntityOptionService;
use App\Modules\Entities\Models\Entity;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use <PERSON>nu<PERSON>s\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\Endpoint;
use Knuckles\Scribe\Attributes\QueryParam;
use Knuckles\Scribe\Attributes\UrlParam;
use Knuckles\Scribe\Attributes\BodyParam;
use Knuckles\Scribe\Attributes\Response;
use Knuckles\Scribe\Attributes\ResponseFromApiResource;

#[Group("Entity Options", "Manage entity-specific configuration options with hierarchy inheritance")]
class EntityOptionController extends BaseApiController
{
    protected EntityOptionService $entityOptionService;

    public function __construct(EntityOptionService $entityOptionService)
    {
        $this->entityOptionService = $entityOptionService;
    }

    #[Endpoint(
        title: "Get Single Option Value",
        description: "Retrieves a single option value for a specific entity. The system always respects the entity hierarchy when retrieving values.",
        authenticated: true
    )]
    #[UrlParam("entityId", "integer", "The entity ID", example: 123)]
    #[UrlParam("optionName", "string", "The option name", example: "allow_applicant_registration")]
    #[QueryParam("include_source", "boolean", "Include source information in response", required: false, example: false)]
    #[Response([
        "success" => true,
        "data" => true,
        "message" => "Entity option retrieved successfully"
    ], 200, "Simple value response")]
    #[Response([
        "success" => true,
        "data" => [
            "entity" => [
                "id" => 123,
                "name" => "Test Client",
                "entity_code" => "TC001",
                "entity_type" => "client"
            ],
            "option_name" => "allow_applicant_registration",
            "option_value" => true,
            "source" => "entity",
            "source_entity" => [
                "id" => 122,
                "name" => "Test Parent Group"
            ],
            "hierarchy_path" => [
                [
                    "id" => 123,
                    "name" => "Test Client",
                    "type" => "client"
                ],
                [
                    "id" => 122,
                    "name" => "Test Parent Group",
                    "type" => "parent_group"
                ]
            ]
        ],
        "message" => "Entity option retrieved successfully"
    ], 200, "Response with source information (when include_source=true)")]
    public function getOption(int $entityId, string $optionName, Request $request): JsonResponse
    {
        try {
            $entity = Entity::find($entityId);
            if (!$entity) {
                return $this->sendNotFound('Entity not found');
            }

            $user = auth()->user();
            if (!$this->hasAccessToEntity($user->id, $entityId)) {
                return $this->sendForbidden('Access denied to this entity');
            }

            $includeSource = $request->boolean('include_source', false);

            if ($includeSource) {
                $result = $this->entityOptionService->getOptionWithSource($optionName, $entityId);

                $response = [
                    'entity' => [
                        'id' => $entity->id,
                        'name' => $entity->name,
                        'entity_code' => $entity->entity_code,
                        'entity_type' => $entity->entity_type->value
                    ],
                    'option_name' => $optionName,
                    'option_value' => $result['value'],
                    'source' => $result['source'],
                    'source_entity' => [
                        'id' => $result['source_id'],
                        'name' => $result['source_name']
                    ],
                    'hierarchy_path' => $result['hierarchy_path']
                ];
            } else {
                $optionValue = $this->entityOptionService->getOptionValue($optionName, $entityId);

                $response = $optionValue;
            }

            return $this->sendResponse($response, 'Entity option retrieved successfully');

        } catch (\Exception $e) {
            return $this->sendServerError('Failed to retrieve entity option: ' . $e->getMessage());
        }
    }

    #[Endpoint(
        title: "Get Multiple Option Values",
        description: "Retrieves multiple option values for a specific entity in a single request. The system always respects the entity hierarchy when retrieving values.",
        authenticated: true
    )]
    #[UrlParam("entityId", "integer", "The entity ID", example: 123)]
    #[QueryParam("include_source", "boolean", "Include source information in response", required: false, example: false)]
    #[BodyParam("", "array", "Array of option names to retrieve", example: ["allow_applicant_registration", "max_applications_per_day"])]
    #[Response([
        "success" => true,
        "data" => [
            "entity" => [
                "id" => 123,
                "name" => "Test Client",
                "entity_code" => "TC001",
                "entity_type" => "client"
            ],
            "options" => [
                "allow_applicant_registration" => true,
                "max_applications_per_day" => 100
            ]
        ],
        "message" => "Entity options retrieved successfully"
    ], 200)]
    public function getMultipleOptions(Request $request, int $entityId): JsonResponse
    {
        try {
            $optionNames = $request->json()->all();

            $validator = Validator::make(['option_names' => $optionNames], [
                'option_names' => 'required|array|min:1',
                'option_names.*' => 'required|string|max:255'
            ]);

            if ($validator->fails()) {
                return $this->sendValidationError($validator->errors()->toArray());
            }

            $entity = Entity::find($entityId);
            if (!$entity) {
                return $this->sendNotFound('Entity not found');
            }

            $user = auth()->user();
            if (!$this->hasAccessToEntity($user->id, $entityId)) {
                return $this->sendForbidden('Access denied to this entity');
            }

            $includeSource = $request->boolean('include_source', false);
            $options = [];

            foreach ($optionNames as $optionName) {
                if ($includeSource) {
                    $result = $this->entityOptionService->getOptionWithSource($optionName, $entityId);
                    $options[$optionName] = [
                        'value' => $result['value'],
                        'source' => $result['source'],
                        'source_entity' => [
                            'id' => $result['source_id'],
                            'name' => $result['source_name']
                        ],
                        'hierarchy_path' => $result['hierarchy_path']
                    ];
                } else {
                    $options[$optionName] = $this->entityOptionService->getOptionValue($optionName, $entityId);
                }
            }

            $response = [
                'entity' => [
                    'id' => $entity->id,
                    'name' => $entity->name,
                    'entity_code' => $entity->entity_code,
                    'entity_type' => $entity->entity_type->value
                ],
                'options' => $options
            ];

            return $this->sendResponse($response, 'Entity options retrieved successfully');

        } catch (\Exception $e) {
            return $this->sendServerError('Failed to retrieve entity options: ' . $e->getMessage());
        }
    }

    #[Endpoint(
        title: "Get User Accessible Entities",
        description: "Retrieves all entities that the authenticated user has access to, including child entities through hierarchy.",
        authenticated: true
    )]
    #[Response([
        "success" => true,
        "data" => [
            "entities" => [
                [
                    "id" => 123,
                    "name" => "Test Client",
                    "entity_code" => "TC001",
                    "entity_type" => "client",
                    "contact_email" => "<EMAIL>",
                    "phone" => "+1234567890"
                ]
            ],
            "total_count" => 1
        ],
        "message" => "Accessible entities retrieved successfully"
    ], 200)]
    public function getUserAccessibleEntities(): JsonResponse
    {
        try {
            $user = auth()->user();
            $accessibleEntityIds = $this->entityOptionService->getUserAccessibleEntityIds($user->id);

            $entities = Entity::whereIn('id', $accessibleEntityIds)
                ->where('status', true)
                ->with('profile')
                ->orderBy('entity_type')
                ->orderBy('name')
                ->get()
                ->map(function ($entity) {
                    return [
                        'id' => $entity->id,
                        'name' => $entity->name,
                        'entity_code' => $entity->entity_code,
                        'entity_type' => $entity->entity_type->value,
                        'contact_email' => $entity->profile->contact_email ?? null,
                        'phone' => $entity->profile->phone ?? null
                    ];
                });

            return $this->sendResponse([
                'entities' => $entities,
                'total_count' => $entities->count()
            ], 'Accessible entities retrieved successfully');

        } catch (\Exception $e) {
            return $this->sendServerError('Failed to retrieve accessible entities: ' . $e->getMessage());
        }
    }

    private function hasAccessToEntity(int $userId, int $entityId): bool
    {
        $accessibleEntityIds = $this->entityOptionService->getUserAccessibleEntityIds($userId);
        return in_array($entityId, $accessibleEntityIds);
    }
}
