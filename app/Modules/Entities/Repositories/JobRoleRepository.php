<?php

declare(strict_types=1);

namespace App\Modules\Entities\Repositories;

use App\Modules\Entities\Contracts\JobRoleRepositoryInterface;
use App\Modules\Entities\Models\JobRole;
use Illuminate\Support\Collection;

class JobRoleRepository implements JobRoleRepositoryInterface
{
    public function getJobRolesWithProducts(int $entityId): Collection
    {
        return JobRole::with(['products.fees'])
            ->where('entity_id', $entityId)
            ->get();
    }

    public function getJobRoleWithProducts(int $jobRoleId): ?JobRole
    {
        return JobRole::with(['products.fees'])
            ->find($jobRoleId);
    }
}
