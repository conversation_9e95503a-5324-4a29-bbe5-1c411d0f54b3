<?php

declare(strict_types=1);

namespace App\Modules\Entities\Contracts;

use App\Modules\Entities\Models\JobRole;
use Illuminate\Support\Collection;

interface JobRoleRepositoryInterface
{
    /**
     * Get job roles for an entity with products
     */
    public function getJobRolesWithProducts(int $entityId): Collection;

    /**
     * Get job role by ID with products
     */
    public function getJobRoleWithProducts(int $jobRoleId): ?JobRole;
}
