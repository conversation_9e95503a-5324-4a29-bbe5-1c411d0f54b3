<?php

declare(strict_types=1);

namespace App\Modules\Entities\Contracts;

use Illuminate\Support\Collection;

interface JobRoleServiceInterface
{
    /**
     * Get all job roles for an entity, including inherited ones from parent and super groups
     * with pricing information for each product
     */
    public function getJobRolesWithInheritanceAndPricing(int $entityId): Collection;

    /**
     * Get job roles for a specific entity only (no inheritance)
     */
    public function getEntityJobRolesWithPricing(int $entityId): Collection;
}
