<?php

declare(strict_types=1);

namespace App\Modules\Entities\Providers;

use Illuminate\Support\ServiceProvider;
use App\Modules\Entities\Contracts\JobRoleServiceInterface;
use App\Modules\Entities\Contracts\JobRoleRepositoryInterface;
use App\Modules\Entities\Services\JobRoleService;
use App\Modules\Entities\Repositories\JobRoleRepository;

class EntitiesServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Bind interfaces to implementations within this module
        $this->app->bind(JobRoleServiceInterface::class, JobRoleService::class);
        $this->app->bind(JobRoleRepositoryInterface::class, JobRoleRepository::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Module-specific bootstrapping can be added here
    }
}
