<?php

declare(strict_types=1);

namespace App\Modules\Entities\SubModules\JobRoles\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateJobRoleRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'job_label' => 'sometimes|required|string|max:255',
            'job_title' => 'sometimes|required|string|max:255',
            'job_workforce' => 'sometimes|required|string|max:255',
            'role_description' => 'nullable|string',
            'self_payment' => 'nullable|boolean',
            'employment_sector' => 'nullable|string|max:255',
            'product_ids' => 'nullable|array',
            'product_ids.*' => 'integer|exists:products,id',
        ];
    }

    public function messages(): array
    {
        return [
            'job_label.required' => 'Job label is required',
            'job_label.max' => 'Job label cannot exceed 255 characters',
            'job_title.required' => 'Job title is required',
            'job_title.max' => 'Job title cannot exceed 255 characters',
            'job_workforce.required' => 'Job workforce is required',
            'job_workforce.max' => 'Job workforce cannot exceed 255 characters',
            'role_description.string' => 'Role description must be a string',
            'self_payment.boolean' => 'Self payment must be true or false',
            'employment_sector.max' => 'Employment sector cannot exceed 255 characters',
            'product_ids.array' => 'Product IDs must be an array',
            'product_ids.*.integer' => 'Each product ID must be an integer',
            'product_ids.*.exists' => 'One or more product IDs do not exist',
        ];
    }

    public function attributes(): array
    {
        return [
            'job_label' => 'job label',
            'job_title' => 'job title',
            'job_workforce' => 'job workforce',
            'role_description' => 'role description',
            'self_payment' => 'self payment',
            'employment_sector' => 'employment sector',
            'product_ids' => 'product IDs',
        ];
    }
}
