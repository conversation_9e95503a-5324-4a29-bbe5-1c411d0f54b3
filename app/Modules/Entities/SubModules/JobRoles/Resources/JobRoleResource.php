<?php

declare(strict_types=1);

namespace App\Modules\Entities\SubModules\JobRoles\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class JobRoleResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'job_label' => $this->job_label,
            'job_title' => $this->job_title,
            'job_workforce' => $this->job_workforce,
            'role_description' => $this->role_description,
            'self_payment' => $this->self_payment,
            'employment_sector' => $this->employment_sector,
            'products' => $this->whenLoaded('products', function () {
                return $this->products->map(function ($product) {
                    return [
                        'id' => $product->id,
                        'name' => $product->name,
                        'product_code' => $product->product_code,
                    ];
                });
            }),
        ];
    }
}
