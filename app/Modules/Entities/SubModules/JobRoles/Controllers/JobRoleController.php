<?php

declare(strict_types=1);

namespace App\Modules\Entities\SubModules\JobRoles\Controllers;

use App\Core\BaseApiController;
use App\Modules\Entities\SubModules\JobRoles\Models\JobRole;
use App\Modules\Entities\SubModules\JobRoles\Requests\CreateJobRoleRequest;
use App\Modules\Entities\Models\Entity;
use SolidFuse\Modules\Billing\Services\BillingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class JobRoleController extends BaseApiController
{
    /**
     * Get job roles
     *
     * Retrieve all job roles with complete data for both table display and applications
     *
     * @authenticated
     */
    public function getJobRoles(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $jobRoles = $this->getJobRolesForUser($user);

            return $this->sendResponse($jobRoles, 'Job roles retrieved successfully');
        } catch (\Exception $e) {
            return $this->sendServerError('Failed to retrieve job roles: ' . $e->getMessage());
        }
    }

    /**
     * Create job role
     *
     * Create a new job role for any client where this user is associated with
     *
     * @authenticated
     */
    public function store(CreateJobRoleRequest $request): JsonResponse
    {
        try {
            $user = $request->user();

            if (!$this->isClientUser($user)) {
                return $this->sendForbidden('Only client users can create job roles');
            }

            $validatedData = $request->validated();

            if (!$this->canUserCreateJobRoleForEntity($user, $validatedData['entity_id'])) {
                return $this->sendForbidden('You do not have access to create job roles for this entity');
            }

            $entity = Entity::find($validatedData['entity_id']);
            if (!$entity || !$entity->status) {
                return $this->sendNotFound('Entity not found or inactive');
            }

            $jobRole = $this->createJobRole($validatedData);

            if (isset($validatedData['product_ids']) && !empty($validatedData['product_ids'])) {
                $jobRole->products()->sync($validatedData['product_ids']);
            }

            $jobRole->load(['entity.profile', 'products']);

            return $this->sendResponse(
                $this->transformJobRoleComplete($jobRole),
                'Job role created successfully'
            );
        } catch (\Exception $e) {
            return $this->sendServerError('Failed to create job role: ' . $e->getMessage());
        }
    }

    /**
     * Update job role
     *
     * Update a job role (client users only)
     *
     * @authenticated
     */
    public function update(Request $request, JobRole $jobRole): JsonResponse
    {
        if (!$this->isClientUser($request->user())) {
            return $this->sendForbidden('Only client users can update job roles');
        }

        if (!$this->canUserAccessJobRole($request->user(), $jobRole)) {
            return $this->sendForbidden('You do not have access to this job role');
        }

        try {
            $validatedData = $request->validate([
                'job_label' => 'sometimes|required|string|max:255',
                'job_title' => 'sometimes|required|string|max:255',
                'job_workforce' => 'sometimes|required|string|max:255',
                'role_description' => 'nullable|string',
                'self_payment' => 'nullable|boolean',
                'employment_sector' => 'nullable|string|max:255',
                'product_ids' => 'nullable|array',
                'product_ids.*' => 'integer|exists:products,id',
            ]);

            $this->updateJobRole($jobRole, $validatedData);

            if (isset($validatedData['product_ids'])) {
                $jobRole->products()->sync($validatedData['product_ids']);
            }

            $jobRole->load(['entity.profile', 'products']);

            return $this->sendResponse(
                $this->transformJobRoleComplete($jobRole),
                'Job role updated successfully'
            );
        } catch (\Exception $e) {
            return $this->sendServerError('Failed to update job role: ' . $e->getMessage());
        }
    }

    /**
     * Delete job role
     *
     * Delete a job role (client users only)
     *
     * @authenticated
     */
    public function destroy(Request $request, JobRole $jobRole): JsonResponse
    {
        if (!$this->isClientUser($request->user())) {
            return $this->sendForbidden('Only client users can delete job roles');
        }

        if (!$this->canUserAccessJobRole($request->user(), $jobRole)) {
            return $this->sendForbidden('You do not have access to this job role');
        }

        try {
            $jobRole->products()->detach();
            $jobRole->delete();

            return $this->sendResponse([], 'Job role deleted successfully');
        } catch (\Exception $e) {
            return $this->sendServerError('Failed to delete job role: ' . $e->getMessage());
        }
    }

    private function getJobRolesForUser($user): array
    {
        // Get all entities the user is associated with (active entities only)
        $userEntityIds = $user->entities()->where('entities.status', true)->pluck('entities.id')->toArray();

        if (empty($userEntityIds)) {
            return [];
        }

        // Get all accessible entity IDs including hierarchy
        $accessibleEntityIds = $this->getAllAccessibleEntityIds($userEntityIds);

        // Get all job roles from entities the user has access to (including hierarchy)
        $jobRoles = JobRole::whereIn('entity_id', $accessibleEntityIds)
            ->with(['entity.profile', 'products.fees', 'products.entitySettings'])
            ->get();

        return $jobRoles->map(function ($jobRole) {
            return $this->transformJobRoleComplete($jobRole);
        })->toArray();
    }

    private function transformJobRoleComplete(JobRole $jobRole): array
    {
        return [
            // Table display data
            'id' => $jobRole->id,
            'job_label' => $jobRole->job_label,
            'job_title' => $jobRole->job_title,
            'job_workforce' => $jobRole->job_workforce,
            'role_description' => $jobRole->role_description,
            'self_payment' => $jobRole->self_payment,
            'employment_sector' => $jobRole->employment_sector,
            'entity_name' => $jobRole->entity?->name ?? null,
            'entity_code' => $jobRole->entity?->entity_code ?? null,

            // Application data
            'job_role_name' => $jobRole->job_label . ' - ' . $jobRole->job_title,
            'organisation_name' => $jobRole->entity?->name ?? null,
            'entity_id' => $jobRole->entity_id,

            // Products with detailed pricing
            'products' => $this->transformProductsWithPricing($jobRole->products ?? collect(), $jobRole->entity),
        ];
    }

    private function getAllAccessibleEntityIds(array $userEntityIds): array
    {
        $accessibleEntityIds = collect($userEntityIds);

        // Add parent entities (entities that the user's entities are children of)
        foreach ($userEntityIds as $entityId) {
            $entity = \App\Modules\Entities\Models\Entity::find($entityId);
            if ($entity) {
                $parentIds = $entity->parentEntities()->where('entities.status', true)->pluck('entities.id');
                $accessibleEntityIds = $accessibleEntityIds->merge($parentIds);
            }
        }

        // Add child entities (entities that are children of the user's entities)
        foreach ($userEntityIds as $entityId) {
            $entity = \App\Modules\Entities\Models\Entity::find($entityId);
            if ($entity) {
                $childIds = $entity->childEntities()->where('entities.status', true)->pluck('entities.id');
                $accessibleEntityIds = $accessibleEntityIds->merge($childIds);
            }
        }

        return $accessibleEntityIds->unique()->toArray();
    }

    private function isClientUser($user): bool
    {
        return $user->user_type === 'client_user';
    }

    private function canUserAccessJobRole($user, JobRole $jobRole): bool
    {
        // Check if user has access to the job role's entity or any related entities
        $userEntityIds = $user->entities()->where('entities.status', true)->pluck('entities.id')->toArray();
        $accessibleEntityIds = $this->getAllAccessibleEntityIds($userEntityIds);

        return in_array($jobRole->entity_id, $accessibleEntityIds);
    }

    private function transformProductsWithPricing($products, $entity): array
    {
        if (!$products || !is_iterable($products)) {
            return [];
        }

        return $products->map(function ($product) use ($entity) {
            $pricingDetails = $this->getProductPricingDetails($product, $entity);

            return [
                'id' => $product->id,
                'name' => $product->name,
                'product_code' => $product->code ?? null,
                'price' => $pricingDetails['price'],
                'admin_fee' => $pricingDetails['admin_fee'],
                'supplier_fee' => $pricingDetails['supplier_fee'],
                'pricing_source' => $pricingDetails['pricing_source'],
                'inheritance_type' => $pricingDetails['inheritance_type'],
                'currency' => 'GBP',
            ];
        })->toArray();
    }

    private function getProductPricingDetails($product, $entity): array
    {
        try {
            // Use SolidFuse BillingService to get full pricing details with hierarchy inheritance
            $billingService = app(BillingService::class);
            $products = $billingService->getProductPricingForEntity($entity->id);
            $productDetails = $products->firstWhere('product_id', $product->id);

            if ($productDetails) {
                return [
                    'price' => ($productDetails['admin_fee'] ?? 0) + ($productDetails['supplier_fee'] ?? 0),
                    'admin_fee' => $productDetails['admin_fee'] ?? 0,
                    'supplier_fee' => $productDetails['supplier_fee'] ?? 0,
                    'pricing_source' => $productDetails['pricing_source'] ?? 'Default',
                    'inheritance_type' => $productDetails['inheritance_type'] ?? 'default',
                ];
            }

            // Fallback if product not found
            return [
                'price' => 50.0,
                'admin_fee' => 40.0,
                'supplier_fee' => 10.0,
                'pricing_source' => 'Default',
                'inheritance_type' => 'default',
            ];
        } catch (\Exception) {
            // If there's any error, return default pricing
            return [
                'price' => 50.0,
                'admin_fee' => 40.0,
                'supplier_fee' => 10.0,
                'pricing_source' => 'Default',
                'inheritance_type' => 'default',
            ];
        }
    }

    private function updateJobRole(JobRole $jobRole, array $data): void
    {
        $updateData = [];

        if (isset($data['job_label'])) {
            $updateData['job_label'] = $data['job_label'];
        }
        if (isset($data['job_title'])) {
            $updateData['job_title'] = $data['job_title'];
        }
        if (isset($data['job_workforce'])) {
            $updateData['job_workforce'] = $data['job_workforce'];
        }
        if (isset($data['role_description'])) {
            $updateData['role_description'] = $data['role_description'];
        }
        if (isset($data['self_payment'])) {
            $updateData['self_payment'] = $data['self_payment'];
        }
        if (isset($data['employment_sector'])) {
            $updateData['employment_sector'] = $data['employment_sector'];
        }

        if (!empty($updateData)) {
            $jobRole->update($updateData);
        }
    }

    private function canUserCreateJobRoleForEntity($user, int $entityId): bool
    {
        $userEntityIds = $user->entities()->where('entities.status', true)->pluck('entities.id')->toArray();
        $accessibleEntityIds = $this->getAllAccessibleEntityIds($userEntityIds);

        return in_array($entityId, $accessibleEntityIds);
    }

    private function createJobRole(array $data): JobRole
    {
        return JobRole::create([
            'entity_id' => $data['entity_id'],
            'job_label' => $data['job_label'],
            'job_title' => $data['job_title'],
            'job_workforce' => $data['job_workforce'],
            'role_description' => $data['role_description'] ?? null,
            'self_payment' => $data['self_payment'] ?? false,
            'employment_sector' => $data['employment_sector'] ?? null,
        ]);
    }
}
