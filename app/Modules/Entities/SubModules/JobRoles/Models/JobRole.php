<?php

declare(strict_types=1);

namespace App\Modules\Entities\SubModules\JobRoles\Models;

use App\Modules\Entities\Models\Entity;
use App\Modules\Products\Models\Product;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class JobRole extends Model
{
    protected $table = 'job_roles';

    protected $fillable = [
        'entity_id',
        'job_label',
        'job_title',
        'job_workforce',
        'role_description',
        'self_payment',
        'employment_sector'
    ];

    protected $casts = [
        'self_payment' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function entity(): BelongsTo
    {
        return $this->belongsTo(Entity::class, 'entity_id');
    }

    public function products(): BelongsToMany
    {
        return $this->belongsToMany(
            Product::class,
            'job_role_product',
            'job_role_id',
            'product_id'
        );
    }

    public function scopeForEntity($query, int $entityId)
    {
        return $query->where('entity_id', $entityId);
    }

    public function hasProduct(int $productId): bool
    {
        return $this->products()->where('product_id', $productId)->exists();
    }

    public function getFormattedTitleAttribute(): string
    {
        return $this->job_label . ' - ' . $this->job_title;
    }
}
