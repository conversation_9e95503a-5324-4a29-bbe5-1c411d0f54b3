<?php

declare(strict_types=1);

namespace App\Modules\Products\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductFormField extends Model
{
    protected $table = 'product_form_fields';

    protected $fillable = [
        'product_id',
        'field_key',
        'label',
        'type',
        'field_values',
        'required',
        'sort_order',
        'validation_rules'
    ];

    protected $casts = [
        'field_values' => 'array',
        'validation_rules' => 'array',
        'required' => 'boolean',
        'sort_order' => 'integer',
    ];

    // Add accessors for backward compatibility with the API response
    public function getFieldNameAttribute(): ?string
    {
        return $this->field_key;
    }

    public function getFieldLabelAttribute(): ?string
    {
        return $this->label;
    }

    public function getFieldTypeAttribute(): ?string
    {
        return $this->type;
    }

    public function getFieldOptionsAttribute(): ?array
    {
        return $this->field_values;
    }

    public function getIsRequiredAttribute(): bool
    {
        return $this->required;
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }
}
