<?php

declare(strict_types=1);

namespace App\Modules\Products\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use App\Modules\Entities\Models\EntityProductSetting;

class Product extends Model
{
    use HasFactory;

    protected $table = 'products';

    protected $fillable = [
        'name',
        'code',
        'variant'
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function entitySettings(): HasMany
    {
        return $this->hasMany(EntityProductSetting::class, 'product_id');
    }

    // Commented out - form fields are now handled in Flutter application
    // public function formFields(): HasMany
    // {
    //     return $this->hasMany(ProductFormField::class, 'product_id');
    // }

    public function fees(): HasMany
    {
        return $this->hasMany(ProductFee::class, 'product_id');
    }

    public function jobRoles(): BelongsToMany
    {
        return $this->belongsToMany(
            \App\Modules\Entities\Models\JobRole::class,
            'job_role_product',
            'product_id',
            'job_role_id'
        );
    }

    public function getDefaultAdminFee(): ?float
    {
        $defaultFee = $this->fees()->where('fee_type', 'admin')->first();
        return $defaultFee ? $defaultFee->amount : null;
    }

    public function getCurrentFee()
    {
        return $this->fees()
            ->where('valid_from', '<=', now()->toDateString())
            ->orderBy('valid_from', 'desc')
            ->first();
    }

    public function getLatestFee()
    {
        return $this->fees()->orderBy('valid_from', 'desc')->first();
    }

    protected static function newFactory()
    {
        return \Database\Factories\ProductFactory::new();
    }
}
