<?php

declare(strict_types=1);

namespace App\Modules\Auth\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

/**
 * @bodyParam current_password string required The user's current password. Example: currentPassword123!
 * @bodyParam new_password string required The new password (min 8 chars, mixed case, numbers, symbols). Example: newPassword456@
 * @bodyParam new_password_confirmation string required Confirmation of the new password. Example: newPassword456@
 * @bodyParam invalidate_all_tokens boolean optional Whether to invalidate all existing tokens (default: false). Example: true
 */
class ChangePasswordRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'current_password' => 'required|string|min:1',
            'new_password' => [
                'required',
                'string',
                'confirmed',
                'different:current_password',
                Password::min(8)
                    ->letters()
                    ->mixedCase()
                    ->numbers()
                    ->symbols()
                    ->uncompromised()
            ],
            'new_password_confirmation' => 'required|string',
            'invalidate_all_tokens' => 'sometimes|boolean'
        ];
    }

    public function messages(): array
    {
        return [
            'current_password.required' => 'Current password is required',
            'current_password.min' => 'Current password cannot be empty',
            'new_password.required' => 'New password is required',
            'new_password.confirmed' => 'New password confirmation does not match',
            'new_password.different' => 'New password must be different from current password',
            'new_password_confirmation.required' => 'New password confirmation is required',
        ];
    }

    public function attributes(): array
    {
        return [
            'current_password' => 'current password',
            'new_password' => 'new password',
            'new_password_confirmation' => 'new password confirmation',
        ];
    }
}
