# Auth Module - MVVM Architecture

This module implements secure authentication and password management functionality following MVVM architecture patterns.

## 🏗️ Architecture

### MVVM Structure

```
app/Modules/Auth/
├── Contracts/                  # Interfaces for dependency injection
│   ├── PasswordServiceInterface.php
│   └── PasswordRepositoryInterface.php
├── Controllers/                # API Controllers (View layer)
│   └── AuthController.php
├── Models/                     # Data Models
│   └── PortalUser.php
├── Repositories/               # Data Access Layer
│   └── PasswordRepository.php
├── Services/                   # Business Logic Layer
│   ├── PasswordService.php
│   └── TwoFactorService.php
├── ViewModels/                 # Presentation Logic
│   └── PasswordChangeViewModel.php
├── Requests/                   # Validation Layer
│   ├── ChangePasswordRequest.php
│   └── LoginRequest.php
├── Providers/                  # Dependency Injection
│   └── AuthServiceProvider.php
└── README.md
```

## 🔐 Password Change Feature

### Security Measures

1. **Authentication Required**: Uses `auth:sanctum` middleware
2. **Rate Limiting**: Custom `throttle.password` middleware (5 attempts per 15 minutes)
3. **Current Password Verification**: Validates current password before change
4. **Strong Password Requirements**: 
   - Minimum 8 characters
   - Mixed case letters
   - Numbers and symbols
   - Not compromised in data breaches
5. **Password Confirmation**: Requires confirmation field
6. **Different Password**: New password must differ from current
7. **Token Management**: Optional invalidation of all user tokens
8. **Audit Logging**: Comprehensive logging of all attempts

### API Endpoint

```
POST /api/v1/auth/change-password
```

**Headers:**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
    "current_password": "currentPassword123!",
    "new_password": "newPassword456@",
    "new_password_confirmation": "newPassword456@",
    "invalidate_all_tokens": false
}
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Password changed successfully",
    "data": {
        "user_id": 1,
        "email": "<EMAIL>",
        "tokens_invalidated": 0,
        "changed_at": "2024-01-15T10:30:00.000Z",
        "security_notice": [
            "Your password has been successfully changed.",
            "If you did not make this change, please contact support immediately."
        ]
    }
}
```

**Error Response (422):**
```json
{
    "success": false,
    "message": "Current password is incorrect",
    "error_code": "INVALID_CURRENT_PASSWORD",
    "data": {
        "user_id": 1,
        "attempted_at": "2024-01-15T10:30:00.000Z",
        "security_recommendations": [
            "Ensure you are entering your current password correctly.",
            "If you have forgotten your password, use the password reset feature.",
            "Contact support if you continue to experience issues."
        ]
    }
}
```

**Rate Limit Response (429):**
```json
{
    "success": false,
    "message": "Too many password change attempts. Please try again later.",
    "error_code": "RATE_LIMIT_EXCEEDED",
    "retry_after": 900,
    "data": {
        "max_attempts": 5,
        "window_minutes": 15,
        "retry_after_seconds": 900
    }
}
```

## 🔧 Components

### PasswordService
- **Purpose**: Business logic for password operations
- **Features**: Password validation, change processing, token management
- **Security**: Current password verification, audit logging

### PasswordRepository
- **Purpose**: Data access for password operations
- **Features**: Password updates, user retrieval, activity logging
- **Database**: Interacts with `portal_users` table

### PasswordChangeViewModel
- **Purpose**: Formats responses for API consumers
- **Features**: Success/error formatting, security notices, recommendations
- **Flexibility**: Different response formats for different scenarios

### ThrottlePasswordChange Middleware
- **Purpose**: Rate limiting for password change attempts
- **Features**: Per-user + IP throttling, automatic cleanup on success
- **Configuration**: 5 attempts per 15 minutes (configurable)

## 🚀 Usage

The service is automatically registered via `AuthServiceProvider` and can be injected:

```php
// In controllers or other services
public function __construct(PasswordServiceInterface $passwordService)
{
    $this->passwordService = $passwordService;
}
```

## 🧪 Testing

Mock the interfaces for testing:

```php
// In tests
$mockRepository = Mockery::mock(PasswordRepositoryInterface::class);
$service = new PasswordService($mockRepository);
```

## 📝 Logging

All password change attempts are logged with:
- User ID and email
- Success/failure status
- Reason for failure
- IP address and user agent
- Timestamp

## 🔒 Security Best Practices

1. **Never log actual passwords**
2. **Use secure password hashing** (Laravel's default)
3. **Implement proper rate limiting**
4. **Validate all inputs thoroughly**
5. **Provide clear error messages** without revealing sensitive info
6. **Log security events** for monitoring
7. **Consider token invalidation** for enhanced security
