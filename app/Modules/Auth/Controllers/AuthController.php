<?php

declare(strict_types=1);

namespace App\Modules\Auth\Controllers;

use App\Core\BaseApiController;
use App\Modules\Auth\Models\PortalUser;
use App\Modules\Auth\Requests\LoginRequest;
use App\Modules\Auth\Requests\ChangePasswordRequest;
use App\Modules\Auth\Services\TwoFactorService;
use App\Modules\Auth\Contracts\PasswordServiceInterface;
use App\Modules\Auth\ViewModels\PasswordChangeViewModel;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class AuthController extends BaseApiController
{
    private TwoFactorService $twoFactorService;
    private PasswordServiceInterface $passwordService;

    public function __construct(
        TwoFactorService $twoFactorService,
        PasswordServiceInterface $passwordService
    ) {
        $this->twoFactorService = $twoFactorService;
        $this->passwordService = $passwordService;
    }

    public function login(LoginRequest $request): JsonResponse
    {
        $user = PortalUser::where('email', $request->email)->first();

        if (!$this->isValidCredentials($user, $request->password)) {
            CentLog("Auth", $request->email, [
                "status" => "failed",
                "reason" => "invalid_credentials",
                "login_method" => "password",
                "user_type" => $user ? $user->user_type : "unknown"
            ], "warning");
            return $this->sendUnauthorized('Invalid credentials');
        }

        if (!$this->isUserActive($user)) {
            CentLog("Auth", $user->id, [
                "status" => "failed",
                "reason" => "account_inactive",
                "login_method" => "password",
                "user_type" => $user->user_type
            ], "warning");
            return $this->sendForbidden('User account is not active');
        }

        if ($this->twoFactorService->isTwoFactorEnabled($user)) {
            $secret = $this->twoFactorService->getUserSecret($user);

            if (!$secret) {
                // Generate secret but DON'T save it to database yet
                $newSecret = $this->twoFactorService->generateSecret();
                $qrCodeImage = $this->twoFactorService->generateQrCodeImage($user, $newSecret);

                return $this->sendResponse([
                    'requires_two_factor_setup' => true,
                    'qr_code_image' => $qrCodeImage,
                    'temp_secret' => $newSecret, // Temporary secret for verification
                    'message' => 'Please set up two-factor authentication by scanning the QR code'
                ], 'Two-factor authentication setup required');
            }

            return $this->sendResponse([
                'requires_two_factor' => true,
                'message' => 'Two-factor authentication required'
            ], 'Two-factor authentication required');
        }

        try {
            $tokenName = $user->user_type === 'applicant' ? 'Applicant API Token' : 'API Token';
            $token = $user->createToken($tokenName)->plainTextToken;
            $this->loadUserRelations($user);

            $data = [
                'user' => $this->transformUser($user),
                'token' => $token,
                'token_type' => 'Bearer',
            ];

            if ($user->user_type !== 'applicant') {
                $data['entities'] = $this->transformEntities($user->entities);
            }

            CentLog("Auth", $user->id, [
                "status" => "success",
                "login_method" => "password",
                "user_type" => $user->user_type,
                "session_id" => session()->getId()
            ]);

            $message = $user->user_type === 'applicant' ? 'Applicant logged in successfully' : 'User logged in successfully';
            return $this->sendResponse($data, $message);
        } catch (\Exception $e) {
            return $this->sendServerError('Login failed: ' . $e->getMessage());
        }
    }

    public function verifyPin(Request $request): JsonResponse
    {
        $request->validate([
            'email' => 'required_without:username|email',
            'username' => 'required_without:email|string',
            'password' => 'required|string',
            'pin' => 'required|string|size:6|regex:/^[0-9]+$/',
            'temp_secret' => 'sometimes|string', // For initial setup
        ]);

        $email = $request->input('email') ?: $request->input('username');
        $user = PortalUser::where('email', $email)->first();

        if (!$this->isValidCredentials($user, $request->password)) {
            CentLog("Auth", $email, [
                "status" => "failed",
                "reason" => "invalid_credentials",
                "login_method" => "2fa",
                "user_type" => $user ? $user->user_type : "unknown"
            ], "warning");
            return $this->sendUnauthorized('Invalid credentials');
        }

        if (!$this->isUserActive($user)) {
            CentLog("Auth", $user->id, [
                "status" => "failed",
                "reason" => "account_inactive",
                "login_method" => "2fa",
                "user_type" => $user->user_type
            ], "warning");
            return $this->sendForbidden('User account is not active');
        }

        if (!$this->twoFactorService->isTwoFactorEnabled($user)) {
            return $this->sendError('Two-factor authentication is not enabled for this user', 400);
        }

        // Check if this is initial setup (temp_secret provided) or regular login
        $secret = $request->input('temp_secret');
        if (!$secret) {
            // Regular login - get saved secret
            $secret = $this->twoFactorService->getUserSecret($user);
            if (!$secret) {
                return $this->sendError('Two-factor authentication is not set up for this user', 400);
            }
        }

        if (!$this->twoFactorService->verifyCode($secret, $request->pin)) {
            return $this->sendUnauthorized('Invalid two-factor authentication PIN');
        }

        // If this was initial setup with temp_secret, save it to database now
        if ($request->input('temp_secret')) {
            $this->twoFactorService->enableTwoFactor($user, $secret);
        }

        try {
            $tokenName = $user->user_type === 'applicant' ? 'Applicant API Token' : 'API Token';
            $token = $user->createToken($tokenName)->plainTextToken;
            $this->loadUserRelations($user);

            $data = [
                'user' => $this->transformUser($user),
                'token' => $token,
                'token_type' => 'Bearer',
            ];

            if ($user->user_type !== 'applicant') {
                $data['entities'] = $this->transformEntities($user->entities);
            }

            CentLog("Auth", $user->id, [
                "status" => "success",
                "login_method" => "2fa",
                "user_type" => $user->user_type,
                "session_id" => session()->getId()
            ]);

            $message = $user->user_type === 'applicant'
                ? 'Applicant logged in successfully with two-factor authentication'
                : 'User logged in successfully with two-factor authentication';

            return $this->sendResponse($data, $message);
        } catch (\Exception $e) {
            return $this->sendServerError('Login failed: ' . $e->getMessage());
        }
    }

    public function logout(Request $request): JsonResponse
    {
        try {
            $user = $request->user();

            CentLog("Auth", $user->id, [
                "status" => "logout",
                "user_type" => $user->user_type,
                "session_id" => session()->getId()
            ]);

            $user->currentAccessToken()->delete();
            return $this->sendResponse([], 'User logged out successfully');
        } catch (\Exception $e) {
            return $this->sendServerError('Logout failed: ' . $e->getMessage());
        }
    }

    /**
     * Change user password
     */
    public function changePassword(ChangePasswordRequest $request): JsonResponse
    {
        try {
            $user = $request->user();

            if (!$user) {
                return $this->sendUnauthorized('User not authenticated');
            }

            // Ensure user is active
            if (!$this->isUserActive($user)) {
                return $this->sendForbidden('User account is not active');
            }

            $result = $this->passwordService->changePassword(
                $user,
                $request->current_password,
                $request->new_password,
                $request->boolean('invalidate_all_tokens', false)
            );

            $viewModel = new PasswordChangeViewModel($user, $result);

            if ($result['success']) {
                return response()->json($viewModel->toSuccessResponse(), 200);
            } else {
                return response()->json($viewModel->toErrorResponse(), 422);
            }

        } catch (\Exception $e) {
            return $this->sendServerError('Password change failed: ' . $e->getMessage());
        }
    }

    private function isValidCredentials(?PortalUser $user, string $password): bool
    {
        return $user && Hash::check($password, $user->password);
    }

    private function isUserActive(PortalUser $user): bool
    {
        $user->load('profile');

        if (!$user->profile) {
            return false;
        }

        return $user->profile->active;
    }

    private function loadUserRelations(PortalUser $user): void
    {
        $user->load([
            'profile',
            'entities' => function ($query) {
                $query->where('status', true)->with('profile');
            }
        ]);
    }

    private function transformUser(PortalUser $user): array
    {
        return [
            'id' => $user->id,
            'email' => $user->email,
            'user_type' => $user->user_type,
            'first_name' => $user->profile->first_name ?? null,
            'last_name' => $user->profile->last_name ?? null,
            'phone' => $user->profile->telephone ?? null,
            'address' => $user->profile->address ?? null,
            'two_factor_enabled' => $user->profile->two_factor_enabled ?? false,
        ];
    }

    private function transformEntities($entities): array
    {
        return $entities->map(function ($entity) {
            return [
                'id' => $entity->id,
                'name' => $entity->name,
                'code' => $entity->entity_code,
                'role' => $entity->pivot->role ?? null,
                'email' => $entity->profile->contact_email ?? null,
                'phone' => $entity->profile->phone ?? null,
            ];
        })->toArray();
    }
}
