<?php

declare(strict_types=1);

namespace App\Modules\Auth\Contracts;

use App\Modules\Auth\Models\PortalUser;

interface PasswordRepositoryInterface
{
    /**
     * Update user password
     *
     * @param PortalUser $user
     * @param string $hashedPassword
     * @return bool
     */
    public function updatePassword(PortalUser $user, string $hashedPassword): bool;

    /**
     * Get user by ID with profile
     *
     * @param int $userId
     * @return PortalUser|null
     */
    public function findUserWithProfile(int $userId): ?PortalUser;

    /**
     * Log password change activity
     *
     * @param int $userId
     * @param bool $success
     * @param string|null $reason
     * @param string|null $ipAddress
     * @param string|null $userAgent
     * @return void
     */
    public function logPasswordActivity(
        int $userId,
        bool $success,
        ?string $reason = null,
        ?string $ipAddress = null,
        ?string $userAgent = null
    ): void;

    /**
     * Count recent password change attempts
     *
     * @param int $userId
     * @param int $minutes
     * @return int
     */
    public function countRecentPasswordAttempts(int $userId, int $minutes = 15): int;
}
