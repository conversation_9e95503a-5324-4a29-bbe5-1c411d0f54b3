<?php

declare(strict_types=1);

namespace App\Modules\Auth\Contracts;

use App\Modules\Auth\Models\PortalUser;

interface PasswordServiceInterface
{
    /**
     * Change user password with security validations
     *
     * @param PortalUser $user
     * @param string $currentPassword
     * @param string $newPassword
     * @param bool $invalidateAllTokens
     * @return array
     */
    public function changePassword(
        PortalUser $user,
        string $currentPassword,
        string $newPassword,
        bool $invalidateAllTokens = false
    ): array;

    /**
     * Verify current password
     *
     * @param PortalUser $user
     * @param string $password
     * @return bool
     */
    public function verifyCurrentPassword(PortalUser $user, string $password): bool;

    /**
     * Log password change attempt
     *
     * @param PortalUser $user
     * @param bool $success
     * @param string|null $reason
     * @return void
     */
    public function logPasswordChangeAttempt(PortalUser $user, bool $success, ?string $reason = null): void;

    /**
     * Invalidate all user tokens
     *
     * @param PortalUser $user
     * @return int Number of tokens invalidated
     */
    public function invalidateAllUserTokens(PortalUser $user): int;
}
