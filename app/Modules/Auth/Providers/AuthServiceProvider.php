<?php

declare(strict_types=1);

namespace App\Modules\Auth\Providers;

use App\Modules\Auth\Contracts\PasswordRepositoryInterface;
use App\Modules\Auth\Contracts\PasswordServiceInterface;
use App\Modules\Auth\Repositories\PasswordRepository;
use App\Modules\Auth\Services\PasswordService;
use Illuminate\Support\ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Bind Password Repository
        $this->app->bind(PasswordRepositoryInterface::class, PasswordRepository::class);

        // Bind Password Service
        $this->app->bind(PasswordServiceInterface::class, PasswordService::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
