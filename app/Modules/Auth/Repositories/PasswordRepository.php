<?php

declare(strict_types=1);

namespace App\Modules\Auth\Repositories;

use App\Modules\Auth\Contracts\PasswordRepositoryInterface;
use App\Modules\Auth\Models\PortalUser;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PasswordRepository implements PasswordRepositoryInterface
{
    /**
     * Update user password
     */
    public function updatePassword(PortalUser $user, string $hashedPassword): bool
    {
        try {
            return $user->update(['password' => $hashedPassword]);
        } catch (\Exception $e) {
            Log::error('Failed to update password for user', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get user by ID with profile
     */
    public function findUserWithProfile(int $userId): ?PortalUser
    {
        return PortalUser::with('profile')->find($userId);
    }

    /**
     * Log password change activity
     */
    public function logPasswordActivity(
        int $userId,
        bool $success,
        ?string $reason = null,
        ?string $ipAddress = null,
        ?string $userAgent = null
    ): void {
        try {
            Log::info('Password change attempt', [
                'user_id' => $userId,
                'success' => $success,
                'reason' => $reason,
                'ip_address' => $ipAddress,
                'user_agent' => $userAgent,
                'timestamp' => now()
            ]);

            // You could also store this in a dedicated audit table
            // For now, we're using Laravel's logging system
        } catch (\Exception $e) {
            Log::error('Failed to log password activity', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Count recent password change attempts
     */
    public function countRecentPasswordAttempts(int $userId, int $minutes = 15): int
    {
        // This would typically query an audit table
        // For now, we'll return 0 as we're using log-based tracking
        // In a production environment, you'd want a dedicated audit table
        return 0;
    }
}
