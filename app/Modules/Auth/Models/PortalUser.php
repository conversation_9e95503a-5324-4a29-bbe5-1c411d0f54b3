<?php

declare(strict_types=1);

namespace App\Modules\Auth\Models;

use App\Modules\Entities\Models\Entity;
use App\Modules\Users\Models\Profile;
use App\Modules\Users\Models\UserType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class PortalUser extends Authenticatable
{
    use HasApiTokens, Notifiable, HasFactory;

    protected $table = 'portal_users';

    protected $fillable = [
        'email',
        'password',
        'user_type'
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function profile(): HasOne
    {
        return $this->hasOne(Profile::class, 'user_id');
    }

    public function entities(): BelongsToMany
    {
        return $this->belongsToMany(
            Entity::class,
            'entity_user_links',
            'user_id',
            'entity_id'
        )->withPivot('role')->withTimestamps();
    }

    public function userType(): BelongsTo
    {
        return $this->belongsTo(UserType::class, 'user_type', 'code');
    }

    public function getUserTypeName(): string
    {
        return $this->userType ? $this->userType->name : $this->user_type;
    }

    public function getPrimaryEntity(): ?Entity
    {
        return $this->entities()->first();
    }

    public function hasAccessToEntity(int $entityId): bool
    {
        return $this->entities()->where('entity_id', $entityId)->exists();
    }

    public function getRoleForEntity(int $entityId): ?string
    {
        $entity = $this->entities()->where('entity_id', $entityId)->first();
        return $entity ? $entity->pivot->role : null;
    }

    public function getMorphClass(): string
    {
        return 'PortalUser';
    }

    public function hasTwoFactorEnabled(): bool
    {
        return $this->profile && $this->profile->two_factor_enabled;
    }

    public function getTwoFactorSecret(): ?string
    {
        if (!$this->profile || !$this->profile->two_factor_secret) {
            return null;
        }

        return decrypt($this->profile->two_factor_secret);
    }

    public function applications(): HasMany
    {
        return $this->hasMany(\App\Modules\Applications\Models\Application::class, 'applicant_id');
    }

    public function applicantMisc(): HasOne
    {
        return $this->hasOne(\App\Modules\Applications\Models\ApplicantMisc::class, 'applicant_id');
    }

    protected static function newFactory()
    {
        return \Database\Factories\PortalUserFactory::new();
    }
}
