<?php

declare(strict_types=1);

namespace App\Modules\Auth\ViewModels;

use App\Modules\Auth\Models\PortalUser;

/**
 * Password Change View Model
 * 
 * Handles presentation logic for password change operations
 */
class PasswordChangeViewModel
{
    protected PortalUser $user;
    protected array $changeResult;

    public function __construct(PortalUser $user, array $changeResult = [])
    {
        $this->user = $user;
        $this->changeResult = $changeResult;
    }

    /**
     * Get formatted success response for API
     */
    public function toSuccessResponse(): array
    {
        return [
            'success' => true,
            'message' => $this->changeResult['message'] ?? 'Password changed successfully',
            'data' => [
                'user_id' => $this->user->id,
                'email' => $this->user->email,
                'tokens_invalidated' => $this->changeResult['tokens_invalidated'] ?? 0,
                'changed_at' => now()->toISOString(),
                'security_notice' => $this->getSecurityNotice()
            ]
        ];
    }

    /**
     * Get formatted error response for API
     */
    public function toErrorResponse(): array
    {
        return [
            'success' => false,
            'message' => $this->changeResult['message'] ?? 'Failed to change password',
            'error_code' => $this->changeResult['error_code'] ?? 'UNKNOWN_ERROR',
            'data' => [
                'user_id' => $this->user->id,
                'attempted_at' => now()->toISOString(),
                'security_recommendations' => $this->getSecurityRecommendations()
            ]
        ];
    }

    /**
     * Get security notice for successful password change
     */
    protected function getSecurityNotice(): array
    {
        $notices = [
            'Your password has been successfully changed.',
            'If you did not make this change, please contact support immediately.'
        ];

        if (($this->changeResult['tokens_invalidated'] ?? 0) > 0) {
            $notices[] = 'All your existing sessions have been invalidated for security.';
            $notices[] = 'You may need to log in again on other devices.';
        }

        return $notices;
    }

    /**
     * Get security recommendations for failed attempts
     */
    protected function getSecurityRecommendations(): array
    {
        $errorCode = $this->changeResult['error_code'] ?? '';

        switch ($errorCode) {
            case 'INVALID_CURRENT_PASSWORD':
                return [
                    'Ensure you are entering your current password correctly.',
                    'If you have forgotten your password, use the password reset feature.',
                    'Contact support if you continue to experience issues.'
                ];

            case 'SAME_PASSWORD':
                return [
                    'Choose a new password that is different from your current one.',
                    'Use a strong password with a mix of letters, numbers, and symbols.',
                    'Consider using a password manager to generate secure passwords.'
                ];

            default:
                return [
                    'Ensure your new password meets all security requirements.',
                    'Try again in a few minutes if you encounter technical issues.',
                    'Contact support if the problem persists.'
                ];
        }
    }

    /**
     * Get user information for logging purposes
     */
    public function getUserContext(): array
    {
        return [
            'user_id' => $this->user->id,
            'email' => $this->user->email,
            'user_type' => $this->user->user_type,
            'has_profile' => $this->user->profile !== null,
            'is_active' => $this->user->profile?->active ?? false
        ];
    }

    /**
     * Get password change summary
     */
    public function getChangeSummary(): array
    {
        return [
            'success' => $this->changeResult['success'] ?? false,
            'message' => $this->changeResult['message'] ?? '',
            'error_code' => $this->changeResult['error_code'] ?? null,
            'tokens_invalidated' => $this->changeResult['tokens_invalidated'] ?? 0,
            'user_context' => $this->getUserContext(),
            'timestamp' => now()->toISOString()
        ];
    }
}
