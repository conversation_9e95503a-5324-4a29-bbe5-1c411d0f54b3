<?php

declare(strict_types=1);

namespace App\Modules\Auth\Services;

use App\Modules\Auth\Contracts\PasswordServiceInterface;
use App\Modules\Auth\Contracts\PasswordRepositoryInterface;
use App\Modules\Auth\Models\PortalUser;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class PasswordService implements PasswordServiceInterface
{
    protected PasswordRepositoryInterface $passwordRepository;

    public function __construct(PasswordRepositoryInterface $passwordRepository)
    {
        $this->passwordRepository = $passwordRepository;
    }

    /**
     * Change user password with security validations
     */
    public function changePassword(
        PortalUser $user,
        string $currentPassword,
        string $newPassword,
        bool $invalidateAllTokens = false
    ): array {
        try {
            // Verify current password
            if (!$this->verifyCurrentPassword($user, $currentPassword)) {
                $this->logPasswordChangeAttempt($user, false, 'Invalid current password');
                return [
                    'success' => false,
                    'message' => 'Current password is incorrect',
                    'error_code' => 'INVALID_CURRENT_PASSWORD'
                ];
            }

            // Check if new password is same as current
            if (Hash::check($newPassword, $user->password)) {
                $this->logPasswordChangeAttempt($user, false, 'New password same as current');
                return [
                    'success' => false,
                    'message' => 'New password must be different from current password',
                    'error_code' => 'SAME_PASSWORD'
                ];
            }

            // Update password in transaction
            $result = DB::transaction(function () use ($user, $newPassword, $invalidateAllTokens) {
                $hashedPassword = Hash::make($newPassword);
                
                if (!$this->passwordRepository->updatePassword($user, $hashedPassword)) {
                    throw new \Exception('Failed to update password');
                }

                $tokensInvalidated = 0;
                if ($invalidateAllTokens) {
                    $tokensInvalidated = $this->invalidateAllUserTokens($user);
                }

                return [
                    'success' => true,
                    'tokens_invalidated' => $tokensInvalidated
                ];
            });

            $this->logPasswordChangeAttempt($user, true, 'Password changed successfully');

            return [
                'success' => true,
                'message' => 'Password changed successfully',
                'tokens_invalidated' => $result['tokens_invalidated']
            ];

        } catch (\Exception $e) {
            $this->logPasswordChangeAttempt($user, false, 'System error: ' . $e->getMessage());
            
            return [
                'success' => false,
                'message' => 'Failed to change password. Please try again.',
                'error_code' => 'SYSTEM_ERROR'
            ];
        }
    }

    /**
     * Verify current password
     */
    public function verifyCurrentPassword(PortalUser $user, string $password): bool
    {
        return Hash::check($password, $user->password);
    }

    /**
     * Log password change attempt
     */
    public function logPasswordChangeAttempt(PortalUser $user, bool $success, ?string $reason = null): void
    {
        $this->passwordRepository->logPasswordActivity(
            $user->id,
            $success,
            $reason,
            request()->ip(),
            request()->userAgent()
        );
    }

    /**
     * Invalidate all user tokens
     */
    public function invalidateAllUserTokens(PortalUser $user): int
    {
        return $user->tokens()->delete();
    }
}
