<?php

declare(strict_types=1);

namespace App\Modules\Auth\Services;

use App\Modules\Auth\Models\PortalUser;
use PragmaRX\Google2FA\Google2FA;
use Illuminate\Support\Str;
use chillerlan\QRCode\QRCode;
use chillerlan\QRCode\QROptions;

class TwoFactorService
{
    private Google2FA $google2fa;

    public function __construct()
    {
        $this->google2fa = new Google2FA();
    }

    public function generateSecret(): string
    {
        return $this->google2fa->generateSecretKey();
    }

    public function generateQrCodeUrl(PortalUser $user, string $secret): string
    {
        $appName = config('app.name', 'API Interface');
        $userEmail = $user->email;

        return $this->google2fa->getQRCodeUrl(
            $appName,
            $userEmail,
            $secret
        );
    }

    /**
     * Generate a QR code image as base64 encoded PNG
     */
    public function generateQrCodeImage(PortalUser $user, string $secret): string
    {
        $qrCodeUrl = $this->generateQrCodeUrl($user, $secret);
        return $this->generateLocalQrCode($qrCodeUrl);
    }

    /**
     * Generate a local QR code as base64 encoded SVG using chillerlan/php-qrcode
     */
    private function generateLocalQrCode(string $data): string
    {
        $options = new QROptions([
            'version'    => -1, // Auto-detect version
            'outputType' => 'svg',
            'eccLevel'   => 1, // ECC_L = 1, ECC_M = 0, ECC_Q = 3, ECC_H = 2
            'scale'      => 6,
            'svgViewBoxSize' => 200,
            'returnResource' => false, // Return string, not resource
        ]);

        $qrcode = new QRCode($options);
        $result = $qrcode->render($data);

        // Check if it's already a data URL
        if (str_starts_with($result, 'data:image/svg+xml;base64,')) {
            return $result;
        }

        // If it's raw SVG, encode it
        return 'data:image/svg+xml;base64,' . base64_encode($result);
    }

    /**
     * Generate a QR code image as base64 encoded PNG (alias for backward compatibility)
     * @deprecated Use generateQrCodeImage() instead
     */
    public function generateQrCodeImageUrl(PortalUser $user, string $secret): string
    {
        return $this->generateQrCodeImage($user, $secret);
    }

    public function verifyCode(string $secret, string $code): bool
    {
        return $this->google2fa->verifyKey($secret, $code);
    }

    public function generateBackupCodes(): array
    {
        $codes = [];
        for ($i = 0; $i < 8; $i++) {
            $codes[] = Str::random(10);
        }
        return $codes;
    }

    public function enableTwoFactor(PortalUser $user, string $secret): bool
    {
        if (!$user->profile) {
            return false;
        }

        $user->profile->update([
            'two_factor_enabled' => true,
            'two_factor_secret' => encrypt($secret)
        ]);

        return true;
    }

    public function disableTwoFactor(PortalUser $user): bool
    {
        if (!$user->profile) {
            return false;
        }

        $user->profile->update([
            'two_factor_enabled' => false,
            'two_factor_secret' => null
        ]);

        return true;
    }

    public function getUserSecret(PortalUser $user): ?string
    {
        if (!$user->profile || !$user->profile->two_factor_secret) {
            return null;
        }

        return decrypt($user->profile->two_factor_secret);
    }

    public function isTwoFactorEnabled(PortalUser $user): bool
    {
        return $user->profile && $user->profile->two_factor_enabled;
    }
}
