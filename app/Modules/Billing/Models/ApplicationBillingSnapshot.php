<?php

declare(strict_types=1);

namespace App\Modules\Billing\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Modules\Applications\Models\Application;

class ApplicationBillingSnapshot extends Model
{
    protected $table = 'application_billing_snapshots';

    protected $fillable = [
        'application_id',
        'supplier_fee',
        'admin_fee',
        'self_payment'
    ];

    protected $casts = [
        'admin_fee' => 'decimal:2',
        'supplier_fee' => 'decimal:2',
        'self_payment' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function application(): BelongsTo
    {
        return $this->belongsTo(Application::class, 'application_id');
    }
}
