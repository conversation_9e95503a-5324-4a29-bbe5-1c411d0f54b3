<?php

declare(strict_types=1);

namespace App\Modules\Billing\Controllers;

use App\Core\BaseApiController;
use App\Modules\Applications\Services\ApplicationAccessService;
use App\Modules\Auth\Models\PortalUser;
use App\Modules\Billing\Requests\GetPaymentStatusRequest;
use App\Modules\Billing\Services\PaymentService;
use Illuminate\Http\JsonResponse;

class BillingController extends BaseApiController
{
    private PaymentService $paymentService;
    private ApplicationAccessService $accessService;

    public function __construct(
        PaymentService $paymentService,
        ApplicationAccessService $accessService
    ) {
        $this->paymentService = $paymentService;
        $this->accessService = $accessService;
    }

    public function getPaymentStatus(GetPaymentStatusRequest $request, int $applicantId): JsonResponse
    {
        try {
            $user = $request->user();

            if (!in_array($user->user_type, ['applicant', 'client_user', 'requester', 'doc_checker'])) {
                return $this->sendForbidden('Access denied for this user type');
            }

            if ($user->user_type === 'applicant' && $user->id !== $applicantId) {
                return $this->sendForbidden('You can only check your own payment status');
            }

            if (in_array($user->user_type, ['client_user', 'requester', 'doc_checker'])) {
                $applicant = PortalUser::find($applicantId);
                if (!$applicant || $applicant->user_type !== 'applicant') {
                    return $this->sendError('Applicant not found', [], 404);
                }

                $accessibleApplications = $this->accessService->getAccessibleApplicationsQuery($user);
                $hasAccess = $accessibleApplications->where('applicant_id', $applicantId)->exists();

                if (!$hasAccess) {
                    return $this->sendForbidden('You do not have access to this applicant');
                }
            }

            $paymentStatus = $this->paymentService->getPaymentStatus($applicantId);

            return $this->sendResponse($paymentStatus, 'Payment status retrieved successfully');
        } catch (\Exception $e) {
            return $this->sendServerError('Failed to retrieve payment status: ' . $e->getMessage());
        }
    }
}
