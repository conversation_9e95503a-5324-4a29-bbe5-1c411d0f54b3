<?php

declare(strict_types=1);

namespace App\Modules\Billing\Services;

use App\Modules\Applications\Models\Application;
use Illuminate\Support\Collection;

class PaymentService
{
    public function getPaymentStatus(int $applicantId): array
    {
        $applicationsWithPayment = Application::where('applications.applicant_id', $applicantId)
            ->join('application_billing_snapshots', 'applications.id', '=', 'application_billing_snapshots.application_id')
            ->join('products', 'applications.product_id', '=', 'products.id')
            ->where('application_billing_snapshots.self_payment', true)
            ->select([
                'applications.id as application_id',
                'products.id as product_id',
                'products.name as product_name',
                'products.code as product_code',
                'application_billing_snapshots.admin_fee',
                'application_billing_snapshots.supplier_fee',
                'application_billing_snapshots.self_payment'
            ])
            ->get();

        $paymentRequired = $applicationsWithPayment->isNotEmpty();
        $totalPaymentAmount = 0;
        $applicationsRequiringPayment = [];

        foreach ($applicationsWithPayment as $app) {
            $totalFee = ($app->admin_fee ?? 0) + ($app->supplier_fee ?? 0);
            $totalPaymentAmount += $totalFee;

            $applicationsRequiringPayment[] = [
                'application_id' => $app->application_id,
                'product' => [
                    'id' => $app->product_id,
                    'name' => $app->product_name,
                    'code' => $app->product_code,
                ],
                'billing' => [
                    'admin_fee' => $app->admin_fee,
                    'supplier_fee' => $app->supplier_fee,
                    'total_fee' => $totalFee,
                    'currency' => 'GBP',
                ],
            ];
        }

        return [
            'payment_required' => $paymentRequired,
            'total_amount' => $totalPaymentAmount,
            'currency' => 'GBP',
            'applications' => $applicationsRequiringPayment,
            'total_applications_requiring_payment' => count($applicationsRequiringPayment),
        ];
    }

    public function hasUnpaidApplications(int $applicantId): bool
    {
        return Application::where('applications.applicant_id', $applicantId)
            ->join('application_billing_snapshots', 'applications.id', '=', 'application_billing_snapshots.application_id')
            ->where('application_billing_snapshots.self_payment', true)
            ->exists();
    }

    public function getApplicationsRequiringPayment(int $applicantId): Collection
    {
        return Application::where('applications.applicant_id', $applicantId)
            ->join('application_billing_snapshots', 'applications.id', '=', 'application_billing_snapshots.application_id')
            ->join('products', 'applications.product_id', '=', 'products.id')
            ->where('application_billing_snapshots.self_payment', true)
            ->select([
                'applications.*',
                'products.name as product_name',
                'products.code as product_code',
                'application_billing_snapshots.admin_fee',
                'application_billing_snapshots.supplier_fee',
                'application_billing_snapshots.self_payment'
            ])
            ->get();
    }
}
