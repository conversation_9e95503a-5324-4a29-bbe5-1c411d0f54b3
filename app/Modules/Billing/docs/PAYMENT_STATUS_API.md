# Payment Status API

This document describes the payment status checking API endpoint and the payment validation in the save application data endpoint.

## Overview

The payment system checks if applicants have applications requiring self-payment based on the `self_payment` flag in the `application_billing_snapshots` table. When an applicant has applications requiring payment, they must complete payment before being able to save application form data.

## Payment Status Endpoint

### Check Payment Status

```http
GET /api/v1/billing/payment-status/{applicantId}
Authorization: Bearer {token}
```

**Parameters:**
- `applicantId` (integer, required): The ID of the applicant to check payment status for

**Access Control:**
- **Applicants**: Can only check their own payment status (`applicantId` must match their user ID)
- **Client Users** (`client_user`, `requester`, `document_checker`): Can check payment status for applicants they have access to through the entity hierarchy

**Response (Payment Required):**
```json
{
    "success": true,
    "data": {
        "payment_required": true,
        "total_amount": 150.00,
        "currency": "GBP",
        "applications": [
            {
                "application_id": 1,
                "product": {
                    "id": 1,
                    "name": "DBS Check",
                    "code": "DBS001"
                },
                "billing": {
                    "admin_fee": 50.00,
                    "supplier_fee": 75.00,
                    "total_fee": 125.00,
                    "currency": "GBP"
                }
            },
            {
                "application_id": 2,
                "product": {
                    "id": 2,
                    "name": "Reference Check",
                    "code": "REF001"
                },
                "billing": {
                    "admin_fee": 15.00,
                    "supplier_fee": 10.00,
                    "total_fee": 25.00,
                    "currency": "GBP"
                }
            }
        ],
        "total_applications_requiring_payment": 2
    },
    "message": "Payment status retrieved successfully"
}
```

**Response (No Payment Required):**
```json
{
    "success": true,
    "data": {
        "payment_required": false,
        "total_amount": 0,
        "currency": "GBP",
        "applications": [],
        "total_applications_requiring_payment": 0
    },
    "message": "Payment status retrieved successfully"
}
```

## Payment Validation in Save Application Data

The save application data endpoint (`POST /api/v1/applications/save-data`) includes payment validation for applicants.

**Payment Required Error Response (402):**
```json
{
    "success": false,
    "message": "Payment required before saving application data",
    "data": {
        "payment_required": true,
        "total_amount": 150.00,
        "currency": "GBP",
        "applications_requiring_payment": [
            {
                "application_id": 1,
                "product": {
                    "id": 1,
                    "name": "DBS Check",
                    "code": "DBS001"
                },
                "billing": {
                    "admin_fee": 50.00,
                    "supplier_fee": 75.00,
                    "total_fee": 125.00,
                    "currency": "GBP"
                }
            }
        ]
    }
}
```

## Error Responses

**Forbidden (403) - Wrong User Type:**
```json
{
    "success": false,
    "message": "Access denied for this user type"
}
```

**Forbidden (403) - Applicant Accessing Other's Data:**
```json
{
    "success": false,
    "message": "You can only check your own payment status"
}
```

**Forbidden (403) - No Access to Applicant:**
```json
{
    "success": false,
    "message": "You do not have access to this applicant"
}
```

**Not Found (404):**
```json
{
    "success": false,
    "message": "Applicant not found"
}
```

**Server Error (500):**
```json
{
    "success": false,
    "message": "Failed to retrieve payment status: [error details]"
}
```

## Important Notes

### Payment Logic

- Payment is only required when `self_payment = true` in the `application_billing_snapshots` table
- The `self_payment` flag is determined by the job role's `self_payment` setting when the application is created
- Client users (`client_user`, `requester`, `document_checker`) can save application data regardless of payment status
- Only applicants are restricted from saving data when payment is required

### Billing Snapshots

- Billing snapshots are created when applications are generated during applicant creation
- They capture the pricing at the time of application creation (admin_fee + supplier_fee)
- The `self_payment` flag determines whether the applicant needs to pay directly

### Error Codes

- `402 Payment Required`: Returned when applicant tries to save data but payment is required
- `403 Forbidden`: Access denied for user type or insufficient permissions
- `404 Not Found`: Applicant not found or not accessible to the requesting user

## Usage Examples

### Check Payment Status (Applicant)

```bash
curl -X GET http://localhost:8002/api/v1/billing/payment-status/123 \
  -H "Authorization: Bearer {applicant_token}"
```

### Check Payment Status (Client User)

```bash
curl -X GET http://localhost:8002/api/v1/billing/payment-status/123 \
  -H "Authorization: Bearer {client_user_token}"
```

### Save Data with Payment Check

```bash
curl -X POST http://localhost:8002/api/v1/applications/save-data \
  -H "Authorization: Bearer {applicant_token}" \
  -H "Content-Type: application/json" \
  -d '{
    "application_id": 1,
    "form_data": {
      "ApplicantDetails::TITLE": "MR",
      "ApplicantDetails::FORENAME": "John"
    }
  }'
```

## Integration Notes

- The payment status endpoint integrates with the existing `ApplicationAccessService` for proper access control
- Payment validation in save-data only applies to applicants, not client users
- The system uses the same billing snapshot data that's created during applicant creation
- All payment amounts are returned in GBP currency
