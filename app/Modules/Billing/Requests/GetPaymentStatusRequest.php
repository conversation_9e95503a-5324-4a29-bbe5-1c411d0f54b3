<?php

declare(strict_types=1);

namespace App\Modules\Billing\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GetPaymentStatusRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'applicantId' => 'required|integer|exists:portal_users,id',
        ];
    }

    public function prepareForValidation(): void
    {
        $this->merge([
            'applicantId' => $this->route('applicantId'),
        ]);
    }
}
