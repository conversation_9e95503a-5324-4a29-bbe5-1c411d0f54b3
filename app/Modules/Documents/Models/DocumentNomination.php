<?php

declare(strict_types=1);

namespace App\Modules\Documents\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Modules\Applications\Models\Application;
use App\Modules\Auth\Models\PortalUser;
use App\Modules\Documents\Services\DocumentProductMappingService;

class DocumentNomination extends Model
{
    use HasFactory;

    protected $table = 'document_nominations';

    protected $fillable = [
        'application_id',
        'document_type_key',
        'document_name',
        'document_group',
        'route_number',
        'key_values',
        'confirms_address',
        'status',
        'nominated_by',
        'verified_by_type',
        'verified_by_user_id',
        'verified_at'
    ];

    protected $casts = [
        'key_values' => 'array',
        'confirms_address' => 'boolean',
        'verified_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function application(): BelongsTo
    {
        return $this->belongsTo(Application::class, 'application_id');
    }

    /**
     * Get document type information from config
     */
    public function getDocumentTypeConfig(): ?array
    {
        if (!$this->document_type_key) {
            return null;
        }

        $mappingService = app(DocumentProductMappingService::class);
        return $mappingService->getDocumentByKey($this->document_type_key);
    }

    public function nominatedBy(): BelongsTo
    {
        return $this->belongsTo(PortalUser::class, 'nominated_by');
    }

    public function documentFiles(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(\App\Modules\Documents\Models\DocumentFile::class, 'document_nomination_id');
    }

    /**
     * Get nominations for a specific application and route
     */
    public static function getForApplicationRoute(int $applicationId, int $routeNumber): \Illuminate\Database\Eloquent\Collection
    {
        return static::where('application_id', $applicationId)
            ->where('route_number', $routeNumber)
            ->get();
    }

    /**
     * Get all nominations for an application
     */
    public static function getForApplication(int $applicationId): \Illuminate\Database\Eloquent\Collection
    {
        return static::where('application_id', $applicationId)
            ->orderBy('route_number')
            ->orderBy('created_at')
            ->get();
    }

    /**
     * Check if document confirms address
     */
    public function confirmsAddress(): bool
    {
        if ($this->confirms_address) {
            return true;
        }

        $documentConfig = $this->getDocumentTypeConfig();
        return $documentConfig['confirms_address'] ?? false;
    }

    /**
     * Get document group
     */
    public function getDocumentGroup(): string
    {
        return $this->document_group ?? '';
    }

    /**
     * Validate the document data
     */
    public function validateData(): array
    {
        if (!$this->document_type_key) {
            return ['document_type_key' => 'Document type key is required'];
        }

        $mappingService = app(DocumentProductMappingService::class);
        return $mappingService->validateDocumentData($this->document_type_key, $this->key_values);
    }

    /**
     * Check if this nomination is valid
     */
    public function isValid(): bool
    {
        $errors = $this->validateData();
        return empty($errors);
    }

    /**
     * Get formatted document data for display
     */
    public function getFormattedData(): array
    {
        $formatted = [];
        $documentConfig = $this->getDocumentTypeConfig();
        $fields = $documentConfig['data_fields'] ?? [];

        foreach ($this->key_values as $key => $value) {
            $fieldConfig = $fields[$key] ?? null;

            if ($fieldConfig) {
                $formatted[$key] = [
                    'label' => ucwords(str_replace('_', ' ', $key)),
                    'value' => $value,
                    'type' => $fieldConfig['type'] ?? 'string'
                ];
            }
        }

        return $formatted;
    }

    /**
     * Create or update nomination
     */
    public static function createOrUpdateNomination(array $data): self
    {
        return static::updateOrCreate(
            [
                'application_id' => $data['application_id'],
                'document_type_key' => $data['document_type_key'],
                'route_number' => $data['route_number']
            ],
            $data
        );
    }
}
