<?php

declare(strict_types=1);

namespace App\Modules\Documents\Config;

/**
 * Document Types Configuration
 * 
 * Defines all available document types for DBS applications with their properties,
 * validation rules, and product applicability.
 */
class DocumentTypes
{
    /**
     * All available document types organized by groups
     */
    public static function getAll(): array
    {
        return [
            // Group 1 - Primary Identity Documents
            'passport_any' => [
                'name' => 'Passport',
                'document_group' => '1',
                'requires_photo' => true,
                'confirms_address' => false,
                'applicable_countries' => ['ANY'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Any current and valid passport. A UK passport can be expired up to a maximum of 6 months.',
                'data_fields' => [
                    'passport_number' => ['type' => 'string', 'required' => true, 'label' => 'Passport Number'],
                    'issue_country' => ['type' => 'string', 'required' => true, 'label' => 'Issue Country'],
                    'issue_date' => ['type' => 'date', 'required' => true, 'label' => 'Issue Date'],
                    'expiry_date' => ['type' => 'date', 'required' => true, 'label' => 'Expiry Date'],
                    'issuing_authority' => ['type' => 'string', 'required' => false, 'label' => 'Issuing Authority']
                ]
            ],
            'e_visa' => [
                'name' => 'e-Visa',
                'document_group' => '1',
                'requires_photo' => true,
                'confirms_address' => false,
                'applicable_countries' => ['ANY'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Accessed via the \'View and Prove\' service. The share code requested by the applicant should be an \'immigration status\' share code. If you do not have a UKVI account to access your eVisa you can create one online (Get access to your online immigration status (eVisa) - GOV.UK)',
                'data_fields' => [
                    'share_code' => ['type' => 'string', 'required' => true, 'label' => 'Share Code'],
                    'expiry_date' => ['type' => 'date', 'required' => true, 'label' => 'Expiry Date']
                ]
            ],
            'biometric_residence_permit_uk' => [
                'name' => 'Biometric Residence Permit',
                'document_group' => '1',
                'requires_photo' => true,
                'confirms_address' => false,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'UK. A BRP showing Indefinite Leave to Remain, Indefinite Leave to Enter or No Time Limit can be used up to 18 months past the expiry date of the BRP. BRP holders should be encouraged to create an account and access their eVisa.',
                'data_fields' => [
                    'permit_number' => ['type' => 'string', 'required' => true, 'label' => 'Permit Number'],
                    'issue_date' => ['type' => 'date', 'required' => true, 'label' => 'Issue Date'],
                    'expiry_date' => ['type' => 'date', 'required' => true, 'label' => 'Expiry Date'],
                    'leave_type' => ['type' => 'string', 'required' => true, 'label' => 'Leave Type'],
                    'issuing_authority' => ['type' => 'string', 'required' => false, 'label' => 'Issuing Authority']
                ]
            ],
            'application_registration_card_group1' => [
                'name' => 'Application Registration Card (ARC)',
                'document_group' => '1',
                'requires_photo' => true,
                'confirms_address' => false,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Issued by the Home Office. Must be checked against the Home Office Employer Checking Service.',
                'data_fields' => [
                    'card_number' => ['type' => 'string', 'required' => true, 'label' => 'Card Number'],
                    'issue_date' => ['type' => 'date', 'required' => true, 'label' => 'Issue Date'],
                    'expiry_date' => ['type' => 'date', 'required' => true, 'label' => 'Expiry Date']
                ]
            ],
            'photocard_drivers_licence_uk' => [
                'name' => 'Current driving licence photocard - (full or provisional)',
                'document_group' => '1',
                'requires_photo' => true,
                'confirms_address' => false,
                'applicable_countries' => ['GB', 'UK', 'IM', 'JE', 'GG'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Current and valid photocard driving licence issued by UK, Isle of Man, and Channel Islands. From 8 June 2015, the paper counterpart to the photocard driving licence will not be valid and will no longer be issued by DVLA.',
                'data_fields' => [
                    'licence_number' => ['type' => 'string', 'required' => true, 'label' => 'Licence Number'],
                    'issue_date' => ['type' => 'date', 'required' => true, 'label' => 'Issue Date'],
                    'expiry_date' => ['type' => 'date', 'required' => true, 'label' => 'Expiry Date'],
                    'licence_type' => ['type' => 'string', 'required' => true, 'label' => 'Licence Type'], // full or provisional
                    'categories' => ['type' => 'string', 'required' => false, 'label' => 'Categories']
                ]
            ],
            'birth_certificate_uk_12m' => [
                'name' => 'Birth certificate - issued within 12 months of birth',
                'document_group' => '1',
                'requires_photo' => false,
                'confirms_address' => false,
                'applicable_countries' => ['GB', 'UK', 'IM', 'JE', 'GG'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'UK, Isle of Man, and Channel Islands - including those issued by UK authorities overseas, for example embassies, High Commissions and HM Forces. Must be original birth certificate. Certified copies are a group 2a document.',
                'data_fields' => [
                    'certificate_number' => ['type' => 'string', 'required' => true, 'label' => 'Certificate Number'],
                    'issue_date' => ['type' => 'date', 'required' => true, 'label' => 'Issue Date'],
                    'date_of_birth' => ['type' => 'date', 'required' => true, 'label' => 'Date Of Birth'],
                    'full_name_on_certificate' => ['type' => 'string', 'required' => true, 'label' => 'Full Name On Certificate'],
                    'issuing_authority' => ['type' => 'string', 'required' => true, 'label' => 'Issuing Authority']
                ]
            ],
            'adoption_certificate_uk' => [
                'name' => 'Adoption certificate',
                'document_group' => '1',
                'requires_photo' => false,
                'confirms_address' => false,
                'applicable_countries' => ['GB', 'UK', 'JE', 'GG'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'UK and Channel Islands',
                'data_fields' => [
                    'entry_number' => ['type' => 'string', 'required' => true, 'label' => 'Entry Number'],
                    'adoption_date' => ['type' => 'date', 'required' => true, 'label' => 'Adoption Date'],
                    'issued_in_uk' => ['type' => 'boolean', 'required' => true, 'label' => 'Is this document issued in United Kingdom (UK)?']
                ]
            ]
        ];
    }

    /**
     * Get secondary document types (Group 2a only - Trusted Government Documents)
     */
    public static function getSecondaryDocuments(): array
    {
        return [
            // Group 2a - Trusted Government Documents
            'photocard_driving_licence_non_uk' => [
                'name' => 'Current driving licence photocard - (full or provisional)',
                'document_group' => '2a',
                'requires_photo' => true,
                'confirms_address' => false,
                'applicable_countries' => ['ANY'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Current and valid. All countries outside the UK (excluding Isle of Man and Channel Islands)',
                'data_fields' => [
                    'licence_number' => ['type' => 'string', 'required' => true, 'label' => 'Licence Number'],
                    'issue_country' => ['type' => 'string', 'required' => true, 'label' => 'Issue Country'],
                    'issue_date' => ['type' => 'date', 'required' => true, 'label' => 'Issue Date'],
                    'expiry_date' => ['type' => 'date', 'required' => true, 'label' => 'Expiry Date'],
                    'licence_type' => ['type' => 'string', 'required' => true, 'label' => 'Licence Type'] // full or provisional
                ]
            ],
            'paper_driving_licence_uk' => [
                'name' => 'Current driving licence (full or provisional) - paper version (if issued before March 2000)',
                'document_group' => '2a',
                'requires_photo' => false,
                'confirms_address' => false,
                'applicable_countries' => ['GB', 'UK', 'IM', 'JE', 'GG'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Current and valid. UK, Isle of Man, and Channel Islands. For a paper licence to be valid it must be issued before March 2000 and all information, including name and address, must be up to date',
                'data_fields' => [
                    'licence_number' => ['type' => 'string', 'required' => true, 'label' => 'Licence Number'],
                    'issue_date' => ['type' => 'date', 'required' => true, 'label' => 'Issue Date'],
                    'expiry_date' => ['type' => 'date', 'required' => true, 'label' => 'Expiry Date'],
                    'licence_type' => ['type' => 'string', 'required' => true, 'label' => 'Licence Type'], // full or provisional
                    'categories' => ['type' => 'string', 'required' => false, 'label' => 'Categories']
                ]
            ],
            'birth_certificate_uk_after_birth' => [
                'name' => 'Birth certificate - issued after time of birth',
                'document_group' => '2a',
                'requires_photo' => false,
                'confirms_address' => false,
                'applicable_countries' => ['GB', 'UK', 'IM', 'JE', 'GG'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'UK, Isle of Man, and Channel Islands',
                'data_fields' => [
                    'certificate_number' => ['type' => 'string', 'required' => true, 'label' => 'Certificate Number'],
                    'issue_date' => ['type' => 'date', 'required' => true, 'label' => 'Issue Date'],
                    'date_of_birth' => ['type' => 'date', 'required' => true, 'label' => 'Date Of Birth'],
                    'full_name_on_certificate' => ['type' => 'string', 'required' => true, 'label' => 'Full Name On Certificate'],
                    'issuing_authority' => ['type' => 'string', 'required' => true, 'label' => 'Issuing Authority']
                ]
            ],
            'marriage_civil_partnership_uk' => [
                'name' => 'Marriage/civil partnership certificate',
                'document_group' => '2a',
                'requires_photo' => false,
                'confirms_address' => false,
                'applicable_countries' => ['GB', 'UK', 'JE', 'GG'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'UK and Channel Islands',
                'data_fields' => [
                    'certificate_number' => ['type' => 'string', 'required' => true, 'label' => 'Certificate Number'],
                    'marriage_date' => ['type' => 'date', 'required' => true, 'label' => 'Marriage Date'],
                    'place_of_marriage' => ['type' => 'string', 'required' => true, 'label' => 'Place Of Marriage'],
                    'registrar_details' => ['type' => 'string', 'required' => false, 'label' => 'Registrar Details']
                ]
            ],
            'immigration_document_non_uk' => [
                'name' => 'Immigration document, visa, or work permit',
                'document_group' => '2a',
                'requires_photo' => true,
                'confirms_address' => false,
                'applicable_countries' => ['ANY'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Issued by a country outside the UK. Valid only for roles whereby the applicant is living and working outside of the UK. Visa/permit must relate to the non-UK country in which the role is based',
                'data_fields' => [
                    'document_number' => ['type' => 'string', 'required' => true, 'label' => 'Document Number'],
                    'issue_country' => ['type' => 'string', 'required' => true, 'label' => 'Issue Country'],
                    'issue_date' => ['type' => 'date', 'required' => true, 'label' => 'Issue Date'],
                    'expiry_date' => ['type' => 'date', 'required' => true, 'label' => 'Expiry Date'],
                    'visa_type' => ['type' => 'string', 'required' => false, 'label' => 'Visa Type']
                ]
            ],
            'hm_forces_id' => [
                'name' => 'HM Forces ID card or HM Armed Forces Veteran card',
                'document_group' => '2a',
                'requires_photo' => true,
                'confirms_address' => false,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'UK',
                'data_fields' => [
                    'card_type' => ['type' => 'string', 'required' => true, 'label' => 'Card Type'], // HM Forces ID or HM Armed Forces Veteran
                    'service_number' => ['type' => 'string', 'required' => true, 'label' => 'Service Number'],
                    'rank' => ['type' => 'string', 'required' => true, 'label' => 'Rank'],
                    'unit' => ['type' => 'string', 'required' => false, 'label' => 'Unit'],
                    'issue_date' => ['type' => 'date', 'required' => true, 'label' => 'Issue Date']
                ]
            ],
            'firearms_licence' => [
                'name' => 'Firearms licence',
                'document_group' => '2a',
                'requires_photo' => false,
                'confirms_address' => false,
                'applicable_countries' => ['GB', 'UK', 'IM', 'JE', 'GG'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'UK, Isle of Man, and Channel Islands',
                'data_fields' => [
                    'licence_number' => ['type' => 'string', 'required' => true, 'label' => 'Licence Number'],
                    'issue_date' => ['type' => 'date', 'required' => true, 'label' => 'Issue Date'],
                    'expiry_date' => ['type' => 'date', 'required' => true, 'label' => 'Expiry Date'],
                    'issuing_authority' => ['type' => 'string', 'required' => true, 'label' => 'Issuing Authority']
                ]
            ]
        ];
    }
}
