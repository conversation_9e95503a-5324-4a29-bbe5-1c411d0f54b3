<?php

declare(strict_types=1);

namespace App\Modules\Documents\Config;

/**
 * Document Registry
 * 
 * Central registry for all document types and product requirements
 */
class DocumentRegistry
{
    /**
     * Get all available document types
     */
    public static function getAllDocuments(): array
    {
        $documents = array_merge(
            DocumentTypes::getAll(),
            DocumentTypes::getSecondaryDocuments(),
            AddressDocuments::getAll(),
            RightToWorkDocuments::getAll()
        );

        // Remove duplicate biometric_residence_permit (without _uk suffix)
        // This is a temporary fix to remove a duplicate document that appears
        // to be coming from legacy database data
        \Log::info("DocumentRegistry: getAllDocuments called. Document count: " . count($documents));
        \Log::info("DocumentRegistry: Document keys: " . implode(', ', array_keys($documents)));

        if (isset($documents['biometric_residence_permit']) && isset($documents['biometric_residence_permit_uk'])) {
            \Log::info("DocumentRegistry: Removing duplicate biometric_residence_permit");
            unset($documents['biometric_residence_permit']);
        } else {
            \Log::info("DocumentRegistry: No duplicate found to remove");
        }

        return $documents;
    }

    /**
     * Get documents by group
     */
    public static function getDocumentsByGroup(string $group): array
    {
        $allDocuments = self::getAllDocuments();
        
        return array_filter($allDocuments, function($document) use ($group) {
            return $document['document_group'] === $group;
        });
    }

    /**
     * Get documents applicable for a specific product
     */
    public static function getDocumentsForProduct(string $productCode): array
    {
        $allDocuments = self::getAllDocuments();
        
        return array_filter($allDocuments, function($document) use ($productCode) {
            return in_array($productCode, $document['applicable_app_types'] ?? []);
        });
    }

    /**
     * Get documents filtered by nationality and product
     */
    public static function getFilteredDocuments(string $productCode, ?string $nationality = null): array
    {
        $documents = self::getDocumentsForProduct($productCode);

        if ($nationality) {
            $documents = array_filter($documents, function($document) use ($nationality) {
                $countries = $document['applicable_countries'] ?? [];

                if (in_array('ANY', $countries) || empty($countries)) {
                    return true;
                }

                $normalizedNationality = strtoupper($nationality);
                $ukNationalities = ['BRITISH', 'UK', 'UNITED KINGDOM', 'GB'];
                $isUkNational = in_array($normalizedNationality, $ukNationalities);

                foreach ($countries as $country) {
                    $normalizedCountry = strtoupper($country);

                    if ($normalizedCountry === $normalizedNationality) {
                        return true;
                    }

                    if ($isUkNational && in_array($normalizedCountry, ['GB', 'UK', 'IM', 'JE', 'GG'])) {
                        return true;
                    }
                }

                return false;
            });
        }

        return $documents;
    }

    /**
     * Get documents grouped by document group for a product
     */
    public static function getGroupedDocumentsForProduct(string $productCode, ?string $nationality = null): array
    {
        \Log::info("DocumentRegistry: getGroupedDocumentsForProduct called with product: $productCode, nationality: $nationality");

        $documents = self::getFilteredDocuments($productCode, $nationality);

        \Log::info("DocumentRegistry: Filtered documents count: " . count($documents));
        \Log::info("DocumentRegistry: Filtered document keys: " . implode(', ', array_keys($documents)));

        $grouped = [];
        foreach ($documents as $key => $document) {
            $group = $document['document_group'];
            $grouped[$group][$key] = $document;
        }

        \Log::info("DocumentRegistry: Group 1 document count: " . (isset($grouped['1']) ? count($grouped['1']) : 0));
        if (isset($grouped['1'])) {
            \Log::info("DocumentRegistry: Group 1 document keys: " . implode(', ', array_keys($grouped['1'])));

            // Final safety check: Remove duplicate biometric_residence_permit from Group 1
            // if both biometric_residence_permit and biometric_residence_permit_uk exist
            if (isset($grouped['1']['biometric_residence_permit']) && isset($grouped['1']['biometric_residence_permit_uk'])) {
                \Log::info("DocumentRegistry: Removing duplicate biometric_residence_permit from Group 1");
                unset($grouped['1']['biometric_residence_permit']);
            }

            \Log::info("DocumentRegistry: Final Group 1 document count: " . count($grouped['1']));
            \Log::info("DocumentRegistry: Final Group 1 document keys: " . implode(', ', array_keys($grouped['1'])));
        }

        return $grouped;
    }

    /**
     * Get document by key
     */
    public static function getDocumentByKey(string $documentKey): ?array
    {
        $allDocuments = self::getAllDocuments();
        return $allDocuments[$documentKey] ?? null;
    }

    /**
     * Check if document is applicable for product
     */
    public static function isDocumentApplicableForProduct(string $documentKey, string $productCode): bool
    {
        $document = self::getDocumentByKey($documentKey);
        
        if (!$document) {
            return false;
        }
        
        return in_array($productCode, $document['applicable_app_types'] ?? []);
    }

    /**
     * Check if document is applicable for nationality
     */
    public static function isDocumentApplicableForNationality(string $documentKey, string $nationality): bool
    {
        $document = self::getDocumentByKey($documentKey);
        
        if (!$document) {
            return false;
        }
        
        $countries = $document['applicable_countries'] ?? [];
        return in_array('ANY', $countries) || 
               in_array($nationality, $countries) || 
               empty($countries);
    }

    /**
     * Get product group requirements
     */
    public static function getProductGroupRequirements(): array
    {
        return [
            'DBSEC' => ['1', '1a', '2a', '2b'],
            'DBSSC' => ['1', '1a', '2a', '2b'],
            'DBSBC' => ['1', '1a', '2a', '2b'],
            'DSBASIC' => ['1', '1a', '2a', '2b'],
            'RTW' => ['1a', 'a', 'b1', 'b2']
        ];
    }

    /**
     * Get allowed document groups for a product
     */
    public static function getAllowedGroupsForProduct(string $productCode): array
    {
        $requirements = self::getProductGroupRequirements();
        return $requirements[$productCode] ?? [];
    }

    /**
     * Get route requirements for different document routes
     */
    public static function getRouteRequirements(): array
    {
        return [
            'route_1' => [
                'description' => 'Route 1: 1 document from Group 1, and 1 further document from either Group 1, or Group 2a or 2b',
                'required_groups' => [
                    '1' => ['min' => 1, 'max' => 1]
                ],
                'total_documents' => 2,
                'max_documents' => 3,
                'address_confirmation_required' => true,
                'additional_groups' => ['1', '2a', '2b'],
                'notes' => 'The combination of documents presented must confirm the applicant\'s name and date of birth. If this can\'t be achieved within 2 documents a third can be selected.'
            ],
            'route_2' => [
                'description' => 'Alternative route - requires 1 document from Group 2a and 2 further documents from either Group 2a or 2b',
                'required_groups' => [
                    '2a' => ['min' => 1, 'max' => 1]
                ],
                'total_documents' => 3,
                'address_confirmation_required' => true,
                'additional_groups' => ['2a', '2b'],
                'notes' => 'If the applicant doesn\'t have any documents in Group 1, they must be able to show: 1 document from Group 2a and 2 further documents from either Group 2a or 2b. The combination of documents presented must confirm the applicant\'s name and date of birth.'
            ],
            'route_3' => [
                'description' => 'Birth certificate route - requires birth certificate plus 2 additional documents',
                'required_groups' => [
                    '1' => ['min' => 1, 'max' => 1, 'specific' => ['birth_certificate_uk_12m', 'birth_certificate_uk_after_birth']]
                ],
                'total_documents' => 3,
                'address_confirmation_required' => true,
                'additional_groups' => ['2a', '2b'],
                'notes' => 'Special route for applicants with birth certificates.'
            ]
        ];
    }

    /**
     * Validate document data against field requirements
     */
    public static function validateDocumentData(string $documentKey, array $data): array
    {
        $document = self::getDocumentByKey($documentKey);
        
        if (!$document) {
            return ['document_key' => 'Invalid document type'];
        }
        
        $errors = [];
        $fields = $document['data_fields'] ?? [];
        
        foreach ($fields as $fieldName => $fieldConfig) {
            if ($fieldConfig['required'] && empty($data[$fieldName])) {
                $errors[$fieldName] = "Field {$fieldName} is required";
                continue;
            }
            
            if (!empty($data[$fieldName])) {
                $fieldErrors = self::validateFieldType($fieldName, $data[$fieldName], $fieldConfig);
                $errors = array_merge($errors, $fieldErrors);
            }
        }
        
        return $errors;
    }

    /**
     * Validate individual field type
     */
    private static function validateFieldType(string $fieldName, $value, array $config): array
    {
        $errors = [];
        
        switch ($config['type']) {
            case 'date':
                if (!self::isValidDate($value)) {
                    $errors[$fieldName] = "Invalid date format for {$fieldName}";
                }
                break;
            case 'string':
                if (!is_string($value)) {
                    $errors[$fieldName] = "Field {$fieldName} must be a string";
                }
                break;
        }
        
        return $errors;
    }

    /**
     * Check if date is valid
     */
    private static function isValidDate(string $date): bool
    {
        $d = \DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }

    /**
     * Get document statistics
     */
    public static function getDocumentStatistics(): array
    {
        $allDocuments = self::getAllDocuments();
        $groups = [];
        $products = [];
        
        foreach ($allDocuments as $document) {
            $group = $document['document_group'];
            $groups[$group] = ($groups[$group] ?? 0) + 1;
            
            foreach ($document['applicable_app_types'] as $product) {
                $products[$product] = ($products[$product] ?? 0) + 1;
            }
        }
        
        return [
            'total_documents' => count($allDocuments),
            'groups' => $groups,
            'products' => $products
        ];
    }
}
