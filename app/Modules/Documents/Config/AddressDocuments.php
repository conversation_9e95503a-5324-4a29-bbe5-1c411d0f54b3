<?php

declare(strict_types=1);

namespace App\Modules\Documents\Config;

/**
 * Financial and Social History Documents Configuration
 *
 * Documents that can be used to confirm address and financial/social history (Group 2b)
 */
class AddressDocuments
{
    /**
     * Get all financial and social history documents (Group 2b)
     */
    public static function getAll(): array
    {
        return [
            'mortgage_statement_uk' => [
                'name' => 'Mortgage statement',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'UK',
                'validity_period' => 'Issued in last 12 months',
                'data_fields' => [
                    'account_number' => ['type' => 'string', 'required' => true, 'label' => 'Account Number'],
                    'statement_date' => ['type' => 'date', 'required' => true, 'label' => 'Statement Date'],
                    'lender_name' => ['type' => 'string', 'required' => true, 'label' => 'Lender Name'],
                    'property_address' => ['type' => 'string', 'required' => true, 'label' => 'Property Address']
                ]
            ],
            'bank_statement_uk' => [
                'name' => 'Bank or building society statement',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK', 'JE', 'GG'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'UK and Channel Islands. A print off of a bank statement that is endorsed with a stamp and signed by the bank is acceptable if you do not have hard copy bank statements posted to you.',
                'validity_period' => 'Issued in last 3 months',
                'data_fields' => [
                    'account_number' => ['type' => 'string', 'required' => true, 'label' => 'Account Number'],
                    'statement_date' => ['type' => 'date', 'required' => true, 'label' => 'Statement Date'],
                    'bank_name' => ['type' => 'string', 'required' => true, 'label' => 'Bank Name'],
                    'account_holder_address' => ['type' => 'string', 'required' => true, 'label' => 'Account Holder Address']
                ]
            ],
            'bank_statement_non_uk' => [
                'name' => 'Bank or building society statement',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['ANY'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Countries outside the UK',
                'validity_period' => 'Issued in last 3 months - branch must be in the country where the applicant lives and works',
                'data_fields' => [
                    'account_number' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'account_number')) . ''],
                    'statement_date' => ['type' => 'date', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'statement_date')) . ''],
                    'bank_name' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'bank_name')) . ''],
                    'account_holder_address' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'account_holder_address')) . ''],
                    'country' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'country')) . ''],
                    'branch_location' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'branch_location')) . '']
                ]
            ],
            'bank_opening_letter_uk' => [
                'name' => 'Bank or building society account opening confirmation letter',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'UK',
                'validity_period' => 'Issued in last 3 months',
                'data_fields' => [
                    'account_number' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'account_number')) . ''],
                    'letter_date' => ['type' => 'date', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'letter_date')) . ''],
                    'bank_name' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'bank_name')) . ''],
                    'account_holder_address' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'account_holder_address')) . '']
                ]
            ],
            'credit_card_statement_uk' => [
                'name' => 'Credit card statement',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'UK',
                'validity_period' => 'Issued in last 3 months',
                'data_fields' => [
                    'card_number_last_four' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'card_number_last_four')) . ''],
                    'statement_date' => ['type' => 'date', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'statement_date')) . ''],
                    'card_provider' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'card_provider')) . ''],
                    'billing_address' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'billing_address')) . '']
                ]
            ],
            'financial_statement_uk' => [
                'name' => 'Financial statement, for example pension or endowment',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'UK',
                'validity_period' => 'Issued in last 12 months',
                'data_fields' => [
                    'account_number' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'account_number')) . ''],
                    'statement_date' => ['type' => 'date', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'statement_date')) . ''],
                    'provider_name' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'provider_name')) . ''],
                    'statement_type' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'statement_type')) . ''],
                    'account_holder_address' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'account_holder_address')) . '']
                ]
            ],
            'p45_p60' => [
                'name' => 'P45 or P60 statement',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK', 'JE', 'GG'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'UK and Channel Islands. Cannot be online document',
                'validity_period' => 'Issued in last 12 months',
                'data_fields' => [
                    'document_type' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'document_type')) . ''], // P45 or P60
                    'employee_number' => ['type' => 'string', 'required' => false, 'label' => '' . ucwords(str_replace('_', ' ', 'employee_number')) . ''],
                    'tax_year' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'tax_year')) . ''],
                    'employer_name' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'employer_name')) . ''],
                    'employee_address' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'employee_address')) . ''],
                    'leaving_date' => ['type' => 'date', 'required' => false, 'label' => '' . ucwords(str_replace('_', ' ', 'leaving_date')) . ''], // for P45
                    'total_pay' => ['type' => 'string', 'required' => false, 'label' => '' . ucwords(str_replace('_', ' ', 'total_pay')) . '']
                ]
            ],
            'council_tax_statement_uk' => [
                'name' => 'Council Tax statement',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK', 'JE', 'GG'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'UK and Channel Islands',
                'validity_period' => 'Issued in last 12 months',
                'data_fields' => [
                    'account_number' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'account_number')) . ''],
                    'statement_date' => ['type' => 'date', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'statement_date')) . ''],
                    'council_name' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'council_name')) . ''],
                    'property_address' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'property_address')) . ''],
                    'council_tax_band' => ['type' => 'string', 'required' => false, 'label' => '' . ucwords(str_replace('_', ' ', 'council_tax_band')) . '']
                ]
            ],
            'sponsorship_letter_non_uk' => [
                'name' => 'Letter of sponsorship from future UK employment provider',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['ANY'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Valid only for applicants residing outside of the UK at time of application',
                'validity_period' => 'Must still be valid',
                'data_fields' => [
                    'employer_name' => ['type' => 'string', 'required' => true, 'label' => 'Employer Name'],
                    'letter_date' => ['type' => 'date', 'required' => true, 'label' => 'Letter Date'],
                    'employer_address' => ['type' => 'string', 'required' => true, 'label' => 'Employer Address'],
                    'applicant_address' => ['type' => 'string', 'required' => true, 'label' => 'Applicant Address'],
                    'employment_details' => ['type' => 'string', 'required' => true, 'label' => 'Employment Details'],
                    'validity_date' => ['type' => 'date', 'required' => true, 'label' => 'Validity Date']
                ]
            ],
            'utility_bill' => [
                'name' => 'Utility bill',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'UK - not mobile telephone bill. Cannot be printed from an online account',
                'validity_period' => 'Issued in last 3 months',
                'data_fields' => [
                    'account_number' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'account_number')) . ''],
                    'bill_date' => ['type' => 'date', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'bill_date')) . ''],
                    'utility_provider' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'utility_provider')) . ''],
                    'service_address' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'service_address')) . ''],
                    'utility_type' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'utility_type')) . ''],
                    'is_hard_copy' => ['type' => 'boolean', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'is_hard_copy')) . ''] // cannot be printed from online
                ]
            ],
            'benefit_statement_uk' => [
                'name' => 'Benefit statement, for example Child Benefit, pension',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'UK',
                'validity_period' => 'Issued in last 3 months',
                'data_fields' => [
                    'benefit_type' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'benefit_type')) . ''],
                    'statement_date' => ['type' => 'date', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'statement_date')) . ''],
                    'issuing_authority' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'issuing_authority')) . ''],
                    'claimant_address' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'claimant_address')) . ''],
                    'reference_number' => ['type' => 'string', 'required' => false, 'label' => '' . ucwords(str_replace('_', ' ', 'reference_number')) . '']
                ]
            ],
            'government_correspondence_uk' => [
                'name' => 'Central or local government, government agency, or local council document giving entitlement',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK', 'JE', 'GG'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'UK and Channel Islands - a letter confirming entitlement to benefits. For example: Personal Independence Payment (PIP), free school meals, universal credit, asylum support etc',
                'validity_period' => 'Issued in last 12 months',
                'data_fields' => [
                    'document_type' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'document_type')) . ''],
                    'document_date' => ['type' => 'date', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'document_date')) . ''],
                    'issuing_authority' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'issuing_authority')) . ''],
                    'recipient_address' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'recipient_address')) . ''],
                    'entitlement_type' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'entitlement_type')) . ''],
                    'reference_number' => ['type' => 'string', 'required' => false, 'label' => '' . ucwords(str_replace('_', ' ', 'reference_number')) . '']
                ]
            ],
            'hmrc_self_assessment' => [
                'name' => 'HMRC self-assessment letters or tax demand letter',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'UK',
                'validity_period' => 'Issued in last 12 months',
                'data_fields' => [
                    'document_type' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'document_type')) . ''], // self-assessment or tax demand
                    'document_date' => ['type' => 'date', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'document_date')) . ''],
                    'tax_reference' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'tax_reference')) . ''],
                    'taxpayer_address' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'taxpayer_address')) . ''],
                    'tax_year' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'tax_year')) . '']
                ]
            ],
            'ehic_ghic' => [
                'name' => 'European Health Insurance Card (EHIC) or Global Health Insurance Card (GHIC)',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => false,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'UK. Must still be valid',
                'validity_period' => 'Must still be valid',
                'data_fields' => [
                    'card_type' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'card_type')) . ''], // EHIC or GHIC
                    'card_number' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'card_number')) . ''],
                    'expiry_date' => ['type' => 'date', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'expiry_date')) . ''],
                    'holder_name' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'holder_name')) . '']
                ]
            ],
            'eea_national_id' => [
                'name' => 'EEA National ID card',
                'document_group' => '2b',
                'requires_photo' => true,
                'confirms_address' => false,
                'applicable_countries' => ['ANY'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Must still be valid',
                'validity_period' => 'Must still be valid',
                'data_fields' => [
                    'card_number' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'card_number')) . ''],
                    'issue_country' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'issue_country')) . ''],
                    'issue_date' => ['type' => 'date', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'issue_date')) . ''],
                    'expiry_date' => ['type' => 'date', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'expiry_date')) . ''],
                    'issuing_authority' => ['type' => 'string', 'required' => false, 'label' => '' . ucwords(str_replace('_', ' ', 'issuing_authority')) . '']
                ]
            ],
            'irish_passport_card_2b' => [
                'name' => 'Irish Passport Card',
                'document_group' => '2b',
                'requires_photo' => true,
                'confirms_address' => false,
                'applicable_countries' => ['IE'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'Cannot be used with an Irish passport. Must still be valid',
                'validity_period' => 'Must still be valid',
                'data_fields' => [
                    'card_number' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'card_number')) . ''],
                    'issue_date' => ['type' => 'date', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'issue_date')) . ''],
                    'expiry_date' => ['type' => 'date', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'expiry_date')) . ''],
                    'issuing_authority' => ['type' => 'string', 'required' => false, 'label' => '' . ucwords(str_replace('_', ' ', 'issuing_authority')) . '']
                ]
            ],
            'pass_card' => [
                'name' => 'Cards carrying the PASS accreditation logo',
                'document_group' => '2b',
                'requires_photo' => true,
                'confirms_address' => false,
                'applicable_countries' => ['GB', 'UK', 'IM', 'JE', 'GG'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'UK, Isle of Man, and Channel Islands. Digital PASS cards are acceptable where they have been issued by an approved digital PASS provider and the QR code has been used to confirm details.',
                'validity_period' => 'Must still be valid',
                'data_fields' => [
                    'card_type' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'card_type')) . ''], // physical or digital
                    'card_number' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'card_number')) . ''],
                    'issue_date' => ['type' => 'date', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'issue_date')) . ''],
                    'expiry_date' => ['type' => 'date', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'expiry_date')) . ''],
                    'issuing_authority' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'issuing_authority')) . ''],
                    'qr_code_verified' => ['type' => 'boolean', 'required' => false, 'label' => '' . ucwords(str_replace('_', ' ', 'qr_code_verified')) . ''] // for digital cards
                ]
            ],
            'head_teacher_letter' => [
                'name' => 'Letter from head teacher, college principal, apprenticeship provider',
                'document_group' => '2b',
                'requires_photo' => false,
                'confirms_address' => true,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'],
                'notes' => 'UK - for 16 to 19 year olds in full time education or on an apprenticeship - only used in exceptional circumstances if other documents cannot be provided',
                'validity_period' => 'Issued in the last month',
                'data_fields' => [
                    'institution_type' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'institution_type')) . ''], // school, college, apprenticeship provider
                    'institution_name' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'institution_name')) . ''],
                    'signatory_name' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'signatory_name')) . ''],
                    'signatory_title' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'signatory_title')) . ''],
                    'letter_date' => ['type' => 'date', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'letter_date')) . ''],
                    'student_address' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'student_address')) . ''],
                    'student_age' => ['type' => 'integer', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'student_age')) . ''],
                    'education_type' => ['type' => 'string', 'required' => true, 'label' => '' . ucwords(str_replace('_', ' ', 'education_type')) . ''], // full time education or apprenticeship
                    'institution_address' => ['type' => 'string', 'required' => false, 'label' => '' . ucwords(str_replace('_', ' ', 'institution_address')) . '']
                ]
            ]
        ];
    }
}
