<?php

declare(strict_types=1);

namespace App\Modules\Documents\Config;

/**
 * Right to Work Documents Configuration
 * 
 * Documents specific to Right to Work (RTW) applications
 */
class RightToWorkDocuments
{
    /**
     * Get all Right to Work documents (Groups a, b1, b2)
     */
    public static function getAll(): array
    {
        return [
            // Group a - UK/IE Passports and Naturalisation
            'passport_uk_ie' => [
                'name' => 'Passport (UK, IE)',
                'document_group' => 'a',
                'requires_photo' => true,
                'confirms_address' => false,
                'applicable_countries' => ['GB', 'UK', 'IE'],
                'applicable_app_types' => ['RTW'],
                'data_fields' => [
                    'passport_number' => ['type' => 'string', 'required' => true, 'label' => 'Passport Number'],
                    'issue_country' => ['type' => 'string', 'required' => true, 'label' => 'Issue Country'],
                    'issue_date' => ['type' => 'date', 'required' => true, 'label' => 'Issue Date'],
                    'expiry_date' => ['type' => 'date', 'required' => true, 'label' => 'Expiry Date'],
                    'issuing_authority' => ['type' => 'string', 'required' => false, 'label' => 'Issuing Authority']
                ]
            ],
            'certificate_registration_naturalisation' => [
                'name' => 'Certificate of Registration or Naturalisation Document',
                'document_group' => 'a',
                'requires_photo' => false,
                'confirms_address' => false,
                'applicable_countries' => ['GB', 'UK'],
                'applicable_app_types' => ['RTW'],
                'data_fields' => [
                    'certificate_number' => ['type' => 'string', 'required' => true, 'label' => 'Certificate Number'],
                    'issue_date' => ['type' => 'date', 'required' => true, 'label' => 'Issue Date'],
                    'issuing_authority' => ['type' => 'string', 'required' => true, 'label' => 'Issuing Authority'],
                    'certificate_type' => ['type' => 'string', 'required' => true, 'label' => 'Certificate Type'],
                    'full_name_on_certificate' => ['type' => 'string', 'required' => true, 'label' => 'Full Name On Certificate']
                ]
            ],

            // Group b1 - Crown Dependencies
            'crown_dependency_document_b1' => [
                'name' => 'A verified document issued by the Bailiwick of Jersey, the Bailiwick of Guernsey or the Isle of Man',
                'document_group' => 'b1',
                'requires_photo' => false,
                'confirms_address' => false,
                'applicable_countries' => ['JE', 'GG', 'IM'],
                'applicable_app_types' => ['RTW'],
                'data_fields' => [
                    'document_type' => ['type' => 'string', 'required' => true],
                    'document_number' => ['type' => 'string', 'required' => true],
                    'issue_date' => ['type' => 'date', 'required' => true],
                    'issuing_authority' => ['type' => 'string', 'required' => true],
                    'verification_details' => ['type' => 'string', 'required' => false]
                ]
            ],

            // Group b2 - Application Certificates and Crown Dependencies
            'certificate_application' => [
                'name' => 'Certificate of Application',
                'document_group' => 'b2',
                'requires_photo' => false,
                'confirms_address' => false,
                'applicable_countries' => ['ANY'],
                'applicable_app_types' => ['RTW'],
                'data_fields' => [
                    'application_number' => ['type' => 'string', 'required' => true],
                    'application_date' => ['type' => 'date', 'required' => true],
                    'issuing_authority' => ['type' => 'string', 'required' => true],
                    'application_type' => ['type' => 'string', 'required' => true],
                    'status' => ['type' => 'string', 'required' => false]
                ]
            ],
            'crown_dependency_document_b2' => [
                'name' => 'A verified document issued by the Bailiwick of Jersey, the Bailiwick of Guernsey or the Isle of Man',
                'document_group' => 'b2',
                'requires_photo' => false,
                'confirms_address' => false,
                'applicable_countries' => ['JE', 'GG', 'IM'],
                'applicable_app_types' => ['RTW'],
                'data_fields' => [
                    'document_type' => ['type' => 'string', 'required' => true],
                    'document_number' => ['type' => 'string', 'required' => true],
                    'issue_date' => ['type' => 'date', 'required' => true],
                    'issuing_authority' => ['type' => 'string', 'required' => true],
                    'verification_details' => ['type' => 'string', 'required' => false]
                ]
            ]
        ];
    }
}
