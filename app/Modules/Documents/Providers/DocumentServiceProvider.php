<?php

declare(strict_types=1);

namespace App\Modules\Documents\Providers;

use Illuminate\Support\ServiceProvider;
use App\Modules\Documents\Services\DocumentProductMappingService;
use App\Modules\Documents\Services\DocumentRequirementService;
use App\Modules\Documents\Services\DocumentValidationService;

class DocumentServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register DocumentProductMappingService (no dependencies)
        $this->app->singleton(DocumentProductMappingService::class, function ($app) {
            return new DocumentProductMappingService();
        });

        // Register DocumentRequirementService (depends on DocumentProductMappingService)
        $this->app->singleton(DocumentRequirementService::class, function ($app) {
            return new DocumentRequirementService(
                $app->make(DocumentProductMappingService::class)
            );
        });

        // Register DocumentValidationService (depends on both services above)
        $this->app->singleton(DocumentValidationService::class, function ($app) {
            return new DocumentValidationService(
                $app->make(DocumentRequirementService::class),
                $app->make(DocumentProductMappingService::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
