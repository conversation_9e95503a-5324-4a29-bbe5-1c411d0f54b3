<?php

declare(strict_types=1);

namespace App\Modules\Documents\Services;

use App\Modules\Applications\Models\Application;

class DocumentValidationService
{
    private DocumentRequirementService $requirementService;
    private DocumentProductMappingService $mappingService;

    public function __construct(
        DocumentRequirementService $requirementService,
        DocumentProductMappingService $mappingService
    ) {
        $this->requirementService = $requirementService;
        $this->mappingService = $mappingService;
    }

    /**
     * Validate document nominations for a route
     */
    public function validateNominations(Application $application, array $nominations, int $routeNumber): array
    {
        $requirements = $this->requirementService->getRouteRequirements($application, $routeNumber);
        $requirements['route_number'] = $routeNumber; // Add route number to requirements

        return [
            'validation_result' => $this->validateOverallRequirements($nominations, $requirements),
            'route_analysis' => $this->analyzeRouteCompletion($nominations, $requirements),
            'document_validations' => $this->validateIndividualDocuments($nominations),
            'next_steps' => $this->determineNextSteps($nominations, $requirements)
        ];
    }

    /**
     * Validate overall route requirements
     */
    private function validateOverallRequirements(array $nominations, array $requirements): array
    {
        $groupCounts = $this->countDocumentsByGroup($nominations);
        $totalDocuments = count($nominations);
        $addressConfirmed = $this->hasAddressConfirmation($nominations);
        
        $isValid = true;
        $errors = [];
        
        // Check required groups
        foreach ($requirements['required_groups'] ?? [] as $group => $groupReq) {
            $provided = $groupCounts[$group] ?? 0;
            if ($provided < $groupReq['min']) {
                $isValid = false;
                $routeNumber = $requirements['route_number'] ?? '';
                $errors[] = "Route {$routeNumber} requires at least {$groupReq['min']} document(s) from Group {$group}";
            }
        }

        // Check total documents
        $requiredTotal = $requirements['total_documents'] ?? 0;
        if ($totalDocuments < $requiredTotal) {
            $isValid = false;
            $errors[] = "Total of {$requiredTotal} documents required, only {$totalDocuments} provided";
        }
        
        // Check address confirmation
        if (($requirements['address_confirmation_required'] ?? false) && !$addressConfirmed) {
            $isValid = false;
            $errors[] = "Address confirmation is required but not provided";
        }
        
        // Check birth certificate for Route 3
        if (($requirements['birth_certificate_required'] ?? false) && !$this->hasBirthCertificate($nominations)) {
            $isValid = false;
            $errors[] = "Birth certificate is required for Route 3";
        }
        
        return [
            'is_valid' => $isValid,
            'route_completed' => $isValid,
            'can_proceed' => $isValid,
            'errors' => $errors
        ];
    }

    /**
     * Analyze route completion status
     */
    private function analyzeRouteCompletion(array $nominations, array $requirements): array
    {
        $groupCounts = $this->countDocumentsByGroup($nominations);
        $totalDocuments = count($nominations);
        $addressConfirmed = $this->hasAddressConfirmation($nominations);
        
        $analysis = [
            'route_number' => $requirements['route_number'] ?? null,
            'requirements_met' => []
        ];
        
        // Analyze required groups
        foreach ($requirements['required_groups'] ?? [] as $group => $groupReq) {
            $provided = $groupCounts[$group] ?? 0;
            $analysis['requirements_met']["{$group}_documents"] = [
                'required' => $groupReq['min'],
                'provided' => $provided,
                'met' => $provided >= $groupReq['min']
            ];
        }
        
        // Analyze total documents
        $analysis['requirements_met']['total_documents'] = [
            'required' => $requirements['total_documents'] ?? 0,
            'provided' => $totalDocuments,
            'met' => $totalDocuments >= ($requirements['total_documents'] ?? 0)
        ];
        
        // Analyze address confirmation
        if ($requirements['address_confirmation_required'] ?? false) {
            $analysis['requirements_met']['address_confirmation'] = [
                'required' => true,
                'provided' => $addressConfirmed,
                'met' => $addressConfirmed
            ];
        }
        
        // Add group distribution
        $analysis['requirements_met']['group_distribution'] = $groupCounts;
        
        return $analysis;
    }

    /**
     * Validate individual documents
     */
    private function validateIndividualDocuments(array $nominations): array
    {
        $validations = [];
        
        foreach ($nominations as $nomination) {
            $documentKey = $nomination['document_type_key'] ?? null;
            $documentConfig = $documentKey ? $this->mappingService->getDocumentByKey($documentKey) : null;

            if (!$documentConfig) {
                $validations[] = [
                    'document_type_key' => $documentKey,
                    'document_name' => 'Unknown Document',
                    'is_valid' => false,
                    'errors' => ['Document type not found'],
                    'warnings' => []
                ];
                continue;
            }

            $errors = $this->mappingService->validateDocumentData($documentKey, $nomination['document_data'] ?? []);
            $warnings = $this->getDocumentWarnings($nomination, $documentConfig);

            $validations[] = [
                'document_type_key' => $documentKey,
                'document_name' => $documentConfig['name'],
                'is_valid' => empty($errors),
                'errors' => array_values($errors),
                'warnings' => $warnings
            ];
        }
        
        return $validations;
    }

    /**
     * Determine next steps based on validation
     */
    private function determineNextSteps(array $nominations, array $requirements): array
    {
        $validation = $this->validateOverallRequirements($nominations, $requirements);
        $routeNumber = $requirements['route_number'] ?? '';

        if ($validation['is_valid']) {
            return [
                'action' => 'complete_route',
                'message' => "All requirements met for Route {$routeNumber}. You can proceed with the application.",
                'alternative_actions' => []
            ];
        }

        return [
            'action' => 'add_documents',
            'message' => "Please add the required documents to complete Route {$routeNumber}.",
            'alternative_actions' => $this->getAlternativeActions($requirements)
        ];
    }

    /**
     * Count documents by group
     */
    private function countDocumentsByGroup(array $nominations): array
    {
        $counts = [];

        foreach ($nominations as $nomination) {
            $documentKey = $nomination['document_type_key'] ?? null;
            $documentConfig = $documentKey ? $this->mappingService->getDocumentByKey($documentKey) : null;

            if ($documentConfig) {
                $group = $documentConfig['document_group'];
                $counts[$group] = ($counts[$group] ?? 0) + 1;
            }
        }

        return $counts;
    }

    /**
     * Check if any document confirms address
     */
    private function hasAddressConfirmation(array $nominations): bool
    {
        foreach ($nominations as $nomination) {
            if ($nomination['confirms_address'] ?? false) {
                return true;
            }

            $documentKey = $nomination['document_type_key'] ?? null;
            $documentConfig = $documentKey ? $this->mappingService->getDocumentByKey($documentKey) : null;

            if ($documentConfig && ($documentConfig['confirms_address'] ?? false)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if birth certificate is present
     */
    private function hasBirthCertificate(array $nominations): bool
    {
        foreach ($nominations as $nomination) {
            $documentKey = $nomination['document_type_key'] ?? null;
            $documentConfig = $documentKey ? $this->mappingService->getDocumentByKey($documentKey) : null;

            if ($documentConfig && stripos($documentConfig['name'], 'birth certificate') !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get document warnings
     */
    private function getDocumentWarnings(array $nomination, array $documentConfig): array
    {
        $warnings = [];
        $data = $nomination['document_data'] ?? [];
        
        // Check expiry dates
        if (isset($data['expiry_date'])) {
            $expiryDate = \DateTime::createFromFormat('Y-m-d', $data['expiry_date']);
            if ($expiryDate && $expiryDate < new \DateTime()) {
                $warnings[] = 'Document has expired';
            }
        }
        
        // Check statement dates (should be recent)
        if (isset($data['statement_date'])) {
            $statementDate = \DateTime::createFromFormat('Y-m-d', $data['statement_date']);
            $threeMonthsAgo = new \DateTime('-3 months');
            if ($statementDate && $statementDate < $threeMonthsAgo) {
                $warnings[] = 'Statement is older than 3 months';
            }
        }
        
        return $warnings;
    }

    /**
     * Process final document submission
     */
    public function processSubmission(Application $application, int $routeNumber, array $nominations, $user): array
    {
        try {
            \Log::info('Document submission processing', [
                'application_id' => $application->id,
                'route_number' => $routeNumber,
                'nominations_count' => count($nominations),
                'nominations' => $nominations
            ]);

            // Convert document_type_id to document_type_key for validation
            $formattedNominations = [];
            foreach ($nominations as $nomination) {
                $documentTypeId = $nomination['document_type_id'];
                \Log::info('Converting document ID to key', [
                    'document_type_id' => $documentTypeId,
                    'type' => gettype($documentTypeId)
                ]);

                $documentConfig = $this->mappingService->getDocumentById($documentTypeId);

                \Log::info('Document config result', [
                    'document_type_id' => $documentTypeId,
                    'config_found' => $documentConfig !== null,
                    'config' => $documentConfig ? ['key' => $documentConfig['key'], 'name' => $documentConfig['name']] : null
                ]);

                if ($documentConfig) {
                    $formattedNominations[] = [
                        'document_type_key' => $documentConfig['key'],
                        'document_data' => $nomination['document_data'],
                        'confirms_address' => $nomination['confirms_address'] ?? false
                    ];
                }
            }

            \Log::info('Formatted nominations for validation', [
                'formatted_count' => count($formattedNominations),
                'formatted_nominations' => $formattedNominations
            ]);

            // First validate the nominations
            $validationResult = $this->validateNominations($application, $formattedNominations, $routeNumber);

            if (!$validationResult['validation_result']['is_valid']) {
                return [
                    'success' => false,
                    'message' => 'Document validation failed',
                    'validation_result' => $validationResult['validation_result'],
                    'route_analysis' => $validationResult['route_analysis'],
                    'document_validations' => $validationResult['document_validations'],
                    'next_steps' => $validationResult['next_steps']
                ];
            }

            // Save the nominations to database
            $this->saveNominations($application, $routeNumber, $nominations, $user);

            // Create process stamp for document nomination completion
            $this->createProcessStamp($application, 'DOCUMENTS_NOMINATED');

            return [
                'success' => true,
                'message' => 'Documents submitted successfully',
                'validation_result' => [
                    'is_valid' => true,
                    'route_completed' => true,
                    'can_proceed' => true
                ],
                'route_analysis' => $validationResult['route_analysis'],
                'document_validations' => $validationResult['document_validations'],
                'next_steps' => [
                    'action' => 'proceed',
                    'message' => 'Document nominations submitted successfully! You can now proceed to upload your documents.'
                ]
            ];

        } catch (\Exception $e) {
            \Log::error('Document submission failed', [
                'application_id' => $application->id,
                'route_number' => $routeNumber,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Document submission failed: ' . $e->getMessage(),
                'validation_result' => [
                    'is_valid' => false,
                    'route_completed' => false,
                    'can_proceed' => false
                ]
            ];
        }
    }

    /**
     * Save nominations to database
     */
    private function saveNominations(Application $application, int $routeNumber, array $nominations, $user): void
    {
        // Clear existing nominations for this application and route
        \App\Modules\Documents\Models\DocumentNomination::where('application_id', $application->id)
            ->where('route_number', $routeNumber)
            ->delete();

        // Save new nominations
        foreach ($nominations as $nomination) {
            $documentTypeId = $nomination['document_type_id'];
            $documentConfig = $this->mappingService->getDocumentById($documentTypeId);

            if ($documentConfig) {
                \App\Modules\Documents\Models\DocumentNomination::create([
                    'application_id' => $application->id,
                    'document_type_key' => $documentConfig['key'],
                    'document_name' => $documentConfig['name'],
                    'document_group' => $documentConfig['document_group'],
                    'route_number' => $routeNumber,
                    'key_values' => $nomination['document_data'],
                    'confirms_address' => $nomination['confirms_address'] ?? false,
                    'status' => 'nominated',
                    'nominated_by' => $user->id
                ]);
            }
        }
    }

    /**
     * Create process stamp for application tracking
     */
    private function createProcessStamp(Application $application, string $stampType): void
    {
        try {
            // Use the ProcessStampService to complete the stamp
            $stampService = app(\App\Services\ProcessStampService::class);

            // Complete the DOCUMENTS_NOMINATED stamp
            $success = $stampService->completeStamp(
                $application->id,
                $stampType,
                1, // System user ID
                [
                    'timestamp' => now()->toISOString(),
                    'action' => 'document_nomination_completed'
                ]
            );

            if ($success) {
                \Log::info('Process stamp created successfully', [
                    'application_id' => $application->id,
                    'stamp_type' => $stampType
                ]);
            } else {
                \Log::warning('Failed to create process stamp', [
                    'application_id' => $application->id,
                    'stamp_type' => $stampType
                ]);
            }
        } catch (\Exception $e) {
            \Log::error('Error creating process stamp', [
                'application_id' => $application->id,
                'stamp_type' => $stampType,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get alternative actions
     */
    private function getAlternativeActions(array $requirements): array
    {
        $actions = [];
        
        $routeNumber = $requirements['route_number'] ?? 1;
        
        if ($routeNumber === 1) {
            $actions[] = [
                'action' => 'try_route_2',
                'message' => 'If you cannot provide Group 1 documents, try Route 2'
            ];
        } elseif ($routeNumber === 2) {
            $actions[] = [
                'action' => 'try_route_3',
                'message' => 'If you cannot complete Route 2, try Route 3 as last resort'
            ];
        }
        
        return $actions;
    }
}
