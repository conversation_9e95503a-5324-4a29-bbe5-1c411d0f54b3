<?php

declare(strict_types=1);

namespace App\Modules\Documents\Services;

use App\Modules\Documents\Config\DocumentRegistry;

class DocumentProductMappingService
{
    /**
     * Get all document types from config
     */
    public function getAllDocumentTypes(): array
    {
        return DocumentRegistry::getAllDocuments();
    }

    /**
     * Get document types applicable for a specific product
     */
    public function getDocumentsForProduct(string $productCode): array
    {
        return DocumentRegistry::getDocumentsForProduct($productCode);
    }

    /**
     * Get document types grouped by document group for a product
     */
    public function getGroupedDocumentsForProduct(string $productCode, ?string $nationality = null): array
    {
        return DocumentRegistry::getGroupedDocumentsForProduct($productCode, $nationality);
    }

    /**
     * Check if a document type is applicable for a product
     */
    public function isDocumentApplicableForProduct(string $documentKey, string $productCode): bool
    {
        return DocumentRegistry::isDocumentApplicableForProduct($documentKey, $productCode);
    }

    /**
     * Get all product codes that a document type is applicable for
     */
    public function getApplicableProductsForDocument(string $documentKey): array
    {
        $document = DocumentRegistry::getDocumentByKey($documentKey);
        return $document['applicable_app_types'] ?? [];
    }

    /**
     * Get document by key
     */
    public function getDocumentByKey(string $documentKey): ?array
    {
        return DocumentRegistry::getDocumentByKey($documentKey);
    }

    /**
     * Get document by ID (converts ID to key first)
     */
    public function getDocumentById($documentId): ?array
    {
        // Ensure we have a valid integer
        if (!is_numeric($documentId)) {
            return null;
        }

        $documentId = (int) $documentId;

        // Convert document ID back to key
        $documentKey = $this->convertIdToKey($documentId);
        if (!$documentKey) {
            return null;
        }

        $documentConfig = $this->getDocumentByKey($documentKey);
        if ($documentConfig) {
            // Add the key to the config since it's not included by default
            $documentConfig['key'] = $documentKey;
        }

        return $documentConfig;
    }

    /**
     * Convert document ID to document key
     * This reverses the process used in the frontend to generate IDs from keys
     */
    private function convertIdToKey(int $documentId): ?string
    {
        $allDocuments = $this->getAllDocumentTypes();

        foreach ($allDocuments as $key => $document) {
            // Generate the same hash that the frontend uses
            $generatedId = $this->generateDocumentId($key);

            if ($generatedId === $documentId) {
                return $key;
            }
        }

        return null;
    }

    /**
     * Generate document ID from key (same logic as frontend)
     * Uses djb2 hash algorithm to match Flutter implementation
     */
    private function generateDocumentId(string $documentKey): int
    {
        // Create a stable hash from the key string using djb2 algorithm
        // This matches the Flutter implementation exactly
        $hash = 5381;
        $length = strlen($documentKey);

        for ($i = 0; $i < $length; $i++) {
            $charCode = ord($documentKey[$i]);
            $hash = (($hash << 5) + $hash + $charCode) & 0xffffffff;
        }

        // Ensure we return a positive integer
        return abs((int)$hash);
    }

    /**
     * Get allowed document groups for a product
     */
    public function getAllowedGroupsForProduct(string $productCode): array
    {
        return DocumentRegistry::getAllowedGroupsForProduct($productCode);
    }

    /**
     * Validate if a document nomination is valid for the product
     */
    public function validateDocumentForProduct(string $documentKey, string $productCode, ?string $nationality = null): array
    {
        $errors = [];

        if (!DocumentRegistry::isDocumentApplicableForProduct($documentKey, $productCode)) {
            $errors[] = "Document type is not applicable for product {$productCode}";
        }

        if ($nationality && !DocumentRegistry::isDocumentApplicableForNationality($documentKey, $nationality)) {
            $errors[] = "Document type is not applicable for nationality {$nationality}";
        }

        return [
            'is_valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Get document data fields for validation
     */
    public function getDocumentDataFields(string $documentKey): array
    {
        $document = DocumentRegistry::getDocumentByKey($documentKey);
        return $document['data_fields'] ?? [];
    }

    /**
     * Validate document data against field requirements
     */
    public function validateDocumentData(string $documentKey, array $data): array
    {
        return DocumentRegistry::validateDocumentData($documentKey, $data);
    }
}
