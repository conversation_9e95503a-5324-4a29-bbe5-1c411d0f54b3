<?php

declare(strict_types=1);

namespace App\Modules\Documents\Services;

use App\Modules\Applications\Models\Application;
use App\Modules\Documents\Config\DocumentRegistry;

class DocumentRequirementService
{
    protected DocumentProductMappingService $mappingService;

    public function __construct(DocumentProductMappingService $mappingService)
    {
        $this->mappingService = $mappingService;
    }
    /**
     * Get applicant context from application data
     */
    public function getApplicantContext(Application $application): array
    {
        $applicantData = $application->getApplicationDataArray();

        $nationality = $this->extractNationality($applicantData);
        $currentCountry = $this->extractCountryFromAddress($applicantData);

        return [
            'nationality' => $nationality,
            'current_address_country' => $currentCountry,
            'is_uk_national' => $this->isUKNational($nationality),
            'is_uk_resident' => $this->isUKResident($currentCountry),
            'work_type' => $this->determineWorkType($application),
            'product_code' => $application->product->code
        ];
    }

    /**
     * Determine recommended route based on applicant context
     */
    public function determineRecommendedRoute(Application $application): int
    {
        // Always try Route 1 first (DBS requirement)
        if ($this->canAttemptRoute($application, 1)) {
            return 1;
        }

        // Fallback to Route 2
        if ($this->canAttemptRoute($application, 2)) {
            return 2;
        }
        
        // Last resort Route 3
        return 3;
    }

    /**
     * Get available routes for application
     */
    public function getAvailableRoutes(Application $application): array
    {
        $routes = [];
        
        if ($this->canAttemptRoute($application, 1)) {
            $routes[] = 1;
        }
        if ($this->canAttemptRoute($application, 2)) {
            $routes[] = 2;
        }
        // Route 3 is always available as last resort
        $routes[] = 3;
        
        return array_unique($routes);
    }

    /**
     * Get route requirements
     */
    public function getRouteRequirements(Application $application, int $routeNumber): array
    {
        $context = $this->getApplicantContext($application);
        $routeRequirements = DocumentRegistry::getRouteRequirements();

        $routeKey = "route_{$routeNumber}";
        if (!isset($routeRequirements[$routeKey])) {
            throw new \InvalidArgumentException('Invalid route number');
        }

        $requirements = $routeRequirements[$routeKey];

        // Modify requirements based on context
        if ($routeNumber === 1 && !$context['is_uk_national'] && $context['work_type'] === 'paid') {
            $requirements['right_to_work_required'] = true;
            $requirements['required_groups']['1a'] = ['min' => 1, 'max' => 1];
        }

        return $requirements;
    }

    /**
     * Get available documents for applicant
     */
    public function getAvailableDocuments(Application $application): array
    {
        $context = $this->getApplicantContext($application);

        return $this->mappingService->getDocumentsForProduct($context['product_code']);
    }

    /**
     * Get documents grouped by document group
     */
    public function getGroupedDocuments(Application $application): array
    {
        $context = $this->getApplicantContext($application);

        return $this->mappingService->getGroupedDocumentsForProduct(
            $context['product_code'],
            $context['nationality']
        );
    }

    /**
     * Check if applicant can attempt a specific route
     */
    public function canAttemptRoute(Application $application, int $routeNumber): bool
    {
        $context = $this->getApplicantContext($application);
        $availableDocuments = $this->getGroupedDocuments($application);

        switch ($routeNumber) {
            case 1:
                return $this->hasGroup1Documents($availableDocuments, $context);
            case 2:
                return $this->hasGroup2aDocuments($availableDocuments, $context);
            default:
                return false;
        }
    }

    private function hasGroup1Documents(array $groupedDocuments, array $context): bool
    {
        if (!isset($groupedDocuments['1'])) {
            return false;
        }

        foreach ($groupedDocuments['1'] as $document) {
            if ($this->isDocumentApplicableForContext($document, $context)) {
                return true;
            }
        }

        return false;
    }

    private function hasGroup2aDocuments(array $groupedDocuments, array $context): bool
    {
        if (!isset($groupedDocuments['2a'])) {
            return false;
        }

        $applicableCount = 0;
        foreach ($groupedDocuments['2a'] as $document) {
            if ($this->isDocumentApplicableForContext($document, $context)) {
                $applicableCount++;
            }
        }

        return $applicableCount >= 1;
    }

    private function isDocumentApplicableForContext(array $document, array $context): bool
    {
        $countries = $document['applicable_countries'] ?? [];

        if (in_array('ANY', $countries)) {
            return true;
        }

        $nationality = strtoupper($context['nationality']);
        $currentCountry = strtoupper($context['current_address_country']);

        $ukNationalities = ['BRITISH', 'UK', 'UNITED KINGDOM', 'GB'];
        $ukCountries = ['UK', 'UNITED KINGDOM', 'GB', 'BRITAIN'];

        if (in_array('GB', $countries) || in_array('UK', $countries)) {
            return in_array($nationality, $ukNationalities) || in_array($currentCountry, $ukCountries);
        }

        return in_array($nationality, $countries) || in_array($currentCountry, $countries);
    }

    private function extractNationality(array $applicantData): string
    {
        return $applicantData['AdditionalApplicantDetails::BIRTH_NATIONALITY'] ??
               $applicantData['BirthNationality'] ??
               $applicantData['PersonalDetails.Nationality'] ??
               'Unknown';
    }

    private function extractCountryFromAddress(array $applicantData): string
    {
        return $applicantData['CurrentAddress::COUNTRY_CODE'] ??
               $applicantData['AddressDetails.CurrentAddress.Country'] ??
               'Unknown';
    }

    private function isUKNational(string $nationality): bool
    {
        return in_array(strtolower($nationality), ['british', 'uk', 'united kingdom', 'gb']);
    }

    private function isUKResident(string $country): bool
    {
        return in_array(strtolower($country), ['uk', 'united kingdom', 'gb', 'britain']);
    }

    private function determineWorkType(Application $application): string
    {
        // This would be determined based on application data or product type
        // For now, assume paid work unless specified otherwise
        return 'paid';
    }
}
