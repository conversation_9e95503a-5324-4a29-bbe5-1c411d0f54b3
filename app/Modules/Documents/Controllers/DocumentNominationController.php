<?php

declare(strict_types=1);

namespace App\Modules\Documents\Controllers;

use App\Core\BaseApiController;
use App\Modules\Applications\Models\Application;
use App\Modules\Applications\Services\ApplicationAccessService;
use App\Models\DocumentNomination;
use App\Modules\Documents\Models\DocDafData;
use App\Modules\Documents\Models\DocumentFile;
use App\Modules\Documents\Services\DocumentRequirementService;
use App\Modules\Documents\Services\DocumentValidationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;

/**
 * Document Nomination Controller
 * 
 * Handles document nomination for DBS applications
 */
class DocumentNominationController extends BaseApiController
{
    private DocumentRequirementService $requirementService;
    private DocumentValidationService $validationService;
    private ApplicationAccessService $accessService;

    public function __construct(
        DocumentRequirementService $requirementService,
        DocumentValidationService $validationService,
        ApplicationAccessService $accessService
    ) {
        $this->requirementService = $requirementService;
        $this->validationService = $validationService;
        $this->accessService = $accessService;
    }

    /**
     * Get available documents and route information for an application
     * 
     * @param int $id Application ID
     * @return JsonResponse
     */
    public function getDocuments(Request $request, int $id): JsonResponse
    {
        \Log::info("DocumentNominationController: getDocuments called for application $id");

        try {
            $user = $request->user();
            $userIp = $request->ip();
            $userAgent = $request->userAgent();

            // Rate limiting for security
            $rateLimitKey = "documents_access:{$user->id}:{$userIp}";
            if (RateLimiter::tooManyAttempts($rateLimitKey, 60)) {
                Log::warning('Rate limit exceeded for documents access', [
                    'user_id' => $user->id,
                    'user_type' => $user->user_type,
                    'ip' => $userIp,
                    'application_id' => $id
                ]);
                return $this->sendError('Too many requests. Please try again later.', [], 429);
            }
            RateLimiter::hit($rateLimitKey, 60);

            // Enhanced user type validation
            $allowedUserTypes = ['applicant', 'client_user', 'requester', 'doc_checker'];
            if (!in_array($user->user_type, $allowedUserTypes)) {
                Log::warning('Unauthorized access attempt to documents endpoint', [
                    'user_id' => $user->id,
                    'user_type' => $user->user_type,
                    'ip' => $userIp,
                    'application_id' => $id
                ]);
                return $this->sendForbidden('Access denied for this user type');
            }

            // Enhanced application ID validation
            if ($id <= 0 || $id > 2147483647) { // Max int value
                Log::warning('Invalid application ID provided', [
                    'user_id' => $user->id,
                    'application_id' => $id,
                    'ip' => $userIp
                ]);
                return $this->sendError('Invalid application ID', [], 400);
            }

            // Enhanced access control with detailed logging
            if (!$this->accessService->canUserAccessApplication($user, $id)) {
                Log::warning('Unauthorized application access attempt', [
                    'user_id' => $user->id,
                    'user_type' => $user->user_type,
                    'application_id' => $id,
                    'ip' => $userIp,
                    'user_agent' => $userAgent
                ]);
                return $this->sendError('Application not found', [], 404);
            }

            // Load application with necessary relationships
            $application = Application::with(['product', 'applicant.profile'])->find($id);

            if (!$application) {
                Log::warning('Application not found', [
                    'user_id' => $user->id,
                    'application_id' => $id,
                    'ip' => $userIp
                ]);
                return $this->sendError('Application not found', [], 404);
            }

            // Log successful access for technical audit
            Log::info('Documents list accessed', [
                'user_id' => $user->id,
                'application_id' => $id,
                'applicant_id' => $application->applicant_id,
                'product_code' => $application->product->code,
                'user_type' => $user->user_type,
                'ip_address' => $userIp
            ]);

            // Get applicant context
            $context = $this->requirementService->getApplicantContext($application);

            // Determine routes
            $recommendedRoute = $this->requirementService->determineRecommendedRoute($application);
            $availableRoutes = $this->requirementService->getAvailableRoutes($application);

            // Get route requirements with enhanced formatting
            $routeRequirements = [];
            foreach ($availableRoutes as $routeNumber) {
                $requirements = $this->requirementService->getRouteRequirements($application, $routeNumber);
                $routeRequirements["route_{$routeNumber}"] = $this->formatRouteRequirements($requirements);
            }

            // Get available documents grouped by group with enhanced metadata
            $availableDocuments = $this->formatAvailableDocuments(
                $this->requirementService->getGroupedDocuments($application)
            );

            // Get current nominations with enhanced formatting
            $currentNominations = $this->formatCurrentNominations(
                DocumentNomination::where('application_id', $id)->get()
            );

            // Enhanced response structure
            $responseData = [
                'application' => [
                    'id' => $application->id,
                    'product_code' => $application->product->code,
                    'product_name' => $application->product->name,
                    'applicant_id' => $application->applicant_id,
                    'applicant_name' => $application->applicant->profile->full_name ?? 'Unknown',
                    'status' => $application->status
                ],
                'applicant_context' => $this->formatApplicantContext($context),
                'routing' => [
                    'recommended_route' => $recommendedRoute,
                    'available_routes' => $availableRoutes,
                    'route_requirements' => $routeRequirements
                ],
                'documents' => [
                    'available_by_group' => $availableDocuments,
                    'total_available' => $this->countTotalDocuments($availableDocuments),
                    'current_nominations' => $currentNominations,
                    'nomination_summary' => $this->getNominationSummary($currentNominations)
                ],
                'metadata' => [
                    'retrieved_at' => now()->toISOString(),
                    'user_type' => $user->user_type,
                    'config_version' => '2.0'
                ]
            ];

            return $this->sendResponse($responseData, 'Document information retrieved successfully');

        } catch (\Exception $e) {
            Log::error('Failed to retrieve document information', [
                'user_id' => $user->id ?? null,
                'application_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->sendError('Failed to retrieve document information', ['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Format route requirements for enhanced response
     */
    private function formatRouteRequirements(array $requirements): array
    {
        return [
            'description' => $requirements['description'] ?? '',
            'total_documents_required' => $requirements['total_documents'] ?? 0,
            'address_confirmation_required' => $requirements['address_confirmation_required'] ?? false,
            'right_to_work_required' => $requirements['right_to_work_required'] ?? false,
            'required_groups' => $requirements['required_groups'] ?? [],
            'additional_groups' => $requirements['additional_groups'] ?? []
        ];
    }

    /**
     * Format available documents with enhanced metadata
     */
    private function formatAvailableDocuments(array $groupedDocuments): array
    {
        // Remove duplicate biometric_residence_permit from Group 1
        // This document should not exist - only biometric_residence_permit_uk should be used
        if (isset($groupedDocuments['1']['biometric_residence_permit'])) {
            unset($groupedDocuments['1']['biometric_residence_permit']);
        }

        $formatted = [];

        foreach ($groupedDocuments as $group => $documents) {
            $formatted[$group] = [
                'group_name' => $this->getGroupDisplayName((string) $group),
                'document_count' => count($documents),
                'documents' => array_map(function($document, $key) {
                    return [
                        'key' => $key,
                        'name' => $document['name'],
                        'requires_photo' => $document['requires_photo'] ?? false,
                        'confirms_address' => $document['confirms_address'] ?? false,
                        'applicable_countries' => $document['applicable_countries'] ?? [],
                        'data_fields' => $this->formatDataFields($document['data_fields'] ?? [])
                    ];
                }, $documents, array_keys($documents))
            ];
        }

        return $formatted;
    }

    /**
     * Format current nominations
     */
    private function formatCurrentNominations($nominations): array
    {
        return $nominations->map(function ($nomination) {
            // Generate document_type_id from document_type_key using the same hash algorithm as Flutter
            $documentTypeId = $this->generateDocumentTypeId($nomination->document_type_key);

            return [
                'id' => $nomination->id,
                'document_type_key' => $nomination->document_type_key,
                'document_type_id' => $documentTypeId,
                'document_name' => $nomination->document_name,
                'document_group' => $nomination->document_group,
                'route_number' => $nomination->route_number,
                'confirms_address' => $nomination->confirms_address,
                'status' => $nomination->status,
                'document_data' => $nomination->key_values ?? [],
                'created_at' => $nomination->created_at?->toISOString(),
                'updated_at' => $nomination->updated_at?->toISOString()
            ];
        })->toArray();
    }

    /**
     * Generate document type ID from key using the same algorithm as Flutter
     */
    private function generateDocumentTypeId(string $key): int
    {
        // DJB2 hash algorithm - same as used in Flutter DocumentType.id getter
        $hash = 5381;
        for ($i = 0; $i < strlen($key); $i++) {
            $hash = (($hash << 5) + $hash + ord($key[$i])) & 0xffffffff;
        }
        // Ensure we always return a positive integer
        return abs($hash);
    }

    /**
     * Format applicant context
     */
    private function formatApplicantContext(array $context): array
    {
        return [
            'nationality' => $context['nationality'] ?? 'Unknown',
            'current_address_country' => $context['current_address_country'] ?? 'Unknown',
            'is_uk_national' => $context['is_uk_national'] ?? false,
            'is_uk_resident' => $context['is_uk_resident'] ?? false,
            'work_type' => $context['work_type'] ?? 'unknown',
            'product_code' => $context['product_code'] ?? 'unknown'
        ];
    }

    /**
     * Count total available documents
     */
    private function countTotalDocuments(array $groupedDocuments): int
    {
        $total = 0;
        foreach ($groupedDocuments as $group) {
            $total += $group['document_count'] ?? 0;
        }
        return $total;
    }

    /**
     * Get nomination summary
     */
    private function getNominationSummary(array $nominations): array
    {
        $summary = [
            'total_nominations' => count($nominations),
            'by_status' => [],
            'by_group' => [],
            'by_route' => []
        ];

        foreach ($nominations as $nomination) {
            // Count by status
            $status = $nomination['status'] ?? 'unknown';
            $summary['by_status'][$status] = ($summary['by_status'][$status] ?? 0) + 1;

            // Count by group
            $group = $nomination['document_group'] ?? 'unknown';
            $summary['by_group'][$group] = ($summary['by_group'][$group] ?? 0) + 1;

            // Count by route
            $route = $nomination['route_number'] ?? 'unknown';
            $summary['by_route'][$route] = ($summary['by_route'][$route] ?? 0) + 1;
        }

        return $summary;
    }

    /**
     * Get display name for document group
     */
    private function getGroupDisplayName(string|int $group): string
    {
        $groupKey = (string) $group;

        $groupNames = [
            '1' => 'Primary Identity Documents',
            '1a' => 'Right to Work Documents',
            '2a' => 'Secondary Identity Documents',
            '2b' => 'Address Confirmation Documents',
            'a' => 'UK/IE Passports and Naturalisation',
            'b1' => 'Crown Dependencies (Route B1)',
            'b2' => 'Crown Dependencies (Route B2)'
        ];

        return $groupNames[$groupKey] ?? "Group {$groupKey}";
    }

    /**
     * Format data fields for response
     */
    private function formatDataFields(array $fields): array
    {
        $formatted = [];

        foreach ($fields as $fieldName => $fieldConfig) {
            $formatted[] = [
                'name' => $fieldName,
                'type' => $fieldConfig['type'] ?? 'string',
                'required' => $fieldConfig['required'] ?? false,
                'label' => $this->generateFieldLabel($fieldName)
            ];
        }

        return $formatted;
    }

    /**
     * Generate human-readable field label
     */
    private function generateFieldLabel(string $fieldName): string
    {
        return ucwords(str_replace('_', ' ', $fieldName));
    }

    /**
     * Validate document nominations
     * 
     * @param Request $request
     * @param int $id Application ID
     * @return JsonResponse
     */
    public function validateDocuments(Request $request, int $id): JsonResponse
    {
        try {
            $user = $request->user();
            $userIp = $request->ip();
            $userAgent = $request->userAgent();

            // Rate limiting for document validation
            $rateLimitKey = "document_validation:{$user->id}:{$userIp}";
            if (RateLimiter::tooManyAttempts($rateLimitKey, 30)) {
                Log::warning('Rate limit exceeded for document validation', [
                    'user_id' => $user->id,
                    'user_type' => $user->user_type,
                    'ip' => $userIp,
                    'application_id' => $id
                ]);
                return $this->sendError('Too many validation requests. Please try again later.', [], 429);
            }
            RateLimiter::hit($rateLimitKey, 60);

            // Enhanced user type validation
            $allowedUserTypes = ['applicant', 'client_user', 'requester', 'doc_checker'];
            if (!in_array($user->user_type, $allowedUserTypes)) {
                Log::warning('Unauthorized access attempt to document validation', [
                    'user_id' => $user->id,
                    'user_type' => $user->user_type,
                    'ip' => $userIp,
                    'application_id' => $id
                ]);
                return $this->sendForbidden('Access denied for this user type');
            }

            // Enhanced application ID validation
            if ($id <= 0 || $id > 2147483647) {
                Log::warning('Invalid application ID for validation', [
                    'user_id' => $user->id,
                    'application_id' => $id,
                    'ip' => $userIp
                ]);
                return $this->sendError('Invalid application ID', [], 400);
            }

            // Enhanced access control
            if (!$this->accessService->canUserAccessApplication($user, $id)) {
                Log::warning('Unauthorized document validation attempt', [
                    'user_id' => $user->id,
                    'user_type' => $user->user_type,
                    'application_id' => $id,
                    'ip' => $userIp,
                    'user_agent' => $userAgent
                ]);
                return $this->sendError('Application not found', [], 404);
            }

            $application = Application::find($id);

            if (!$application) {
                return $this->sendError('Application not found', [], 404);
            }

            // Validate request data with more lenient rules for empty documents
            $validatedData = $request->validate([
                'route_number' => 'required|integer|min:1|max:3',
                'nominated_documents' => 'required|array|min:1',
                'nominated_documents.*.document_type_id' => 'required|integer|min:0',
                'nominated_documents.*.document_data' => 'array',
                'nominated_documents.*.confirms_address' => 'boolean'
            ]);

            $routeNumber = $validatedData['route_number'];
            $nominations = $validatedData['nominated_documents'];

            // Filter out empty documents (document_type_id = 0 or empty document_data)
            $validNominations = array_filter($nominations, function($nomination) {
                return $nomination['document_type_id'] > 0 &&
                       !empty($nomination['document_data']) &&
                       is_array($nomination['document_data']);
            });

            // Convert document_type_id to document_type_key for validation
            $formattedNominations = [];
            foreach ($validNominations as $nomination) {
                $documentTypeId = $nomination['document_type_id'];
                $documentConfig = app(\App\Modules\Documents\Services\DocumentProductMappingService::class)
                    ->getDocumentById($documentTypeId);

                if ($documentConfig) {
                    $formattedNominations[] = [
                        'document_type_key' => $documentConfig['key'],
                        'document_data' => $nomination['document_data'],
                        'confirms_address' => $nomination['confirms_address'] ?? false
                    ];
                }
            }

            // Validate the formatted nominations
            $validationResult = $this->validationService->validateNominations($application, $formattedNominations, $routeNumber);

            // For validation endpoint, we only return validation results without saving
            return $this->sendResponse($validationResult,
                $validationResult['validation_result']['is_valid']
                    ? 'Documents validated successfully'
                    : 'Document validation failed'
            );

        } catch (ValidationException $e) {
            return $this->sendError('Validation failed', $e->errors(), 422);
        } catch (\Exception $e) {
            return $this->sendError('Failed to validate documents', ['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Submit document nominations (final submission)
     *
     * @param Request $request
     * @param int $id Application ID
     * @return JsonResponse
     */
    public function submitDocuments(Request $request, int $id): JsonResponse
    {
        // Add immediate logging to see if we reach this method
        Log::info('submitDocuments method called', [
            'application_id' => $id,
            'method' => $request->method(),
            'url' => $request->url(),
            'user_id' => $request->user()?->id ?? 'no user'
        ]);

        try {
            $user = $request->user();
            $userIp = $request->ip();
            $userAgent = $request->userAgent();

            // Rate limiting for document submission
            $rateLimitKey = "document_submission:{$user->id}:{$userIp}";
            if (RateLimiter::tooManyAttempts($rateLimitKey, 10)) {
                Log::warning('Rate limit exceeded for document submission', [
                    'user_id' => $user->id,
                    'user_type' => $user->user_type,
                    'ip' => $userIp,
                    'application_id' => $id
                ]);
                return $this->sendError('Too many submission requests. Please try again later.', [], 429);
            }

            RateLimiter::hit($rateLimitKey, 60);

            // Enhanced access control
            if (!$this->accessService->canUserAccessApplication($user, $id)) {
                Log::warning('Unauthorized document submission attempt', [
                    'user_id' => $user->id,
                    'user_type' => $user->user_type,
                    'application_id' => $id,
                    'ip' => $userIp,
                    'user_agent' => $userAgent
                ]);
                return $this->sendError('Application not found', [], 404);
            }

            // Load application
            $application = Application::with(['product', 'applicant.profile'])->find($id);
            if (!$application) {
                return $this->sendError('Application not found', [], 404);
            }

            // Validate request data with more lenient rules for empty documents
            $validatedData = $request->validate([
                'route_number' => 'required|integer|min:1|max:3',
                'nominated_documents' => 'required|array|min:1',
                'nominated_documents.*.document_type_id' => 'required|integer|min:0',
                'nominated_documents.*.document_data' => 'array',
                'nominated_documents.*.confirms_address' => 'boolean'
            ]);

            Log::info('Document submission initiated', [
                'user_id' => $user->id,
                'application_id' => $id,
                'route_number' => $validatedData['route_number'],
                'document_count' => count($validatedData['nominated_documents']),
                'ip' => $userIp
            ]);

            $routeNumber = $validatedData['route_number'];
            $nominations = $validatedData['nominated_documents'];

            // Filter out empty documents (document_type_id = 0 or empty document_data)
            $validNominations = array_filter($nominations, function($nomination) {
                return $nomination['document_type_id'] > 0 &&
                       !empty($nomination['document_data']) &&
                       is_array($nomination['document_data']);
            });

            // Convert document_type_id to document_type_key for validation
            $formattedNominations = [];
            foreach ($validNominations as $nomination) {
                $documentTypeId = $nomination['document_type_id'];
                $documentConfig = app(\App\Modules\Documents\Services\DocumentProductMappingService::class)
                    ->getDocumentById($documentTypeId);

                if ($documentConfig) {
                    $formattedNominations[] = [
                        'document_type_key' => $documentConfig['key'],
                        'document_data' => $nomination['document_data'],
                        'confirms_address' => $nomination['confirms_address'] ?? false
                    ];
                }
            }

            // Validate the formatted nominations
            $validationResult = $this->validationService->validateNominations($application, $formattedNominations, $routeNumber);

            // If validation passes, save nominations
            if ($validationResult['validation_result']['is_valid']) {
                // Save nominations to database (only valid ones)
                $this->saveNominations($application, $routeNumber, $validNominations, $user);

                // Create process stamp
                $this->createProcessStamp($application, 'DOCUMENTS_NOMINATED');

                Log::info('Document submission completed successfully', [
                    'user_id' => $user->id,
                    'application_id' => $id,
                    'route_number' => $routeNumber
                ]);

                return $this->sendResponse([
                    'success' => true,
                    'data' => $validationResult
                ], 'Documents submitted successfully');
            } else {
                Log::info('Document validation failed', [
                    'user_id' => $user->id,
                    'application_id' => $id,
                    'route_number' => $routeNumber,
                    'errors' => $validationResult['validation_result']['errors'] ?? []
                ]);

                return $this->sendResponse([
                    'success' => false,
                    'data' => $validationResult
                ], 'Document validation failed');
            }

        } catch (ValidationException $e) {
            return $this->sendError('Validation failed', $e->errors(), 422);
        } catch (\Exception $e) {
            Log::error('Document submission error', [
                'user_id' => $user->id ?? null,
                'application_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->sendError('Failed to submit documents', ['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Save nominations to database
     */
    private function saveNominations(Application $application, int $routeNumber, array $nominations, $user): void
    {
        // Clear existing nominations for this application and route
        \App\Modules\Documents\Models\DocumentNomination::where('application_id', $application->id)
            ->where('route_number', $routeNumber)
            ->delete();

        // Save new nominations
        foreach ($nominations as $nomination) {
            $documentTypeId = $nomination['document_type_id'];
            $documentConfig = app(\App\Modules\Documents\Services\DocumentProductMappingService::class)
                ->getDocumentById($documentTypeId);

            if ($documentConfig) {
                \App\Modules\Documents\Models\DocumentNomination::create([
                    'application_id' => $application->id,
                    'document_type_key' => $documentConfig['key'],
                    'document_name' => $documentConfig['name'],
                    'document_group' => $documentConfig['document_group'],
                    'route_number' => $routeNumber,
                    'key_values' => $nomination['document_data'],
                    'confirms_address' => $nomination['confirms_address'] ?? false,
                    'status' => 'nominated',
                    'nominated_by' => $user->id
                ]);
            }
        }
    }

    /**
     * Create process stamp for application tracking
     */
    private function createProcessStamp(Application $application, string $stampType): void
    {
        try {
            // Use the ProcessStampService to complete the stamp
            $stampService = app(\App\Services\ProcessStampService::class);

            // Complete the DOCUMENTS_NOMINATED stamp
            $success = $stampService->completeStamp(
                $application->id,
                $stampType,
                1, // System user ID
                [
                    'timestamp' => now()->toISOString(),
                    'action' => 'document_nomination_completed'
                ]
            );

            if ($success) {
                Log::info('Process stamp created successfully', [
                    'application_id' => $application->id,
                    'stamp_type' => $stampType
                ]);
            } else {
                Log::warning('Failed to create process stamp', [
                    'application_id' => $application->id,
                    'stamp_type' => $stampType
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error creating process stamp', [
                'application_id' => $application->id,
                'stamp_type' => $stampType,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Upload a file for a document nomination
     */
    public function uploadFile(Request $request, int $applicationId, int $nominationId): JsonResponse
    {
        try {
            // Validate the request
            $request->validate([
                'file' => 'required|file|mimes:pdf,png,jpg,jpeg|max:10240', // 10MB max
            ]);

            // Get the application and verify access
            $application = Application::findOrFail($applicationId);
            $user = $request->user();

            // Verify user has access to this application
            if (!$this->accessService->canUserAccessApplication($user, $application)) {
                return $this->sendError('Access denied', [], 403);
            }

            // Find the nomination
            $nomination = DocDafData::where('application_id', $applicationId)
                ->where('id', $nominationId)
                ->firstOrFail();

            /** @var UploadedFile $file */
            $file = $request->file('file');

            // Generate unique filename and UUID
            $originalName = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();
            $storedName = time() . '_' . uniqid() . '.' . $extension;
            $fileUuid = \Illuminate\Support\Str::uuid();
            $s3Key = "documents/{$applicationId}/nominations/{$nominationId}/{$storedName}";

            // Upload to S3
            $uploaded = Storage::disk('s3')->putFileAs(
                "documents/{$applicationId}/nominations/{$nominationId}",
                $file,
                $storedName,
                'private'
            );

            if (!$uploaded) {
                return $this->sendError('Failed to upload file to storage', [], 500);
            }

            // Calculate file hash
            $fileHash = hash_file('sha256', $file->getRealPath());

            // Calculate retention date (7 years from now for DBS documents)
            $retentionDate = now()->addYears(7)->toDateString();

            // Save file record to database
            $documentFile = DocumentFile::create([
                'application_id' => $applicationId,
                'doc_daf_data_id' => $nominationId,
                'original_filename' => $originalName,
                'stored_filename' => $storedName,
                'file_uuid' => $fileUuid,
                's3_key' => $s3Key,
                's3_bucket' => config('filesystems.disks.s3.bucket'),
                's3_region' => config('filesystems.disks.s3.region'),
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'file_hash' => $fileHash,
                'file_extension' => $extension,
                'uploaded_by' => $user->id,
                'uploaded_at' => now(),
                'upload_ip' => $request->ip(),
                'retention_until' => $retentionDate,
                'status' => 'active',
            ]);

            Log::info('Document file uploaded successfully', [
                'application_id' => $applicationId,
                'nomination_id' => $nominationId,
                'file_id' => $documentFile->id,
                'original_filename' => $originalName,
                's3_key' => $s3Key,
            ]);

            return $this->sendResponse([
                'file_id' => $documentFile->id,
                'original_filename' => $originalName,
                'file_size' => $documentFile->getFormattedFileSize(),
                'upload_status' => 'success',
                'message' => 'File uploaded successfully'
            ], 'Document file uploaded successfully');

        } catch (ValidationException $e) {
            return $this->sendError('Validation failed', $e->errors(), 422);
        } catch (\Exception $e) {
            Log::error('Failed to upload document file', [
                'application_id' => $applicationId,
                'nomination_id' => $nominationId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->sendError('Failed to upload document file', [
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
