<?php

declare(strict_types=1);

namespace App\Modules\Documents\Controllers;

use App\Core\BaseApiController;
use App\Modules\Applications\Models\Application;
use App\Modules\Applications\Services\ApplicationAccessService;
use App\Models\DocumentNomination;
use App\Modules\Documents\Models\DocDafData;
use App\Modules\Documents\Models\DocumentFile;
use App\Modules\Documents\Services\DocumentRequirementService;
use App\Modules\Documents\Services\DocumentValidationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;

/**
 * Document Nomination Controller
 * 
 * Handles document nomination for DBS applications
 */
class DocumentNominationController extends BaseApiController
{
    private DocumentRequirementService $requirementService;
    private DocumentValidationService $validationService;
    private ApplicationAccessService $accessService;

    public function __construct(
        DocumentRequirementService $requirementService,
        DocumentValidationService $validationService,
        ApplicationAccessService $accessService
    ) {
        $this->requirementService = $requirementService;
        $this->validationService = $validationService;
        $this->accessService = $accessService;
    }

    /**
     * Get available documents and route information for an application
     * 
     * @param int $id Application ID
     * @return JsonResponse
     */
    public function getDocuments(Request $request, int $id): JsonResponse
    {
        \Log::info("DocumentNominationController: getDocuments called for application $id");

        try {
            $user = $request->user();
            $userIp = $request->ip();
            $userAgent = $request->userAgent();

            // Rate limiting for security
            $rateLimitKey = "documents_access:{$user->id}:{$userIp}";
            if (RateLimiter::tooManyAttempts($rateLimitKey, 60)) {
                Log::warning('Rate limit exceeded for documents access', [
                ]);
                return $this->sendError('Too many requests. Please try again later.', [], 429);
            }
            RateLimiter::hit($rateLimitKey, 60);

            // Enhanced user type validation
            $allowedUserTypes = ['applicant', 'client_user', 'requester', 'doc_checker'];
            if (!in_array($user->user_type, $allowedUserTypes)) {
                Log::warning('Unauthorized access attempt to documents endpoint', [
                ]);
                return $this->sendForbidden('Access denied for this user type');
            }

            // Enhanced application ID validation
            if ($id <= 0 || $id > 2147483647) { // Max int value
                Log::warning('Invalid application ID provided', [
                ]);
                return $this->sendError('Invalid application ID', [], 400);
            }

            // Enhanced access control with detailed logging
            if (!$this->accessService->canUserAccessApplication($user, $id)) {
                Log::warning('Unauthorized application access attempt', [
                ]);
                return $this->sendError('Application not found', [], 404);
            }

            // Load application with necessary relationships
            $application = Application::with(['product', 'applicant.profile'])->find($id);

            if (!$application) {
                Log::warning('Application not found', [
                ]);
                return $this->sendError('Application not found', [], 404);
            }

            // Log successful access for technical audit
            Log::info('Documents list accessed', [
            ]);

            // Get applicant context
            $context = $this->requirementService->getApplicantContext($application);

            // Determine routes
            $recommendedRoute = $this->requirementService->determineRecommendedRoute($application);
            $availableRoutes = $this->requirementService->getAvailableRoutes($application);

            // Get route requirements with enhanced formatting
            $routeRequirements = [];
            foreach ($availableRoutes as $routeNumber) {
                $requirements = $this->requirementService->getRouteRequirements($application, $routeNumber);
                $routeRequirements["route_{$routeNumber}"] = $this->formatRouteRequirements($requirements);
            }

            // Get available documents grouped by group with enhanced metadata
            $availableDocuments = $this->formatAvailableDocuments(
                $this->requirementService->getGroupedDocuments($application)
            );

            // Get current nominations with enhanced formatting
            $currentNominations = $this->formatCurrentNominations(
                DocumentNomination::where('application_id', $id)->get()
            );

            // Enhanced response structure
            $responseData = [
                'applicant_context' => $this->formatApplicantContext($context),
                'recommended_route' => $recommendedRoute,
                'available_routes' => $availableRoutes,
                'route_requirements' => $routeRequirements,
                'available_documents' => $availableDocuments,
                'current_nominations' => $currentNominations,
                'total_documents' => $this->countTotalDocuments($availableDocuments),
                'nomination_summary' => $this->getNominationSummary($currentNominations)
            ];

            return $this->sendResponse($responseData, 'Document information retrieved successfully');

        } catch (\Exception $e) {
            Log::error('Failed to retrieve document information', [
            ]);
            return $this->sendError('Failed to retrieve document information', ['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Format route requirements for enhanced response
     */
    private function formatRouteRequirements(array $requirements): array
    {
        return [
        ];
    }

    /**
     * Format available documents with enhanced metadata
     */
    private function formatAvailableDocuments(array $groupedDocuments): array
    {
        // Remove duplicate biometric_residence_permit from Group 1
        // This document should not exist - only biometric_residence_permit_uk should be used
        if (isset($groupedDocuments['1']['biometric_residence_permit'])) {
            unset($groupedDocuments['1']['biometric_residence_permit']);
        }

        $formatted = [];

        foreach ($groupedDocuments as $group => $documents) {
            $formatted[$group] = [
                    return [
                    ];
                }, $documents, array_keys($documents))
            ];
        }

        return $formatted;
    }

    /**
     * Format current nominations
     */
    private function formatCurrentNominations($nominations): array
    {
        return $nominations->map(function ($nomination) {
            // Generate document_type_id from document_type_key using the same hash algorithm as Flutter
            $documentTypeId = $this->generateDocumentTypeId($nomination->document_type_key);

            return [
            ];
        })->toArray();
    }

    /**
     * Generate document type ID from key using the same algorithm as Flutter
     */
    private function generateDocumentTypeId(string $key): int
    {
        // DJB2 hash algorithm - same as used in Flutter DocumentType.id getter
        $hash = 5381;
        for ($i = 0; $i < strlen($key); $i++) {
            $hash = (($hash << 5) + $hash + ord($key[$i])) & 0xffffffff;
        }
        // Ensure we always return a positive integer
        return abs($hash);
    }

    /**
     * Format applicant context
     */
    private function formatApplicantContext(array $context): array
    {
        return [
        ];
    }

    /**
     * Count total available documents
     */
    private function countTotalDocuments(array $groupedDocuments): int
    {
        $total = 0;
        foreach ($groupedDocuments as $group) {
            $total += $group['document_count'] ?? 0;
        }
        return $total;
    }

    /**
     * Get nomination summary
     */
    private function getNominationSummary(array $nominations): array
    {
        $summary = [
        ];

        foreach ($nominations as $nomination) {
            // Count by status
            $status = $nomination['status'] ?? 'unknown';
            $summary['by_status'][$status] = ($summary['by_status'][$status] ?? 0) + 1;

            // Count by group
            $group = $nomination['document_group'] ?? 'unknown';
            $summary['by_group'][$group] = ($summary['by_group'][$group] ?? 0) + 1;

            // Count by route
            $route = $nomination['route_number'] ?? 'unknown';
            $summary['by_route'][$route] = ($summary['by_route'][$route] ?? 0) + 1;
        }

        return $summary;
    }

    /**
     * Get display name for document group
     */
    private function getGroupDisplayName(string|int $group): string
    {
        $groupKey = (string) $group;

        $groupNames = [
        ];

        return $groupNames[$groupKey] ?? "Group {$groupKey}";
    }

    /**
     * Format data fields for response
     */
    private function formatDataFields(array $fields): array
    {
        $formatted = [];

        foreach ($fields as $fieldName => $fieldConfig) {
            $formatted[] = [
            ];
        }

        return $formatted;
    }

    /**
     * Generate human-readable field label
     */
    private function generateFieldLabel(string $fieldName): string
    {
        return ucwords(str_replace('_', ' ', $fieldName));
    }

    /**
     * Validate document nominations
     * 
     * @param Request $request
     * @param int $id Application ID
     * @return JsonResponse
     */
    public function validateDocuments(Request $request, int $id): JsonResponse
    {
        try {
            $user = $request->user();
            $userIp = $request->ip();
            $userAgent = $request->userAgent();

            // Rate limiting for document validation
            $rateLimitKey = "document_validation:{$user->id}:{$userIp}";
            if (RateLimiter::tooManyAttempts($rateLimitKey, 30)) {
                Log::warning('Rate limit exceeded for document validation', [
                ]);
                return $this->sendError('Too many validation requests. Please try again later.', [], 429);
            }
            RateLimiter::hit($rateLimitKey, 60);

            // Enhanced user type validation
            $allowedUserTypes = ['applicant', 'client_user', 'requester', 'doc_checker'];
            if (!in_array($user->user_type, $allowedUserTypes)) {
                Log::warning('Unauthorized access attempt to document validation', [
                ]);
                return $this->sendForbidden('Access denied for this user type');
            }

            // Enhanced application ID validation
            if ($id <= 0 || $id > 2147483647) {
                Log::warning('Invalid application ID for validation', [
                ]);
                return $this->sendError('Invalid application ID', [], 400);
            }

            // Enhanced access control
            if (!$this->accessService->canUserAccessApplication($user, $id)) {
                Log::warning('Unauthorized document validation attempt', [
                ]);
                return $this->sendError('Application not found', [], 404);
            }

            $application = Application::find($id);

            if (!$application) {
                return $this->sendError('Application not found', [], 404);
            }

            // Validate request data with more lenient rules for empty documents
            $validatedData = $request->validate([
            ]);

            $routeNumber = $validatedData['route_number'];
            $nominations = $validatedData['nominated_documents'];

            // Filter out empty documents (document_type_id = 0 or empty document_data)
            $validNominations = array_filter($nominations, function($nomination) {
                return $nomination['document_type_id'] > 0 &&
                       !empty($nomination['document_data']) &&
                       is_array($nomination['document_data']);
            });

            // Convert document_type_id to document_type_key for validation
            $formattedNominations = [];
            foreach ($validNominations as $nomination) {
                $documentTypeId = $nomination['document_type_id'];
                $documentConfig = app(\App\Modules\Documents\Services\DocumentProductMappingService::class)
                    ->getDocumentById($documentTypeId);

                if ($documentConfig) {
                    $formattedNominations[] = [
                    ];
                }
            }

            // Validate the formatted nominations
            $validationResult = $this->validationService->validateNominations($application, $formattedNominations, $routeNumber);

            // For validation endpoint, we only return validation results without saving
            return $this->sendResponse($validationResult,
                $validationResult['validation_result']['is_valid']
                    ? 'Documents validated successfully'
                    : 'Document validation failed'
            );

        } catch (ValidationException $e) {
            return $this->sendError('Validation failed', $e->errors(), 422);
        } catch (\Exception $e) {
            return $this->sendError('Failed to validate documents', ['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Submit document nominations (final submission)
     *
     * @param Request $request
     * @param int $id Application ID
     * @return JsonResponse
     */
    public function submitDocuments(Request $request, int $id): JsonResponse
    {
        // Add immediate logging to see if we reach this method
        Log::info('submitDocuments method called', [
        ]);

        try {
            $user = $request->user();
            $userIp = $request->ip();
            $userAgent = $request->userAgent();

            // Rate limiting for document submission
            $rateLimitKey = "document_submission:{$user->id}:{$userIp}";
            if (RateLimiter::tooManyAttempts($rateLimitKey, 10)) {
                Log::warning('Rate limit exceeded for document submission', [
                ]);
                return $this->sendError('Too many submission requests. Please try again later.', [], 429);
            }

            RateLimiter::hit($rateLimitKey, 60);

            // Enhanced access control
            if (!$this->accessService->canUserAccessApplication($user, $id)) {
                Log::warning('Unauthorized document submission attempt', [
                ]);
                return $this->sendError('Application not found', [], 404);
            }

            // Load application
            $application = Application::with(['product', 'applicant.profile'])->find($id);
            if (!$application) {
                return $this->sendError('Application not found', [], 404);
            }

            // Validate request data with more lenient rules for empty documents
            $validatedData = $request->validate([
            ]);

            Log::info('Document submission initiated', [
            ]);

            $routeNumber = $validatedData['route_number'];
            $nominations = $validatedData['nominated_documents'];

            // Filter out empty documents (document_type_id = 0 or empty document_data)
            $validNominations = array_filter($nominations, function($nomination) {
                return $nomination['document_type_id'] > 0 &&
                       !empty($nomination['document_data']) &&
                       is_array($nomination['document_data']);
            });

            // Convert document_type_id to document_type_key for validation
            $formattedNominations = [];
            foreach ($validNominations as $nomination) {
                $documentTypeId = $nomination['document_type_id'];
                $documentConfig = app(\App\Modules\Documents\Services\DocumentProductMappingService::class)
                    ->getDocumentById($documentTypeId);

                if ($documentConfig) {
                    $formattedNominations[] = [
                    ];
                }
            }

            // Validate the formatted nominations
            $validationResult = $this->validationService->validateNominations($application, $formattedNominations, $routeNumber);

            // If validation passes, save nominations
            if ($validationResult['validation_result']['is_valid']) {
                // Save nominations to database (only valid ones) and get the created IDs
                $createdNominations = $this->saveNominations($application, $routeNumber, $validNominations, $user);

                // Create process stamp
                $this->createProcessStamp($application, 'DOCUMENTS_NOMINATED');

                Log::info('Document submission completed successfully', [
                ]);

                // Add nomination IDs to the response
                $validationResult['created_nominations'] = $createdNominations;

                return $this->sendResponse([
                ], 'Documents submitted successfully');
            } else {
                Log::info('Document validation failed', [
                ]);

                return $this->sendResponse([
                ], 'Document validation failed');
            }

        } catch (ValidationException $e) {
            return $this->sendError('Validation failed', $e->errors(), 422);
        } catch (\Exception $e) {
            Log::error('Document submission error', [
            ]);
            return $this->sendError('Failed to submit documents', ['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Save nominations to database and return created nomination IDs
     */
    private function saveNominations(Application $application, int $routeNumber, array $nominations, $user): array
    {
        // Clear existing nominations for this application and route
        \App\Modules\Documents\Models\DocumentNomination::where('application_id', $application->id)
            ->where('route_number', $routeNumber)
            ->delete();

        $createdNominations = [];

        // Save new nominations
        foreach ($nominations as $nomination) {
            $documentTypeId = $nomination['document_type_id'];
            $documentConfig = app(\App\Modules\Documents\Services\DocumentProductMappingService::class)
                ->getDocumentById($documentTypeId);

            if ($documentConfig) {
                $createdNomination = \App\Modules\Documents\Models\DocumentNomination::create([
                ]);

                // Store the mapping of document_type_id to nomination database ID
                $createdNominations[] = [
                ];

                Log::info('[NOMINATION] Created nomination', [
                ]);
            }
        }

        return $createdNominations;
    }

    /**
     * Create process stamp for application tracking
     */
    private function createProcessStamp(Application $application, string $stampType): void
    {
        try {
            // Use the ProcessStampService to complete the stamp
            $stampService = app(\App\Services\ProcessStampService::class);

            // Complete the DOCUMENTS_NOMINATED stamp
            $success = $stampService->completeStamp(
                $application->id,
                $stampType,
                1, // System user ID
                [
                ]
            );

            if ($success) {
                Log::info('Process stamp created successfully', [
                ]);
            } else {
                Log::warning('Failed to create process stamp', [
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error creating process stamp', [
            ]);
        }
    }

    /**
     * Upload a file for a document nomination
     */
    public function uploadFile(Request $request, int $applicationId, int $nominationId): JsonResponse
    {
        try {
            // Validate the request
            $request->validate([
                'file' => 'required|file|mimes:pdf,png,jpg,jpeg|max:10240', // 10MB max
            ]);

            // Get the application and verify access
            $application = Application::findOrFail($applicationId);
            $user = $request->user();

            // Verify user has access to this application
            if (!$this->accessService->canUserAccessApplication($user, $application)) {
                return $this->sendError('Access denied', [], 403);
            }

            // Find the nomination
            $nomination = DocDafData::where('application_id', $applicationId)
                ->where('id', $nominationId)
                ->firstOrFail();

            /** @var UploadedFile $file */
            $file = $request->file('file');

            // Generate unique filename and UUID
            $originalName = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();
            $storedName = time() . '_' . uniqid() . '.' . $extension;
            $fileUuid = \Illuminate\Support\Str::uuid();
            $s3Key = "documents/{$applicationId}/nominations/{$nominationId}/{$storedName}";

            // Upload to S3
            $uploaded = Storage::disk('secure_documents')->putFileAs(
                "documents/{$applicationId}/nominations/{$nominationId}",
                $file,
                $storedName,
                'private'
            );

            if (!$uploaded) {
                return $this->sendError('Failed to upload file to storage', [], 500);
            }

            // Calculate file hash
            $fileHash = hash_file('sha256', $file->getRealPath());

            // Calculate retention date (7 years from now for DBS documents)
            $retentionDate = now()->addYears(7)->toDateString();

            // Save file record to database
            try {
                $documentFile = DocumentFile::create([
                    'application_id' => $applicationId,
                    'doc_daf_data_id' => $nominationId,
                    'original_filename' => $originalName,
                    'stored_filename' => $storedName,
                    'file_uuid' => $fileUuid,
                    's3_key' => $s3Key,
                    's3_bucket' => config('filesystems.disks.secure_documents.bucket'),
                    's3_region' => config('filesystems.disks.secure_documents.region'),
                    'file_size' => $file->getSize(),
                    'mime_type' => $file->getMimeType(),
                    'file_hash' => $fileHash,
                    'file_extension' => $extension,
                    'uploaded_by' => $user->id,
                    'uploaded_at' => now(),
                    'upload_ip' => $request->ip(),
                    'retention_until' => $retentionDate,
                    'status' => 'active',
                ]);
            } catch (\Exception $dbError) {
                throw $dbError;
            }

            return $this->sendResponse([
                'file_id' => $documentFile->id,
                'original_filename' => $originalName,
                'file_size' => $file->getSize(),
                'upload_date' => $documentFile->uploaded_at->toISOString(),
                's3_key' => $s3Key
            ], 'Document file uploaded successfully');

        } catch (ValidationException $e) {
            return $this->sendError('Validation failed', $e->errors(), 422);
        } catch (\Exception $e) {
            return $this->sendError('Failed to upload document file', [
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
