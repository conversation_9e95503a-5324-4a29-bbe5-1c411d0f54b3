<?php

declare(strict_types=1);

namespace App\Modules\Documents\Controllers;

use App\Core\BaseApiController;
use App\Modules\Applications\Models\Application;
use App\Modules\Applications\Services\ApplicationAccessService;
use App\Modules\Documents\Models\DocumentNomination;
use App\Services\SecureDocumentStorageService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * Document Upload Controller
 * 
 * Handles secure document file uploads to S3
 */
class DocumentUploadController extends BaseApiController
{
    private SecureDocumentStorageService $storageService;
    private ApplicationAccessService $accessService;

    public function __construct(
        SecureDocumentStorageService $storageService,
        ApplicationAccessService $accessService
    ) {
        $this->storageService = $storageService;
        $this->accessService = $accessService;
    }

    /**
     * Upload document file for a nomination
     */
    public function uploadDocumentFile(Request $request, int $applicationId, int $nominationId): JsonResponse
    {
        try {
            $user = $request->user();

            // Validate request
            $validator = Validator::make($request->all(), [
                'file' => 'required|file|mimes:pdf,png,jpg,jpeg|max:10240', // 10MB max
            ]);

            if ($validator->fails()) {
                return $this->sendError('Validation failed', $validator->errors(), 422);
            }

            // Verify application access
            if (!$this->accessService->canUserAccessApplication($user, $applicationId)) {
                Log::warning('❌ Application access denied', [
                    'user_id' => $user->id,
                    'application_id' => $applicationId
                ]);
                return $this->sendError('Application not found or access denied', [], 404);
            }

            // Get the application
            $application = \App\Modules\Applications\Models\Application::find($applicationId);
            if (!$application) {
                Log::warning('❌ Application not found', [
                    'application_id' => $applicationId
                ]);
                return $this->sendError('Application not found', [], 404);
            }



            // Find the document nomination
            $nomination = DocumentNomination::where('id', $nominationId)
                ->where('application_id', $applicationId)
                ->first();

            if (!$nomination) {
                Log::warning('❌ Document nomination not found', [
                    'nomination_id' => $nominationId,
                    'application_id' => $applicationId
                ]);
                return $this->sendError('Document nomination not found', [], 404);
            }

            // Upload file to S3
            $file = $request->file('file');

            $documentFile = $this->storageService->storeDocumentFile($file, $nomination, $user->id);



            return $this->sendResponse([
                'file_id' => $documentFile->id,
                'filename' => $documentFile->original_filename,
                'file_size' => $documentFile->file_size,
                'upload_date' => $documentFile->created_at->toISOString(),
                's3_key' => $documentFile->s3_key,
                's3_bucket' => $documentFile->s3_bucket
            ], 'Document file uploaded successfully');

        } catch (\Exception $e) {
            Log::error('💥 Document upload error', [
                'user_id' => $user->id ?? null,
                'application_id' => $applicationId,
                'nomination_id' => $nominationId,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->sendError('Failed to upload document file', ['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Get uploaded files for a nomination
     */
    public function getDocumentFiles(Request $request, int $applicationId, int $nominationId): JsonResponse
    {
        try {
            $user = $request->user();

            // Verify application access
            $application = $this->accessService->getApplicationForUser($user, $applicationId);
            if (!$application) {
                return $this->sendError('Application not found or access denied', [], 404);
            }

            // Find the document nomination
            $nomination = DocumentNomination::where('id', $nominationId)
                ->where('application_id', $applicationId)
                ->with('documentFiles')
                ->first();

            if (!$nomination) {
                return $this->sendError('Document nomination not found', [], 404);
            }

            $files = $nomination->documentFiles->map(function ($file) {
                return [
                    'id' => $file->id,
                    'filename' => $file->original_filename,
                    'file_size' => $file->file_size,
                    'mime_type' => $file->mime_type,
                    'upload_date' => $file->created_at->toISOString(),
                    'uploaded_by' => $file->uploadedBy->name ?? 'Unknown'
                ];
            });

            return $this->sendResponse([
                'files' => $files,
                'total_files' => $files->count()
            ], 'Document files retrieved successfully');

        } catch (\Exception $e) {
            Log::error('Get document files error', [
                'user_id' => $user->id ?? null,
                'application_id' => $applicationId,
                'nomination_id' => $nominationId,
                'error' => $e->getMessage()
            ]);
            return $this->sendError('Failed to retrieve document files', ['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Delete a document file
     */
    public function deleteDocumentFile(Request $request, int $applicationId, int $nominationId, int $fileId): JsonResponse
    {
        try {
            $user = $request->user();

            // Verify application access
            $application = $this->accessService->getApplicationForUser($user, $applicationId);
            if (!$application) {
                return $this->sendError('Application not found or access denied', [], 404);
            }

            // Find the document nomination
            $nomination = DocumentNomination::where('id', $nominationId)
                ->where('application_id', $applicationId)
                ->first();

            if (!$nomination) {
                return $this->sendError('Document nomination not found', [], 404);
            }

            // Delete the file
            $success = $this->storageService->deleteDocumentFile($fileId, $user->id);

            if ($success) {


                return $this->sendResponse([], 'Document file deleted successfully');
            } else {
                return $this->sendError('Failed to delete document file', [], 500);
            }

        } catch (\Exception $e) {
            Log::error('Delete document file error', [
                'user_id' => $user->id ?? null,
                'application_id' => $applicationId,
                'nomination_id' => $nominationId,
                'file_id' => $fileId,
                'error' => $e->getMessage()
            ]);
            return $this->sendError('Failed to delete document file', ['error' => $e->getMessage()], 500);
        }
    }
}
