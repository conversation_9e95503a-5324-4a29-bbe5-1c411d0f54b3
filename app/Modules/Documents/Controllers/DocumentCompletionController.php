<?php

declare(strict_types=1);

namespace App\Modules\Documents\Controllers;

use App\Core\BaseApiController;
use App\Models\DocumentNomination;
use App\Models\DocumentFile;
use App\Modules\Applications\Models\Application;
use App\Modules\Applications\Services\ApplicationAccessService;
use App\Modules\Documents\Services\DocumentValidationService;
use App\Services\SecureDocumentStorageService;
use App\Services\ProcessStampService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class DocumentCompletionController extends BaseApiController
{
    private DocumentValidationService $validationService;
    private ApplicationAccessService $accessService;
    private SecureDocumentStorageService $storageService;
    private ProcessStampService $processStampService;

    public function __construct(
        DocumentValidationService $validationService,
        ApplicationAccessService $accessService,
        SecureDocumentStorageService $storageService,
        ProcessStampService $processStampService
    ) {
        $this->validationService = $validationService;
        $this->accessService = $accessService;
        $this->storageService = $storageService;
        $this->processStampService = $processStampService;
    }

    public function getDocumentStatus(Request $request, int $applicationId): JsonResponse
    {
        try {
            $user = $request->user();

            // Check application access
            $application = $this->accessService->getApplicationForUser($applicationId, $user->id);

            if (!$application) {
                return $this->sendError('Application not found or access denied', [], 404);
            }

            // Get document nominations for this application
            $documentNominations = DocumentNomination::where('application_id', $applicationId)
                ->with(['documentFiles', 'nominatedBy'])
                ->orderBy('created_at', 'desc')
                ->get();

            // Check if DOCUMENTS_NOMINATED stamp exists
            $documentsNominatedStamp = DB::table('processed_stamps')
                ->join('process_stamps_main', 'processed_stamps.stamp_id', '=', 'process_stamps_main.SAMP_ID')
                ->where('processed_stamps.application_id', $applicationId)
                ->where('process_stamps_main.STAMP_TAG', 'DOCUMENTS_NOMINATED')
                ->first();

            $status = $this->determineApplicationStatus($documentNominations, $documentsNominatedStamp);

            return $this->sendResponse([
                'application_id' => $applicationId,
                'status' => $status,
                'documents' => $documentNominations->map(function ($nomination) {
                    return [
                        'id' => $nomination->id,
                        'document_type' => $nomination->document_type_key,
                        'document_name' => $nomination->document_name,
                        'status' => $nomination->status,
                        'route_number' => $nomination->route_number,
                        'nominated_at' => $nomination->created_at,
                        'verified_at' => $nomination->verified_at,
                        'has_files' => $nomination->documentFiles->count() > 0,
                        'file_count' => $nomination->documentFiles->count()
                    ];
                }),
                'total_documents' => $documentNominations->count(),
                'completed_documents' => $documentNominations->where('status', 'approved')->count()
            ], 'Document status retrieved successfully');

        } catch (\Exception $e) {
            Log::error('Failed to get document status', [
                'application_id' => $applicationId,
                'error' => $e->getMessage()
            ]);

            return $this->sendError('Failed to retrieve document status', [], 500);
        }
    }

    public function completeDocumentNomination(Request $request, int $applicationId): JsonResponse
    {
        try {
            // Get authenticated user
            $user = $request->user();
            
            // Check application access
            $application = $this->accessService->getApplicationForUser($applicationId, $user->id);
            
            if (!$application) {
                return $this->sendError('Application not found or access denied', [], 404);
            }

            // Validate request data
            $validatedData = $request->validate([
                'route_number' => 'required|integer|min:1|max:3',
                'nominated_documents' => 'required|array|min:1',
                'nominated_documents.*.document_type_key' => 'required|string',
                'nominated_documents.*.document_data' => 'required|array',
                'nominated_documents.*.confirms_address' => 'boolean',
                'nominated_documents.*.file' => 'required|file|mimes:jpeg,png,pdf,tiff,bmp|max:10240' // 10MB
            ]);

            $routeNumber = $validatedData['route_number'];
            $nominations = $validatedData['nominated_documents'];

            // Validate document nominations
            $validationResult = $this->validationService->validateNominations($application, $nominations, $routeNumber);

            if (!$validationResult['validation_result']['is_valid']) {
                return $this->sendError('Document validation failed', $validationResult, 422);
            }

            // Process document completion
            $result = $this->processDocumentCompletion($application, $nominations, $routeNumber, $user->id);

            return $this->sendResponse($result, 'Documents completed successfully');

        } catch (ValidationException $e) {
            return $this->sendError('Validation failed', $e->errors(), 422);
        } catch (\Exception $e) {
            Log::error('Document completion failed', [
                'application_id' => $applicationId,
                'user_id' => $user->id ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->sendError('Document completion failed', ['error' => $e->getMessage()], 500);
        }
    }

    private function processDocumentCompletion(
        Application $application,
        array $nominations,
        int $routeNumber,
        int $userId
    ): array {
        DB::beginTransaction();

        try {
            $completedDocuments = [];

            // Clear existing nominations for this route
            DocumentNomination::where('application_id', $application->id)
                ->where('route_number', $routeNumber)
                ->delete();

            foreach ($nominations as $nomination) {
                // Create document nomination
                $documentNomination = $this->createDocumentNomination(
                    $application,
                    $nomination,
                    $routeNumber,
                    $userId
                );

                // Store document file
                $documentFile = $this->storageService->storeDocumentFile(
                    $nomination['file'],
                    $documentNomination,
                    $userId
                );

                $completedDocuments[] = [
                    'document_nomination_id' => $documentNomination->id,
                    'document_type_key' => $documentNomination->document_type_key,
                    'document_name' => $documentNomination->document_name,
                    'file_id' => $documentFile->id,
                    'file_name' => $documentFile->original_filename,
                    'file_size' => $documentFile->getFileSizeFormatted(),
                    'status' => $documentNomination->status
                ];
            }

            // Update application current route
            $application->update(['current_route' => $routeNumber]);

            // Create process stamp for document completion
            $this->createDocumentCompletionStamp($application, $routeNumber, $userId, $completedDocuments);

            DB::commit();

            return [
                'application_id' => $application->id,
                'route_number' => $routeNumber,
                'completed_documents' => $completedDocuments,
                'total_documents' => count($completedDocuments),
                'next_steps' => $this->getNextSteps($application, $routeNumber)
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    private function createDocumentNomination(
        Application $application,
        array $nomination,
        int $routeNumber,
        int $userId
    ): DocumentNomination {
        // Get document configuration
        $documentConfig = $this->getDocumentConfig($nomination['document_type_key']);

        return DocumentNomination::create([
            'application_id' => $application->id,
            'document_type_key' => $nomination['document_type_key'],
            'document_name' => $documentConfig['name'],
            'document_group' => $documentConfig['document_group'],
            'route_number' => $routeNumber,
            'key_values' => $nomination['document_data'],
            'confirms_address' => $nomination['confirms_address'] ?? $documentConfig['confirms_address'] ?? false,
            'status' => 'nominated',
            'nominated_by' => $userId
        ]);
    }

    private function createDocumentCompletionStamp(
        Application $application,
        int $routeNumber,
        int $userId,
        array $completedDocuments
    ): void {
        $stampData = [
            'route_number' => $routeNumber,
            'document_count' => count($completedDocuments),
            'documents' => array_map(function ($doc) {
                return [
                    'type' => $doc['document_type_key'],
                    'name' => $doc['document_name']
                ];
            }, $completedDocuments),
            'completion_timestamp' => now()->toISOString()
        ];

        $this->processStampService->completeStamp(
            $application->id,
            'DOCUMENTS_NOMINATED',
            $userId,
            $stampData,
            "Documents nominated for Route {$routeNumber} - {$stampData['document_count']} documents submitted"
        );
    }

    private function getDocumentConfig(string $documentTypeKey): array
    {
        // Get from existing config files
        $allDocuments = array_merge(
            \App\Modules\Documents\Config\DocumentTypes::getAll(),
            \App\Modules\Documents\Config\AddressDocuments::getAll(),
            \App\Modules\Documents\Config\RightToWorkDocuments::getAll()
        );

        if (!isset($allDocuments[$documentTypeKey])) {
            throw new \InvalidArgumentException("Unknown document type: {$documentTypeKey}");
        }

        return $allDocuments[$documentTypeKey];
    }

    private function getNextSteps(Application $application, int $routeNumber): array
    {
        return [
            'status' => 'Documents submitted successfully',
            'message' => "Your documents for Route {$routeNumber} have been submitted and are now under review.",
            'estimated_review_time' => '3-5 business days',
            'next_action' => 'Wait for admin review',
            'can_edit' => false // Documents cannot be edited once submitted
        ];
    }

    private function determineApplicationStatus($documentNominations, $documentsNominatedStamp): array
    {
        if (!$documentsNominatedStamp) {
            return [
                'stage' => 'pending_documents',
                'message' => 'No documents have been nominated yet',
                'next_action' => 'Submit required documents'
            ];
        }

        if ($documentsNominatedStamp->status === 'completed') {
            $allApproved = $documentNominations->every(function ($nomination) {
                return $nomination->status === 'approved';
            });

            $hasRejected = $documentNominations->contains(function ($nomination) {
                return $nomination->status === 'rejected';
            });

            $hasResubmissionRequired = $documentNominations->contains(function ($nomination) {
                return $nomination->status === 'resubmission_required';
            });

            if ($allApproved) {
                return [
                    'stage' => 'documents_approved',
                    'message' => 'All documents have been approved and submitted to DBS',
                    'next_action' => 'Wait for DBS processing',
                    'estimated_completion' => 'DBS processing typically takes 2-4 weeks'
                ];
            } elseif ($hasRejected || $hasResubmissionRequired) {
                return [
                    'stage' => 'documents_require_action',
                    'message' => 'Some documents require attention or resubmission',
                    'next_action' => 'Review rejected documents and resubmit if required'
                ];
            } else {
                return [
                    'stage' => 'documents_under_review',
                    'message' => 'Documents are currently being reviewed by our team',
                    'next_action' => 'Wait for review completion',
                    'estimated_review_time' => '3-5 business days'
                ];
            }
        }

        return [
            'stage' => 'documents_processing',
            'message' => 'Documents are being processed',
            'next_action' => 'Please wait while we process your submission'
        ];
    }
}
