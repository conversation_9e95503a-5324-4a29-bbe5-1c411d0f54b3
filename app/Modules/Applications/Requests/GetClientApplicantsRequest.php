<?php

declare(strict_types=1);

namespace App\Modules\Applications\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GetClientApplicantsRequest extends FormRequest
{
    public function authorize(): bool
    {
        // Authorization is handled in the controller
        return true;
    }

    public function rules(): array
    {
        return [
            'search' => 'sometimes|string|max:255',
            'status_filter' => 'sometimes|string|in:all,in_progress,further_action_pending,staff_review_pending,complete',
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|min:1|max:100'
        ];
    }

    public function messages(): array
    {
        return [
            'search.string' => 'Search term must be a string',
            'search.max' => 'Search term cannot exceed 255 characters',
            'status_filter.in' => 'Invalid status filter. Must be one of: all, in_progress, further_action_pending, staff_review_pending, complete',
            'page.integer' => 'Page must be an integer',
            'page.min' => 'Page must be at least 1',
            'per_page.integer' => 'Per page must be an integer',
            'per_page.min' => 'Per page must be at least 1',
            'per_page.max' => 'Per page cannot exceed 100'
        ];
    }

    protected function prepareForValidation(): void
    {
        // Sanitize search input
        if ($this->has('search')) {
            $this->merge([
                'search' => trim(strip_tags($this->input('search')))
            ]);
        }

        // Set defaults
        $this->merge([
            'status_filter' => $this->input('status_filter', 'all'),
            'page' => $this->input('page', 1),
            'per_page' => $this->input('per_page', 20)
        ]);
    }
}
