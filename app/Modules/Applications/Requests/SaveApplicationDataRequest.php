<?php

declare(strict_types=1);

namespace App\Modules\Applications\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Contracts\Validation\Validator;

/**
 * @bodyParam application_id integer required The ID of the application to save data for. Example: 2
 * @bodyParam form_data object required Form data as key-value pairs with string values. Example: {"ApplicantDetails::TITLE": "MR", "ApplicantDetails::FORENAME": "John", "ApplicantDetails::DATE_OF_BIRTH": "1990/01/01"}
 */
class SaveApplicationDataRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Prepare the data for validation.
     * Convert null values to empty strings and apply business logic validation
     *
     * Note: Identity verification fields (IDENTITY_VERIFIED, EVIDENCE_CHECKED_BY)
     * are excluded from validation as they are handled later in the process.
     */
    protected function prepareForValidation(): void
    {
        if ($this->has('form_data') && is_array($this->form_data)) {
            $formData = $this->form_data;

            // Convert null values to empty strings
            array_walk_recursive($formData, function (&$value) {
                if ($value === null) {
                    $value = '';
                }
            });

            $this->merge(['form_data' => $formData]);
        }
    }

    /**
     * Get the validation rules for the request.
     */

    public function rules(): array
    {
        $rules = [
            'application_id' => 'required|integer|exists:applications,id',
            'form_data' => 'present|array|min:1',
            'form_data.*' => 'string',
        ];

        // Add business logic validation rules
        $this->addBusinessLogicRules($rules);

        return $rules;
    }

    /**
     * Add business logic validation rules
     */
    private function addBusinessLogicRules(array &$rules): void
    {
        $formData = $this->input('form_data', []);

        // 1. Birth Surname Until is mandatory (cannot be empty)
        if (array_key_exists('AdditionalApplicantDetails::BIRTH_SURNAME_UNTIL', $formData)) {
            $rules['form_data.AdditionalApplicantDetails::BIRTH_SURNAME_UNTIL'] = 'required|string|min:1';
        }

        // 2. Postcode is required for UK addresses (CurrentAddress)
        $currentCountryCode = $formData['CurrentAddress::COUNTRY_CODE'] ?? '';
        if ($currentCountryCode === 'GB') {
            $rules['form_data.CurrentAddress::POSTCODE'] = 'required|string|min:1';
        }

        // 3. Postcode is required for UK addresses (PreviousAddress)
        foreach ($formData as $key => $value) {
            if (preg_match('/^PreviousAddress::(\d+)::Address::CountryCode$/', $key, $matches)) {
                if ($value === 'GB') {
                    $index = $matches[1];
                    $postcodeKey = "PreviousAddress::{$index}::Address::Postcode";
                    $rules["form_data.{$postcodeKey}"] = 'required|string|min:1';
                }
            }
        }

        // 4. NI Number can be empty (no additional rules needed - already nullable in SolidFuse)
    }

    /**
     * Get custom error messages for validation rules
     */
    public function messages(): array
    {
        return [
            // Basic validation messages
            'application_id.required' => 'Application ID is required',
            'application_id.integer' => 'Application ID must be an integer',
            'application_id.exists' => 'Application not found',
            'form_data.present' => 'Form data is required',
            'form_data.array' => 'Form data must be an array',
            'form_data.min' => 'Form data cannot be empty',
            'form_data.*.required' => 'All form fields are required',
            'form_data.*.string' => 'All form field values must be strings',

            // Business logic validation messages
            'form_data.AdditionalApplicantDetails::BIRTH_SURNAME_UNTIL.required' => 'Birth surname until date is mandatory and cannot be empty.',
            'form_data.AdditionalApplicantDetails::BIRTH_SURNAME_UNTIL.min' => 'Birth surname until date is mandatory and cannot be empty.',
            'form_data.CurrentAddress::POSTCODE.required' => 'Postcode is required for UK addresses.',
            'form_data.CurrentAddress::POSTCODE.min' => 'Postcode is required for UK addresses.',
            'form_data.PreviousAddress::*.Address::Postcode.required' => 'Postcode is required for UK addresses.',
            'form_data.PreviousAddress::*.Address::Postcode.min' => 'Postcode is required for UK addresses.',
        ];
    }

    protected function failedValidation(Validator $validator): void
    {
        throw new HttpResponseException(
            response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'data' => $validator->errors()
            ], 422)
        );
    }
}
