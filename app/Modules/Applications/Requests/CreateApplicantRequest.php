<?php

declare(strict_types=1);

namespace App\Modules\Applications\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * @bodyParam first_name string required The applicant's first name. Example: <PERSON>
 * @bodyParam last_name string required The applicant's last name. Example: <PERSON>
 * @bodyParam email string required The applicant's unique email address. Example: <EMAIL>
 * @bodyParam phone string required The applicant's phone number. Example: +44 123 123 123
 * @bodyParam job_role_id integer required The job role ID (products will be auto-selected from this job role). Example: 1
 */
class CreateApplicantRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:portal_users,email|max:255',
            'phone' => 'required|string|max:20',
            'job_role_id' => 'required|integer|exists:job_roles,id',
        ];
    }

    public function messages(): array
    {
        return [
            'first_name.required' => 'First name is required',
            'last_name.required' => 'Last name is required',
            'email.required' => 'Email address is required',
            'email.email' => 'Please provide a valid email address',
            'email.unique' => 'This email address is already registered',
            'phone.required' => 'Phone number is required',
            'job_role_id.required' => 'Job role selection is required',
            'job_role_id.exists' => 'Selected job role is invalid',
        ];
    }
}
