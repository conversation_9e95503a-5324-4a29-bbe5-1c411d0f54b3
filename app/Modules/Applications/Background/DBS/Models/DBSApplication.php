<?php

declare(strict_types=1);

namespace App\Modules\Applications\Background\DBS\Models;

use App\Modules\Applications\Models\Application;

/**
 * DBS Application Model
 * 
 * Extends the base Application model with DBS-specific functionality
 */
class DBSApplication extends Application
{
    /**
     * Check if this is a DBS Enhanced application
     */
    public function isDBSEnhanced(): bool
    {
        // Product ID 3 is typically DBS Enhanced
        // You can also check the product table for variant = 'DBS' and type = 'enhanced'
        return $this->product_id === 3;
    }

    /**
     * Get DBS-specific application data with proper structure
     */
    public function getDBSApplicationData(): array
    {
        $data = $this->getApplicationDataArray();
        
        // Ensure proper DBS data structure
        return $this->normalizeDBSData($data);
    }

    /**
     * Normalize DBS data to match expected structure
     */
    private function normalizeDBSData(array $data): array
    {
        // This method can be used to transform flat form data
        // into the nested structure expected by DBS validation
        
        $normalized = [];
        
        foreach ($data as $key => $value) {
            // Handle nested structures like CurrentAddress.Address.AddressLine1
            if (str_contains($key, '.')) {
                $this->setNestedValue($normalized, $key, $value);
            } else {
                $normalized[$key] = $value;
            }
        }
        
        return $normalized;
    }

    /**
     * Set nested array value using dot notation
     */
    private function setNestedValue(array &$array, string $key, $value): void
    {
        $keys = explode('.', $key);
        $current = &$array;
        
        foreach ($keys as $k) {
            if (!isset($current[$k]) || !is_array($current[$k])) {
                $current[$k] = [];
            }
            $current = &$current[$k];
        }
        
        $current = $value;
    }

    /**
     * Set DBS application data with proper validation
     */
    public function setDBSApplicationData(array $data): void
    {
        // Flatten nested data for storage
        $flatData = $this->flattenDBSData($data);
        
        $this->setApplicationData($flatData);
    }

    /**
     * Flatten nested DBS data for storage
     */
    private function flattenDBSData(array $data, string $prefix = ''): array
    {
        $flattened = [];
        
        foreach ($data as $key => $value) {
            $newKey = $prefix ? $prefix . '.' . $key : $key;
            
            if (is_array($value) && !$this->isIndexedArray($value)) {
                $flattened = array_merge($flattened, $this->flattenDBSData($value, $newKey));
            } else {
                $flattened[$newKey] = is_array($value) ? json_encode($value) : $value;
            }
        }
        
        return $flattened;
    }

    /**
     * Check if array is indexed (not associative)
     */
    private function isIndexedArray(array $array): bool
    {
        return array_keys($array) === range(0, count($array) - 1);
    }
}
