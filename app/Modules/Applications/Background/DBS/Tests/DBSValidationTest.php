<?php

declare(strict_types=1);

namespace App\Modules\Applications\Background\DBS\Tests;

use App\Modules\Applications\Background\DBS\Services\DBSValidationService;
use Illuminate\Validation\ValidationException;

/**
 * Simple test class to verify DBS validation functionality
 */
class DBSValidationTest
{
    private DBSValidationService $validationService;

    public function __construct(DBSValidationService $validationService)
    {
        $this->validationService = $validationService;
    }

    /**
     * Test field name validation
     */
    public function testFieldNameValidation(): array
    {
        $results = [];

        // Test valid DBS Enhanced field names
        $validData = [
            'Title' => 'MR',
            'Forename' => 'JOHN',
            'PresentSurname' => 'SMITH',
            'CurrentAddress.Address.AddressLine1' => '123 MAIN STREET',
            'CurrentAddress.Address.AddressTown' => 'LONDON',
            'CurrentAddress.Address.CountryCode' => 'GB',
            'CurrentAddress.ResidentFromGyearMonth' => '2020-01',
            'DateOfBirth' => '1990-01-01',
            'Gender' => 'male',
        ];

        try {
            $this->validationService->validateFieldNames($validData, 3);
            $results['valid_fields'] = 'PASS - Valid field names accepted';
        } catch (ValidationException $e) {
            $results['valid_fields'] = 'FAIL - Valid field names rejected: ' . json_encode($e->errors());
        }

        // Test invalid field names
        $invalidData = [
            'Title' => 'MR',
            'InvalidField' => 'test',
            'AnotherInvalidField' => 'test2',
        ];

        try {
            $this->validationService->validateFieldNames($invalidData, 3);
            $results['invalid_fields'] = 'FAIL - Invalid field names accepted';
        } catch (ValidationException $e) {
            $results['invalid_fields'] = 'PASS - Invalid field names rejected: ' . json_encode($e->errors());
        }

        return $results;
    }

    /**
     * Test allowed field names retrieval
     */
    public function testAllowedFieldNames(): array
    {
        $results = [];

        // Test DBS Enhanced field names
        $enhancedFields = $this->validationService->getAllowedFieldNames(3);
        $results['enhanced_fields_count'] = count($enhancedFields);
        $results['enhanced_fields_sample'] = array_slice($enhancedFields, 0, 5);

        // Test basic DBS field names
        $basicFields = $this->validationService->getAllowedFieldNames(1);
        $results['basic_fields_count'] = count($basicFields);
        $results['basic_fields_sample'] = array_slice($basicFields, 0, 5);

        return $results;
    }

    /**
     * Test array field validation
     */
    public function testArrayFieldValidation(): array
    {
        $results = [];

        // Test valid array fields
        $arrayData = [
            'PreviousAddress.0.Address.AddressLine1' => '456 OLD STREET',
            'PreviousAddress.0.Address.AddressTown' => 'MANCHESTER',
            'PreviousAddress.0.Address.CountryCode' => 'GB',
            'PreviousAddress.0.ResidentDates.ResidentFromGyearMonth' => '2018-01',
            'PreviousAddress.0.ResidentDates.ResidentToGyearMonth' => '2019-12',
            'OtherSurnames.0.Name' => 'JONES',
            'OtherSurnames.0.UsedFrom' => '2010',
            'OtherSurnames.0.UsedTo' => '2015',
        ];

        try {
            $this->validationService->validateFieldNames($arrayData, 3);
            $results['array_fields'] = 'PASS - Array field names accepted';
        } catch (ValidationException $e) {
            $results['array_fields'] = 'FAIL - Array field names rejected: ' . json_encode($e->errors());
        }

        return $results;
    }

    /**
     * Run all tests
     */
    public function runAllTests(): array
    {
        return [
            'field_name_validation' => $this->testFieldNameValidation(),
            'allowed_field_names' => $this->testAllowedFieldNames(),
            'array_field_validation' => $this->testArrayFieldValidation(),
        ];
    }
}
