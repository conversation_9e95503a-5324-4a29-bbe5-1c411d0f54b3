<?php

declare(strict_types=1);

namespace App\Modules\Applications\Background\DBS\Services;

use App\Modules\Applications\Background\DBS\Models\DBSApplication;
use App\Modules\Applications\Background\DBS\Repositories\DBSRepositoryInterface;

/**
 * DBS Application Service
 * 
 * Business logic layer for DBS applications
 */
class DBSApplicationService
{
    protected DBSRepositoryInterface $repository;
    protected DBSValidationService $validationService;

    public function __construct(
        DBSRepositoryInterface $repository,
        DBSValidationService $validationService
    ) {
        $this->repository = $repository;
        $this->validationService = $validationService;
    }

    /**
     * Process DBS application data submission
     */
    public function processApplicationData(int $applicationId, array $formData): array
    {
        $application = $this->repository->findById($applicationId);
        
        if (!$application) {
            throw new \Exception('DBS Application not found');
        }

        // Validate field names first
        $this->validationService->validateFieldNames($formData, $application->product_id);

        // Validate and save the data
        $success = $this->repository->saveApplicationData($application, $formData);

        if (!$success) {
            throw new \Exception('Failed to save DBS application data');
        }

        return [
            'application_id' => $applicationId,
            'status' => 'saved',
            'is_complete' => $this->repository->isApplicationComplete($application),
            'validation_errors' => $this->repository->getValidationErrors($application)
        ];
    }

    /**
     * Get DBS application details with validation status
     */
    public function getApplicationDetails(int $applicationId): array
    {
        $application = $this->repository->findById($applicationId);
        
        if (!$application) {
            throw new \Exception('DBS Application not found');
        }

        $data = $application->getDBSApplicationData();
        $validationErrors = $this->repository->getValidationErrors($application);
        $isComplete = $this->repository->isApplicationComplete($application);

        return [
            'application' => [
                'id' => $application->id,
                'product_id' => $application->product_id,
                'status' => $application->status,
                'is_dbs_enhanced' => $application->isDBSEnhanced(),
                'is_complete' => $isComplete,
            ],
            'form_data' => $data,
            'validation_errors' => $validationErrors,
            'allowed_fields' => $this->validationService->getAllowedFieldNames($application->product_id),
            'required_fields' => $this->repository->getRequiredFields($application)
        ];
    }

    /**
     * Validate DBS form data without saving
     */
    public function validateFormData(array $formData, int $productId): array
    {
        try {
            $validatedData = $this->validationService->validateFormData($formData, $productId);
            
            return [
                'valid' => true,
                'validated_data' => $validatedData,
                'errors' => []
            ];
        } catch (\Illuminate\Validation\ValidationException $e) {
            return [
                'valid' => false,
                'validated_data' => [],
                'errors' => $e->errors()
            ];
        }
    }

    /**
     * Get allowed field names for a product
     */
    public function getAllowedFieldNames(int $productId): array
    {
        return $this->validationService->getAllowedFieldNames($productId);
    }

    /**
     * Check if a product is DBS Enhanced
     */
    public function isDBSEnhancedProduct(int $productId): bool
    {
        return $productId === 3;
    }

    /**
     * Get DBS applications by applicant
     */
    public function getApplicationsByApplicant(int $applicantId): array
    {
        return $this->repository->findByApplicantId($applicantId);
    }

    /**
     * Get all DBS Enhanced applications
     */
    public function getDBSEnhancedApplications(): array
    {
        return $this->repository->findDBSEnhancedApplications();
    }
}
