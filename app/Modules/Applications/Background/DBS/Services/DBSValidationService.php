<?php

declare(strict_types=1);

namespace App\Modules\Applications\Background\DBS\Services;

use SolidFuse\Modules\Applications\Services\DBSEnhancedValidationService;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

/**
 * DBS Validation Service for API Interface
 *
 * Provides comprehensive validation for DBS applications using SolidFuse validation rules
 * and ensures only recognized field names are accepted
 */
class DBSValidationService
{
    protected $solidFuseValidationService;

    public function __construct($solidFuseValidationService)
    {
        $this->solidFuseValidationService = $solidFuseValidationService;
    }

    /**
     * Validate DBS form data - delegates to SolidFuse service
     */
    public function validateFormData(array $formData, int $productId): array
    {
        // For DBS products (product_id 1 or 3), use SolidFuse validation
        if ($this->isDBSEnhancedProduct($productId)) {
            if ($this->solidFuseValidationService && method_exists($this->solidFuseValidationService, 'validateFormData')) {
                // Transform API field names to SolidFuse format
                $transformedData = $this->transformApiFieldNamesToSolidFuseFormat($formData);
                return $this->solidFuseValidationService->validateFormData($transformedData, $productId);
            } else {
                throw new \Exception('SolidFuse validation service not available for DBS products');
            }
        }

        throw new \Exception('Unsupported product type for DBS validation');
    }

    /**
     * Transform API field names to SolidFuse format
     * Converts field names from API format (ApplicantDetails::NI_NUMBER) to SolidFuse format (NINumber)
     */
    private function transformApiFieldNamesToSolidFuseFormat(array $formData): array
    {
        $transformed = [];

        foreach ($formData as $apiFieldName => $value) {
            // Skip identity verification fields - these are handled later in the process
            if ($this->isIdentityVerificationField($apiFieldName)) {
                continue;
            }

            $solidFuseFieldName = $this->convertApiFieldNameToSolidFuse($apiFieldName);
            $transformedValue = $this->transformFieldValue($solidFuseFieldName, $value);
            $transformed[$solidFuseFieldName] = $transformedValue;
        }

        return $transformed;
    }

    /**
     * Transform field values to the format expected by SolidFuse validation
     */
    private function transformFieldValue(string $fieldName, $value)
    {
        // Convert 'y'/'n' strings to boolean for 'accepted' validation rules
        // These fields use Laravel's 'accepted' rule which expects boolean true
        $acceptedFields = [
            'DeclarationByApplicant',
            'IdentityVerified',  // Excluded from processing but included for completeness
            'RBdetails.CurrentAddressDetailsChecked',
        ];

        // Convert 'y'/'n' strings to boolean for other boolean-like fields
        $booleanFields = [
            'Volunteer',
            'WorkingAtHomeAddress',
            'RBdetails.Volunteer',
            'RBdetails.WorkingAtHomeAddress',
        ];

        // Handle 'accepted' fields - must be boolean true for validation to pass
        if (in_array($fieldName, $acceptedFields)) {
            if ($value === 'y' || $value === '1' || $value === 1 || $value === true || $value === 'true') {
                return true;
            }
            // For 'accepted' fields, anything else should be false
            return false;
        }

        // Handle other boolean fields - can be true or false
        if (in_array($fieldName, $booleanFields)) {
            if ($value === 'y' || $value === '1' || $value === 1 || $value === true || $value === 'true') {
                return true;
            }
            if ($value === 'n' || $value === '0' || $value === 0 || $value === false || $value === 'false') {
                return false;
            }
        }

        return $value;
    }

    /**
     * Check if a field is an identity verification field that should be skipped during save
     */
    private function isIdentityVerificationField(string $apiFieldName): bool
    {
        $identityFields = [
            'ApplicantIdentityDetails::IDENTITY_VERIFIED',
            'ApplicantIdentityDetails::EVIDENCE_CHECKED_BY',
        ];

        return in_array($apiFieldName, $identityFields);
    }

    /**
     * Convert individual API field name to SolidFuse format
     */
    private function convertApiFieldNameToSolidFuse(string $apiFieldName): string
    {
        // Handle API field names with :: separators
        if (str_contains($apiFieldName, '::')) {
            $parts = explode('::', $apiFieldName);

            // Remove the first part (section name like ApplicantDetails, CurrentAddress, etc.)
            array_shift($parts);

            // Join remaining parts and convert to SolidFuse format
            $fieldName = implode('.', $parts);

            // Handle specific field name mappings
            $fieldMappings = [
                'NI_NUMBER' => 'NINumber',
                'DATE_OF_BIRTH' => 'DateOfBirth',
                'PRESENT_SURNAME' => 'PresentSurname',
                'FORENAME' => 'Forename',
                'TITLE' => 'Title',
                'GENDER' => 'Gender',
                'ADDRESS_LINE1' => 'Address.AddressLine1',
                'ADDRESS_LINE2' => 'Address.AddressLine2',
                'ADDRESS_TOWN' => 'Address.AddressTown',
                'ADDRESS_COUNTY' => 'Address.AddressCounty',
                'POSTCODE' => 'Address.Postcode',
                'COUNTRY_CODE' => 'Address.CountryCode',
                'RESIDENT_FROM_YEAR_MONTH' => 'ResidentFromGyearMonth',
                // Additional Applicant Details
                'BIRTH_SURNAME' => 'BirthSurname',
                'BIRTH_SURNAME_UNTIL' => 'BirthSurnameUntil',
                'BIRTH_TOWN' => 'BirthTown',
                'BIRTH_COUNTY' => 'BirthCounty',
                'BIRTH_COUNTRY' => 'BirthCountry',
                'BIRTH_NATIONALITY' => 'BirthNationality',
                'CONTACT_NUMBER' => 'ContactNumber',
                'UNSPENT_CONVICTIONS' => 'UnspentConvictions',
                'DECLARATION_BY_APPLICANT' => 'DeclarationByApplicant',
                'LANGUAGE_PREFERENCE' => 'LanguagePreference',
                // Identity Details - REMOVED: These are handled later in the process
                // 'IDENTITY_VERIFIED' => 'IdentityVerified',
                // 'EVIDENCE_CHECKED_BY' => 'EvidenceCheckedBy',
            ];

            // Handle different section prefixes
            if (str_starts_with($apiFieldName, 'CurrentAddress::')) {
                $fieldPart = str_replace('CurrentAddress::', '', $apiFieldName);
                if (isset($fieldMappings[$fieldPart])) {
                    return 'CurrentAddress.' . $fieldMappings[$fieldPart];
                }
            }

            if (str_starts_with($apiFieldName, 'AdditionalApplicantDetails::')) {
                $fieldPart = str_replace('AdditionalApplicantDetails::', '', $apiFieldName);
                if (isset($fieldMappings[$fieldPart])) {
                    return $fieldMappings[$fieldPart];
                }
            }

            if (str_starts_with($apiFieldName, 'ApplicantIdentityDetails::')) {
                $fieldPart = str_replace('ApplicantIdentityDetails::', '', $apiFieldName);
                if (isset($fieldMappings[$fieldPart])) {
                    return $fieldMappings[$fieldPart];
                }
            }

            // Apply field mappings for ApplicantDetails and other fields
            if (isset($fieldMappings[$fieldName])) {
                return $fieldMappings[$fieldName];
            }

            // Handle PreviousAddress with index
            if (str_contains($apiFieldName, 'PreviousAddress::')) {
                // Extract the original field name part for mapping lookup
                $parts = explode('::', $apiFieldName);
                $originalFieldPart = end($parts); // Get the last part (e.g., AddressLine1, ResidentFromGyearMonth)

                // Convert PreviousAddress::0::Address::AddressLine1 to PreviousAddress.0.Address.AddressLine1
                $converted = str_replace('::', '.', $apiFieldName);

                // Special mappings for PreviousAddress fields (without Address. prefix since it's already in the structure)
                $previousAddressFieldMappings = [
                    'AddressLine1' => 'AddressLine1',
                    'AddressLine2' => 'AddressLine2',
                    'AddressTown' => 'AddressTown',
                    'AddressCounty' => 'AddressCounty',
                    'Postcode' => 'Postcode',
                    'CountryCode' => 'CountryCode',
                    'ResidentFromGyearMonth' => 'ResidentFromGyearMonth',
                    'ResidentToGyearMonth' => 'ResidentToGyearMonth',
                ];

                // Apply field mappings to the original field part
                if (isset($previousAddressFieldMappings[$originalFieldPart])) {
                    // Replace the last part with the mapped value
                    $lastDotPos = strrpos($converted, '.');
                    if ($lastDotPos !== false) {
                        $converted = substr($converted, 0, $lastDotPos + 1) . $previousAddressFieldMappings[$originalFieldPart];
                    }
                }

                return $converted;
            }

            return $fieldName;
        }

        // Return as-is if no :: separator
        return $apiFieldName;
    }

    /**
     * Validate field names - delegates to SolidFuse service
     */
    public function validateFieldNames(array $formData, int $productId): void
    {
        if ($this->isDBSEnhancedProduct($productId)) {
            if ($this->solidFuseValidationService && method_exists($this->solidFuseValidationService, 'validateFieldNames')) {
                // Transform API field names to SolidFuse format
                $transformedData = $this->transformApiFieldNamesToSolidFuseFormat($formData);
                $this->solidFuseValidationService->validateFieldNames($transformedData, $productId);
            } else {
                throw new \Exception('SolidFuse validation service not available for field name validation');
            }
        } else {
            throw new \Exception('Unsupported product type for field name validation');
        }
    }

    /**
     * Extract all field names from form data including nested and array fields
     */
    private function extractAllFieldNames(array $formData, string $prefix = ''): array
    {
        $fields = [];

        foreach ($formData as $key => $value) {
            $fullKey = $prefix ? $prefix . '.' . $key : $key;
            $fields[] = $fullKey;

            if (is_array($value)) {
                $fields = array_merge($fields, $this->extractAllFieldNames($value, $fullKey));
            }
        }

        return $fields;
    }

    /**
     * Check if a field is allowed, considering dynamic array patterns
     */
    private function isFieldAllowed(string $field, array $allowedFields): bool
    {
        // Direct match
        if (in_array($field, $allowedFields)) {
            return true;
        }

        // Check for array patterns directly
        if ($this->matchesArrayPattern($field, '')) {
            return true;
        }

        return false;
    }

    /**
     * Check if field matches an array pattern
     */
    private function matchesArrayPattern(string $field, string $pattern): bool
    {
        // Handle patterns like PreviousAddress, OtherSurnames, OtherForenames
        $arrayPatterns = [
            'PreviousAddress' => [
                'PreviousAddress.*.Address.AddressLine1',
                'PreviousAddress.*.Address.AddressLine2',
                'PreviousAddress.*.Address.AddressTown',
                'PreviousAddress.*.Address.AddressCounty',
                'PreviousAddress.*.Address.Postcode',
                'PreviousAddress.*.Address.CountryCode',
                'PreviousAddress.*.ResidentDates.ResidentFromGyearMonth',
                'PreviousAddress.*.ResidentDates.ResidentToGyearMonth',
            ],
            'OtherSurnames' => [
                'OtherSurnames.*.Name',
                'OtherSurnames.*.UsedFrom',
                'OtherSurnames.*.UsedTo',
            ],
            'OtherForenames' => [
                'OtherForenames.*.Name',
                'OtherForenames.*.UsedFrom',
                'OtherForenames.*.UsedTo',
            ],
        ];

        // Check if the field matches any array pattern directly
        foreach ($arrayPatterns as $basePattern => $subPatterns) {
            foreach ($subPatterns as $subPattern) {
                // Replace * with \d+ first, then escape for regex
                $pattern = str_replace('*', '\d+', $subPattern);
                $regex = preg_quote($pattern, '/');
                // Fix the escaped backslashes
                $regex = str_replace('\\\\d\\+', '\\d+', $regex);

                if (preg_match('/^' . $regex . '$/', $field)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Get all allowed field names - delegates to SolidFuse service
     */
    public function getAllowedFieldNames(int $productId): array
    {
        if ($this->isDBSEnhancedProduct($productId)) {
            if ($this->solidFuseValidationService && method_exists($this->solidFuseValidationService, 'getAllowedFieldNames')) {
                return $this->solidFuseValidationService->getAllowedFieldNames($productId);
            } else {
                throw new \Exception('SolidFuse validation service not available for field names');
            }
        }

        throw new \Exception('Unsupported product type for field names');
    }

    /**
     * Get all allowed field names for DBS Enhanced applications
     */
    private function getDBSEnhancedFieldNames(): array
    {
        return [
            // ApplicantDetails Structure
            'Title',
            'Forename',
            'PresentSurname',
            
            // Middle Names (Optional container with 1-3 elements)
            'Middlenames',
            'Middlenames.0',
            'Middlenames.1',
            'Middlenames.2',
            
            // Current Address Structure
            'CurrentAddress.Address.AddressLine1',
            'CurrentAddress.Address.AddressLine2',
            'CurrentAddress.Address.AddressTown',
            'CurrentAddress.Address.AddressCounty',
            'CurrentAddress.Address.Postcode',
            'CurrentAddress.Address.CountryCode',
            'CurrentAddress.ResidentFromGyearMonth',
            
            // Previous Addresses (0-200 repeatable) - we'll validate dynamically
            'PreviousAddress',
            
            // Personal Information
            'DateOfBirth',
            'Gender',
            'NINumber',
            
            // Additional Applicant Details
            'BirthSurname',
            'BirthSurnameUntil',
            
            // Other Surnames (0-200 repeatable) - we'll validate dynamically
            'OtherSurnames',
            
            // Other Forenames (0-200 repeatable) - we'll validate dynamically
            'OtherForenames',
            
            // Birth Details
            'BirthTown',
            'BirthCounty',
            'BirthCountry',
            'BirthNationality',
            
            // Contact Information
            'ContactNumber',
            
            // Declarations
            'UnspentConvictions',
            'DeclarationByApplicant',
            'LanguagePreference',
            
            // Identity Verification
            'IdentityVerified',
            'EvidenceCheckedBy',
            
            // Passport Details (Optional structure)
            'PassportDetails.PassportNumber',
            'PassportDetails.PassportDob',
            'PassportDetails.PassportNationality',
            'PassportDetails.PassportIssueDate',
            
            // Driver Licence Details (Optional structure)
            'DriverLicenceDetails.DriverLicenceNumber',
            'DriverLicenceDetails.DriverLicenceDOB',
            'DriverLicenceDetails.DriverLicenceType',
            'DriverLicenceDetails.DriverLicenceValidFrom',
            'DriverLicenceDetails.DriverLicenceIssueCountry',
            
            // Potential Employer Details
            'PotentialEmployerDetails.PositionAppliedFor',
            'PotentialEmployerDetails.OrganisationName',
            
            // RB Details
            'RBdetails.RBApplicationReference',
            'RBdetails.RBNumber',
            'RBdetails.CSigNumber',
            'RBdetails.DisclosureType',
            'RBdetails.WorkingWithVulnerableAdults',
            'RBdetails.WorkingWithChildren',
            'RBdetails.CurrentAddressDetailsChecked',
            'RBdetails.Volunteer',
            'RBdetails.WorkingAtHomeAddress',
        ];
    }

    /**
     * Get all allowed field names for basic DBS applications
     */
    private function getBasicDBSFieldNames(): array
    {
        return [
            // Personal Details
            'Title',
            'Forename',
            'PresentSurname',
            'BirthSurname',
            
            // Current Address
            'CurrentAddress.Address.AddressLine1',
            'CurrentAddress.Address.AddressLine2',
            'CurrentAddress.Address.AddressTown',
            'CurrentAddress.Address.AddressCounty',
            'CurrentAddress.Address.Postcode',
            'CurrentAddress.Address.CountryCode',
            'CurrentAddress.ResidentFromGyearMonth',
            
            // Previous Address
            'PreviousAddress',
            
            // Personal Information
            'DateOfBirth',
            'Gender',
            'NINumber',
            
            // Birth Details
            'BirthTown',
            'BirthCounty',
            'BirthCountry',
            'BirthNationality',
            
            // Contact
            'ContactNumber',
            
            // Declarations
            'UnspentConvictions',
            'DeclarationByApplicant',
            'LanguagePreference',
            'IdentityVerified',
            'EvidenceCheckedBy',
            
            // Position Details
            'PotentialEmployerDetails.PositionAppliedFor',
            'PotentialEmployerDetails.OrganisationName',
            
            // RB Details
            'RBdetails.RBApplicationReference',
            'RBdetails.RBNumber',
            'RBdetails.CSigNumber',
            'RBdetails.WorkingWithVulnerableAdults',
            'RBdetails.WorkingWithChildren',
            'RBdetails.CurrentAddressDetailsChecked',
            'RBdetails.Volunteer',
            'RBdetails.WorkingAtHomeAddress',
        ];
    }

    /**
     * Check if product is DBS Enhanced
     */
    private function isDBSEnhancedProduct(int $productId): bool
    {
        // Product ID 3 is DBS Enhanced, Product ID 1 is DBS Standard
        // Both use the same comprehensive validation
        return in_array($productId, [1, 3]);
    }

    /**
     * Validate form data using SolidFuse service
     * Only validates fields that are present in the request
     */
    private function validateSolidFuseFormData(array $formData): array
    {
        try {
            // Try to validate with SolidFuse
            // Note: SolidFuse validation might still require all fields
            // If it fails due to missing required fields, we'll catch it and return the data
            return $this->solidFuseValidationService->validateFormData($formData);
        } catch (\Exception $e) {
            // If SolidFuse validation fails due to missing required fields,
            // we still want to validate the fields that are present
            $errorMessage = $e->getMessage();

            // Check if the error is about missing required fields
            if (str_contains($errorMessage, 'required') || str_contains($errorMessage, 'is required')) {
                // For now, just return the form data as-is since we only want to validate present fields
                // In the future, we could implement partial SolidFuse validation
                throw $e; // Re-throw to trigger fallback validation
            }

            // For other validation errors (like invalid values), re-throw
            throw $e;
        }
    }

    /**
     * Validate basic DBS form data for non-enhanced products
     * Only validates fields that are present in the request
     */
    private function validateBasicDBSFormData(array $formData, int $productId): array
    {
        // Get all possible validation rules
        $allRules = $this->getBasicValidationRules($productId);
        $messages = $this->getBasicValidationMessages();

        // Only apply rules for fields that are actually present in the request
        $applicableRules = [];
        foreach ($formData as $fieldName => $value) {
            if (isset($allRules[$fieldName])) {
                // Remove 'required' from the rule since we only validate present fields
                $rule = $allRules[$fieldName];
                $rule = str_replace('required|', '', $rule);
                $rule = str_replace('|required', '', $rule);
                if ($rule === 'required') {
                    $rule = ''; // If only required, make it empty
                }

                if (!empty($rule)) {
                    $applicableRules[$fieldName] = $rule;
                }
            }
        }

        // Only validate if there are applicable rules
        if (!empty($applicableRules)) {
            $validator = Validator::make($formData, $applicableRules, $messages);

            if ($validator->fails()) {
                throw new ValidationException($validator);
            }
        }

        return $formData; // Return original data since we're not requiring all fields
    }

    /**
     * Get basic validation rules for non-enhanced DBS products
     */
    private function getBasicValidationRules(int $productId): array
    {
        return [
            'Title' => 'required|string|in:MR,MRS,MISS,MS,DR,PROF,REV,SIR,LADY,LORD',
            'Forename' => 'required|string|max:60',
            'PresentSurname' => 'required|string|max:60',
            'DateOfBirth' => 'required|date|before:today',
            'Gender' => 'required|in:male,female',
            'CurrentAddress.Address.AddressLine1' => 'required|string|max:60',
            'CurrentAddress.Address.AddressTown' => 'required|string|max:30',
            'CurrentAddress.Address.CountryCode' => 'required|string|size:2',
            'CurrentAddress.ResidentFromGyearMonth' => 'required|date_format:Y-m|before_or_equal:today',
            'BirthTown' => 'required|string|max:30',
            'BirthCountry' => 'required|string|size:2',
            'UnspentConvictions' => 'required|in:y,n',
            'DeclarationByApplicant' => 'required|accepted',
            'LanguagePreference' => 'required|in:english,welsh',
            'IdentityVerified' => 'required|accepted',
            'EvidenceCheckedBy' => 'required|string|max:60',
            'PotentialEmployerDetails.PositionAppliedFor' => 'required|string|max:60',
            'PotentialEmployerDetails.OrganisationName' => 'required|string|max:60',
            'RBdetails.RBApplicationReference' => 'required|string|min:1|max:30',
            'RBdetails.RBNumber' => 'required|string|regex:/^\d{11}$/',
            'RBdetails.CSigNumber' => 'required|string|regex:/^\d{11}$/',
            'RBdetails.WorkingWithVulnerableAdults' => 'required|in:y,n',
            'RBdetails.WorkingWithChildren' => 'required|in:y,n',
            'RBdetails.CurrentAddressDetailsChecked' => 'required|accepted',
            'RBdetails.Volunteer' => 'required|in:y,n',
            'RBdetails.WorkingAtHomeAddress' => 'required|in:y,n',
        ];
    }

    /**
     * Get basic validation messages
     */
    private function getBasicValidationMessages(): array
    {
        return [
            'Title.required' => 'Applicant\'s title is required',
            'Forename.required' => 'First forename is required',
            'PresentSurname.required' => 'Current surname is required',
            'DateOfBirth.required' => 'Date of birth is required',
            'Gender.required' => 'Gender is required',
            'CurrentAddress.Address.AddressLine1.required' => 'Address Line 1 is required',
            'CurrentAddress.Address.AddressTown.required' => 'Town/City is required',
            'CurrentAddress.Address.CountryCode.required' => 'Country is required',
            'CurrentAddress.ResidentFromGyearMonth.required' => 'Resident from date is required',
            'BirthTown.required' => 'Town of birth is required',
            'BirthCountry.required' => 'Country of birth is required',
            'UnspentConvictions.required' => 'Unspent convictions declaration is required',
            'DeclarationByApplicant.required' => 'Applicant declaration is required',
            'LanguagePreference.required' => 'Language preference is required',
            'IdentityVerified.required' => 'Identity verification is required',
            'EvidenceCheckedBy.required' => 'Name of person who checked identity is required',
            'PotentialEmployerDetails.PositionAppliedFor.required' => 'Position applied for is required',
            'PotentialEmployerDetails.OrganisationName.required' => 'Organisation name is required',
            'RBdetails.RBApplicationReference.required' => 'RB application reference is required',
            'RBdetails.RBNumber.required' => 'Registered Body number is required',
            'RBdetails.CSigNumber.required' => 'Countersignatory number is required',
            'RBdetails.WorkingWithVulnerableAdults.required' => 'Working with vulnerable adults declaration is required',
            'RBdetails.WorkingWithChildren.required' => 'Working with children declaration is required',
            'RBdetails.CurrentAddressDetailsChecked.required' => 'Current address details check is required',
            'RBdetails.Volunteer.required' => 'Volunteer status is required',
            'RBdetails.WorkingAtHomeAddress.required' => 'Working at home address declaration is required',
        ];
    }
}
