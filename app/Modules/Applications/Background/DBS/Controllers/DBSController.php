<?php

declare(strict_types=1);

namespace App\Modules\Applications\Background\DBS\Controllers;

use App\Core\BaseApiController;
use App\Modules\Applications\Background\DBS\Services\DBSValidationService;
use App\Modules\Applications\Models\Application;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

/**
 * DBS Controller
 *
 * Provides endpoints for DBS STRD Enhanced validation and data saving
 */
class DBSController extends BaseApiController
{
    private DBSValidationService $validationService;

    public function __construct(DBSValidationService $validationService)
    {
        $this->validationService = $validationService;
    }



    /**
     * Get allowed field names for a product
     */
    public function getAllowedFields(Request $request): JsonResponse
    {
        try {
            $productId = (int) $request->input('product_id', 3);
            $allowedFields = $this->validationService->getAllowedFieldNames($productId);

            return $this->sendResponse([
                'product_id' => $productId,
                'allowed_fields' => $allowedFields,
                'field_count' => count($allowedFields),
            ], 'Allowed field names retrieved successfully');
        } catch (\Exception $e) {
            return $this->sendServerError('Failed to get allowed fields: ' . $e->getMessage());
        }
    }

    /**
     * Save DBS data with validation
     */
    public function saveDBSData(Request $request): JsonResponse
    {
        try {
            $applicationId = (int) $request->input('application_id');
            $productId = (int) $request->input('product_id', 3);

            // Validate that application_id is provided
            if (!$applicationId) {
                return $this->sendError('Validation failed', ['application_id' => 'Application ID is required'], 422);
            }

            // Find the application
            $application = Application::find($applicationId);
            if (!$application) {
                return $this->sendError('Validation failed', ['application_id' => 'Application not found'], 404);
            }

            // Get all request data except application_id and product_id to treat as form fields
            $allRequestData = $request->except(['application_id', 'product_id']);

            // If form_data is provided and is an array, use it as the form data
            if ($request->has('form_data') && is_array($request->input('form_data'))) {
                $formData = $request->input('form_data');
            } else {
                // Otherwise, treat all other fields in the request as form data
                // This handles cases where someone sends "form_data": "string" or other invalid formats
                $formData = $allRequestData;
            }

            // Validate field names first - return specific error for unrecognized fields
            try {
                $this->validationService->validateFieldNames($formData, $productId);
            } catch (\Illuminate\Validation\ValidationException $e) {
                $errors = [];
                foreach (array_keys($formData) as $fieldName) {
                    $allowedFields = $this->validationService->getAllowedFieldNames($productId);
                    if (!$this->isFieldAllowed($fieldName, $allowedFields)) {
                        $errors[$fieldName] = "Field name not recognised";
                    }
                }

                if (!empty($errors)) {
                    return $this->sendError('Validation failed', $errors, 422);
                }
            }

            // Validate form data values (ABV Rules)
            try {
                $validatedData = $this->validationService->validateFormData($formData, $productId);

                // Save the validated data to the database
                DB::transaction(function () use ($application, $validatedData) {
                    $application->setApplicationData($validatedData);
                });

                return $this->sendResponse([
                    'application_id' => $applicationId,
                    'message' => 'DBS data saved successfully',
                    'validated_data' => $validatedData,
                ], 'DBS data saved to database successfully');

            } catch (\Illuminate\Validation\ValidationException $e) {
                // Return validation errors for field values
                $errors = [];
                foreach (array_keys($e->errors()) as $field) {
                    $errors[$field] = 'Validation failed';
                }
                return $this->sendError('Validation failed', $errors, 422);
            }

        } catch (\Exception $e) {
            return $this->sendServerError('DBS data validation error: ' . $e->getMessage());
        }
    }

    /**
     * Check if field is allowed (helper method)
     */
    private function isFieldAllowed(string $field, array $allowedFields): bool
    {
        // Direct match
        if (in_array($field, $allowedFields)) {
            return true;
        }

        // Check for array patterns
        $arrayPatterns = [
            'PreviousAddress.*.Address.AddressLine1',
            'PreviousAddress.*.Address.AddressLine2',
            'PreviousAddress.*.Address.AddressTown',
            'PreviousAddress.*.Address.AddressCounty',
            'PreviousAddress.*.Address.Postcode',
            'PreviousAddress.*.Address.CountryCode',
            'PreviousAddress.*.ResidentDates.ResidentFromGyearMonth',
            'PreviousAddress.*.ResidentDates.ResidentToGyearMonth',
            'OtherSurnames.*.Name',
            'OtherSurnames.*.UsedFrom',
            'OtherSurnames.*.UsedTo',
            'OtherForenames.*.Name',
            'OtherForenames.*.UsedFrom',
            'OtherForenames.*.UsedTo',
        ];

        foreach ($arrayPatterns as $pattern) {
            $regex = str_replace('*', '\d+', preg_quote($pattern, '/'));
            $regex = str_replace('\\\\d\\+', '\\d+', $regex);
            if (preg_match('/^' . $regex . '$/', $field)) {
                return true;
            }
        }

        return false;
    }


}
