<?php

declare(strict_types=1);

namespace App\Modules\Applications\Background\DBS\ViewModels;

use App\Modules\Applications\Background\DBS\Models\DBSApplication;

/**
 * DBS Application View Model
 * 
 * Handles presentation logic for DBS applications
 */
class DBSApplicationViewModel
{
    protected DBSApplication $application;

    public function __construct(DBSApplication $application)
    {
        $this->application = $application;
    }

    /**
     * Get formatted application data for API response
     */
    public function toApiResponse(): array
    {
        return [
            'id' => $this->application->id,
            'external_reference' => $this->application->external_reference,
            'status' => $this->application->status,
            'result' => $this->application->result,
            'consent_date' => $this->application->consent_date?->format('Y-m-d'),
            'created_at' => $this->application->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->application->updated_at->format('Y-m-d H:i:s'),
            'product' => [
                'id' => $this->application->product->id,
                'name' => $this->application->product->name,
                'code' => $this->application->product->code,
                'description' => $this->application->product->description,
                'is_dbs_enhanced' => $this->application->isDBSEnhanced(),
            ],
            'applicant' => [
                'id' => $this->application->applicant->id,
                'email' => $this->application->applicant->email,
                'name' => $this->application->applicant->profile 
                    ? trim($this->application->applicant->profile->first_name . ' ' . $this->application->applicant->profile->last_name)
                    : null,
            ],
            'submitted_by' => [
                'id' => $this->application->submittedBy->id,
                'email' => $this->application->submittedBy->email,
                'name' => $this->application->submittedBy->profile
                    ? trim($this->application->submittedBy->profile->first_name . ' ' . $this->application->submittedBy->profile->last_name)
                    : null,
            ],
        ];
    }

    /**
     * Get detailed application data including form data
     */
    public function toDetailedApiResponse(): array
    {
        $basicData = $this->toApiResponse();
        
        $basicData['form_data'] = $this->application->getDBSApplicationData();
        
        // Add form fields configuration if available
        if ($this->application->product && $this->application->product->formFields) {
            $basicData['product']['form_fields'] = $this->application->product->formFields->map(function ($field) {
                return [
                    'field_name' => $field->field_name,
                    'field_label' => $field->field_label,
                    'field_type' => $field->field_type,
                    'field_options' => $field->field_options,
                    'is_required' => $field->is_required,
                    'sort_order' => $field->sort_order,
                ];
            })->toArray();
        }

        return $basicData;
    }

    /**
     * Get validation summary for the application
     */
    public function getValidationSummary(): array
    {
        // This would typically use the validation service
        // For now, return basic structure
        return [
            'is_complete' => false, // Would be calculated
            'completion_percentage' => 0, // Would be calculated
            'missing_required_fields' => [], // Would be calculated
            'validation_errors' => [], // Would be calculated
        ];
    }

    /**
     * Get form sections with completion status
     */
    public function getFormSections(): array
    {
        $sections = [
            'personal_details' => [
                'name' => 'Personal Details',
                'fields' => ['Title', 'Forename', 'PresentSurname', 'DateOfBirth', 'Gender'],
                'is_complete' => false,
            ],
            'current_address' => [
                'name' => 'Current Address',
                'fields' => [
                    'CurrentAddress.Address.AddressLine1',
                    'CurrentAddress.Address.AddressTown',
                    'CurrentAddress.Address.CountryCode',
                    'CurrentAddress.ResidentFromGyearMonth'
                ],
                'is_complete' => false,
            ],
            'birth_details' => [
                'name' => 'Birth Details',
                'fields' => ['BirthTown', 'BirthCountry'],
                'is_complete' => false,
            ],
            'declarations' => [
                'name' => 'Declarations',
                'fields' => [
                    'UnspentConvictions',
                    'DeclarationByApplicant',
                    'LanguagePreference',
                    'IdentityVerified'
                ],
                'is_complete' => false,
            ],
            'employer_details' => [
                'name' => 'Employer Details',
                'fields' => [
                    'PotentialEmployerDetails.PositionAppliedFor',
                    'PotentialEmployerDetails.OrganisationName'
                ],
                'is_complete' => false,
            ],
            'rb_details' => [
                'name' => 'Registered Body Details',
                'fields' => [
                    'RBdetails.RBApplicationReference',
                    'RBdetails.RBNumber',
                    'RBdetails.CSigNumber'
                ],
                'is_complete' => false,
            ],
        ];

        // Add DBS Enhanced specific sections
        if ($this->application->isDBSEnhanced()) {
            $sections['additional_names'] = [
                'name' => 'Additional Names',
                'fields' => ['BirthSurname', 'OtherSurnames', 'OtherForenames'],
                'is_complete' => false,
            ];
            
            $sections['identity_documents'] = [
                'name' => 'Identity Documents',
                'fields' => ['PassportDetails', 'DriverLicenceDetails'],
                'is_complete' => false,
            ];
        }

        return $sections;
    }

    /**
     * Get application progress percentage
     */
    public function getProgressPercentage(): int
    {
        // This would calculate based on completed required fields
        // For now, return 0
        return 0;
    }

    /**
     * Check if application can be submitted
     */
    public function canBeSubmitted(): bool
    {
        // This would check if all required fields are completed and valid
        // For now, return false
        return false;
    }
}
