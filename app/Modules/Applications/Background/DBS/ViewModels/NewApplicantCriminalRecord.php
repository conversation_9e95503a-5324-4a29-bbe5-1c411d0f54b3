<?php

declare(strict_types=1);

namespace App\Modules\Applications\Background\DBS\ViewModels;

use Livewire\Component;
use App\Services\NewProcessStampService;
use App\Services\ProcessStampFieldService;
use App\Models\ProcessStampMain;
use App\Models\ProcessedStamp;

class NewApplicantCriminalRecord extends Component
{
    public ?int $selectedApplicationId = null;
    public ?int $productId = null;
    public array $processStamps = [];
    public bool $showStampModal = false;
    public ?int $currentStampId = null;
    public array $fieldData = [];

    public function __construct(
        private NewProcessStampService $stampService,
        private ProcessStampFieldService $fieldService
    ) {
        parent::__construct();
    }

    public function mount(?int $applicationId = null, ?int $productId = null): void
    {
        $this->selectedApplicationId = $applicationId;
        $this->productId = $productId;
        
        if ($applicationId && $productId) {
            $this->loadProcessStamps();
        }
    }

    public function render()
    {
        return view('applications.background.dbs.new-criminal-record', [
            'processStamps' => $this->processStamps,
            'progress' => $this->getProgress()
        ]);
    }

    public function loadProcessStamps(): void
    {
        if (!$this->selectedApplicationId || !$this->productId) {
            return;
        }

        $this->stampService->initializeStampsForApplication(
            $this->selectedApplicationId, 
            $this->productId
        );

        $this->processStamps = $this->stampService
            ->getStampsForApplication($this->selectedApplicationId, $this->productId)
            ->toArray();
    }

    public function openStampModal(int $stampId): void
    {
        $this->currentStampId = $stampId;
        $this->fieldData = [];
        
        $processedStamp = ProcessedStamp::forApplication($this->selectedApplicationId)
            ->forStamp($stampId)
            ->first();

        if ($processedStamp && $processedStamp->field_data) {
            $this->fieldData = $processedStamp->field_data;
        }

        $this->showStampModal = true;
    }

    public function closeStampModal(): void
    {
        $this->showStampModal = false;
        $this->currentStampId = null;
        $this->fieldData = [];
    }

    public function completeStamp(): void
    {
        if (!$this->currentStampId || !$this->selectedApplicationId) {
            return;
        }

        $processStamp = ProcessStampMain::find($this->currentStampId);
        
        if ($processStamp && $processStamp->hasField()) {
            $validationErrors = $this->fieldService->validateFieldData($processStamp, $this->fieldData);
            
            if (!empty($validationErrors)) {
                foreach ($validationErrors as $error) {
                    $this->dispatch('toastify', [
                        'type' => 'error',
                        'message' => $error
                    ]);
                }
                return;
            }
        }

        $success = $this->stampService->completeStamp(
            $this->selectedApplicationId,
            $this->currentStampId,
            auth()->id() ?? 1,
            $this->fieldData
        );

        if ($success) {
            $this->dispatch('toastify', [
                'type' => 'success',
                'message' => 'Process stamp completed successfully!'
            ]);

            $this->loadProcessStamps();
            $this->closeStampModal();
        } else {
            $this->dispatch('toastify', [
                'type' => 'error',
                'message' => 'Failed to complete process stamp.'
            ]);
        }
    }

    public function skipStamp(int $stampId): void
    {
        if (!$this->selectedApplicationId) {
            return;
        }

        $success = $this->stampService->skipStamp(
            $this->selectedApplicationId,
            $stampId,
            auth()->id() ?? 1
        );

        if ($success) {
            $this->dispatch('toastify', [
                'type' => 'info',
                'message' => 'Process stamp skipped.'
            ]);

            $this->loadProcessStamps();
        } else {
            $this->dispatch('toastify', [
                'type' => 'error',
                'message' => 'Failed to skip process stamp.'
            ]);
        }
    }

    public function resetStamp(int $stampId): void
    {
        if (!$this->selectedApplicationId) {
            return;
        }

        $success = $this->stampService->resetStamp(
            $this->selectedApplicationId,
            $stampId,
            auth()->id() ?? 1
        );

        if ($success) {
            $this->dispatch('toastify', [
                'type' => 'info',
                'message' => 'Process stamp reset to pending.'
            ]);

            $this->loadProcessStamps();
        } else {
            $this->dispatch('toastify', [
                'type' => 'error',
                'message' => 'Failed to reset process stamp.'
            ]);
        }
    }

    public function getProgress(): array
    {
        if (!$this->selectedApplicationId) {
            return [
                'total' => 0,
                'completed' => 0,
                'skipped' => 0,
                'pending' => 0,
                'completion_percentage' => 0
            ];
        }

        return $this->stampService->getApplicationProgress($this->selectedApplicationId);
    }

    public function getStampsByCategory(): array
    {
        $categories = [
            'initial' => 'Initial Processing',
            'document' => 'Document Processing',
            'remedial' => 'Remedial Actions',
            'final' => 'Final Processing',
            'completion' => 'Completion'
        ];

        $grouped = [];
        
        foreach ($categories as $key => $label) {
            $grouped[$key] = [
                'label' => $label,
                'stamps' => collect($this->processStamps)->filter(function ($item) use ($key) {
                    return $item['stamp']->CATEGORY === $key;
                })->values()->toArray()
            ];
        }

        return $grouped;
    }

    public function getCurrentStamp(): ?ProcessStampMain
    {
        if (!$this->currentStampId) {
            return null;
        }

        return ProcessStampMain::find($this->currentStampId);
    }

    public function getFieldConfiguration(): array
    {
        $stamp = $this->getCurrentStamp();
        
        if (!$stamp) {
            return [];
        }

        return $this->fieldService->getFieldConfiguration($stamp);
    }
}
