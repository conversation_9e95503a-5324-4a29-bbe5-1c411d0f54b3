<?php

declare(strict_types=1);

namespace App\Modules\Applications\Background\DBS\Providers;

use Illuminate\Support\ServiceProvider;
use App\Modules\Applications\Background\DBS\Repositories\DBSRepositoryInterface;
use App\Modules\Applications\Background\DBS\Repositories\DBSRepository;
use App\Modules\Applications\Background\DBS\Services\DBSValidationService;
use App\Modules\Applications\Background\DBS\Services\DBSApplicationService;
use SolidFuse\Modules\Applications\Services\DBSEnhancedValidationService;

/**
 * DBS Service Provider
 * 
 * Registers DBS module services and bindings
 */
class DBSServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Bind repository interface to implementation
        $this->app->bind(DBSRepositoryInterface::class, DBSRepository::class);

        // Register DBS validation service
        $this->app->singleton(DBSValidationService::class, function ($app) {
            try {
                $solidFuseService = $app->make(DBSEnhancedValidationService::class);
                return new DBSValidationService($solidFuseService);
            } catch (\Exception $e) {
                // If SolidFuse service is not available, pass null
                // The service will handle this and use basic validation
                return new DBSValidationService(null);
            }
        });

        // Register DBS application service
        $this->app->singleton(DBSApplicationService::class, function ($app) {
            return new DBSApplicationService(
                $app->make(DBSRepositoryInterface::class),
                $app->make(DBSValidationService::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Module-specific bootstrapping can be added here
        // For example: loading routes, views, migrations, etc.
    }
}
