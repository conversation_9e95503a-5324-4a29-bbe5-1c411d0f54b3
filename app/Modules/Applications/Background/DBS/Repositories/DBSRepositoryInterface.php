<?php

declare(strict_types=1);

namespace App\Modules\Applications\Background\DBS\Repositories;

use App\Modules\Applications\Background\DBS\Models\DBSApplication;

/**
 * DBS Repository Interface
 * 
 * Defines the contract for DBS application data access
 */
interface DBSRepositoryInterface
{
    /**
     * Find DBS application by ID
     */
    public function findById(int $id): ?DBSApplication;

    /**
     * Find DBS applications by applicant ID
     */
    public function findByApplicantId(int $applicantId): array;

    /**
     * Find DBS Enhanced applications
     */
    public function findDBSEnhancedApplications(): array;

    /**
     * Save DBS application data
     */
    public function saveApplicationData(DBSApplication $application, array $data): bool;

    /**
     * Get DBS application validation errors
     */
    public function getValidationErrors(DBSApplication $application): array;

    /**
     * Check if DBS application is complete
     */
    public function isApplicationComplete(DBSApplication $application): bool;

    /**
     * Get required fields for DBS application
     */
    public function getRequiredFields(DBSApplication $application): array;
}
