<?php

declare(strict_types=1);

namespace App\Modules\Applications\Background\DBS\Repositories;

use App\Modules\Applications\Background\DBS\Models\DBSApplication;
use App\Modules\Applications\Background\DBS\Services\DBSValidationService;
use Illuminate\Support\Facades\DB;

/**
 * DBS Repository Implementation
 * 
 * Handles data access for DBS applications
 */
class DBSRepository implements DBSRepositoryInterface
{
    protected DBSValidationService $validationService;

    public function __construct(DBSValidationService $validationService)
    {
        $this->validationService = $validationService;
    }

    /**
     * Find DBS application by ID
     */
    public function findById(int $id): ?DBSApplication
    {
        return DBSApplication::find($id);
    }

    /**
     * Find DBS applications by applicant ID
     */
    public function findByApplicantId(int $applicantId): array
    {
        return DBSApplication::where('applicant_id', $applicantId)->get()->toArray();
    }

    /**
     * Find DBS Enhanced applications
     */
    public function findDBSEnhancedApplications(): array
    {
        return DBSApplication::where('product_id', 3)->get()->toArray();
    }

    /**
     * Save DBS application data
     */
    public function saveApplicationData(DBSApplication $application, array $data): bool
    {
        try {
            // Validate the data first
            $validatedData = $this->validationService->validateFormData($data, $application->product_id);
            
            return DB::transaction(function () use ($application, $validatedData) {
                $application->setDBSApplicationData($validatedData);
                return true;
            });
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get DBS application validation errors
     */
    public function getValidationErrors(DBSApplication $application): array
    {
        $data = $application->getDBSApplicationData();
        
        try {
            $this->validationService->validateFormData($data, $application->product_id);
            return [];
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $e->errors();
        }
    }

    /**
     * Check if DBS application is complete
     */
    public function isApplicationComplete(DBSApplication $application): bool
    {
        $data = $application->getDBSApplicationData();
        
        try {
            $this->validationService->validateFormData($data, $application->product_id);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get required fields for DBS application
     */
    public function getRequiredFields(DBSApplication $application): array
    {
        return $this->validationService->getAllowedFieldNames($application->product_id);
    }
}
