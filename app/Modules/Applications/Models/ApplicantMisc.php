<?php

declare(strict_types=1);

namespace App\Modules\Applications\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Modules\Auth\Models\PortalUser;
use App\Modules\Entities\Models\Entity;

class ApplicantMisc extends Model
{
    protected $table = 'applicant_misc';

    protected $fillable = [
        'applicant_id',
        'entityid',
        'applicantstatus',
        'employmentstatus',
        'applicant_comment',
        'resigned',
        'resigned_by',
        'resigned_date',
        'account_delete_date',
        'withdrawn_migrated_date',
        'withdrawn_data_version',
        'created_by'
    ];

    protected $casts = [
        'applicantstatus' => 'integer',
        'employmentstatus' => 'integer',
        'resigned' => 'boolean',
        'resigned_date' => 'date',
        'account_delete_date' => 'date',
        'withdrawn_migrated_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function applicant(): BelongsTo
    {
        return $this->belongsTo(PortalUser::class, 'applicant_id');
    }

    public function entity(): BelongsTo
    {
        return $this->belongsTo(Entity::class, 'entityid');
    }
}
