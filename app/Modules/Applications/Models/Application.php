<?php

declare(strict_types=1);

namespace App\Modules\Applications\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Modules\Auth\Models\PortalUser;
use App\Modules\Products\Models\Product;

class Application extends Model
{
    protected $table = 'applications';

    protected $fillable = [
        'applicant_id',
        'product_id',
        'status',
        'result',
        'submitted_by',
        'external_reference',
        'consent_date',
        'consent_user',
        'current_route'
    ];

    protected $casts = [
        'consent_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function applicant(): BelongsTo
    {
        return $this->belongsTo(PortalUser::class, 'applicant_id');
    }

    public function submittedBy(): BelongsTo
    {
        return $this->belongsTo(PortalUser::class, 'submitted_by');
    }

    public function consentUser(): BelongsTo
    {
        return $this->belongsTo(PortalUser::class, 'consent_user');
    }



    public function applicationData(): HasMany
    {
        return $this->hasMany(ApplicationData::class, 'application_id');
    }

    public function billingSnapshot(): HasMany
    {
        return $this->hasMany(\App\Modules\Billing\Models\ApplicationBillingSnapshot::class, 'application_id');
    }

    public function docDafData(): HasMany
    {
        return $this->hasMany(\App\Modules\Documents\Models\DocDafData::class, 'application_id');
    }



    public function getApplicationDataArray(): array
    {
        return $this->applicationData->pluck('value', 'field_key')->toArray();
    }

    public function setApplicationData(array $data): void
    {
        $this->applicationData()->delete();

        foreach ($data as $fieldKey => $value) {
            $this->applicationData()->create([
                'field_key' => $fieldKey,
                'value' => $value,
            ]);
        }
    }
}
