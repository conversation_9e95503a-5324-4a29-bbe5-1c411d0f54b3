<?php

declare(strict_types=1);

namespace App\Modules\Applications\Services;

use App\Modules\Applications\Models\Application;
use App\Modules\Billing\Models\ApplicationBillingSnapshot;
use App\Modules\Applications\Models\ApplicantMisc;
use App\Modules\Auth\Models\PortalUser;
use App\Modules\Entities\SubModules\JobRoles\Models\JobRole;
use App\Modules\Users\Models\Profile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use SolidFuse\Modules\Billing\Services\BillingService;

class ApplicantService
{
    private BillingService $billingService;

    public function __construct(BillingService $billingService)
    {
        $this->billingService = $billingService;
    }

    /**
     * Create a new applicant with applications and billing snapshots
     */
    public function createApplicant(array $data, PortalUser $createdBy): array
    {
        return DB::transaction(function () use ($data, $createdBy) {
            // Get job role with entity and products
            $jobRole = JobRole::with(['entity', 'products'])->findOrFail($data['job_role_id']);

            // Ensure job role has products associated
            if ($jobRole->products->isEmpty()) {
                throw new \Exception('Selected job role has no products associated with it');
            }

            // Generate a random password for the applicant
            $password = Str::random(12);

            // Create the applicant user
            $applicant = PortalUser::create([
                'email' => $data['email'],
                'password' => Hash::make($password),
                'user_type' => 'applicant'
            ]);

            // Create the profile
            Profile::create([
                'user_id' => $applicant->id,
                'first_name' => $data['first_name'],
                'last_name' => $data['last_name'],
                'telephone' => $data['phone'],
                'active' => true,
                'two_factor_enabled' => false,
            ]);

            // Create applicant_misc record to link applicant to entity
            ApplicantMisc::create([
                'applicant_id' => $applicant->id,
                'entityid' => $jobRole->entity_id,
                'applicantstatus' => 1, // 1 = active
                'employmentstatus' => 1, // 1 = pending
                'created_by' => $createdBy->id,
            ]);

            // Create applications for all products associated with the job role
            $applications = [];
            $billingSnapshots = [];

            foreach ($jobRole->products as $product) {
                $application = Application::create([
                    'applicant_id' => $applicant->id,
                    'product_id' => $product->id,
                    'status' => 'draft',
                    'submitted_by' => $createdBy->id,
                ]);

                $applications[] = $application;

                // Create billing snapshot for this application
                $billingSnapshot = $this->createBillingSnapshot(
                    $application,
                    $product->id,
                    $jobRole
                );

                $billingSnapshots[] = $billingSnapshot;
            }

            return [
                'applicant' => $applicant,
                'profile' => $applicant->profile,
                'applicant_misc' => $applicant->fresh()->load('applicantMisc'),
                'applications' => $applications,
                'billing_snapshots' => $billingSnapshots,
                'job_role' => $jobRole,
                'entity' => $jobRole->entity,
                'generated_password' => $password,
            ];
        });
    }

    /**
     * Create billing snapshot for an application
     */
    private function createBillingSnapshot(Application $application, int $productId, JobRole $jobRole): ApplicationBillingSnapshot
    {
        $entity = $jobRole->entity;

        // Get all product pricing for the entity using SolidFuse BillingService
        $allProductPricing = $this->billingService->getProductPricingForEntity($entity->id);

        // Find pricing info for this specific product
        $pricingInfo = $allProductPricing->firstWhere('product_id', $productId);

        // Extract pricing data with fallbacks
        $adminFee = $pricingInfo['admin_fee'] ?? 0;
        $supplierFee = $pricingInfo['supplier_fee'] ?? 0;
        $selfPayment = $jobRole->self_payment ?? true;

        return ApplicationBillingSnapshot::create([
            'application_id' => $application->id,
            'admin_fee' => $adminFee,
            'supplier_fee' => $supplierFee,
            'self_payment' => $selfPayment,
        ]);
    }

    /**
     * Check if user can create applicants for the given job role
     */
    public function canUserCreateApplicantForJobRole(PortalUser $user, int $jobRoleId): bool
    {
        // Only client users can create applicants
        if (!in_array($user->user_type, ['client_user', 'requester', 'doc_checker'])) {
            return false;
        }

        $jobRole = JobRole::find($jobRoleId);
        if (!$jobRole) {
            return false;
        }

        // Check if user has access to the job role's entity
        $userEntityIds = $user->entities()->where('entities.status', true)->pluck('entities.id')->toArray();
        $accessibleEntityIds = $this->getAllAccessibleEntityIds($userEntityIds);

        return in_array($jobRole->entity_id, $accessibleEntityIds);
    }

    /**
     * Get all accessible entity IDs including hierarchy
     */
    private function getAllAccessibleEntityIds(array $userEntityIds): array
    {
        $accessibleEntityIds = collect($userEntityIds);

        // Add child entities (entities that are children of the user's entities)
        foreach ($userEntityIds as $entityId) {
            $entity = \App\Modules\Entities\Models\Entity::find($entityId);
            if ($entity) {
                $childIds = $entity->childEntities()->where('entities.status', true)->pluck('entities.id');
                $accessibleEntityIds = $accessibleEntityIds->merge($childIds);
            }
        }

        return $accessibleEntityIds->unique()->toArray();
    }
}
