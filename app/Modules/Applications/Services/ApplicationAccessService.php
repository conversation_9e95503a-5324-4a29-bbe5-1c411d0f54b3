<?php

declare(strict_types=1);

namespace App\Modules\Applications\Services;

use App\Modules\Auth\Models\PortalUser;
use App\Modules\Entities\Models\Entity;
use Illuminate\Support\Facades\DB;

class ApplicationAccessService
{
    /**
     * Check if a user can access applications for a given applicant
     * Uses entity hierarchy to determine access rights
     */
    public function canUserAccessApplicantApplications(PortalUser $user, PortalUser $applicant): bool
    {
        // If user is the applicant themselves, they can access their own applications
        if ($user->id === $applicant->id && $user->user_type === 'applicant') {
            return true;
        }

        // If user is not a client user type, deny access
        if (!in_array($user->user_type, ['client_user', 'requester', 'doc_checker'])) {
            return false;
        }

        // For client users, check if they have access to the applicant's entity
        return $this->canClientUserAccessApplicant($user, $applicant->id);
    }

    /**
     * Get all entity IDs that a user can access (including hierarchy)
     * Enhanced with SolidFuse integration for proper hierarchy traversal
     */
    public function getUserAccessibleEntityIds(PortalUser $user): array
    {
        // Get direct entity associations
        $directEntityIds = $user->entities()->where('entities.status', true)->pluck('entities.id')->toArray();

        $allAccessibleIds = $directEntityIds;

        // For each entity the user is associated with, get all child entities using SolidFuse
        foreach ($directEntityIds as $entityId) {
            // Use SolidFuse EntityHelper to get child entities
            $childEntities = \SolidFuse\Modules\Entities\Helpers\EntityHelper::getChildEntities($entityId);
            $childEntityIds = $childEntities->pluck('id')->toArray();
            $allAccessibleIds = array_merge($allAccessibleIds, $childEntityIds);

            // Recursively get children of children for complete hierarchy
            $this->addChildEntitiesRecursively($childEntityIds, $allAccessibleIds);
        }

        return array_unique($allAccessibleIds);
    }



    /**
     * Recursively add child entities to the accessible IDs array
     * Uses SolidFuse EntityHelper for consistent hierarchy traversal
     */
    private function addChildEntitiesRecursively(array $entityIds, array &$allAccessibleIds): void
    {
        foreach ($entityIds as $entityId) {
            $childEntities = \SolidFuse\Modules\Entities\Helpers\EntityHelper::getChildEntities($entityId);
            $childEntityIds = $childEntities->pluck('id')->toArray();

            if (!empty($childEntityIds)) {
                $allAccessibleIds = array_merge($allAccessibleIds, $childEntityIds);
                // Recursively process children of children
                $this->addChildEntitiesRecursively($childEntityIds, $allAccessibleIds);
            }
        }
    }

    /**
     * Get applications that a user can access based on role
     */
    public function getAccessibleApplicationsQuery(PortalUser $user)
    {
        if ($user->user_type === 'applicant') {
            // Applicants can only see their own applications
            return DB::table('applications')->where('applicant_id', $user->id);
        }

        // For client users, get applications where they have access to the applicant's entity
        if (in_array($user->user_type, ['client_user', 'requester', 'doc_checker'])) {
            $accessibleEntityIds = $this->getUserAccessibleEntityIds($user);

            // Get applicant IDs that are associated with accessible entities from applicant_misc table
            $accessibleApplicantIds = [];

            if (!empty($accessibleEntityIds)) {
                $accessibleApplicantIds = DB::table('applicant_misc')
                    ->whereIn('entityid', $accessibleEntityIds)
                    ->pluck('applicant_id')
                    ->toArray();
            }

            // Also include applicants who are not in applicant_misc table (not yet assigned to entities)
            $unassociatedApplicantIds = DB::table('portal_users')
                ->leftJoin('applicant_misc', 'portal_users.id', '=', 'applicant_misc.applicant_id')
                ->where('portal_users.user_type', 'applicant')
                ->whereNull('applicant_misc.applicant_id')
                ->pluck('portal_users.id')
                ->toArray();

            $allAccessibleApplicantIds = array_merge($accessibleApplicantIds, $unassociatedApplicantIds);

            if (empty($allAccessibleApplicantIds)) {
                // If no accessible applicants, return empty query
                return DB::table('applications')->whereRaw('1 = 0');
            }

            return DB::table('applications')->whereIn('applicant_id', $allAccessibleApplicantIds);
        }

        // For other user types, deny access
        return DB::table('applications')->whereRaw('1 = 0');
    }

    /**
     * Check if user can access a specific application
     */
    public function canUserAccessApplication(PortalUser $user, int $applicationId): bool
    {
        $application = DB::table('applications')->where('id', $applicationId)->first();

        if (!$application) {
            return false;
        }

        if ($user->user_type === 'applicant') {
            // Applicants can only access their own applications
            return $application->applicant_id == $user->id;
        }

        // For client users, check if they have access to the applicant's entity
        if (in_array($user->user_type, ['client_user', 'requester', 'doc_checker'])) {
            return $this->canClientUserAccessApplicant($user, $application->applicant_id);
        }

        return false;
    }

    /**
     * Check if a client user can access an applicant based on entity relationships
     */
    public function canClientUserAccessApplicant(PortalUser $clientUser, int $applicantId): bool
    {
        // Get the applicant
        $applicant = PortalUser::find($applicantId);
        if (!$applicant || $applicant->user_type !== 'applicant') {
            return false;
        }

        // Get entity ID from applicant_misc table
        $applicantMisc = DB::table('applicant_misc')
            ->where('applicant_id', $applicantId)
            ->first();

        // If applicant is not in applicant_misc table, allow access for now
        // This handles cases where applicants are created but not yet assigned to entities
        if (!$applicantMisc) {
            return true;
        }

        $applicantEntityId = $applicantMisc->entityid;

        // Get all entity IDs that the client user can access (including hierarchy)
        $clientUserAccessibleEntityIds = $this->getUserAccessibleEntityIds($clientUser);

        // Check if the applicant's entity is in the client user's accessible entities
        return in_array($applicantEntityId, $clientUserAccessibleEntityIds);
    }

    /**
     * Get entity hierarchy for a given entity
     */
    public function getEntityHierarchy(int $entityId): array
    {
        $entity = Entity::find($entityId);

        if (!$entity) {
            return ['current' => null, 'parent' => null, 'super_group' => null];
        }

        $parent = null;
        $superGroup = null;

        // Find parent based on entity type and relationships
        if ($entity->entity_type->value === 'client') {
            // For clients, find parent_group
            $parentRelation = DB::table('entity_relationships')
                ->where('child_entity_id', $entityId)
                ->first();

            if ($parentRelation) {
                $parent = Entity::find($parentRelation->parent_entity_id);

                // If parent is parent_group, find its super_group
                if ($parent && $parent->entity_type->value === 'parent_group') {
                    $superRelation = DB::table('entity_relationships')
                        ->where('child_entity_id', $parent->id)
                        ->first();

                    if ($superRelation) {
                        $superGroup = Entity::find($superRelation->parent_entity_id);
                    }
                }
            }
        } elseif ($entity->entity_type->value === 'parent_group') {
            // For parent_groups, find super_group
            $superRelation = DB::table('entity_relationships')
                ->where('child_entity_id', $entityId)
                ->first();

            if ($superRelation) {
                $superGroup = Entity::find($superRelation->parent_entity_id);
            }
        }

        return [
            'current' => $entity,
            'parent' => $parent,
            'super_group' => $superGroup
        ];
    }

    /**
     * Get all accessible applicants with status summary for client users
     * Enhanced with SolidFuse integration, lazy loading, and improved performance
     * Returns status summary cards and detailed applicant list with search and filtering
     */
    public function getClientApplicantsWithStatusSummary(
        PortalUser $user,
        ?string $searchTerm = null,
        string $statusFilter = 'all',
        int $page = 1,
        int $perPage = 20
    ): array
    {
        // Validate user type - only client users can access this functionality
        if (!in_array($user->user_type, ['client_user', 'requester', 'doc_checker'])) {
            throw new \Exception('Access denied for this user type');
        }

        // Get all accessible entity IDs for the user using enhanced SolidFuse integration
        $accessibleEntityIds = $this->getUserAccessibleEntityIds($user);

        if (empty($accessibleEntityIds)) {
            return $this->getEmptyApplicantsResponse();
        }

        // Build optimized query with lazy loading for better performance
        $applicantsQuery = $this->buildApplicantsQuery($accessibleEntityIds, $searchTerm);

        // Get total count for pagination before applying limit
        $totalCount = $applicantsQuery->count();

        if ($totalCount === 0) {
            return $this->getEmptyApplicantsResponse();
        }

        // Apply pagination to the query for lazy loading
        $offset = ($page - 1) * $perPage;
        $applicants = $applicantsQuery->offset($offset)->limit($perPage)->get();

        // Get applications for these applicants with status information (lazy loaded)
        $applicantIds = $applicants->pluck('applicant_id')->toArray();

        // Get applications with product information using optimized query
        $applications = $this->getApplicationsForApplicants($applicantIds);

        // Group applications by applicant
        $applicationsByApplicant = $applications->groupBy('applicant_id');

        // Calculate status summary for all accessible applicants (not just current page)
        $statusCounts = $this->calculateStatusSummary($accessibleEntityIds, $searchTerm);

        // Transform applicant data for current page
        $transformedApplicants = $this->transformApplicantsData(
            $applicants,
            $applicationsByApplicant,
            $statusFilter
        );

        // Convert to numbered object keys as per user preference
        $numberedApplicants = [];
        foreach ($transformedApplicants as $index => $applicant) {
            $numberedApplicants[(string)$index] = $applicant;
        }

        return [
            'status_summary' => $statusCounts,
            'applicants' => $numberedApplicants,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $totalCount,
                'total_pages' => ceil($totalCount / $perPage),
                'has_more' => ($page * $perPage) < $totalCount
            ]
        ];
    }

    /**
     * Determine overall applicant status based on their applications
     */
    private function determineApplicantOverallStatus($applications): string
    {
        if ($applications->isEmpty()) {
            return 'in_progress'; // Default for applicants with no applications
        }

        $statuses = $applications->pluck('status')->toArray();

        // If all applications are completed, applicant is complete
        if (collect($statuses)->every(fn($status) => $status === 'completed')) {
            return 'complete';
        }

        // If any application is in progress or pending, applicant is in progress
        if (collect($statuses)->contains(fn($status) => in_array($status, ['in_progress', 'pending', 'draft']))) {
            return 'in_progress';
        }

        // If any application requires further action (rejected, requires_action)
        if (collect($statuses)->contains(fn($status) => in_array($status, ['rejected', 'requires_action', 'incomplete']))) {
            return 'further_action_pending';
        }

        // If any application is under review
        if (collect($statuses)->contains(fn($status) => in_array($status, ['under_review', 'reviewing', 'submitted']))) {
            return 'staff_review_pending';
        }

        // Default fallback
        return 'in_progress';
    }

    /**
     * Get display name for status
     */
    private function getStatusDisplayName(string $status): string
    {
        return match ($status) {
            'in_progress' => 'In Progress',
            'further_action_pending' => 'Further Action Pending',
            'staff_review_pending' => 'Staff Review Pending',
            'complete' => 'Complete',
            default => 'In Progress'
        };
    }

    /**
     * Build optimized applicants query with search functionality
     * Handles both applicant_misc and entity_user_links associations
     */
    private function buildApplicantsQuery(array $accessibleEntityIds, ?string $searchTerm = null)
    {
        // First, get applicants from applicant_misc table (new applicants)
        $applicantMiscQuery = DB::table('portal_users as pu')
            ->join('portal_user_profiles as p', 'pu.id', '=', 'p.user_id')
            ->join('applicant_misc as am', 'pu.id', '=', 'am.applicant_id')
            ->join('entities as e', 'am.entityid', '=', 'e.id')
            ->where('pu.user_type', 'applicant')
            ->where('p.active', true)
            ->whereIn('am.entityid', $accessibleEntityIds)
            ->select([
                'pu.id as applicant_id',
                'pu.email',
                'pu.created_at as date_created',
                'p.first_name',
                'p.last_name',
                'e.id as entity_id',
                'e.name as organization_name',
                DB::raw('am.applicantstatus'),
                DB::raw('am.employmentstatus'),
                DB::raw("'applicant_misc' as source")
            ]);

        // Second, get applicants from entity_user_links table (existing/seeded applicants)
        $entityLinksQuery = DB::table('portal_users as pu')
            ->join('portal_user_profiles as p', 'pu.id', '=', 'p.user_id')
            ->join('entity_user_links as eul', 'pu.id', '=', 'eul.user_id')
            ->join('entities as e', 'eul.entity_id', '=', 'e.id')
            ->leftJoin('applicant_misc as am_check', 'pu.id', '=', 'am_check.applicant_id')
            ->where('pu.user_type', 'applicant')
            ->where('p.active', true)
            ->whereIn('eul.entity_id', $accessibleEntityIds)
            ->whereNull('am_check.applicant_id') // Exclude those already in applicant_misc
            ->select([
                'pu.id as applicant_id',
                'pu.email',
                'pu.created_at as date_created',
                'p.first_name',
                'p.last_name',
                'e.id as entity_id',
                'e.name as organization_name',
                DB::raw('1 as applicantstatus'), // Default status
                DB::raw('1 as employmentstatus'), // Default status
                DB::raw("'entity_links' as source")
            ]);

        // Union both queries
        $unionQuery = $applicantMiscQuery->union($entityLinksQuery);

        // Create a subquery to apply search and ordering
        $finalQuery = DB::table(DB::raw("({$unionQuery->toSql()}) as applicants"))
            ->mergeBindings($unionQuery);

        // Apply search filter if provided
        if (!empty($searchTerm)) {
            $finalQuery->where(function ($subQuery) use ($searchTerm) {
                $subQuery->where('first_name', 'LIKE', "%{$searchTerm}%")
                    ->orWhere('last_name', 'LIKE', "%{$searchTerm}%")
                    ->orWhere('email', 'LIKE', "%{$searchTerm}%")
                    ->orWhere('organization_name', 'LIKE', "%{$searchTerm}%")
                    ->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%{$searchTerm}%"]);
            });
        }

        return $finalQuery->orderBy('date_created', 'desc');
    }

    /**
     * Get empty response structure for applicants
     */
    private function getEmptyApplicantsResponse(): array
    {
        return [
            'status_summary' => [
                'total_applicants' => 0,
                'in_progress' => 0,
                'further_action_pending' => 0,
                'staff_review_pending' => 0,
                'complete' => 0
            ],
            'applicants' => [],
            'pagination' => [
                'current_page' => 1,
                'per_page' => 20,
                'total' => 0,
                'total_pages' => 0,
                'has_more' => false
            ]
        ];
    }

    /**
     * Get applications for specific applicants with optimized query
     */
    private function getApplicationsForApplicants(array $applicantIds)
    {
        if (empty($applicantIds)) {
            return collect();
        }

        return DB::table('applications as a')
            ->join('products as pr', 'a.product_id', '=', 'pr.id')
            ->whereIn('a.applicant_id', $applicantIds)
            ->select([
                'a.applicant_id',
                'a.id as application_id',
                'a.status',
                'a.result',
                'a.created_at',
                'pr.name as product_name',
                'pr.code as product_code'
            ])
            ->orderBy('a.created_at', 'desc')
            ->get();
    }

    /**
     * Calculate status summary for all accessible applicants
     */
    private function calculateStatusSummary(array $accessibleEntityIds, ?string $searchTerm = null): array
    {
        // Get all applicants (not paginated) for status summary
        $allApplicantsQuery = $this->buildApplicantsQuery($accessibleEntityIds, $searchTerm);
        $allApplicants = $allApplicantsQuery->get();

        $statusCounts = [
            'total_applicants' => $allApplicants->count(),
            'in_progress' => 0,
            'further_action_pending' => 0,
            'staff_review_pending' => 0,
            'complete' => 0
        ];

        if ($allApplicants->isEmpty()) {
            return $statusCounts;
        }

        // Get all applications for status calculation
        $allApplicantIds = $allApplicants->pluck('applicant_id')->toArray();
        $allApplications = $this->getApplicationsForApplicants($allApplicantIds);
        $applicationsByApplicant = $allApplications->groupBy('applicant_id');

        foreach ($allApplicants as $applicant) {
            $applicantApplications = $applicationsByApplicant->get($applicant->applicant_id, collect());
            $overallStatus = $this->determineApplicantOverallStatus($applicantApplications);
            $statusCounts[$overallStatus]++;
        }

        return $statusCounts;
    }

    /**
     * Transform applicants data with applications and filtering
     */
    private function transformApplicantsData($applicants, $applicationsByApplicant, string $statusFilter): array
    {
        $transformedApplicants = [];

        foreach ($applicants as $applicant) {
            $applicantApplications = $applicationsByApplicant->get($applicant->applicant_id, collect());

            // Determine overall applicant status based on applications
            $overallStatus = $this->determineApplicantOverallStatus($applicantApplications);

            // Apply status filter
            if ($statusFilter !== 'all' && $overallStatus !== $statusFilter) {
                continue;
            }

            // Generate reference number (using applicant ID for now)
            $reference = str_pad((string)$applicant->applicant_id, 3, '0', STR_PAD_LEFT);

            // Get requested checks (products)
            $requestedChecks = $applicantApplications->map(function ($app) {
                return [
                    'product_code' => $app->product_code,
                    'product_name' => $app->product_name,
                    'status' => $app->status
                ];
            })->toArray();

            $transformedApplicants[] = [
                'applicant_id' => $applicant->applicant_id,
                'name' => trim($applicant->first_name . ' ' . $applicant->last_name),
                'email' => $applicant->email,
                'reference' => $reference,
                'organization' => $applicant->organization_name,
                'date_created' => $applicant->date_created,
                'requested_checks' => $requestedChecks,
                'status' => $overallStatus,
                'status_display' => $this->getStatusDisplayName($overallStatus)
            ];
        }

        return $transformedApplicants;
    }
}
