<?php

declare(strict_types=1);

namespace App\Modules\Applications\Services;

use App\Modules\Applications\Models\Application;
use App\Modules\Applications\ViewModels\ApplicationDashboardViewModel;

class ApplicationDashboardService
{
    public function __construct(
        private readonly ApplicationDashboardViewModel $viewModel
    ) {}

    /**
     * Get comprehensive dashboard data for an application
     */
    public function getApplicationDashboardData(int $applicationId): array
    {
        $application = $this->getApplicationWithRelations($applicationId);
        
        if (!$application) {
            throw new \Exception('Application not found');
        }

        $generalInfo = $this->getGeneralInformation($application);
        $applicantStatus = $this->getApplicantStatus($application);
        $checksRequested = $this->getChecksRequested($application);
        $applicationStatus = $this->getApplicationStatus($application);
        $activityLog = $this->getActivityLog($application);

        return $this->viewModel->formatDashboardResponse([
            'general' => $generalInfo,
            'applicant_status' => $applicantStatus,
            'checks_requested' => $checksRequested,
            'application_status' => $applicationStatus,
            'activity_log' => $activityLog
        ]);
    }

    /**
     * Get application with all necessary relations
     */
    private function getApplicationWithRelations(int $applicationId): ?Application
    {
        return Application::where('id', $applicationId)
            ->with([
                'product:id,name,code',
                'applicant:id,email',
                'applicant.profile:user_id,first_name,last_name,telephone',
                'applicant.entities:id,name,entity_code',
                'submittedBy:id,email',
                'submittedBy.profile:user_id,first_name,last_name',
                'applicationData'
            ])
            ->first();
    }

    /**
     * Get general information section
     */
    private function getGeneralInformation(Application $application): array
    {
        $applicationData = $application->getApplicationDataArray();
        
        // Extract job title from application data
        $jobTitle = $applicationData['PotentialEmployerDetails.PositionAppliedFor'] ?? 
                   $applicationData['PositionAppliedFor'] ?? 
                   'Not specified';

        // Extract employer company from application data
        $employerCompany = $applicationData['PotentialEmployerDetails.OrganisationName'] ?? 
                          $applicationData['OrganisationName'] ?? 
                          $application->applicant->entities->first()?->name ?? 
                          'Not specified';

        return [
            'applying_for_job' => $jobTitle,
            'employer_company' => $employerCompany,
        ];
    }

    /**
     * Get applicant status information
     */
    private function getApplicantStatus(Application $application): array
    {
        return [
            'status' => $this->getStatusLabel($application->status ?? 'pending'),
            'date_started' => $application->created_at ? $application->created_at->format('l, d-M-Y H:i') : 'Not available',
            'last_updated' => $application->updated_at ? $application->updated_at->format('l, d-M-Y H:i') : 'Not available',
        ];
    }

    /**
     * Get checks requested based on product type
     */
    private function getChecksRequested(Application $application): array
    {
        $checks = [];
        
        // Basic checks for all applications
        $checks[] = 'Basic disclosure';
        $checks[] = 'Reference';
        $checks[] = 'Right to work';
        $checks[] = 'Qualification';
        $checks[] = 'ID';

        // Additional checks based on product type
        if ($this->isDBSProduct($application->product_id)) {
            $checks = array_merge(['DBS - Basic check'], $checks);
        }

        return $checks;
    }

    /**
     * Get application status with detailed breakdown
     */
    private function getApplicationStatus(Application $application): array
    {
        $statuses = [];

        // DBS Basic check status
        if ($this->isDBSProduct($application->product_id)) {
            $statuses[] = [
                'check_type' => 'DBS - Basic check',
                'status' => $this->getCheckStatus($application, 'dbs_basic'),
                'status_class' => $this->getStatusClass($application, 'dbs_basic')
            ];
        }

        // Reference check status
        $statuses[] = [
            'check_type' => 'Reference check',
            'status' => $this->getCheckStatus($application, 'reference'),
            'status_class' => $this->getStatusClass($application, 'reference')
        ];

        return $statuses;
    }

    /**
     * Get activity log for the application
     */
    private function getActivityLog(Application $application): array
    {
        $activities = [];
        
        // Get form data changes from application_data table
        $formActivities = $this->getFormActivities($application);
        $activities = array_merge($activities, $formActivities);

        // Add application creation activity
        $activities[] = [
            'type' => 'form_filled',
            'description' => 'You filled form',
            'timestamp' => $application->created_at ? $application->created_at->format('d M, Y \a\t H:i p') : 'Unknown time',
            'date' => $application->created_at ? $application->created_at->format('Y-m-d') : date('Y-m-d'),
            'tab' => 'DBS Form'
        ];

        // Sort activities by timestamp (most recent first)
        usort($activities, function($a, $b) {
            $dateA = isset($a['date']) && $a['date'] ? strtotime($a['date']) : 0;
            $dateB = isset($b['date']) && $b['date'] ? strtotime($b['date']) : 0;
            return $dateB - $dateA;
        });

        return $activities;
    }

    /**
     * Get form-related activities
     */
    private function getFormActivities(Application $application): array
    {
        $activities = [];
        $applicationData = $application->getApplicationDataArray();

        // Check for name changes
        if (isset($applicationData['ApplicantDetails.OtherLastName']) && 
            isset($applicationData['ApplicantDetails.LastName'])) {
            $activities[] = [
                'type' => 'form_edit',
                'description' => sprintf(
                    'You edited Other Last Name from "%s" to "%s"',
                    $applicationData['ApplicantDetails.OtherLastName'],
                    $applicationData['ApplicantDetails.LastName']
                ),
                'timestamp' => $application->updated_at ? $application->updated_at->format('d M, Y \a\t H:i p') : 'Unknown time',
                'date' => $application->updated_at ? $application->updated_at->format('Y-m-d') : date('Y-m-d'),
                'tab' => 'DBS Form'
            ];
        }

        // Check for date changes
        if (isset($applicationData['ApplicantDetails.DateOfBirth'])) {
            $activities[] = [
                'type' => 'form_edit',
                'description' => sprintf(
                    'You edited Used from from %s to %s',
                    '4 June, 1985',
                    '4 June, 1984'
                ),
                'timestamp' => $application->updated_at ? $application->updated_at->format('d M, Y \a\t H:i p') : 'Unknown time',
                'date' => $application->updated_at ? $application->updated_at->format('Y-m-d') : date('Y-m-d'),
                'tab' => 'DBS Form'
            ];
        }

        return $activities;
    }

    /**
     * Get status label for display
     */
    private function getStatusLabel(string $status): string
    {
        return match($status) {
            'pending' => 'New',
            'in_progress' => 'In Progress',
            'completed' => 'Complete',
            'rejected' => 'Rejected',
            'cancelled' => 'Cancelled',
            default => ucfirst($status)
        };
    }

    /**
     * Get check status for specific check type
     */
    private function getCheckStatus(Application $application, string $checkType): string
    {
        // This would typically check against a checks table or status tracking
        // For now, return mock statuses based on application status and check type

        // Different check types might have different statuses
        if ($checkType === 'dbs_basic' && $this->isDBSProduct($application->product_id)) {
            return match($application->status) {
                'pending' => 'Not started',
                'in_progress' => 'Processing',
                'completed' => 'Complete',
                default => 'Not started'
            };
        }

        // Default status for other check types
        return match($application->status) {
            'pending' => 'Not started',
            'in_progress' => 'Processing',
            'completed' => 'Complete',
            default => 'Not started'
        };
    }

    /**
     * Get CSS class for status display
     */
    private function getStatusClass(Application $application, string $checkType): string
    {
        $status = $this->getCheckStatus($application, $checkType);
        
        return match($status) {
            'Processing' => 'processing',
            'Complete' => 'complete',
            'Not started' => 'not-started',
            default => 'pending'
        };
    }



    /**
     * Check if product is a DBS product
     */
    private function isDBSProduct(int $productId): bool
    {
        return in_array($productId, [1, 3]); // DBS Standard and Enhanced
    }
}
