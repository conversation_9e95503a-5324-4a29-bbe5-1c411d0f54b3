<?php

declare(strict_types=1);

namespace App\Modules\Applications\ViewModels;

class ApplicationDashboardViewModel
{
    /**
     * Format the dashboard response data
     */
    public function formatDashboardResponse(array $data): array
    {
        return [
            'overview' => $this->formatOverview($data),
            'application_status' => $this->formatApplicationStatus($data),
            'activity_log' => $this->formatActivityLog($data)
        ];
    }

    /**
     * Format the overview section
     */
    private function formatOverview(array $data): array
    {
        return [
            'general' => [
                'applying_for_job' => $data['general']['applying_for_job'],
                'employer_company' => $data['general']['employer_company'],
            ],
            'applicant_status' => [
                'status' => $data['applicant_status']['status'],
                'date_started' => $data['applicant_status']['date_started'],
                'last_updated' => $data['applicant_status']['last_updated'],
            ],
            'checks_requested' => $data['checks_requested']
        ];
    }

    /**
     * Format the application status section
     */
    private function formatApplicationStatus(array $data): array
    {
        return $data['application_status'];
    }

    /**
     * Format the activity log section
     */
    private function formatActivityLog(array $data): array
    {
        return [
            'tabs' => [
                'DBS Form',
                'Reference Check',
                'Right To Work',
                'Qualification',
                'ID'
            ],
            'activities' => $data['activity_log']
        ];
    }


}
