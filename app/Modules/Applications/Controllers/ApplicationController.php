<?php

declare(strict_types=1);

namespace App\Modules\Applications\Controllers;

use App\Core\BaseApiController;
use App\Modules\Applications\Models\Application;
use App\Modules\Applications\Requests\CreateApplicantRequest;
use App\Modules\Applications\Requests\GetClientApplicantsRequest;
use App\Modules\Applications\Requests\SaveApplicationDataRequest;
use App\Modules\Applications\Services\ApplicationAccessService;
use App\Modules\Applications\Services\ApplicantService;
use App\Modules\Applications\Background\DBS\Services\DBSValidationService;
use App\Modules\Billing\Services\PaymentService;
use App\Modules\Products\Models\ProductFormField;
use App\Services\ProcessStampService;
use App\Services\FormCompletionStatusService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ApplicationController extends BaseApiController
{
    private ApplicationAccessService $accessService;
    private ApplicantService $applicantService;
    private PaymentService $paymentService;
    private DBSValidationService $dbsValidationService;
    private ProcessStampService $processStampService;
    private FormCompletionStatusService $formCompletionStatusService;

    public function __construct(
        ApplicationAccessService $accessService,
        ApplicantService $applicantService,
        PaymentService $paymentService,
        DBSValidationService $dbsValidationService,
        ProcessStampService $processStampService,
        FormCompletionStatusService $formCompletionStatusService
    ) {
        $this->accessService = $accessService;
        $this->applicantService = $applicantService;
        $this->paymentService = $paymentService;
        $this->dbsValidationService = $dbsValidationService;
        $this->processStampService = $processStampService;
        $this->formCompletionStatusService = $formCompletionStatusService;
    }





    public function getApplicantDetails(Request $request, string $applicantId): JsonResponse
    {
        try {
            $user = $request->user();

            if (!in_array($user->user_type, ['applicant', 'client_user', 'requester', 'doc_checker'])) {
                return $this->sendForbidden('Access denied for this user type');
            }

            // Validate applicant ID
            if (!is_numeric($applicantId) || (int)$applicantId <= 0) {
                return $this->sendError('Invalid applicant ID', [], 400);
            }

            $applicantIdInt = (int)$applicantId;

            // Check if user can access this applicant's data
            if ($user->user_type === 'applicant' && $applicantIdInt !== $user->id) {
                return $this->sendForbidden('You can only access your own data');
            }

            // For client users, check if they have access to this applicant through hierarchy
            if (in_array($user->user_type, ['client_user', 'requester', 'doc_checker'])) {
                $accessibleApplicantIds = $this->accessService->getAccessibleApplicationsQuery($user)
                    ->pluck('applicant_id')
                    ->unique()
                    ->toArray();

                if (!in_array($applicantIdInt, $accessibleApplicantIds)) {
                    return $this->sendForbidden('You do not have access to this applicant');
                }
            }

            // Get applicant details with profile
            $applicant = \App\Modules\Auth\Models\PortalUser::where('id', $applicantIdInt)
                ->with(['profile', 'entities'])
                ->first();

            if (!$applicant) {
                return $this->sendError('Applicant not found', [], 404);
            }

            // Get all applications for this applicant
            $applications = Application::where('applicant_id', $applicantIdInt)
                ->with([
                    'product:id,name,code,variant',
                    'applicationData',
                    'submittedBy:id,email',
                    'submittedBy.profile:user_id,first_name,last_name'
                ])
                ->orderBy('created_at', 'desc')
                ->get();

            // Transform the data
            $responseData = [
                'applicant' => [
                    'id' => $applicant->id,
                    'email' => $applicant->email,
                    'user_type' => $applicant->user_type,
                    'profile' => [
                        'first_name' => $applicant->profile?->first_name ?? null,
                        'last_name' => $applicant->profile?->last_name ?? null,
                        'full_name' => $applicant->profile
                            ? trim(($applicant->profile->first_name ?? '') . ' ' . ($applicant->profile->last_name ?? ''))
                            : null,
                        'telephone' => $applicant->profile?->telephone ?? null,
                        'address' => $applicant->profile?->address ?? null,
                        'date_of_birth' => $applicant->profile?->date_of_birth ?? null,
                    ],
                    'entities' => $applicant->entities?->map(function ($entity) {
                        return [
                            'id' => $entity->id,
                            'name' => $entity->name,
                            'entity_code' => $entity->entity_code,
                            'role' => $entity->pivot?->role ?? null,
                        ];
                    })->toArray() ?? [],
                ],
                'applications' => $applications->map(function ($application) {
                    $formData = $application->getApplicationDataArray();
                    $isComplete = $this->determineApplicationCompletionStatus($application, $formData);
                    $isStarted = !empty($formData);

                    return [
                        'id' => $application->id,
                        'external_reference' => $application->external_reference,
                        'status' => $application->status,
                        'result' => $application->result,
                        'consent_date' => $application->consent_date?->format('Y-m-d'),
                        'created_at' => $application->created_at?->format('Y-m-d H:i:s'),
                        'product' => [
                            'id' => $application->product?->id,
                            'name' => $application->product?->name,
                            'code' => $application->product?->code,
                            'variant' => $application->product?->variant,
                        ],
                        'completion_status' => [
                            'is_started' => $isStarted,
                            'is_complete' => $isComplete,
                            'status_text' => $this->getCompletionStatusText($isStarted, $isComplete)
                        ],
                        'submitted_by' => [
                            'id' => $application->submittedBy?->id,
                            'email' => $application->submittedBy?->email,
                            'name' => $application->submittedBy?->profile
                                ? trim(($application->submittedBy->profile->first_name ?? '') . ' ' . ($application->submittedBy->profile->last_name ?? ''))
                                : null,
                        ],
                    ];
                })->toArray(),
                'total_applications' => $applications->count(),
            ];

            return $this->sendResponse($responseData, 'Applicant details retrieved successfully');
        } catch (\Exception $e) {
            return $this->sendServerError('Failed to retrieve applicant details: ' . $e->getMessage());
        }
    }

    public function getApplicationFormData(Request $request, string $applicationId): JsonResponse
    {
        try {
            $user = $request->user();

            if (!in_array($user->user_type, ['applicant', 'client_user', 'requester', 'doc_checker'])) {
                return $this->sendForbidden('Access denied for this user type');
            }

            // Validate and cast applicationId to integer
            if (!is_numeric($applicationId) || (int)$applicationId <= 0) {
                return $this->sendError('Invalid application ID', [], 400);
            }

            $applicationIdInt = (int)$applicationId;

            if (!$this->accessService->canUserAccessApplication($user, $applicationIdInt)) {
                return $this->sendError('Application not found', [], 404);
            }

            $application = Application::where('id', $applicationIdInt)
                ->with([
                    'product:id,name,code,variant',
                    'applicationData',
                    'applicant:id,email',
                    'applicant.profile:user_id,first_name,last_name',
                    'submittedBy:id,email',
                    'submittedBy.profile:user_id,first_name,last_name'
                ])
                ->first();

            if (!$application) {
                return $this->sendError('Application not found', [], 404);
            }

            // Get form data
            $formData = $application->getApplicationDataArray();

            // Determine completion status
            $isComplete = $this->determineApplicationCompletionStatus($application, $formData);
            $isStarted = !empty($formData);

            // Get process stamp completion status
            $processStampStatus = $this->formCompletionStatusService->checkFormCompletionStatus($applicationIdInt);

            // Get applicant name
            $applicantName = null;
            if ($application->applicant && $application->applicant->profile) {
                $profile = $application->applicant->profile;
                $applicantName = trim(($profile->first_name ?? '') . ' ' . ($profile->last_name ?? ''));
            }

            $responseData = [
                'application_id' => $application->id,
                'external_reference' => $application->external_reference,
                'status' => $application->status,
                'form_data' => $formData,
                'completion_status' => [
                    'is_started' => $isStarted,
                    'is_complete' => $isComplete,
                    'status_text' => $this->getCompletionStatusText($isStarted, $isComplete),
                    'process_stamp_status' => $processStampStatus
                ],
                'applicant' => [
                    'name' => $applicantName ?: 'Applicant',
                    'email' => $application->applicant->email ?? null
                ],
                'product' => [
                    'id' => $application->product->id,
                    'name' => $application->product->name,
                    'code' => $application->product->code,
                    'variant' => $application->product->variant,
                ],
                'can_proceed_to_documents' => $processStampStatus['is_completed'] ?? false,
                'next_step' => $processStampStatus['is_completed'] ? 'document_nomination' : 'complete_form'
            ];

            return $this->sendResponse($responseData, 'Application form data retrieved successfully');
        } catch (\Exception $e) {
            return $this->sendServerError('Failed to retrieve application form data: ' . $e->getMessage());
        }
    }



    public function saveApplicationData(SaveApplicationDataRequest $request): JsonResponse
    {
        try {
            $user = $request->user();

            if (!in_array($user->user_type, ['applicant', 'client_user', 'requester', 'doc_checker'])) {
                return $this->sendForbidden('Access denied for this user type');
            }

            $applicationId = $request->validated()['application_id'];
            $formData = $request->validated()['form_data'];

            if (!$this->accessService->canUserAccessApplication($user, $applicationId)) {
                return $this->sendError('Application not found', [], 404);
            }

            $application = Application::where('id', $applicationId)
                ->with(['product'])
                ->first();

            if (!$application) {
                return $this->sendError('Application not found', [], 404);
            }

            $validationErrors = $this->validateFormData($formData, $application->product_id);

            if (!empty($validationErrors)) {
                return $this->sendError('Form validation failed', $validationErrors, 422);
            }

            if ($user->user_type === 'applicant' && $this->paymentService->hasUnpaidApplications($application->applicant_id)) {
                $paymentStatus = $this->paymentService->getPaymentStatus($application->applicant_id);
                return $this->sendError(
                    'Payment required before saving application data',
                    [
                        'payment_required' => true,
                        'total_amount' => $paymentStatus['total_amount'],
                        'currency' => $paymentStatus['currency'],
                        'applications_requiring_payment' => $paymentStatus['applications'],
                    ],
                    402
                );
            }

            DB::transaction(function () use ($application, $formData, $user) {
                $application->setApplicationData($formData);

                $this->processStampService->completeStamp(
                    $application->id,
                    'APPLICANT_FORM_COMPLETED',
                    1, // Use default admin user ID for system actions
                    [
                        'form_data_saved' => true,
                        'saved_at' => now()->toISOString(),
                        'saved_by_user_id' => $user->id,
                        'saved_by_user_type' => $user->user_type
                    ],
                    'Applicant form data saved successfully'
                );
            });

            return $this->sendResponse(
                ['application_id' => $applicationId],
                'Application data saved successfully'
            );
        } catch (\Exception $e) {
            return $this->sendServerError('Failed to save application data: ' . $e->getMessage());
        }
    }

    public function getClientApplicants(GetClientApplicantsRequest $request): JsonResponse
    {
        try {
            $user = $request->user();

            // Updated role validation to match requirements
            if (!in_array($user->user_type, ['client_user', 'requester', 'doc_checker'])) {
                return $this->sendForbidden('Access denied for this user type');
            }

            $searchTerm = $request->input('search');
            $statusFilter = $request->input('status_filter', 'all');
            $page = (int) $request->input('page', 1);
            $perPage = (int) $request->input('per_page', 20);

            $applicantsData = $this->accessService->getClientApplicantsWithStatusSummary(
                $user,
                $searchTerm,
                $statusFilter,
                $page,
                $perPage
            );

            return $this->sendResponse($applicantsData, 'Client applicants retrieved successfully');
        } catch (\Exception $e) {
            return $this->sendServerError('Failed to retrieve client applicants: ' . $e->getMessage());
        }
    }

    public function createApplicant(CreateApplicantRequest $request): JsonResponse
    {
        try {
            $user = $request->user();

            if (!in_array($user->user_type, ['client_user', 'requester', 'doc_checker'])) {
                return $this->sendForbidden('Access denied for this user type');
            }

            $validatedData = $request->validated();

            if (!$this->applicantService->canUserCreateApplicantForJobRole($user, $validatedData['job_role_id'])) {
                return $this->sendForbidden('You do not have access to create applicants for this job role');
            }

            $result = $this->applicantService->createApplicant($validatedData, $user);

            $responseData = [
                'applicant' => [
                    'id' => $result['applicant']->id,
                    'email' => $result['applicant']->email,
                    'first_name' => $result['profile']->first_name,
                    'last_name' => $result['profile']->last_name,
                    'phone' => $result['profile']->telephone,
                    'full_name' => $result['profile']->full_name,
                ],
                'entity' => [
                    'id' => $result['entity']->id,
                    'name' => $result['entity']->name,
                    'entity_code' => $result['entity']->entity_code,
                ],
                'job_role' => [
                    'id' => $result['job_role']->id,
                    'job_label' => $result['job_role']->job_label,
                    'job_title' => $result['job_role']->job_title,
                ],
                'applications' => array_map(function ($application, $index) use ($result) {
                    $billingSnapshot = $result['billing_snapshots'][$index] ?? null;

                    return [
                        'id' => $application->id,
                        'product_id' => $application->product_id,
                        'status' => $application->status,
                        'billing' => $billingSnapshot ? [
                            'admin_fee' => $billingSnapshot->admin_fee,
                            'supplier_fee' => $billingSnapshot->supplier_fee,
                            'total_fee' => ($billingSnapshot->admin_fee ?? 0) + ($billingSnapshot->supplier_fee ?? 0),
                            'self_payment' => $billingSnapshot->self_payment,
                            'currency' => 'GBP',
                        ] : null,
                    ];
                }, $result['applications'], array_keys($result['applications'])),
                'generated_password' => $result['generated_password'],
                'total_applications' => count($result['applications']),
                'products_auto_selected' => count($result['applications']),
            ];

            return $this->sendResponse($responseData, 'Applicant created successfully with ' . count($result['applications']) . ' applications auto-generated');
        } catch (\Exception $e) {
            if (str_contains($e->getMessage(), 'no products associated')) {
                return $this->sendError($e->getMessage(), [], 400);
            }

            return $this->sendServerError('Failed to create applicant: ' . $e->getMessage());
        }
    }











    private function validateFormData(array $formData, ?int $productId = null): array
    {
        // Check if this is a DBS product and use SolidFuse validation
        if ($productId && $this->isDBSProduct($productId)) {
            try {
                // Use SolidFuse validation service directly
                $this->dbsValidationService->validateFormData($formData, $productId);
                return []; // No errors if validation passes
            } catch (\Illuminate\Validation\ValidationException $e) {
                // Transform SolidFuse errors to the required format
                $errors = [];
                foreach ($e->errors() as $field => $messages) {
                    // Check if it's a field name recognition error
                    $message = is_array($messages) ? $messages[0] : $messages;
                    if (str_contains($message, 'not recognized') || str_contains($message, 'not recognised') || str_contains($message, 'unrecognized')) {
                        $errors[$field] = 'Field name not recognised';
                    } else {
                        $errors[$field] = 'Validation failed';
                    }
                }
                return $errors;
            } catch (\Exception $e) {
                return ['validation_error' => $e->getMessage()];
            }
        }

        // For non-DBS products, form fields are now handled in Flutter application
        // Return empty array (no validation errors) since validation is done client-side
        return [];
    }

    /**
     * Check if product is a DBS product
     */
    private function isDBSProduct(int $productId): bool
    {
        // Product ID 3 is DBS Enhanced, Product ID 1 is DBS Standard
        return in_array($productId, [1, 3]);
    }





    /**
     * Determine if an application form is complete based on required fields
     */
    private function determineApplicationCompletionStatus(Application $application, array $formData): bool
    {
        // If no form data exists, it's not complete
        if (empty($formData)) {
            return false;
        }

        // Check if all required fields are filled
        if ($application->product && $application->product->formFields) {
            $requiredFields = $application->product->formFields->where('is_required', true);

            foreach ($requiredFields as $field) {
                $fieldName = $field->field_name;
                if (!isset($formData[$fieldName]) || trim($formData[$fieldName]) === '') {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Get human-readable completion status text
     */
    private function getCompletionStatusText(bool $isStarted, bool $isComplete): string
    {
        if (!$isStarted) {
            return 'Not started';
        } elseif ($isComplete) {
            return 'Complete';
        } else {
            return 'In progress';
        }
    }
}
