"""
ML Training Manager for continuous model improvement
Handles training data collection, model retraining, and performance monitoring
"""

import asyncio
import json
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import cv2
import numpy as np
import structlog
import torch
import torch.nn as nn
import torch.optim as optim
from PIL import Image
from sklearn.model_selection import train_test_split
from torch.utils.data import DataLoader, Dataset
from torchvision import transforms

from app.core.config import get_settings
from app.database.connection import database_manager

logger = structlog.get_logger()


class PassportDataset(Dataset):
    """Custom dataset for passport document training"""
    
    def __init__(self, image_paths: List[Path], labels: List[Dict], transform=None):
        self.image_paths = image_paths
        self.labels = labels
        self.transform = transform
        
    def __len__(self):
        return len(self.image_paths)
    
    def __getitem__(self, idx):
        image_path = self.image_paths[idx]
        label = self.labels[idx]
        
        # Load image
        image = Image.open(image_path).convert('RGB')
        
        if self.transform:
            image = self.transform(image)
            
        # Convert label to tensor
        fraud_score = torch.tensor(label.get('is_fraudulent', 0), dtype=torch.float32)
        authenticity_score = torch.tensor(label.get('is_authentic', 1), dtype=torch.float32)
        alteration_score = torch.tensor(label.get('is_altered', 0), dtype=torch.float32)
        
        return {
            'image': image,
            'fraud_score': fraud_score,
            'authenticity_score': authenticity_score,
            'alteration_score': alteration_score,
            'metadata': label
        }


class PassportFraudDetectionModel(nn.Module):
    """Neural network for passport fraud detection"""
    
    def __init__(self, num_classes=3):
        super(PassportFraudDetectionModel, self).__init__()
        
        # Convolutional layers for feature extraction
        self.conv_layers = nn.Sequential(
            nn.Conv2d(3, 32, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2),
            
            nn.Conv2d(32, 64, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2),
            
            nn.Conv2d(64, 128, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.MaxPool2d(2),
            
            nn.Conv2d(128, 256, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.AdaptiveAvgPool2d((8, 8))
        )
        
        # Fully connected layers
        self.fc_layers = nn.Sequential(
            nn.Flatten(),
            nn.Linear(256 * 8 * 8, 512),
            nn.ReLU(),
            nn.Dropout(0.5),
            
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Linear(256, num_classes),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        x = self.conv_layers(x)
        x = self.fc_layers(x)
        return x


class TrainingManager:
    """Manages ML model training and continuous improvement"""
    
    def __init__(self):
        self.settings = get_settings()
        self.device = torch.device(self.settings.DEVICE if torch.cuda.is_available() else "cpu")
        self.training_data_path = Path(self.settings.TRAINING_DATA_PATH)
        self.model_backup_path = Path(self.settings.MODEL_BACKUP_PATH)
        self.model_path = Path(self.settings.MODEL_PATH)
        
        # Create directories
        self.training_data_path.mkdir(parents=True, exist_ok=True)
        self.model_backup_path.mkdir(parents=True, exist_ok=True)
        self.model_path.mkdir(parents=True, exist_ok=True)
        
        # Training configuration
        self.batch_size = self.settings.TRAINING_BATCH_SIZE
        self.epochs = self.settings.TRAINING_EPOCHS
        self.learning_rate = 0.001
        
        # Data transforms
        self.train_transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.RandomHorizontalFlip(p=0.3),
            transforms.RandomRotation(degrees=5),
            transforms.ColorJitter(brightness=0.2, contrast=0.2),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        self.val_transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
    async def collect_training_data(self, image_data: bytes, document_type: str, 
                                  extracted_data: Dict, fraud_analysis: Dict,
                                  user_feedback: Optional[Dict] = None):
        """Collect and store training data from document processing"""
        
        try:
            # Create timestamp-based filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
            image_filename = f"{document_type}_{timestamp}.jpg"
            metadata_filename = f"{document_type}_{timestamp}.json"
            
            # Save image
            image_path = self.training_data_path / "images" / image_filename
            image_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(image_path, 'wb') as f:
                f.write(image_data)
            
            # Prepare metadata
            metadata = {
                'document_type': document_type,
                'timestamp': timestamp,
                'extracted_data': extracted_data,
                'fraud_analysis': fraud_analysis,
                'user_feedback': user_feedback or {},
                'image_path': str(image_path),
                'is_fraudulent': fraud_analysis.get('is_fraudulent', False),
                'is_authentic': fraud_analysis.get('is_authentic', True),
                'is_altered': fraud_analysis.get('is_altered', False),
                'confidence_score': fraud_analysis.get('confidence_score', 0.5)
            }
            
            # Save metadata
            metadata_path = self.training_data_path / "metadata" / metadata_filename
            metadata_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            logger.info("Training data collected", 
                       document_type=document_type,
                       image_path=str(image_path),
                       metadata_path=str(metadata_path))
            
            # Check if we have enough data for training
            await self._check_training_trigger()
            
        except Exception as e:
            logger.error("Failed to collect training data", error=str(e))
            raise
    
    async def _check_training_trigger(self):
        """Check if we should trigger model retraining"""
        
        try:
            # Count available training samples
            metadata_files = list((self.training_data_path / "metadata").glob("*.json"))
            
            if len(metadata_files) >= self.settings.MIN_TRAINING_SAMPLES:
                logger.info(f"Training trigger: {len(metadata_files)} samples available")
                
                # Schedule training (run in background)
                asyncio.create_task(self.train_models())
                
        except Exception as e:
            logger.error("Failed to check training trigger", error=str(e))
    
    async def train_models(self):
        """Train ML models with collected data"""
        
        logger.info("🚀 Starting model training...")
        
        try:
            # Load training data
            image_paths, labels = await self._load_training_data()
            
            if len(image_paths) < self.settings.MIN_TRAINING_SAMPLES:
                logger.warning(f"Insufficient training data: {len(image_paths)} samples")
                return
            
            # Split data
            train_paths, val_paths, train_labels, val_labels = train_test_split(
                image_paths, labels, test_size=0.2, random_state=42
            )
            
            # Create datasets
            train_dataset = PassportDataset(train_paths, train_labels, self.train_transform)
            val_dataset = PassportDataset(val_paths, val_labels, self.val_transform)
            
            # Create data loaders
            train_loader = DataLoader(train_dataset, batch_size=self.batch_size, shuffle=True)
            val_loader = DataLoader(val_dataset, batch_size=self.batch_size, shuffle=False)
            
            # Initialize model
            model = PassportFraudDetectionModel().to(self.device)
            criterion = nn.BCELoss()
            optimizer = optim.Adam(model.parameters(), lr=self.learning_rate)
            
            # Training loop
            best_val_loss = float('inf')
            
            for epoch in range(self.epochs):
                # Training phase
                model.train()
                train_loss = 0.0
                
                for batch in train_loader:
                    images = batch['image'].to(self.device)
                    fraud_scores = batch['fraud_score'].to(self.device)
                    authenticity_scores = batch['authenticity_score'].to(self.device)
                    alteration_scores = batch['alteration_score'].to(self.device)
                    
                    optimizer.zero_grad()
                    
                    outputs = model(images)
                    
                    # Multi-task loss
                    loss_fraud = criterion(outputs[:, 0], fraud_scores)
                    loss_auth = criterion(outputs[:, 1], authenticity_scores)
                    loss_alter = criterion(outputs[:, 2], alteration_scores)
                    
                    total_loss = loss_fraud + loss_auth + loss_alter
                    total_loss.backward()
                    optimizer.step()
                    
                    train_loss += total_loss.item()
                
                # Validation phase
                model.eval()
                val_loss = 0.0
                
                with torch.no_grad():
                    for batch in val_loader:
                        images = batch['image'].to(self.device)
                        fraud_scores = batch['fraud_score'].to(self.device)
                        authenticity_scores = batch['authenticity_score'].to(self.device)
                        alteration_scores = batch['alteration_score'].to(self.device)
                        
                        outputs = model(images)
                        
                        loss_fraud = criterion(outputs[:, 0], fraud_scores)
                        loss_auth = criterion(outputs[:, 1], authenticity_scores)
                        loss_alter = criterion(outputs[:, 2], alteration_scores)
                        
                        total_loss = loss_fraud + loss_auth + loss_alter
                        val_loss += total_loss.item()
                
                avg_train_loss = train_loss / len(train_loader)
                avg_val_loss = val_loss / len(val_loader)
                
                logger.info(f"Epoch {epoch+1}/{self.epochs}",
                           train_loss=avg_train_loss,
                           val_loss=avg_val_loss)
                
                # Save best model
                if avg_val_loss < best_val_loss:
                    best_val_loss = avg_val_loss
                    await self._save_model(model, f"passport_fraud_detection_best.pth")
            
            # Save final model
            await self._save_model(model, f"passport_fraud_detection_latest.pth")
            
            logger.info("✅ Model training completed successfully")
            
        except Exception as e:
            logger.error("❌ Model training failed", error=str(e))
            raise
    
    async def _load_training_data(self) -> Tuple[List[Path], List[Dict]]:
        """Load training data from storage"""
        
        metadata_files = list((self.training_data_path / "metadata").glob("*.json"))
        image_paths = []
        labels = []
        
        for metadata_file in metadata_files:
            try:
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                
                image_path = Path(metadata['image_path'])
                if image_path.exists():
                    image_paths.append(image_path)
                    labels.append(metadata)
                    
            except Exception as e:
                logger.warning(f"Failed to load metadata from {metadata_file}", error=str(e))
                continue
        
        return image_paths, labels
    
    async def _save_model(self, model: nn.Module, filename: str):
        """Save trained model"""
        
        model_path = self.model_path / filename
        
        # Create backup of existing model
        if model_path.exists():
            backup_path = self.model_backup_path / f"{filename}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copy2(model_path, backup_path)
        
        # Save new model
        torch.save({
            'model_state_dict': model.state_dict(),
            'timestamp': datetime.now().isoformat(),
            'training_samples': len(list((self.training_data_path / "metadata").glob("*.json"))),
        }, model_path)
        
        logger.info("Model saved", path=str(model_path))


# Global training manager instance
training_manager = TrainingManager()
