"""
Advanced OCR engine for document data extraction
"""

import re
from typing import Dict, List, Optional, Tuple

import cv2
import easyocr
import numpy as np
import pytesseract
import structlog
from PIL import Image

from app.core.config import get_settings

logger = structlog.get_logger()


class OCREngine:
    """Multi-engine OCR system with intelligent text extraction"""
    
    def __init__(self):
        self.settings = get_settings()
        
        # Initialize OCR engines
        self.tesseract_config = '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz .,/-'
        self.easyocr_reader = easyocr.Reader(['en'], gpu=False)  # Set to True if GPU available
        
        # Regex patterns for data extraction
        self.patterns = {
            'ni_number': r'[A-Z]{2}\s*\d{2}\s*\d{2}\s*\d{2}\s*[A-Z]',
            'postcode': r'[A-Z]{1,2}\d[A-Z\d]?\s*\d[A-Z]{2}',
            'date': r'\b\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}\b',
            'passport_number': r'[A-Z0-9]{8,9}',
            'driving_license': r'[A-Z0-9]{5,16}',
            'phone': r'(\+44\s?|0)(\d{2,4}\s?\d{3,4}\s?\d{3,4})',
            'email': r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
        }
    
    async def extract_data(self, image: np.ndarray, document_type: str) -> Dict:
        """
        Extract structured data from document image
        
        Uses multiple OCR engines and intelligent parsing
        """
        
        logger.info("📝 Starting OCR data extraction", document_type=document_type)
        
        try:
            # Prepare image for OCR
            ocr_image = self._prepare_image_for_ocr(image)
            
            # Extract text using multiple engines
            tesseract_text = await self._extract_with_tesseract(ocr_image)
            easyocr_text = await self._extract_with_easyocr(ocr_image)
            
            # Combine and clean text
            combined_text = self._combine_ocr_results(tesseract_text, easyocr_text)
            
            # Extract structured data
            extracted_data = await self._extract_structured_data(
                combined_text, document_type, image
            )
            
            # Calculate confidence scores
            confidence_scores = self._calculate_confidence_scores(
                extracted_data, tesseract_text, easyocr_text
            )
            
            result = {
                **extracted_data,
                "confidence_scores": confidence_scores,
                "raw_text": combined_text,
                "ocr_engines_used": ["tesseract", "easyocr"]
            }
            
            logger.info("✅ OCR extraction completed", 
                       fields_extracted=len([k for k, v in extracted_data.items() if v]))
            
            return result
            
        except Exception as e:
            logger.error("❌ OCR extraction failed", error=str(e))
            raise
    
    def _prepare_image_for_ocr(self, image: np.ndarray) -> np.ndarray:
        """Prepare image for optimal OCR performance"""
        
        # Convert to grayscale
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # Apply adaptive thresholding
        binary = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        
        # Noise removal
        kernel = np.ones((1, 1), np.uint8)
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel)
        
        # Dilation and erosion to improve text
        kernel = np.ones((2, 2), np.uint8)
        processed = cv2.dilate(cleaned, kernel, iterations=1)
        processed = cv2.erode(processed, kernel, iterations=1)
        
        return processed
    
    async def _extract_with_tesseract(self, image: np.ndarray) -> str:
        """Extract text using Tesseract OCR"""
        
        try:
            # Convert numpy array to PIL Image
            pil_image = Image.fromarray(image)
            
            # Extract text
            text = pytesseract.image_to_string(pil_image, config=self.tesseract_config)
            
            return text.strip()
            
        except Exception as e:
            logger.warning("⚠️ Tesseract extraction failed", error=str(e))
            return ""
    
    async def _extract_with_easyocr(self, image: np.ndarray) -> str:
        """Extract text using EasyOCR"""
        
        try:
            # EasyOCR expects RGB format
            if len(image.shape) == 2:
                rgb_image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
            else:
                rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Extract text
            results = self.easyocr_reader.readtext(rgb_image)
            
            # Combine all detected text
            text_parts = []
            for (bbox, text, confidence) in results:
                if confidence > 0.5:  # Filter low-confidence results
                    text_parts.append(text)
            
            return ' '.join(text_parts)
            
        except Exception as e:
            logger.warning("⚠️ EasyOCR extraction failed", error=str(e))
            return ""
    
    def _combine_ocr_results(self, tesseract_text: str, easyocr_text: str) -> str:
        """Intelligently combine results from multiple OCR engines"""
        
        # Simple combination - in practice, you might want more sophisticated merging
        combined = f"{tesseract_text}\n{easyocr_text}"
        
        # Remove duplicates and clean up
        lines = combined.split('\n')
        unique_lines = []
        
        for line in lines:
            line = line.strip()
            if line and line not in unique_lines:
                unique_lines.append(line)
        
        return '\n'.join(unique_lines)
    
    async def _extract_structured_data(self, text: str, document_type: str, image: np.ndarray) -> Dict:
        """Extract structured data based on document type"""
        
        extracted = {
            "full_name": None,
            "date_of_birth": None,
            "address": None,
            "ni_number": None,
            "document_number": None,
            "expiry_date": None,
            "issue_date": None,
            "issuing_authority": None
        }
        
        # Apply document-specific extraction
        if "passport" in document_type.lower():
            extracted.update(await self._extract_passport_data(text, image))
        elif "driving" in document_type.lower():
            extracted.update(await self._extract_license_data(text, image))
        elif "certificate" in document_type.lower():
            extracted.update(await self._extract_certificate_data(text, image))
        else:
            # Generic extraction
            extracted.update(self._extract_generic_data(text))
        
        return extracted
    
    async def _extract_passport_data(self, text: str, image: np.ndarray) -> Dict:
        """Extract passport-specific data"""
        
        data = {}
        
        # Extract passport number
        passport_match = re.search(self.patterns['passport_number'], text)
        if passport_match:
            data["document_number"] = passport_match.group()
        
        # Extract dates
        dates = re.findall(self.patterns['date'], text)
        if len(dates) >= 2:
            data["issue_date"] = dates[0]
            data["expiry_date"] = dates[1]
        
        # Extract name (usually appears after "Name" or similar)
        name_patterns = [
            r'(?:Name|Given Names?|Surname)[:\s]+([A-Z][A-Z\s]+)',
            r'([A-Z]{2,}\s+[A-Z]{2,}(?:\s+[A-Z]{2,})?)'
        ]
        
        for pattern in name_patterns:
            name_match = re.search(pattern, text, re.IGNORECASE)
            if name_match:
                data["full_name"] = name_match.group(1).strip()
                break
        
        # Extract nationality/issuing authority
        authority_patterns = [
            r'(?:United Kingdom|UK|GBR)',
            r'(?:Issuing Authority)[:\s]+([A-Z\s]+)'
        ]
        
        for pattern in authority_patterns:
            auth_match = re.search(pattern, text, re.IGNORECASE)
            if auth_match:
                data["issuing_authority"] = auth_match.group().strip()
                break
        
        return data
    
    async def _extract_license_data(self, text: str, image: np.ndarray) -> Dict:
        """Extract driving license data"""
        
        data = {}
        
        # Extract license number
        license_match = re.search(self.patterns['driving_license'], text)
        if license_match:
            data["document_number"] = license_match.group()
        
        # Extract address
        address_lines = []
        lines = text.split('\n')
        
        for i, line in enumerate(lines):
            line = line.strip()
            # Look for postcode to identify address
            if re.search(self.patterns['postcode'], line):
                # Collect previous lines as address
                start_idx = max(0, i - 3)
                address_lines = lines[start_idx:i+1]
                break
        
        if address_lines:
            data["address"] = ' '.join([line.strip() for line in address_lines if line.strip()])
        
        # Extract dates
        dates = re.findall(self.patterns['date'], text)
        if dates:
            data["issue_date"] = dates[0]
            if len(dates) > 1:
                data["expiry_date"] = dates[1]
        
        return data
    
    async def _extract_certificate_data(self, text: str, image: np.ndarray) -> Dict:
        """Extract certificate data"""
        
        data = {}
        
        # Extract name (usually prominently displayed)
        name_patterns = [
            r'(?:This is to certify that|Certificate of)[:\s\n]+([A-Z][A-Z\s]+)',
            r'([A-Z]{2,}\s+[A-Z]{2,}(?:\s+[A-Z]{2,})?)'
        ]
        
        for pattern in name_patterns:
            name_match = re.search(pattern, text, re.IGNORECASE)
            if name_match:
                data["full_name"] = name_match.group(1).strip()
                break
        
        # Extract dates
        dates = re.findall(self.patterns['date'], text)
        if dates:
            data["issue_date"] = dates[0]
        
        # Extract issuing authority
        authority_patterns = [
            r'(?:Registrar|Registry|Authority|Council)[:\s]+([A-Z][A-Z\s]+)',
            r'([A-Z][A-Z\s]+ (?:Registry|Council|Authority))'
        ]
        
        for pattern in authority_patterns:
            auth_match = re.search(pattern, text, re.IGNORECASE)
            if auth_match:
                data["issuing_authority"] = auth_match.group(1).strip()
                break
        
        return data
    
    def _extract_generic_data(self, text: str) -> Dict:
        """Extract generic data patterns"""
        
        data = {}
        
        # Extract NI number
        ni_match = re.search(self.patterns['ni_number'], text)
        if ni_match:
            data["ni_number"] = ni_match.group().replace(' ', '')
        
        # Extract postcode (part of address)
        postcode_match = re.search(self.patterns['postcode'], text)
        if postcode_match:
            # Try to extract full address around postcode
            lines = text.split('\n')
            for i, line in enumerate(lines):
                if postcode_match.group() in line:
                    # Get surrounding lines as address
                    start_idx = max(0, i - 2)
                    end_idx = min(len(lines), i + 2)
                    address_lines = lines[start_idx:end_idx]
                    data["address"] = ' '.join([l.strip() for l in address_lines if l.strip()])
                    break
        
        # Extract dates
        dates = re.findall(self.patterns['date'], text)
        if dates:
            data["date_of_birth"] = dates[0]
        
        return data
    
    def _calculate_confidence_scores(self, extracted_data: Dict, tesseract_text: str, easyocr_text: str) -> Dict:
        """Calculate confidence scores for extracted fields"""
        
        confidence_scores = {}
        
        for field, value in extracted_data.items():
            if value:
                # Simple confidence calculation based on pattern matching
                # In practice, you'd use more sophisticated methods
                
                if field == "ni_number":
                    confidence_scores[field] = 0.9 if re.match(self.patterns['ni_number'], value) else 0.6
                elif field == "document_number":
                    confidence_scores[field] = 0.85
                elif field == "date_of_birth" or "date" in field:
                    confidence_scores[field] = 0.8 if re.match(self.patterns['date'], value) else 0.5
                else:
                    confidence_scores[field] = 0.7
            else:
                confidence_scores[field] = 0.0
        
        return confidence_scores
