"""
Document preprocessing and image enhancement with ML training integration
"""

import io
from typing import Dict, Optional, Tuple

import cv2
import numpy as np
import structlog
from PIL import Image

from app.core.config import get_settings

logger = structlog.get_logger()


class DocumentProcessor:
    """Advanced document preprocessing and enhancement"""
    
    def __init__(self):
        self.settings = get_settings()
        self.max_size = self.settings.MAX_IMAGE_SIZE
    
    async def preprocess_document(self, file_content: bytes, document_type: str) -> np.ndarray:
        """
        Comprehensive document preprocessing pipeline
        
        Steps:
        1. Load and validate image
        2. Resize if necessary
        3. Noise reduction
        4. Contrast enhancement
        5. Perspective correction
        6. Quality assessment
        """
        
        logger.info("🔍 Starting document preprocessing", document_type=document_type)
        
        try:
            # Load image from bytes
            image = self._load_image_from_bytes(file_content)
            
            # Resize if too large
            image = self._resize_image(image)
            
            # Convert to OpenCV format
            cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            
            # Apply preprocessing pipeline
            processed_image = await self._apply_preprocessing_pipeline(cv_image, document_type)
            
            # Quality assessment
            quality_score = self._assess_image_quality(processed_image)
            logger.info("📊 Image quality assessed", quality_score=quality_score)
            
            if quality_score < 0.5:
                logger.warning("⚠️ Poor image quality detected", quality_score=quality_score)
            
            return processed_image
            
        except Exception as e:
            logger.error("❌ Document preprocessing failed", error=str(e))
            raise
    
    def _load_image_from_bytes(self, file_content: bytes) -> Image.Image:
        """Load image from byte content"""
        
        try:
            image = Image.open(io.BytesIO(file_content))
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            return image
            
        except Exception as e:
            logger.error("❌ Failed to load image", error=str(e))
            raise ValueError(f"Invalid image format: {str(e)}")
    
    def _resize_image(self, image: Image.Image) -> Image.Image:
        """Resize image if it exceeds maximum dimensions"""
        
        width, height = image.size
        max_dimension = max(width, height)
        
        if max_dimension > self.max_size:
            # Calculate new dimensions maintaining aspect ratio
            ratio = self.max_size / max_dimension
            new_width = int(width * ratio)
            new_height = int(height * ratio)
            
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            logger.info("📏 Image resized", 
                       original_size=f"{width}x{height}",
                       new_size=f"{new_width}x{new_height}")
        
        return image
    
    async def _apply_preprocessing_pipeline(self, image: np.ndarray, document_type: str) -> np.ndarray:
        """Apply comprehensive preprocessing pipeline"""
        
        # Step 1: Noise reduction
        denoised = cv2.bilateralFilter(image, 9, 75, 75)
        
        # Step 2: Contrast enhancement
        enhanced = self._enhance_contrast(denoised)
        
        # Step 3: Perspective correction (if needed)
        corrected = await self._correct_perspective(enhanced, document_type)
        
        # Step 4: Sharpening
        sharpened = self._apply_sharpening(corrected)
        
        return sharpened
    
    def _enhance_contrast(self, image: np.ndarray) -> np.ndarray:
        """Enhance image contrast using CLAHE"""
        
        # Convert to LAB color space
        lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
        
        # Apply CLAHE to L channel
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        lab[:, :, 0] = clahe.apply(lab[:, :, 0])
        
        # Convert back to BGR
        enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
        
        return enhanced
    
    async def _correct_perspective(self, image: np.ndarray, document_type: str) -> np.ndarray:
        """Detect and correct perspective distortion"""
        
        # Convert to grayscale for edge detection
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Edge detection
        edges = cv2.Canny(gray, 50, 150, apertureSize=3)
        
        # Find contours
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Find the largest rectangular contour (likely the document)
        document_contour = self._find_document_contour(contours, image.shape)
        
        if document_contour is not None:
            # Apply perspective correction
            corrected = self._apply_perspective_transform(image, document_contour)
            return corrected
        
        # Return original if no clear document boundary found
        return image
    
    def _find_document_contour(self, contours, image_shape) -> np.ndarray:
        """Find the contour that likely represents the document boundary"""
        
        height, width = image_shape[:2]
        min_area = (width * height) * 0.1  # Minimum 10% of image area
        
        for contour in sorted(contours, key=cv2.contourArea, reverse=True):
            area = cv2.contourArea(contour)
            
            if area < min_area:
                continue
            
            # Approximate contour to polygon
            epsilon = 0.02 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)
            
            # Look for quadrilateral (4 corners)
            if len(approx) == 4:
                return approx
        
        return None
    
    def _apply_perspective_transform(self, image: np.ndarray, contour: np.ndarray) -> np.ndarray:
        """Apply perspective transformation to correct document orientation"""
        
        # Order points: top-left, top-right, bottom-right, bottom-left
        rect = self._order_points(contour.reshape(4, 2))
        
        # Calculate dimensions of the corrected document
        width_a = np.sqrt(((rect[2][0] - rect[3][0]) ** 2) + ((rect[2][1] - rect[3][1]) ** 2))
        width_b = np.sqrt(((rect[1][0] - rect[0][0]) ** 2) + ((rect[1][1] - rect[0][1]) ** 2))
        max_width = max(int(width_a), int(width_b))
        
        height_a = np.sqrt(((rect[1][0] - rect[2][0]) ** 2) + ((rect[1][1] - rect[2][1]) ** 2))
        height_b = np.sqrt(((rect[0][0] - rect[3][0]) ** 2) + ((rect[0][1] - rect[3][1]) ** 2))
        max_height = max(int(height_a), int(height_b))
        
        # Define destination points
        dst = np.array([
            [0, 0],
            [max_width - 1, 0],
            [max_width - 1, max_height - 1],
            [0, max_height - 1]
        ], dtype="float32")
        
        # Calculate perspective transform matrix
        matrix = cv2.getPerspectiveTransform(rect, dst)
        
        # Apply transformation
        warped = cv2.warpPerspective(image, matrix, (max_width, max_height))
        
        return warped
    
    def _order_points(self, pts: np.ndarray) -> np.ndarray:
        """Order points in clockwise order starting from top-left"""
        
        rect = np.zeros((4, 2), dtype="float32")
        
        # Sum and difference of coordinates
        s = pts.sum(axis=1)
        diff = np.diff(pts, axis=1)
        
        # Top-left: smallest sum, bottom-right: largest sum
        rect[0] = pts[np.argmin(s)]
        rect[2] = pts[np.argmax(s)]
        
        # Top-right: smallest difference, bottom-left: largest difference
        rect[1] = pts[np.argmin(diff)]
        rect[3] = pts[np.argmax(diff)]
        
        return rect
    
    def _apply_sharpening(self, image: np.ndarray) -> np.ndarray:
        """Apply unsharp masking for image sharpening"""
        
        # Create Gaussian blur
        gaussian = cv2.GaussianBlur(image, (0, 0), 2.0)
        
        # Unsharp masking
        sharpened = cv2.addWeighted(image, 1.5, gaussian, -0.5, 0)
        
        return sharpened
    
    def _assess_image_quality(self, image: np.ndarray) -> float:
        """Assess overall image quality (0.0 to 1.0)"""
        
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Calculate Laplacian variance (blur detection)
        laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
        
        # Normalize to 0-1 range (higher values indicate sharper images)
        blur_score = min(laplacian_var / 1000.0, 1.0)
        
        # Calculate brightness and contrast
        mean_brightness = np.mean(gray)
        brightness_score = 1.0 - abs(mean_brightness - 127.5) / 127.5
        
        contrast_score = np.std(gray) / 127.5
        contrast_score = min(contrast_score, 1.0)
        
        # Combined quality score
        quality_score = (blur_score * 0.5 + brightness_score * 0.25 + contrast_score * 0.25)

        return quality_score

    async def collect_training_data(self, file_content: bytes, document_type: str,
                                  extracted_data: Dict, fraud_analysis: Dict,
                                  user_feedback: Optional[Dict] = None):
        """Collect training data for ML model improvement"""

        try:
            # Import here to avoid circular imports
            from app.ml.training_manager import training_manager

            await training_manager.collect_training_data(
                image_data=file_content,
                document_type=document_type,
                extracted_data=extracted_data,
                fraud_analysis=fraud_analysis,
                user_feedback=user_feedback
            )

            logger.info("Training data collected successfully",
                       document_type=document_type)

        except Exception as e:
            logger.error("Failed to collect training data",
                        document_type=document_type,
                        error=str(e))
