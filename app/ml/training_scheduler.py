"""
Training Scheduler for automated quarterly model retraining
"""

import asyncio
from datetime import datetime, timedelta
from typing import Optional

import structlog
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger

from app.core.config import get_settings
from app.ml.training_manager import training_manager

logger = structlog.get_logger()


class TrainingScheduler:
    """Manages automated training schedules"""
    
    def __init__(self):
        self.settings = get_settings()
        self.scheduler: Optional[AsyncIOScheduler] = None
        self.is_running = False
        
    async def start(self):
        """Start the training scheduler"""
        
        if not self.settings.TRAINING_SCHEDULE_ENABLED:
            logger.info("Training scheduler disabled in configuration")
            return
            
        try:
            self.scheduler = AsyncIOScheduler()
            
            # Schedule quarterly training (every 3 months on the 1st at 2 AM)
            if self.settings.QUARTERLY_TRAINING_ENABLED:
                self.scheduler.add_job(
                    self._quarterly_training_job,
                    CronTrigger(month='1,4,7,10', day=1, hour=2, minute=0),
                    id='quarterly_training',
                    name='Quarterly Model Training',
                    max_instances=1,
                    coalesce=True
                )
                logger.info("Quarterly training scheduled")
            
            # Schedule daily cleanup (remove old training data)
            self.scheduler.add_job(
                self._cleanup_job,
                CronTrigger(hour=1, minute=0),
                id='daily_cleanup',
                name='Daily Training Data Cleanup',
                max_instances=1,
                coalesce=True
            )
            logger.info("Daily cleanup scheduled")
            
            # Schedule weekly model validation
            self.scheduler.add_job(
                self._validation_job,
                CronTrigger(day_of_week='sun', hour=3, minute=0),
                id='weekly_validation',
                name='Weekly Model Validation',
                max_instances=1,
                coalesce=True
            )
            logger.info("Weekly validation scheduled")
            
            # Start scheduler
            self.scheduler.start()
            self.is_running = True
            
            logger.info("✅ Training scheduler started successfully")
            
        except Exception as e:
            logger.error("❌ Failed to start training scheduler", error=str(e))
            raise
    
    async def stop(self):
        """Stop the training scheduler"""
        
        if self.scheduler and self.is_running:
            self.scheduler.shutdown(wait=True)
            self.is_running = False
            logger.info("Training scheduler stopped")
    
    async def trigger_immediate_training(self):
        """Trigger immediate model training"""
        
        logger.info("🚀 Triggering immediate model training...")
        
        try:
            await training_manager.train_models()
            logger.info("✅ Immediate training completed")
            
        except Exception as e:
            logger.error("❌ Immediate training failed", error=str(e))
            raise
    
    async def _quarterly_training_job(self):
        """Quarterly training job"""
        
        logger.info("🗓️ Starting quarterly training job...")
        
        try:
            # Check if we have sufficient data
            metadata_files = list((training_manager.training_data_path / "metadata").glob("*.json"))
            
            if len(metadata_files) < self.settings.MIN_TRAINING_SAMPLES:
                logger.warning(f"Insufficient data for quarterly training: {len(metadata_files)} samples")
                return
            
            # Perform training
            await training_manager.train_models()
            
            # Log training completion
            logger.info("✅ Quarterly training completed successfully",
                       samples_used=len(metadata_files))
            
            # Send notification (if configured)
            await self._send_training_notification("quarterly", True, len(metadata_files))
            
        except Exception as e:
            logger.error("❌ Quarterly training failed", error=str(e))
            await self._send_training_notification("quarterly", False, 0, str(e))
    
    async def _cleanup_job(self):
        """Daily cleanup job to remove old training data"""
        
        logger.info("🧹 Starting daily cleanup job...")
        
        try:
            # Remove training data older than 6 months
            cutoff_date = datetime.now() - timedelta(days=180)
            
            metadata_dir = training_manager.training_data_path / "metadata"
            images_dir = training_manager.training_data_path / "images"
            
            cleaned_count = 0
            
            for metadata_file in metadata_dir.glob("*.json"):
                try:
                    # Check file modification time
                    if datetime.fromtimestamp(metadata_file.stat().st_mtime) < cutoff_date:
                        # Load metadata to get image path
                        import json
                        with open(metadata_file, 'r') as f:
                            metadata = json.load(f)
                        
                        # Remove image file
                        image_path = Path(metadata.get('image_path', ''))
                        if image_path.exists():
                            image_path.unlink()
                        
                        # Remove metadata file
                        metadata_file.unlink()
                        cleaned_count += 1
                        
                except Exception as e:
                    logger.warning(f"Failed to clean up {metadata_file}", error=str(e))
                    continue
            
            logger.info("✅ Daily cleanup completed", files_cleaned=cleaned_count)
            
        except Exception as e:
            logger.error("❌ Daily cleanup failed", error=str(e))
    
    async def _validation_job(self):
        """Weekly model validation job"""
        
        logger.info("🔍 Starting weekly model validation...")
        
        try:
            # Load recent validation data (last 7 days)
            recent_date = datetime.now() - timedelta(days=7)
            
            metadata_dir = training_manager.training_data_path / "metadata"
            recent_files = []
            
            for metadata_file in metadata_dir.glob("*.json"):
                if datetime.fromtimestamp(metadata_file.stat().st_mtime) >= recent_date:
                    recent_files.append(metadata_file)
            
            if len(recent_files) < 10:
                logger.info("Insufficient recent data for validation", files_count=len(recent_files))
                return
            
            # TODO: Implement model validation logic
            # This would involve loading the current model and testing it against recent data
            
            logger.info("✅ Weekly validation completed", validation_samples=len(recent_files))
            
        except Exception as e:
            logger.error("❌ Weekly validation failed", error=str(e))
    
    async def _send_training_notification(self, training_type: str, success: bool, 
                                        samples_count: int, error_message: str = ""):
        """Send training completion notification"""
        
        try:
            # TODO: Implement notification system (email, webhook, etc.)
            notification_data = {
                'type': 'training_completion',
                'training_type': training_type,
                'success': success,
                'samples_count': samples_count,
                'timestamp': datetime.now().isoformat(),
                'error_message': error_message if not success else None
            }
            
            logger.info("Training notification", **notification_data)
            
        except Exception as e:
            logger.error("Failed to send training notification", error=str(e))
    
    def get_next_training_schedule(self) -> Optional[datetime]:
        """Get the next scheduled training time"""
        
        if not self.scheduler or not self.is_running:
            return None
        
        try:
            job = self.scheduler.get_job('quarterly_training')
            if job and job.next_run_time:
                return job.next_run_time.replace(tzinfo=None)
            
        except Exception as e:
            logger.error("Failed to get next training schedule", error=str(e))
        
        return None
    
    def get_scheduler_status(self) -> dict:
        """Get current scheduler status"""
        
        status = {
            'is_running': self.is_running,
            'quarterly_training_enabled': self.settings.QUARTERLY_TRAINING_ENABLED,
            'next_training': None,
            'jobs': []
        }
        
        if self.scheduler and self.is_running:
            try:
                status['next_training'] = self.get_next_training_schedule()
                
                for job in self.scheduler.get_jobs():
                    status['jobs'].append({
                        'id': job.id,
                        'name': job.name,
                        'next_run': job.next_run_time.replace(tzinfo=None) if job.next_run_time else None
                    })
                    
            except Exception as e:
                logger.error("Failed to get scheduler status", error=str(e))
        
        return status


# Global training scheduler instance
training_scheduler = TrainingScheduler()
