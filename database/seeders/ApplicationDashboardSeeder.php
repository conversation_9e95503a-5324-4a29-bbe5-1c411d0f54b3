<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Modules\Auth\Models\PortalUser;
use App\Modules\Users\Models\Profile;
use App\Modules\Entities\Models\Entity;
use App\Modules\Products\Models\Product;
use App\Modules\Applications\Models\Application;
use App\Modules\Applications\Models\ApplicationData;
use Illuminate\Support\Facades\Hash;

class ApplicationDashboardSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a test applicant
        $applicant = PortalUser::create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'user_type' => 'applicant'
        ]);

        // Create applicant profile
        Profile::create([
            'user_id' => $applicant->id,
            'first_name' => 'John',
            'last_name' => 'Smith',
            'telephone' => '+44 ************',
            'address' => '123 Test Street, London, UK',
            'active' => true,
        ]);

        // Create an entity (employer)
        $entity = Entity::create([
            'name' => 'Smith & Sons',
            'entity_code' => 'SMITH001',
            'entity_type' => 'client',
            'status' => true
        ]);

        // Link applicant to entity
        $applicant->entities()->attach($entity->id, ['role' => 'applicant']);

        // Create a DBS product
        $product = Product::create([
            'name' => 'DBS - Basic check',
            'code' => 'DBS001',
            'variant' => 'DBS'
        ]);

        // Create an application
        $application = Application::create([
            'applicant_id' => $applicant->id,
            'product_id' => $product->id,
            'status' => 'in_progress',
            'result' => null,
            'submitted_by' => $applicant->id,
            'external_reference' => 'APP-' . str_pad('1', 6, '0', STR_PAD_LEFT),
            'consent_date' => now()->subDays(5),
            'consent_user' => $applicant->id,
        ]);

        // Create application data (form fields)
        $applicationData = [
            'ApplicantDetails.Title' => 'Mr',
            'ApplicantDetails.FirstName' => 'John',
            'ApplicantDetails.LastName' => 'Smith',
            'ApplicantDetails.OtherLastName' => 'Alex',
            'ApplicantDetails.DateOfBirth' => '1984-06-04',
            'ApplicantDetails.PlaceOfBirth' => 'London',
            'ApplicantDetails.Gender' => 'Male',
            'ApplicantDetails.Nationality' => 'British',
            'PotentialEmployerDetails.PositionAppliedFor' => 'Cleaner',
            'PotentialEmployerDetails.OrganisationName' => 'Smith & Sons',
            'ContactDetails.Email' => '<EMAIL>',
            'ContactDetails.TelephoneNumber' => '+44 ************',
            'AddressDetails.CurrentAddress.Line1' => '123 Test Street',
            'AddressDetails.CurrentAddress.City' => 'London',
            'AddressDetails.CurrentAddress.PostCode' => 'SW1A 1AA',
            'AddressDetails.CurrentAddress.Country' => 'United Kingdom',
            'UnspentConvictions' => 'No',
            'DeclarationByApplicant' => 'Yes',
            'LanguagePreference' => 'English',
            'IdentityVerified' => 'Yes',
            'EvidenceCheckedBy' => 'John Smith'
        ];

        foreach ($applicationData as $fieldKey => $value) {
            ApplicationData::create([
                'application_id' => $application->id,
                'field_key' => $fieldKey,
                'value' => $value,
            ]);
        }

        $this->command->info('Sample application dashboard data created successfully!');
        $this->command->info('Applicant Email: <EMAIL>');
        $this->command->info('Password: password');
        $this->command->info('Application ID: ' . $application->id);
    }
}
