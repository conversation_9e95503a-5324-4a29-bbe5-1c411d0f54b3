<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DocumentsNominatedProcessStampSeeder extends Seeder
{
    public function run(): void
    {
        // Check if DOCUMENTS_NOMINATED stamp already exists
        $existingStamp = DB::table('process_stamps_main')
            ->where('STAMP_TAG', 'DOCUMENTS_NOMINATED')
            ->first();

        if ($existingStamp) {
            $this->command->info('DOCUMENTS_NOMINATED stamp already exists');
            return;
        }

        // Create the DOCUMENTS_NOMINATED process stamp
        $stampId = DB::table('process_stamps_main')->insertGetId([
            'STAMP_TAG' => 'DOCUMENTS_NOMINATED',
            'STAMP_NAME' => 'Documents Nominated',
            'CATEGORY' => 'document',
            'ORDER' => 20, // After initial application steps
            'FIELD' => 'Comment',
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $this->command->info("Created DOCUMENTS_NOMINATED stamp with ID: {$stampId}");

        // Link this stamp to all DBS product types
        $dbsProducts = DB::table('products')
            ->whereIn('product_code', ['DBSEC', 'DBSSC', 'DBSBC', 'DSBASIC'])
            ->pluck('id');

        foreach ($dbsProducts as $productId) {
            // Check if link already exists
            $linkExists = DB::table('process_stamp_links')
                ->where('STAMP_ID', $stampId)
                ->where('PRODUCT_ID', $productId)
                ->exists();

            if (!$linkExists) {
                DB::table('process_stamp_links')->insert([
                    'STAMP_ID' => $stampId,
                    'PRODUCT_ID' => $productId,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);

                $this->command->info("Linked DOCUMENTS_NOMINATED stamp to product ID: {$productId}");
            }
        }

        $this->command->info('DOCUMENTS_NOMINATED process stamp setup completed');
    }
}
