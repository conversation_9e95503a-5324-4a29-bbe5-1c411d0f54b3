<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class FixProduct5StampDefinitions extends Seeder
{
    public function run(): void
    {
        // Product ID 5 is "DBS - Basic check" which should have the same stamp definitions as Product ID 2 "DBS Basic Check"
        
        // Get all stamp definitions that are associated with Product ID 2 (DBS Basic Check)
        $product2Definitions = DB::table('process_stamp_definition_products')
            ->where('product_id', 2)
            ->get();
            
        echo "Found " . $product2Definitions->count() . " stamp definitions for Product ID 2\n";
        
        // Add these same definitions to Product ID 5
        foreach ($product2Definitions as $definition) {
            // Check if this definition is already associated with Product ID 5
            $exists = DB::table('process_stamp_definition_products')
                ->where('process_stamp_definition_id', $definition->process_stamp_definition_id)
                ->where('product_id', 5)
                ->exists();
                
            if (!$exists) {
                DB::table('process_stamp_definition_products')->insert([
                    'process_stamp_definition_id' => $definition->process_stamp_definition_id,
                    'product_id' => 5,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
                echo "Added stamp definition {$definition->process_stamp_definition_id} to Product ID 5\n";
            } else {
                echo "Stamp definition {$definition->process_stamp_definition_id} already exists for Product ID 5\n";
            }
        }
        
        echo "Completed adding stamp definitions to Product ID 5\n";
        
        // Now initialize stamps for Application ID 3 which uses Product ID 5
        $stampService = app(\App\Services\ProcessStampService::class);
        $result = $stampService->initializeApplicationStamps(3, 5);
        
        if ($result) {
            echo "Successfully initialized process stamps for Application ID 3\n";
            
            // Verify the stamps were created
            $stamps = $stampService->getApplicationStamps(3);
            echo "Application ID 3 now has " . $stamps->count() . " process stamps\n";
            
            $sections = $stampService->getStampsBySection(3);
            echo "Application ID 3 now has " . count($sections) . " stamp sections\n";
        } else {
            echo "Failed to initialize process stamps for Application ID 3\n";
        }
    }
}
