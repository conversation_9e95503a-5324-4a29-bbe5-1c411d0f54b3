<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Modules\Auth\Models\PortalUser;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class PortalUserFactory extends Factory
{
    protected $model = PortalUser::class;

    public function definition(): array
    {
        return [
            'email' => fake()->unique()->safeEmail(),
            'password' => Hash::make('password'),
            'user_type' => 'client_user',
        ];
    }

    public function applicant(): static
    {
        return $this->state(fn (array $attributes) => [
            'user_type' => 'applicant',
        ]);
    }

    public function clientUser(): static
    {
        return $this->state(fn (array $attributes) => [
            'user_type' => 'client_user',
        ]);
    }

    public function requester(): static
    {
        return $this->state(fn (array $attributes) => [
            'user_type' => 'requester',
        ]);
    }

    public function documentChecker(): static
    {
        return $this->state(fn (array $attributes) => [
            'user_type' => 'document_checker',
        ]);
    }
}
