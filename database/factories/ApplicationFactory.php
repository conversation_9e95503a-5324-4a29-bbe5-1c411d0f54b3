<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Application;
use Illuminate\Database\Eloquent\Factories\Factory;

class ApplicationFactory extends Factory
{
    protected $model = Application::class;

    public function definition(): array
    {
        return [
            'reference_number' => fake()->unique()->regexify('[A-Z]{3}[0-9]{6}'),
            'applicant_id' => 1,
            'product_id' => 1,
            'status' => fake()->randomElement(['pending', 'in_progress', 'completed']),
            'form_data' => [
                'first_name' => fake()->firstName(),
                'last_name' => fake()->lastName(),
                'email' => fake()->email(),
            ],
        ];
    }
}
