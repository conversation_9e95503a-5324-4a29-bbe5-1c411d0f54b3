<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Modules\Products\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProductFactory extends Factory
{
    protected $model = Product::class;

    public function definition(): array
    {
        return [
            'name' => fake()->words(3, true),
            'code' => fake()->unique()->regexify('[A-Z]{4}[0-9]{3}'),
            'variant' => fake()->randomElement(['A', 'B', 'C']),
        ];
    }


}
