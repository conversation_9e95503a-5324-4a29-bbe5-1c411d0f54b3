<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Modules\Entities\Models\Entity;
use SolidFuse\Modules\Entities\Enums\EntityType;
use Illuminate\Database\Eloquent\Factories\Factory;

class EntityFactory extends Factory
{
    protected $model = Entity::class;

    public function definition(): array
    {
        return [
            'name' => fake()->company(),
            'entity_code' => fake()->unique()->regexify('[A-Z]{3}[0-9]{3}'),
            'entity_type' => EntityType::CLIENT,
            'status' => true,
        ];
    }

    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => false,
        ]);
    }

    public function superGroup(): static
    {
        return $this->state(fn (array $attributes) => [
            'entity_type' => EntityType::SUPER_GROUP,
        ]);
    }

    public function parentGroup(): static
    {
        return $this->state(fn (array $attributes) => [
            'entity_type' => EntityType::PARENT_GROUP,
        ]);
    }

    public function client(): static
    {
        return $this->state(fn (array $attributes) => [
            'entity_type' => EntityType::CLIENT,
        ]);
    }
}
