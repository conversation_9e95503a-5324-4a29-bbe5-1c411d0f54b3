<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('doc_daf_data', function (Blueprint $table) {
            $table->id();
            $table->foreignId('application_id')->constrained('applications')->onDelete('cascade');
            $table->foreignId('document_type_id')->constrained('document_types')->onDelete('cascade');
            $table->tinyInteger('route_number'); // 1, 2, or 3
            $table->json('document_data'); // All document details (passport number, dates, etc.)
            $table->boolean('confirms_address')->default(false);
            $table->enum('status', ['nominated', 'verified', 'rejected', 'pending'])->default('nominated');
            $table->text('notes')->nullable();
            $table->foreignId('nominated_by')->constrained('portal_users')->onDelete('cascade');
            $table->foreignId('verified_by')->nullable()->constrained('portal_users')->onDelete('set null');
            $table->timestamps();

            $table->index(['application_id', 'route_number']);
            $table->index(['application_id', 'status']);
            $table->unique(['application_id', 'document_type_id', 'route_number'], 'unique_app_doc_route');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('doc_daf_data');
    }
};
