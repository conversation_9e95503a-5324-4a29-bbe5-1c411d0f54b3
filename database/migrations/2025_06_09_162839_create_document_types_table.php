<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('document_types', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('document_group', 10); // '1', '1a', '2a', '2b', 'a', 'b1', 'b2'
            $table->boolean('requires_photo')->default(false);
            $table->boolean('confirms_address')->default(false);
            $table->json('applicable_countries')->nullable(); // ['GB', 'UK'] or ['ANY']
            $table->json('data_fields'); // Dynamic form fields definition
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['document_group', 'is_active']);
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('document_types');
    }
};
