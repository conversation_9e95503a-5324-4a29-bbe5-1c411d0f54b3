<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('document_files', function (Blueprint $table) {
            $table->id();
            $table->foreignId('application_id')->constrained('applications')->onDelete('cascade');
            $table->foreignId('doc_daf_data_id')->constrained('doc_daf_data')->onDelete('cascade');
            
            // File identification
            $table->string('original_filename');
            $table->string('stored_filename');
            $table->uuid('file_uuid')->unique(); // UUID for file identification
            
            // S3 storage details
            $table->string('s3_key', 500);
            $table->string('s3_bucket', 100);
            $table->string('s3_region', 50);
            $table->string('s3_version_id', 100)->nullable();
            $table->string('s3_etag', 100)->nullable();
            
            // File metadata
            $table->bigInteger('file_size');
            $table->string('mime_type', 100);
            $table->string('file_hash', 64); // SHA-256 hash for integrity
            $table->string('file_extension', 10);
            
            // Storage and lifecycle
            $table->enum('storage_class', ['STANDARD', 'STANDARD_IA', 'GLACIER', 'DEEP_ARCHIVE'])
                  ->default('STANDARD');
            $table->timestamp('transition_to_ia_at')->nullable();
            $table->timestamp('transition_to_glacier_at')->nullable();
            
            // Upload details
            $table->foreignId('uploaded_by')->constrained('portal_users');
            $table->timestamp('uploaded_at');
            $table->ipAddress('upload_ip')->nullable();
            $table->string('upload_session_id', 100)->nullable();
            
            // Access tracking (basic counters only - detailed logs in centralized system)
            $table->integer('access_count')->default(0);
            $table->timestamp('last_accessed_at')->nullable();
            
            // Status and lifecycle
            $table->enum('status', ['active', 'archived', 'deleted', 'corrupted'])->default('active');
            $table->timestamp('archived_at')->nullable();
            $table->timestamp('deleted_at')->nullable();
            $table->string('deletion_reason', 200)->nullable();
            
            // GDPR and compliance
            $table->date('retention_until');
            $table->enum('data_classification', ['public', 'internal', 'confidential', 'restricted'])
                  ->default('restricted');
            $table->string('legal_basis', 100)->default('legitimate_interest');
            
            $table->timestamps();
            
            // Performance indexes
            $table->index(['application_id', 'status']);
            $table->index(['doc_daf_data_id']);
            $table->index(['s3_bucket', 's3_key']);
            $table->index(['file_hash']);
            $table->index(['uploaded_at']);
            $table->index(['retention_until', 'status']);
            $table->index(['storage_class']);
            $table->index(['status', 'archived_at']);
            $table->index(['file_uuid']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('document_files');
    }
};
