<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('doc_daf_data', function (Blueprint $table) {
            // Add new fields for config-based document types
            $table->string('document_type_key')->nullable()->after('document_type_id');
            $table->string('document_name')->nullable()->after('document_type_key');
            $table->string('document_group')->nullable()->after('document_name');
        });

        // Remove the foreign key constraint and drop the column
        Schema::table('doc_daf_data', function (Blueprint $table) {
            $table->dropForeign(['document_type_id']);
            $table->dropColumn('document_type_id');
        });

        // Make the new fields required
        Schema::table('doc_daf_data', function (Blueprint $table) {
            $table->string('document_type_key')->nullable(false)->change();
            $table->string('document_name')->nullable(false)->change();
            $table->string('document_group')->nullable(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('doc_daf_data', function (Blueprint $table) {
            // Add back document_type_id column
            $table->unsignedBigInteger('document_type_id')->nullable()->after('application_id');

            // Restore foreign key constraint (only if document_types table exists)
            $table->foreign('document_type_id')->references('id')->on('document_types')->onDelete('cascade');

            // Remove the new fields
            $table->dropColumn(['document_type_key', 'document_name', 'document_group']);
        });
    }
};
