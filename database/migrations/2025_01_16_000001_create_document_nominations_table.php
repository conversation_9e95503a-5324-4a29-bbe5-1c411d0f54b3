<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('document_nominations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('application_id')->constrained('applications')->onDelete('cascade');
            $table->string('document_type_key', 100);
            $table->string('document_name', 255);
            $table->string('document_group', 10);
            $table->tinyInteger('route_number');
            $table->json('key_values');
            $table->boolean('confirms_address')->default(false);
            $table->enum('status', [
                'nominated',
                'under_review', 
                'approved',
                'rejected',
                'resubmission_required'
            ])->default('nominated');
            $table->foreignId('nominated_by')->constrained('portal_users');
            $table->enum('verified_by_type', ['admin_user', 'portal_user', 'system'])->nullable();
            $table->unsignedBigInteger('verified_by_user_id')->nullable();
            $table->timestamp('verified_at')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['application_id', 'status']);
            $table->index(['document_type_key', 'status']);
            $table->index(['nominated_by']);
            $table->index(['route_number', 'document_group']);
            $table->index(['status', 'verified_at']);
            $table->index(['created_at']);

            // Unique constraint to prevent duplicate nominations
            $table->unique(['application_id', 'document_type_key', 'route_number'], 'unique_app_doc_route');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('document_nominations');
    }
};
