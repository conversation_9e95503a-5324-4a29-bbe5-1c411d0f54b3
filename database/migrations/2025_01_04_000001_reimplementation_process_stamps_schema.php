<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        
        Schema::dropIfExists('process_stamps');
        Schema::dropIfExists('process_stamp_definition_products');
        Schema::dropIfExists('process_stamp_definitions');
        
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        Schema::create('process_stamps_main', function (Blueprint $table) {
            $table->id('SAMP_ID');
            $table->string('STAMP_TAG', 100)->unique();
            $table->string('STAMP_NAME', 255);
            $table->enum('CATEGORY', ['initial', 'document', 'remedial', 'final', 'completion']);
            $table->integer('ORDER')->default(0);
            $table->enum('FIELD', ['Comment', 'Date', 'TextField'])->nullable();
            $table->timestamps();

            $table->index(['CATEGORY', 'ORDER']);
            $table->index('STAMP_TAG');
        });

        Schema::create('process_stamp_links', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('STAMP_ID');
            $table->unsignedBigInteger('PRODUCT_ID');
            $table->timestamps();

            $table->foreign('STAMP_ID')->references('SAMP_ID')->on('process_stamps_main')->onDelete('cascade');
            $table->foreign('PRODUCT_ID')->references('id')->on('products')->onDelete('cascade');
            
            $table->unique(['STAMP_ID', 'PRODUCT_ID']);
            $table->index('PRODUCT_ID');
        });

        Schema::create('processed_stamps', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('application_id');
            $table->unsignedBigInteger('stamp_id');
            $table->unsignedBigInteger('user_id');
            $table->enum('status', ['pending', 'completed', 'skipped'])->default('pending');
            $table->json('field_data')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();

            $table->foreign('application_id')->references('id')->on('applications')->onDelete('cascade');
            $table->foreign('stamp_id')->references('SAMP_ID')->on('process_stamps_main')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('admin_users')->onDelete('cascade');
            
            $table->unique(['application_id', 'stamp_id']);
            $table->index(['application_id', 'status']);
            $table->index('stamp_id');
        });
    }

    public function down(): void
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        
        Schema::dropIfExists('processed_stamps');
        Schema::dropIfExists('process_stamp_links');
        Schema::dropIfExists('process_stamps_main');
        
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        Schema::create('process_stamp_definitions', function (Blueprint $table) {
            $table->id();
            $table->string('stamp_code', 100)->unique();
            $table->string('stamp_name', 255);
            $table->text('stamp_description')->nullable();
            $table->string('category', 50);
            $table->integer('sort_order')->default(0);
            $table->json('conditions')->nullable();
            $table->boolean('is_required')->default(true);
            $table->boolean('is_automated')->default(false);
            $table->string('completion_trigger', 100)->nullable();
            $table->json('stamp_data_fields')->nullable();
            $table->json('next_stamps')->nullable();
            $table->timestamps();

            $table->index(['category', 'sort_order']);
        });

        Schema::create('process_stamp_definition_products', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('process_stamp_definition_id');
            $table->unsignedBigInteger('product_id');
            $table->timestamps();

            $table->foreign('process_stamp_definition_id')->references('id')->on('process_stamp_definitions')->onDelete('cascade');
            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
            
            $table->unique(['process_stamp_definition_id', 'product_id'], 'unique_definition_product');
        });

        Schema::create('process_stamps', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('product_id');
            $table->unsignedBigInteger('application_id');
            $table->string('stamp_code', 100);
            $table->string('stamp_name', 255);
            $table->text('stamp_description')->nullable();
            $table->string('category', 50);
            $table->integer('sort_order')->default(0);
            $table->enum('status', ['pending', 'in_progress', 'completed', 'stamped', 'skipped', 'not_applicable'])->default('pending');
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->unsignedBigInteger('completed_by')->nullable();
            $table->json('stamp_data')->nullable();
            $table->text('comments')->nullable();
            $table->json('internal_notes')->nullable();
            $table->json('conditions')->nullable();
            $table->boolean('is_required')->default(true);
            $table->boolean('is_automated')->default(false);
            $table->json('next_stamps')->nullable();
            $table->string('completion_trigger', 100)->nullable();
            $table->timestamps();

            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
            $table->foreign('application_id')->references('id')->on('applications')->onDelete('cascade');
            $table->foreign('completed_by')->references('id')->on('admin_users')->onDelete('set null');
            
            $table->index(['application_id', 'status']);
            $table->index(['product_id', 'category']);
            $table->index('stamp_code');
        });
    }
};
