<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('document_types');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Recreate the document_types table if needed
        Schema::create('document_types', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('document_group', 10);
            $table->boolean('requires_photo')->default(false);
            $table->boolean('confirms_address')->default(false);
            $table->json('applicable_countries')->nullable();
            $table->json('data_fields');
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['document_group', 'is_active']);
            $table->index('is_active');
        });
    }
};
