<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('document_files', function (Blueprint $table) {
            $table->id();
            $table->foreignId('document_nomination_id')->constrained('document_nominations')->onDelete('cascade');
            $table->string('original_filename', 255);
            $table->string('s3_key', 500);
            $table->string('s3_bucket', 100);
            $table->bigInteger('file_size');
            $table->string('mime_type', 100);
            $table->string('file_hash', 64); // SHA-256 hash
            $table->foreignId('uploaded_by')->constrained('portal_users');
            $table->timestamps();

            // Indexes for performance
            $table->index(['document_nomination_id']);
            $table->index(['s3_key']);
            $table->index(['file_hash']);
            $table->index(['uploaded_by']);
            $table->index(['created_at']);

            // Ensure unique S3 keys
            $table->unique(['s3_bucket', 's3_key'], 'unique_s3_location');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('document_files');
    }
};
