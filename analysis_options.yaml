include: package:flutter_lints/flutter.yaml

analyzer:
  errors:
    unused_import: ignore
  strong-mode:
    implicit-casts: false
    implicit-dynamic: false

linter:
  rules:
    - always_declare_return_types
    - annotate_overrides
    - avoid_print
    - camel_case_types
    - constant_identifier_names
    - prefer_const_constructors
    - type_init_formals
    - avoid_returning_null
    - prefer_is_empty
    - avoid_single_cascade_in_expression_statements
    - avoid_unused_constructor_parameters
    - avoid_void_async
    - curly_braces_in_flow_control_structures
    - directives_ordering
    - empty_statements
    - iterable_contains_unrelated_type
    - list_remove_unrelated_type
    - prefer_adjacent_string_concatenation
    - prefer_collection_literals
    - prefer_const_literals_to_create_immutables
    - prefer_equal_for_default_values
    - prefer_typing_uninitialized_variables
    - unnecessary_getters_setters
    - use_function_type_syntax_for_parameters

dart_code_metrics:
  metrics:
    cyclomatic-complexity: 10
    number-of-parameters: 4
    maximum-nesting-level: 1 
    lines-of-code: 50

