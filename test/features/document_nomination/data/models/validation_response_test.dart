import 'package:SolidCheck/features/document_nomination/data/models/validation_response.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ValidationResponse', () {
    test('should create ValidationResponse from JSON correctly', () {
      final json = {
        'success': true,
        'data': {
          'validation_result': {
            'is_valid': true,
            'route_completed': true,
            'can_proceed': true
          },
          'route_analysis': {
            'requirements_met': {
              'group_1_documents': {
                'required': 1,
                'provided': 1,
                'met': true
              },
              'total_documents': {
                'required': 3,
                'provided': 3,
                'met': true
              }
            }
          },
          'document_validations': [
            {
              'document_type_id': 1,
              'is_valid': true,
              'errors': [],
              'warnings': ['Document expires soon']
            }
          ],
          'next_steps': {
            'action': 'complete_route',
            'message': 'All requirements met for Route 1'
          }
        }
      };

      final response = ValidationResponse.fromJson(json);

      expect(response.success, isTrue);
      expect(response.data.validationResult.isValid, isTrue);
      expect(response.data.validationResult.routeCompleted, isTrue);
      expect(response.data.validationResult.canProceed, isTrue);
      
      expect(response.data.routeAnalysis.requirementsMet.length, equals(2));
      expect(response.data.routeAnalysis.requirementsMet['group_1_documents']?.met, isTrue);
      
      expect(response.data.documentValidations.length, equals(1));
      expect(response.data.documentValidations[0].documentTypeId, equals(1));
      expect(response.data.documentValidations[0].isValid, isTrue);
      expect(response.data.documentValidations[0].warnings.length, equals(1));
      
      expect(response.data.nextSteps.action, equals('complete_route'));
      expect(response.data.nextSteps.message, equals('All requirements met for Route 1'));
    });

    test('should handle validation errors', () {
      final json = {
        'success': false,
        'data': {
          'validation_result': {
            'is_valid': false,
            'route_completed': false,
            'can_proceed': false
          },
          'route_analysis': {
            'requirements_met': {}
          },
          'document_validations': [
            {
              'document_type_id': 1,
              'is_valid': false,
              'errors': ['Document has expired', 'Invalid format'],
              'warnings': []
            }
          ],
          'next_steps': {
            'action': 'fix_errors',
            'message': 'Please fix the validation errors'
          }
        }
      };

      final response = ValidationResponse.fromJson(json);

      expect(response.success, isFalse);
      expect(response.data.validationResult.isValid, isFalse);
      expect(response.data.validationResult.routeCompleted, isFalse);
      expect(response.data.validationResult.canProceed, isFalse);
      
      expect(response.data.documentValidations[0].isValid, isFalse);
      expect(response.data.documentValidations[0].errors.length, equals(2));
      expect(response.data.documentValidations[0].errors[0], equals('Document has expired'));
      expect(response.data.documentValidations[0].errors[1], equals('Invalid format'));
    });
  });

  group('ValidationResult', () {
    test('should create ValidationResult from JSON correctly', () {
      final json = {
        'is_valid': true,
        'route_completed': false,
        'can_proceed': true
      };

      final result = ValidationResult.fromJson(json);

      expect(result.isValid, isTrue);
      expect(result.routeCompleted, isFalse);
      expect(result.canProceed, isTrue);
    });

    test('should handle defaults for missing fields', () {
      final json = <String, dynamic>{};

      final result = ValidationResult.fromJson(json);

      expect(result.isValid, isFalse);
      expect(result.routeCompleted, isFalse);
      expect(result.canProceed, isFalse);
    });
  });

  group('RequirementStatus', () {
    test('should create RequirementStatus from JSON correctly', () {
      final json = {
        'required': 3,
        'provided': 2,
        'met': false
      };

      final status = RequirementStatus.fromJson(json);

      expect(status.required, equals(3));
      expect(status.provided, equals(2));
      expect(status.met, isFalse);
    });

    test('should handle defaults for missing fields', () {
      final json = <String, dynamic>{};

      final status = RequirementStatus.fromJson(json);

      expect(status.required, equals(0));
      expect(status.provided, equals(0));
      expect(status.met, isFalse);
    });
  });

  group('DocumentValidation', () {
    test('should create DocumentValidation from JSON correctly', () {
      final json = {
        'document_type_id': 1,
        'is_valid': true,
        'errors': ['Error 1', 'Error 2'],
        'warnings': ['Warning 1']
      };

      final validation = DocumentValidation.fromJson(json);

      expect(validation.documentTypeId, equals(1));
      expect(validation.isValid, isTrue);
      expect(validation.errors.length, equals(2));
      expect(validation.errors[0], equals('Error 1'));
      expect(validation.warnings.length, equals(1));
      expect(validation.warnings[0], equals('Warning 1'));
    });

    test('should handle empty errors and warnings', () {
      final json = {
        'document_type_id': 2,
        'is_valid': true,
        'errors': [],
        'warnings': []
      };

      final validation = DocumentValidation.fromJson(json);

      expect(validation.documentTypeId, equals(2));
      expect(validation.isValid, isTrue);
      expect(validation.errors.isEmpty, isTrue);
      expect(validation.warnings.isEmpty, isTrue);
    });

    test('should handle defaults for missing fields', () {
      final json = <String, dynamic>{};

      final validation = DocumentValidation.fromJson(json);

      expect(validation.documentTypeId, equals(0));
      expect(validation.isValid, isFalse);
      expect(validation.errors.isEmpty, isTrue);
      expect(validation.warnings.isEmpty, isTrue);
    });
  });

  group('NextSteps', () {
    test('should create NextSteps from JSON correctly', () {
      final json = {
        'action': 'submit_documents',
        'message': 'Ready to submit all documents'
      };

      final nextSteps = NextSteps.fromJson(json);

      expect(nextSteps.action, equals('submit_documents'));
      expect(nextSteps.message, equals('Ready to submit all documents'));
    });

    test('should handle defaults for missing fields', () {
      final json = <String, dynamic>{};

      final nextSteps = NextSteps.fromJson(json);

      expect(nextSteps.action, equals(''));
      expect(nextSteps.message, equals(''));
    });
  });
}
