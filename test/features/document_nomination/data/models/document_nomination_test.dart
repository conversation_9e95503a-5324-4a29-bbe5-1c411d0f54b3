import 'package:SolidCheck/features/document_nomination/data/models/document_nomination.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DocumentNomination', () {
    test('should create DocumentNomination from JSON correctly', () {
      final json = {
        'document_type_id': 1,
        'document_data': {
          'passport_number': '*********',
          'issue_country': 'United Kingdom',
          'issue_date': '2020-01-15',
          'expiry_date': '2030-01-15'
        },
        'confirms_address': false
      };

      final nomination = DocumentNomination.fromJson(json);

      expect(nomination.documentTypeId, equals(1));
      expect(nomination.documentData['passport_number'], equals('*********'));
      expect(nomination.documentData['issue_country'], equals('United Kingdom'));
      expect(nomination.documentData['issue_date'], equals('2020-01-15'));
      expect(nomination.documentData['expiry_date'], equals('2030-01-15'));
      expect(nomination.confirmsAddress, isFalse);
    });

    test('should convert to JSON correctly', () {
      final nomination = DocumentNomination(
        documentTypeId: 1,
        documentData: {
          'passport_number': '*********',
          'issue_country': 'United Kingdom',
        },
        confirmsAddress: true,
      );

      final json = nomination.toJson();

      expect(json['document_type_id'], equals(1));
      expect(json['document_data']['passport_number'], equals('*********'));
      expect(json['document_data']['issue_country'], equals('United Kingdom'));
      expect(json['confirms_address'], isTrue);
    });

    test('should create copy with updated values', () {
      final original = DocumentNomination(
        documentTypeId: 1,
        documentData: {'field1': 'value1'},
        confirmsAddress: false,
      );

      final updated = original.copyWith(
        documentTypeId: 2,
        confirmsAddress: true,
      );

      expect(updated.documentTypeId, equals(2));
      expect(updated.documentData['field1'], equals('value1'));
      expect(updated.confirmsAddress, isTrue);
      
      expect(original.documentTypeId, equals(1));
      expect(original.confirmsAddress, isFalse);
    });

    test('should create copy with updated document data', () {
      final original = DocumentNomination(
        documentTypeId: 1,
        documentData: {'field1': 'value1'},
        confirmsAddress: false,
      );

      final updated = original.copyWith(
        documentData: {'field1': 'value1', 'field2': 'value2'},
      );

      expect(updated.documentData.length, equals(2));
      expect(updated.documentData['field1'], equals('value1'));
      expect(updated.documentData['field2'], equals('value2'));
      
      expect(original.documentData.length, equals(1));
    });

    test('should handle equality correctly', () {
      final nomination1 = DocumentNomination(
        documentTypeId: 1,
        documentData: {'field1': 'value1'},
        confirmsAddress: false,
      );

      final nomination2 = DocumentNomination(
        documentTypeId: 1,
        documentData: {'field1': 'different'},
        confirmsAddress: false,
      );

      final nomination3 = DocumentNomination(
        documentTypeId: 2,
        documentData: {'field1': 'value1'},
        confirmsAddress: false,
      );

      expect(nomination1, equals(nomination2));
      expect(nomination1, isNot(equals(nomination3)));
    });

    test('should handle defaults for missing fields', () {
      final json = <String, dynamic>{};

      final nomination = DocumentNomination.fromJson(json);

      expect(nomination.documentTypeId, equals(0));
      expect(nomination.documentData.isEmpty, isTrue);
      expect(nomination.confirmsAddress, isFalse);
    });
  });

  group('DocumentNominationRequest', () {
    test('should create DocumentNominationRequest from JSON correctly', () {
      final json = {
        'route_number': 1,
        'nominated_documents': [
          {
            'document_type_id': 1,
            'document_data': {'passport_number': '*********'},
            'confirms_address': false
          },
          {
            'document_type_id': 2,
            'document_data': {'licence_number': 'ABC123'},
            'confirms_address': true
          }
        ]
      };

      final request = DocumentNominationRequest.fromJson(json);

      expect(request.routeNumber, equals(1));
      expect(request.nominatedDocuments.length, equals(2));
      
      final firstDoc = request.nominatedDocuments[0];
      expect(firstDoc.documentTypeId, equals(1));
      expect(firstDoc.documentData['passport_number'], equals('*********'));
      expect(firstDoc.confirmsAddress, isFalse);
      
      final secondDoc = request.nominatedDocuments[1];
      expect(secondDoc.documentTypeId, equals(2));
      expect(secondDoc.documentData['licence_number'], equals('ABC123'));
      expect(secondDoc.confirmsAddress, isTrue);
    });

    test('should convert to JSON correctly', () {
      final request = DocumentNominationRequest(
        routeNumber: 2,
        nominatedDocuments: [
          DocumentNomination(
            documentTypeId: 1,
            documentData: {'passport_number': '*********'},
            confirmsAddress: false,
          ),
        ],
      );

      final json = request.toJson();

      expect(json['route_number'], equals(2));
      expect(json['nominated_documents'].length, equals(1));
      expect(json['nominated_documents'][0]['document_type_id'], equals(1));
      expect(json['nominated_documents'][0]['document_data']['passport_number'], equals('*********'));
      expect(json['nominated_documents'][0]['confirms_address'], isFalse);
    });

    test('should convert to raw JSON string', () {
      final request = DocumentNominationRequest(
        routeNumber: 1,
        nominatedDocuments: [],
      );

      final rawJson = request.toRawJson();

      expect(rawJson, isA<String>());
      expect(rawJson.contains('"route_number":1'), isTrue);
      expect(rawJson.contains('"nominated_documents":[]'), isTrue);
    });

    test('should handle empty nominated documents', () {
      final json = {
        'route_number': 3,
        'nominated_documents': []
      };

      final request = DocumentNominationRequest.fromJson(json);

      expect(request.routeNumber, equals(3));
      expect(request.nominatedDocuments.isEmpty, isTrue);
    });

    test('should handle defaults for missing fields', () {
      final json = <String, dynamic>{};

      final request = DocumentNominationRequest.fromJson(json);

      expect(request.routeNumber, equals(1));
      expect(request.nominatedDocuments.isEmpty, isTrue);
    });
  });
}
