import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DocumentType', () {
    test('should create DocumentType from JSON correctly', () {
      final json = {
        'key': 'passport_any',
        'name': 'Passport (Any Country)',
        'requires_photo': true,
        'confirms_address': false,
        'applicable_countries': ['ANY'],
        'data_fields': [
          {
            'name': 'passport_number',
            'type': 'string',
            'required': true,
            'label': 'Passport Number'
          },
          {
            'name': 'issue_country',
            'type': 'string',
            'required': true,
            'label': 'Issue Country'
          }
        ]
      };

      final documentType = DocumentType.fromJson(json);

      expect(documentType.key, equals('passport_any'));
      expect(documentType.name, equals('Passport (Any Country)'));
      expect(documentType.requiresPhoto, isTrue);
      expect(documentType.confirmsAddress, isFalse);
      expect(documentType.applicableCountries, equals(['ANY']));
      expect(documentType.dataFields.length, equals(2));

      final passportNumberField = documentType.dataFields[0];
      expect(passportNumberField.name, equals('passport_number'));
      expect(passportNumberField.type, equals('string'));
      expect(passportNumberField.required, isTrue);
      expect(passportNumberField.label, equals('Passport Number'));

      final issueCountryField = documentType.dataFields[1];
      expect(issueCountryField.name, equals('issue_country'));
      expect(issueCountryField.type, equals('string'));
      expect(issueCountryField.required, isTrue);
      expect(issueCountryField.label, equals('Issue Country'));
    });

    test('should handle empty data fields', () {
      final json = {
        'key': 'birth_certificate',
        'name': 'Birth Certificate',
        'requires_photo': false,
        'confirms_address': false,
        'applicable_countries': ['UK'],
        'data_fields': []
      };

      final documentType = DocumentType.fromJson(json);

      expect(documentType.key, equals('birth_certificate'));
      expect(documentType.name, equals('Birth Certificate'));
      expect(documentType.dataFields.isEmpty, isTrue);
    });

    test('should convert to JSON correctly', () {
      final documentType = DocumentType(
        key: 'passport_any',
        name: 'Passport (Any Country)',
        requiresPhoto: true,
        confirmsAddress: false,
        applicableCountries: ['ANY'],
        dataFields: [
          DocumentDataField(
            name: 'passport_number',
            type: 'string',
            required: true,
            label: 'Passport Number',
          ),
        ],
      );

      final json = documentType.toJson();

      expect(json['key'], equals('passport_any'));
      expect(json['name'], equals('Passport (Any Country)'));
      expect(json['requires_photo'], isTrue);
      expect(json['confirms_address'], isFalse);
      expect(json['applicable_countries'], equals(['ANY']));
      expect(json['data_fields'][0]['name'], equals('passport_number'));
      expect(json['data_fields'][0]['type'], equals('string'));
      expect(json['data_fields'][0]['required'], isTrue);
      expect(json['data_fields'][0]['label'], equals('Passport Number'));
    });

    test('should handle missing fields with defaults', () {
      final json = <String, dynamic>{};

      final documentType = DocumentType.fromJson(json);

      expect(documentType.key, equals(''));
      expect(documentType.name, equals(''));
      expect(documentType.requiresPhoto, isFalse);
      expect(documentType.confirmsAddress, isFalse);
      expect(documentType.applicableCountries.isEmpty, isTrue);
      expect(documentType.dataFields.isEmpty, isTrue);
    });
  });

  group('DocumentDataField', () {
    test('should create DocumentDataField from JSON correctly', () {
      final json = {
        'name': 'birth_date',
        'type': 'date',
        'required': true,
        'label': 'Date of Birth'
      };

      final field = DocumentDataField.fromJson(json);

      expect(field.name, equals('birth_date'));
      expect(field.type, equals('date'));
      expect(field.required, isTrue);
      expect(field.label, equals('Date of Birth'));
    });

    test('should handle optional fields', () {
      final json = {
        'name': 'optional_field',
        'type': 'string',
        'required': false,
        'label': 'Optional Field'
      };

      final field = DocumentDataField.fromJson(json);

      expect(field.name, equals('optional_field'));
      expect(field.type, equals('string'));
      expect(field.required, isFalse);
      expect(field.label, equals('Optional Field'));
    });

    test('should convert to JSON correctly', () {
      final field = DocumentDataField(
        name: 'test_field',
        type: 'string',
        required: true,
        label: 'Test Field',
      );

      final json = field.toJson();

      expect(json['name'], equals('test_field'));
      expect(json['type'], equals('string'));
      expect(json['required'], isTrue);
      expect(json['label'], equals('Test Field'));
    });

    test('should handle defaults for missing fields', () {
      final json = <String, dynamic>{};

      final field = DocumentDataField.fromJson(json);

      expect(field.name, equals(''));
      expect(field.type, equals('string'));
      expect(field.required, isFalse);
      expect(field.label, equals(''));
    });
  });
}
