import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:SolidCheck/features/document_nomination/presentation/widgets/document_data_form.dart';
import 'package:SolidCheck/features/document_nomination/presentation/widgets/document_upload_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DocumentDataForm Upload Functionality', () {
    testWidgets('should show upload widget for documents requiring photos', (WidgetTester tester) async {
      final documentType = DocumentType(
        key: 'passport',
        name: 'Passport',
        requiresPhoto: true,
        confirmsAddress: false,
        applicableCountries: ['UK'],
        dataFields: [
          DocumentDataField(
            name: 'passport_number',
            type: 'string',
            required: true,
            label: 'Passport Number',
          ),
        ],
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentDataForm(
              documentType: documentType,
              onSave: (nomination) {},
              onCancel: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should show upload section for documents requiring photos
      expect(find.text('Upload Document *'), findsOneWidget);
      expect(find.byType(DocumentUploadWidget), findsOneWidget);
    });

    testWidgets('should not show upload widget for documents not requiring photos', (WidgetTester tester) async {
      final documentType = DocumentType(
        key: 'bank_statement',
        name: 'Bank Statement',
        requiresPhoto: false,
        confirmsAddress: true,
        applicableCountries: ['UK'],
        dataFields: [
          DocumentDataField(
            name: 'account_number',
            type: 'string',
            required: true,
            label: 'Account Number',
          ),
        ],
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentDataForm(
              documentType: documentType,
              onSave: (nomination) {},
              onCancel: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should not show upload section for documents not requiring photos
      expect(find.text('Upload Document *'), findsNothing);
      expect(find.byType(DocumentUploadWidget), findsNothing);
    });

    testWidgets('should detect photo requirement by document name', (WidgetTester tester) async {
      final documentType = DocumentType(
        key: 'driving_license',
        name: 'Driving License',
        requiresPhoto: false, // API flag is false
        confirmsAddress: false,
        applicableCountries: ['UK'],
        dataFields: [],
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentDataForm(
              documentType: documentType,
              onSave: (nomination) {},
              onCancel: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should show upload section because name contains "driving license"
      expect(find.text('Upload Document *'), findsOneWidget);
      expect(find.byType(DocumentUploadWidget), findsOneWidget);
    });

    testWidgets('should prevent save without file upload for photo documents', (WidgetTester tester) async {
      final documentType = DocumentType(
        key: 'passport',
        name: 'Passport',
        requiresPhoto: true,
        confirmsAddress: false,
        applicableCountries: ['UK'],
        dataFields: [
          DocumentDataField(
            name: 'passport_number',
            type: 'string',
            required: true,
            label: 'Passport Number',
          ),
        ],
      );

      bool saveWasCalled = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentDataForm(
              documentType: documentType,
              onSave: (nomination) {
                saveWasCalled = true;
              },
              onCancel: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Fill in required field
      await tester.enterText(find.byType(TextFormField), '*********');

      // Try to save without uploading file
      await tester.tap(find.text('Add Document'));
      await tester.pumpAndSettle();

      // Should show error message and not call save
      expect(find.text('Please upload a document file before proceeding.'), findsOneWidget);
      expect(saveWasCalled, isFalse);
    });

    testWidgets('should show upload widget in correct layout', (WidgetTester tester) async {
      final documentType = DocumentType(
        key: 'passport',
        name: 'Passport',
        requiresPhoto: true,
        confirmsAddress: false,
        applicableCountries: ['UK'],
        dataFields: [
          DocumentDataField(
            name: 'passport_number',
            type: 'string',
            required: true,
            label: 'Passport Number',
          ),
        ],
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentDataForm(
              documentType: documentType,
              onSave: (nomination) {},
              onCancel: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should show upload widget and form fields in correct order
      expect(find.byType(DocumentUploadWidget), findsOneWidget);
      expect(find.text('Upload Document *'), findsOneWidget);
      expect(find.text('Passport Number *'), findsOneWidget);
    });

    testWidgets('should show proper form fields and upload section layout', (WidgetTester tester) async {
      final documentType = DocumentType(
        key: 'passport',
        name: 'Passport',
        requiresPhoto: true,
        confirmsAddress: false,
        applicableCountries: ['UK'],
        dataFields: [
          DocumentDataField(
            name: 'passport_number',
            type: 'string',
            required: true,
            label: 'Passport Number',
          ),
          DocumentDataField(
            name: 'issue_country',
            type: 'string',
            required: false,
            label: 'Issue Country',
          ),
        ],
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentDataForm(
              documentType: documentType,
              onSave: (nomination) {},
              onCancel: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should show form fields
      expect(find.text('Passport Number *'), findsOneWidget);
      expect(find.text('Issue Country'), findsOneWidget);
      
      // Should show upload section
      expect(find.text('Upload Document *'), findsOneWidget);
      expect(find.byType(DocumentUploadWidget), findsOneWidget);
      
      // Should show address confirmation checkbox
      expect(find.text('This document confirms my current address'), findsOneWidget);
      
      // Should show action buttons
      expect(find.text('Cancel'), findsOneWidget);
      expect(find.text('Add Document'), findsOneWidget);
    });
  });
}
