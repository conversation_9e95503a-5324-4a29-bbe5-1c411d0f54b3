import 'dart:io';

import 'package:SolidCheck/features/document_nomination/presentation/widgets/document_upload_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DocumentUploadWidget', () {
    testWidgets('should display initial upload area', (WidgetTester tester) async {
      bool fileSelected = false;
      File? selectedFile;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentUploadWidget(
              documentName: 'Test Document',
              onFileSelected: (File file) {
                fileSelected = true;
                selectedFile = file;
              },
            ),
          ),
        ),
      );

      expect(find.byType(DocumentUploadWidget), findsOneWidget);
      expect(find.byIcon(Icons.cloud_upload_outlined), findsOneWidget);
      expect(find.textContaining('Select your file or drag and drop'), findsOneWidget);
      expect(find.textContaining('PDF, PNG, JPG, JPEG'), findsOneWidget);
      expect(find.text('Browse'), findsOneWidget);
    });

    testWidgets('should display custom allowed extensions', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentUploadWidget(
              documentName: 'Test Document',
              onFileSelected: (File file) {},
              allowedExtensions: const ['pdf', 'docx'],
            ),
          ),
        ),
      );

      expect(find.textContaining('PDF, DOCX'), findsOneWidget);
    });

    testWidgets('should show error state with wrong format message', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentUploadWidget(
              documentName: 'Test Document',
              onFileSelected: (File file) {},
            ),
          ),
        ),
      );

      expect(find.byType(DocumentUploadWidget), findsOneWidget);
      expect(find.textContaining('Select your file or drag and drop'), findsOneWidget);
    });

    testWidgets('should show file size limit in widget', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentUploadWidget(
              documentName: 'Test Document',
              onFileSelected: (File file) {},
              maxFileSizeInMB: 5,
            ),
          ),
        ),
      );

      expect(find.byType(DocumentUploadWidget), findsOneWidget);
      expect(find.text('Browse'), findsOneWidget);
    });

    testWidgets('should show upload button initially', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentUploadWidget(
              documentName: 'Test Document',
              onFileSelected: (File file) {},
            ),
          ),
        ),
      );

      expect(find.text('Browse'), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
    });

    testWidgets('should handle callback when provided', (WidgetTester tester) async {
      bool callbackCalled = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentUploadWidget(
              documentName: 'Test Document',
              onFileSelected: (File file) {
                callbackCalled = true;
              },
              onUpload: () {
                callbackCalled = true;
              },
            ),
          ),
        ),
      );

      expect(find.byType(DocumentUploadWidget), findsOneWidget);
      expect(find.text('Browse'), findsOneWidget);
    });

    testWidgets('should display upload area with cloud icon', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DocumentUploadWidget(
              documentName: 'Test Document',
              onFileSelected: (File file) {},
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.cloud_upload_outlined), findsOneWidget);
      expect(find.byType(InkWell), findsAtLeastNWidgets(1));
      expect(find.byType(Container), findsAtLeastNWidgets(1));
    });
  });
}
