import 'package:SolidCheck/features/document_nomination/data/models/available_documents_response.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:SolidCheck/features/document_nomination/presentation/providers/document_nomination_provider.dart';
import 'package:SolidCheck/features/document_nomination/presentation/widgets/enhanced_document_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'enhanced_document_list_test.mocks.dart';

@GenerateMocks([DocumentNominationNotifier])
void main() {
  group('EnhancedDocumentList', () {
    late MockDocumentNominationNotifier mockNotifier;
    late AvailableDocumentsData testDocumentsData;
    late DocumentNominationState testState;

    setUp(() {
      mockNotifier = MockDocumentNominationNotifier();
      
      final testDocuments = [
        DocumentType(
          key: 'passport_any',
          name: 'Passport (Any Country)',
          requiresPhoto: true,
          confirmsAddress: false,
          applicableCountries: ['ANY'],
          dataFields: [
            DocumentDataField(
              name: 'passport_number',
              type: 'string',
              required: true,
              label: 'Passport Number',
            ),
          ],
        ),
        DocumentType(
          key: 'driving_licence',
          name: 'Driving Licence',
          requiresPhoto: true,
          confirmsAddress: true,
          applicableCountries: ['UK'],
          dataFields: [
            DocumentDataField(
              name: 'licence_number',
              type: 'string',
              required: true,
              label: 'Licence Number',
            ),
          ],
        ),
        DocumentType(
          key: 'utility_bill',
          name: 'Utility Bill',
          requiresPhoto: false,
          confirmsAddress: true,
          applicableCountries: ['UK'],
          dataFields: [
            DocumentDataField(
              name: 'account_number',
              type: 'string',
              required: true,
              label: 'Account Number',
            ),
          ],
        ),
      ];

      testDocumentsData = AvailableDocumentsData(
        application: ApplicationInfo(
          id: 'test-app-id',
          applicantName: 'Test Applicant',
          productCode: 'DBS_STANDARD',
        ),
        applicantContext: ApplicantContext(
          nationality: 'British',
          currentAddressCountry: 'United Kingdom',
          isUkNational: true,
          isUkResident: true,
          workType: 'EMPLOYEE',
          productCode: 'DBS_STANDARD',
        ),
        routing: RoutingInfo(
          recommendedRoute: 1,
          availableRoutes: [1, 2],
          routeRequirements: {},
        ),
        documents: DocumentsInfo(
          availableByGroup: {
            'group_1': DocumentGroup(
              groupName: 'group_1',
              documentCount: 2,
              documents: [testDocuments[0], testDocuments[1]],
            ),
            'group_2a': DocumentGroup(
              groupName: 'group_2a',
              documentCount: 1,
              documents: [testDocuments[2]],
            ),
          },
          totalAvailable: 3,
        ),
        currentNominations: [],
      );

      testState = DocumentNominationState(
        nominations: [],
        selectedRoute: 1,
        availableDocuments: testDocumentsData,
      );
    });

    Widget createTestWidget() {
      return ProviderScope(
        overrides: [
          documentNominationProvider.overrideWith((ref) => mockNotifier),
        ],
        child: MaterialApp(
          home: Scaffold(
            body: EnhancedDocumentList(
              applicationId: 'test-app-id',
              applicantName: 'Test Applicant',
              documentsData: testDocumentsData,
            ),
          ),
        ),
      );
    }

    testWidgets('should display header and instructions', (WidgetTester tester) async {
      when(mockNotifier.state).thenReturn(testState);
      
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Nominating Further Documents'), findsOneWidget);
      expect(find.text('To nominate or check further documents, please click on a document from the list below.'), findsOneWidget);
    });

    testWidgets('should display document groups correctly', (WidgetTester tester) async {
      when(mockNotifier.state).thenReturn(testState);
      
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Group 1: Primary identity documents'), findsOneWidget);
      expect(find.text('Group 2a: Trusted government documents'), findsOneWidget);
    });

    testWidgets('should display documents in groups', (WidgetTester tester) async {
      when(mockNotifier.state).thenReturn(testState);
      
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Passport (Any Country)'), findsOneWidget);
      expect(find.text('Driving Licence'), findsOneWidget);
      expect(find.text('Utility Bill'), findsOneWidget);
    });

    testWidgets('should show document badges correctly', (WidgetTester tester) async {
      when(mockNotifier.state).thenReturn(testState);
      
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Photo'), findsNWidgets(2)); // Passport and Driving Licence
      expect(find.text('Address'), findsNWidgets(2)); // Driving Licence and Utility Bill
    });

    testWidgets('should show nominated documents with check icon', (WidgetTester tester) async {
      final stateWithNominations = testState.copyWith(
        nominations: [
          DocumentNomination(
            documentTypeId: testDocumentsData.documents.availableByGroup['group_1']!.documents[0].id,
            documentData: {'passport_number': '*********'},
            confirmsAddress: false,
          ),
        ],
      );
      
      when(mockNotifier.state).thenReturn(stateWithNominations);
      
      await tester.pumpWidget(createTestWidget());

      expect(find.byIcon(Icons.check_circle), findsOneWidget);
    });

    testWidgets('should display legend correctly', (WidgetTester tester) async {
      when(mockNotifier.state).thenReturn(testState);
      
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Less than 3 months old'), findsOneWidget);
      expect(find.text('Less than 12 months old'), findsOneWidget);
    });

    testWidgets('should handle empty document list', (WidgetTester tester) async {
      final emptyDocumentsData = AvailableDocumentsData(
        application: ApplicationInfo(
          id: 'test-app-id',
          applicantName: 'Test Applicant',
          productCode: 'DBS_STANDARD',
        ),
        applicantContext: ApplicantContext(
          nationality: 'British',
          currentAddressCountry: 'United Kingdom',
          isUkNational: true,
          isUkResident: true,
          workType: 'EMPLOYEE',
          productCode: 'DBS_STANDARD',
        ),
        routing: RoutingInfo(
          recommendedRoute: 1,
          availableRoutes: [1],
          routeRequirements: {},
        ),
        documents: DocumentsInfo(
          availableByGroup: {},
          totalAvailable: 0,
        ),
        currentNominations: [],
      );

      final emptyState = testState.copyWith(availableDocuments: emptyDocumentsData);
      when(mockNotifier.state).thenReturn(emptyState);

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            documentNominationProvider.overrideWith((ref) => mockNotifier),
          ],
          child: MaterialApp(
            home: Scaffold(
              body: EnhancedDocumentList(
                applicationId: 'test-app-id',
                applicantName: 'Test Applicant',
                documentsData: emptyDocumentsData,
              ),
            ),
          ),
        ),
      );

      expect(find.text('No Documents Available'), findsOneWidget);
      expect(find.byIcon(Icons.description_outlined), findsOneWidget);
    });

    testWidgets('should handle document tap correctly', (WidgetTester tester) async {
      when(mockNotifier.state).thenReturn(testState);
      
      await tester.pumpWidget(createTestWidget());

      final documentItem = find.text('Passport (Any Country)');
      expect(documentItem, findsOneWidget);

      await tester.tap(documentItem);
      await tester.pumpAndSettle();

      // Verify that navigation would occur (in a real app, this would navigate to document detail screen)
      // Since we're testing in isolation, we just verify the tap was handled
    });

    testWidgets('should display correct group names', (WidgetTester tester) async {
      when(mockNotifier.state).thenReturn(testState);
      
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Group 1: Primary identity documents'), findsOneWidget);
      expect(find.text('Group 2a: Trusted government documents'), findsOneWidget);
    });

    testWidgets('should show documents in mobile layout on small screens', (WidgetTester tester) async {
      when(mockNotifier.state).thenReturn(testState);
      
      // Set a small screen size to trigger mobile layout
      tester.view.physicalSize = const Size(400, 800);
      tester.view.devicePixelRatio = 1.0;
      
      await tester.pumpWidget(createTestWidget());

      // Verify that documents are displayed (mobile layout should show all documents vertically)
      expect(find.text('Passport (Any Country)'), findsOneWidget);
      expect(find.text('Driving Licence'), findsOneWidget);
      expect(find.text('Utility Bill'), findsOneWidget);
      
      // Reset the screen size
      addTearDown(tester.view.reset);
    });

    testWidgets('should show documents in desktop layout on large screens', (WidgetTester tester) async {
      when(mockNotifier.state).thenReturn(testState);
      
      // Set a large screen size to trigger desktop layout
      tester.view.physicalSize = const Size(1200, 800);
      tester.view.devicePixelRatio = 1.0;
      
      await tester.pumpWidget(createTestWidget());

      // Verify that documents are displayed (desktop layout should show documents in columns)
      expect(find.text('Passport (Any Country)'), findsOneWidget);
      expect(find.text('Driving Licence'), findsOneWidget);
      expect(find.text('Utility Bill'), findsOneWidget);
      
      // Reset the screen size
      addTearDown(tester.view.reset);
    });
  });
}
