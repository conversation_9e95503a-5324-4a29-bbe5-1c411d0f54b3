import 'package:SolidCheck/features/document_nomination/presentation/screens/document_nomination_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DocumentNominationScreen Enhanced Methods', () {
    late DocumentNominationScreen screen;

    setUp(() {
      screen = const DocumentNominationScreen(
        applicationId: 'test-app-id',
        applicantName: '<PERSON>',
      );
    });

    test('should create DocumentNominationScreen instance', () {
      expect(screen, isA<DocumentNominationScreen>());
      expect(screen.applicationId, equals('test-app-id'));
      expect(screen.applicantName, equals('<PERSON> Doe'));
    });

    test('should have correct widget type', () {
      expect(screen, isA<StatefulWidget>());
    });
  });

  group('Document Proof Detection', () {
    test('should identify address proof documents correctly', () {
      final addressProofDocuments = [
        'Current Driving License, Paper version',
        'Mortgage Statement',
        'Bank/Building Society Statement',
        'Credit Card Statement',
        'Council Tax Statement',
        'Utility Bill',
        'P45/P60 Statement',
      ];

      for (final docName in addressProofDocuments) {
        expect(
          _mockDocumentProvidesAddressProof(docName),
          isTrue,
          reason: '$docName should be identified as address proof',
        );
      }
    });

    test('should identify date of birth proof documents correctly', () {
      final dateOfBirthProofDocuments = [
        'Birth Certificate',
        'Passport',
        'Adoption Certificate',
      ];

      for (final docName in dateOfBirthProofDocuments) {
        expect(
          _mockDocumentProvidesDateOfBirthProof(docName),
          isTrue,
          reason: '$docName should be identified as date of birth proof',
        );
      }
    });

    test('should not identify non-proof documents', () {
      final nonProofDocuments = [
        'Marriage Certificate',
        'HM Forces ID card',
        'Fire Arms License',
      ];

      for (final docName in nonProofDocuments) {
        expect(
          _mockDocumentProvidesAddressProof(docName),
          isFalse,
          reason: '$docName should not be identified as address proof',
        );
        expect(
          _mockDocumentProvidesDateOfBirthProof(docName),
          isFalse,
          reason: '$docName should not be identified as date of birth proof',
        );
      }
    });
  });

  group('Group Display Names', () {
    test('should return correct group display names', () {
      expect(_mockGetGroupDisplayName('group_1'), equals('Group 1:'));
      expect(_mockGetGroupDisplayName('group_2a'), equals('Group 2a:'));
      expect(_mockGetGroupDisplayName('group_2b'), equals('Group 2b:'));
      expect(_mockGetGroupDisplayName('group_3'), equals('Document Group 3:'));
    });

    test('should return correct group subtitles', () {
      expect(_mockGetGroupSubtitle('group_1'), equals('Primary Identity Document'));
      expect(_mockGetGroupSubtitle('group_2a'), equals('Trusted government documents'));
      expect(_mockGetGroupSubtitle('group_2b'), equals('Financial & social history documents'));
      expect(_mockGetGroupSubtitle('group_3'), equals('Documents'));
    });
  });
}

bool _mockDocumentProvidesAddressProof(String documentName) {
  final addressProofDocuments = [
    'current driving license, paper version',
    'mortgage statement',
    'bank/building society statement',
    'bank/building society statement opening letter',
    'credit card statement',
    'financial statement e.g. pension or endowment',
    'p45/p60 statement',
    'council tax statement',
    'utility bill',
    'benefits statement e.g child allowance, pension',
    'correspondence or personalized document',
    'cards with the pass accreditation logo',
    'letter from head teacher/principle',
  ];
  
  return addressProofDocuments.any((proof) => 
      documentName.toLowerCase().contains(proof.toLowerCase()));
}

bool _mockDocumentProvidesDateOfBirthProof(String documentName) {
  final dateOfBirthProofDocuments = [
    'birth certificate',
    'passport',
    'adoption certificate',
  ];
  
  return dateOfBirthProofDocuments.any((proof) => 
      documentName.toLowerCase().contains(proof.toLowerCase()));
}

String _mockGetGroupDisplayName(String groupName) {
  switch (groupName) {
    case 'group_1':
      return 'Group 1:';
    case 'group_2a':
      return 'Group 2a:';
    case 'group_2b':
      return 'Group 2b:';
    default:
      return 'Document ${groupName.replaceAll('group_', 'Group ')}:';
  }
}

String _mockGetGroupSubtitle(String groupName) {
  switch (groupName) {
    case 'group_1':
      return 'Primary Identity Document';
    case 'group_2a':
      return 'Trusted government documents';
    case 'group_2b':
      return 'Financial & social history documents';
    default:
      return 'Documents';
  }
}
