import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Document Upload Detection', () {
    test('should detect documents that require photos', () {
      final documentsRequiringPhotos = [
        DocumentType(
          key: 'passport',
          name: 'Passport',
          requiresPhoto: false, // API might not set this
          confirmsAddress: false,
          applicableCountries: ['UK'],
          dataFields: [],
        ),
        DocumentType(
          key: 'driving_licence',
          name: 'Current Photo Driving Licence (Full or Provisional)',
          requiresPhoto: false,
          confirmsAddress: false,
          applicableCountries: ['UK'],
          dataFields: [],
        ),
        DocumentType(
          key: 'birth_certificate',
          name: 'Birth Certificate (Issued within 12m of birth)',
          requiresPhoto: false,
          confirmsAddress: false,
          applicableCountries: ['UK'],
          dataFields: [],
        ),
        DocumentType(
          key: 'hm_forces_id',
          name: 'HM Forces ID card',
          requiresPhoto: false,
          confirmsAddress: false,
          applicableCountries: ['UK'],
          dataFields: [],
        ),
      ];

      for (final document in documentsRequiringPhotos) {
        expect(
          _mockDocumentRequiresPhoto(document),
          isTrue,
          reason: '${document.name} should require photo upload',
        );
      }
    });

    test('should not detect documents that do not require photos', () {
      final documentsNotRequiringPhotos = [
        DocumentType(
          key: 'marriage_certificate',
          name: 'Marriage/civil partnership certificate',
          requiresPhoto: false,
          confirmsAddress: false,
          applicableCountries: ['UK'],
          dataFields: [],
        ),
        DocumentType(
          key: 'utility_bill',
          name: 'Utility Bill',
          requiresPhoto: false,
          confirmsAddress: true,
          applicableCountries: ['UK'],
          dataFields: [],
        ),
        DocumentType(
          key: 'bank_statement',
          name: 'Bank/Building Society Statement',
          requiresPhoto: false,
          confirmsAddress: true,
          applicableCountries: ['UK'],
          dataFields: [],
        ),
      ];

      for (final document in documentsNotRequiringPhotos) {
        expect(
          _mockDocumentRequiresPhoto(document),
          isFalse,
          reason: '${document.name} should not require photo upload',
        );
      }
    });

    test('should handle documents with requiresPhoto flag set to true', () {
      final documentWithFlag = DocumentType(
        key: 'test_document',
        name: 'Test Document',
        requiresPhoto: true,
        confirmsAddress: false,
        applicableCountries: ['UK'],
        dataFields: [],
      );

      expect(documentWithFlag.requiresPhoto, isTrue);
    });

    test('should detect various forms of document names', () {
      final testCases = [
        {'name': 'Passport', 'shouldRequirePhoto': true},
        {'name': 'Current Driving License', 'shouldRequirePhoto': true},
        {'name': 'Current Driving Licence', 'shouldRequirePhoto': true},
        {'name': 'Photo Card', 'shouldRequirePhoto': true},
        {'name': 'Identity Card', 'shouldRequirePhoto': true},
        {'name': 'ID Card', 'shouldRequirePhoto': true},
        {'name': 'Adoption Certificate', 'shouldRequirePhoto': true},
        {'name': 'Birth Certificate', 'shouldRequirePhoto': true},
        {'name': 'HM Forces ID', 'shouldRequirePhoto': true},
        {'name': 'Fire Arms License', 'shouldRequirePhoto': true},
        {'name': 'Firearms License', 'shouldRequirePhoto': true},
        {'name': 'Utility Bill', 'shouldRequirePhoto': false},
        {'name': 'Bank Statement', 'shouldRequirePhoto': false},
        {'name': 'Council Tax Statement', 'shouldRequirePhoto': false},
      ];

      for (final testCase in testCases) {
        final document = DocumentType(
          key: 'test',
          name: testCase['name'] as String,
          requiresPhoto: false,
          confirmsAddress: false,
          applicableCountries: ['UK'],
          dataFields: [],
        );

        expect(
          _mockDocumentRequiresPhoto(document),
          equals(testCase['shouldRequirePhoto']),
          reason: '${testCase['name']} detection should be ${testCase['shouldRequirePhoto']}',
        );
      }
    });
  });
}

bool _mockDocumentRequiresPhoto(DocumentType document) {
  final photoRequiredDocuments = [
    'passport',
    'driving licence',
    'driving license',
    'photo card',
    'identity card',
    'id card',
    'adoption certificate',
    'birth certificate',
    'hm forces id',
    'fire arms license',
    'firearms license',
  ];
  
  return photoRequiredDocuments.any((photoDoc) => 
      document.name.toLowerCase().contains(photoDoc.toLowerCase()));
}
