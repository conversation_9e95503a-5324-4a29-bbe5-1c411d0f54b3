import 'package:SolidCheck/features/document_nomination/data/models/document_nomination.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:SolidCheck/features/document_nomination/presentation/providers/document_nomination_provider.dart';
import 'package:SolidCheck/features/document_nomination/presentation/screens/document_detail_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'document_detail_screen_test.mocks.dart';

@GenerateMocks([DocumentNominationNotifier])
void main() {
  group('DocumentDetailScreen', () {
    late MockDocumentNominationNotifier mockNotifier;
    late DocumentType testDocumentType;
    late DocumentNomination testNomination;

    setUp(() {
      mockNotifier = MockDocumentNominationNotifier();
      
      testDocumentType = DocumentType(
        key: 'passport_any',
        name: 'Passport (Any Country)',
        requiresPhoto: true,
        confirmsAddress: false,
        applicableCountries: ['ANY'],
        dataFields: [
          DocumentDataField(
            name: 'passport_number',
            type: 'string',
            required: true,
            label: 'Passport Number',
          ),
          DocumentDataField(
            name: 'issue_date',
            type: 'date',
            required: true,
            label: 'Issue Date',
          ),
        ],
      );

      testNomination = DocumentNomination(
        documentTypeId: testDocumentType.id,
        documentData: {
          'passport_number': '*********',
          'issue_date': '01/01/2020',
        },
        confirmsAddress: false,
      );
    });

    Widget createTestWidget({DocumentNomination? existingNomination}) {
      return ProviderScope(
        overrides: [
          documentNominationProvider.overrideWith((ref) => mockNotifier),
        ],
        child: MaterialApp(
          home: DocumentDetailScreen(
            applicationId: 'test-app-id',
            applicantName: 'Test Applicant',
            documentType: testDocumentType,
            existingNomination: existingNomination,
          ),
        ),
      );
    }

    testWidgets('should display document information correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Document Details'), findsOneWidget);
      expect(find.text('Passport (Any Country)'), findsOneWidget);
      expect(find.text('Document Information'), findsOneWidget);
      expect(find.text('Passport Number'), findsOneWidget);
      expect(find.text('Issue Date'), findsOneWidget);
    });

    testWidgets('should show upload section when document requires photo', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Document Upload'), findsOneWidget);
      expect(find.byType(DocumentUploadWidget), findsOneWidget);
    });

    testWidgets('should not show upload section when document does not require photo', (WidgetTester tester) async {
      final nonPhotoDocument = DocumentType(
        key: 'bank_statement',
        name: 'Bank Statement',
        requiresPhoto: false,
        confirmsAddress: true,
        applicableCountries: ['UK'],
        dataFields: [
          DocumentDataField(
            name: 'account_number',
            type: 'string',
            required: true,
            label: 'Account Number',
          ),
        ],
      );

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            documentNominationProvider.overrideWith((ref) => mockNotifier),
          ],
          child: MaterialApp(
            home: DocumentDetailScreen(
              applicationId: 'test-app-id',
              applicantName: 'Test Applicant',
              documentType: nonPhotoDocument,
            ),
          ),
        ),
      );

      expect(find.text('Document Upload'), findsNothing);
      expect(find.byType(DocumentUploadWidget), findsNothing);
    });

    testWidgets('should show address confirmation section when document confirms address', (WidgetTester tester) async {
      final addressDocument = DocumentType(
        key: 'utility_bill',
        name: 'Utility Bill',
        requiresPhoto: false,
        confirmsAddress: true,
        applicableCountries: ['UK'],
        dataFields: [
          DocumentDataField(
            name: 'account_number',
            type: 'string',
            required: true,
            label: 'Account Number',
          ),
        ],
      );

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            documentNominationProvider.overrideWith((ref) => mockNotifier),
          ],
          child: MaterialApp(
            home: DocumentDetailScreen(
              applicationId: 'test-app-id',
              applicantName: 'Test Applicant',
              documentType: addressDocument,
            ),
          ),
        ),
      );

      expect(find.text('Address Confirmation'), findsOneWidget);
      expect(find.text('This document confirms my current address'), findsOneWidget);
      expect(find.byType(Checkbox), findsOneWidget);
    });

    testWidgets('should populate fields with existing nomination data', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(existingNomination: testNomination));

      final passportNumberField = find.widgetWithText(TextFormField, '*********');
      final issueDateField = find.widgetWithText(TextFormField, '01/01/2020');

      expect(passportNumberField, findsOneWidget);
      expect(issueDateField, findsOneWidget);
    });

    testWidgets('should show nominate button for new document', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Nominate Document'), findsOneWidget);
      expect(find.text('Update Document'), findsNothing);
    });

    testWidgets('should show update button for existing document', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(existingNomination: testNomination));

      expect(find.text('Update Document'), findsOneWidget);
      expect(find.text('Nominate Document'), findsNothing);
    });

    testWidgets('should validate required fields', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      final saveButton = find.text('Save Document');
      await tester.tap(saveButton);
      await tester.pump();

      expect(find.text('Passport Number is required'), findsOneWidget);
      expect(find.text('Issue Date is required'), findsOneWidget);
    });

    testWidgets('should call addDocumentNomination when saving valid data', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Fill in the form
      await tester.enterText(find.byKey(const Key('passport_number')), '*********');
      await tester.enterText(find.byKey(const Key('issue_date')), '01/01/2020');

      final saveButton = find.text('Save Document');
      await tester.tap(saveButton);
      await tester.pump();

      verify(mockNotifier.addDocumentNomination(any)).called(1);
    });

    testWidgets('should navigate back when cancel button is pressed', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      final cancelButton = find.text('Cancel');
      await tester.tap(cancelButton);
      await tester.pumpAndSettle();

      // Verify navigation occurred (screen should be popped)
      expect(find.byType(DocumentDetailScreen), findsNothing);
    });

    testWidgets('should navigate back when back button is pressed', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      final backButton = find.byIcon(Icons.arrow_back);
      await tester.tap(backButton);
      await tester.pumpAndSettle();

      // Verify navigation occurred (screen should be popped)
      expect(find.byType(DocumentDetailScreen), findsNothing);
    });

    testWidgets('should show date picker when date field is tapped', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      final dateField = find.byKey(const Key('issue_date'));
      await tester.tap(dateField);
      await tester.pumpAndSettle();

      expect(find.byType(DatePickerDialog), findsOneWidget);
    });

    testWidgets('should display document badges correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Photo required'), findsOneWidget);
      expect(find.byIcon(Icons.book), findsOneWidget); // Passport icon
    });

    testWidgets('should handle file selection for upload', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      final uploadWidget = find.byType(DocumentUploadWidget);
      expect(uploadWidget, findsOneWidget);

      // Verify upload widget is configured correctly
      final widget = tester.widget<DocumentUploadWidget>(uploadWidget);
      expect(widget.documentName, equals('Passport (Any Country)'));
      expect(widget.allowedExtensions, equals(['pdf', 'png', 'jpg', 'jpeg']));
      expect(widget.maxFileSizeInMB, equals(10));
    });
  });
}
