import 'package:SolidCheck/features/document_nomination/presentation/screens/document_nomination_demo_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DocumentNominationDemoScreen', () {
    Widget createTestWidget() {
      return MaterialApp(
        home: DocumentNominationDemoScreen(
          applicationId: 'test-app-id',
          applicantName: '<PERSON>',
        ),
      );
    }

    testWidgets('should display header with correct title', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Upload Documents - UK Residents'), findsOneWidget);
      expect(find.byIcon(Icons.arrow_back), findsOneWidget);
    });

    testWidgets('should display tasks section', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Tasks'), findsOneWidget);
      expect(find.text('Upload at least 1 document from group1'), findsOneWidget);
      expect(find.text('Upload 2 additional documents from any group'), findsOneWidget);
      expect(find.text('1 of the uploaded documents must show your current address'), findsOneWidget);
    });

    testWidgets('should display key section', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Key:'), findsOneWidget);
      expect(find.text('Proof of Address'), findsOneWidget);
      expect(find.text('Proof of Date of Birth'), findsOneWidget);
    });

    testWidgets('should display document groups', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Group 1:'), findsOneWidget);
      expect(find.text('Group 2a:'), findsOneWidget);
      expect(find.text('Group 2b:'), findsOneWidget);
      expect(find.text('Primary Identity Document'), findsOneWidget);
      expect(find.text('Trusted government documents'), findsOneWidget);
      expect(find.text('Financial & social history documents'), findsOneWidget);
    });

    testWidgets('should display document items in groups', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Passport'), findsOneWidget);
      expect(find.text('Current Photo Driving Licence (Full or Provisional)'), findsOneWidget);
      expect(find.text('Mortgage Statement'), findsOneWidget);
      expect(find.text('Bank/Building Society Statement'), findsOneWidget);
    });

    testWidgets('should display submit button', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Submit'), findsOneWidget);
      expect(find.byType(ElevatedButton), findsAtLeastNWidgets(1));
    });

    testWidgets('should show snackbar when submit is pressed', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      await tester.tap(find.text('Submit'));
      await tester.pump();

      expect(find.text('Submit functionality would be implemented here'), findsOneWidget);
    });

    testWidgets('should display task items with correct styling', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('0/1'), findsOneWidget);
      expect(find.text('0/2'), findsOneWidget);
    });

    testWidgets('should display key items with colored indicators', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Documents marked with a purple line provide proof of address'), findsOneWidget);
      expect(find.text('Documents marked with a pink line provide proof of date of birth'), findsOneWidget);
    });

    testWidgets('should display info icons for document items', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.byIcon(Icons.info_outline), findsAtLeastNWidgets(1));
    });

    testWidgets('should display dropdown arrow for Group 1', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.byIcon(Icons.keyboard_arrow_down), findsOneWidget);
    });
  });
}
