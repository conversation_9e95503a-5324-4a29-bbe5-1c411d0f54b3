import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Tasks and Key Layout', () {
    testWidgets('should display side by side on desktop', (WidgetTester tester) async {
      await tester.binding.setSurfaceSize(const Size(1200, 800)); // Desktop size
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: _buildTestTasksAndKeySection(false), // Desktop layout
          ),
        ),
      );

      expect(find.byType(Row), findsAtLeastNWidgets(1));
      expect(find.byType(Expanded), findsAtLeastNWidgets(2));
      expect(find.text('Tasks'), findsOneWidget);
      expect(find.text('Key:'), findsOneWidget);
    });

    testWidgets('should display stacked on mobile', (WidgetTester tester) async {
      await tester.binding.setSurfaceSize(const Size(400, 800)); // Mobile size
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: _buildTestTasksAndKeySection(true), // Mobile layout
          ),
        ),
      );

      expect(find.byType(Column), findsAtLeastNWidgets(1));
      expect(find.text('Tasks'), findsOneWidget);
      expect(find.text('Key:'), findsOneWidget);
    });

    testWidgets('should have compact styling', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: _buildTestTasksWidget(),
          ),
        ),
      );

      final containers = tester.widgetList<Container>(find.byType(Container));
      expect(containers.isNotEmpty, isTrue);
      
      expect(find.text('Tasks'), findsOneWidget);
      expect(find.textContaining('Upload at least 1 document'), findsOneWidget);
    });

    testWidgets('should have proper flex ratios', (WidgetTester tester) async {
      await tester.binding.setSurfaceSize(const Size(1200, 800));
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: _buildTestTasksAndKeySection(false),
          ),
        ),
      );

      final expandedWidgets = tester.widgetList<Expanded>(find.byType(Expanded)).toList();
      expect(expandedWidgets.length, greaterThanOrEqualTo(2));

      // Find the main layout expanded widgets (should have flex 3 and 2)
      final mainExpandedWidgets = expandedWidgets.where((w) => w.flex == 3 || w.flex == 2).toList();
      expect(mainExpandedWidgets.length, equals(2));
      expect(mainExpandedWidgets.any((w) => w.flex == 3), isTrue); // Tasks widget
      expect(mainExpandedWidgets.any((w) => w.flex == 2), isTrue); // Key widget
    });
  });
}

Widget _buildTestTasksAndKeySection(bool isMobile) {
  if (isMobile) {
    return Column(
      children: [
        _buildTestTasksWidget(),
        const SizedBox(height: 12),
        _buildTestKeyWidget(),
      ],
    );
  } else {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 3,
          child: _buildTestTasksWidget(),
        ),
        const SizedBox(width: 16),
        Expanded(
          flex: 2,
          child: _buildTestKeyWidget(),
        ),
      ],
    );
  }
}

Widget _buildTestTasksWidget() {
  return Container(
    padding: const EdgeInsets.all(12),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(8),
      border: Border.all(color: Colors.grey),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Tasks',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        _buildTestTaskItem('Upload at least 1 document from group1', '0/1', false),
        const SizedBox(height: 6),
        _buildTestTaskItem('Upload 2 additional documents from any group', '0/2', false),
        const SizedBox(height: 6),
        _buildTestTaskItem('1 of the uploaded documents must show your current address', '0/1', false),
      ],
    ),
  );
}

Widget _buildTestKeyWidget() {
  return Container(
    padding: const EdgeInsets.all(12),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(8),
      border: Border.all(color: Colors.grey),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Key:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        _buildTestKeyItem('Proof of Address', 'Documents marked with a purple line', Colors.purple),
        const SizedBox(height: 6),
        _buildTestKeyItem('Proof of Date of Birth', 'Documents marked with a pink line', Colors.pink),
      ],
    ),
  );
}

Widget _buildTestTaskItem(String text, String count, bool isCompleted) {
  final color = isCompleted ? Colors.green : Colors.orange;
  
  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
    decoration: BoxDecoration(
      color: color.withValues(alpha: 0.1),
      borderRadius: BorderRadius.circular(6),
      border: Border.all(color: color.withValues(alpha: 0.3)),
    ),
    child: Row(
      children: [
        Expanded(
          child: Text(
            text,
            style: const TextStyle(fontSize: 12),
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            count,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    ),
  );
}

Widget _buildTestKeyItem(String title, String description, Color color) {
  return Row(
    children: [
      Container(
        width: 3,
        height: 30,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(2),
        ),
      ),
      const SizedBox(width: 10),
      Expanded(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 2),
            Text(
              description,
              style: const TextStyle(fontSize: 10),
            ),
          ],
        ),
      ),
    ],
  );
}
