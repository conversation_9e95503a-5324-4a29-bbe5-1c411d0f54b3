import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Document Nomination UI Improvements', () {
    testWidgets('should have submit button positioned correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: _buildTestLayoutWithSubmitButton(),
          ),
        ),
      );

      expect(find.byType(Row), findsAtLeastNWidgets(1));
      expect(find.byType(Expanded), findsAtLeastNWidgets(1));
      expect(find.text('Submit Documents'), findsOneWidget);
      
      final submitButton = find.byType(ElevatedButton);
      expect(submitButton, findsOneWidget);
      
      final submitButtonWidget = tester.widget<ElevatedButton>(submitButton);
      expect(submitButtonWidget.child, isA<Text>());
    });

    testWidgets('should not have back to dashboard button', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: _buildTestLayoutWithSubmitButton(),
          ),
        ),
      );

      expect(find.text('Back to Dashboard'), findsNothing);
      expect(find.byType(OutlinedButton), findsNothing);
    });

    testWidgets('should have tasks and key in side-by-side layout', (WidgetTester tester) async {
      await tester.binding.setSurfaceSize(const Size(1200, 800)); // Desktop size
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: _buildTestTasksAndKeyLayout(),
          ),
        ),
      );

      expect(find.byType(Row), findsAtLeastNWidgets(1));
      expect(find.text('Tasks for Route 1'), findsOneWidget);
      expect(find.text('Key:'), findsOneWidget);
    });

    testWidgets('should not have dropdown design in document groups', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: _buildTestDocumentGroup(),
          ),
        ),
      );

      expect(find.byIcon(Icons.keyboard_arrow_down), findsNothing);
      expect(find.text('Group 1:'), findsOneWidget);
      expect(find.text('Primary Identity Document'), findsOneWidget);
    });

    testWidgets('should have dynamic tasks based on route', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: _buildTestDynamicTasks(),
          ),
        ),
      );

      expect(find.textContaining('Tasks for Route'), findsOneWidget);
      expect(find.textContaining('Upload'), findsAtLeastNWidgets(1));
      expect(find.textContaining('documents'), findsAtLeastNWidgets(1));
    });

    testWidgets('should have upload widget for documents requiring photos', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: _buildTestDocumentWithUpload(),
          ),
        ),
      );

      expect(find.text('Passport'), findsOneWidget);
      expect(find.byIcon(Icons.cloud_upload_outlined), findsOneWidget);
      expect(find.textContaining('Select your file'), findsOneWidget);
    });
  });
}

Widget _buildTestLayoutWithSubmitButton() {
  return Padding(
    padding: const EdgeInsets.all(16),
    child: Row(
      children: [
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey),
            ),
            child: const Text('Tasks and Key Section'),
          ),
        ),
        const SizedBox(width: 16),
        SizedBox(
          width: 200,
          child: ElevatedButton(
            onPressed: () {},
            child: const Text('Submit Documents'),
          ),
        ),
      ],
    ),
  );
}

Widget _buildTestTasksAndKeyLayout() {
  return Row(
    children: [
      Expanded(
        flex: 3,
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Tasks for Route 1',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 8),
              Text('Upload 1 document from Group 1'),
              Text('Upload 2 total documents'),
            ],
          ),
        ),
      ),
      const SizedBox(width: 16),
      Expanded(
        flex: 2,
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.green[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.green),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Key:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 8),
              Text('Proof of Address'),
              Text('Proof of Date of Birth'),
            ],
          ),
        ),
      ),
    ],
  );
}

Widget _buildTestDocumentGroup() {
  return Container(
    decoration: BoxDecoration(
      border: Border.all(color: Colors.grey),
      borderRadius: BorderRadius.circular(8),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
          ),
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Group 1:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 4),
              Text(
                'Primary Identity Document',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
        const Padding(
          padding: EdgeInsets.all(16),
          child: Text('Passport'),
        ),
      ],
    ),
  );
}

Widget _buildTestDynamicTasks() {
  return Container(
    padding: const EdgeInsets.all(12),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(8),
      border: Border.all(color: Colors.grey),
    ),
    child: const Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tasks for Route 2',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 8),
        Text('Upload 1 document from Group 1'),
        Text('Upload 3 total documents'),
        Text('1 document must show your current address'),
      ],
    ),
  );
}

Widget _buildTestDocumentWithUpload() {
  return Column(
    children: [
      Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.orange.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
        ),
        child: const Row(
          children: [
            Expanded(
              child: Text(
                'Passport',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            Icon(
              Icons.info_outline,
              size: 16,
              color: Colors.grey,
            ),
          ],
        ),
      ),
      Container(
        margin: const EdgeInsets.only(top: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey),
        ),
        child: Column(
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 32),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey),
              ),
              child: const Column(
                children: [
                  Icon(
                    Icons.cloud_upload_outlined,
                    size: 48,
                    color: Colors.blue,
                  ),
                  SizedBox(height: 12),
                  Text(
                    'Select your file or drag and drop\nonly PDF, PNG, JPG, JPEG file accepted',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {},
              child: const Text('Browse'),
            ),
          ],
        ),
      ),
    ],
  );
}
