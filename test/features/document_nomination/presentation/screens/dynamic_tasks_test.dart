import 'package:SolidCheck/features/document_nomination/data/models/route_requirement.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Dynamic Tasks Generation', () {
    test('should create RouteRequirement with correct properties', () {
      final routeRequirement = RouteRequirement(
        description: 'Test route',
        totalDocuments: 3,
        addressConfirmationRequired: true,
        requiredGroups: {
          'group_1': GroupRequirement(min: 1, max: 1),
          'group_2a': GroupRequirement(min: 1, max: 2),
        },
      );

      expect(routeRequirement.totalDocuments, equals(3));
      expect(routeRequirement.addressConfirmationRequired, isTrue);
      expect(routeRequirement.requiredGroups.length, equals(2));
      expect(routeRequirement.requiredGroups['group_1']?.min, equals(1));
      expect(routeRequirement.requiredGroups['group_2a']?.min, equals(1));
    });

    test('should handle RouteRequirement from JSON', () {
      final json = {
        'description': 'Route 1 requirements',
        'total_documents': 2,
        'address_confirmation_required': true,
        'required_groups': {
          'group_1': {'min': 1, 'max': 1},
          'group_2a': {'min': 1, 'max': 3},
        },
      };

      final routeRequirement = RouteRequirement.fromJson(json);

      expect(routeRequirement.description, equals('Route 1 requirements'));
      expect(routeRequirement.totalDocuments, equals(2));
      expect(routeRequirement.addressConfirmationRequired, isTrue);
      expect(routeRequirement.requiredGroups.length, equals(2));
      expect(routeRequirement.requiredGroups['group_1']?.min, equals(1));
      expect(routeRequirement.requiredGroups['group_2a']?.min, equals(1));
      expect(routeRequirement.requiredGroups['group_2a']?.max, equals(3));
    });

    test('should handle empty required groups', () {
      final routeRequirement = RouteRequirement(
        description: 'Simple route',
        totalDocuments: 1,
        addressConfirmationRequired: false,
        requiredGroups: {},
      );

      expect(routeRequirement.requiredGroups.isEmpty, isTrue);
      expect(routeRequirement.addressConfirmationRequired, isFalse);
    });

    test('should handle GroupRequirement properties', () {
      final groupRequirement = GroupRequirement(min: 2, max: 5);

      expect(groupRequirement.min, equals(2));
      expect(groupRequirement.max, equals(5));
    });

    test('should create GroupRequirement from JSON', () {
      final json = {'min': 1, 'max': 3};
      final groupRequirement = GroupRequirement.fromJson(json);

      expect(groupRequirement.min, equals(1));
      expect(groupRequirement.max, equals(3));
    });

    test('should handle missing JSON fields with defaults', () {
      final routeJson = <String, dynamic>{};
      final routeRequirement = RouteRequirement.fromJson(routeJson);

      expect(routeRequirement.description, equals(''));
      expect(routeRequirement.totalDocuments, equals(0));
      expect(routeRequirement.addressConfirmationRequired, isFalse);
      expect(routeRequirement.requiredGroups.isEmpty, isTrue);

      final groupJson = <String, dynamic>{};
      final groupRequirement = GroupRequirement.fromJson(groupJson);

      expect(groupRequirement.min, equals(0));
      expect(groupRequirement.max, equals(0));
    });

    test('should convert RouteRequirement to JSON correctly', () {
      final routeRequirement = RouteRequirement(
        description: 'Test route',
        totalDocuments: 3,
        addressConfirmationRequired: true,
        requiredGroups: {
          'group_1': GroupRequirement(min: 1, max: 1),
        },
      );

      final json = routeRequirement.toJson();

      expect(json['description'], equals('Test route'));
      expect(json['total_documents'], equals(3));
      expect(json['address_confirmation_required'], isTrue);
      expect(json['required_groups'], isA<Map<String, dynamic>>());
      expect(json['required_groups']['group_1'], isA<Map<String, dynamic>>());
      expect(json['required_groups']['group_1']['min'], equals(1));
      expect(json['required_groups']['group_1']['max'], equals(1));
    });

    test('should convert GroupRequirement to JSON correctly', () {
      final groupRequirement = GroupRequirement(min: 2, max: 4);
      final json = groupRequirement.toJson();

      expect(json['min'], equals(2));
      expect(json['max'], equals(4));
    });

    test('should have proper toString methods', () {
      final routeRequirement = RouteRequirement(
        description: 'Test route',
        totalDocuments: 2,
        addressConfirmationRequired: true,
        requiredGroups: {},
      );

      final groupRequirement = GroupRequirement(min: 1, max: 3);

      expect(routeRequirement.toString(), contains('RouteRequirement'));
      expect(routeRequirement.toString(), contains('Test route'));
      expect(routeRequirement.toString(), contains('2'));
      expect(routeRequirement.toString(), contains('true'));

      expect(groupRequirement.toString(), contains('GroupRequirement'));
      expect(groupRequirement.toString(), contains('1'));
      expect(groupRequirement.toString(), contains('3'));
    });
  });

  group('Dynamic Tasks Logic', () {
    test('should generate correct task count for group requirements', () {
      final routeRequirement = RouteRequirement(
        description: 'Route with groups',
        totalDocuments: 3,
        addressConfirmationRequired: true,
        requiredGroups: {
          'group_1': GroupRequirement(min: 1, max: 1),
          'group_2a': GroupRequirement(min: 2, max: 3),
        },
      );

      // Test group 1 requirement
      final group1 = routeRequirement.requiredGroups['group_1'];
      expect(group1?.min, equals(1));
      expect(group1?.max, equals(1));

      // Test group 2a requirement
      final group2a = routeRequirement.requiredGroups['group_2a'];
      expect(group2a?.min, equals(2));
      expect(group2a?.max, equals(3));

      // Test total documents
      expect(routeRequirement.totalDocuments, equals(3));

      // Test address confirmation
      expect(routeRequirement.addressConfirmationRequired, isTrue);
    });

    test('should handle route without group requirements', () {
      final routeRequirement = RouteRequirement(
        description: 'Simple route',
        totalDocuments: 2,
        addressConfirmationRequired: false,
        requiredGroups: {},
      );

      expect(routeRequirement.requiredGroups.isEmpty, isTrue);
      expect(routeRequirement.totalDocuments, equals(2));
      expect(routeRequirement.addressConfirmationRequired, isFalse);
    });

    test('should handle multiple group requirements', () {
      final routeRequirement = RouteRequirement(
        description: 'Complex route',
        totalDocuments: 5,
        addressConfirmationRequired: true,
        requiredGroups: {
          'group_1': GroupRequirement(min: 1, max: 1),
          'group_2a': GroupRequirement(min: 1, max: 2),
          'group_2b': GroupRequirement(min: 1, max: 2),
          'group_3': GroupRequirement(min: 0, max: 1),
        },
      );

      expect(routeRequirement.requiredGroups.length, equals(4));
      
      // Check each group
      expect(routeRequirement.requiredGroups['group_1']?.min, equals(1));
      expect(routeRequirement.requiredGroups['group_2a']?.min, equals(1));
      expect(routeRequirement.requiredGroups['group_2b']?.min, equals(1));
      expect(routeRequirement.requiredGroups['group_3']?.min, equals(0));
    });
  });
}
