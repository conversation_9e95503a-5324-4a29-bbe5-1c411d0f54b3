import 'package:SolidCheck/features/document_nomination/data/models/available_documents_response.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:SolidCheck/features/document_nomination/data/models/route_requirement.dart';
import 'package:SolidCheck/features/document_nomination/presentation/providers/document_nomination_provider.dart';
import 'package:SolidCheck/features/document_nomination/presentation/screens/document_nomination_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

class MockDocumentNominationNotifier extends Mock implements DocumentNominationNotifier {}

void main() {
  group('DocumentNominationScreen Enhanced', () {
    late MockDocumentNominationNotifier mockNotifier;

    setUp(() {
      mockNotifier = MockDocumentNominationNotifier();
    });

    AvailableDocumentsData createMockData() {
      return AvailableDocumentsData(
        application: ApplicationInfo(
          id: 1,
          productCode: 'test',
          productName: 'Test Product',
          applicantName: '<PERSON>',
          status: 'active',
        ),
        applicantContext: ApplicantContext(
          nationality: 'UK',
          currentAddressCountry: 'UK',
          isUkNational: true,
          isUkResident: true,
          workType: 'test',
          productCode: 'test',
        ),
        routing: RoutingInfo(
          recommendedRoute: 1,
          availableRoutes: [1],
          routeRequirements: {
            'route_1': RouteRequirement(
              description: 'Test route',
              requiredGroups: {},
              totalDocuments: 3,
              addressConfirmationRequired: true,
            ),
          },
        ),
        documents: DocumentsInfo(
          availableByGroup: {
            'group_1': DocumentGroup(
              groupName: 'Group 1',
              documentCount: 1,
              documents: [
                DocumentType(
                  key: 'passport',
                  name: 'Passport',
                  requiresPhoto: true,
                  confirmsAddress: false,
                  dataFields: [],
                  applicableCountries: ['UK'],
                ),
              ],
            ),
          },
          totalAvailable: 1,
        ),
        currentNominations: [],
      );
    }

    Widget createTestWidget() {
      return ProviderScope(
        overrides: [
          documentNominationProvider.overrideWith((ref) => mockNotifier),
        ],
        child: const MaterialApp(
          home: DocumentNominationScreen(
            applicationId: 'test-app-id',
            applicantName: 'John Doe',
          ),
        ),
      );
    }

    testWidgets('should display enhanced tasks section', (WidgetTester tester) async {
      when(mockNotifier.state).thenReturn(
        DocumentNominationState(availableDocuments: createMockData()),
      );

      await tester.pumpWidget(createTestWidget());

      expect(find.text('Tasks'), findsOneWidget);
    });

    testWidgets('should display enhanced key section', (WidgetTester tester) async {
      when(mockNotifier.state).thenReturn(
        DocumentNominationState(availableDocuments: createMockData()),
      );

      await tester.pumpWidget(createTestWidget());

      expect(find.text('Key:'), findsOneWidget);
      expect(find.text('Proof of Address'), findsOneWidget);
      expect(find.text('Proof of Date of Birth'), findsOneWidget);
    });

    testWidgets('should display enhanced document groups', (WidgetTester tester) async {
      when(mockNotifier.state).thenReturn(
        DocumentNominationState(availableDocuments: createMockData()),
      );

      await tester.pumpWidget(createTestWidget());

      expect(find.text('Group 1:'), findsOneWidget);
      expect(find.text('Primary Identity Document'), findsOneWidget);
    });
  });
}
