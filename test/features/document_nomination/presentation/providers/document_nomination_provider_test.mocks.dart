// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in SolidCheck/test/features/document_nomination/presentation/providers/document_nomination_provider_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:mockito/mockito.dart' as _i1;
import 'package:SolidCheck/features/document_nomination/data/models/available_documents_response.dart'
    as _i2;
import 'package:SolidCheck/features/document_nomination/data/models/document_nomination.dart'
    as _i6;
import 'package:SolidCheck/features/document_nomination/data/models/validation_response.dart'
    as _i3;
import 'package:SolidCheck/features/document_nomination/data/repositories/document_nomination_repository.dart'
    as _i4;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAvailableDocumentsResponse_0 extends _i1.SmartFake
    implements _i2.AvailableDocumentsResponse {
  _FakeAvailableDocumentsResponse_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeValidationResponse_1 extends _i1.SmartFake
    implements _i3.ValidationResponse {
  _FakeValidationResponse_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [DocumentNominationRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockDocumentNominationRepository extends _i1.Mock
    implements _i4.DocumentNominationRepository {
  MockDocumentNominationRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<_i2.AvailableDocumentsResponse> getAvailableDocuments(
    String? applicationId,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getAvailableDocuments, [applicationId]),
            returnValue: _i5.Future<_i2.AvailableDocumentsResponse>.value(
              _FakeAvailableDocumentsResponse_0(
                this,
                Invocation.method(#getAvailableDocuments, [applicationId]),
              ),
            ),
          )
          as _i5.Future<_i2.AvailableDocumentsResponse>);

  @override
  _i5.Future<_i3.ValidationResponse> validateDocumentNominations(
    String? applicationId,
    _i6.DocumentNominationRequest? request,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#validateDocumentNominations, [
              applicationId,
              request,
            ]),
            returnValue: _i5.Future<_i3.ValidationResponse>.value(
              _FakeValidationResponse_1(
                this,
                Invocation.method(#validateDocumentNominations, [
                  applicationId,
                  request,
                ]),
              ),
            ),
          )
          as _i5.Future<_i3.ValidationResponse>);

  @override
  _i5.Future<_i3.ValidationResponse> submitDocumentNominations(
    String? applicationId,
    _i6.DocumentNominationRequest? request,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#submitDocumentNominations, [
              applicationId,
              request,
            ]),
            returnValue: _i5.Future<_i3.ValidationResponse>.value(
              _FakeValidationResponse_1(
                this,
                Invocation.method(#submitDocumentNominations, [
                  applicationId,
                  request,
                ]),
              ),
            ),
          )
          as _i5.Future<_i3.ValidationResponse>);

  @override
  _i5.Future<bool> hasValidToken() =>
      (super.noSuchMethod(
            Invocation.method(#hasValidToken, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<void> ensureAuthenticated() =>
      (super.noSuchMethod(
            Invocation.method(#ensureAuthenticated, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);
}
