import 'package:SolidCheck/features/document_nomination/data/models/available_documents_response.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_nomination.dart';
import 'package:SolidCheck/features/document_nomination/data/models/validation_response.dart';
import 'package:SolidCheck/features/document_nomination/data/repositories/document_nomination_repository.dart';
import 'package:SolidCheck/features/document_nomination/presentation/providers/document_nomination_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'document_nomination_provider_test.mocks.dart';

@GenerateMocks([DocumentNominationRepository])
void main() {
  late MockDocumentNominationRepository mockRepository;

  setUp(() {
    mockRepository = MockDocumentNominationRepository();
  });

  group('DocumentNominationNotifier - New Methods', () {
    test('should add document nomination correctly', () {
      final notifier = DocumentNominationNotifier(mockRepository);
      final nomination = DocumentNomination(
        documentTypeId: 1,
        documentData: {'passport_number': '*********'},
        confirmsAddress: false,
      );

      notifier.addDocumentNomination(nomination);

      expect(notifier.state.nominations, contains(nomination));
      expect(notifier.state.nominations.length, equals(1));
    });

    test('should replace existing nomination for same document type', () {
      final notifier = DocumentNominationNotifier(mockRepository);
      final nomination1 = DocumentNomination(
        documentTypeId: 1,
        documentData: {'passport_number': '*********'},
        confirmsAddress: false,
      );
      final nomination2 = DocumentNomination(
        documentTypeId: 1,
        documentData: {'passport_number': '*********'},
        confirmsAddress: true,
      );

      notifier.addDocumentNomination(nomination1);
      notifier.addDocumentNomination(nomination2);

      expect(notifier.state.nominations.length, equals(1));
      expect(notifier.state.nominations.first.documentData['passport_number'], equals('*********'));
      expect(notifier.state.nominations.first.confirmsAddress, isTrue);
    });

    test('should remove document nomination correctly', () {
      final notifier = DocumentNominationNotifier(mockRepository);
      final nomination = DocumentNomination(
        documentTypeId: 1,
        documentData: {'passport_number': '*********'},
        confirmsAddress: false,
      );

      notifier.addDocumentNomination(nomination);
      expect(notifier.state.nominations.length, equals(1));

      notifier.removeDocumentNomination(1);
      expect(notifier.state.nominations.length, equals(0));
    });

    test('should handle removing non-existent nomination', () {
      final notifier = DocumentNominationNotifier(mockRepository);

      // Should not throw error when removing non-existent nomination
      expect(() => notifier.removeDocumentNomination(999), returnsNormally);
      expect(notifier.state.nominations.length, equals(0));
    });
  });

  group('DocumentNominationNotifier - Existing Tests', () {
    late MockDocumentNominationRepository mockRepository;
    late DocumentNominationNotifier notifier;

    setUp(() {
      mockRepository = MockDocumentNominationRepository();
      notifier = DocumentNominationNotifier(mockRepository);
    });

    test('initial state should be correct', () {
      expect(notifier.state.isLoading, isFalse);
      expect(notifier.state.error, isNull);
      expect(notifier.state.availableDocuments, isNull);
      expect(notifier.state.selectedRoute, equals(1));
      expect(notifier.state.nominations.isEmpty, isTrue);
      expect(notifier.state.lastValidation, isNull);
      expect(notifier.state.isValidating, isFalse);
      expect(notifier.state.isSubmitting, isFalse);
      expect(notifier.state.successMessage, isNull);
    });

    test('loadAvailableDocuments should update state correctly on success', () async {
      final mockResponse = AvailableDocumentsResponse(
        success: true,
        data: AvailableDocumentsData(
          application: ApplicationInfo(
            id: 1,
            productCode: 'DBSEC',
            productName: 'DBS Enhanced Check',
            applicantId: 123,
            applicantName: 'Test User',
            status: 'draft',
          ),
          applicantContext: ApplicantContext(
            nationality: 'British',
            currentAddressCountry: 'United Kingdom',
            isUkNational: true,
            isUkResident: true,
            workType: 'paid',
            productCode: 'DBSEC',
          ),
          routing: RoutingInfo(
            recommendedRoute: 2,
            availableRoutes: [1, 2, 3],
            routeRequirements: {},
          ),
          documents: DocumentsInfo(
            availableByGroup: {},
            totalAvailable: 0,
          ),
          currentNominations: [],
        ),
      );

      when(mockRepository.getAvailableDocuments('123'))
          .thenAnswer((_) async => mockResponse);

      await notifier.loadAvailableDocuments('123');

      expect(notifier.state.isLoading, isFalse);
      expect(notifier.state.error, isNull);
      expect(notifier.state.availableDocuments, isNotNull);
      expect(notifier.state.selectedRoute, equals(2));
    });

    test('loadAvailableDocuments should handle errors correctly', () async {
      when(mockRepository.getAvailableDocuments('123'))
          .thenThrow(Exception('Network error'));

      await notifier.loadAvailableDocuments('123');

      expect(notifier.state.isLoading, isFalse);
      expect(notifier.state.error, contains('Network error'));
      expect(notifier.state.availableDocuments, isNull);
    });

    test('selectRoute should update selected route', () {
      notifier.state = notifier.state.copyWith(
        availableDocuments: AvailableDocumentsData(
          application: ApplicationInfo(
            id: 1,
            productCode: 'DBSEC',
            productName: 'DBS Enhanced Check',
            applicantName: 'Test User',
            status: 'draft',
          ),
          applicantContext: ApplicantContext(
            nationality: 'British',
            currentAddressCountry: 'United Kingdom',
            isUkNational: true,
            isUkResident: true,
            workType: 'paid',
            productCode: 'DBSEC',
          ),
          routing: RoutingInfo(
            recommendedRoute: 1,
            availableRoutes: [1, 2, 3],
            routeRequirements: {},
          ),
          documents: DocumentsInfo(
            availableByGroup: {},
            totalAvailable: 0,
          ),
          currentNominations: [],
        ),
      );

      notifier.selectRoute(3);

      expect(notifier.state.selectedRoute, equals(3));
      expect(notifier.state.nominations.isEmpty, isTrue);
      expect(notifier.state.lastValidation, isNull);
    });

    test('selectRoute should not update if route not available', () {
      notifier.state = notifier.state.copyWith(
        availableDocuments: AvailableDocumentsData(
          application: ApplicationInfo(
            id: 1,
            productCode: 'DBSEC',
            productName: 'DBS Enhanced Check',
            applicantName: 'Test User',
            status: 'draft',
          ),
          applicantContext: ApplicantContext(
            nationality: 'British',
            currentAddressCountry: 'United Kingdom',
            isUkNational: true,
            isUkResident: true,
            workType: 'paid',
            productCode: 'DBSEC',
          ),
          routing: RoutingInfo(
            recommendedRoute: 1,
            availableRoutes: [1, 2],
            routeRequirements: {},
          ),
          documents: DocumentsInfo(
            availableByGroup: {},
            totalAvailable: 0,
          ),
          currentNominations: [],
        ),
        selectedRoute: 2,
      );

      notifier.selectRoute(3);

      expect(notifier.state.selectedRoute, equals(2));
    });

    test('addDocumentNomination should add new nomination', () {
      final nomination = DocumentNomination(
        documentTypeId: 1,
        documentData: {'passport_number': '*********'},
        confirmsAddress: false,
      );

      notifier.addDocumentNomination(nomination);

      expect(notifier.state.nominations.length, equals(1));
      expect(notifier.state.nominations[0].documentTypeId, equals(1));
    });

    test('addDocumentNomination should update existing nomination', () {
      final nomination1 = DocumentNomination(
        documentTypeId: 1,
        documentData: {'passport_number': '*********'},
        confirmsAddress: false,
      );

      final nomination2 = DocumentNomination(
        documentTypeId: 1,
        documentData: {'passport_number': '*********'},
        confirmsAddress: true,
      );

      notifier.addDocumentNomination(nomination1);
      notifier.addDocumentNomination(nomination2);

      expect(notifier.state.nominations.length, equals(1));
      expect(notifier.state.nominations[0].documentData['passport_number'], equals('*********'));
      expect(notifier.state.nominations[0].confirmsAddress, isTrue);
    });

    test('removeDocumentNomination should remove nomination', () {
      final nomination = DocumentNomination(
        documentTypeId: 1,
        documentData: {'passport_number': '*********'},
        confirmsAddress: false,
      );

      notifier.addDocumentNomination(nomination);
      expect(notifier.state.nominations.length, equals(1));

      notifier.removeDocumentNomination(1);
      expect(notifier.state.nominations.isEmpty, isTrue);
    });

    test('validateNominations should call repository and update state', () async {
      final nomination = DocumentNomination(
        documentTypeId: 1,
        documentData: {'passport_number': '*********'},
        confirmsAddress: false,
      );

      notifier.addDocumentNomination(nomination);

      final mockValidationResponse = ValidationResponse(
        success: true,
        data: ValidationData(
          validationResult: ValidationResult(
            isValid: true,
            routeCompleted: true,
            canProceed: true,
          ),
          routeAnalysis: RouteAnalysis(requirementsMet: {}),
          documentValidations: [],
          nextSteps: NextSteps(action: 'submit', message: 'Ready to submit'),
        ),
      );

      when(mockRepository.validateDocumentNominations(any, any))
          .thenAnswer((_) async => mockValidationResponse);

      await notifier.validateNominations('123');

      expect(notifier.state.isValidating, isFalse);
      expect(notifier.state.lastValidation, isNotNull);
      expect(notifier.state.lastValidation!.validationResult.canProceed, isTrue);
      verify(mockRepository.validateDocumentNominations('123', any)).called(1);
    });

    test('submitNominations should call repository and return success', () async {
      final nomination = DocumentNomination(
        documentTypeId: 1,
        documentData: {'passport_number': '*********'},
        confirmsAddress: false,
      );

      notifier.addDocumentNomination(nomination);
      notifier.state = notifier.state.copyWith(
        lastValidation: ValidationData(
          validationResult: ValidationResult(
            isValid: true,
            routeCompleted: true,
            canProceed: true,
          ),
          routeAnalysis: RouteAnalysis(requirementsMet: {}),
          documentValidations: [],
          nextSteps: NextSteps(action: 'submit', message: 'Ready to submit'),
        ),
      );

      final mockSubmitResponse = ValidationResponse(
        success: true,
        data: ValidationData(
          validationResult: ValidationResult(
            isValid: true,
            routeCompleted: true,
            canProceed: true,
          ),
          routeAnalysis: RouteAnalysis(requirementsMet: {}),
          documentValidations: [],
          nextSteps: NextSteps(action: 'complete', message: 'Submitted successfully'),
        ),
      );

      when(mockRepository.submitDocumentNominations(any, any))
          .thenAnswer((_) async => mockSubmitResponse);

      final result = await notifier.submitNominations('123');

      expect(result, isTrue);
      expect(notifier.state.isSubmitting, isFalse);
      expect(notifier.state.successMessage, contains('successfully'));
      verify(mockRepository.submitDocumentNominations('123', any)).called(1);
    });

    test('state getters should work correctly', () {
      final nomination = DocumentNomination(
        documentTypeId: 1,
        documentData: {'passport_number': '*********'},
        confirmsAddress: false,
      );

      notifier.addDocumentNomination(nomination);

      expect(notifier.state.hasNominations, isTrue);
      expect(notifier.state.isDocumentNominated(1), isTrue);
      expect(notifier.state.isDocumentNominated(2), isFalse);
      expect(notifier.state.getNominationForDocument(1), isNotNull);
      expect(notifier.state.getNominationForDocument(2), isNull);
    });
  });
}
