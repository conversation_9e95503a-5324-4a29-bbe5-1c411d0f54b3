import 'package:SolidCheck/features/dbs/data/models/dbs_form_data.dart';
import 'package:SolidCheck/features/document_nomination/services/smart_document_service.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('SmartDocumentService', () {
    late DBSFormData mockFormData;

    setUp(() {
      mockFormData = DBSFormData(
        applicantDetails: ApplicantDetailsData(
          title: 'Mr',
          forename: '<PERSON>',
          middlenames: [],
          presentSurname: 'Smith',
          dateOfBirth: '1990-05-15',
          gender: 'male',
          niNumber: '*********',
          email: '<EMAIL>',
          contactNumber: '07123456789',
          currentAddress: CurrentAddressData(
            addressLine1: '10 Downing Street',
            addressLine2: '',
            addressTown: 'London',
            addressCounty: 'Greater London',
            postcode: 'SW1A 1AA',
            countryCode: 'GB',
            residentFromGyearMonth: '2020-01',
          ),
          previousAddresses: [
            PreviousAddressData(
              addressLine1: '1 Main Street',
              addressLine2: '',
              addressTown: 'Manchester',
              addressCounty: 'Greater Manchester',
              postcode: 'M1 1AA',
              countryCode: 'GB',
              residentFromGyearMonth: '2018-01',
              residentToGyearMonth: '2019-12',
            ),
          ],
          additionalApplicantDetails: AdditionalApplicantDetailsData(
            birthSurname: '',
            birthSurnameUntil: '',
            otherSurnames: [],
            otherForenames: [],
            birthTown: 'London',
            birthCounty: 'Greater London',
            birthCountry: 'GB',
            birthNationality: 'BRITISH',
            unspentConvictions: 'n',
            declarationByApplicant: 'y',
            languagePreference: 'english',
          ),
          applicantIdentityDetails: ApplicantIdentityDetailsData(
            identityVerified: 'y',
            evidenceCheckedBy: 'Test',
            nationalInsuranceNumber: '*********',
          ),
        ),
      );
    });

    group('getSimplifiedFields', () {
      test('returns essential fields for passport', () {
        final fields = SmartDocumentService.getSimplifiedFields('passport_uk');

        expect(fields['essential'], contains('document_number'));
        expect(fields['verification'], contains('name_matches_application'));
        expect(fields['optional'], isEmpty);
      });

      test('returns essential fields for driving licence', () {
        final fields = SmartDocumentService.getSimplifiedFields('photocard_drivers_licence_uk');

        expect(fields['essential'], contains('licence_number'));
        expect(fields['verification'], contains('name_matches_application'));
        expect(fields['optional'], isEmpty);
      });

      test('returns essential fields for bank statement', () {
        final fields = SmartDocumentService.getSimplifiedFields('bank_statement_uk');

        expect(fields['essential'], contains('statement_date'));
        expect(fields['verification'], contains('address_matches_application'));
        expect(fields['optional'], isEmpty);
      });
    });

    group('getDocumentRelevanceScore', () {
      test('gives high score for UK documents when applicant is British', () {
        final score = SmartDocumentService.getDocumentRelevanceScore('passport_uk', mockFormData);
        
        expect(score, greaterThan(0.8));
      });

      test('gives high score for UK driving licence when applicant is British and over 17', () {
        final score = SmartDocumentService.getDocumentRelevanceScore('photocard_drivers_licence_uk', mockFormData);
        
        expect(score, greaterThan(0.7));
      });

      test('gives lower score for non-UK documents when applicant is British', () {
        final score = SmartDocumentService.getDocumentRelevanceScore('passport_irish', mockFormData);
        
        expect(score, lessThan(0.5));
      });

      test('gives high score for Irish documents when applicant is Irish', () {
        final irishFormData = mockFormData.copyWith(
          applicantDetails: mockFormData.applicantDetails.copyWith(
            additionalApplicantDetails: mockFormData.applicantDetails.additionalApplicantDetails.copyWith(
              birthNationality: 'IRISH',
              birthCountry: 'IE',
            ),
          ),
        );

        final score = SmartDocumentService.getDocumentRelevanceScore('passport_irish', irishFormData);

        expect(score, greaterThan(0.8));
      });
    });

    group('getDocumentRelevanceLevel', () {
      test('returns highly_recommended for score >= 0.8', () {
        final level = SmartDocumentService.getDocumentRelevanceLevel(0.9);
        expect(level, equals('highly_recommended'));
      });

      test('returns recommended for score >= 0.5', () {
        final level = SmartDocumentService.getDocumentRelevanceLevel(0.6);
        expect(level, equals('recommended'));
      });

      test('returns suitable for score >= 0.3', () {
        final level = SmartDocumentService.getDocumentRelevanceLevel(0.4);
        expect(level, equals('suitable'));
      });

      test('returns alternative for score < 0.3', () {
        final level = SmartDocumentService.getDocumentRelevanceLevel(0.2);
        expect(level, equals('alternative'));
      });
    });

    group('validateUKDrivingLicence', () {
      test('validates correct UK driving licence number', () {
        final validLicence = 'SMITH900515J9J9A';
        final result = SmartDocumentService.validateUKDrivingLicence(validLicence, mockFormData);

        expect(result, isNull);
      });

      test('rejects incorrect length', () {
        final invalidLicence = 'SMITH901055J99A';
        final result = SmartDocumentService.validateUKDrivingLicence(invalidLicence, mockFormData);
        
        expect(result, equals('UK driving licence number must be 16 characters'));
      });

      test('rejects incorrect surname', () {
        final invalidLicence = 'JONES901055J99AA';
        final result = SmartDocumentService.validateUKDrivingLicence(invalidLicence, mockFormData);
        
        expect(result, contains('surname does not match'));
      });
    });

    group('getSmartDefaults', () {
      test('provides UK as default country for British applicant', () {
        final defaults = SmartDocumentService.getSmartDefaults('passport_uk', mockFormData);
        
        expect(defaults['issue_country'], contains('United Kingdom'));
      });

      test('provides current tax year for council tax', () {
        final defaults = SmartDocumentService.getSmartDefaults('council_tax_uk', mockFormData);
        final currentYear = DateTime.now().year;
        
        expect(defaults['tax_year'], equals('$currentYear-${currentYear + 1}'));
      });
    });

    group('getContextualHints', () {
      test('provides postcode hints for address documents', () {
        final hints = SmartDocumentService.getContextualHints('postcode_on_document', 'bank_statement_uk', mockFormData);
        
        expect(hints, isNotEmpty);
        expect(hints.first, contains('SW1A 1AA'));
        expect(hints.first, contains('M1 1AA'));
      });

      test('provides format hint for UK driving licence', () {
        final hints = SmartDocumentService.getContextualHints('licence_number', 'photocard_drivers_licence_uk', mockFormData);
        
        expect(hints, isNotEmpty);
        expect(hints.first, contains('16-character format'));
      });
    });
  });
}
