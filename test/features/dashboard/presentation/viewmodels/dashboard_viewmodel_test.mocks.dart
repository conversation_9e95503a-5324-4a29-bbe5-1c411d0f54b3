// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in SolidCheck/test/features/dashboard/presentation/viewmodels/dashboard_viewmodel_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:mockito/mockito.dart' as _i1;
import 'package:SolidCheck/features/dashboard/data/models/client_applicants.dart'
    as _i2;
import 'package:SolidCheck/features/dashboard/data/models/dashboard_stats.dart'
    as _i3;
import 'package:SolidCheck/features/dashboard/domain/usecases/get_applicants_usecase.dart'
    as _i4;
import 'package:SolidCheck/features/dashboard/domain/usecases/get_dashboard_stats_usecase.dart'
    as _i6;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeClientApplicantsModel_0 extends _i1.SmartFake
    implements _i2.ClientApplicantsModel {
  _FakeClientApplicantsModel_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeDashboardStatsData_1 extends _i1.SmartFake
    implements _i3.DashboardStatsData {
  _FakeDashboardStatsData_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [GetApplicantsUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetApplicantsUseCase extends _i1.Mock
    implements _i4.GetApplicantsUseCase {
  MockGetApplicantsUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<_i2.ClientApplicantsModel> execute() =>
      (super.noSuchMethod(
            Invocation.method(#execute, []),
            returnValue: _i5.Future<_i2.ClientApplicantsModel>.value(
              _FakeClientApplicantsModel_0(
                this,
                Invocation.method(#execute, []),
              ),
            ),
          )
          as _i5.Future<_i2.ClientApplicantsModel>);

  @override
  _i5.Future<_i2.ClientApplicantsModel> executeWithSearch(String? query) =>
      (super.noSuchMethod(
            Invocation.method(#executeWithSearch, [query]),
            returnValue: _i5.Future<_i2.ClientApplicantsModel>.value(
              _FakeClientApplicantsModel_0(
                this,
                Invocation.method(#executeWithSearch, [query]),
              ),
            ),
          )
          as _i5.Future<_i2.ClientApplicantsModel>);

  @override
  _i5.Future<_i2.ClientApplicantsModel> executeWithPagination({
    int? page = 1,
    int? perPage = 20,
    String? search,
    String? statusFilter,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#executeWithPagination, [], {
              #page: page,
              #perPage: perPage,
              #search: search,
              #statusFilter: statusFilter,
            }),
            returnValue: _i5.Future<_i2.ClientApplicantsModel>.value(
              _FakeClientApplicantsModel_0(
                this,
                Invocation.method(#executeWithPagination, [], {
                  #page: page,
                  #perPage: perPage,
                  #search: search,
                  #statusFilter: statusFilter,
                }),
              ),
            ),
          )
          as _i5.Future<_i2.ClientApplicantsModel>);
}

/// A class which mocks [GetDashboardStatsUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetDashboardStatsUseCase extends _i1.Mock
    implements _i6.GetDashboardStatsUseCase {
  MockGetDashboardStatsUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<_i3.DashboardStatsData> execute() =>
      (super.noSuchMethod(
            Invocation.method(#execute, []),
            returnValue: _i5.Future<_i3.DashboardStatsData>.value(
              _FakeDashboardStatsData_1(this, Invocation.method(#execute, [])),
            ),
          )
          as _i5.Future<_i3.DashboardStatsData>);

  @override
  _i5.Future<_i3.DashboardStatsData> executeWithFallback() =>
      (super.noSuchMethod(
            Invocation.method(#executeWithFallback, []),
            returnValue: _i5.Future<_i3.DashboardStatsData>.value(
              _FakeDashboardStatsData_1(
                this,
                Invocation.method(#executeWithFallback, []),
              ),
            ),
          )
          as _i5.Future<_i3.DashboardStatsData>);

  @override
  _i5.Future<int> getStatsForStatus(String? status) =>
      (super.noSuchMethod(
            Invocation.method(#getStatsForStatus, [status]),
            returnValue: _i5.Future<int>.value(0),
          )
          as _i5.Future<int>);
}
