import 'package:SolidCheck/features/dashboard/data/models/client_applicants.dart';
import 'package:SolidCheck/features/dashboard/data/models/dashboard_stats.dart';
import 'package:SolidCheck/features/dashboard/domain/usecases/get_applicants_usecase.dart';
import 'package:SolidCheck/features/dashboard/domain/usecases/get_dashboard_stats_usecase.dart';
import 'package:SolidCheck/features/dashboard/presentation/viewmodels/dashboard_viewmodel.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'dashboard_viewmodel_test.mocks.dart';

@GenerateMocks([GetApplicantsUseCase, GetDashboardStatsUseCase])
void main() {
  late DashboardViewModel viewModel;
  late MockGetApplicantsUseCase mockGetApplicantsUseCase;
  late MockGetDashboardStatsUseCase mockGetDashboardStatsUseCase;

  setUp(() {
    mockGetApplicantsUseCase = MockGetApplicantsUseCase();
    mockGetDashboardStatsUseCase = MockGetDashboardStatsUseCase();
    viewModel = DashboardViewModel(
      mockGetApplicantsUseCase,
      mockGetDashboardStatsUseCase,
    );
  });

  group('DashboardViewModel Error Handling Tests', () {
    test('should handle server error (500) with user-friendly message', () async {
      when(mockGetApplicantsUseCase.executeWithPagination(
        page: anyNamed('page'),
        perPage: anyNamed('perPage'),
        search: anyNamed('search'),
        statusFilter: anyNamed('statusFilter'),
      )).thenThrow(Exception('Server error (500): Internal server error'));

      await viewModel.loadApplicants(page: 1, perPage: 20);

      expect(viewModel.state.isLoading, false);
      expect(viewModel.state.error, contains('Server is temporarily unavailable'));
      expect(viewModel.state.error, contains('try again in a few moments'));
    });

    test('should handle connection refused error with specific message', () async {
      when(mockGetApplicantsUseCase.executeWithPagination(
        page: anyNamed('page'),
        perPage: anyNamed('perPage'),
        search: anyNamed('search'),
        statusFilter: anyNamed('statusFilter'),
      )).thenThrow(Exception('Network error: connection refused'));

      await viewModel.loadApplicants(page: 1, perPage: 20);

      expect(viewModel.state.isLoading, false);
      expect(viewModel.state.error, contains('Network connection error'));
      expect(viewModel.state.error, contains('internet connection'));
    });

    test('should handle timeout error with appropriate message', () async {
      when(mockGetApplicantsUseCase.executeWithPagination(
        page: anyNamed('page'),
        perPage: anyNamed('perPage'),
        search: anyNamed('search'),
        statusFilter: anyNamed('statusFilter'),
      )).thenThrow(Exception('Network error: timeout'));

      await viewModel.loadApplicants(page: 1, perPage: 20);

      expect(viewModel.state.isLoading, false);
      expect(viewModel.state.error, contains('Network connection error'));
      expect(viewModel.state.error, contains('internet connection'));
    });

    test('should handle authentication error with login prompt', () async {
      when(mockGetApplicantsUseCase.executeWithPagination(
        page: anyNamed('page'),
        perPage: anyNamed('perPage'),
        search: anyNamed('search'),
        statusFilter: anyNamed('statusFilter'),
      )).thenThrow(Exception('Authentication error: Invalid token'));

      await viewModel.loadApplicants(page: 1, perPage: 20);

      expect(viewModel.state.isLoading, false);
      expect(viewModel.state.error, contains('session has expired'));
      expect(viewModel.state.error, contains('log in again'));
    });

    test('should handle generic error with fallback message', () async {
      when(mockGetApplicantsUseCase.executeWithPagination(
        page: anyNamed('page'),
        perPage: anyNamed('perPage'),
        search: anyNamed('search'),
        statusFilter: anyNamed('statusFilter'),
      )).thenThrow(Exception('Unknown error occurred'));

      await viewModel.loadApplicants(page: 1, perPage: 20);

      expect(viewModel.state.isLoading, false);
      expect(viewModel.state.error, contains('Unable to load applicants'));
      expect(viewModel.state.error, contains('Please try again'));
    });

    test('should retry loading applicants with exponential backoff', () async {
      var callCount = 0;
      when(mockGetApplicantsUseCase.executeWithPagination(
        page: anyNamed('page'),
        perPage: anyNamed('perPage'),
        search: anyNamed('search'),
        statusFilter: anyNamed('statusFilter'),
      )).thenAnswer((_) async {
        callCount++;
        if (callCount < 3) {
          throw Exception('Temporary error');
        }
        return ClientApplicantsModel(
          data: [],
          pagination: null,
          statusSummary: null,
        );
      });

      when(mockGetDashboardStatsUseCase.execute()).thenAnswer((_) async =>
        DashboardStatsData(
          totalApplicants: 0,
          inProgress: 0,
          furtherActionPending: 0,
          staffReviewPending: 0,
          complete: 0,
        ));

      await viewModel.initialize();

      verify(mockGetApplicantsUseCase.executeWithPagination(
        page: anyNamed('page'),
        perPage: anyNamed('perPage'),
        search: anyNamed('search'),
        statusFilter: anyNamed('statusFilter'),
      )).called(3);

      expect(viewModel.state.isLoading, false);
      expect(viewModel.state.error, isNull);
    });

    test('should set fallback stats when stats loading fails', () async {
      when(mockGetApplicantsUseCase.executeWithPagination(
        page: anyNamed('page'),
        perPage: anyNamed('perPage'),
        search: anyNamed('search'),
        statusFilter: anyNamed('statusFilter'),
      )).thenAnswer((_) async => ClientApplicantsModel(
        data: [],
        pagination: null,
        statusSummary: null,
      ));

      when(mockGetDashboardStatsUseCase.execute())
          .thenThrow(Exception('Stats loading failed'));

      await viewModel.initialize();

      expect(viewModel.state.isLoading, false);
      expect(viewModel.state.error, isNull);
      expect(viewModel.state.stats, isNotNull);
      expect(viewModel.state.stats!.totalApplicants, 0);
    });

    test('should clear error when clearError is called', () async {
      when(mockGetApplicantsUseCase.executeWithPagination(
        page: anyNamed('page'),
        perPage: anyNamed('perPage'),
        search: anyNamed('search'),
        statusFilter: anyNamed('statusFilter'),
      )).thenThrow(Exception('Test error'));

      await viewModel.initialize();
      expect(viewModel.state.error, isNotNull);

      viewModel.clearError();
      expect(viewModel.state.error, isNull);
    });

    test('should handle loadApplicants error with user-friendly messages', () async {
      when(mockGetApplicantsUseCase.executeWithPagination(
        page: anyNamed('page'),
        perPage: anyNamed('perPage'),
        search: anyNamed('search'),
        statusFilter: anyNamed('statusFilter'),
      )).thenThrow(Exception('Network error: Failed to connect'));

      await viewModel.loadApplicants(page: 1, perPage: 20);

      expect(viewModel.state.isLoading, false);
      expect(viewModel.state.error, contains('Network connection error'));
      expect(viewModel.state.error, contains('internet connection'));
    });

    test('should handle authentication error in loadApplicants', () async {
      when(mockGetApplicantsUseCase.executeWithPagination(
        page: anyNamed('page'),
        perPage: anyNamed('perPage'),
        search: anyNamed('search'),
        statusFilter: anyNamed('statusFilter'),
      )).thenThrow(Exception('Authentication error: Invalid token'));

      await viewModel.loadApplicants(page: 1, perPage: 20);

      expect(viewModel.state.isLoading, false);
      expect(viewModel.state.error, contains('session has expired'));
      expect(viewModel.state.error, contains('log in again'));
    });
  });

  group('DashboardViewModel Health Check Tests', () {
    test('should return true when server health check passes', () async {
      final result = await viewModel.checkServerHealth();
      expect(result, isA<bool>());
    });

    test('should return false when server health check fails', () async {
      final result = await viewModel.checkServerHealth();
      expect(result, isA<bool>());
    });
  });
}
