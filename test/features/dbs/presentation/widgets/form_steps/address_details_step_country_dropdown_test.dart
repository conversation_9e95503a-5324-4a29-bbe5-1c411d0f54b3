import 'package:SolidCheck/shared/data/country_data.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Country Dropdown Data Tests', () {
    test('should not have duplicate country ISO codes', () {
      final countryCodes = Countries.all.map((country) => country.isoCode).toList();
      final uniqueCodes = countryCodes.toSet();
      
      expect(countryCodes.length, equals(uniqueCodes.length), 
        reason: 'There should be no duplicate ISO codes in the country list');
    });

    test('should not have duplicate country names', () {
      final countryNames = Countries.all.map((country) => country.name).toList();
      final uniqueNames = countryNames.toSet();
      
      expect(countryNames.length, equals(uniqueNames.length), 
        reason: 'There should be no duplicate country names in the country list');
    });

    test('should have United Kingdom with GB code', () {
      final uk = Countries.all.firstWhere(
        (country) => country.name == 'United Kingdom',
        orElse: () => throw Exception('United Kingdom not found'),
      );
      
      expect(uk.isoCode, equals('GB'));
      expect(uk.nationality, equals('BRITISH'));
    });

    test('should generate dropdown items with ISO codes as values', () {
      final dropdownItems = Countries.getCountryDropdownItems();
      
      expect(dropdownItems.isNotEmpty, isTrue);
      
      // Check that all values are ISO codes (2 characters)
      for (final item in dropdownItems) {
        expect(item.value?.length, equals(2), 
          reason: 'All dropdown values should be 2-character ISO codes');
      }
      
      // Check that United Kingdom dropdown item has GB as value
      final ukItem = dropdownItems.firstWhere(
        (item) => item.value == 'GB',
        orElse: () => throw Exception('GB dropdown item not found'),
      );
      
      expect(ukItem.value, equals('GB'));
    });

    test('should not have duplicate values in dropdown items', () {
      final dropdownItems = Countries.getCountryDropdownItems();
      final values = dropdownItems.map((item) => item.value).toList();
      final uniqueValues = values.toSet();
      
      expect(values.length, equals(uniqueValues.length), 
        reason: 'There should be no duplicate values in dropdown items');
    });

    test('should validate ISO codes correctly', () {
      expect(Countries.isValidIsoCode('GB'), isTrue);
      expect(Countries.isValidIsoCode('US'), isTrue);
      expect(Countries.isValidIsoCode('FR'), isTrue);
      expect(Countries.isValidIsoCode('INVALID'), isFalse);
      expect(Countries.isValidIsoCode('United Kingdom'), isFalse);
    });

    test('should get country name by ISO code', () {
      expect(Countries.getCountryNameByCode('GB'), equals('United Kingdom'));
      expect(Countries.getCountryNameByCode('US'), equals('United States'));
      expect(Countries.getCountryNameByCode('INVALID'), isNull);
    });
  });
}
