import 'package:SolidCheck/features/auth/data/repositories/auth.dart';
import 'package:SolidCheck/features/dbs/data/models/dbs_form_data.dart';
import 'package:SolidCheck/features/dbs/presentation/viewmodels/dbs_form_viewmodel.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'dbs_form_viewmodel_test.mocks.dart';

@GenerateMocks([AuthRepository])
void main() {
  group('DBSFormViewModel', () {
    late MockAuthRepository mockAuthRepository;
    late DBSFormViewModel viewModel;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
      viewModel = DBSFormViewModel(mockAuthRepository);
    });

    test('submitForm fails when no authentication token is available', () async {
      when(mockAuthRepository.getToken()).thenAnswer((_) async => null);

      await viewModel.submitForm();

      expect(viewModel.state.isLoading, false);
      expect(viewModel.state.error, 'Authentication required. Please log in again.');
      expect(viewModel.state.isSubmitted, false);
    });

    test('submitForm calls getToken from auth repository', () async {
      const testToken = 'valid-token-123';
      when(mockAuthRepository.getToken()).thenAnswer((_) async => testToken);

      await viewModel.submitForm();

      verify(mockAuthRepository.getToken()).called(1);
    });

    test('initial state has correct default values', () {
      expect(viewModel.state.isLoading, false);
      expect(viewModel.state.isSubmitted, false);
      expect(viewModel.state.error, null);
      expect(viewModel.state.currentStep, 0);
      expect(viewModel.state.unspentConvictions, 'n');
      expect(viewModel.state.declarationByApplicant, 'n');
      expect(viewModel.state.languagePreference, 'english');
    });

    test('updateConsentFields updates state correctly', () {
      viewModel.updateConsentFields(
        unspentConvictions: 'y',
        declarationByApplicant: 'y',
        languagePreference: 'welsh',
      );

      expect(viewModel.state.unspentConvictions, 'y');
      expect(viewModel.state.declarationByApplicant, 'y');
      expect(viewModel.state.languagePreference, 'welsh');
    });

    test('nextStep increments current step when allowed', () {
      final initialStep = viewModel.state.currentStep;
      
      viewModel.nextStep();
      
      expect(viewModel.state.currentStep, initialStep + 1);
    });

    test('previousStep decrements current step when possible', () {
      viewModel.goToStep(2);
      final currentStep = viewModel.state.currentStep;
      
      viewModel.previousStep();
      
      expect(viewModel.state.currentStep, currentStep - 1);
    });

    group('Smart Birth Surname Until Calculation', () {
      test('should return current year when no other surnames exist', () {
        final currentYear = DateTime.now().year.toString();

        // Initialize with empty other surnames
        final formData = DBSFormData(
          applicantDetails: ApplicantDetailsData.empty().copyWith(
            additionalApplicantDetails: AdditionalApplicantDetailsData.empty().copyWith(
              otherSurnames: [],
            ),
          ),
        );

        viewModel.state = viewModel.state.copyWith(formData: formData);

        final result = viewModel.calculateSmartBirthSurnameUntil();
        expect(result, equals(currentYear));
      });

      test('should return earliest used from year when other surnames exist', () {
        // Initialize with multiple other surnames
        final formData = DBSFormData(
          applicantDetails: ApplicantDetailsData.empty().copyWith(
            additionalApplicantDetails: AdditionalApplicantDetailsData.empty().copyWith(
              otherSurnames: [
                OtherSurnameData(
                  name: 'SMITH',
                  usedFrom: '2020',
                  usedTo: '2022',
                ),
                OtherSurnameData(
                  name: 'JONES',
                  usedFrom: '2018', // This is the earliest
                  usedTo: '2019',
                ),
                OtherSurnameData(
                  name: 'BROWN',
                  usedFrom: '2021',
                  usedTo: '2023',
                ),
              ],
            ),
          ),
        );

        viewModel.state = viewModel.state.copyWith(formData: formData);

        final result = viewModel.calculateSmartBirthSurnameUntil();
        expect(result, equals('2018'));
      });

      test('should handle empty used from years gracefully', () {
        final currentYear = DateTime.now().year.toString();

        // Initialize with other surnames but empty used from years
        final formData = DBSFormData(
          applicantDetails: ApplicantDetailsData.empty().copyWith(
            additionalApplicantDetails: AdditionalApplicantDetailsData.empty().copyWith(
              otherSurnames: [
                OtherSurnameData(
                  name: 'SMITH',
                  usedFrom: '', // Empty
                  usedTo: '2022',
                ),
                OtherSurnameData(
                  name: 'JONES',
                  usedFrom: '', // Empty
                  usedTo: '2019',
                ),
              ],
            ),
          ),
        );

        viewModel.state = viewModel.state.copyWith(formData: formData);

        final result = viewModel.calculateSmartBirthSurnameUntil();
        expect(result, equals(currentYear)); // Should fall back to current year
      });
    });
  });
}
