import 'package:SolidCheck/features/dbs/data/models/postcode_lookup_models.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('PostcodeLookupResponse', () {
    test('should create PostcodeLookupResponse from JSON', () {
      final json = {
        'success': true,
        'count': 2,
        'addresses': [
          {
            'address_line_1': '1 <PERSON> Ludd Close',
            'address_line_2': 'Anstey',
            'town_city': 'LEICESTER',
            'postcode': 'LE7 7AQ',
            'udprn': '28291445'
          },
          {
            'address_line_1': '3 Ned Ludd Close',
            'address_line_2': 'Anstey',
            'town_city': 'LEICESTER',
            'postcode': 'LE7 7AQ',
            'udprn': '28291446'
          }
        ]
      };

      final response = PostcodeLookupResponse.fromJson(json);

      expect(response.success, true);
      expect(response.count, 2);
      expect(response.addresses.length, 2);
      expect(response.addresses[0].addressLine1, '1 <PERSON>');
      expect(response.addresses[1].addressLine1, '3 <PERSON> Close');
    });

    test('should handle empty addresses list', () {
      final json = {
        'success': false,
        'count': 0,
        'addresses': []
      };

      final response = PostcodeLookupResponse.fromJson(json);

      expect(response.success, false);
      expect(response.count, 0);
      expect(response.addresses.length, 0);
    });

    test('should handle missing fields with defaults', () {
      final json = <String, dynamic>{};

      final response = PostcodeLookupResponse.fromJson(json);

      expect(response.success, false);
      expect(response.count, 0);
      expect(response.addresses.length, 0);
    });

    test('should convert to JSON correctly', () {
      final response = PostcodeLookupResponse(
        success: true,
        count: 1,
        addresses: [
          AddressResult(
            addressLine1: '1 Test Street',
            addressLine2: 'Test Area',
            townCity: 'TEST CITY',
            postcode: 'TE1 1ST',
            udprn: '12345678',
          ),
        ],
      );

      final json = response.toJson();

      expect(json['success'], true);
      expect(json['count'], 1);
      expect(json['addresses'], isA<List>());
      expect(json['addresses'].length, 1);
    });
  });

  group('AddressResult', () {
    test('should create AddressResult from JSON', () {
      final json = {
        'address_line_1': '1 Ned Ludd Close',
        'address_line_2': 'Anstey',
        'town_city': 'LEICESTER',
        'postcode': 'LE7 7AQ',
        'udprn': '28291445'
      };

      final address = AddressResult.fromJson(json);

      expect(address.addressLine1, '1 Ned Ludd Close');
      expect(address.addressLine2, 'Anstey');
      expect(address.townCity, 'LEICESTER');
      expect(address.postcode, 'LE7 7AQ');
      expect(address.udprn, '28291445');
    });

    test('should handle missing fields with empty strings', () {
      final json = <String, dynamic>{};

      final address = AddressResult.fromJson(json);

      expect(address.addressLine1, '');
      expect(address.addressLine2, '');
      expect(address.townCity, '');
      expect(address.postcode, '');
      expect(address.udprn, '');
    });

    test('should convert to JSON correctly', () {
      final address = AddressResult(
        addressLine1: '1 Test Street',
        addressLine2: 'Test Area',
        townCity: 'TEST CITY',
        postcode: 'TE1 1ST',
        udprn: '12345678',
      );

      final json = address.toJson();

      expect(json['address_line_1'], '1 Test Street');
      expect(json['address_line_2'], 'Test Area');
      expect(json['town_city'], 'TEST CITY');
      expect(json['postcode'], 'TE1 1ST');
      expect(json['udprn'], '12345678');
    });

    test('should generate correct display address', () {
      final address = AddressResult(
        addressLine1: '1 Ned Ludd Close',
        addressLine2: 'Anstey',
        townCity: 'LEICESTER',
        postcode: 'LE7 7AQ',
        udprn: '28291445',
      );

      expect(address.displayAddress, '1 Ned Ludd Close, Anstey, LEICESTER, LE7 7AQ');
    });

    test('should handle empty fields in display address', () {
      final address = AddressResult(
        addressLine1: '1 Test Street',
        addressLine2: '',
        townCity: 'TEST CITY',
        postcode: 'TE1 1ST',
        udprn: '12345678',
      );

      expect(address.displayAddress, '1 Test Street, TEST CITY, TE1 1ST');
    });

    test('should handle all empty fields in display address', () {
      final address = AddressResult(
        addressLine1: '',
        addressLine2: '',
        townCity: '',
        postcode: '',
        udprn: '12345678',
      );

      expect(address.displayAddress, '');
    });

    test('should handle only address line 1 in display address', () {
      final address = AddressResult(
        addressLine1: '1 Test Street',
        addressLine2: '',
        townCity: '',
        postcode: '',
        udprn: '12345678',
      );

      expect(address.displayAddress, '1 Test Street');
    });
  });
}
