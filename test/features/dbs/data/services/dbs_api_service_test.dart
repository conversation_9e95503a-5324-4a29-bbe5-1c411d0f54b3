import 'package:SolidCheck/features/dbs/data/models/dbs_api_request.dart';
import 'package:SolidCheck/features/dbs/data/services/dbs_api_service.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'dbs_api_service_test.mocks.dart';

@GenerateMocks([Dio])
void main() {
  group('DBSApiService', () {
    late MockDio mockDio;
    late DBSApiRequest testRequest;

    setUp(() {
      mockDio = MockDio();
      testRequest = DBSApiRequest(
        applicantDetails: ApplicantDetailsApi(
          title: 'MR',
          forename: 'TEST',
          presentSurname: 'USER',
          dateOfBirth: '1990-01-01',
          gender: 'male',
          niNumber: '',
          currentAddress: CurrentAddressApi(
            address: AddressApi(
              addressLine1: '123 Test Street',
              addressLine2: 'Test Area',
              addressTown: 'Test Town',
              addressCounty: 'Test County',
              postcode: 'TE1 1ST',
              countryCode: 'GB',
            ),
            residentFromGyearMonth: '2020-01',
          ),
          previousAddresses: [],
          additionalApplicantDetails: AdditionalApplicantDetailsApi(
            birthSurname: 'USER',
            birthSurnameUntil: '',
            birthTown: 'Test Town',
            birthCounty: 'Test County',
            birthCountry: 'GB',
            birthNationality: 'BRITISH',
            contactNumber: '',
            unspentConvictions: 'n',
            declarationByApplicant: 'y',
            languagePreference: 'english',
          ),
          applicantIdentityDetails: ApplicantIdentityDetailsApi(
            identityVerified: 'y',
            evidenceCheckedBy: 'SYSTEM',
          ),
        ),
      );
    });

    test('saveFormData includes Authorization header with token', () async {
      final token = 'test-token-123';
      final responseData = {'success': true, 'message': 'Data saved'};
      
      when(mockDio.post(
        any,
        data: any,
        options: any,
      )).thenAnswer((_) async => Response(
        data: responseData,
        statusCode: 200,
        requestOptions: RequestOptions(path: ''),
      ));

      DBSApiService.initialize();
      
      final result = await DBSApiService.saveFormData(testRequest, token: token);

      expect(result['success'], true);
      expect(result['message'], 'Form data saved successfully');

      final captured = verify(mockDio.post(
        captureAny,
        data: captureAny,
        options: captureAny,
      )).captured;

      final options = captured[2] as Options;
      expect(options.headers?['Authorization'], 'Bearer $token');
    });

    test('saveFormData requires token parameter', () async {
      // This test verifies that the token parameter is required at compile time
      // The actual test is that the code compiles with the required parameter
      const token = 'test-token';

      when(mockDio.post(
        any,
        data: any,
        options: any,
      )).thenAnswer((_) async => Response(
        data: {'success': true},
        statusCode: 200,
        requestOptions: RequestOptions(path: ''),
      ));

      DBSApiService.initialize();

      final result = await DBSApiService.saveFormData(testRequest, token: token);
      expect(result['success'], true);
    });

    test('saveFormData handles 401 unauthorized response', () async {
      final token = 'invalid-token';
      
      when(mockDio.post(
        any,
        data: any,
        options: any,
      )).thenThrow(DioException(
        response: Response(
          data: {'message': 'Unauthenticated.'},
          statusCode: 401,
          requestOptions: RequestOptions(path: ''),
        ),
        requestOptions: RequestOptions(path: ''),
        type: DioExceptionType.badResponse,
      ));

      DBSApiService.initialize();
      
      final result = await DBSApiService.saveFormData(testRequest, token: token);

      expect(result['success'], false);
      expect(result['message'], contains('Authentication failed'));
    });

    test('saveFormData handles network errors', () async {
      final token = 'test-token';
      
      when(mockDio.post(
        any,
        data: any,
        options: any,
      )).thenThrow(DioException(
        requestOptions: RequestOptions(path: ''),
        type: DioExceptionType.connectionTimeout,
      ));

      DBSApiService.initialize();
      
      final result = await DBSApiService.saveFormData(testRequest, token: token);

      expect(result['success'], false);
      expect(result['message'], contains('Connection timeout'));
    });
  });
}
