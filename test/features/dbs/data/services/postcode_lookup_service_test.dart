import 'package:SolidCheck/features/dbs/data/models/postcode_lookup_models.dart';
import 'package:SolidCheck/features/dbs/data/services/postcode_lookup_service.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('PostcodeLookupService', () {
    setUp(() {
      PostcodeLookupService.initialize();
    });

    group('lookupPostcode', () {
      test('should return PostcodeLookupResponse when API call is successful', () async {
        const postcode = 'LE7 7AQ';

        final result = await PostcodeLookupService.lookupPostcode(postcode);

        expect(result.success, true);
        expect(result.count, greaterThan(0));
        expect(result.addresses.length, greaterThan(0));
        expect(result.addresses[0].addressLine1, isNotEmpty);
        expect(result.addresses[0].townCity, 'LEICESTER');
        expect(result.addresses[0].postcode, 'LE7 7AQ');
      });

      test('should throw exception when postcode is empty', () async {
        expect(
          () => PostcodeLookupService.lookupPostcode(''),
          throwsA(isA<Exception>().having(
            (e) => e.toString(),
            'message',
            contains('Postcode cannot be empty'),
          )),
        );
      });

      test('should handle invalid postcode gracefully', () async {
        const postcode = 'INVALID';

        final result = await PostcodeLookupService.lookupPostcode(postcode);

        expect(result.success, true);
        expect(result.count, 0);
        expect(result.addresses.length, 0);
      });
    });

    group('isValidPostcodeFormat', () {
      test('should return true for valid UK postcodes', () {
        expect(PostcodeLookupService.isValidPostcodeFormat('LE7 7AQ'), true);
        expect(PostcodeLookupService.isValidPostcodeFormat('SW1A 1AA'), true);
        expect(PostcodeLookupService.isValidPostcodeFormat('M1 1AA'), true);
        expect(PostcodeLookupService.isValidPostcodeFormat('B33 8TH'), true);
        expect(PostcodeLookupService.isValidPostcodeFormat('W1A 0AX'), true);
        expect(PostcodeLookupService.isValidPostcodeFormat('EC1A 1BB'), true);
      });

      test('should return false for invalid postcodes', () {
        expect(PostcodeLookupService.isValidPostcodeFormat(''), false);
        expect(PostcodeLookupService.isValidPostcodeFormat('INVALID'), false);
        expect(PostcodeLookupService.isValidPostcodeFormat('123456'), false);
        expect(PostcodeLookupService.isValidPostcodeFormat('LE7'), false);
        expect(PostcodeLookupService.isValidPostcodeFormat('LE7 7'), false);
      });

      test('should handle postcodes without spaces', () {
        expect(PostcodeLookupService.isValidPostcodeFormat('LE77AQ'), true);
        expect(PostcodeLookupService.isValidPostcodeFormat('SW1A1AA'), true);
      });
    });

    group('formatPostcodeForDisplay', () {
      test('should format postcodes with proper spacing', () {
        expect(PostcodeLookupService.formatPostcodeForDisplay('LE77AQ'), 'LE7 7AQ');
        expect(PostcodeLookupService.formatPostcodeForDisplay('SW1A1AA'), 'SW1A 1AA');
        expect(PostcodeLookupService.formatPostcodeForDisplay('M11AA'), 'M1 1AA');
      });

      test('should handle already formatted postcodes', () {
        expect(PostcodeLookupService.formatPostcodeForDisplay('LE7 7AQ'), 'LE7 7AQ');
        expect(PostcodeLookupService.formatPostcodeForDisplay('SW1A 1AA'), 'SW1A 1AA');
      });

      test('should handle lowercase postcodes', () {
        expect(PostcodeLookupService.formatPostcodeForDisplay('le77aq'), 'LE7 7AQ');
        expect(PostcodeLookupService.formatPostcodeForDisplay('sw1a1aa'), 'SW1A 1AA');
      });

      test('should handle short postcodes', () {
        expect(PostcodeLookupService.formatPostcodeForDisplay('LE7'), 'LE7');
        expect(PostcodeLookupService.formatPostcodeForDisplay('M1'), 'M1');
      });
    });
  });
}
