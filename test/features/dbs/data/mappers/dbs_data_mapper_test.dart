import 'package:SolidCheck/features/dbs/data/mappers/dbs_data_mapper.dart';
import 'package:SolidCheck/features/dbs/data/models/dbs_form_data.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DBSDataMapper', () {
    group('validateForSubmission', () {
      test('should return error when postcode is missing for GB address', () {
        final formData = DBSFormData(
          applicantDetails: ApplicantDetailsData(
            title: 'MR',
            forename: 'JOHN',
            middlenames: [],
            presentSurname: 'DOE',
            dateOfBirth: '1990-01-01',
            gender: 'male',
            niNumber: '*********',
            email: '<EMAIL>',
            contactNumber: '01234567890',
            currentAddress: CurrentAddressData(
              addressLine1: '123 TEST STREET',
              addressLine2: '',
              addressTown: 'LONDON',
              addressCounty: 'GREATER LONDON',
              postcode: '', // Missing postcode
              countryCode: 'GB',
              residentFromGyearMonth: '2020-01',
            ),
            previousAddresses: [],
            additionalApplicantDetails: AdditionalApplicantDetailsData(
              birthSurname: '',
              birthSurnameUntil: '',
              otherSurnames: [],
              otherForenames: [],
              birthTown: 'LONDON',
              birthCounty: 'GREATER LONDON',
              birthCountry: 'GB',
              birthNationality: 'BRITISH',
              unspentConvictions: 'n',
              declarationByApplicant: 'n',
              languagePreference: 'english',
            ),
            applicantIdentityDetails: ApplicantIdentityDetailsData(
              identityVerified: 'y',
              evidenceCheckedBy: 'SYSTEM',
              nationalInsuranceNumber: '*********',
            ),
          ),
        );

        final errors = DBSDataMapper.validateForSubmission(formData);

        expect(errors, contains('Postcode is required for UK addresses'));
      });

      test('should not return postcode error when postcode is provided for GB address', () {
        final formData = DBSFormData(
          applicantDetails: ApplicantDetailsData(
            title: 'MR',
            forename: 'JOHN',
            middlenames: [],
            presentSurname: 'DOE',
            dateOfBirth: '1990-01-01',
            gender: 'male',
            niNumber: '*********',
            email: '<EMAIL>',
            contactNumber: '01234567890',
            currentAddress: CurrentAddressData(
              addressLine1: '123 TEST STREET',
              addressLine2: '',
              addressTown: 'LONDON',
              addressCounty: 'GREATER LONDON',
              postcode: 'SW1A 1AA', // Valid postcode
              countryCode: 'GB',
              residentFromGyearMonth: '2020-01',
            ),
            previousAddresses: [],
            additionalApplicantDetails: AdditionalApplicantDetailsData(
              birthSurname: '',
              birthSurnameUntil: '',
              otherSurnames: [],
              otherForenames: [],
              birthTown: 'LONDON',
              birthCounty: 'GREATER LONDON',
              birthCountry: 'GB',
              birthNationality: 'BRITISH',
              unspentConvictions: 'n',
              declarationByApplicant: 'n',
              languagePreference: 'english',
            ),
            applicantIdentityDetails: ApplicantIdentityDetailsData(
              identityVerified: 'y',
              evidenceCheckedBy: 'SYSTEM',
              nationalInsuranceNumber: '*********',
            ),
          ),
        );

        final errors = DBSDataMapper.validateForSubmission(formData);

        expect(errors, isNot(contains('Postcode is required for UK addresses')));
      });

      test('should not require postcode for non-GB addresses', () {
        final formData = DBSFormData(
          applicantDetails: ApplicantDetailsData(
            title: 'MR',
            forename: 'JOHN',
            middlenames: [],
            presentSurname: 'DOE',
            dateOfBirth: '1990-01-01',
            gender: 'male',
            niNumber: '*********',
            email: '<EMAIL>',
            contactNumber: '01234567890',
            currentAddress: CurrentAddressData(
              addressLine1: '123 TEST STREET',
              addressLine2: '',
              addressTown: 'PARIS',
              addressCounty: '',
              postcode: '', // No postcode for non-GB
              countryCode: 'FR',
              residentFromGyearMonth: '2020-01',
            ),
            previousAddresses: [],
            additionalApplicantDetails: AdditionalApplicantDetailsData(
              birthSurname: '',
              birthSurnameUntil: '',
              otherSurnames: [],
              otherForenames: [],
              birthTown: 'LONDON',
              birthCounty: 'GREATER LONDON',
              birthCountry: 'GB',
              birthNationality: 'BRITISH',
              unspentConvictions: 'n',
              declarationByApplicant: 'n',
              languagePreference: 'english',
            ),
            applicantIdentityDetails: ApplicantIdentityDetailsData(
              identityVerified: 'y',
              evidenceCheckedBy: 'SYSTEM',
              nationalInsuranceNumber: '*********',
            ),
          ),
        );

        final errors = DBSDataMapper.validateForSubmission(formData);

        expect(errors, isNot(contains('Postcode is required for UK addresses')));
      });
    });

    group('mapToApiRequest', () {
      test('should format postcode correctly for API', () {
        final formData = DBSFormData(
          applicantDetails: ApplicantDetailsData(
            title: 'mr',
            forename: 'john',
            middlenames: [],
            presentSurname: 'doe',
            dateOfBirth: '1990-01-01',
            gender: 'male',
            niNumber: 'ab123456c',
            email: '<EMAIL>',
            contactNumber: '01234567890',
            currentAddress: CurrentAddressData(
              addressLine1: '123 test street',
              addressLine2: '',
              addressTown: 'london',
              addressCounty: 'greater london',
              postcode: 'sw1a 1aa', // Lowercase with space
              countryCode: 'gb',
              residentFromGyearMonth: '2020-01',
            ),
            previousAddresses: [],
            additionalApplicantDetails: AdditionalApplicantDetailsData(
              birthSurname: '',
              birthSurnameUntil: '',
              otherSurnames: [],
              otherForenames: [],
              birthTown: 'london',
              birthCounty: 'greater london',
              birthCountry: 'gb',
              birthNationality: 'british',
              unspentConvictions: 'n',
              declarationByApplicant: 'y',
              languagePreference: 'english',
            ),
            applicantIdentityDetails: ApplicantIdentityDetailsData(
              identityVerified: 'y',
              evidenceCheckedBy: 'SYSTEM',
              nationalInsuranceNumber: '*********',
            ),
          ),
        );

        final apiRequest = DBSDataMapper.mapToApiRequest(formData);
        final currentAddressJson = apiRequest.applicantDetails.currentAddress.toJson();

        expect(currentAddressJson['Address']['Postcode'], equals('SW1A1AA'));
        expect(currentAddressJson['Address']['CountryCode'], equals('GB'));
      });
    });

    group('postcode lookup scenario', () {
      test('should validate correctly when postcode is provided via lookup', () {
        // Simulate the scenario where user uses postcode lookup
        final formData = DBSFormData(
          applicantDetails: ApplicantDetailsData(
            title: 'MR',
            forename: 'JOHN',
            middlenames: [],
            presentSurname: 'DOE',
            dateOfBirth: '1990-01-01',
            gender: 'male',
            niNumber: '*********',
            email: '<EMAIL>',
            contactNumber: '01234567890',
            currentAddress: CurrentAddressData(
              addressLine1: '123 TEST STREET',
              addressLine2: '',
              addressTown: 'LONDON',
              addressCounty: 'GREATER LONDON',
              postcode: 'SW1A1AA', // Postcode from lookup (no space)
              countryCode: 'GB',
              residentFromGyearMonth: '2020-01',
            ),
            previousAddresses: [],
            additionalApplicantDetails: AdditionalApplicantDetailsData(
              birthSurname: '',
              birthSurnameUntil: '',
              otherSurnames: [],
              otherForenames: [],
              birthTown: 'LONDON',
              birthCounty: 'GREATER LONDON',
              birthCountry: 'GB',
              birthNationality: 'BRITISH',
              unspentConvictions: 'n',
              declarationByApplicant: 'n',
              languagePreference: 'english',
            ),
            applicantIdentityDetails: ApplicantIdentityDetailsData(
              identityVerified: 'y',
              evidenceCheckedBy: 'SYSTEM',
              nationalInsuranceNumber: '*********',
            ),
          ),
        );

        final errors = DBSDataMapper.validateForSubmission(formData);

        // Should not have postcode error
        expect(errors, isNot(contains('Postcode is required for UK addresses')));

        // Should pass validation for postcode
        final hasPostcodeError = errors.any((error) => error.toLowerCase().contains('postcode'));
        expect(hasPostcodeError, isFalse);
      });

      test('should format postcode correctly when mapping to API', () {
        final formData = DBSFormData(
          applicantDetails: ApplicantDetailsData(
            title: 'mr',
            forename: 'john',
            middlenames: [],
            presentSurname: 'doe',
            dateOfBirth: '1990-01-01',
            gender: 'male',
            niNumber: 'ab123456c',
            email: '<EMAIL>',
            contactNumber: '01234567890',
            currentAddress: CurrentAddressData(
              addressLine1: '123 test street',
              addressLine2: '',
              addressTown: 'london',
              addressCounty: 'greater london',
              postcode: 'SW1A 1AA', // With space
              countryCode: 'gb',
              residentFromGyearMonth: '2020-01',
            ),
            previousAddresses: [],
            additionalApplicantDetails: AdditionalApplicantDetailsData(
              birthSurname: '',
              birthSurnameUntil: '',
              otherSurnames: [],
              otherForenames: [],
              birthTown: 'london',
              birthCounty: 'greater london',
              birthCountry: 'gb',
              birthNationality: 'british',
              unspentConvictions: 'n',
              declarationByApplicant: 'y',
              languagePreference: 'english',
            ),
            applicantIdentityDetails: ApplicantIdentityDetailsData(
              identityVerified: 'y',
              evidenceCheckedBy: 'SYSTEM',
              nationalInsuranceNumber: '*********',
            ),
          ),
        );

        final apiRequest = DBSDataMapper.mapToApiRequest(formData);
        final currentAddressJson = apiRequest.applicantDetails.currentAddress.toJson();

        // Should format postcode correctly (uppercase, no spaces)
        expect(currentAddressJson['Address']['Postcode'], equals('SW1A1AA'));
        expect(currentAddressJson['Address']['CountryCode'], equals('GB'));
        expect(currentAddressJson['Address']['AddressLine1'], equals('123 TEST STREET'));
        expect(currentAddressJson['Address']['AddressTown'], equals('LONDON'));
      });
    });
  });

  group('mapToSaveDataRequest', () {
    test('should map form data to flat save-data API format', () {
      final formData = DBSFormData(
        applicantDetails: ApplicantDetailsData(
          title: 'MR',
          forename: 'JOHN',
          middlenames: [],
          presentSurname: 'SMITH',
          dateOfBirth: '1990-01-01',
          gender: 'male',
          niNumber: '*********',
          email: '<EMAIL>',
          contactNumber: '07123456789',
          currentAddress: CurrentAddressData(
            addressLine1: '123 MAIN STREET',
            addressLine2: 'APARTMENT 4B',
            addressTown: 'LONDON',
            addressCounty: 'GREATER LONDON',
            postcode: 'SW1A 1AA',
            countryCode: 'GB',
            residentFromGyearMonth: '2020-01',
          ),
          previousAddresses: [
            PreviousAddressData(
              addressLine1: '456 OLD STREET',
              addressLine2: 'FLAT 2',
              addressTown: 'MANCHESTER',
              addressCounty: 'GREATER MANCHESTER',
              postcode: 'M1 1AA',
              countryCode: 'GB',
              residentFromGyearMonth: '2018-01',
              residentToGyearMonth: '2019-12',
            ),
          ],
          additionalApplicantDetails: AdditionalApplicantDetailsData(
            birthSurname: 'JONES',
            birthSurnameUntil: '2010',
            otherSurnames: [],
            otherForenames: [],
            birthTown: 'BIRMINGHAM',
            birthCounty: 'WEST MIDLANDS',
            birthCountry: 'GB',
            birthNationality: 'BRITISH',
            unspentConvictions: 'n',
            declarationByApplicant: 'y',
            languagePreference: 'english',
          ),
          applicantIdentityDetails: ApplicantIdentityDetailsData(
            identityVerified: 'y',
            evidenceCheckedBy: 'SYSTEM',
            nationalInsuranceNumber: '*********',
          ),
        ),
      );

      final result = DBSDataMapper.mapToSaveDataRequest(
        applicationId: 123,
        formData: formData,
        unspentConvictions: 'n',
        declarationByApplicant: 'y',
        languagePreference: 'english',
        identityVerified: 'y',
        evidenceCheckedBy: 'SYSTEM',
      );

      expect(result['application_id'], equals(123));
      expect(result['form_data'], isA<Map<String, String>>());

      final flatData = result['form_data'] as Map<String, String>;

      // Test basic applicant details
      expect(flatData['ApplicantDetails::TITLE'], equals('MR'));
      expect(flatData['ApplicantDetails::FORENAME'], equals('JOHN'));
      expect(flatData['ApplicantDetails::PRESENT_SURNAME'], equals('SMITH'));
      expect(flatData['ApplicantDetails::DATE_OF_BIRTH'], equals('1990-01-01'));
      expect(flatData['ApplicantDetails::GENDER'], equals('male'));
      expect(flatData['ApplicantDetails::NI_NUMBER'], equals('*********'));

      // Test current address
      expect(flatData['CurrentAddress::ADDRESS_LINE1'], equals('123 MAIN STREET'));
      expect(flatData['CurrentAddress::ADDRESS_LINE2'], equals('APARTMENT 4B'));
      expect(flatData['CurrentAddress::ADDRESS_TOWN'], equals('LONDON'));
      expect(flatData['CurrentAddress::ADDRESS_COUNTY'], equals('GREATER LONDON'));
      expect(flatData['CurrentAddress::POSTCODE'], equals('SW1A 1AA'));
      expect(flatData['CurrentAddress::COUNTRY_CODE'], equals('GB'));
      expect(flatData['CurrentAddress::RESIDENT_FROM_YEAR_MONTH'], equals('2020-01'));

      // Test previous address
      expect(flatData['PreviousAddress::0::Address::AddressLine1'], equals('456 OLD STREET'));
      expect(flatData['PreviousAddress::0::Address::AddressLine2'], equals('FLAT 2'));
      expect(flatData['PreviousAddress::0::Address::AddressTown'], equals('MANCHESTER'));
      expect(flatData['PreviousAddress::0::Address::AddressCounty'], equals('GREATER MANCHESTER'));
      expect(flatData['PreviousAddress::0::Address::Postcode'], equals('M1 1AA'));
      expect(flatData['PreviousAddress::0::Address::CountryCode'], equals('GB'));
      expect(flatData['PreviousAddress::0::ResidentDates::ResidentFromGyearMonth'], equals('2018-01'));
      expect(flatData['PreviousAddress::0::ResidentDates::ResidentToGyearMonth'], equals('2019-12'));

      // Test additional details
      expect(flatData['AdditionalApplicantDetails::BIRTH_SURNAME'], equals('JONES'));
      expect(flatData['AdditionalApplicantDetails::BIRTH_SURNAME_UNTIL'], equals('2010'));
      expect(flatData['AdditionalApplicantDetails::BIRTH_TOWN'], equals('BIRMINGHAM'));
      expect(flatData['AdditionalApplicantDetails::BIRTH_COUNTY'], equals('WEST MIDLANDS'));
      expect(flatData['AdditionalApplicantDetails::BIRTH_COUNTRY'], equals('GB'));
      expect(flatData['AdditionalApplicantDetails::BIRTH_NATIONALITY'], equals('BRITISH'));
      expect(flatData['AdditionalApplicantDetails::CONTACT_NUMBER'], equals('07123456789'));
      expect(flatData['AdditionalApplicantDetails::UNSPENT_CONVICTIONS'], equals('n'));
      expect(flatData['AdditionalApplicantDetails::DECLARATION_BY_APPLICANT'], equals('y'));
      expect(flatData['AdditionalApplicantDetails::LANGUAGE_PREFERENCE'], equals('english'));

      // Test identity details
      expect(flatData['ApplicantIdentityDetails::IDENTITY_VERIFIED'], equals('y'));
      expect(flatData['ApplicantIdentityDetails::EVIDENCE_CHECKED_BY'], equals('SYSTEM'));
    });

    test('should handle multiple previous addresses correctly', () {
      final formData = DBSFormData(
        applicantDetails: ApplicantDetailsData(
          title: 'MR',
          forename: 'JOHN',
          middlenames: [],
          presentSurname: 'SMITH',
          dateOfBirth: '1990-01-01',
          gender: 'male',
          niNumber: '*********',
          email: '<EMAIL>',
          contactNumber: '07123456789',
          currentAddress: CurrentAddressData.empty(),
          previousAddresses: [
            PreviousAddressData(
              addressLine1: 'FIRST ADDRESS',
              addressLine2: '',
              addressTown: 'LONDON',
              addressCounty: 'GREATER LONDON',
              postcode: 'SW1A 1AA',
              countryCode: 'GB',
              residentFromGyearMonth: '2020-01',
              residentToGyearMonth: '2021-12',
            ),
            PreviousAddressData(
              addressLine1: 'SECOND ADDRESS',
              addressLine2: 'FLAT 5',
              addressTown: 'MANCHESTER',
              addressCounty: 'GREATER MANCHESTER',
              postcode: 'M1 1AA',
              countryCode: 'GB',
              residentFromGyearMonth: '2018-01',
              residentToGyearMonth: '2019-12',
            ),
          ],
          additionalApplicantDetails: AdditionalApplicantDetailsData.empty(),
          applicantIdentityDetails: ApplicantIdentityDetailsData.empty(),
        ),
      );

      final result = DBSDataMapper.mapToSaveDataRequest(
        applicationId: 456,
        formData: formData,
      );

      final flatData = result['form_data'] as Map<String, String>;

      // Test first previous address
      expect(flatData['PreviousAddress::0::Address::AddressLine1'], equals('FIRST ADDRESS'));
      expect(flatData['PreviousAddress::0::Address::AddressLine2'], equals(''));
      expect(flatData['PreviousAddress::0::ResidentDates::ResidentFromGyearMonth'], equals('2020-01'));
      expect(flatData['PreviousAddress::0::ResidentDates::ResidentToGyearMonth'], equals('2021-12'));

      // Test second previous address
      expect(flatData['PreviousAddress::1::Address::AddressLine1'], equals('SECOND ADDRESS'));
      expect(flatData['PreviousAddress::1::Address::AddressLine2'], equals('FLAT 5'));
      expect(flatData['PreviousAddress::1::ResidentDates::ResidentFromGyearMonth'], equals('2018-01'));
      expect(flatData['PreviousAddress::1::ResidentDates::ResidentToGyearMonth'], equals('2019-12'));
    });

    test('should handle empty/null values correctly', () {
      final formData = DBSFormData(
        applicantDetails: ApplicantDetailsData(
          title: 'MR',
          forename: 'JOHN',
          middlenames: [],
          presentSurname: 'SMITH',
          dateOfBirth: '1990-01-01',
          gender: 'male',
          niNumber: '', // Empty string
          email: '<EMAIL>',
          contactNumber: '07123456789',
          currentAddress: CurrentAddressData(
            addressLine1: '123 MAIN STREET',
            addressLine2: '', // Empty string
            addressTown: 'LONDON',
            addressCounty: 'GREATER LONDON',
            postcode: '', // Empty string - this was causing the error
            countryCode: 'GB',
            residentFromGyearMonth: '2020-01',
          ),
          previousAddresses: [],
          additionalApplicantDetails: AdditionalApplicantDetailsData(
            birthSurname: 'JONES',
            birthSurnameUntil: '', // Empty string - this was causing the error
            otherSurnames: [],
            otherForenames: [],
            birthTown: 'BIRMINGHAM',
            birthCounty: 'WEST MIDLANDS',
            birthCountry: 'GB',
            birthNationality: 'BRITISH',
            unspentConvictions: 'n',
            declarationByApplicant: 'y',
            languagePreference: 'english',
          ),
          applicantIdentityDetails: ApplicantIdentityDetailsData(
            identityVerified: 'y',
            evidenceCheckedBy: 'SYSTEM',
            nationalInsuranceNumber: '',
          ),
        ),
      );

      final result = DBSDataMapper.mapToSaveDataRequest(
        applicationId: 123,
        formData: formData,
      );

      final flatData = result['form_data'] as Map<String, String>;

      // Verify that empty fields are converted to empty strings, not null
      expect(flatData['ApplicantDetails::NI_NUMBER'], equals(''));
      expect(flatData['CurrentAddress::ADDRESS_LINE2'], equals(''));
      expect(flatData['CurrentAddress::POSTCODE'], equals(''));
      expect(flatData['AdditionalApplicantDetails::BIRTH_SURNAME_UNTIL'], equals(''));

      // Verify all values are strings
      flatData.forEach((key, value) {
        expect(value, isA<String>(), reason: 'Field $key should be a string but was ${value.runtimeType}');
      });
    });
  });
}
