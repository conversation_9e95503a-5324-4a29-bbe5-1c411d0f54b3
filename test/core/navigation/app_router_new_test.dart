import 'package:SolidCheck/core/navigation/app_router_new.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_nomination.dart';
import 'package:SolidCheck/features/document_nomination/data/models/document_type.dart';
import 'package:SolidCheck/features/document_nomination/presentation/screens/document_detail_screen.dart';
import 'package:SolidCheck/features/document_nomination/presentation/screens/document_nomination_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('AppRouter', () {
    late DocumentType testDocumentType;
    late DocumentNomination testNomination;

    setUp(() {
      testDocumentType = DocumentType(
        key: 'passport_any',
        name: 'Passport (Any Country)',
        requiresPhoto: true,
        confirmsAddress: false,
        applicableCountries: ['ANY'],
        dataFields: [
          DocumentDataField(
            name: 'passport_number',
            type: 'string',
            required: true,
            label: 'Passport Number',
          ),
        ],
      );

      testNomination = DocumentNomination(
        documentTypeId: testDocumentType.id,
        documentData: {'passport_number': '*********'},
        confirmsAddress: false,
      );
    });

    group('generateRoute', () {
      testWidgets('should generate document nomination route correctly', (WidgetTester tester) async {
        settings = const RouteSettings(
          name: AppRouter.documentNomination,
          arguments: {
            'applicationId': 'test-app-id',
            'applicantName': 'Test Applicant',
          },
        );

        final route = AppRouter.generateRoute(settings);

        expect(route, isA<MaterialPageRoute>());
        expect(route!.settings, equals(settings));

        // Build the route to verify it creates the correct widget
        final widget = route.builder(MockBuildContext());
        expect(widget, isA<DocumentNominationScreen>());

        final screen = widget as DocumentNominationScreen;
        expect(screen.applicationId, equals('test-app-id'));
        expect(screen.applicantName, equals('Test Applicant'));
      });

      testWidgets('should generate document detail route correctly', (WidgetTester tester) async {
        final settings = RouteSettings(
          name: AppRouter.documentDetail,
          arguments: {
            'applicationId': 'test-app-id',
            'applicantName': 'Test Applicant',
            'documentType': testDocumentType,
            'existingNomination': testNomination,
          },
        );

        final route = AppRouter.generateRoute(settings);

        expect(route, isA<MaterialPageRoute>());
        expect(route!.settings, equals(settings));

        // Build the route to verify it creates the correct widget
        final widget = route.builder(MockBuildContext());
        expect(widget, isA<DocumentDetailScreen>());

        final screen = widget as DocumentDetailScreen;
        expect(screen.applicationId, equals('test-app-id'));
        expect(screen.applicantName, equals('Test Applicant'));
        expect(screen.documentType, equals(testDocumentType));
        expect(screen.existingNomination, equals(testNomination));
      });

      testWidgets('should generate document detail route without existing nomination', (WidgetTester tester) async {
        final settings = RouteSettings(
          name: AppRouter.documentDetail,
          arguments: {
            'applicationId': 'test-app-id',
            'applicantName': 'Test Applicant',
            'documentType': testDocumentType,
          },
        );

        final route = AppRouter.generateRoute(settings);

        expect(route, isA<MaterialPageRoute>());
        expect(route!.settings, equals(settings));

        final widget = route.builder(MockBuildContext());
        expect(widget, isA<DocumentDetailScreen>());

        final screen = widget as DocumentDetailScreen;
        expect(screen.applicationId, equals('test-app-id'));
        expect(screen.applicantName, equals('Test Applicant'));
        expect(screen.documentType, equals(testDocumentType));
        expect(screen.existingNomination, isNull);
      });

      testWidgets('should handle document detail route with missing document type', (WidgetTester tester) async {
        settings = const RouteSettings(
          name: AppRouter.documentDetail,
          arguments: {
            'applicationId': 'test-app-id',
            'applicantName': 'Test Applicant',
          },
        );

        final route = AppRouter.generateRoute(settings);

        expect(route, isA<MaterialPageRoute>());
        expect(route!.settings, equals(settings));

        final widget = route.builder(MockBuildContext());
        expect(widget, isA<Scaffold>());

        // Verify error message is shown
        final scaffold = widget as Scaffold;
        final body = scaffold.body as Center;
        final text = body.child as Text;
        expect(text.data, equals('Invalid document type'));
      });

      testWidgets('should handle document nomination route with missing arguments', (WidgetTester tester) async {
        settings = const RouteSettings(
          name: AppRouter.documentNomination,
        );

        final route = AppRouter.generateRoute(settings);

        expect(route, isA<MaterialPageRoute>());
        expect(route!.settings, equals(settings));

        final widget = route.builder(MockBuildContext());
        expect(widget, isA<DocumentNominationScreen>());

        final screen = widget as DocumentNominationScreen;
        expect(screen.applicationId, equals(''));
        expect(screen.applicantName, equals(''));
      });

      testWidgets('should handle document detail route with missing arguments', (WidgetTester tester) async {
        settings = const RouteSettings(
          name: AppRouter.documentDetail,
        );

        final route = AppRouter.generateRoute(settings);

        expect(route, isA<MaterialPageRoute>());

        final widget = route.builder(MockBuildContext());
        expect(widget, isA<Scaffold>());

        // Verify error message is shown for missing document type
        final scaffold = widget as Scaffold;
        final body = scaffold.body as Center;
        final text = body.child as Text;
        expect(text.data, equals('Invalid document type'));
      });

      testWidgets('should return default route for unknown route name', (WidgetTester tester) async {
        settings = const RouteSettings(
          name: '/unknown-route',
        );

        final route = AppRouter.generateRoute(settings);

        expect(route, isA<MaterialPageRoute>());
        expect(route!.settings, equals(settings));

        // Should return LoginScreen as default
        final widget = route.builder(MockBuildContext());
        expect(widget.runtimeType.toString(), contains('LoginScreen'));
      });
    });

    group('Navigation Methods', () {
      testWidgets('should have correct route constants', (WidgetTester tester) async {
        expect(AppRouter.documentNomination, equals('/document-nomination'));
        expect(AppRouter.documentDetail, equals('/document-detail'));
        expect(AppRouter.login, equals('/login'));
        expect(AppRouter.dashboard, equals('/dashboard'));
      });

      test('should have navigateToDocumentDetail method with correct signature', () {
        // This test verifies that the method exists and has the correct signature
        expect(AppRouter.navigateToDocumentDetail, isA<Function>());
      });

      test('should have navigateToDocumentNomination method with correct signature', () {
        // This test verifies that the method exists and has the correct signature
        expect(AppRouter.navigateToDocumentNomination, isA<Function>());
      });
    });
  });
}

// Mock BuildContext for testing
class MockBuildContext implements BuildContext {
  @override
  bool get debugDoingBuild => false;

  @override
  InheritedWidget? dependOnInheritedElement(InheritedElement ancestor, {Object? aspect}) => null;

  @override
  T? dependOnInheritedWidgetOfExactType<T extends InheritedWidget>({Object? aspect}) => null;

  @override
  DiagnosticsNode describeElement(String name, {DiagnosticsTreeStyle style = DiagnosticsTreeStyle.errorProperty}) {
    throw UnimplementedError();
  }

  @override
  List<DiagnosticsNode> describeMissingAncestor({required Type expectedAncestorType}) {
    throw UnimplementedError();
  }

  @override
  DiagnosticsNode describeOwnershipChain(String name) {
    throw UnimplementedError();
  }

  @override
  DiagnosticsNode describeWidget(String name, {DiagnosticsTreeStyle style = DiagnosticsTreeStyle.errorProperty}) {
    throw UnimplementedError();
  }

  @override
  void dispatchNotification(Notification notification) {}

  @override
  T? findAncestorRenderObjectOfType<T extends RenderObject>() => null;

  @override
  T? findAncestorStateOfType<T extends State<StatefulWidget>>() => null;

  @override
  T? findAncestorWidgetOfExactType<T extends Widget>() => null;

  @override
  RenderObject? findRenderObject() => null;

  @override
  T? findRootAncestorStateOfType<T extends State<StatefulWidget>>() => null;

  @override
  InheritedElement? getElementForInheritedWidgetOfExactType<T extends InheritedWidget>() => null;

  @override
  BuildOwner? get owner => null;

  @override
  Size? get size => null;

  @override
  void visitAncestorElements(bool Function(Element element) visitor) {}

  @override
  void visitChildElements(ElementVisitor visitor) {}

  @override
  Widget get widget => throw UnimplementedError();

  @override
  bool get mounted => true;
}
