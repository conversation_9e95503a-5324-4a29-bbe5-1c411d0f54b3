{"openapi": "3.1.0", "info": {"title": "API Interface Documentation", "version": "1.0.0", "description": "API interface for Flutter application to manage entities, applications, and user authentication for portal users."}, "servers": [{"url": "http://localhost:8001/api"}], "security": [{"http": []}], "paths": {"/v1/auth/login": {"post": {"operationId": "auth.login", "description": "Authenticate user with email and password to receive bearer token", "summary": "User login", "tags": ["<PERSON><PERSON>"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"user": {"type": "object", "properties": {"id": {"type": "string"}, "email": {"type": "string"}, "user_type": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "phone": {"type": "string"}, "address": {"type": "string"}, "two_factor_enabled": {"type": "string"}, "two_factor_secret": {"type": "string"}}, "required": ["id", "email", "user_type", "first_name", "last_name", "phone", "address", "two_factor_enabled", "two_factor_secret"]}, "entities": {"type": "array", "items": {}}, "token": {"type": "string"}, "token_type": {"type": "string", "example": "Bearer"}}, "required": ["user", "entities", "token", "token_type"]}, "message": {"type": "string", "example": "User logged in successfully"}}, "required": ["success", "data", "message"]}}}}, "403": {"$ref": "#/components/responses/AuthorizationException"}, "401": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Invalid credentials"}}, "required": ["success", "message"]}}}}, "422": {"$ref": "#/components/responses/ValidationException"}}}}, "/v1/auth/logout": {"post": {"operationId": "auth.logout", "description": "Logout the authenticated user and invalidate the current access token", "summary": "User logout", "tags": ["<PERSON><PERSON>"], "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"type": "string"}, "minItems": 0, "maxItems": 0, "additionalItems": false}, "message": {"type": "string", "example": "User logged out successfully"}}, "required": ["success", "data", "message"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/v1/auth/user": {"get": {"operationId": "auth.user", "description": "Retrieve the authenticated user's information and associated entities", "summary": "Get authenticated user", "tags": ["<PERSON><PERSON>"], "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"user": {"type": "object", "properties": {"id": {"type": "string"}, "email": {"type": "string"}, "user_type": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "phone": {"type": "string"}, "address": {"type": "string"}, "two_factor_enabled": {"type": "string"}, "two_factor_secret": {"type": "string"}}, "required": ["id", "email", "user_type", "first_name", "last_name", "phone", "address", "two_factor_enabled", "two_factor_secret"]}, "entities": {"type": "array", "items": {}}}, "required": ["user", "entities"]}, "message": {"type": "string", "example": "User retrieved successfully"}}, "required": ["success", "data", "message"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/v1/job-roles": {"get": {"operationId": "jobRole.getJobRoles", "description": "Retrieve all job roles for table display based on user's entity access", "summary": "Get job roles", "tags": ["JobRole"], "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"anyOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}, "minItems": 0, "maxItems": 0, "additionalItems": false}]}, "message": {"type": "string", "example": "Job roles retrieved successfully"}}, "required": ["success", "data", "message"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/v1/job-roles/{jobRole}": {"put": {"operationId": "jobRole.update", "description": "Update a job role (client users only)", "summary": "Update job role", "tags": ["JobRole"], "parameters": [{"name": "jobRole", "in": "path", "required": true, "description": "The job role ID", "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"job_label": {"type": "string", "maxLength": 255}, "job_title": {"type": "string", "maxLength": 255}, "job_workforce": {"type": "string", "maxLength": 255}, "role_description": {"type": ["string", "null"]}, "self_payment": {"type": ["boolean", "null"]}, "employment_sector": {"type": ["string", "null"], "maxLength": 255}, "product_ids": {"type": ["array", "null"], "items": {"type": "integer"}}}}}}}, "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"id": {"type": "string"}, "job_label": {"type": "string"}, "job_title": {"type": "string"}, "job_workforce": {"type": "string"}, "role_description": {"type": "string"}, "self_payment": {"type": "string"}, "employment_sector": {"type": "string"}, "entity_name": {"type": "string"}, "entity_code": {"type": "string"}, "products": {"anyOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}, "minItems": 0, "maxItems": 0, "additionalItems": false}]}}, "required": ["id", "job_label", "job_title", "job_workforce", "role_description", "self_payment", "employment_sector", "entity_name", "entity_code", "products"]}, "message": {"type": "string", "example": "Job role updated successfully"}}, "required": ["success", "data", "message"]}}}}, "403": {"description": "", "content": {"application/json": {"schema": {"anyOf": [{"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "You do not have access to this job role"}}, "required": ["success", "message"]}, {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Only client users can update job roles"}}, "required": ["success", "message"]}]}}}}, "422": {"$ref": "#/components/responses/ValidationException"}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}, "delete": {"operationId": "jobRole.destroy", "description": "Delete a job role (client users only)", "summary": "Delete job role", "tags": ["JobRole"], "parameters": [{"name": "jobRole", "in": "path", "required": true, "description": "The job role ID", "schema": {"type": "integer"}}], "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "array", "items": {"type": "string"}, "minItems": 0, "maxItems": 0, "additionalItems": false}, "message": {"type": "string", "example": "Job role deleted successfully"}}, "required": ["success", "data", "message"]}}}}, "403": {"description": "", "content": {"application/json": {"schema": {"anyOf": [{"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "You do not have access to this job role"}}, "required": ["success", "message"]}, {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "example": "Only client users can delete job roles"}}, "required": ["success", "message"]}]}}}}, "404": {"$ref": "#/components/responses/ModelNotFoundException"}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}, "/v1/job-role-applications": {"get": {"operationId": "jobRole.jobRoleApplications", "description": "Retrieve job roles with pricing information for application selection", "summary": "Get job roles for applications", "tags": ["JobRole"], "responses": {"500": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"anyOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}, "minItems": 0, "maxItems": 0, "additionalItems": false}]}, "message": {"type": "string", "example": "Job roles for applications retrieved successfully"}}, "required": ["success", "data", "message"]}}}}, "401": {"$ref": "#/components/responses/AuthenticationException"}}}}}, "components": {"securitySchemes": {"http": {"type": "http", "description": "Laravel Sanctum Bearer Token Authentication. Use the token received from the login endpoint.", "scheme": "bearer", "bearerFormat": "sanctum"}}, "schemas": {"LoginRequest": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}, "password": {"type": "string"}}, "required": ["email", "password"], "title": "LoginRequest"}}, "responses": {"ValidationException": {"description": "Validation error", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "Errors overview."}, "errors": {"type": "object", "description": "A detailed description of each field that failed validation.", "additionalProperties": {"type": "array", "items": {"type": "string"}}}}, "required": ["message", "errors"]}}}}, "AuthorizationException": {"description": "Authorization error", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "Error overview."}}, "required": ["message"]}}}}, "AuthenticationException": {"description": "Unauthenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "Error overview."}}, "required": ["message"]}}}}, "ModelNotFoundException": {"description": "Not found", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "Error overview."}}, "required": ["message"]}}}}}}}