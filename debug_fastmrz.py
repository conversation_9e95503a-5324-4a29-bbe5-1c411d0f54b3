#!/usr/bin/env python3
"""
Debug script to test FastMRZ engine directly
"""

import sys
import os
import numpy as np
import cv2
from pathlib import Path

# Add the SolidTech directory to Python path
sys.path.insert(0, '/home/<USER>/Work/SolidTech')

from engines.mrz.fast_mrz_engine import FastMRZEngine
from app.models.document_models import DocumentType

def test_fastmrz_engine():
    """Test FastMRZ engine directly"""
    
    print("🧪 Testing FastMRZ Engine Directly")
    print("=" * 50)
    
    # Initialize FastMRZ engine
    try:
        engine = FastMRZEngine()
        print("✅ FastMRZ engine initialized")
    except Exception as e:
        print(f"❌ Failed to initialize FastMRZ engine: {e}")
        return
    
    # Create a test image (simple test)
    test_image = np.zeros((400, 600, 3), dtype=np.uint8)
    test_image.fill(255)  # White background
    
    # Add some test MRZ-like text
    cv2.putText(test_image, "P<GBRUK<SPECIMEN<<ANGELA<ZOE<<<<<<<<<<<<<<<<", 
                (10, 350), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
    cv2.putText(test_image, "537530179ZGBR8812049F2608189<<<<<<<<<<<<<08", 
                (10, 380), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
    
    print("📝 Created test image with MRZ text")
    
    # Test text extraction
    try:
        print("\n🔍 Testing text extraction...")
        mrz_text = engine.extract_text(test_image)
        print(f"Extracted text: {repr(mrz_text)}")
        
        if not mrz_text:
            print("⚠️ No text extracted - checking OCR setup...")
            
            # Test tesseract directly
            import pytesseract
            try:
                version = pytesseract.get_tesseract_version()
                print(f"✅ Tesseract version: {version}")
                
                # Test basic OCR
                basic_text = pytesseract.image_to_string(test_image)
                print(f"Basic OCR result: {repr(basic_text[:100])}")
                
            except Exception as e:
                print(f"❌ Tesseract error: {e}")
        
    except Exception as e:
        print(f"❌ Text extraction failed: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # Test field extraction
    try:
        print("\n📋 Testing field extraction...")
        fields = engine.extract_fields(test_image, DocumentType.PASSPORT)
        
        if fields:
            print(f"✅ Extracted {len(fields)} fields:")
            for field_name, field in fields.items():
                print(f"  {field_name}: {field.value} (confidence: {field.confidence})")
        else:
            print("❌ No fields extracted")
            
    except Exception as e:
        print(f"❌ Field extraction failed: {e}")
        import traceback
        traceback.print_exc()
    
    # Test engine info
    try:
        print("\n🔧 Engine information:")
        info = engine.get_engine_info()
        for key, value in info.items():
            print(f"  {key}: {value}")
    except Exception as e:
        print(f"❌ Failed to get engine info: {e}")

def check_dependencies():
    """Check if all dependencies are available"""
    print("🔍 Checking dependencies...")
    
    # Check OpenCV
    try:
        import cv2
        print(f"✅ OpenCV version: {cv2.__version__}")
    except ImportError:
        print("❌ OpenCV not available")
    
    # Check Tesseract
    try:
        import pytesseract
        version = pytesseract.get_tesseract_version()
        print(f"✅ Tesseract version: {version}")
        
        # Check available languages
        langs = pytesseract.get_languages()
        print(f"✅ Available languages: {langs}")
        
        if 'mrz' in langs:
            print("✅ MRZ language pack available")
        else:
            print("⚠️ MRZ language pack not available")
            
    except Exception as e:
        print(f"❌ Tesseract error: {e}")
    
    # Check ONNX model
    model_path = "/home/<USER>/Work/SolidTech/engines/mrz/models/mrz_seg.onnx"
    if Path(model_path).exists():
        print(f"✅ ONNX model found: {model_path}")
    else:
        print(f"❌ ONNX model not found: {model_path}")
    
    # Check numpy
    try:
        import numpy as np
        print(f"✅ NumPy version: {np.__version__}")
    except ImportError:
        print("❌ NumPy not available")

if __name__ == "__main__":
    print("🔧 FastMRZ Debug Tool")
    print("=" * 30)
    
    check_dependencies()
    print()
    test_fastmrz_engine()
