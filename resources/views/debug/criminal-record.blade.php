<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - Criminal Record Process Stamps</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @livewireStyles
</head>
<body class="bg-gray-100">
    <div class="container mx-auto py-8">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold mb-6">Debug: Criminal Record Process Stamps</h1>
            <p class="text-gray-600 mb-4">Applicant ID: {{ $applicantId }}</p>
            
            <!-- DBS Criminal Record Component -->
            @livewire('dbs-applicant-criminal-record', ['applicantId' => $applicantId])
        </div>
    </div>
    
    @livewireScripts
</body>
</html>
