<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ config('app.name') }} - API Tester</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-800 mb-8">{{ config('app.name') }} - API Tester</h1>

            <!-- Navigation -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <div class="flex flex-wrap gap-4 mb-4">
                    <a href="/docs/api" target="_blank" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors">
                        📚 API Documentation
                    </a>
                    <a href="/telescope" target="_blank" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-md transition-colors">
                        🔭 Telescope (Debug)
                    </a>
                    <button onclick="clearAll()" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md transition-colors">
                        🗑️ Clear All
                    </button>
                </div>

                <!-- Token Storage -->
                <div class="border-t pt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Bearer Token:</label>
                    <input type="text" id="bearerToken" placeholder="Enter your bearer token here..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <p class="text-sm text-gray-500 mt-1">Get token from login endpoint and paste here for authenticated requests</p>
                </div>
            </div>

            <!-- Authentication Section -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">🔐 Authentication</h2>

                <!-- Login -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-700 mb-3">Login with Existing Portal User</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                        <input type="email" id="loginEmail" placeholder="Email" value="<EMAIL>"
                               class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <input type="password" id="loginPassword" placeholder="Password" value="password"
                               class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <button onclick="login()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md transition-colors">
                        Login
                    </button>
                    <p class="text-sm text-gray-500 mt-2">
                        Use existing portal_users credentials. Available test users: <EMAIL>, <EMAIL>, <EMAIL>
                    </p>
                </div>

                <!-- User Info -->
                <div class="border-t pt-4">
                    <h3 class="text-lg font-medium text-gray-700 mb-3">User Information</h3>
                    <button onclick="getUserInfo()" class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-md transition-colors">
                        Get Current User
                    </button>
                    <button onclick="logout()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors ml-2">
                        Logout
                    </button>
                </div>
            </div>

            <!-- Job Roles Section -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">🏢 Job Roles & Pricing</h2>

                <!-- Get Job Roles -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-700 mb-3">Get Job Roles with Product Pricing</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                        <input type="number" id="entityId" placeholder="Entity ID" value="1"
                               class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <select id="jobRoleType" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="all">All (with inheritance)</option>
                            <option value="own">Entity's own only</option>
                        </select>
                    </div>
                    <button onclick="getJobRoles()" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-md transition-colors">
                        Get Job Roles
                    </button>
                    <p class="text-sm text-gray-500 mt-2">
                        Retrieves job roles with product pricing using entity hierarchy inheritance logic
                    </p>
                </div>

                <!-- Get Single Job Role -->
                <div class="border-t pt-4">
                    <h3 class="text-lg font-medium text-gray-700 mb-3">Get Single Job Role</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                        <input type="number" id="jobRoleId" placeholder="Job Role ID" value="1"
                               class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <button onclick="getSingleJobRole()" class="bg-teal-500 hover:bg-teal-600 text-white px-4 py-2 rounded-md transition-colors">
                        Get Job Role
                    </button>
                </div>
            </div>

            <!-- Response Section -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">📋 Response</h2>
                <div id="responseContainer" class="bg-gray-50 border rounded-md p-4 min-h-32">
                    <p class="text-gray-500">API responses will appear here...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '/api/v1';

        function getAuthHeaders() {
            const token = document.getElementById('bearerToken').value;
            return token ? { 'Authorization': `Bearer ${token}` } : {};
        }

        function displayResponse(response, isError = false) {
            const container = document.getElementById('responseContainer');
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = isError ? 'text-red-600' : 'text-green-600';

            container.innerHTML = `
                <div class="mb-2">
                    <span class="text-sm text-gray-500">[${timestamp}]</span>
                    <span class="${statusClass} font-medium">Status: ${response.status || 'Error'}</span>
                </div>
                <pre class="bg-white p-3 rounded border overflow-auto text-sm">${JSON.stringify(response.data || response, null, 2)}</pre>
            `;

            // Auto-fill token if login successful
            if (!isError && response.data && response.data.data && response.data.data.token) {
                document.getElementById('bearerToken').value = response.data.data.token;
            }
        }

        async function login() {
            try {
                const response = await axios.post(`${API_BASE}/auth/login`, {
                    email: document.getElementById('loginEmail').value,
                    password: document.getElementById('loginPassword').value
                });
                displayResponse(response);
            } catch (error) {
                displayResponse(error.response || error, true);
            }
        }



        async function getUserInfo() {
            try {
                const response = await axios.get(`${API_BASE}/auth/user`, {
                    headers: getAuthHeaders()
                });
                displayResponse(response);
            } catch (error) {
                displayResponse(error.response || error, true);
            }
        }

        async function logout() {
            try {
                const response = await axios.post(`${API_BASE}/auth/logout`, {}, {
                    headers: getAuthHeaders()
                });
                displayResponse(response);
                document.getElementById('bearerToken').value = '';
            } catch (error) {
                displayResponse(error.response || error, true);
            }
        }

        async function getJobRoles() {
            try {
                const entityId = document.getElementById('entityId').value;
                const jobRoleType = document.getElementById('jobRoleType').value;

                let endpoint = `${API_BASE}/entities/${entityId}/job-roles`;
                if (jobRoleType === 'own') {
                    endpoint += '/own';
                }

                const response = await axios.get(endpoint, {
                    headers: getAuthHeaders()
                });
                displayResponse(response);
            } catch (error) {
                displayResponse(error.response || error, true);
            }
        }

        async function getSingleJobRole() {
            try {
                const jobRoleId = document.getElementById('jobRoleId').value;
                const response = await axios.get(`${API_BASE}/job-roles/${jobRoleId}`, {
                    headers: getAuthHeaders()
                });
                displayResponse(response);
            } catch (error) {
                displayResponse(error.response || error, true);
            }
        }

        function clearAll() {
            document.getElementById('bearerToken').value = '';
            document.getElementById('responseContainer').innerHTML = '<p class="text-gray-500">API responses will appear here...</p>';
        }
    </script>
</body>
</html>
