"""
UK Driving License Processor - Specialized processing for UK photocard driving licenses
Optimized for DVLA format and UK-specific validation rules
"""

import numpy as np
import cv2
import re
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, timedelta

from app.core.interfaces import IDocumentProcessor
from app.models.document_models import (
    DocumentProcessingRequest,
    DocumentExtractionResult,
    DocumentType,
    ProcessingStatus,
    ExtractedField,
    ExtractionMethod,
    FraudAnalysis,
    ValidationResult,
    ProcessingMetadata
)


class UKLicenseProcessor(IDocumentProcessor):
    """Specialized processor for UK photocard driving licenses"""

    def __init__(self, ocr_engine, fraud_detector, face_processor, field_validator):
        """Initialize with required dependencies"""
        self.ocr_engine = ocr_engine
        self.fraud_detector = fraud_detector
        self.face_processor = face_processor
        self.field_validator = field_validator
        self.logger = logging.getLogger(__name__)

        # UK license validation rules
        self.validation_rules = {
            'licence_number': {
                'pattern': r'^[A-Z]{5}\d{6}[A-Z]{2}\d{2}$',
                'length': 16,
                'required': True,
                'description': 'UK license format: AAAAA999999AA99'
            },
            'first_name': {
                'min_length': 1,
                'max_length': 50,
                'pattern': r'^[A-Z\s\-\'\.]+$',
                'required': True
            },
            'last_name': {
                'min_length': 1,
                'max_length': 50,
                'pattern': r'^[A-Z\s\-\'\.]+$',
                'required': True
            },
            'date_of_birth': {
                'format': 'DD/MM/YYYY',
                'min_age': 17,  # UK minimum driving age
                'max_age': 120,
                'required': True
            },
            'issue_date': {
                'format': 'DD/MM/YYYY',
                'max_future_days': 0,  # Cannot be in future
                'required': False
            },
            'expiry_date': {
                'format': 'DD/MM/YYYY',
                'min_future_days': 1,  # Should be in future
                'required': True
            },
            'postcode': {
                'pattern': r'^[A-Z]{1,2}\d[A-Z\d]?\s?\d[A-Z]{2}$',
                'required': True,
                'description': 'UK postcode format'
            },
            'address_line_1': {
                'min_length': 1,
                'max_length': 100,
                'required': True
            },
            'address_line_2': {
                'max_length': 100,
                'required': False
            },
            'city': {
                'min_length': 1,
                'max_length': 50,
                'required': True
            }
        }

        # UK license categories
        self.license_categories = {
            'A': 'Motorcycles',
            'A1': 'Light motorcycles',
            'A2': 'Standard motorcycles',
            'B': 'Cars',
            'B1': 'Light vehicles',
            'BE': 'Car with trailer',
            'C': 'Large vehicles',
            'C1': 'Medium vehicles',
            'CE': 'Large vehicle with trailer',
            'C1E': 'Medium vehicle with trailer',
            'D': 'Buses',
            'D1': 'Minibuses',
            'DE': 'Bus with trailer',
            'D1E': 'Minibus with trailer'
        }

    def process_document(self, request: DocumentProcessingRequest) -> DocumentExtractionResult:
        """Process UK driving license document"""
        try:
            self.logger.info(f"🔄 Processing UK driving license: {request.request_id}")

            # Convert image data to numpy array
            image = self._bytes_to_image(request.image_data)
            if image is None:
                return self._create_error_result(request, ["Invalid image data"])

            # Preprocess image for better OCR
            processed_image = self._preprocess_license_image(image)

            # Extract fields using OCR
            extracted_fields = self._extract_license_fields(processed_image)

            # Enhance fields with UK-specific processing
            enhanced_fields = self._enhance_license_fields(extracted_fields, image)

            # Validate UK license fields
            validation_result = self._validate_license_fields(enhanced_fields)

            # Analyze for fraud
            fraud_analysis = self._analyze_license_fraud(image, enhanced_fields)

            # Extract face/portrait
            face_analysis = self._extract_license_face(image)

            # Calculate confidence
            confidence = self._calculate_license_confidence(enhanced_fields, fraud_analysis)

            # Generate recommendations
            recommendations = self._generate_license_recommendations(
                enhanced_fields, validation_result, fraud_analysis
            )

            # Create result
            result = DocumentExtractionResult(
                status=ProcessingStatus.SUCCESS,
                document_type=request.document_type,
                extracted_fields=enhanced_fields,
                fraud_analysis=fraud_analysis,
                face_analysis=face_analysis,
                validation_result=validation_result,
                processing_metadata=ProcessingMetadata(
                    processing_method="uk_license_specialized",
                    processing_time=0.0,
                    model_version="1.0.0",
                    request_id=request.request_id,
                    application_id=request.application_id
                ),
                recommendations=recommendations,
                confidence=confidence
            )

            self.logger.info(f"✅ UK license processed: {confidence:.2f} confidence")
            return result

        except Exception as e:
            self.logger.error(f"❌ UK license processing failed: {e}")
            return self._create_error_result(request, [str(e)])

    def validate_fields(self, fields: Dict[str, ExtractedField], document_type: DocumentType) -> ValidationResult:
        """Validate UK license fields"""
        return self._validate_license_fields(fields)

    def supports_document_type(self, document_type: DocumentType) -> bool:
        """Check if processor supports document type"""
        return document_type == DocumentType.UK_DRIVING_LICENSE

    def _extract_license_fields(self, image: np.ndarray) -> Dict[str, ExtractedField]:
        """Extract fields using OCR engine"""
        try:
            if self.ocr_engine:
                return self.ocr_engine.extract_fields(image, DocumentType.UK_DRIVING_LICENSE)
            else:
                self.logger.warning("⚠️ No OCR engine available")
                return {}
        except Exception as e:
            self.logger.error(f"❌ OCR extraction failed: {e}")
            return {}

    def _enhance_license_fields(self, fields: Dict[str, ExtractedField], image: np.ndarray) -> Dict[str, ExtractedField]:
        """Enhance extracted fields with UK-specific processing"""
        enhanced = fields.copy()

        # Validate and format license number
        if 'licence_number' in enhanced:
            license_num = enhanced['licence_number'].value.upper().replace(' ', '')
            if self._validate_uk_license_number(license_num):
                enhanced['licence_number'].value = self._format_uk_license_number(license_num)

                # Extract encoded information from license number
                encoded_info = self._decode_license_number(license_num)
                if encoded_info:
                    for key, value in encoded_info.items():
                        enhanced[f'encoded_{key}'] = ExtractedField(
                            value=value,
                            confidence=enhanced['licence_number'].confidence,
                            extraction_method=ExtractionMethod.PATTERN_MATCHING,
                            field_type='text',
                            required=False,
                            editable=False
                        )

        # Format postcode
        if 'postcode' in enhanced:
            postcode = enhanced['postcode'].value.upper().strip()
            formatted_postcode = self._format_uk_postcode(postcode)
            if formatted_postcode:
                enhanced['postcode'].value = formatted_postcode

        # Calculate age and license validity
        if 'date_of_birth' in enhanced:
            try:
                dob_str = enhanced['date_of_birth'].value
                dob = datetime.strptime(dob_str, '%d/%m/%Y')
                age = (datetime.now() - dob).days // 365
                enhanced['age'] = ExtractedField(
                    value=str(age),
                    confidence=enhanced['date_of_birth'].confidence,
                    extraction_method=ExtractionMethod.PATTERN_MATCHING,
                    field_type='number',
                    required=False,
                    editable=False
                )
            except ValueError:
                pass

        # Check license expiry
        if 'expiry_date' in enhanced:
            try:
                expiry_str = enhanced['expiry_date'].value
                expiry = datetime.strptime(expiry_str, '%d/%m/%Y')
                is_expired = expiry < datetime.now()
                days_until_expiry = (expiry - datetime.now()).days

                enhanced['is_expired'] = ExtractedField(
                    value=str(is_expired).lower(),
                    confidence=enhanced['expiry_date'].confidence,
                    extraction_method=ExtractionMethod.PATTERN_MATCHING,
                    field_type='boolean',
                    required=False,
                    editable=False
                )

                enhanced['days_until_expiry'] = ExtractedField(
                    value=str(days_until_expiry),
                    confidence=enhanced['expiry_date'].confidence,
                    extraction_method=ExtractionMethod.PATTERN_MATCHING,
                    field_type='number',
                    required=False,
                    editable=False
                )
            except ValueError:
                pass

        return enhanced

    def _validate_uk_license_number(self, license_num: str) -> bool:
        """Validate UK driving license number format"""
        pattern = r'^[A-Z]{5}\d{6}[A-Z]{2}\d{2}$'
        return bool(re.match(pattern, license_num))

    def _format_uk_license_number(self, license_num: str) -> str:
        """Format UK license number with spaces for readability"""
        if len(license_num) == 16:
            return f"{license_num[:5]} {license_num[5:11]} {license_num[11:13]} {license_num[13:]}"
        return license_num

    def _decode_license_number(self, license_num: str) -> Optional[Dict[str, str]]:
        """Decode information from UK license number"""
        if len(license_num) != 16:
            return None

        try:
            # First 5 characters: surname (encoded)
            surname_code = license_num[:5]

            # Characters 6-7: decade of birth
            decade = license_num[5:7]

            # Characters 8-9: month of birth (with gender encoding)
            month_code = int(license_num[7:9])
            month = month_code if month_code <= 12 else month_code - 50
            gender = 'M' if month_code <= 12 else 'F'

            # Characters 10-11: day of birth
            day = license_num[9:11]

            # Characters 12-13: year of birth within decade
            year_in_decade = license_num[11:13]

            # Characters 14-15: initials (encoded)
            initials_code = license_num[13:15]

            # Character 16: check digit
            check_digit = license_num[15]

            return {
                'surname_code': surname_code,
                'birth_decade': decade,
                'birth_month': str(month).zfill(2),
                'birth_day': day,
                'birth_year_in_decade': year_in_decade,
                'gender': gender,
                'initials_code': initials_code,
                'check_digit': check_digit
            }
        except (ValueError, IndexError):
            return None

    def _format_uk_postcode(self, postcode: str) -> Optional[str]:
        """Format UK postcode properly"""
        # Remove spaces and convert to uppercase
        clean = postcode.upper().replace(' ', '')

        # UK postcode patterns
        patterns = [
            r'^([A-Z]{1,2}\d[A-Z\d]?)(\d[A-Z]{2})$',  # Standard format
            r'^([A-Z]{1,2}\d)(\d[A-Z]{2})$'           # Simplified format
        ]

        for pattern in patterns:
            match = re.match(pattern, clean)
            if match:
                return f"{match.group(1)} {match.group(2)}"

        return None

    def _validate_license_fields(self, fields: Dict[str, ExtractedField]) -> ValidationResult:
        """Validate UK license fields"""
        errors = []
        warnings = []
        field_validations = {}

        for field_name, field in fields.items():
            if field_name in self.validation_rules:
                rules = self.validation_rules[field_name]
                field_errors, field_warnings = self._validate_single_field(field_name, field.value, rules)

                if field_errors:
                    errors.extend(field_errors)
                if field_warnings:
                    warnings.extend(field_warnings)

                field_validations[field_name] = {
                    'is_valid': len(field_errors) == 0,
                    'errors': field_errors,
                    'warnings': field_warnings
                }

        # Cross-field validations
        cross_validations = self._validate_license_cross_fields(fields)
        if cross_validations:
            warnings.extend(cross_validations)

        return ValidationResult(
            is_valid=len(errors) == 0,
            is_consistent=len(cross_validations) == 0,
            errors=errors,
            warnings=warnings,
            field_validations=field_validations,
            cross_field_validations=cross_validations
        )

    def _validate_single_field(self, field_name: str, value: str, rules: Dict) -> Tuple[List[str], List[str]]:
        """Validate a single field against rules"""
        errors = []
        warnings = []

        if not value and rules.get('required', False):
            errors.append(f"{field_name} is required")
            return errors, warnings

        if not value:
            return errors, warnings

        # Length validation
        if 'min_length' in rules and len(value) < rules['min_length']:
            errors.append(f"{field_name} too short")

        if 'max_length' in rules and len(value) > rules['max_length']:
            errors.append(f"{field_name} too long")

        if 'length' in rules and len(value) != rules['length']:
            errors.append(f"{field_name} must be {rules['length']} characters")

        # Pattern validation
        if 'pattern' in rules and not re.match(rules['pattern'], value.replace(' ', '')):
            errors.append(f"{field_name} format is invalid")

        # Date validation
        if 'format' in rules and rules['format'] == 'DD/MM/YYYY':
            try:
                date_obj = datetime.strptime(value, '%d/%m/%Y')

                if 'min_age' in rules or 'max_age' in rules:
                    age = (datetime.now() - date_obj).days // 365
                    if 'min_age' in rules and age < rules['min_age']:
                        errors.append(f"Age too young for driving license ({age} years)")
                    if 'max_age' in rules and age > rules['max_age']:
                        warnings.append(f"Unusual age for driving license ({age} years)")

                if 'max_future_days' in rules:
                    days_future = (date_obj - datetime.now()).days
                    if days_future > rules['max_future_days']:
                        errors.append(f"{field_name} cannot be in the future")

                if 'min_future_days' in rules:
                    days_future = (date_obj - datetime.now()).days
                    if days_future < rules['min_future_days']:
                        if days_future < 0:
                            errors.append(f"{field_name} is expired")
                        else:
                            warnings.append(f"{field_name} expires soon ({days_future} days)")

            except ValueError:
                errors.append(f"{field_name} date format is invalid")

        return errors, warnings

    def _validate_license_cross_fields(self, fields: Dict[str, ExtractedField]) -> List[str]:
        """Validate relationships between license fields"""
        warnings = []

        # Check if encoded gender matches extracted data
        if 'encoded_gender' in fields and 'gender' in fields:
            encoded_gender = fields['encoded_gender'].value
            extracted_gender = fields['gender'].value.upper()
            if encoded_gender != extracted_gender[0]:
                warnings.append("Gender mismatch between encoded and extracted data")

        # Check if encoded birth info matches extracted DOB
        if all(f in fields for f in ['encoded_birth_month', 'encoded_birth_day', 'date_of_birth']):
            try:
                dob = datetime.strptime(fields['date_of_birth'].value, '%d/%m/%Y')
                encoded_month = int(fields['encoded_birth_month'].value)
                encoded_day = int(fields['encoded_birth_day'].value)

                if dob.month != encoded_month or dob.day != encoded_day:
                    warnings.append("Birth date mismatch between encoded and extracted data")
            except (ValueError, KeyError):
                pass

        # Check license validity period
        if 'issue_date' in fields and 'expiry_date' in fields:
            try:
                issue = datetime.strptime(fields['issue_date'].value, '%d/%m/%Y')
                expiry = datetime.strptime(fields['expiry_date'].value, '%d/%m/%Y')

                validity_years = (expiry - issue).days // 365
                if validity_years < 9 or validity_years > 11:  # UK licenses typically valid for 10 years
                    warnings.append(f"Unusual license validity period ({validity_years} years)")
            except ValueError:
                pass

        return warnings

    def _preprocess_license_image(self, image: np.ndarray) -> np.ndarray:
        """Preprocess UK license image for better OCR"""
        try:
            # Enhance contrast and sharpness
            lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
            l, a, b = cv2.split(lab)

            # Apply CLAHE to L channel
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
            l = clahe.apply(l)

            # Merge channels and convert back
            enhanced = cv2.merge([l, a, b])
            enhanced = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)

            return enhanced

        except Exception as e:
            self.logger.error(f"❌ License image preprocessing failed: {e}")
            return image

    def _analyze_license_fraud(self, image: np.ndarray, fields: Dict[str, ExtractedField]) -> FraudAnalysis:
        """Analyze UK license for fraud indicators"""
        try:
            if self.fraud_detector:
                return self.fraud_detector.analyze_document(image, DocumentType.UK_DRIVING_LICENSE)
            else:
                # Basic fraud analysis
                fraud_indicators = []

                # Check for suspicious patterns
                if 'licence_number' in fields:
                    license_num = fields['licence_number'].value.replace(' ', '')
                    if not self._validate_uk_license_number(license_num):
                        fraud_indicators.append("Invalid license number format")

                return FraudAnalysis(
                    is_authentic=len(fraud_indicators) == 0,
                    is_fraudulent=len(fraud_indicators) > 0,
                    is_altered=False,
                    confidence_score=0.8 if len(fraud_indicators) == 0 else 0.3,
                    authenticity_score=0.8,
                    alteration_score=0.0,
                    fraud_indicators=fraud_indicators,
                    security_features_verified=['license_number_format'],
                    requires_manual_review=len(fraud_indicators) > 0
                )
        except Exception as e:
            self.logger.error(f"❌ License fraud analysis failed: {e}")
            return FraudAnalysis(
                is_authentic=True,
                is_fraudulent=False,
                is_altered=False,
                confidence_score=0.5,
                authenticity_score=0.5,
                alteration_score=0.0,
                requires_manual_review=True
            )

    def _extract_license_face(self, image: np.ndarray):
        """Extract face from UK license"""
        try:
            if self.face_processor:
                return self.face_processor.extract_face(image)
            else:
                return None
        except Exception as e:
            self.logger.error(f"❌ License face extraction failed: {e}")
            return None

    def _calculate_license_confidence(self, fields: Dict[str, ExtractedField], fraud_analysis: FraudAnalysis) -> float:
        """Calculate overall confidence for license processing"""
        if not fields:
            return 0.0

        # Weight different factors
        field_confidence = sum(field.confidence for field in fields.values()) / len(fields)
        fraud_confidence = fraud_analysis.confidence_score

        # Required fields boost
        required_fields = ['licence_number', 'first_name', 'last_name', 'date_of_birth', 'postcode']
        required_present = sum(1 for field in required_fields if field in fields)
        completeness_score = required_present / len(required_fields)

        # License number validation boost
        license_valid = False
        if 'licence_number' in fields:
            license_num = fields['licence_number'].value.replace(' ', '')
            license_valid = self._validate_uk_license_number(license_num)

        license_score = 1.0 if license_valid else 0.5

        # Weighted average
        overall = (field_confidence * 0.4) + (fraud_confidence * 0.2) + (completeness_score * 0.2) + (license_score * 0.2)

        return min(0.99, max(0.01, overall))

    def _generate_license_recommendations(self, fields, validation_result, fraud_analysis) -> List[str]:
        """Generate UK license-specific recommendations"""
        recommendations = []

        if not fields:
            recommendations.append("⚠️ No license data extracted - check image quality")
            return recommendations

        # Check for expired license
        if 'is_expired' in fields and fields['is_expired'].value == 'true':
            recommendations.append("🚨 Driving license is expired - renewal required")
        elif 'days_until_expiry' in fields:
            try:
                days = int(fields['days_until_expiry'].value)
                if 0 < days < 90:  # 3 months
                    recommendations.append(f"⚠️ License expires in {days} days - consider renewal")
            except ValueError:
                pass

        # License number validation
        if 'licence_number' in fields:
            license_num = fields['licence_number'].value.replace(' ', '')
            if not self._validate_uk_license_number(license_num):
                recommendations.append("❌ Invalid UK license number format")

        # Validation recommendations
        if validation_result.errors:
            recommendations.append("❌ Validation errors found - review license data")

        # Fraud recommendations
        if fraud_analysis.is_fraudulent:
            recommendations.append("🚨 License shows signs of fraud - manual verification required")
        elif fraud_analysis.requires_manual_review:
            recommendations.append("👁️ Manual review recommended for authenticity")

        # Default success
        if not recommendations:
            recommendations.append("✅ UK driving license processed successfully")

        return recommendations

    def _bytes_to_image(self, image_data: bytes) -> Optional[np.ndarray]:
        """Convert bytes to OpenCV image"""
        try:
            nparr = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            return image
        except Exception as e:
            self.logger.error(f"❌ Failed to decode image: {e}")
            return None

    def _create_error_result(self, request: DocumentProcessingRequest, errors: List[str]) -> DocumentExtractionResult:
        """Create error result for license processing"""
        return DocumentExtractionResult(
            status=ProcessingStatus.FAILED,
            document_type=request.document_type,
            extracted_fields={},
            fraud_analysis=FraudAnalysis(
                is_authentic=False,
                is_fraudulent=False,
                is_altered=False,
                confidence_score=0.0,
                authenticity_score=0.0,
                alteration_score=0.0,
                requires_manual_review=True
            ),
            face_analysis=None,
            validation_result=ValidationResult(
                is_valid=False,
                is_consistent=False,
                errors=errors
            ),
            processing_metadata=ProcessingMetadata(
                processing_method="uk_license_error",
                processing_time=0.0,
                model_version="1.0.0",
                request_id=request.request_id,
                application_id=request.application_id
            ),
            recommendations=["❌ UK license processing failed - see errors"],
            confidence=0.0
        )