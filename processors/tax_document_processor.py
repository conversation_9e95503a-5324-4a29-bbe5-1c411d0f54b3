"""
Tax Document Processor - Specialized processing for P60/P45 tax documents
Optimized for HMRC format and UK tax document validation
"""

import numpy as np
import cv2
import re
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime

from app.core.interfaces import IDocumentProcessor
from app.models.document_models import (
    DocumentProcessingRequest,
    DocumentExtractionResult,
    DocumentType,
    ProcessingStatus,
    ExtractedField,
    ExtractionMethod,
    FraudAnalysis,
    ValidationResult,
    ProcessingMetadata
)


class TaxDocumentProcessor(IDocumentProcessor):
    """Specialized processor for P60/P45 tax documents"""
    
    def __init__(self, ocr_engine, fraud_detector, field_validator):
        """Initialize with required dependencies"""
        self.ocr_engine = ocr_engine
        self.fraud_detector = fraud_detector
        self.field_validator = field_validator
        self.logger = logging.getLogger(__name__)
        
        # Tax document validation rules
        self.validation_rules = {
            'employee_name': {
                'min_length': 2,
                'max_length': 100,
                'pattern': r'^[A-Z\s\-\'\.]+$',
                'required': True
            },
            'first_name': {
                'min_length': 1,
                'max_length': 50,
                'pattern': r'^[A-Z\s\-\'\.]+$',
                'required': True
            },
            'last_name': {
                'min_length': 1,
                'max_length': 50,
                'pattern': r'^[A-Z\s\-\'\.]+$',
                'required': True
            },
            'employer_name': {
                'min_length': 2,
                'max_length': 200,
                'required': False
            },
            'ni_number': {
                'pattern': r'^[A-Z]{2}\s?\d{2}\s?\d{2}\s?\d{2}\s?[A-Z]$',
                'length_clean': 9,
                'required': True,
                'description': 'UK National Insurance number format'
            },
            'tax_year': {
                'pattern': r'^\d{4}[-/]\d{2,4}$',
                'required': False,
                'description': 'Tax year format: YYYY-YY or YYYY/YYYY'
            },
            'total_pay': {
                'pattern': r'^\d+\.?\d{0,2}$',
                'required': False,
                'description': 'Total pay amount'
            },
            'tax_deducted': {
                'pattern': r'^\d+\.?\d{0,2}$',
                'required': False,
                'description': 'Tax deducted amount'
            },
            'paye_reference': {
                'pattern': r'^[A-Z0-9]{3}/[A-Z0-9]+$',
                'required': False,
                'description': 'PAYE reference format'
            },
            'postcode': {
                'pattern': r'^[A-Z]{1,2}\d[A-Z\d]?\s?\d[A-Z]{2}$',
                'required': False,
                'description': 'UK postcode format'
            }
        }
        
        # P60/P45 field extraction patterns
        self.extraction_patterns = {
            'employee_name': [
                r'(?:Employee|Name|To employee)\s*:?\s*([A-Z][A-Z\s\-\'\.]+?)(?:\s+(?:Address|Employer|Tax|Year|NI|National)|\n|$)',
                r'To\s+employee\s*:?\s*([A-Z][A-Z\s\-\'\.]+?)(?:\s+(?:Address|Employer|Tax|Year|NI|National)|\n|$)',
                r'Name\s*:?\s*([A-Z][A-Z\s\-\'\.]+?)(?:\s+(?:Address|Employer|Tax|Year|NI|National)|\n|$)'
            ],
            'employer_name': [
                r'(?:Employer|Company)\s*:?\s*([A-Z][A-Z\s&.,\-]+?)(?:\s+(?:Address|Tax|Year|NI|PAYE|National)|\n|$)',
                r'PAYE\s+reference\s*:?\s*[A-Z0-9/]+\s*([A-Z][A-Z\s&.,\-]+?)(?:\s+(?:Address|Tax|Year|NI|National)|\n|$)'
            ],
            'ni_number': [
                r'(?:National Insurance|NI|N\.I\.)\s*(?:number|No\.?)?\s*:?\s*([A-Z]{2}\s?\d{2}\s?\d{2}\s?\d{2}\s?[A-Z])',
                r'([A-Z]{2}\s?\d{2}\s?\d{2}\s?\d{2}\s?[A-Z])(?=\s|$|\n)'
            ],
            'tax_year': [
                r'(?:Tax year|Year)\s*:?\s*(\d{4}[-/]\d{2,4})',
                r'(\d{4}[-/]\d{2,4})\s*(?:tax year|year)'
            ],
            'total_pay': [
                r'(?:Total pay|Gross pay|Pay)\s*:?\s*£?\s*([0-9,]+\.?\d{0,2})',
                r'£\s*([0-9,]+\.?\d{0,2})\s*(?:total|gross|pay)'
            ],
            'tax_deducted': [
                r'(?:Tax deducted|Income tax|PAYE)\s*:?\s*£?\s*([0-9,]+\.?\d{0,2})',
                r'£\s*([0-9,]+\.?\d{0,2})\s*(?:tax|deducted|PAYE)'
            ],
            'paye_reference': [
                r'(?:PAYE reference|PAYE ref|Employer ref)\s*:?\s*([A-Z0-9]{3}/[A-Z0-9]+)',
                r'([A-Z0-9]{3}/[A-Z0-9]+)'
            ],
            'postcode': [
                r'([A-Z]{1,2}\d[A-Z\d]?\s?\d[A-Z]{2})',
                r'(?:Address|employee)\s*:?[^£\n]*?([A-Z]{1,2}\d[A-Z\d]?\s?\d[A-Z]{2})'
            ]
        }
    
    def process_document(self, request: DocumentProcessingRequest) -> DocumentExtractionResult:
        """Process P60/P45 tax document"""
        try:
            self.logger.info(f"🔄 Processing tax document: {request.request_id}")
            
            # Convert image data to numpy array
            image = self._bytes_to_image(request.image_data)
            if image is None:
                return self._create_error_result(request, ["Invalid image data"])
            
            # Preprocess image for better OCR
            processed_image = self._preprocess_tax_document_image(image)
            
            # Extract text using OCR
            raw_text = self._extract_raw_text(processed_image)
            
            # Extract structured fields using patterns
            extracted_fields = self._extract_tax_fields_with_patterns(raw_text)
            
            # Enhance fields with tax-specific processing
            enhanced_fields = self._enhance_tax_fields(extracted_fields)
            
            # Validate tax document fields
            validation_result = self._validate_tax_fields(enhanced_fields)
            
            # Analyze for fraud (basic for tax documents)
            fraud_analysis = self._analyze_tax_document_fraud(image, enhanced_fields)
            
            # Calculate confidence
            confidence = self._calculate_tax_document_confidence(enhanced_fields, validation_result)
            
            # Generate recommendations
            recommendations = self._generate_tax_document_recommendations(
                enhanced_fields, validation_result, fraud_analysis
            )
            
            # Create result
            result = DocumentExtractionResult(
                status=ProcessingStatus.SUCCESS,
                document_type=request.document_type,
                extracted_fields=enhanced_fields,
                fraud_analysis=fraud_analysis,
                face_analysis=None,  # Tax documents don't have faces
                validation_result=validation_result,
                processing_metadata=ProcessingMetadata(
                    processing_method="tax_document_specialized",
                    processing_time=0.0,
                    model_version="1.0.0",
                    request_id=request.request_id,
                    application_id=request.application_id
                ),
                recommendations=recommendations,
                confidence=confidence
            )
            
            self.logger.info(f"✅ Tax document processed: {confidence:.2f} confidence")
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Tax document processing failed: {e}")
            return self._create_error_result(request, [str(e)])
    
    def validate_fields(self, fields: Dict[str, ExtractedField], document_type: DocumentType) -> ValidationResult:
        """Validate tax document fields"""
        return self._validate_tax_fields(fields)
    
    def supports_document_type(self, document_type: DocumentType) -> bool:
        """Check if processor supports document type"""
        return document_type in [DocumentType.P60, DocumentType.P45, DocumentType.P45_P60]
    
    def _extract_raw_text(self, image: np.ndarray) -> str:
        """Extract raw text using OCR engine"""
        try:
            if self.ocr_engine:
                return self.ocr_engine.extract_text(image)
            else:
                self.logger.warning("⚠️ No OCR engine available")
                return ""
        except Exception as e:
            self.logger.error(f"❌ OCR text extraction failed: {e}")
            return ""
    
    def _extract_tax_fields_with_patterns(self, text: str) -> Dict[str, ExtractedField]:
        """Extract tax document fields using regex patterns"""
        fields = {}
        
        if not text:
            return fields
        
        # Clean text for better matching
        text = text.replace('\n', ' ').replace('\r', ' ')
        text = re.sub(r'\s+', ' ', text)  # Normalize whitespace
        
        # Extract each field using patterns
        for field_name, patterns in self.extraction_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, text, re.IGNORECASE | re.MULTILINE)
                if match:
                    value = match.group(1).strip()
                    
                    # Clean up the extracted value
                    cleaned_value = self._clean_field_value(field_name, value)
                    
                    if cleaned_value:  # Only add if we have a valid value
                        fields[field_name] = ExtractedField(
                            value=cleaned_value,
                            confidence=0.85,  # High confidence for pattern matching
                            extraction_method=ExtractionMethod.PATTERN_MATCHING,
                            field_type=self._get_field_type(field_name),
                            required=self.validation_rules.get(field_name, {}).get('required', False),
                            editable=True
                        )
                        break  # Use first successful match
        
        return fields
    
    def _clean_field_value(self, field_name: str, value: str) -> str:
        """Clean extracted field values"""
        if not value:
            return ""
        
        # Name fields
        if field_name in ['employee_name', 'first_name', 'last_name', 'employer_name']:
            # Remove common prefixes/suffixes
            value = re.sub(r'^(Mr|Mrs|Miss|Ms|Dr|Prof)\.?\s+', '', value, flags=re.IGNORECASE)
            value = re.sub(r'\s+(Ltd|Limited|PLC|Inc)\.?$', '', value, flags=re.IGNORECASE)
            return value.title().strip()
        
        # NI number
        elif field_name == 'ni_number':
            # Remove spaces and format properly
            ni = re.sub(r'[^A-Z0-9]', '', value.upper())
            if len(ni) == 9:
                return f"{ni[:2]} {ni[2:4]} {ni[4:6]} {ni[6:8]} {ni[8]}"
            return value.strip()
        
        # Currency fields
        elif field_name in ['total_pay', 'tax_deducted']:
            # Remove currency symbols and clean
            value = re.sub(r'[£$€]', '', value)
            value = re.sub(r'[^\d.,]', '', value)
            return value.strip()
        
        # Postcode
        elif field_name == 'postcode':
            postcode = value.upper().strip()
            # Add space if missing
            if ' ' not in postcode and len(postcode) >= 5:
                postcode = postcode[:-3] + ' ' + postcode[-3:]
            return postcode
        
        # Default cleaning
        return value.strip()
    
    def _get_field_type(self, field_name: str) -> str:
        """Get field type for UI rendering"""
        if field_name in ['total_pay', 'tax_deducted']:
            return 'currency'
        elif field_name in ['employee_name', 'employer_name']:
            return 'textarea'
        else:
            return 'text'
    
    def _enhance_tax_fields(self, fields: Dict[str, ExtractedField]) -> Dict[str, ExtractedField]:
        """Enhance extracted fields with tax-specific processing"""
        enhanced = fields.copy()
        
        # Split employee name into first and last name if not already done
        if 'employee_name' in enhanced and 'first_name' not in enhanced:
            full_name = enhanced['employee_name'].value
            name_parts = full_name.split()
            
            if len(name_parts) >= 2:
                enhanced['first_name'] = ExtractedField(
                    value=name_parts[0],
                    confidence=enhanced['employee_name'].confidence,
                    extraction_method=ExtractionMethod.PATTERN_MATCHING,
                    field_type='text',
                    required=True,
                    editable=True
                )
                
                enhanced['last_name'] = ExtractedField(
                    value=' '.join(name_parts[1:]),
                    confidence=enhanced['employee_name'].confidence,
                    extraction_method=ExtractionMethod.PATTERN_MATCHING,
                    field_type='text',
                    required=True,
                    editable=True
                )
        
        # Calculate net pay if we have total pay and tax deducted
        if 'total_pay' in enhanced and 'tax_deducted' in enhanced:
            try:
                total = float(enhanced['total_pay'].value.replace(',', ''))
                tax = float(enhanced['tax_deducted'].value.replace(',', ''))
                net_pay = total - tax
                
                enhanced['net_pay'] = ExtractedField(
                    value=f"{net_pay:.2f}",
                    confidence=min(enhanced['total_pay'].confidence, enhanced['tax_deducted'].confidence),
                    extraction_method=ExtractionMethod.PATTERN_MATCHING,
                    field_type='currency',
                    required=False,
                    editable=False
                )
            except (ValueError, KeyError):
                pass
        
        # Validate and enhance tax year
        if 'tax_year' in enhanced:
            tax_year = enhanced['tax_year'].value
            # Convert YYYY-YY to YYYY/YYYY format
            if '-' in tax_year and len(tax_year) == 7:
                start_year = tax_year[:4]
                end_year_short = tax_year[5:]
                end_year = start_year[:2] + end_year_short
                enhanced['tax_year'].value = f"{start_year}/{end_year}"
        
        return enhanced

    def _validate_tax_fields(self, fields: Dict[str, ExtractedField]) -> ValidationResult:
        """Validate tax document fields"""
        errors = []
        warnings = []
        field_validations = {}

        for field_name, field in fields.items():
            if field_name in self.validation_rules:
                rules = self.validation_rules[field_name]
                field_errors, field_warnings = self._validate_single_field(field_name, field.value, rules)

                if field_errors:
                    errors.extend(field_errors)
                if field_warnings:
                    warnings.extend(field_warnings)

                field_validations[field_name] = {
                    'is_valid': len(field_errors) == 0,
                    'errors': field_errors,
                    'warnings': field_warnings
                }

        # Cross-field validations
        cross_validations = self._validate_tax_cross_fields(fields)
        if cross_validations:
            warnings.extend(cross_validations)

        return ValidationResult(
            is_valid=len(errors) == 0,
            is_consistent=len(cross_validations) == 0,
            errors=errors,
            warnings=warnings,
            field_validations=field_validations,
            cross_field_validations=cross_validations
        )

    def _validate_single_field(self, field_name: str, value: str, rules: Dict) -> Tuple[List[str], List[str]]:
        """Validate a single field against rules"""
        errors = []
        warnings = []

        if not value and rules.get('required', False):
            errors.append(f"{field_name} is required")
            return errors, warnings

        if not value:
            return errors, warnings

        # Length validation
        if 'min_length' in rules and len(value) < rules['min_length']:
            errors.append(f"{field_name} too short")

        if 'max_length' in rules and len(value) > rules['max_length']:
            errors.append(f"{field_name} too long")

        if 'length_clean' in rules:
            clean_value = re.sub(r'[^A-Z0-9]', '', value.upper())
            if len(clean_value) != rules['length_clean']:
                errors.append(f"{field_name} invalid length")

        # Pattern validation
        if 'pattern' in rules:
            if field_name == 'ni_number':
                # Special handling for NI number with or without spaces
                clean_ni = re.sub(r'[^A-Z0-9]', '', value.upper())
                formatted_ni = f"{clean_ni[:2]} {clean_ni[2:4]} {clean_ni[4:6]} {clean_ni[6:8]} {clean_ni[8]}" if len(clean_ni) == 9 else value
                if not re.match(rules['pattern'], formatted_ni):
                    errors.append(f"{field_name} format is invalid")
            else:
                if not re.match(rules['pattern'], value):
                    errors.append(f"{field_name} format is invalid")

        return errors, warnings

    def _validate_tax_cross_fields(self, fields: Dict[str, ExtractedField]) -> List[str]:
        """Validate relationships between tax document fields"""
        warnings = []

        # Check if net pay calculation makes sense
        if all(f in fields for f in ['total_pay', 'tax_deducted', 'net_pay']):
            try:
                total = float(fields['total_pay'].value.replace(',', ''))
                tax = float(fields['tax_deducted'].value.replace(',', ''))
                net = float(fields['net_pay'].value.replace(',', ''))

                expected_net = total - tax
                if abs(net - expected_net) > 0.01:  # Allow for rounding
                    warnings.append("Net pay calculation doesn't match total pay minus tax deducted")
            except (ValueError, KeyError):
                pass

        # Check if tax deducted is reasonable percentage of total pay
        if 'total_pay' in fields and 'tax_deducted' in fields:
            try:
                total = float(fields['total_pay'].value.replace(',', ''))
                tax = float(fields['tax_deducted'].value.replace(',', ''))

                if total > 0:
                    tax_rate = (tax / total) * 100
                    if tax_rate > 50:  # More than 50% tax rate is unusual
                        warnings.append(f"High tax rate ({tax_rate:.1f}%) - please verify")
                    elif tax_rate < 0:
                        warnings.append("Negative tax rate - please verify amounts")
            except (ValueError, KeyError):
                pass

        # Check tax year validity
        if 'tax_year' in fields:
            tax_year = fields['tax_year'].value
            try:
                if '/' in tax_year:
                    start_year, end_year = tax_year.split('/')
                    start_year = int(start_year)
                    end_year = int(end_year)

                    if end_year != start_year + 1:
                        warnings.append("Tax year format should be consecutive years")

                    current_year = datetime.now().year
                    if start_year > current_year + 1:
                        warnings.append("Tax year is in the future")
                    elif start_year < current_year - 10:
                        warnings.append("Tax year is very old")
            except (ValueError, IndexError):
                pass

        return warnings

    def _preprocess_tax_document_image(self, image: np.ndarray) -> np.ndarray:
        """Preprocess tax document image for better OCR"""
        try:
            # Convert to grayscale
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image

            # Enhance contrast
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(gray)

            # Denoise
            denoised = cv2.fastNlMeansDenoising(enhanced)

            # Convert back to BGR for consistency
            if len(image.shape) == 3:
                return cv2.cvtColor(denoised, cv2.COLOR_GRAY2BGR)
            else:
                return denoised

        except Exception as e:
            self.logger.error(f"❌ Tax document image preprocessing failed: {e}")
            return image

    def _analyze_tax_document_fraud(self, image: np.ndarray, fields: Dict[str, ExtractedField]) -> FraudAnalysis:
        """Analyze tax document for fraud indicators"""
        try:
            fraud_indicators = []

            # Basic fraud checks for tax documents
            if 'ni_number' in fields:
                ni_number = fields['ni_number'].value.replace(' ', '')
                if not self._validate_ni_number_checksum(ni_number):
                    fraud_indicators.append("Invalid NI number checksum")

            # Check for suspicious amounts
            if 'total_pay' in fields:
                try:
                    total_pay = float(fields['total_pay'].value.replace(',', ''))
                    if total_pay > 1000000:  # £1M+ is unusual
                        fraud_indicators.append("Unusually high total pay amount")
                    elif total_pay < 0:
                        fraud_indicators.append("Negative total pay amount")
                except ValueError:
                    pass

            return FraudAnalysis(
                is_authentic=len(fraud_indicators) == 0,
                is_fraudulent=len(fraud_indicators) > 2,
                is_altered=len(fraud_indicators) > 0,
                confidence_score=0.8 if len(fraud_indicators) == 0 else 0.5,
                authenticity_score=0.8,
                alteration_score=0.1 if len(fraud_indicators) > 0 else 0.0,
                fraud_indicators=fraud_indicators,
                security_features_verified=['ni_number_format', 'amount_validation'],
                requires_manual_review=len(fraud_indicators) > 0
            )

        except Exception as e:
            self.logger.error(f"❌ Tax document fraud analysis failed: {e}")
            return FraudAnalysis(
                is_authentic=True,
                is_fraudulent=False,
                is_altered=False,
                confidence_score=0.5,
                authenticity_score=0.5,
                alteration_score=0.0,
                requires_manual_review=True
            )

    def _validate_ni_number_checksum(self, ni_number: str) -> bool:
        """Validate NI number checksum (simplified)"""
        if len(ni_number) != 9:
            return False

        # Basic format check
        if not re.match(r'^[A-Z]{2}\d{6}[A-Z]$', ni_number):
            return False

        # Check for invalid prefixes
        invalid_prefixes = ['BG', 'GB', 'NK', 'KN', 'TN', 'NT', 'ZZ']
        if ni_number[:2] in invalid_prefixes:
            return False

        return True

    def _calculate_tax_document_confidence(self, fields: Dict[str, ExtractedField], validation_result: ValidationResult) -> float:
        """Calculate overall confidence for tax document processing"""
        if not fields:
            return 0.0

        # Base confidence from field extraction
        field_confidence = sum(field.confidence for field in fields.values()) / len(fields)

        # Required fields boost
        required_fields = ['employee_name', 'ni_number']
        required_present = sum(1 for field in required_fields if field in fields)
        completeness_score = required_present / len(required_fields)

        # Validation penalty
        validation_score = 1.0 if validation_result.is_valid else 0.7

        # Key field bonus (NI number is critical)
        ni_bonus = 0.1 if 'ni_number' in fields else 0.0

        # Weighted calculation
        overall = (field_confidence * 0.5) + (completeness_score * 0.3) + (validation_score * 0.2) + ni_bonus

        return min(0.99, max(0.01, overall))

    def _generate_tax_document_recommendations(self, fields, validation_result, fraud_analysis) -> List[str]:
        """Generate tax document-specific recommendations"""
        recommendations = []

        if not fields:
            recommendations.append("⚠️ No tax document data extracted - check image quality")
            return recommendations

        # Check for missing critical fields
        if 'employee_name' not in fields:
            recommendations.append("❌ Employee name not found - manual entry required")

        if 'ni_number' not in fields:
            recommendations.append("❌ National Insurance number not found - manual entry required")

        # Validation recommendations
        if validation_result.errors:
            recommendations.append("❌ Validation errors found - review tax document data")

        if validation_result.warnings:
            recommendations.append("⚠️ Validation warnings - check flagged fields")

        # Fraud recommendations
        if fraud_analysis.is_fraudulent:
            recommendations.append("🚨 Document shows signs of alteration - manual verification required")
        elif fraud_analysis.requires_manual_review:
            recommendations.append("👁️ Manual review recommended for data verification")

        # Data completeness recommendations
        missing_optional = []
        optional_fields = ['employer_name', 'total_pay', 'tax_deducted', 'postcode']
        for field in optional_fields:
            if field not in fields:
                missing_optional.append(field)

        if missing_optional:
            recommendations.append(f"📋 Consider manual entry for: {', '.join(missing_optional)}")

        # Default success
        if not recommendations:
            recommendations.append("✅ Tax document processed successfully")

        return recommendations

    def _bytes_to_image(self, image_data: bytes) -> Optional[np.ndarray]:
        """Convert bytes to OpenCV image"""
        try:
            nparr = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            return image
        except Exception as e:
            self.logger.error(f"❌ Failed to decode image: {e}")
            return None

    def _create_error_result(self, request: DocumentProcessingRequest, errors: List[str]) -> DocumentExtractionResult:
        """Create error result for tax document processing"""
        return DocumentExtractionResult(
            status=ProcessingStatus.FAILED,
            document_type=request.document_type,
            extracted_fields={},
            fraud_analysis=FraudAnalysis(
                is_authentic=False,
                is_fraudulent=False,
                is_altered=False,
                confidence_score=0.0,
                authenticity_score=0.0,
                alteration_score=0.0,
                requires_manual_review=True
            ),
            face_analysis=None,
            validation_result=ValidationResult(
                is_valid=False,
                is_consistent=False,
                errors=errors
            ),
            processing_metadata=ProcessingMetadata(
                processing_method="tax_document_error",
                processing_time=0.0,
                model_version="1.0.0",
                request_id=request.request_id,
                application_id=request.application_id
            ),
            recommendations=["❌ Tax document processing failed - see errors"],
            confidence=0.0
        )
