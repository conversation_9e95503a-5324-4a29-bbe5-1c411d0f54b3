"""
Passport Document Processor - Specialized processing for passport documents
Optimized for MRZ extraction and passport-specific validation
"""

import numpy as np
import cv2
import re
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, timedelta

from app.core.interfaces import IDocumentProcessor
from app.models.document_models import (
    DocumentProcessingRequest,
    DocumentExtractionResult,
    DocumentType,
    ProcessingStatus,
    ExtractedField,
    ExtractionMethod,
    FraudAnalysis,
    ValidationResult,
    ProcessingMetadata
)


class PassportProcessor(IDocumentProcessor):
    """Specialized processor for passport documents"""
    
    def __init__(self, mrz_engine, fraud_detector, face_processor, field_validator):
        """Initialize with required dependencies"""
        self.mrz_engine = mrz_engine
        self.fraud_detector = fraud_detector
        self.face_processor = face_processor
        self.field_validator = field_validator
        self.logger = logging.getLogger(__name__)
        
        # Passport-specific validation rules
        self.validation_rules = {
            'passport_number': {
                'min_length': 6,
                'max_length': 12,
                'pattern': r'^[A-Z0-9]+$',
                'required': True
            },
            'first_name': {
                'min_length': 1,
                'max_length': 50,
                'pattern': r'^[A-Z\s\-\'\.]+$',
                'required': True
            },
            'last_name': {
                'min_length': 1,
                'max_length': 50,
                'pattern': r'^[A-Z\s\-\'\.]+$',
                'required': True
            },
            'nationality': {
                'length': 3,
                'pattern': r'^[A-Z]{3}$',
                'required': True
            },
            'date_of_birth': {
                'format': 'DD/MM/YYYY',
                'min_age': 0,
                'max_age': 150,
                'required': True
            },
            'expiry_date': {
                'format': 'DD/MM/YYYY',
                'min_future_days': 1,
                'required': True
            },
            'issuing_country': {
                'length': 3,
                'pattern': r'^[A-Z]{3}$',
                'required': True
            }
        }
        
        # Country code mappings
        self.country_codes = {
            'GBR': 'United Kingdom',
            'USA': 'United States',
            'CAN': 'Canada',
            'AUS': 'Australia',
            'IND': 'India',
            'DEU': 'Germany',
            'FRA': 'France',
            'ITA': 'Italy',
            'ESP': 'Spain',
            'NLD': 'Netherlands'
        }
    
    def process_document(self, request: DocumentProcessingRequest) -> DocumentExtractionResult:
        """Process passport document"""
        try:
            self.logger.info(f"🔄 Processing passport document: {request.request_id}")
            
            # Convert image data to numpy array
            image = self._bytes_to_image(request.image_data)
            if image is None:
                return self._create_error_result(request, ["Invalid image data"])
            
            # Preprocess image for better MRZ extraction
            processed_image = self._preprocess_passport_image(image)
            
            # Extract MRZ fields using FastMRZ
            extracted_fields = self._extract_mrz_fields(processed_image)
            
            # Enhance fields with additional processing
            enhanced_fields = self._enhance_passport_fields(extracted_fields, image)
            
            # Perform passport-specific validation
            validation_result = self._validate_passport_fields(enhanced_fields)
            
            # Analyze for fraud
            fraud_analysis = self._analyze_passport_fraud(image, enhanced_fields)
            
            # Extract face/portrait
            face_analysis = self._extract_passport_face(image)
            
            # Calculate confidence
            confidence = self._calculate_passport_confidence(enhanced_fields, fraud_analysis)
            
            # Generate recommendations
            recommendations = self._generate_passport_recommendations(
                enhanced_fields, validation_result, fraud_analysis
            )
            
            # Create result
            result = DocumentExtractionResult(
                status=ProcessingStatus.SUCCESS,
                document_type=request.document_type,
                extracted_fields=enhanced_fields,
                fraud_analysis=fraud_analysis,
                face_analysis=face_analysis,
                validation_result=validation_result,
                processing_metadata=ProcessingMetadata(
                    processing_method="passport_specialized",
                    processing_time=0.0,  # Will be set by service
                    model_version="1.0.0",
                    request_id=request.request_id,
                    application_id=request.application_id
                ),
                recommendations=recommendations,
                confidence=confidence
            )
            
            self.logger.info(f"✅ Passport processed successfully: {confidence:.2f} confidence")
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Passport processing failed: {e}")
            return self._create_error_result(request, [str(e)])
    
    def validate_fields(self, fields: Dict[str, ExtractedField], document_type: DocumentType) -> ValidationResult:
        """Validate passport fields"""
        return self._validate_passport_fields(fields)
    
    def supports_document_type(self, document_type: DocumentType) -> bool:
        """Check if processor supports document type"""
        return document_type in [DocumentType.PASSPORT, DocumentType.PASSPORT_ANY]
    
    def _extract_mrz_fields(self, image: np.ndarray) -> Dict[str, ExtractedField]:
        """Extract MRZ fields using FastMRZ engine"""
        try:
            if self.mrz_engine:
                return self.mrz_engine.extract_fields(image, DocumentType.PASSPORT)
            else:
                self.logger.warning("⚠️ No MRZ engine available")
                return {}
        except Exception as e:
            self.logger.error(f"❌ MRZ extraction failed: {e}")
            return {}
    
    def _enhance_passport_fields(self, fields: Dict[str, ExtractedField], image: np.ndarray) -> Dict[str, ExtractedField]:
        """Enhance extracted fields with additional processing"""
        enhanced = fields.copy()
        
        # Convert nationality code to full name
        if 'nationality' in enhanced:
            code = enhanced['nationality'].value
            if code in self.country_codes:
                enhanced['nationality_full'] = ExtractedField(
                    value=self.country_codes[code],
                    confidence=enhanced['nationality'].confidence,
                    extraction_method=ExtractionMethod.PATTERN_MATCHING,
                    field_type='text',
                    required=False,
                    editable=True
                )
        
        # Convert issuing country code to full name
        if 'issuing_country' in enhanced:
            code = enhanced['issuing_country'].value
            if code in self.country_codes:
                enhanced['issuing_country_full'] = ExtractedField(
                    value=self.country_codes[code],
                    confidence=enhanced['issuing_country'].confidence,
                    extraction_method=ExtractionMethod.PATTERN_MATCHING,
                    field_type='text',
                    required=False,
                    editable=True
                )
        
        # Calculate age from date of birth
        if 'date_of_birth' in enhanced:
            try:
                dob_str = enhanced['date_of_birth'].value
                dob = datetime.strptime(dob_str, '%d/%m/%Y')
                age = (datetime.now() - dob).days // 365
                enhanced['age'] = ExtractedField(
                    value=str(age),
                    confidence=enhanced['date_of_birth'].confidence,
                    extraction_method=ExtractionMethod.PATTERN_MATCHING,
                    field_type='number',
                    required=False,
                    editable=False
                )
            except ValueError:
                pass
        
        # Check if passport is expired
        if 'expiry_date' in enhanced:
            try:
                expiry_str = enhanced['expiry_date'].value
                expiry = datetime.strptime(expiry_str, '%d/%m/%Y')
                is_expired = expiry < datetime.now()
                days_until_expiry = (expiry - datetime.now()).days
                
                enhanced['is_expired'] = ExtractedField(
                    value=str(is_expired).lower(),
                    confidence=enhanced['expiry_date'].confidence,
                    extraction_method=ExtractionMethod.PATTERN_MATCHING,
                    field_type='boolean',
                    required=False,
                    editable=False
                )
                
                enhanced['days_until_expiry'] = ExtractedField(
                    value=str(days_until_expiry),
                    confidence=enhanced['expiry_date'].confidence,
                    extraction_method=ExtractionMethod.PATTERN_MATCHING,
                    field_type='number',
                    required=False,
                    editable=False
                )
            except ValueError:
                pass
        
        return enhanced
    
    def _validate_passport_fields(self, fields: Dict[str, ExtractedField]) -> ValidationResult:
        """Validate passport-specific fields"""
        errors = []
        warnings = []
        field_validations = {}
        
        for field_name, field in fields.items():
            if field_name in self.validation_rules:
                rules = self.validation_rules[field_name]
                field_errors, field_warnings = self._validate_single_field(field_name, field.value, rules)
                
                if field_errors:
                    errors.extend(field_errors)
                if field_warnings:
                    warnings.extend(field_warnings)
                
                field_validations[field_name] = {
                    'is_valid': len(field_errors) == 0,
                    'errors': field_errors,
                    'warnings': field_warnings
                }
        
        # Cross-field validations
        cross_validations = self._validate_passport_cross_fields(fields)
        if cross_validations:
            warnings.extend(cross_validations)
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            is_consistent=len(cross_validations) == 0,
            errors=errors,
            warnings=warnings,
            field_validations=field_validations,
            cross_field_validations=cross_validations
        )
    
    def _validate_single_field(self, field_name: str, value: str, rules: Dict) -> Tuple[List[str], List[str]]:
        """Validate a single field against rules"""
        errors = []
        warnings = []
        
        if not value and rules.get('required', False):
            errors.append(f"{field_name} is required")
            return errors, warnings
        
        if not value:
            return errors, warnings
        
        # Length validation
        if 'min_length' in rules and len(value) < rules['min_length']:
            errors.append(f"{field_name} too short (min {rules['min_length']} characters)")
        
        if 'max_length' in rules and len(value) > rules['max_length']:
            errors.append(f"{field_name} too long (max {rules['max_length']} characters)")
        
        if 'length' in rules and len(value) != rules['length']:
            errors.append(f"{field_name} must be exactly {rules['length']} characters")
        
        # Pattern validation
        if 'pattern' in rules and not re.match(rules['pattern'], value):
            errors.append(f"{field_name} format is invalid")
        
        # Date validation
        if 'format' in rules and rules['format'] == 'DD/MM/YYYY':
            try:
                date_obj = datetime.strptime(value, '%d/%m/%Y')
                
                if 'min_age' in rules or 'max_age' in rules:
                    age = (datetime.now() - date_obj).days // 365
                    if 'min_age' in rules and age < rules['min_age']:
                        errors.append(f"{field_name} indicates age too young")
                    if 'max_age' in rules and age > rules['max_age']:
                        errors.append(f"{field_name} indicates age too old")
                
                if 'min_future_days' in rules:
                    days_future = (date_obj - datetime.now()).days
                    if days_future < rules['min_future_days']:
                        if days_future < 0:
                            errors.append(f"{field_name} is in the past")
                        else:
                            warnings.append(f"{field_name} expires soon ({days_future} days)")
                            
            except ValueError:
                errors.append(f"{field_name} date format is invalid")
        
        return errors, warnings
    
    def _validate_passport_cross_fields(self, fields: Dict[str, ExtractedField]) -> List[str]:
        """Validate relationships between passport fields"""
        warnings = []
        
        # Check if birth date is before expiry date
        if 'date_of_birth' in fields and 'expiry_date' in fields:
            try:
                dob = datetime.strptime(fields['date_of_birth'].value, '%d/%m/%Y')
                expiry = datetime.strptime(fields['expiry_date'].value, '%d/%m/%Y')
                
                if dob >= expiry:
                    warnings.append("Birth date should be before expiry date")
                    
                # Check if passport was issued when person was too young
                min_issue_age = 16  # Most countries issue passports from 16
                issue_age = (expiry - timedelta(days=10*365) - dob).days // 365  # Assume 10-year validity
                if issue_age < min_issue_age:
                    warnings.append(f"Passport may have been issued when applicant was very young ({issue_age} years)")
                    
            except ValueError:
                pass
        
        return warnings
    
    def _preprocess_passport_image(self, image: np.ndarray) -> np.ndarray:
        """Preprocess passport image for better MRZ extraction"""
        try:
            # Convert to grayscale for MRZ processing
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image
            
            # Enhance contrast for MRZ region
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(gray)
            
            # Convert back to BGR for consistency
            if len(image.shape) == 3:
                return cv2.cvtColor(enhanced, cv2.COLOR_GRAY2BGR)
            else:
                return enhanced
                
        except Exception as e:
            self.logger.error(f"❌ Image preprocessing failed: {e}")
            return image
    
    def _analyze_passport_fraud(self, image: np.ndarray, fields: Dict[str, ExtractedField]) -> FraudAnalysis:
        """Analyze passport for fraud indicators"""
        try:
            if self.fraud_detector:
                return self.fraud_detector.analyze_document(image, DocumentType.PASSPORT)
            else:
                # Return basic fraud analysis
                return FraudAnalysis(
                    is_authentic=True,
                    is_fraudulent=False,
                    is_altered=False,
                    confidence_score=0.8,
                    authenticity_score=0.8,
                    alteration_score=0.0,
                    fraud_indicators=[],
                    security_features_verified=['mrz_checksum'],
                    requires_manual_review=False
                )
        except Exception as e:
            self.logger.error(f"❌ Fraud analysis failed: {e}")
            return FraudAnalysis(
                is_authentic=True,
                is_fraudulent=False,
                is_altered=False,
                confidence_score=0.5,
                authenticity_score=0.5,
                alteration_score=0.0,
                requires_manual_review=True
            )
    
    def _extract_passport_face(self, image: np.ndarray):
        """Extract face from passport"""
        try:
            if self.face_processor:
                return self.face_processor.extract_face(image)
            else:
                return None
        except Exception as e:
            self.logger.error(f"❌ Face extraction failed: {e}")
            return None
    
    def _calculate_passport_confidence(self, fields: Dict[str, ExtractedField], fraud_analysis: FraudAnalysis) -> float:
        """Calculate overall confidence for passport processing"""
        if not fields:
            return 0.0
        
        # Weight different factors
        field_confidence = sum(field.confidence for field in fields.values()) / len(fields)
        fraud_confidence = fraud_analysis.confidence_score
        
        # Required fields boost
        required_fields = ['passport_number', 'first_name', 'last_name', 'date_of_birth']
        required_present = sum(1 for field in required_fields if field in fields)
        completeness_score = required_present / len(required_fields)
        
        # Weighted average
        overall = (field_confidence * 0.5) + (fraud_confidence * 0.3) + (completeness_score * 0.2)
        
        return min(0.99, max(0.01, overall))
    
    def _generate_passport_recommendations(self, fields, validation_result, fraud_analysis) -> List[str]:
        """Generate passport-specific recommendations"""
        recommendations = []
        
        if not fields:
            recommendations.append("⚠️ No passport data extracted - check image quality")
            return recommendations
        
        # Check for expired passport
        if 'is_expired' in fields and fields['is_expired'].value == 'true':
            recommendations.append("🚨 Passport is expired - renewal required")
        elif 'days_until_expiry' in fields:
            try:
                days = int(fields['days_until_expiry'].value)
                if 0 < days < 180:  # 6 months
                    recommendations.append(f"⚠️ Passport expires in {days} days - consider renewal")
            except ValueError:
                pass
        
        # Validation recommendations
        if validation_result.errors:
            recommendations.append("❌ Validation errors found - review passport data")
        
        # Fraud recommendations
        if fraud_analysis.is_fraudulent:
            recommendations.append("🚨 Document shows signs of fraud - manual verification required")
        elif fraud_analysis.requires_manual_review:
            recommendations.append("👁️ Manual review recommended for authenticity")
        
        # Default success
        if not recommendations:
            recommendations.append("✅ Passport processed successfully")
        
        return recommendations
    
    def _bytes_to_image(self, image_data: bytes) -> Optional[np.ndarray]:
        """Convert bytes to OpenCV image"""
        try:
            nparr = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            return image
        except Exception as e:
            self.logger.error(f"❌ Failed to decode image: {e}")
            return None
    
    def _create_error_result(self, request: DocumentProcessingRequest, errors: List[str]) -> DocumentExtractionResult:
        """Create error result for passport processing"""
        return DocumentExtractionResult(
            status=ProcessingStatus.FAILED,
            document_type=request.document_type,
            extracted_fields={},
            fraud_analysis=FraudAnalysis(
                is_authentic=False,
                is_fraudulent=False,
                is_altered=False,
                confidence_score=0.0,
                authenticity_score=0.0,
                alteration_score=0.0,
                requires_manual_review=True
            ),
            face_analysis=None,
            validation_result=ValidationResult(
                is_valid=False,
                is_consistent=False,
                errors=errors
            ),
            processing_metadata=ProcessingMetadata(
                processing_method="passport_error",
                processing_time=0.0,
                model_version="1.0.0",
                request_id=request.request_id,
                application_id=request.application_id
            ),
            recommendations=["❌ Passport processing failed - see errors"],
            confidence=0.0
        )
