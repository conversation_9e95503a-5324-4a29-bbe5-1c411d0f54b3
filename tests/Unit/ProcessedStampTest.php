<?php

declare(strict_types=1);

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\ProcessStampMain;
use App\Models\ProcessedStamp;
use App\Models\Application;
use App\Models\AdminUser;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ProcessedStampTest extends TestCase
{
    use RefreshDatabase;

    private ProcessedStamp $processedStamp;
    private ProcessStampMain $processStamp;
    private Application $application;
    private AdminUser $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->application = Application::factory()->create();
        $this->user = AdminUser::factory()->create();
        
        $this->processStamp = ProcessStampMain::create([
            'STAMP_TAG' => 'TEST_STAMP',
            'STAMP_NAME' => 'Test Stamp',
            'CATEGORY' => 'initial',
            'ORDER' => 10,
            'FIELD' => 'Comment'
        ]);

        $this->processedStamp = ProcessedStamp::create([
            'application_id' => $this->application->id,
            'stamp_id' => $this->processStamp->SAMP_ID,
            'user_id' => $this->user->id,
            'status' => 'pending'
        ]);
    }

    public function testIsPendingReturnsTrueForPendingStatus(): void
    {
        $this->assertTrue($this->processedStamp->isPending());
        $this->assertFalse($this->processedStamp->isCompleted());
        $this->assertFalse($this->processedStamp->isSkipped());
    }

    public function testIsCompletedReturnsTrueForCompletedStatus(): void
    {
        $this->processedStamp->update(['status' => 'completed']);

        $this->assertFalse($this->processedStamp->isPending());
        $this->assertTrue($this->processedStamp->isCompleted());
        $this->assertFalse($this->processedStamp->isSkipped());
    }

    public function testIsSkippedReturnsTrueForSkippedStatus(): void
    {
        $this->processedStamp->update(['status' => 'skipped']);

        $this->assertFalse($this->processedStamp->isPending());
        $this->assertFalse($this->processedStamp->isCompleted());
        $this->assertTrue($this->processedStamp->isSkipped());
    }

    public function testMarkAsCompletedUpdatesStatusAndData(): void
    {
        $fieldData = ['value' => 'Test comment'];
        
        $result = $this->processedStamp->markAsCompleted($this->user->id, $fieldData);

        $this->assertTrue($result);
        $this->processedStamp->refresh();
        $this->assertEquals('completed', $this->processedStamp->status);
        $this->assertEquals($fieldData, $this->processedStamp->field_data);
        $this->assertNotNull($this->processedStamp->completed_at);
    }

    public function testMarkAsSkippedUpdatesStatusAndCompletedAt(): void
    {
        $result = $this->processedStamp->markAsSkipped($this->user->id);

        $this->assertTrue($result);
        $this->processedStamp->refresh();
        $this->assertEquals('skipped', $this->processedStamp->status);
        $this->assertNotNull($this->processedStamp->completed_at);
    }

    public function testGetFieldDataReturnsAllDataWhenNoKeyProvided(): void
    {
        $fieldData = ['value' => 'Test comment', 'extra' => 'data'];
        $this->processedStamp->update(['field_data' => $fieldData]);

        $result = $this->processedStamp->getFieldData();

        $this->assertEquals($fieldData, $result);
    }

    public function testGetFieldDataReturnsSpecificValueWhenKeyProvided(): void
    {
        $fieldData = ['value' => 'Test comment', 'extra' => 'data'];
        $this->processedStamp->update(['field_data' => $fieldData]);

        $result = $this->processedStamp->getFieldData('value');

        $this->assertEquals('Test comment', $result);
    }

    public function testGetFieldDataReturnsNullForNonExistentKey(): void
    {
        $fieldData = ['value' => 'Test comment'];
        $this->processedStamp->update(['field_data' => $fieldData]);

        $result = $this->processedStamp->getFieldData('nonexistent');

        $this->assertNull($result);
    }

    public function testSetFieldDataUpdatesFieldData(): void
    {
        $this->processedStamp->setFieldData('test_key', 'test_value');

        $this->assertEquals('test_value', $this->processedStamp->field_data['test_key']);
    }

    public function testSetFieldDataPreservesExistingData(): void
    {
        $this->processedStamp->update(['field_data' => ['existing' => 'data']]);
        $this->processedStamp->setFieldData('new_key', 'new_value');

        $this->assertEquals('data', $this->processedStamp->field_data['existing']);
        $this->assertEquals('new_value', $this->processedStamp->field_data['new_key']);
    }

    public function testValidateFieldDataReturnsEmptyArrayWhenNoField(): void
    {
        $noFieldStamp = ProcessStampMain::create([
            'STAMP_TAG' => 'NO_FIELD_STAMP',
            'STAMP_NAME' => 'No Field Stamp',
            'CATEGORY' => 'initial',
            'ORDER' => 20,
            'FIELD' => null
        ]);

        $processedStamp = ProcessedStamp::create([
            'application_id' => $this->application->id,
            'stamp_id' => $noFieldStamp->SAMP_ID,
            'user_id' => $this->user->id,
            'status' => 'pending'
        ]);

        $errors = $processedStamp->validateFieldData();

        $this->assertEquals([], $errors);
    }

    public function testValidateFieldDataReturnsErrorsForInvalidComment(): void
    {
        $longComment = str_repeat('a', 101);
        $this->processedStamp->update(['field_data' => ['value' => $longComment]]);

        $errors = $this->processedStamp->validateFieldData();

        $this->assertNotEmpty($errors);
        $this->assertStringContainsString('100 characters', $errors[0]);
    }

    public function testValidateFieldDataReturnsErrorsForInvalidDate(): void
    {
        $dateStamp = ProcessStampMain::create([
            'STAMP_TAG' => 'DATE_STAMP',
            'STAMP_NAME' => 'Date Stamp',
            'CATEGORY' => 'initial',
            'ORDER' => 30,
            'FIELD' => 'Date'
        ]);

        $processedStamp = ProcessedStamp::create([
            'application_id' => $this->application->id,
            'stamp_id' => $dateStamp->SAMP_ID,
            'user_id' => $this->user->id,
            'status' => 'pending',
            'field_data' => ['value' => 'invalid-date']
        ]);

        $errors = $processedStamp->validateFieldData();

        $this->assertNotEmpty($errors);
        $this->assertStringContainsString('valid date', $errors[0]);
    }

    public function testScopeForApplicationFiltersCorrectly(): void
    {
        $anotherApplication = Application::factory()->create();
        
        ProcessedStamp::create([
            'application_id' => $anotherApplication->id,
            'stamp_id' => $this->processStamp->SAMP_ID,
            'user_id' => $this->user->id,
            'status' => 'pending'
        ]);

        $stampsForApp = ProcessedStamp::forApplication($this->application->id)->get();
        $stampsForAnotherApp = ProcessedStamp::forApplication($anotherApplication->id)->get();

        $this->assertCount(1, $stampsForApp);
        $this->assertCount(1, $stampsForAnotherApp);
    }

    public function testScopeForStampFiltersCorrectly(): void
    {
        $anotherStamp = ProcessStampMain::create([
            'STAMP_TAG' => 'ANOTHER_STAMP',
            'STAMP_NAME' => 'Another Stamp',
            'CATEGORY' => 'initial',
            'ORDER' => 40,
            'FIELD' => null
        ]);

        ProcessedStamp::create([
            'application_id' => $this->application->id,
            'stamp_id' => $anotherStamp->SAMP_ID,
            'user_id' => $this->user->id,
            'status' => 'pending'
        ]);

        $stampsForStamp = ProcessedStamp::forStamp($this->processStamp->SAMP_ID)->get();
        $stampsForAnotherStamp = ProcessedStamp::forStamp($anotherStamp->SAMP_ID)->get();

        $this->assertCount(1, $stampsForStamp);
        $this->assertCount(1, $stampsForAnotherStamp);
    }
}
