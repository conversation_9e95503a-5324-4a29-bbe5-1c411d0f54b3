<?php

declare(strict_types=1);

namespace Tests\Unit;

use Tests\TestCase;
use App\Modules\Entities\Services\EntityOptionService;
use App\Modules\Entities\Models\Entity;
use App\Modules\Entities\Models\EntityOption;
use App\Modules\Entities\Models\Option;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use SolidFuse\Modules\Entities\Enums\EntityType;

class EntityOptionServiceTest extends TestCase
{
    use RefreshDatabase;

    protected EntityOptionService $service;
    protected Entity $superGroup;
    protected Entity $parentGroup;
    protected Entity $client;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->service = new EntityOptionService();
        $this->createEntityHierarchy();
    }

    private function createEntityHierarchy(): void
    {
        $this->superGroup = Entity::create([
            'name' => 'Test Super Group',
            'entity_code' => 'TSG001',
            'entity_type' => EntityType::SUPER_GROUP,
            'status' => true
        ]);

        $this->parentGroup = Entity::create([
            'name' => 'Test Parent Group',
            'entity_code' => 'TPG001',
            'entity_type' => EntityType::PARENT_GROUP,
            'status' => true
        ]);

        $this->client = Entity::create([
            'name' => 'Test Client',
            'entity_code' => 'TC001',
            'entity_type' => EntityType::CLIENT,
            'status' => true
        ]);

        DB::table('entity_relationships')->insert([
            ['parent_entity_id' => $this->superGroup->id, 'child_entity_id' => $this->parentGroup->id],
            ['parent_entity_id' => $this->parentGroup->id, 'child_entity_id' => $this->client->id]
        ]);
    }

    public function test_get_option_value_direct(): void
    {
        $option = Option::create([
            'name' => 'test_option',
            'description' => 'Test option',
            'option_type' => 'text',
            'section' => 'test',
            'option_value_type' => 'text',
            'sort_order' => 1
        ]);

        EntityOption::create([
            'entity_id' => $this->client->id,
            'option_id' => $option->id,
            'value' => 'direct_value',
            'is_overridden' => true
        ]);

        $result = $this->service->getOptionValue('test_option', $this->client->id);

        $this->assertEquals('direct_value', $result);
    }

    public function test_get_option_value_with_inheritance(): void
    {
        $option = Option::create([
            'name' => 'inherited_option',
            'description' => 'Inherited option',
            'option_type' => 'text',
            'section' => 'test',
            'option_value_type' => 'text',
            'sort_order' => 1
        ]);

        EntityOption::create([
            'entity_id' => $this->parentGroup->id,
            'option_id' => $option->id,
            'value' => 'parent_value',
            'is_overridden' => true
        ]);

        EntityOption::create([
            'entity_id' => $this->client->id,
            'option_id' => $option->id,
            'value' => 'child_value',
            'is_overridden' => false
        ]);

        $result = $this->service->getOptionValue('inherited_option', $this->client->id);

        $this->assertEquals('parent_value', $result);
    }

    public function test_get_option_value_boolean_formatting(): void
    {
        $option = Option::create([
            'name' => 'boolean_option',
            'description' => 'Boolean option',
            'option_type' => 'boolean',
            'section' => 'test',
            'option_value_type' => 'boolean',
            'sort_order' => 1
        ]);

        EntityOption::create([
            'entity_id' => $this->client->id,
            'option_id' => $option->id,
            'value' => 'true',
            'is_overridden' => true
        ]);

        $result = $this->service->getOptionValue('boolean_option', $this->client->id);

        $this->assertTrue($result);
    }

    public function test_get_option_value_numeric_formatting(): void
    {
        $option = Option::create([
            'name' => 'numeric_option',
            'description' => 'Numeric option',
            'option_type' => 'integer',
            'section' => 'test',
            'option_value_type' => 'integer',
            'sort_order' => 1
        ]);

        EntityOption::create([
            'entity_id' => $this->client->id,
            'option_id' => $option->id,
            'value' => '123',
            'is_overridden' => true
        ]);

        $result = $this->service->getOptionValue('numeric_option', $this->client->id);

        $this->assertEquals(123, $result);
    }

    public function test_get_option_with_source_direct(): void
    {
        $option = Option::create([
            'name' => 'test_option',
            'description' => 'Test option',
            'option_type' => 'text',
            'section' => 'test',
            'option_value_type' => 'text',
            'sort_order' => 1
        ]);

        EntityOption::create([
            'entity_id' => $this->client->id,
            'option_id' => $option->id,
            'value' => 'direct_value',
            'is_overridden' => true
        ]);

        $result = $this->service->getOptionWithSource('test_option', $this->client->id);

        $this->assertEquals('direct_value', $result['value']);
        $this->assertEquals('entity', $result['source']);
        $this->assertEquals($this->client->id, $result['source_id']);
        $this->assertEquals('Test Client', $result['source_name']);
    }

    public function test_get_option_with_source_inherited(): void
    {
        $option = Option::create([
            'name' => 'inherited_option',
            'description' => 'Inherited option',
            'option_type' => 'text',
            'section' => 'test',
            'option_value_type' => 'text',
            'sort_order' => 1
        ]);

        EntityOption::create([
            'entity_id' => $this->parentGroup->id,
            'option_id' => $option->id,
            'value' => 'parent_value',
            'is_overridden' => true
        ]);

        EntityOption::create([
            'entity_id' => $this->client->id,
            'option_id' => $option->id,
            'value' => 'child_value',
            'is_overridden' => false
        ]);

        $result = $this->service->getOptionWithSource('inherited_option', $this->client->id);

        $this->assertEquals('parent_value', $result['value']);
        $this->assertEquals('entity', $result['source']);
        $this->assertEquals($this->parentGroup->id, $result['source_id']);
        $this->assertEquals('Test Parent Group', $result['source_name']);
    }

    public function test_get_option_not_found(): void
    {
        $result = $this->service->getOptionValue('non_existent_option', $this->client->id);

        $this->assertNull($result);
    }

    public function test_get_option_entity_not_found(): void
    {
        $result = $this->service->getOptionValue('test_option', 99999);

        $this->assertNull($result);
    }

    public function test_hierarchy_path_in_source_result(): void
    {
        $option = Option::create([
            'name' => 'hierarchy_option',
            'description' => 'Hierarchy option',
            'option_type' => 'text',
            'section' => 'test',
            'option_value_type' => 'text',
            'sort_order' => 1
        ]);

        EntityOption::create([
            'entity_id' => $this->superGroup->id,
            'option_id' => $option->id,
            'value' => 'super_value',
            'is_overridden' => true
        ]);

        EntityOption::create([
            'entity_id' => $this->client->id,
            'option_id' => $option->id,
            'value' => 'client_value',
            'is_overridden' => false
        ]);

        $result = $this->service->getOptionWithSource('hierarchy_option', $this->client->id);

        $this->assertIsArray($result['hierarchy_path']);
        $this->assertCount(3, $result['hierarchy_path']);

        $this->assertEquals($this->client->id, $result['hierarchy_path'][0]['id']);
        $this->assertEquals($this->parentGroup->id, $result['hierarchy_path'][1]['id']);
        $this->assertEquals($this->superGroup->id, $result['hierarchy_path'][2]['id']);
    }
}
