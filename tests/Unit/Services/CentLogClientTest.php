<?php

namespace Tests\Unit\Services;

use App\Services\CentLogClient;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;

use Illuminate\Http\Request as HttpRequest;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;
use Mockery;

class CentLogClientTest extends TestCase
{

    private $mockHttpClient;
    private $centLogClient;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->mockHttpClient = Mockery::mock(Client::class);
        $this->centLogClient = new CentLogClient();
        
        $reflection = new \ReflectionClass($this->centLogClient);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $clientProperty->setValue($this->centLogClient, $this->mockHttpClient);
    }

    public function testLogSendsCorrectDataForAuthSuccess()
    {
        $this->mockRequest();
        
        $this->mockHttpClient
            ->shouldReceive('post')
            ->once()
            ->with('http://localhost:8004/api/logs', Mockery::type('array'))
            ->andReturn(new Response(201));

        $this->centLogClient->log('Auth', '123', [
            'status' => 'success',
            'login_method' => 'password'
        ]);
        
        $this->assertTrue(true);
    }

    public function testLogHandlesRequestException()
    {
        $this->mockRequest();
        
        Log::shouldReceive('warning')
            ->once()
            ->with('CentLog failed to send log', Mockery::type('array'));

        $this->mockHttpClient
            ->shouldReceive('post')
            ->once()
            ->andThrow(new RequestException('Connection failed', new Request('POST', 'test')));

        $this->centLogClient->log('Auth', '123', ['status' => 'success']);
        
        $this->assertTrue(true);
    }

    private function mockRequest()
    {
        $request = Mockery::mock(HttpRequest::class);
        $request->shouldReceive('ip')->andReturn('127.0.0.1');
        $request->shouldReceive('userAgent')->andReturn('TestAgent');
        
        $this->app->instance('request', $request);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
