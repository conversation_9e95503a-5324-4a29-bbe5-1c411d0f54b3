<?php

declare(strict_types=1);

namespace Tests\Unit\Services;

use App\Services\FormCompletionStatusService;
use App\Services\ProcessStampService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class FormCompletionStatusServiceTest extends TestCase
{
    use RefreshDatabase;

    private FormCompletionStatusService $service;
    private ProcessStampService $processStampService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->processStampService = $this->createMock(ProcessStampService::class);
        $this->service = new FormCompletionStatusService($this->processStampService);
    }

    public function testCheckFormCompletionStatusWhenNoStampExists(): void
    {
        $applicationId = 1;

        $result = $this->service->checkFormCompletionStatus($applicationId);

        $this->assertFalse($result['is_completed']);
        $this->assertEquals('not_started', $result['completion_status']);
        $this->assertNull($result['completed_at']);
        $this->assertNull($result['completed_by']);
        $this->assertNull($result['form_data']);
        $this->assertEquals('Form has not been started yet', $result['message']);
    }

    public function testCheckFormCompletionStatusWhenStampIsPending(): void
    {
        $applicationId = 1;
        $this->createProcessStampDefinition();
        $this->createPendingProcessedStamp($applicationId);

        $result = $this->service->checkFormCompletionStatus($applicationId);

        $this->assertFalse($result['is_completed']);
        $this->assertEquals('pending', $result['completion_status']);
        $this->assertNull($result['completed_at']);
        $this->assertNull($result['completed_by']);
        $this->assertEquals('Application form is in progress', $result['message']);
    }

    public function testCheckFormCompletionStatusWhenStampIsCompleted(): void
    {
        $applicationId = 1;
        $userId = 123;
        $completedAt = now();
        
        $this->createProcessStampDefinition();
        $this->createCompletedProcessedStamp($applicationId, $userId, $completedAt);

        $result = $this->service->checkFormCompletionStatus($applicationId);

        $this->assertTrue($result['is_completed']);
        $this->assertEquals('completed', $result['completion_status']);
        $this->assertNotNull($result['completed_at']);
        $this->assertNotNull($result['completed_by']);
        $this->assertEquals($userId, $result['completed_by']['user_id']);
        $this->assertEquals('applicant', $result['completed_by']['user_type']);
        $this->assertEquals('Application form has been completed successfully', $result['message']);
    }

    public function testIsFormCompletedReturnsTrueWhenCompleted(): void
    {
        $applicationId = 1;
        $this->createProcessStampDefinition();
        $this->createCompletedProcessedStamp($applicationId, 123, now());

        $result = $this->service->isFormCompleted($applicationId);

        $this->assertTrue($result);
    }

    public function testIsFormCompletedReturnsFalseWhenPending(): void
    {
        $applicationId = 1;
        $this->createProcessStampDefinition();
        $this->createPendingProcessedStamp($applicationId);

        $result = $this->service->isFormCompleted($applicationId);

        $this->assertFalse($result);
    }

    public function testIsFormCompletedReturnsFalseWhenNoStamp(): void
    {
        $applicationId = 1;

        $result = $this->service->isFormCompleted($applicationId);

        $this->assertFalse($result);
    }

    public function testGetFormCompletionDetailsReturnsNullWhenNotCompleted(): void
    {
        $applicationId = 1;
        $this->createProcessStampDefinition();
        $this->createPendingProcessedStamp($applicationId);

        $result = $this->service->getFormCompletionDetails($applicationId);

        $this->assertNull($result);
    }

    public function testGetFormCompletionDetailsReturnsDataWhenCompleted(): void
    {
        $applicationId = 1;
        $userId = 123;
        $completedAt = now();
        
        $this->createProcessStampDefinition();
        $this->createCompletedProcessedStamp($applicationId, $userId, $completedAt);

        $result = $this->service->getFormCompletionDetails($applicationId);

        $this->assertNotNull($result);
        $this->assertNotNull($result['completed_at']);
        $this->assertNotNull($result['completed_by']);
        $this->assertEquals($userId, $result['completed_by']['user_id']);
    }

    private function createProcessStampDefinition(): void
    {
        DB::table('process_stamps_main')->insert([
            'SAMP_ID' => 1,
            'STAMP_TAG' => 'APPLICANT_FORM_COMPLETED',
            'STAMP_NAME' => 'Applicant Form Completed',
            'CATEGORY' => 'APPLICATION',
            'ORDER' => 1,
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }

    private function createPendingProcessedStamp(int $applicationId): void
    {
        DB::table('processed_stamps')->insert([
            'application_id' => $applicationId,
            'stamp_id' => 1,
            'user_id' => null,
            'status' => 'pending',
            'field_data' => null,
            'completed_at' => null,
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }

    private function createCompletedProcessedStamp(int $applicationId, int $userId, $completedAt): void
    {
        $fieldData = json_encode([
            'form_data_saved' => true,
            'saved_at' => $completedAt->toISOString(),
            'saved_by_user_id' => $userId,
            'saved_by_user_type' => 'applicant'
        ]);

        DB::table('processed_stamps')->insert([
            'application_id' => $applicationId,
            'stamp_id' => 1,
            'user_id' => 1,
            'status' => 'completed',
            'field_data' => $fieldData,
            'completed_at' => $completedAt,
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }
}
