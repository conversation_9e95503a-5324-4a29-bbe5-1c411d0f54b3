<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Modules\Auth\Models\PortalUser;
use App\Modules\Auth\Services\TwoFactorService;
use App\Modules\Users\Models\Profile;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TwoFactorAuthTest extends TestCase
{
    use RefreshDatabase;

    private PortalUser $user;
    private TwoFactorService $twoFactorService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->twoFactorService = app(TwoFactorService::class);
        
        // Create a test user
        $this->user = PortalUser::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'client_user'
        ]);

        // Create profile
        Profile::create([
            'user_id' => $this->user->id,
            'first_name' => 'Test',
            'last_name' => 'User',
            'active' => true,
            'two_factor_enabled' => false,
        ]);
    }

    public function test_can_generate_qr_code_for_2fa_setup(): void
    {
        $token = $this->user->createToken('test')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/v1/auth/2fa/qr-code');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'secret',
                    'qr_code_image',
                    'manual_entry_key'
                ],
                'message'
            ]);
    }

    public function test_can_check_2fa_status(): void
    {
        $token = $this->user->createToken('test')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/v1/auth/2fa/status');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'two_factor_enabled' => false
                ]
            ]);
    }

    public function test_login_without_2fa_when_disabled(): void
    {
        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password'
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'user',
                    'entities',
                    'token',
                    'token_type'
                ],
                'message'
            ]);
    }

    public function test_login_requires_2fa_when_enabled(): void
    {
        // Enable 2FA for user
        $this->user->profile->update([
            'two_factor_enabled' => true,
            'two_factor_secret' => encrypt('TESTSECRET123456')
        ]);

        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'requires_two_factor' => true
                ]
            ]);
    }

    public function test_two_factor_service_can_generate_secret(): void
    {
        $secret = $this->twoFactorService->generateSecret();
        
        $this->assertIsString($secret);
        $this->assertGreaterThan(10, strlen($secret));
    }

    public function test_two_factor_service_can_generate_qr_code_image(): void
    {
        $secret = 'TESTSECRET123456';
        $qrCodeImage = $this->twoFactorService->generateQrCodeImage($this->user, $secret);

        $this->assertIsString($qrCodeImage);
        $this->assertStringStartsWith('data:image/svg+xml;base64,', $qrCodeImage);

        // Decode the base64 to verify it's a valid SVG
        $base64Data = substr($qrCodeImage, strlen('data:image/svg+xml;base64,'));
        $decodedData = base64_decode($base64Data);
        $this->assertNotFalse($decodedData);
        $this->assertStringContainsString('<svg', $decodedData); // SVG tag
        $this->assertStringContainsString('</svg>', $decodedData); // SVG closing tag
    }
}
