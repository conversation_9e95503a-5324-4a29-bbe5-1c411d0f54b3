<?php

declare(strict_types=1);

namespace Tests\Feature;

use Tests\TestCase;
use App\Modules\Auth\Models\PortalUser;
use App\Modules\Entities\Models\Entity;
use App\Modules\Entities\SubModules\JobRoles\Models\JobRole;
use Laravel\Sanctum\Sanctum;
use Illuminate\Foundation\Testing\RefreshDatabase;

class EntityJobRolesRouteTest extends TestCase
{
    use RefreshDatabase;

    private PortalUser $clientUser;
    private Entity $entity;
    private JobRole $jobRole;

    protected function setUp(): void
    {
        parent::setUp();

        $this->entity = Entity::factory()->create([
            'name' => 'Test Entity',
            'entity_code' => 'TEST001',
            'entity_type' => 'client',
            'status' => true
        ]);

        $this->clientUser = PortalUser::factory()->create([
            'email' => '<EMAIL>',
            'user_type' => 'client'
        ]);

        $this->clientUser->entities()->attach($this->entity->id, ['role' => 'admin']);

        $this->jobRole = JobRole::factory()->create([
            'entity_id' => $this->entity->id,
            'job_label' => 'Test Job Role',
            'job_title' => 'Test Job Title',
            'job_workforce' => 'Test Workforce'
        ]);
    }

    public function test_entity_job_roles_route_exists(): void
    {
        Sanctum::actingAs($this->clientUser);

        $response = $this->getJson("/api/v1/entities/{$this->entity->id}/job-roles");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'entity' => [
                        'id',
                        'name',
                        'entity_code',
                        'entity_type'
                    ],
                    'job_roles',
                    'total_count'
                ]
            ]);
    }

    public function test_entity_job_roles_own_route_exists(): void
    {
        Sanctum::actingAs($this->clientUser);

        $response = $this->getJson("/api/v1/entities/{$this->entity->id}/job-roles/own");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'entity' => [
                        'id',
                        'name',
                        'entity_code',
                        'entity_type'
                    ],
                    'job_roles',
                    'total_count'
                ]
            ]);
    }

    public function test_entity_job_roles_requires_authentication(): void
    {
        $response = $this->getJson("/api/v1/entities/{$this->entity->id}/job-roles");

        $response->assertStatus(401);
    }

    public function test_entity_job_roles_requires_entity_access(): void
    {
        $otherUser = PortalUser::factory()->create([
            'email' => '<EMAIL>',
            'user_type' => 'client'
        ]);

        Sanctum::actingAs($otherUser);

        $response = $this->getJson("/api/v1/entities/{$this->entity->id}/job-roles");

        $response->assertStatus(403)
            ->assertJson([
                'success' => false,
                'message' => 'Access denied to this entity'
            ]);
    }

    public function test_entity_not_found_returns_404(): void
    {
        Sanctum::actingAs($this->clientUser);

        $response = $this->getJson("/api/v1/entities/999999/job-roles");

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'Entity not found'
            ]);
    }
}
