<?php

declare(strict_types=1);

namespace Tests\Feature;

use Tests\TestCase;
use App\Modules\Auth\Models\PortalUser;
use App\Modules\Entities\Models\Entity;
use App\Modules\Entities\SubModules\JobRoles\Models\JobRole;
use App\Modules\Products\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;

class CreateJobRoleTest extends TestCase
{
    use RefreshDatabase;

    private PortalUser $clientUser;
    private PortalUser $applicantUser;
    private Entity $entity;
    private Product $product;

    protected function setUp(): void
    {
        parent::setUp();

        $this->entity = Entity::factory()->create([
            'name' => 'Test Entity',
            'entity_code' => 'TEST001',
            'entity_type' => 'client',
            'status' => true
        ]);

        $this->clientUser = PortalUser::factory()->create([
            'email' => '<EMAIL>',
            'user_type' => 'client_user'
        ]);

        $this->applicantUser = PortalUser::factory()->create([
            'email' => '<EMAIL>',
            'user_type' => 'applicant'
        ]);

        $this->clientUser->entities()->attach($this->entity->id, ['role' => 'admin']);

        $this->product = Product::factory()->create([
            'name' => 'Test Product',
            'code' => 'PROD001'
        ]);
    }

    public function test_client_user_can_create_job_role_for_accessible_entity(): void
    {
        Sanctum::actingAs($this->clientUser);

        $jobRoleData = [
            'entity_id' => $this->entity->id,
            'job_label' => 'Software Developer',
            'job_title' => 'Senior Software Developer',
            'job_workforce' => 'Technology',
            'role_description' => 'Develop and maintain software applications',
            'self_payment' => false,
            'employment_sector' => 'Technology',
            'product_ids' => [$this->product->id]
        ];

        $response = $this->postJson('/api/v1/job-roles', $jobRoleData);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Job role created successfully'
            ])
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'entity_id',
                    'job_label',
                    'job_title',
                    'job_workforce',
                    'role_description',
                    'self_payment',
                    'employment_sector',
                    'entity',
                    'products'
                ]
            ]);

        $this->assertDatabaseHas('job_roles', [
            'entity_id' => $this->entity->id,
            'job_label' => 'Software Developer',
            'job_title' => 'Senior Software Developer',
            'job_workforce' => 'Technology'
        ]);
    }

    public function test_applicant_user_cannot_create_job_role(): void
    {
        Sanctum::actingAs($this->applicantUser);

        $jobRoleData = [
            'entity_id' => $this->entity->id,
            'job_label' => 'Software Developer',
            'job_title' => 'Senior Software Developer',
            'job_workforce' => 'Technology'
        ];

        $response = $this->postJson('/api/v1/job-roles', $jobRoleData);

        $response->assertStatus(403)
            ->assertJson([
                'success' => false,
                'message' => 'Only client users can create job roles'
            ]);
    }

    public function test_client_user_cannot_create_job_role_for_inaccessible_entity(): void
    {
        $otherEntity = Entity::factory()->create([
            'name' => 'Other Entity',
            'entity_code' => 'OTHER001',
            'entity_type' => 'client',
            'status' => true
        ]);

        Sanctum::actingAs($this->clientUser);

        $jobRoleData = [
            'entity_id' => $otherEntity->id,
            'job_label' => 'Software Developer',
            'job_title' => 'Senior Software Developer',
            'job_workforce' => 'Technology'
        ];

        $response = $this->postJson('/api/v1/job-roles', $jobRoleData);

        $response->assertStatus(403)
            ->assertJson([
                'success' => false,
                'message' => 'You do not have access to create job roles for this entity'
            ]);
    }

    public function test_create_job_role_requires_authentication(): void
    {
        $jobRoleData = [
            'entity_id' => $this->entity->id,
            'job_label' => 'Software Developer',
            'job_title' => 'Senior Software Developer',
            'job_workforce' => 'Technology'
        ];

        $response = $this->postJson('/api/v1/job-roles', $jobRoleData);

        $response->assertStatus(401);
    }

    public function test_create_job_role_validates_required_fields(): void
    {
        Sanctum::actingAs($this->clientUser);

        $response = $this->postJson('/api/v1/job-roles', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'entity_id',
                'job_label',
                'job_title',
                'job_workforce'
            ]);
    }

    public function test_create_job_role_validates_entity_exists(): void
    {
        Sanctum::actingAs($this->clientUser);

        $jobRoleData = [
            'entity_id' => 99999,
            'job_label' => 'Software Developer',
            'job_title' => 'Senior Software Developer',
            'job_workforce' => 'Technology'
        ];

        $response = $this->postJson('/api/v1/job-roles', $jobRoleData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['entity_id']);
    }

    public function test_create_job_role_with_products(): void
    {
        Sanctum::actingAs($this->clientUser);

        $product2 = Product::factory()->create([
            'name' => 'Test Product 2',
            'product_code' => 'PROD002'
        ]);

        $jobRoleData = [
            'entity_id' => $this->entity->id,
            'job_label' => 'Software Developer',
            'job_title' => 'Senior Software Developer',
            'job_workforce' => 'Technology',
            'product_ids' => [$this->product->id, $product2->id]
        ];

        $response = $this->postJson('/api/v1/job-roles', $jobRoleData);

        $response->assertStatus(200);

        $jobRole = JobRole::where('job_label', 'Software Developer')->first();
        $this->assertCount(2, $jobRole->products);
        $this->assertTrue($jobRole->products->contains($this->product));
        $this->assertTrue($jobRole->products->contains($product2));
    }
}
