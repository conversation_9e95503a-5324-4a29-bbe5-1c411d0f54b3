<?php

declare(strict_types=1);

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Modules\Applications\Models\Application;
use App\Modules\Auth\Models\PortalUser;
use App\Modules\Products\Models\Product;
use App\Modules\Products\Models\ProductFormField;
use App\Services\ProcessStampService;
use Illuminate\Support\Facades\DB;

class SaveApplicationDataProcessStampTest extends TestCase
{
    use RefreshDatabase;

    private Application $application;
    private PortalUser $applicant;
    private Product $product;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->product = Product::factory()->create();
        $this->applicant = PortalUser::factory()->create(['user_type' => 'applicant']);
        $this->application = Application::factory()->create([
            'applicant_id' => $this->applicant->id,
            'product_id' => $this->product->id
        ]);

        ProductFormField::factory()->create([
            'product_id' => $this->product->id,
            'field_key' => 'ApplicantDetails::TITLE',
            'type' => 'select',
            'required' => true,
            'validation_rules' => ['required', 'in:MR,MS,MRS,DR']
        ]);

        ProductFormField::factory()->create([
            'product_id' => $this->product->id,
            'field_key' => 'ApplicantDetails::FORENAME',
            'type' => 'text',
            'required' => true,
            'validation_rules' => ['required', 'string', 'max:50']
        ]);

        $this->createProcessStampDefinition();
        $this->initializeApplicationStamps();
    }

    private function createProcessStampDefinition(): void
    {
        // The APPLICANT_FORM_COMPLETED stamp already exists in the database
        // We just need to ensure there's a link to our test product
        $stamp = DB::table('process_stamps_main')
            ->where('STAMP_TAG', 'APPLICANT_FORM_COMPLETED')
            ->first();

        if ($stamp) {
            // Check if link exists, if not create it
            $linkExists = DB::table('process_stamp_links')
                ->where('STAMP_ID', $stamp->SAMP_ID)
                ->where('PRODUCT_ID', $this->product->id)
                ->exists();

            if (!$linkExists) {
                DB::table('process_stamp_links')->insert([
                    'STAMP_ID' => $stamp->SAMP_ID,
                    'PRODUCT_ID' => $this->product->id,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
        }
    }

    private function initializeApplicationStamps(): void
    {
        $processStampService = app(ProcessStampService::class);
        $processStampService->initializeStampsForApplication(
            $this->application->id,
            $this->product->id
        );
    }

    public function testSaveApplicationDataCompletesProcessStamp(): void
    {
        $token = $this->applicant->createToken('test')->plainTextToken;

        $formData = [
            'ApplicantDetails::TITLE' => 'MR',
            'ApplicantDetails::FORENAME' => 'John'
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/v1/applications/save-data', [
            'application_id' => $this->application->id,
            'form_data' => $formData
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Application data saved successfully',
                'data' => [
                    'application_id' => $this->application->id
                ]
            ]);

        $processStamp = DB::table('processed_stamps')
            ->join('process_stamps_main', 'processed_stamps.stamp_id', '=', 'process_stamps_main.SAMP_ID')
            ->where('processed_stamps.application_id', $this->application->id)
            ->where('process_stamps_main.STAMP_TAG', 'APPLICANT_FORM_COMPLETED')
            ->select('processed_stamps.*')
            ->first();

        $this->assertNotNull($processStamp);
        $this->assertEquals('completed', $processStamp->status);
        $this->assertEquals(1, $processStamp->user_id); // Admin user ID
        $this->assertNotNull($processStamp->completed_at);

        $stampData = json_decode($processStamp->field_data, true);
        $this->assertTrue($stampData['form_data_saved']);
        $this->assertArrayHasKey('saved_at', $stampData);
        $this->assertEquals($this->applicant->id, $stampData['saved_by_user_id']);
        $this->assertEquals('applicant', $stampData['saved_by_user_type']);
    }

    public function testSaveApplicationDataWithValidationErrorDoesNotCompleteStamp(): void
    {
        $token = $this->applicant->createToken('test')->plainTextToken;

        $formData = [
            'ApplicantDetails::TITLE' => 'INVALID_TITLE',
            'ApplicantDetails::FORENAME' => ''
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/v1/applications/save-data', [
            'application_id' => $this->application->id,
            'form_data' => $formData
        ]);

        $response->assertStatus(422);

        $processStamp = DB::table('processed_stamps')
            ->join('process_stamps_main', 'processed_stamps.stamp_id', '=', 'process_stamps_main.SAMP_ID')
            ->where('processed_stamps.application_id', $this->application->id)
            ->where('process_stamps_main.STAMP_TAG', 'APPLICANT_FORM_COMPLETED')
            ->select('processed_stamps.*')
            ->first();

        $this->assertNotNull($processStamp);
        $this->assertEquals('pending', $processStamp->status);
        $this->assertNull($processStamp->completed_at);
        $this->assertNull($processStamp->user_id);
    }

    public function testSaveApplicationDataWithClientUserCompletesStamp(): void
    {
        $clientUser = PortalUser::factory()->create(['user_type' => 'client_user']);
        $token = $clientUser->createToken('test')->plainTextToken;

        $formData = [
            'ApplicantDetails::TITLE' => 'MS',
            'ApplicantDetails::FORENAME' => 'Jane'
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/v1/applications/save-data', [
            'application_id' => $this->application->id,
            'form_data' => $formData
        ]);

        $response->assertStatus(200);

        $processStamp = DB::table('processed_stamps')
            ->join('process_stamps_main', 'processed_stamps.stamp_id', '=', 'process_stamps_main.SAMP_ID')
            ->where('processed_stamps.application_id', $this->application->id)
            ->where('process_stamps_main.STAMP_TAG', 'APPLICANT_FORM_COMPLETED')
            ->select('processed_stamps.*')
            ->first();

        $this->assertNotNull($processStamp);
        $this->assertEquals('completed', $processStamp->status);
        $this->assertEquals(1, $processStamp->user_id); // Admin user ID

        $stampData = json_decode($processStamp->field_data, true);
        $this->assertEquals($clientUser->id, $stampData['saved_by_user_id']);
        $this->assertEquals('client_user', $stampData['saved_by_user_type']);
    }

    public function testProcessStampIsNotDuplicatedOnMultipleSaves(): void
    {
        $token = $this->applicant->createToken('test')->plainTextToken;

        $formData = [
            'ApplicantDetails::TITLE' => 'MR',
            'ApplicantDetails::FORENAME' => 'John'
        ];

        $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/v1/applications/save-data', [
            'application_id' => $this->application->id,
            'form_data' => $formData
        ]);

        $formData['ApplicantDetails::FORENAME'] = 'Johnny';

        $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/v1/applications/save-data', [
            'application_id' => $this->application->id,
            'form_data' => $formData
        ]);

        $processStampCount = DB::table('processed_stamps')
            ->join('process_stamps_main', 'processed_stamps.stamp_id', '=', 'process_stamps_main.SAMP_ID')
            ->where('processed_stamps.application_id', $this->application->id)
            ->where('process_stamps_main.STAMP_TAG', 'APPLICANT_FORM_COMPLETED')
            ->count();

        $this->assertEquals(1, $processStampCount);

        $processStamp = DB::table('processed_stamps')
            ->join('process_stamps_main', 'processed_stamps.stamp_id', '=', 'process_stamps_main.SAMP_ID')
            ->where('processed_stamps.application_id', $this->application->id)
            ->where('process_stamps_main.STAMP_TAG', 'APPLICANT_FORM_COMPLETED')
            ->select('processed_stamps.*')
            ->first();

        $this->assertEquals('completed', $processStamp->status);
    }
}
