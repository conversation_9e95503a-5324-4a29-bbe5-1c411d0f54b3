<?php

declare(strict_types=1);

namespace Tests\Feature;

use Tests\TestCase;
use App\Modules\Auth\Models\PortalUser;
use App\Modules\Entities\Models\Entity;
use App\Modules\Entities\Models\EntityOption;
use App\Modules\Entities\Models\Option;
use App\Modules\Users\Models\Profile;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Laravel\Sanctum\Sanctum;
use SolidFuse\Modules\Entities\Enums\EntityType;

class EntityOptionTest extends TestCase
{
    use RefreshDatabase;

    protected PortalUser $user;
    protected Entity $superGroup;
    protected Entity $parentGroup;
    protected Entity $client;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->createEntityHierarchy();
        $this->createUserWithAccess();
    }

    private function createEntityHierarchy(): void
    {
        $this->superGroup = Entity::create([
            'name' => 'Test Super Group',
            'entity_code' => 'TSG001',
            'entity_type' => EntityType::SUPER_GROUP,
            'status' => true
        ]);

        $this->parentGroup = Entity::create([
            'name' => 'Test Parent Group',
            'entity_code' => 'TPG001',
            'entity_type' => EntityType::PARENT_GROUP,
            'status' => true
        ]);

        $this->client = Entity::create([
            'name' => 'Test Client',
            'entity_code' => 'TC001',
            'entity_type' => EntityType::CLIENT,
            'status' => true
        ]);

        DB::table('entity_relationships')->insert([
            ['parent_entity_id' => $this->superGroup->id, 'child_entity_id' => $this->parentGroup->id],
            ['parent_entity_id' => $this->parentGroup->id, 'child_entity_id' => $this->client->id]
        ]);
    }

    private function createUserWithAccess(): void
    {
        $this->user = PortalUser::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'client_user',
            'email_verified_at' => now()
        ]);

        Profile::create([
            'user_id' => $this->user->id,
            'first_name' => 'Test',
            'last_name' => 'User',
            'active' => true
        ]);

        DB::table('entity_user_links')->insert([
            'user_id' => $this->user->id,
            'entity_id' => $this->client->id,
            'role' => 'admin'
        ]);
    }

    public function test_get_option_with_direct_value(): void
    {
        $option = Option::create([
            'name' => 'allow_applicant_registration',
            'description' => 'Allow applicant registration',
            'option_type' => 'boolean',
            'section' => 'general',
            'option_value_type' => 'boolean',
            'sort_order' => 1
        ]);

        EntityOption::create([
            'entity_id' => $this->client->id,
            'option_id' => $option->id,
            'value' => 'true',
            'is_overridden' => true
        ]);

        Sanctum::actingAs($this->user);

        $response = $this->getJson("/api/v1/entities/{$this->client->id}/option/allow_applicant_registration");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => true
            ]);
    }

    public function test_get_option_with_inheritance(): void
    {
        $option = Option::create([
            'name' => 'allow_applicant_registration',
            'description' => 'Allow applicant registration',
            'option_type' => 'boolean',
            'section' => 'general',
            'option_value_type' => 'boolean',
            'sort_order' => 1
        ]);

        EntityOption::create([
            'entity_id' => $this->parentGroup->id,
            'option_id' => $option->id,
            'value' => 'true',
            'is_overridden' => true
        ]);

        EntityOption::create([
            'entity_id' => $this->client->id,
            'option_id' => $option->id,
            'value' => 'false',
            'is_overridden' => false
        ]);

        Sanctum::actingAs($this->user);

        $response = $this->getJson("/api/v1/entities/{$this->client->id}/option/allow_applicant_registration?include_source=true");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'option_value' => true,
                    'source' => 'entity',
                    'source_entity' => [
                        'id' => $this->parentGroup->id,
                        'name' => 'Test Parent Group'
                    ]
                ]
            ]);
    }

    public function test_get_multiple_options(): void
    {
        $option1 = Option::create([
            'name' => 'allow_applicant_registration',
            'description' => 'Allow applicant registration',
            'option_type' => 'boolean',
            'section' => 'general',
            'option_value_type' => 'boolean',
            'sort_order' => 1
        ]);

        $option2 = Option::create([
            'name' => 'max_applications_per_day',
            'description' => 'Maximum applications per day',
            'option_type' => 'integer',
            'section' => 'limits',
            'option_value_type' => 'integer',
            'sort_order' => 2
        ]);

        EntityOption::create([
            'entity_id' => $this->client->id,
            'option_id' => $option1->id,
            'value' => 'true',
            'is_overridden' => true
        ]);

        EntityOption::create([
            'entity_id' => $this->client->id,
            'option_id' => $option2->id,
            'value' => '100',
            'is_overridden' => true
        ]);

        Sanctum::actingAs($this->user);

        $response = $this->postJson("/api/v1/entities/{$this->client->id}/options", [
            'allow_applicant_registration', 'max_applications_per_day'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'options' => [
                        'allow_applicant_registration' => true,
                        'max_applications_per_day' => 100
                    ]
                ]
            ]);
    }

    public function test_access_denied_for_unauthorized_entity(): void
    {
        $unauthorizedEntity = Entity::create([
            'name' => 'Unauthorized Entity',
            'entity_code' => 'UE001',
            'entity_type' => EntityType::CLIENT,
            'status' => true
        ]);

        Sanctum::actingAs($this->user);

        $response = $this->getJson("/api/v1/entities/{$unauthorizedEntity->id}/option/test_option");

        $response->assertStatus(403)
            ->assertJson([
                'success' => false,
                'message' => 'Access denied to this entity'
            ]);
    }

    public function test_get_accessible_entities(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->getJson('/api/v1/entities/accessible');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'entities' => [
                        '*' => [
                            'id',
                            'name',
                            'entity_code',
                            'entity_type'
                        ]
                    ],
                    'total_count'
                ]
            ]);

        $entities = $response->json('data.entities');
        $entityIds = collect($entities)->pluck('id')->toArray();
        
        $this->assertContains($this->client->id, $entityIds);
    }

    public function test_option_not_found_returns_null(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->getJson("/api/v1/entities/{$this->client->id}/option/non_existent_option");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => null
            ]);
    }

    public function test_validation_error_for_invalid_request_body(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson("/api/v1/entities/{$this->client->id}/options", [
            'invalid' => 'data'
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['option_names']);
    }

    public function test_entity_not_found(): void
    {
        Sanctum::actingAs($this->user);

        $response = $this->getJson("/api/v1/entities/99999/option/test_option");

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'Entity not found'
            ]);
    }
}
