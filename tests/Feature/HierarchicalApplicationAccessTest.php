<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Modules\Applications\Models\Application;
use App\Modules\Applications\Services\ApplicationAccessService;
use App\Modules\Auth\Models\PortalUser;
use App\Modules\Entities\Models\Entity;
use App\Modules\Products\Models\Product;
use App\Modules\Users\Models\Profile;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class HierarchicalApplicationAccessTest extends TestCase
{
    use RefreshDatabase;

    private PortalUser $applicant;
    private PortalUser $clientUser;
    private PortalUser $parentGroupUser;
    private PortalUser $superGroupUser;
    private Entity $superGroup;
    private Entity $parentGroup;
    private Entity $client;
    private Product $product;
    private Application $application;
    private ApplicationAccessService $accessService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->accessService = app(ApplicationAccessService::class);
        
        // Create entity hierarchy: Super Group -> Parent Group -> Client
        $this->superGroup = Entity::create([
            'name' => 'Super Group',
            'entity_code' => 'SUPER001',
            'entity_type' => 'super_group',
            'status' => true
        ]);

        $this->parentGroup = Entity::create([
            'name' => 'Parent Group',
            'entity_code' => 'PARENT001',
            'entity_type' => 'parent_group',
            'status' => true
        ]);

        $this->client = Entity::create([
            'name' => 'Client Entity',
            'entity_code' => 'CLIENT001',
            'entity_type' => 'client',
            'status' => true
        ]);

        // Create entity relationships
        DB::table('entity_relationships')->insert([
            'parent_entity_id' => $this->superGroup->id,
            'child_entity_id' => $this->parentGroup->id,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        DB::table('entity_relationships')->insert([
            'parent_entity_id' => $this->parentGroup->id,
            'child_entity_id' => $this->client->id,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Create users
        $this->applicant = $this->createUser('<EMAIL>', 'applicant');
        $this->clientUser = $this->createUser('<EMAIL>', 'client_user');
        $this->parentGroupUser = $this->createUser('<EMAIL>', 'client_user');
        $this->superGroupUser = $this->createUser('<EMAIL>', 'client_user');

        // Associate users with entities
        $this->clientUser->entities()->attach($this->client->id);
        $this->parentGroupUser->entities()->attach($this->parentGroup->id);
        $this->superGroupUser->entities()->attach($this->superGroup->id);

        // Create product and application
        $this->product = Product::create([
            'name' => 'Test Product',
            'code' => 'PROD001',
            'variant' => 'A'
        ]);

        $this->application = Application::create([
            'entity_id' => $this->client->id,
            'product_id' => $this->product->id,
            'applicant_id' => $this->applicant->id,
            'submitted_by' => $this->clientUser->id,
            'external_reference' => 'APP001',
            'status' => 'pending',
        ]);
    }

    private function createUser(string $email, string $userType): PortalUser
    {
        $user = PortalUser::create([
            'email' => $email,
            'password' => bcrypt('password'),
            'user_type' => $userType
        ]);

        Profile::create([
            'user_id' => $user->id,
            'first_name' => 'Test',
            'last_name' => 'User',
            'active' => true,
        ]);

        return $user;
    }

    public function test_applicant_can_access_own_applications(): void
    {
        $canAccess = $this->accessService->canUserAccessApplicantApplications($this->applicant, $this->applicant);
        $this->assertTrue($canAccess);
    }

    public function test_client_user_can_access_applications_from_their_entity(): void
    {
        $canAccess = $this->accessService->canUserAccessApplicantApplications($this->clientUser, $this->applicant);
        $this->assertTrue($canAccess);
    }

    public function test_parent_group_user_can_access_applications_from_child_entities(): void
    {
        $canAccess = $this->accessService->canUserAccessApplicantApplications($this->parentGroupUser, $this->applicant);
        $this->assertTrue($canAccess);
    }

    public function test_super_group_user_can_access_applications_from_all_child_entities(): void
    {
        $canAccess = $this->accessService->canUserAccessApplicantApplications($this->superGroupUser, $this->applicant);
        $this->assertTrue($canAccess);
    }

    public function test_user_cannot_access_applications_outside_hierarchy(): void
    {
        // Create another client not in the hierarchy
        $otherClient = Entity::create([
            'name' => 'Other Client',
            'entity_code' => 'OTHER001',
            'entity_type' => 'client',
            'status' => true
        ]);

        $otherUser = $this->createUser('<EMAIL>', 'client_user');
        $otherUser->entities()->attach($otherClient->id);

        $canAccess = $this->accessService->canUserAccessApplicantApplications($otherUser, $this->applicant);
        $this->assertFalse($canAccess);
    }

    public function test_get_user_accessible_entity_ids(): void
    {
        // Client user should have access to their own entity
        $clientAccessibleIds = $this->accessService->getUserAccessibleEntityIds($this->clientUser);
        $this->assertContains($this->client->id, $clientAccessibleIds);

        // Parent group user should have access to parent group and client
        $parentAccessibleIds = $this->accessService->getUserAccessibleEntityIds($this->parentGroupUser);
        $this->assertContains($this->parentGroup->id, $parentAccessibleIds);
        $this->assertContains($this->client->id, $parentAccessibleIds);

        // Super group user should have access to all entities
        $superAccessibleIds = $this->accessService->getUserAccessibleEntityIds($this->superGroupUser);
        $this->assertContains($this->superGroup->id, $superAccessibleIds);
        $this->assertContains($this->parentGroup->id, $superAccessibleIds);
        $this->assertContains($this->client->id, $superAccessibleIds);
    }

    public function test_can_user_access_specific_application(): void
    {
        // Applicant can access their own application
        $this->assertTrue($this->accessService->canUserAccessApplication($this->applicant, $this->application->id));

        // Client user can access application from their entity
        $this->assertTrue($this->accessService->canUserAccessApplication($this->clientUser, $this->application->id));

        // Parent group user can access application from child entity
        $this->assertTrue($this->accessService->canUserAccessApplication($this->parentGroupUser, $this->application->id));

        // Super group user can access application from any child entity
        $this->assertTrue($this->accessService->canUserAccessApplication($this->superGroupUser, $this->application->id));
    }



    public function test_invalid_user_type_access_denied(): void
    {
        // Create user with invalid type
        $invalidUser = PortalUser::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'invalid_type'
        ]);

        $token = $invalidUser->createToken('test')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/v1/dashboard');

        $response->assertStatus(403)
            ->assertJson([
                'success' => false,
                'message' => 'Access denied for this user type'
            ]);
    }
}
