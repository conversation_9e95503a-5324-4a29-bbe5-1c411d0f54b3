<?php

namespace Tests\Feature;

use Tests\TestCase;
use Livewire\Livewire;
use App\Core\Dashboard\Dashboard;
use App\Modules\Auth\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

class DashboardTabManagementTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test admin user and authenticate
        $user = User::create([
            'name' => 'Test Admin',
            'username' => 'testadmin',
            'password' => 'password'
        ]);
        $this->actingAs($user);
    }

    public function test_dashboard_component_loads_successfully()
    {
        Livewire::test(Dashboard::class)
            ->assertSuccessful()
            ->assertViewIs('dashboard::dashboard')
            ->assertSet('activeTab', 'dashboard.home-d41d8cd98f00b204e9800998ecf8427e');
    }

    public function test_can_open_new_tab()
    {
        Livewire::test(Dashboard::class)
            ->call('openTab', 'test-component', 'Test Tab', [])
            ->assertSuccessful()
            ->assertSet('activeTab', 'test-component-d41d8cd98f00b204e9800998ecf8427e')
            ->assertCount('tabs', 2);
    }

    public function test_can_switch_between_tabs()
    {
        $component = Livewire::test(Dashboard::class)
            ->call('openTab', 'test-component', 'Test Tab', [])
            ->assertSuccessful();

        $homeTabKey = 'dashboard.home-d41d8cd98f00b204e9800998ecf8427e';
        $testTabKey = 'test-component-d41d8cd98f00b204e9800998ecf8427e';

        $component
            ->call('switchTab', $homeTabKey)
            ->assertSet('activeTab', $homeTabKey)
            ->call('switchTab', $testTabKey)
            ->assertSet('activeTab', $testTabKey);
    }

    public function test_can_close_tab_and_switches_to_remaining_tab()
    {
        $component = Livewire::test(Dashboard::class)
            ->call('openTab', 'test-component', 'Test Tab', [])
            ->assertCount('tabs', 2);

        $testTabKey = 'test-component-d41d8cd98f00b204e9800998ecf8427e';
        $homeTabKey = 'dashboard.home-d41d8cd98f00b204e9800998ecf8427e';

        $component
            ->call('closeTab', $testTabKey)
            ->assertCount('tabs', 1)
            ->assertSet('activeTab', $homeTabKey);
    }

    public function test_closing_active_tab_switches_to_first_remaining_tab()
    {
        $component = Livewire::test(Dashboard::class)
            ->call('openTab', 'test-component-1', 'Test Tab 1', [])
            ->call('openTab', 'test-component-2', 'Test Tab 2', [])
            ->assertCount('tabs', 3);

        $activeTabKey = 'test-component-2-d41d8cd98f00b204e9800998ecf8427e';
        $firstTabKey = 'dashboard.home-d41d8cd98f00b204e9800998ecf8427e';

        $component
            ->assertSet('activeTab', $activeTabKey)
            ->call('closeTab', $activeTabKey)
            ->assertCount('tabs', 2)
            ->assertSet('activeTab', $firstTabKey);
    }

    public function test_closing_last_tab_opens_home_tab()
    {
        $component = Livewire::test(Dashboard::class);
        
        $homeTabKey = 'dashboard.home-d41d8cd98f00b204e9800998ecf8427e';

        $component
            ->call('closeTab', $homeTabKey)
            ->assertCount('tabs', 1)
            ->assertSet('activeTab', 'dashboard.home-d41d8cd98f00b204e9800998ecf8427e');
    }

    public function test_duplicate_tabs_are_not_created()
    {
        $component = Livewire::test(Dashboard::class)
            ->call('openTab', 'test-component', 'Test Tab', ['param' => 'value'])
            ->assertCount('tabs', 2);

        $component
            ->call('openTab', 'test-component', 'Test Tab', ['param' => 'value'])
            ->assertCount('tabs', 2);
    }

    public function test_can_replace_current_tab()
    {
        $component = Livewire::test(Dashboard::class)
            ->call('openTab', 'test-component', 'Test Tab', [])
            ->assertCount('tabs', 2);

        $component
            ->call('replaceTab', 'new-component', 'New Tab', [])
            ->assertCount('tabs', 2)
            ->assertSeeText('New Tab');
    }

    public function test_refresh_component_dispatches_refresh_event()
    {
        Livewire::test(Dashboard::class)
            ->call('refreshComponent')
            ->assertDispatched('$refresh');
    }

    public function test_tab_changed_event_is_dispatched_when_opening_tab()
    {
        Livewire::test(Dashboard::class)
            ->call('openTab', 'test-component', 'Test Tab', [])
            ->assertDispatched('tabChanged');
    }

    public function test_tab_changed_event_is_dispatched_when_switching_tab()
    {
        $component = Livewire::test(Dashboard::class)
            ->call('openTab', 'test-component', 'Test Tab', []);

        $homeTabKey = 'dashboard.home-d41d8cd98f00b204e9800998ecf8427e';

        $component
            ->call('switchTab', $homeTabKey)
            ->assertDispatched('tabChanged');
    }

    public function test_invalid_tab_switch_is_ignored()
    {
        $component = Livewire::test(Dashboard::class);
        $originalActiveTab = $component->get('activeTab');

        $component
            ->call('switchTab', 'non-existent-tab-key')
            ->assertSet('activeTab', $originalActiveTab);
    }
}
