<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Modules\Applications\Models\Application;
use App\Modules\Applications\Models\ApplicationData;
use App\Modules\Auth\Models\PortalUser;
use App\Modules\Entities\Models\Entity;
use App\Modules\Products\Models\Product;
use App\Modules\Products\Models\ProductFormField;
use App\Modules\Users\Models\Profile;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SaveApplicationDataTest extends TestCase
{
    use RefreshDatabase;

    private PortalUser $applicant;
    private PortalUser $clientUser;
    private Entity $entity;
    private Product $product;
    private Application $application;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test applicant
        $this->applicant = PortalUser::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'applicant'
        ]);

        Profile::create([
            'user_id' => $this->applicant->id,
            'first_name' => 'John',
            'last_name' => 'Doe',
            'active' => true,
        ]);

        // Create test client user
        $this->clientUser = PortalUser::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'client_user'
        ]);

        Profile::create([
            'user_id' => $this->clientUser->id,
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'active' => true,
        ]);

        // Create test entity
        $this->entity = Entity::create([
            'name' => 'Test Entity',
            'entity_code' => 'TEST001',
            'entity_type' => 'client',
            'status' => true
        ]);

        // Create test product
        $this->product = Product::create([
            'name' => 'Test Product',
            'code' => 'PROD001',
            'variant' => 'A'
        ]);

        // Create product form fields
        ProductFormField::create([
            'product_id' => $this->product->id,
            'field_name' => 'ApplicantDetails::TITLE',
            'field_label' => 'Title',
            'field_type' => 'select',
            'field_options' => ['MR', 'MRS', 'MS', 'DR'],
            'is_required' => true,
            'sort_order' => 1
        ]);

        ProductFormField::create([
            'product_id' => $this->product->id,
            'field_name' => 'ApplicantDetails::FORENAME',
            'field_label' => 'First Name',
            'field_type' => 'text',
            'is_required' => true,
            'sort_order' => 2
        ]);

        ProductFormField::create([
            'product_id' => $this->product->id,
            'field_name' => 'ApplicantDetails::DATE_OF_BIRTH',
            'field_label' => 'Date of Birth',
            'field_type' => 'date',
            'is_required' => true,
            'sort_order' => 3
        ]);

        // Associate applicant with entity
        $this->applicant->entities()->attach($this->entity->id, ['role' => 'applicant']);

        // Associate client user with entity
        $this->clientUser->entities()->attach($this->entity->id, ['role' => 'admin']);

        // Create test application
        $this->application = Application::create([
            'applicant_id' => $this->applicant->id,
            'product_id' => $this->product->id,
            'status' => 'pending',
            'submitted_by' => $this->clientUser->id,
            'external_reference' => 'APP001',
        ]);
    }

    public function test_applicant_can_save_application_data(): void
    {
        $token = $this->applicant->createToken('test')->plainTextToken;

        $formData = [
            'ApplicantDetails::TITLE' => 'MR',
            'ApplicantDetails::FORENAME' => 'Tester',
            'ApplicantDetails::DATE_OF_BIRTH' => '1995/05/26'
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/v1/applications/save-data', [
            'application_id' => $this->application->id,
            'form_data' => $formData
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Application data saved successfully',
                'data' => [
                    'application_id' => $this->application->id
                ]
            ]);

        // Verify data was saved to database
        $this->assertDatabaseHas('application_data', [
            'application_id' => $this->application->id,
            'field_key' => 'ApplicantDetails::TITLE',
            'value' => 'MR'
        ]);

        $this->assertDatabaseHas('application_data', [
            'application_id' => $this->application->id,
            'field_key' => 'ApplicantDetails::FORENAME',
            'value' => 'Tester'
        ]);
    }

    public function test_validation_fails_for_missing_required_fields(): void
    {
        $token = $this->applicant->createToken('test')->plainTextToken;

        $formData = [
            'ApplicantDetails::TITLE' => 'MR',
            // Missing required FORENAME field
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/v1/applications/save-data', [
            'application_id' => $this->application->id,
            'form_data' => $formData
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Form validation failed'
            ]);
    }

    public function test_validation_fails_for_invalid_date(): void
    {
        $token = $this->applicant->createToken('test')->plainTextToken;

        $formData = [
            'ApplicantDetails::TITLE' => 'MR',
            'ApplicantDetails::FORENAME' => 'Tester',
            'ApplicantDetails::DATE_OF_BIRTH' => 'invalid-date'
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/v1/applications/save-data', [
            'application_id' => $this->application->id,
            'form_data' => $formData
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Form validation failed'
            ]);
    }

    public function test_client_user_can_save_application_data(): void
    {
        $token = $this->clientUser->createToken('test')->plainTextToken;

        $formData = [
            'ApplicantDetails::TITLE' => 'MR',
            'ApplicantDetails::FORENAME' => 'Tester',
            'ApplicantDetails::DATE_OF_BIRTH' => '1995/05/26'
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/v1/applications/save-data', [
            'application_id' => $this->application->id,
            'form_data' => $formData
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Application data saved successfully'
            ]);
    }

    public function test_unauthorized_user_cannot_save_application_data(): void
    {
        $response = $this->postJson('/api/v1/applications/save-data', [
            'application_id' => $this->application->id,
            'form_data' => ['test' => 'value']
        ]);

        $response->assertStatus(401);
    }

    public function test_validation_fails_when_form_data_is_string(): void
    {
        $token = $this->applicant->createToken('test')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/v1/applications/save-data', [
            'application_id' => $this->application->id,
            'form_data' => 'invalid_string'
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Validation failed',
                'data' => [
                    'form_data' => ['Form data must be an array']
                ]
            ]);
    }

    public function test_validation_fails_when_form_data_is_number(): void
    {
        $token = $this->applicant->createToken('test')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/v1/applications/save-data', [
            'application_id' => $this->application->id,
            'form_data' => 123
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Validation failed',
                'data' => [
                    'form_data' => ['Form data must be an array']
                ]
            ]);
    }

    public function test_validation_fails_when_form_data_is_empty_array(): void
    {
        $token = $this->applicant->createToken('test')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/v1/applications/save-data', [
            'application_id' => $this->application->id,
            'form_data' => []
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Validation failed',
                'data' => [
                    'form_data' => ['Form data cannot be empty']
                ]
            ]);
    }

    public function test_validation_fails_when_form_data_is_missing(): void
    {
        $token = $this->applicant->createToken('test')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/v1/applications/save-data', [
            'application_id' => $this->application->id
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Validation failed',
                'data' => [
                    'form_data' => ['Form data is required']
                ]
            ]);
    }

    public function test_validation_fails_when_form_data_contains_non_string_values(): void
    {
        $token = $this->applicant->createToken('test')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/v1/applications/save-data', [
            'application_id' => $this->application->id,
            'form_data' => [
                'valid_field' => 'string_value',
                'invalid_field' => 123
            ]
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Validation failed'
            ]);

        // Check that the error is specifically for the non-string field
        $response->assertJsonPath('data.form_data\.invalid_field.0', 'All form field values must be strings');
    }

    public function test_client_user_cannot_save_data_for_applicant_from_different_entity(): void
    {
        // Create another entity and client user
        $otherEntity = Entity::create([
            'name' => 'Other Entity',
            'entity_code' => 'OTHER001',
            'entity_type' => 'client',
            'status' => true
        ]);

        $otherClientUser = PortalUser::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'client_user'
        ]);

        Profile::create([
            'user_id' => $otherClientUser->id,
            'first_name' => 'Other',
            'last_name' => 'Client',
            'active' => true,
        ]);

        // Associate other client user with other entity
        $otherClientUser->entities()->attach($otherEntity->id, ['role' => 'admin']);

        $token = $otherClientUser->createToken('test')->plainTextToken;

        $formData = [
            'ApplicantDetails::TITLE' => 'MR',
            'ApplicantDetails::FORENAME' => 'Tester',
            'ApplicantDetails::DATE_OF_BIRTH' => '1995/05/26'
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/v1/applications/save-data', [
            'application_id' => $this->application->id,
            'form_data' => $formData
        ]);

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'Application not found'
            ]);
    }
}
