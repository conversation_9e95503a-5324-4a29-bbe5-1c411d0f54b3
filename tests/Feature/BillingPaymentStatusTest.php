<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Modules\Applications\Models\Application;
use App\Modules\Auth\Models\PortalUser;
use App\Modules\Billing\Models\ApplicationBillingSnapshot;
use App\Modules\Entities\Models\Entity;
use App\Modules\Products\Models\Product;
use App\Modules\Users\Models\Profile;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BillingPaymentStatusTest extends TestCase
{
    use RefreshDatabase;

    private PortalUser $applicant;
    private PortalUser $clientUser;
    private Entity $entity;
    private Product $product;
    private Application $application;

    protected function setUp(): void
    {
        parent::setUp();

        $this->entity = Entity::create([
            'name' => 'Test Entity',
            'entity_code' => 'TEST001',
            'entity_type' => 'client',
            'status' => true
        ]);

        $this->product = Product::create([
            'name' => 'Test Product',
            'code' => 'PROD001',
            'variant' => 'A'
        ]);

        $this->applicant = PortalUser::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'applicant',
            'active' => true
        ]);

        Profile::create([
            'user_id' => $this->applicant->id,
            'first_name' => 'Test',
            'last_name' => 'Applicant',
            'telephone' => '1234567890'
        ]);

        $this->clientUser = PortalUser::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'client_user',
            'active' => true
        ]);

        Profile::create([
            'user_id' => $this->clientUser->id,
            'first_name' => 'Client',
            'last_name' => 'User',
            'telephone' => '0987654321'
        ]);

        $this->clientUser->entities()->attach($this->entity->id, ['role' => 'admin']);

        $this->application = Application::create([
            'applicant_id' => $this->applicant->id,
            'product_id' => $this->product->id,
            'status' => 'pending',
            'submitted_by' => $this->clientUser->id
        ]);
    }

    public function test_applicant_can_check_own_payment_status_no_payment_required(): void
    {
        ApplicationBillingSnapshot::create([
            'application_id' => $this->application->id,
            'admin_fee' => 50.00,
            'supplier_fee' => 25.00,
            'self_payment' => false
        ]);

        $token = $this->applicant->createToken('test')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson("/api/v1/billing/payment-status/{$this->applicant->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'payment_required' => false,
                    'total_amount' => 0,
                    'currency' => 'GBP',
                    'applications' => [],
                    'total_applications_requiring_payment' => 0
                ],
                'message' => 'Payment status retrieved successfully'
            ]);
    }

    public function test_applicant_can_check_own_payment_status_payment_required(): void
    {
        ApplicationBillingSnapshot::create([
            'application_id' => $this->application->id,
            'admin_fee' => 50.00,
            'supplier_fee' => 25.00,
            'self_payment' => true
        ]);

        $token = $this->applicant->createToken('test')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson("/api/v1/billing/payment-status/{$this->applicant->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'payment_required' => true,
                    'total_amount' => 75.00,
                    'currency' => 'GBP',
                    'total_applications_requiring_payment' => 1
                ],
                'message' => 'Payment status retrieved successfully'
            ]);

        $responseData = $response->json('data');
        $this->assertCount(1, $responseData['applications']);
        $this->assertEquals($this->application->id, $responseData['applications'][0]['application_id']);
        $this->assertEquals(75.00, $responseData['applications'][0]['billing']['total_fee']);
    }

    public function test_applicant_cannot_check_other_applicant_payment_status(): void
    {
        $otherApplicant = PortalUser::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'applicant',
            'active' => true
        ]);

        $token = $this->applicant->createToken('test')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson("/api/v1/billing/payment-status/{$otherApplicant->id}");

        $response->assertStatus(403)
            ->assertJson([
                'success' => false,
                'message' => 'You can only check your own payment status'
            ]);
    }

    public function test_client_user_can_check_accessible_applicant_payment_status(): void
    {
        ApplicationBillingSnapshot::create([
            'application_id' => $this->application->id,
            'admin_fee' => 30.00,
            'supplier_fee' => 20.00,
            'self_payment' => true
        ]);

        $token = $this->clientUser->createToken('test')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson("/api/v1/billing/payment-status/{$this->applicant->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'payment_required' => true,
                    'total_amount' => 50.00,
                    'currency' => 'GBP',
                    'total_applications_requiring_payment' => 1
                ],
                'message' => 'Payment status retrieved successfully'
            ]);
    }

    public function test_unauthorized_user_cannot_access_payment_status(): void
    {
        $response = $this->getJson("/api/v1/billing/payment-status/{$this->applicant->id}");

        $response->assertStatus(401);
    }

    public function test_invalid_user_type_cannot_access_payment_status(): void
    {
        $adminUser = PortalUser::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'admin',
            'active' => true
        ]);

        $token = $adminUser->createToken('test')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson("/api/v1/billing/payment-status/{$this->applicant->id}");

        $response->assertStatus(403)
            ->assertJson([
                'success' => false,
                'message' => 'Access denied for this user type'
            ]);
    }
}
