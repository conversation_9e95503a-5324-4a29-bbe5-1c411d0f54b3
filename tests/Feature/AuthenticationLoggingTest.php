<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Modules\Auth\Models\PortalUser;
use App\Modules\Users\Models\Profile;
use App\Services\CentLogClient;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;
use Mockery;

class AuthenticationLoggingTest extends TestCase
{
    use RefreshDatabase;

    private $mockCentLogClient;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->mockCentLogClient = Mockery::mock(CentLogClient::class);
        $this->app->instance(CentLogClient::class, $this->mockCentLogClient);
    }

    public function test_successful_login_logs_authentication_event(): void
    {
        $user = PortalUser::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'client_user'
        ]);

        Profile::create([
            'user_id' => $user->id,
            'first_name' => 'Test',
            'last_name' => 'User',
            'active' => true,
            'two_factor_enabled' => false,
        ]);

        $this->mockCentLogClient
            ->shouldReceive('log')
            ->once()
            ->with('Auth', $user->id, Mockery::subset([
                'status' => 'success',
                'login_method' => 'password',
                'user_type' => 'client_user'
            ]), 'info');

        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password'
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'user',
                'token',
                'token_type'
            ]
        ]);
    }

    public function test_failed_login_logs_authentication_failure(): void
    {
        $user = PortalUser::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('correct_password'),
            'user_type' => 'client_user'
        ]);

        Profile::create([
            'user_id' => $user->id,
            'first_name' => 'Test',
            'last_name' => 'User',
            'active' => true,
            'two_factor_enabled' => false,
        ]);

        $this->mockCentLogClient
            ->shouldReceive('log')
            ->once()
            ->with('Auth', '<EMAIL>', Mockery::subset([
                'status' => 'failed',
                'reason' => 'invalid_credentials',
                'login_method' => 'password'
            ]), 'warning');

        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'wrong_password'
        ]);

        $response->assertStatus(401);
    }

    public function test_inactive_user_login_logs_authentication_failure(): void
    {
        $user = PortalUser::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'client_user'
        ]);

        Profile::create([
            'user_id' => $user->id,
            'first_name' => 'Test',
            'last_name' => 'User',
            'active' => false,
            'two_factor_enabled' => false,
        ]);

        $this->mockCentLogClient
            ->shouldReceive('log')
            ->once()
            ->with('Auth', $user->id, Mockery::subset([
                'status' => 'failed',
                'reason' => 'account_inactive',
                'login_method' => 'password'
            ]), 'warning');

        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password'
        ]);

        $response->assertStatus(403);
    }

    public function test_logout_logs_authentication_event(): void
    {
        $user = PortalUser::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'client_user'
        ]);

        Profile::create([
            'user_id' => $user->id,
            'first_name' => 'Test',
            'last_name' => 'User',
            'active' => true,
            'two_factor_enabled' => false,
        ]);

        $token = $user->createToken('test')->plainTextToken;

        $this->mockCentLogClient
            ->shouldReceive('log')
            ->once()
            ->with('Auth', $user->id, Mockery::subset([
                'status' => 'logout',
                'user_type' => 'client_user'
            ]), 'info');

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/v1/auth/logout');

        $response->assertStatus(200);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
