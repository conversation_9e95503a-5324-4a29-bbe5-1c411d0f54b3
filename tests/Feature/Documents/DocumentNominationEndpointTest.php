<?php

declare(strict_types=1);

namespace Tests\Feature\Documents;

use App\Modules\Applications\Models\Application;
use App\Modules\Auth\Models\PortalUser;
use App\Modules\Documents\Models\DocDafData;
use App\Modules\Products\Models\Product;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\RateLimiter;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class DocumentNominationEndpointTest extends TestCase
{
    use RefreshDatabase;

    private PortalUser $applicant;
    private PortalUser $clientUser;
    private Application $application;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test users
        $this->applicant = PortalUser::factory()->create([
            'user_type' => 'applicant',
            'email' => '<EMAIL>'
        ]);

        $this->clientUser = PortalUser::factory()->create([
            'user_type' => 'client_user',
            'email' => '<EMAIL>'
        ]);

        // Create test product
        $product = Product::factory()->create([
            'code' => 'DBSEC',
            'name' => 'DBS Enhanced Check'
        ]);

        // Create test application
        $this->application = Application::factory()->create([
            'applicant_id' => $this->applicant->id,
            'product_id' => $product->id,
            'status' => 'in_progress'
        ]);
    }

    public function test_get_documents_requires_authentication(): void
    {
        $response = $this->getJson("/api/v1/applications/{$this->application->id}/documents");

        $response->assertStatus(401);
    }

    public function test_get_documents_validates_user_type(): void
    {
        $invalidUser = PortalUser::factory()->create([
            'user_type' => 'admin',
            'email' => '<EMAIL>'
        ]);

        Sanctum::actingAs($invalidUser);

        $response = $this->getJson("/api/v1/applications/{$this->application->id}/documents");

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'Access denied for this user type'
                ]);
    }

    public function test_get_documents_validates_application_id(): void
    {
        Sanctum::actingAs($this->applicant);

        // Test invalid application ID
        $response = $this->getJson('/api/v1/applications/0/documents');

        $response->assertStatus(400)
                ->assertJson([
                    'success' => false,
                    'message' => 'Invalid application ID'
                ]);
    }

    public function test_get_documents_checks_application_access(): void
    {
        $otherApplicant = PortalUser::factory()->create([
            'user_type' => 'applicant',
            'email' => '<EMAIL>'
        ]);

        Sanctum::actingAs($otherApplicant);

        $response = $this->getJson("/api/v1/applications/{$this->application->id}/documents");

        $response->assertStatus(404)
                ->assertJson([
                    'success' => false,
                    'message' => 'Application not found'
                ]);
    }

    public function test_applicant_can_access_own_documents(): void
    {
        Sanctum::actingAs($this->applicant);

        $response = $this->getJson("/api/v1/applications/{$this->application->id}/documents");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'application' => [
                            'id',
                            'product_code',
                            'product_name',
                            'applicant_name',
                            'status'
                        ],
                        'applicant_context' => [
                            'nationality',
                            'current_address_country',
                            'is_uk_national',
                            'is_uk_resident',
                            'work_type',
                            'product_code'
                        ],
                        'routing' => [
                            'recommended_route',
                            'available_routes',
                            'route_requirements'
                        ],
                        'documents' => [
                            'available_by_group',
                            'total_available',
                            'current_nominations',
                            'nomination_summary'
                        ],
                        'metadata' => [
                            'retrieved_at',
                            'user_type',
                            'config_version'
                        ]
                    ]
                ]);
    }

    public function test_response_contains_config_based_documents(): void
    {
        Sanctum::actingAs($this->applicant);

        $response = $this->getJson("/api/v1/applications/{$this->application->id}/documents");

        $response->assertStatus(200);

        $data = $response->json('data');

        // Verify config-based structure
        $this->assertArrayHasKey('documents', $data);
        $this->assertArrayHasKey('available_by_group', $data['documents']);
        $this->assertIsInt($data['documents']['total_available']);
        $this->assertGreaterThan(0, $data['documents']['total_available']);

        // Verify document groups exist
        $availableGroups = array_keys($data['documents']['available_by_group']);
        $this->assertContains('1', $availableGroups); // Primary identity documents should be available

        // Verify document structure
        foreach ($data['documents']['available_by_group'] as $group => $groupData) {
            $this->assertArrayHasKey('group_name', $groupData);
            $this->assertArrayHasKey('document_count', $groupData);
            $this->assertArrayHasKey('documents', $groupData);
            $this->assertIsArray($groupData['documents']);

            foreach ($groupData['documents'] as $document) {
                $this->assertArrayHasKey('key', $document);
                $this->assertArrayHasKey('name', $document);
                $this->assertArrayHasKey('requires_photo', $document);
                $this->assertArrayHasKey('confirms_address', $document);
                $this->assertArrayHasKey('applicable_countries', $document);
                $this->assertArrayHasKey('data_fields', $document);
            }
        }
    }

    public function test_rate_limiting_works(): void
    {
        Sanctum::actingAs($this->applicant);

        // Clear any existing rate limits
        RateLimiter::clear("documents_access:{$this->applicant->id}:127.0.0.1");

        // Make requests up to the limit
        for ($i = 0; $i < 60; $i++) {
            $response = $this->getJson("/api/v1/applications/{$this->application->id}/documents");
            $response->assertStatus(200);
        }

        // Next request should be rate limited
        $response = $this->getJson("/api/v1/applications/{$this->application->id}/documents");
        $response->assertStatus(429)
                ->assertJson([
                    'success' => false,
                    'message' => 'Too many requests. Please try again later.'
                ]);
    }

    public function test_response_includes_current_nominations(): void
    {
        // Create a test nomination
        DocDafData::create([
            'application_id' => $this->application->id,
            'document_type_key' => 'passport_any',
            'document_name' => 'Passport (Any Country)',
            'document_group' => '1',
            'route_number' => 1,
            'document_data' => ['passport_number' => 'TEST123'],
            'confirms_address' => false,
            'status' => 'nominated',
            'nominated_by' => $this->applicant->id
        ]);

        Sanctum::actingAs($this->applicant);

        $response = $this->getJson("/api/v1/applications/{$this->application->id}/documents");

        $response->assertStatus(200);

        $data = $response->json('data');
        $this->assertCount(1, $data['documents']['current_nominations']);
        $this->assertEquals('passport_any', $data['documents']['current_nominations'][0]['document_type_key']);
        $this->assertEquals('nominated', $data['documents']['current_nominations'][0]['status']);

        // Verify nomination summary
        $summary = $data['documents']['nomination_summary'];
        $this->assertEquals(1, $summary['total_nominations']);
        $this->assertEquals(1, $summary['by_status']['nominated']);
        $this->assertEquals(1, $summary['by_group']['1']);
        $this->assertEquals(1, $summary['by_route'][1]);
    }

    public function test_metadata_is_included(): void
    {
        Sanctum::actingAs($this->applicant);

        $response = $this->getJson("/api/v1/applications/{$this->application->id}/documents");

        $response->assertStatus(200);

        $data = $response->json('data');
        $metadata = $data['metadata'];

        $this->assertArrayHasKey('retrieved_at', $metadata);
        $this->assertEquals('applicant', $metadata['user_type']);
        $this->assertEquals('2.0', $metadata['config_version']);
        $this->assertNotEmpty($metadata['retrieved_at']);
    }
}
