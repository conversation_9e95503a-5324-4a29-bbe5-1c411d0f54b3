<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Modules\Auth\Models\PortalUser;
use App\Modules\Entities\Models\Entity;
use App\Modules\Entities\SubModules\JobRoles\Models\JobRole;
use App\Modules\Products\Models\Product;
use App\Modules\Users\Models\Profile;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CreateApplicantTest extends TestCase
{
    use RefreshDatabase;

    private PortalUser $clientUser;
    private Entity $entity;
    private JobRole $jobRole;
    private Product $product1;
    private Product $product2;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test client user
        $this->clientUser = PortalUser::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'client_user'
        ]);

        Profile::create([
            'user_id' => $this->clientUser->id,
            'first_name' => 'Jane',
            'last_name' => 'Client',
            'active' => true,
        ]);

        // Create test entity
        $this->entity = Entity::create([
            'name' => 'Test Entity',
            'entity_code' => 'TEST001',
            'entity_type' => 'client',
            'status' => true
        ]);

        // Associate client user with entity
        $this->clientUser->entities()->attach($this->entity->id, ['role' => 'admin']);

        // Create test job role
        $this->jobRole = JobRole::create([
            'entity_id' => $this->entity->id,
            'job_label' => 'Cleaner',
            'job_title' => 'Office Cleaner',
            'job_workforce' => 'Full-time',
            'role_description' => 'Cleaning office spaces',
            'self_payment' => false,
            'employment_sector' => 'Cleaning'
        ]);

        // Create test products
        $this->product1 = Product::create([
            'name' => 'DBS Basic Check',
            'code' => 'DBS001',
            'variant' => 'A'
        ]);

        $this->product2 = Product::create([
            'name' => 'Reference Check',
            'code' => 'REF001',
            'variant' => 'B'
        ]);

        // Associate products with job role
        $this->jobRole->products()->attach([$this->product1->id, $this->product2->id]);
    }

    public function test_client_user_can_create_applicant(): void
    {
        $token = $this->clientUser->createToken('test')->plainTextToken;

        $requestData = [
            'first_name' => 'John',
            'last_name' => 'Smith',
            'email' => '<EMAIL>',
            'phone' => '+44 123 123 123',
            'job_role_id' => $this->jobRole->id,
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/v1/applicants', $requestData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'applicant' => [
                        'id',
                        'email',
                        'first_name',
                        'last_name',
                        'phone',
                        'full_name'
                    ],
                    'entity' => [
                        'id',
                        'name',
                        'entity_code'
                    ],
                    'job_role' => [
                        'id',
                        'job_label',
                        'job_title'
                    ],
                    'applications' => [
                        '*' => [
                            'id',
                            'product_id',
                            'status',
                            'billing' => [
                                'admin_fee',
                                'supplier_fee',
                                'total_fee',
                                'self_payment',
                                'currency'
                            ]
                        ]
                    ],
                    'generated_password',
                    'total_applications',
                    'products_auto_selected'
                ],
                'message'
            ]);

        // Verify response data
        $responseData = $response->json('data');
        $this->assertEquals('John', $responseData['applicant']['first_name']);
        $this->assertEquals('Smith', $responseData['applicant']['last_name']);
        $this->assertEquals('<EMAIL>', $responseData['applicant']['email']);
        $this->assertEquals('+44 123 123 123', $responseData['applicant']['phone']);
        $this->assertEquals('John Smith', $responseData['applicant']['full_name']);

        $this->assertEquals($this->entity->id, $responseData['entity']['id']);
        $this->assertEquals('Test Entity', $responseData['entity']['name']);

        $this->assertEquals($this->jobRole->id, $responseData['job_role']['id']);
        $this->assertEquals('Cleaner', $responseData['job_role']['job_label']);

        $this->assertEquals(2, $responseData['total_applications']);
        $this->assertCount(2, $responseData['applications']);

        // Verify applications have correct product IDs
        $applicationProductIds = array_column($responseData['applications'], 'product_id');
        $this->assertContains($this->product1->id, $applicationProductIds);
        $this->assertContains($this->product2->id, $applicationProductIds);

        // Verify all applications are in draft status
        foreach ($responseData['applications'] as $application) {
            $this->assertEquals('draft', $application['status']);
        }

        // Verify password is generated
        $this->assertNotEmpty($responseData['generated_password']);
        $this->assertIsString($responseData['generated_password']);

        // Verify billing snapshots were created
        $application1 = \App\Modules\Applications\Models\Application::where([
            'applicant_id' => $responseData['applicant']['id'],
            'product_id' => $this->product1->id
        ])->first();

        $application2 = \App\Modules\Applications\Models\Application::where([
            'applicant_id' => $responseData['applicant']['id'],
            'product_id' => $this->product2->id
        ])->first();

        $this->assertDatabaseHas('application_billing_snapshots', [
            'application_id' => $application1->id,
        ]);

        $this->assertDatabaseHas('application_billing_snapshots', [
            'application_id' => $application2->id,
        ]);

        // Verify billing information is included in response
        foreach ($responseData['applications'] as $application) {
            $this->assertArrayHasKey('billing', $application);
            $this->assertArrayHasKey('admin_fee', $application['billing']);
            $this->assertArrayHasKey('supplier_fee', $application['billing']);
            $this->assertArrayHasKey('total_fee', $application['billing']);
            $this->assertArrayHasKey('self_payment', $application['billing']);
            $this->assertArrayHasKey('currency', $application['billing']);
            $this->assertEquals('GBP', $application['billing']['currency']);
        }
    }

    public function test_validation_errors_for_invalid_data(): void
    {
        $token = $this->clientUser->createToken('test')->plainTextToken;

        $requestData = [
            'first_name' => '',
            'last_name' => '',
            'email' => 'invalid-email',
            'phone' => '',
            'job_role_id' => 999,
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/v1/applicants', $requestData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'first_name',
                'last_name',
                'email',
                'phone',
                'job_role_id'
            ]);
    }

    public function test_duplicate_email_validation(): void
    {
        // Create existing user with same email
        PortalUser::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'applicant'
        ]);

        $token = $this->clientUser->createToken('test')->plainTextToken;

        $requestData = [
            'first_name' => 'John',
            'last_name' => 'Smith',
            'email' => '<EMAIL>',
            'phone' => '+44 123 123 123',
            'job_role_id' => $this->jobRole->id,
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/v1/applicants', $requestData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['email']);
    }

    public function test_non_client_user_cannot_create_applicant(): void
    {
        // Create applicant user
        $applicant = PortalUser::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'applicant'
        ]);

        Profile::create([
            'user_id' => $applicant->id,
            'first_name' => 'John',
            'last_name' => 'Applicant',
            'active' => true,
        ]);

        $token = $applicant->createToken('test')->plainTextToken;

        $requestData = [
            'first_name' => 'John',
            'last_name' => 'Smith',
            'email' => '<EMAIL>',
            'phone' => '+44 123 123 123',
            'job_role_id' => $this->jobRole->id,
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/v1/applicants', $requestData);

        $response->assertStatus(403)
            ->assertJson([
                'success' => false,
                'message' => 'Access denied for this user type'
            ]);
    }

    public function test_unauthenticated_user_cannot_create_applicant(): void
    {
        $requestData = [
            'first_name' => 'John',
            'last_name' => 'Smith',
            'email' => '<EMAIL>',
            'phone' => '+44 123 123 123',
            'job_role_id' => $this->jobRole->id,
        ];

        $response = $this->postJson('/api/v1/applicants', $requestData);

        $response->assertStatus(401);
    }
}
