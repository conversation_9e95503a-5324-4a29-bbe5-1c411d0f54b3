<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Modules\Applications\Models\Application;
use App\Modules\Applications\Models\ApplicationFormData;
use App\Modules\Auth\Models\PortalUser;
use App\Modules\Entities\Models\Entity;
use App\Modules\Products\Models\Product;
use App\Modules\Users\Models\Profile;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ApplicantApplicationsTest extends TestCase
{
    use RefreshDatabase;

    private PortalUser $applicant;
    private PortalUser $clientUser;
    private Entity $entity;
    private Product $product;
    private Application $application;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test applicant
        $this->applicant = PortalUser::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'applicant'
        ]);

        Profile::create([
            'user_id' => $this->applicant->id,
            'first_name' => 'John',
            'last_name' => 'Applicant',
            'active' => true,
        ]);

        // Create test client user
        $this->clientUser = PortalUser::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'client_user'
        ]);

        Profile::create([
            'user_id' => $this->clientUser->id,
            'first_name' => 'Jane',
            'last_name' => 'Client',
            'active' => true,
        ]);

        // Create test entity
        $this->entity = Entity::create([
            'name' => 'Test Entity',
            'entity_code' => 'TEST001',
            'entity_type' => 'client',
            'status' => true
        ]);

        // Create test product
        $this->product = Product::create([
            'name' => 'Test Product',
            'code' => 'PROD001',
            'variant' => 'A'
        ]);

        // Create test application
        $this->application = Application::create([
            'entity_id' => $this->entity->id,
            'product_id' => $this->product->id,
            'applicant_id' => $this->applicant->id,
            'submitted_by' => $this->clientUser->id,
            'external_reference' => 'APP001',
            'status' => 'pending',
        ]);

        // Create test form data
        ApplicationFormData::create([
            'application_id' => $this->application->id,
            'field_name' => 'full_name',
            'field_value' => 'John Applicant'
        ]);

        ApplicationFormData::create([
            'application_id' => $this->application->id,
            'field_name' => 'email',
            'field_value' => '<EMAIL>'
        ]);
    }

    public function test_applicant_can_login(): void
    {
        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password'
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'user',
                    'token',
                    'token_type'
                ],
                'message'
            ])
            ->assertJson([
                'message' => 'Applicant logged in successfully'
            ]);

        // Verify applicant response doesn't include entities
        $this->assertArrayNotHasKey('entities', $response->json('data'));
    }

    public function test_client_user_can_login_and_gets_entities(): void
    {
        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password'
        ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'user',
                    'entities',
                    'token',
                    'token_type'
                ],
                'message'
            ])
            ->assertJson([
                'message' => 'User logged in successfully'
            ]);

        // Verify client user response includes entities
        $this->assertArrayHasKey('entities', $response->json('data'));
    }



    public function test_applicant_can_get_their_details(): void
    {
        $token = $this->applicant->createToken('test')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson("/api/v1/applicants/{$this->applicant->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'applicant' => [
                        'id',
                        'email',
                        'user_type',
                        'profile',
                        'entities'
                    ],
                    'applications' => [
                        '*' => [
                            'id',
                            'external_reference',
                            'status',
                            'product',
                            'completion_status',
                            'submitted_by'
                        ]
                    ],
                    'total_applications'
                ],
                'message'
            ]);

        $this->assertEquals($this->applicant->id, $response->json('data.applicant.id'));
        $this->assertEquals(1, $response->json('data.total_applications'));
    }



    public function test_applicant_cannot_access_other_applicants_details(): void
    {
        // Create another applicant
        $otherApplicant = PortalUser::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'applicant'
        ]);

        Profile::create([
            'user_id' => $otherApplicant->id,
            'first_name' => 'Other',
            'last_name' => 'Applicant',
            'active' => true,
        ]);

        $token = $otherApplicant->createToken('test')->plainTextToken;

        // Try to access the first applicant's details
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson("/api/v1/applicants/{$this->applicant->id}");

        $response->assertStatus(403)
            ->assertJson([
                'success' => false,
                'message' => 'You can only access your own data'
            ]);
    }

    public function test_applicant_can_get_application_form_data(): void
    {
        $token = $this->applicant->createToken('test')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson("/api/v1/applications/{$this->application->id}/form-data");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'application_id',
                    'external_reference',
                    'status',
                    'form_data',
                    'form_fields',
                    'completion_status' => [
                        'is_started',
                        'is_complete',
                        'status_text'
                    ],
                    'product' => [
                        'id',
                        'name',
                        'code',
                        'variant'
                    ]
                ],
                'message'
            ]);

        $completionStatus = $response->json('data.completion_status');
        $this->assertIsBool($completionStatus['is_started']);
        $this->assertIsBool($completionStatus['is_complete']);
        $this->assertIsString($completionStatus['status_text']);
    }
}
