<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code Test</title>
</head>
<body>
    <h1>QR Code Test for 2FA</h1>
    
    <div id="result"></div>
    <button onclick="testLogin()">Test Login with 2FA</button>
    
    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing login...';

            try {
                const response = await fetch('http://localhost:8001/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({
                        username: '<EMAIL>',
                        password: 'test'
                    })
                });

                const data = await response.json();
                console.log('Response:', data);

                if (data.success && data.data.requires_two_factor_setup) {
                    resultDiv.innerHTML = `
                        <h3>2FA Setup Required</h3>
                        <p><strong>Temporary Secret:</strong> ${data.data.temp_secret}</p>

                        <h4>QR Code:</h4>
                        <img src="${data.data.qr_code_image}" alt="QR Code" style="border: 1px solid #ccc; padding: 10px;">

                        <h4>Instructions:</h4>
                        <ol>
                            <li>Open your authenticator app (Google Authenticator, Microsoft Authenticator, etc.)</li>
                            <li>Scan the QR code above</li>
                            <li>Enter the 6-digit code from your app along with the temp_secret to complete setup</li>
                        </ol>

                        <h4>Verify PIN:</h4>
                        <input type="text" id="pinInput" placeholder="Enter 6-digit PIN" maxlength="6">
                        <button onclick="verifyPin('${data.data.temp_secret}')">Verify PIN</button>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <h3>Response:</h3>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <h3>Error:</h3>
                    <p style="color: red;">${error.message}</p>
                `;
            }
        }

        async function verifyPin(tempSecret) {
            const pin = document.getElementById('pinInput').value;
            const resultDiv = document.getElementById('result');

            if (!pin || pin.length !== 6) {
                alert('Please enter a 6-digit PIN');
                return;
            }

            try {
                const response = await fetch('http://localhost:8001/api/v1/auth/verify-pin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({
                        username: '<EMAIL>',
                        password: 'test',
                        pin: pin,
                        temp_secret: tempSecret
                    })
                });

                const data = await response.json();
                console.log('Verify PIN Response:', data);

                if (data.success) {
                    resultDiv.innerHTML += `
                        <h3 style="color: green;">✅ 2FA Setup Complete!</h3>
                        <p>You have successfully set up two-factor authentication.</p>
                        <p><strong>Token:</strong> ${data.data.token}</p>
                    `;
                } else {
                    resultDiv.innerHTML += `
                        <h3 style="color: red;">❌ Verification Failed</h3>
                        <p>${data.message}</p>
                    `;
                }
            } catch (error) {
                console.error('Verify PIN Error:', error);
                resultDiv.innerHTML += `
                    <h3 style="color: red;">❌ Error:</h3>
                    <p>${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
